#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import numpy as np
import json
import argparse
import os
import time

def draw_feature_points(image_path, landmarks, output_path=None):
    """
    在图像上绘制特征点
    
    Args:
        image_path: 输入图像路径
        landmarks: 特征点数据
        output_path: 输出图像路径
    
    Returns:
        绘制了特征点的图像
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return None
    
    height, width = image.shape[:2]
    print(f"图像尺寸: {width}x{height}")
    
    # 绘制特征点
    for landmark in landmarks:
        x = int(landmark.get('x', 0) * width)
        y = int(landmark.get('y', 0) * height)
        dx = landmark.get('dx', 0) * width * 3.0  # 使用与变形算法相同的增强系数
        dy = landmark.get('dy', 0) * height * 3.0
        
        # 计算变形后的位置
        x2 = int(x + dx)
        y2 = int(y + dy)
        
        # 绘制特征点（红色圆点）
        cv2.circle(image, (x, y), 8, (0, 0, 255), -1)
        
        # 绘制变形向量（蓝色箭头）
        if dx != 0 or dy != 0:
            cv2.arrowedLine(image, (x, y), (x2, y2), (255, 0, 0), 3)
    
    # 绘制变形中心
    if landmarks:
        # 计算中心点
        x_coords = [landmark.get('x', 0) * width for landmark in landmarks]
        y_coords = [landmark.get('y', 0) * height for landmark in landmarks]
        center_x = int(sum(x_coords) / len(x_coords))
        center_y = int(sum(y_coords) / len(y_coords))
        
        # 绘制变形中心（绿色圆圈）
        cv2.circle(image, (center_x, center_y), 15, (0, 255, 0), 2)
        
        # 绘制影响半径（绿色圆圈）
        influence_radius = int(width * 0.05)  # 与变形算法中相同的影响半径
        cv2.circle(image, (center_x, center_y), influence_radius, (0, 255, 0), 2)
    
    # 保存结果
    if output_path is None:
        timestamp = int(time.time())
        base_name, ext = os.path.splitext(image_path)
        output_path = f"{base_name}_points_{timestamp}{ext}"
    
    cv2.imwrite(output_path, image)
    print(f"已保存带特征点的图像到: {output_path}")
    
    return image

def main():
    parser = argparse.ArgumentParser(description='在图像上绘制特征点')
    parser.add_argument('--input', required=True, help='输入图像路径')
    parser.add_argument('--output', help='输出图像路径')
    parser.add_argument('--data', required=True, help='特征点数据（JSON格式）')
    
    args = parser.parse_args()
    
    # 解析特征点数据
    try:
        data = json.loads(args.data)
        landmarks = data.get('landmarks', [])
    except json.JSONDecodeError:
        print("无法解析JSON数据")
        return
    
    # 绘制特征点
    draw_feature_points(args.input, landmarks, args.output)

if __name__ == "__main__":
    main()
