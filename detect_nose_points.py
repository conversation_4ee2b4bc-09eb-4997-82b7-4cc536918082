#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import mediapipe as mp
import numpy as np
import json
import argparse
import os
import time

def detect_face_landmarks(image_path, output_path=None):
    """
    使用MediaPipe检测面部特征点
    
    Args:
        image_path: 输入图像路径
        output_path: 输出图像路径
    
    Returns:
        特征点数据和绘制了特征点的图像
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return None, None
    
    # 转换为RGB (MediaPipe需要RGB格式)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    height, width = image.shape[:2]
    print(f"图像尺寸: {width}x{height}")
    
    # 初始化MediaPipe Face Mesh
    mp_face_mesh = mp.solutions.face_mesh
    face_mesh = mp_face_mesh.FaceMesh(
        static_image_mode=True,
        max_num_faces=1,
        refine_landmarks=True,
        min_detection_confidence=0.5
    )
    
    # 处理图像
    results = face_mesh.process(image_rgb)
    
    # 检查是否检测到面部
    if not results.multi_face_landmarks:
        print("未检测到面部")
        return None, None
    
    # 获取第一个检测到的面部
    face_landmarks = results.multi_face_landmarks[0]
    
    # 鼻翼宽度相关的特征点ID
    nose_width_points = {
        115: "左鼻翼内侧点",
        344: "右鼻翼内侧点",
        220: "左鼻翼软骨点",
        440: "右鼻翼软骨点",
        79: "左鼻孔外缘点",
        309: "右鼻孔外缘点",
        114: "左鼻尖外侧点",
        343: "右鼻尖外侧点",
        129: "左鼻翼点",
        358: "右鼻翼点",
        219: "左鼻翼外缘点",
        439: "右鼻翼外缘点"
    }
    
    # 提取特征点坐标
    landmarks_data = []
    for idx, point_id in enumerate(nose_width_points.keys()):
        if point_id < len(face_landmarks.landmark):
            landmark = face_landmarks.landmark[point_id]
            x, y = landmark.x, landmark.y
            
            # 计算变形向量 (左侧点向右移动，右侧点向左移动)
            dx = 0
            if "左" in nose_width_points[point_id]:
                dx = 0.02  # 向右移动
            elif "右" in nose_width_points[point_id]:
                dx = -0.02  # 向左移动
            
            # 根据点的重要性调整变形强度
            if "内侧" in nose_width_points[point_id]:
                dx *= 1.0  # 最大强度
            elif "软骨" in nose_width_points[point_id]:
                dx *= 0.9
            elif "外缘" in nose_width_points[point_id]:
                dx *= 0.8
            elif "外侧" in nose_width_points[point_id]:
                dx *= 0.7
            elif "鼻翼点" in nose_width_points[point_id]:
                dx *= 0.6
            elif "外缘" in nose_width_points[point_id]:
                dx *= 0.5  # 最小强度
            
            landmarks_data.append({
                "id": point_id,
                "x": x,
                "y": y,
                "dx": dx,
                "dy": 0,
                "name": nose_width_points[point_id]
            })
    
    # 创建用于可视化的图像副本
    vis_image = image.copy()
    
    # 绘制特征点和变形向量
    for landmark in landmarks_data:
        x = int(landmark["x"] * width)
        y = int(landmark["y"] * height)
        dx = int(landmark["dx"] * width * 3)  # 放大变形向量以便可视化
        
        # 绘制特征点（红色圆点）
        cv2.circle(vis_image, (x, y), 5, (0, 0, 255), -1)
        
        # 绘制变形向量（蓝色箭头）
        if dx != 0:
            cv2.arrowedLine(vis_image, (x, y), (x + dx, y), (255, 0, 0), 2)
        
        # 绘制特征点ID和名称
        cv2.putText(vis_image, f"{landmark['id']}", (x + 5, y - 5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 计算特征点的平均位置作为变形中心
    center_x = int(np.mean([landmark["x"] * width for landmark in landmarks_data]))
    center_y = int(np.mean([landmark["y"] * height for landmark in landmarks_data]))
    
    # 绘制变形中心（绿色圆圈）
    cv2.circle(vis_image, (center_x, center_y), 10, (0, 255, 0), 2)
    
    # 绘制影响半径（绿色圆圈）
    influence_radius = int(width * 0.05)  # 与变形算法中相同
    cv2.circle(vis_image, (center_x, center_y), influence_radius, (0, 255, 0), 2)
    
    # 获取基本文件名（无论是否提供输出路径）
    timestamp = int(time.time())
    base_name, ext = os.path.splitext(image_path)
    
    # 保存结果
    if output_path is None:
        output_path = f"{base_name}_nose_points_{timestamp}{ext}"
    
    cv2.imwrite(output_path, vis_image)
    print(f"已保存带特征点的图像到: {output_path}")
    
    # 创建用于变形的数据
    deformation_data = {
        "landmarks": [
            {
                "id": landmark["id"],
                "x": landmark["x"],
                "y": landmark["y"],
                "dx": landmark["dx"],
                "dy": landmark["dy"]
            } for landmark in landmarks_data
        ]
    }
    
    # 保存变形数据为JSON文件
    json_path = f"{base_name}_nose_deformation_{timestamp}.json"
    with open(json_path, 'w') as f:
        json.dump(deformation_data, f, indent=2)
    print(f"已保存变形数据到: {json_path}")
    
    return deformation_data, vis_image

def main():
    parser = argparse.ArgumentParser(description='检测面部特征点并生成鼻翼宽度变形数据')
    parser.add_argument('--input', required=True, help='输入图像路径')
    parser.add_argument('--output', help='输出图像路径')
    
    args = parser.parse_args()
    
    # 检测特征点并生成变形数据
    deformation_data, _ = detect_face_landmarks(args.input, args.output)
    
    if deformation_data:
        print("变形数据生成成功")
        # 打印用于命令行的变形数据
        cmd_data = json.dumps(deformation_data)
        print(f"\n可用于测试的命令:")
        print(f"python3 core/image_deformation.py --input {args.input} --output testdata/test_face_nostril_width.jpg --data '{cmd_data}'")
    else:
        print("变形数据生成失败")

if __name__ == "__main__":
    main()
