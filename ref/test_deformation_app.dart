import 'dart:io';
import 'package:flutter/material.dart';
import 'lib/core/image_deformation_engine.dart';
import 'lib/widgets/image_comparison_widget.dart';
import 'lib/painters/landmark_painter.dart';

void main() {
  runApp(const DeformationTestApp());
}

class DeformationTestApp extends StatelessWidget {
  const DeformationTestApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '图像变形测试',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const DeformationTestScreen(),
    );
  }
}

class DeformationTestScreen extends StatefulWidget {
  const DeformationTestScreen({Key? key}) : super(key: key);

  @override
  State<DeformationTestScreen> createState() => _DeformationTestScreenState();
}

class _DeformationTestScreenState extends State<DeformationTestScreen> with TickerProviderStateMixin {
  late String _originalImagePath;
  String? _transformedImagePath;
  bool _isLoading = false;
  bool _isComparing = false;
  double _dividerPosition = 0.5;
  
  // 特征点数据
  List<Map<String, dynamic>> _landmarks = [];
  
  // 变形参数
  double _deformationValue = 0.0;
  
  // 特征点状态
  final Set<int> _visibleIndexes = {};
  final Set<int> _highlightIndexes = {};
  final Set<int> _primaryHighlightIndexes = {};
  final Set<int> _secondaryHighlightIndexes = {};
  
  // 闪烁动画控制器
  late AnimationController _blinkController;
  double _blinkValue = 1.0;
  
  @override
  void initState() {
    super.initState();
    // 获取应用程序目录并设置图像路径
    _originalImagePath = '/Users/<USER>/beautifun/testdata/test_face.jpg';
    print('设置测试图像路径: $_originalImagePath');
    
    _checkImage();
    _generateLandmarks();
    
    // 初始化闪烁动画控制器
    _blinkController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _blinkController.addListener(() {
      setState(() {
        _blinkValue = (1 + _blinkController.value) / 2; // 0.5-1.0
      });
    });
    
    _blinkController.repeat(reverse: true);
  }
  
  @override
  void dispose() {
    _blinkController.dispose();
    super.dispose();
  }
  
  // 检查图像是否存在
  Future<void> _checkImage() async {
    final file = File(_originalImagePath);
    if (!await file.exists()) {
      print('错误：测试图像不存在 - $_originalImagePath');
    } else {
      print('测试图像：$_originalImagePath');
    }
  }
  
  // 生成模拟特征点数据
  void _generateLandmarks() {
    // 创建5x5网格的特征点
    for (int y = 0; y < 5; y++) {
      for (int x = 0; x < 5; x++) {
        final index = y * 5 + x + 1;
        _landmarks.add({
          'index': index,
          'x': 0.3 + x * 0.1,
          'y': 0.3 + y * 0.1,
        });
        
        // 添加到可见点
        _visibleIndexes.add(index);
      }
    }
    
    // 设置主导点
    _primaryHighlightIndexes.addAll([3, 8, 13]);
    
    // 设置协同点
    _secondaryHighlightIndexes.addAll([2, 4, 7, 9, 12, 14]);
    
    // 设置高亮点
    _highlightIndexes.addAll([6, 10, 16, 18]);
  }
  
  // 应用变形
  Future<void> _applyDeformation() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // 创建变形数据
      final deformations = <int, Offset>{};
      
      // 根据滑块值计算变形程度
      final factor = _deformationValue;
      
      // 更明显的变形效果 - 模拟瘦脸效果
      // 左侧面部轮廓向内移动
      deformations[0] = Offset(-0.15 * factor, 0.0);  // 左上
      deformations[5] = Offset(-0.15 * factor, 0.0);  // 左中
      deformations[10] = Offset(-0.15 * factor, 0.0); // 左下
      deformations[15] = Offset(-0.15 * factor, 0.0); // 左下角
      deformations[20] = Offset(-0.10 * factor, 0.0); // 左下角
      
      // 右侧面部轮廓向内移动
      deformations[4] = Offset(0.15 * factor, 0.0);   // 右上
      deformations[9] = Offset(0.15 * factor, 0.0);   // 右中
      deformations[14] = Offset(0.15 * factor, 0.0);  // 右下
      deformations[19] = Offset(0.15 * factor, 0.0);  // 右下角
      deformations[24] = Offset(0.10 * factor, 0.0);  // 右下角
      
      // 下巴上提
      deformations[20] = Offset(-0.05 * factor, -0.10 * factor);
      deformations[21] = Offset(-0.05 * factor, -0.10 * factor);
      deformations[22] = Offset(0.0, -0.15 * factor);
      deformations[23] = Offset(0.05 * factor, -0.10 * factor);
      deformations[24] = Offset(0.05 * factor, -0.10 * factor);
      
      // 额头拉伸
      deformations[1] = Offset(0.0, -0.10 * factor);
      deformations[2] = Offset(0.0, -0.12 * factor);
      deformations[3] = Offset(0.0, -0.10 * factor);
      
      print('变形数据：');
      deformations.forEach((key, value) {
        print('  点 $key: 偏移 (${value.dx}, ${value.dy})');
      });
      
      print('特征点数据：');
      for (int i = 0; i < _landmarks.length; i++) {
        print('  点 $i: ${_landmarks[i]}');
      }
      
      // 创建变形引擎
      final deformationEngine = ImageDeformationEngine();
      
      // 应用变形
      print('开始应用变形...');
      print('原始图像路径: $_originalImagePath');
      
      // 检查图像文件是否存在
      final imageFile = File(_originalImagePath);
      if (!imageFile.existsSync()) {
        print('错误: 原始图像文件不存在: $_originalImagePath');
        setState(() {
          _isLoading = false;
        });
        return;
      } else {
        print('原始图像文件存在，大小: ${await imageFile.length()} 字节');
      }
      
      final result = await deformationEngine.applyDeformation(
        imagePath: _originalImagePath,
        deformations: deformations,
        landmarks: _landmarks,
      );
      
      if (result.success) {
        setState(() {
          _transformedImagePath = result.outputPath;
          _isLoading = false;
        });
        
        print('变形成功！');
        print('输出路径：${result.outputPath}');
        
        // 检查输出文件是否存在
        final outputFile = File(result.outputPath);
        if (outputFile.existsSync()) {
          print('输出文件存在，大小: ${await outputFile.length()} 字节');
        } else {
          print('警告: 输出文件不存在: ${result.outputPath}');
        }
      } else {
        setState(() {
          _isLoading = false;
        });
        
        print('变形失败：${result.errorMessage}');
      }
    } catch (e, stackTrace) {
      setState(() {
        _isLoading = false;
      });
      
      print('变形过程发生异常：$e');
      print('堆栈跟踪：$stackTrace');
    }
  }
  
  // 切换对比模式
  void _toggleComparisonMode() {
    setState(() {
      _isComparing = !_isComparing;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图像变形测试'),
      ),
      body: Column(
        children: [
          // 图像显示区域
          Expanded(
            child: Container(
              color: Colors.grey[900],
              child: Center(
                child: _buildImageDisplay(),
              ),
            ),
          ),
          
          // 控制面板
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[200],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  '变形程度',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Slider(
                  value: _deformationValue,
                  min: 0.0,
                  max: 1.0,
                  divisions: 10,
                  label: _deformationValue.toStringAsFixed(1),
                  onChanged: (value) {
                    setState(() {
                      _deformationValue = value;
                    });
                  },
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: _isLoading ? null : _applyDeformation,
                      child: _isLoading 
                          ? const SizedBox(
                              width: 20, 
                              height: 20, 
                              child: CircularProgressIndicator(strokeWidth: 2)
                            )
                          : const Text('应用变形'),
                    ),
                    
                    if (_transformedImagePath != null)
                      ElevatedButton(
                        onPressed: _toggleComparisonMode,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isComparing ? Colors.orange : null,
                        ),
                        child: Text(_isComparing ? '退出对比' : '对比效果'),
                      ),
                  ],
                ),
                
                if (_isComparing && _transformedImagePath != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Row(
                      children: [
                        const Text('分隔线位置:'),
                        Expanded(
                          child: Slider(
                            value: _dividerPosition,
                            min: 0.1,
                            max: 0.9,
                            onChanged: (value) {
                              setState(() {
                                _dividerPosition = value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  // 构建图像显示区域
  Widget _buildImageDisplay() {
    if (_isComparing && _transformedImagePath != null) {
      return ImageComparisonWidget(
        originalImagePath: _originalImagePath,
        transformedImagePath: _transformedImagePath!,
        dividerPosition: _dividerPosition,
        dividerColor: Colors.white,
        dividerWidth: 2.0,
      );
    }
    
    return Stack(
      fit: StackFit.expand,
      children: [
        // 图像
        Image.file(
          File(_transformedImagePath ?? _originalImagePath),
          fit: BoxFit.contain,
        ),
        
        // 特征点
        CustomPaint(
          painter: LandmarkPainter(
            landmarks: _landmarks,
            visibleIndexes: _visibleIndexes,
            highlightIndexes: _highlightIndexes,
            primaryHighlightIndexes: _primaryHighlightIndexes,
            secondaryHighlightIndexes: _secondaryHighlightIndexes,
            blinkValue: _blinkValue,
            scale: 1.0,
            offset: Offset.zero,
            showIndexes: true,
          ),
        ),
      ],
    );
  }
}
