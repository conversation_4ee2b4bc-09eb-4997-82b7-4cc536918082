"""Face detection and landmark extraction module."""

import logging
from typing import Optional, Dict, Any

import cv2
import numpy as np
import insightface
from insightface.app import FaceAnalysis

logger = logging.getLogger(__name__)

class FaceDetector:
    """Face detector using InsightFace."""

    def __init__(self) -> None:
        """Initialize face detector."""
        self.app = FaceAnalysis(allowed_modules=['detection', 'landmark_2d_106'])
        self.app.prepare(ctx_id=0, det_size=(640, 640))
        logger.info("Face detector initialized")

    def detect(self, image: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        Detect face and extract landmarks from image.

        Args:
            image: Input image in BGR format (OpenCV)

        Returns:
            Dictionary containing face landmarks and attributes if face is detected,
            None otherwise
        """
        try:
            faces = self.app.get(image)
            if not faces:
                logger.warning("No face detected in image")
                return None

            # Get the first face (assuming single face processing)
            face = faces[0]
            
            return {
                'bbox': face.bbox,
                'landmarks': face.landmark_2d_106,
                'kps': face.kps,
                'gender': face.gender,
                'age': face.age
            }

        except Exception as e:
            logger.error(f"Error in face detection: {str(e)}")
            return None

    def get_face_attributes(self, face_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract face attributes from detection results.

        Args:
            face_info: Face detection results

        Returns:
            Dictionary containing face attributes
        """
        return {
            'gender': face_info['gender'],
            'age': face_info['age'],
            'bbox': face_info['bbox'].tolist(),
            'landmark_count': len(face_info['landmarks'])
        }
