"""Real-time face morphing engine."""
import numpy as np
import logging
from typing import Any, Dict, Optional, Tuple
from scipy.spatial import Delaunay

from .transform_engine import TransformEngine
from .landmark_processor import LandmarkProcessor
from .mesh_generator import MeshGenerator
from .aesthetic_analyzer import AestheticAnalyzer
from .image_processor import ImageProcessor

logger = logging.getLogger(__name__)

class MorphEngine:
    """
    Core morphing engine that integrates all basic modules for face beautification.
    
    This class coordinates different components to achieve face morphing effects:
    - Image preprocessing
    - Landmark detection and processing
    - Mesh generation and optimization
    - Aesthetic analysis
    - Image transformation
    """

    def __init__(self) -> None:
        """Initialize morph engine with all required components."""
        # 初始化基础组件
        self.transform_engine = TransformEngine()
        self.landmark_processor = LandmarkProcessor()
        self.mesh_generator = MeshGenerator()
        self.aesthetic_analyzer = AestheticAnalyzer()
        self.image_processor = ImageProcessor()
        
        # 缓存相关
        self.last_triangulation: Optional[Delaunay] = None
        self.last_landmarks: Optional[np.ndarray] = None
        
        logger.info("Morph engine initialized successfully")

    def apply_morphing(
        self,
        image: np.ndarray,
        landmarks: np.ndarray,
        params: Dict[str, float]
    ) -> np.ndarray:
        """
        Apply morphing effect to the image.

        Args:
            image: Input image array of shape (H, W, C)
            landmarks: Face landmarks array of shape (n_points, 2)
            params: Dictionary containing morphing parameters:
                   - jaw_width: float [-1, 1]
                   - nose_height: float [-1, 1]
                   - eye_size: float [-1, 1]
                   etc.

        Returns:
            Morphed image array of shape (H, W, C)
        """
        try:
            # 1. 图像预处理
            preprocessed = self.image_processor.enhance(image, {
                'brightness': params.get('brightness', 1.0),
                'contrast': params.get('contrast', 1.0)
            })
            
            # 2. 生成变形网格
            warped_landmarks = self._calculate_warped_landmarks(landmarks, params)
            mesh = self.mesh_generator.generate_mesh(warped_landmarks)
            
            # 3. 应用变形
            result = self.transform_engine.apply_transform(
                preprocessed,
                mesh,
                self._get_transform_params(params)
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in morphing process: {str(e)}")
            raise

    def _calculate_warped_landmarks(
        self,
        landmarks: np.ndarray,
        params: dict
    ) -> np.ndarray:
        """
        计算变形后的特征点位置。
        
        根据提供的参数，对特征点进行变形调整。
        
        Args:
            landmarks: 原始特征点数组
            params: 变形参数字典，包含：
                   - jaw_width: 下巴宽度调整，范围[-1, 1]
                   - nose_height: 鼻子高度调整，范围[-1, 1]
                   
        Returns:
            变形后的特征点数组
        """
        import logging
        logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger(__name__)
        
        # 创建结果数组
        result = landmarks.copy()
        
        # 处理每个变形参数
        for param_name, strength in params.items():
            if param_name == 'jaw_width':
                logger.debug(f"Adjusting jaw width with strength {strength}")
                result = self._adjust_jaw_width(result, strength)
            elif param_name == 'nose_height':
                logger.debug(f"Adjusting nose height with strength {strength}")
                result = self._adjust_nose_height(result, strength)
            elif param_name == 'nose_width':
                result = self._adjust_nose_width(result, strength)
            elif param_name == 'eye_size':
                result = self._adjust_eye_size(result, strength)
            elif param_name == 'eye_corners':
                result = self._adjust_eye_corners(result, **strength)
            else:
                logger.warning(f"Unknown morphing parameter: {param_name}")
        
        return result

    def _adjust_jaw_width(
        self,
        landmarks: np.ndarray,
        strength: float
    ) -> np.ndarray:
        """
        调整下巴宽度。
        
        通过移动下巴轮廓点来调整宽度。移动方式：
        1. 中心点不动
        2. 左右点向外移动，移动距离基于到中心点的距离
        
        Args:
            landmarks: 人脸特征点数组
            strength: 调整强度，范围[-1, 1]
                     正值：增加宽度
                     负值：减少宽度

        Returns:
            调整后的特征点数组
        """
        import logging
        logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger(__name__)
        
        # 获取下巴轮廓点索引
        jaw_indices = self.landmark_processor.get_jaw_indices()
        logger.debug(f"Jaw indices: {jaw_indices}")
        
        # 创建结果数组
        result = landmarks.copy()
        
        # 找到最左和最右的点
        left_idx = jaw_indices[0]
        right_idx = jaw_indices[-1]
        
        # 计算当前宽度
        current_width = landmarks[right_idx, 0] - landmarks[left_idx, 0]
        logger.debug(f"Current width: {current_width}")
        
        # 计算移动距离（基于当前宽度的一定比例）
        movement = current_width * strength  # 使用当前宽度作为基准
        logger.debug(f"Movement: {movement}")
        
        # 移动左右点
        result[left_idx, 0] -= movement  # 左点向左移动
        result[right_idx, 0] += movement  # 右点向右移动
        
        return result

    def _adjust_nose_height(
        self,
        landmarks: np.ndarray,
        strength: float
    ) -> np.ndarray:
        """
        调整鼻子高度。
        
        通过移动鼻梁和鼻尖的点来调整鼻子高度。移动方式：
        1. 鼻梁点：垂直方向移动，越靠近鼻尖移动越大
        2. 鼻尖点：垂直方向移动最大
        3. 鼻翼点：随鼻尖移动，保持相对位置
        
        Args:
            landmarks: 人脸特征点数组
            strength: 调整强度，范围[-1, 1]
                     正值：增加高度
                     负值：减少高度

        Returns:
            调整后的特征点数组
        """
        import logging
        logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger(__name__)
        
        # 获取鼻子相关的特征点索引
        nose_bridge = self.landmark_processor.get_nose_bridge_indices()
        nose_tip = self.landmark_processor.get_nose_tip_index()
        nose_wings = self.landmark_processor.get_nose_wing_indices()
        
        logger.debug(f"Nose bridge indices: {nose_bridge}")
        logger.debug(f"Nose tip index: {nose_tip}")
        logger.debug(f"Nose wing indices: {nose_wings}")
        
        # 创建结果数组
        result = landmarks.copy()
        
        # 计算当前鼻子高度（从鼻梁顶点到鼻尖的垂直距离）
        nose_height = landmarks[nose_tip, 1] - landmarks[nose_bridge[0], 1]
        logger.debug(f"Current nose height: {nose_height}")
        
        # 计算移动距离（基于当前高度的一定比例）
        movement = nose_height * strength  # 使用当前高度作为基准
        logger.debug(f"Movement: {movement}")
        
        # 调整鼻梁点（从上到下移动距离逐渐增加）
        for i, idx in enumerate(nose_bridge):
            # 移动距离随着靠近鼻尖而增加
            ratio = (i + 1) / len(nose_bridge)
            point_movement = movement * ratio
            logger.debug(f"Moving nose bridge point {idx} by {point_movement}")
            result[idx, 1] += point_movement
        
        # 调整鼻尖和鼻翼（最大移动距离）
        # 注意：当strength为负值时，移动方向相反
        if strength < 0:
            # 减少高度时，向上移动鼻尖和鼻翼
            result[nose_tip, 1] += movement  # movement已经是负值
            for idx in nose_wings:
                result[idx, 1] += movement
        else:
            # 增加高度时，向下移动鼻尖和鼻翼
            result[nose_tip, 1] += movement
            for idx in nose_wings:
                result[idx, 1] += movement
        
        return result

    def _adjust_nose_width(
        self,
        landmarks: np.ndarray,
        strength: float
    ) -> np.ndarray:
        """
        调整鼻子宽度。
        
        通过移动鼻翼点来调整鼻子宽度。移动方式：
        1. 鼻尖点保持不动
        2. 左右鼻翼点水平移动
        3. 移动距离基于当前鼻子宽度
        
        Args:
            landmarks: 人脸特征点数组
            strength: 调整强度，范围[-1, 1]
                     正值：增加宽度
                     负值：减少宽度

        Returns:
            调整后的特征点数组
        """
        import logging
        logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger(__name__)
        
        # 获取鼻子相关的特征点索引
        nose_tip = self.landmark_processor.get_nose_tip_index()
        nose_wings = self.landmark_processor.get_nose_wing_indices()
        
        logger.debug(f"Nose tip index: {nose_tip}")
        logger.debug(f"Nose wing indices: {nose_wings}")
        
        # 创建结果数组
        result = landmarks.copy()
        
        # 计算当前鼻子宽度（左右鼻翼的水平距离）
        left_wing = landmarks[nose_wings[0]]
        right_wing = landmarks[nose_wings[-1]]
        current_width = right_wing[0] - left_wing[0]
        logger.debug(f"Current nose width: {current_width}")
        
        # 计算移动距离（基于当前宽度的一定比例）
        movement = current_width * strength
        logger.debug(f"Movement: {movement}")
        
        # 调整鼻翼点
        for idx in nose_wings[:len(nose_wings)//2]:  # 左侧鼻翼点
            result[idx, 0] -= movement  # 向左移动
            logger.debug(f"Moving left wing point {idx} by -{movement}")
            
        for idx in nose_wings[len(nose_wings)//2:]:  # 右侧鼻翼点
            result[idx, 0] += movement  # 向右移动
            logger.debug(f"Moving right wing point {idx} by {movement}")
        
        return result

    def _adjust_eye_size(
        self,
        landmarks: np.ndarray,
        strength: float,
        maintain_ratio: bool = True
    ) -> np.ndarray:
        """
        调整眼睛大小，保持美学比例。
        
        通过移动眼睛轮廓点来调整眼睛大小。遵循以下美学原则：
        1. 标准眼裂宽高比约为3:1
        2. 内外眼角比例约为1:3:1（内角:瞳孔:外角）
        3. 上下眼睑弧度自然
        4. 左右眼对称
        
        Args:
            landmarks: 人脸特征点数组
            strength: 调整强度，范围[-1, 1]
                     正值：增大眼睛
                     负值：缩小眼睛
            maintain_ratio: 是否保持眼睛宽高比

        Returns:
            调整后的特征点数组
        """
        import logging
        logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger(__name__)
        
        # 获取眼睛相关的特征点索引
        left_eye = self.landmark_processor.get_left_eye_indices()
        right_eye = self.landmark_processor.get_right_eye_indices()
        
        # 创建结果数组
        result = landmarks.copy()
        
        # 分别处理左右眼
        for eye_indices in [left_eye, right_eye]:
            # 计算眼睛中心点
            eye_points = result[eye_indices]
            center = np.mean(eye_points, axis=0)
            
            # 计算当前眼睛尺寸
            max_y = np.max(eye_points[:, 1])
            min_y = np.min(eye_points[:, 1])
            max_x = np.max(eye_points[:, 0])
            min_x = np.min(eye_points[:, 0])
            eye_height = max_y - min_y
            eye_width = max_x - min_x
            
            # 计算理想的宽高比（3:1）
            if maintain_ratio:
                target_ratio = 3.0
                current_ratio = eye_width / eye_height
                if current_ratio < target_ratio:
                    # 需要增加宽度
                    horizontal_strength = strength * 1.2
                    vertical_strength = strength * 0.8
                else:
                    # 需要增加高度
                    horizontal_strength = strength * 0.8
                    vertical_strength = strength * 1.2
            else:
                horizontal_strength = vertical_strength = strength
            
            # 计算移动距离
            vertical_movement = eye_height * vertical_strength * 0.5
            horizontal_movement = eye_width * horizontal_strength * 0.3
            
            # 调整每个点的位置
            for idx in eye_indices:
                point = result[idx]
                # 根据点的位置决定移动方向
                if abs(point[1] - center[1]) > abs(point[0] - center[0]):
                    # 主要是垂直方向的点（上下眼睑）
                    if point[1] < center[1]:  # 上眼睑点
                        point[1] -= vertical_movement
                    else:  # 下眼睑点
                        point[1] += vertical_movement
                else:
                    # 主要是水平方向的点（内外眼角）
                    if point[0] < center[0]:  # 内眼角点
                        point[0] -= horizontal_movement
                    else:  # 外眼角点
                        point[0] += horizontal_movement
                
                result[idx] = point
                logger.debug(f"Moved point {idx} from {landmarks[idx]} to {result[idx]}")
        
        return result

    def _adjust_eye_corners(self, landmarks: np.ndarray, inner_corner_ratio: float = 0.0,
                        outer_corner_ratio: float = 0.0) -> np.ndarray:
        """调整眼角形状。

        Args:
            landmarks: 人脸特征点数组
            inner_corner_ratio: 内眼角调整比例，范围[-1.0, 1.0]。
                              正值表示内眼角变尖锐，负值表示内眼角变圆润
            outer_corner_ratio: 外眼角调整比例，范围[-1.0, 1.0]。
                              正值表示上扬，负值表示下垂

        Returns:
            调整后的特征点数组
        """
        # 参数验证
        inner_corner_ratio = np.clip(inner_corner_ratio, -1.0, 1.0)
        outer_corner_ratio = np.clip(outer_corner_ratio, -1.0, 1.0)

        # 如果没有调整，直接返回原始特征点
        if inner_corner_ratio == 0.0 and outer_corner_ratio == 0:
            return landmarks.copy()

        result = landmarks.copy()
        
        # 获取眼睛特征点索引
        left_eye_indices = self.landmark_processor.get_left_eye_indices()
        right_eye_indices = self.landmark_processor.get_right_eye_indices()
        
        # 获取内眼角和外眼角的索引
        left_inner_corner_idx = left_eye_indices[0]  # 左眼内眼角
        left_outer_corner_idx = left_eye_indices[2]  # 左眼外眼角
        right_inner_corner_idx = right_eye_indices[0]  # 右眼内眼角
        right_outer_corner_idx = right_eye_indices[2]  # 右眼外眼角

        # 获取眼睛高度（用于限制移动距离）
        left_eye_height = np.linalg.norm(landmarks[left_eye_indices[1]] - landmarks[left_eye_indices[3]])
        right_eye_height = np.linalg.norm(landmarks[right_eye_indices[1]] - landmarks[right_eye_indices[3]])
        eye_height = min(left_eye_height, right_eye_height)  # 使用较小的眼睛高度作为基准

        # 调整内眼角
        if inner_corner_ratio != 0.0:
            # 获取内眼角周围的点
            left_inner_points = [
                landmarks[left_inner_corner_idx],  # 内眼角点
                landmarks[left_eye_indices[1]],    # 上眼睑中点
                landmarks[left_eye_indices[3]]     # 下眼睑中点
            ]
            right_inner_points = [
                landmarks[right_inner_corner_idx],
                landmarks[right_eye_indices[1]],
                landmarks[right_eye_indices[3]]
            ]

            # 计算内眼角的移动方向和距离
            for eye_points, corner_idx, is_left in [(left_inner_points, left_inner_corner_idx, True),
                                                  (right_inner_points, right_inner_corner_idx, False)]:
                # 计算眼角点到上下眼睑中点的向量
                to_upper = eye_points[1] - eye_points[0]
                to_lower = eye_points[2] - eye_points[0]

                # 计算移动距离（基于眼睛高度）
                move_distance = eye_height * 0.1 * abs(inner_corner_ratio)  # 减小系数以限制移动距离

                # 计算移动方向（内眼角的移动方向应该是向眼睛中心）
                direction = np.array([1, 0]) if is_left else np.array([-1, 0])
                
                # 应用移动
                old_pos = landmarks[corner_idx].copy()
                result[corner_idx] = old_pos + direction * move_distance
                logger.debug(f"{'左' if is_left else '右'}眼内眼角: {old_pos} -> {result[corner_idx]}, "
                           f"移动距离: {move_distance}, 方向: {direction}")
        
        # 调整外眼角
        if outer_corner_ratio != 0:
            for is_left, eye_indices in [(True, left_eye_indices), (False, right_eye_indices)]:
                # 获取外眼角索引
                corner_idx = eye_indices[2]

                # 计算移动距离（基于眼睛高度）
                move_distance = eye_height * 0.1 * abs(outer_corner_ratio)  # 使用相同的系数

                # 应用移动
                old_pos = landmarks[corner_idx].copy()
                result[corner_idx] = np.array([
                    old_pos[0],  # 保持x坐标不变
                    old_pos[1] + (move_distance if outer_corner_ratio > 0 else -move_distance)  # 根据ratio决定上扬或下垂
                ], dtype=np.float32)  # 确保数据类型一致
                logger.debug(f"{'左' if is_left else '右'}眼外眼角: {old_pos} -> {result[corner_idx]}, "
                           f"移动距离: {move_distance}, 方向: {'上' if outer_corner_ratio > 0 else '下'}")
        
        return result

    def _create_double_eyelid(
        self,
        landmarks: np.ndarray,
        fold_width: float = 0.7,  # 默认7mm，归一化到0-1范围
        fold_type: str = 'natural',  # 'natural', 'parallel', 'tapered'
        fold_depth: float = 0.5,  # 褶皱深度
        symmetry: float = 1.0,    # 左右对称度，1.0表示完全对称
        curve_strength: float = 0.5  # 褶皱弧度强度
    ) -> np.ndarray:
        """
        创建双眼皮效果。
        
        通过在上眼睑添加自然的褶皱来创建双眼皮效果。主要特点：
        1. 褶皱宽度通常在6-8mm之间
        2. 褶皱走向自然，呈弧形
        3. 内外眼角褶皱渐变过渡
        4. 保持左右对称
        
        Args:
            landmarks: 人脸特征点数组
            fold_width: 双眼皮褶皱宽度，范围[0.4-1.0]，对应4-10mm
            fold_type: 褶皱类型
                     'natural': 自然褶皱，弧度自然
                     'parallel': 平行褶皱，较为规则
                     'tapered': 渐细褶皱，外宽内窄
            fold_depth: 褶皱深度，范围[0-1]，影响褶皱的明显程度
            symmetry: 左右对称度，范围[0-1]
                    1.0表示完全对称
                    0.0表示完全不对称（保持原始状态）
            curve_strength: 褶皱弧度强度，范围[0-1]
                         影响褶皱的弯曲程度

        Returns:
            调整后的特征点数组
        """
        import logging
        logging.basicConfig(level=logging.DEBUG)
        logger = logging.getLogger(__name__)
        
        # 参数验证和调整
        fold_width = np.clip(fold_width, 0.4, 1.0)
        fold_depth = np.clip(fold_depth, 0, 1.0)
        symmetry = np.clip(symmetry, 0, 1.0)
        curve_strength = np.clip(curve_strength, 0, 1.0)
        
        # 获取眼睛相关的特征点索引
        left_eye = self.landmark_processor.get_left_eye_indices()
        right_eye = self.landmark_processor.get_right_eye_indices()
        
        # 创建结果数组
        result = landmarks.copy()
        
        # 计算左右眼的总高度
        left_height = result[left_eye[0], 1] - result[left_eye[2], 1]  # 上眼睑到下眼睑的距离
        right_height = result[right_eye[0], 1] - result[right_eye[2], 1]
        
        # 计算目标褶皱高度比例（相对于眼睛总高度）
        target_ratio = fold_width * fold_depth
        
        # 如果symmetry为0，直接返回原始状态
        if symmetry == 0:
            return result
            
        for eye_indices in [left_eye, right_eye]:
            # 获取眼睛点
            eye_points = result[eye_indices]
            
            # 计算眼睛中心点
            center = np.mean(eye_points, axis=0)
            
            # 获取上眼睑点（假设是第一个点）
            upper_lid_idx = eye_indices[0]
            upper_lid_point = result[upper_lid_idx]
            
            # 计算眼睛尺寸
            max_y = np.max(eye_points[:, 1])
            min_y = np.min(eye_points[:, 1])
            max_x = np.max(eye_points[:, 0])
            min_x = np.min(eye_points[:, 0])
            eye_height = max_y - min_y
            
            # 使用目标比例计算褶皱高度
            fold_height = eye_height * target_ratio
            
            # 创建更多控制点来形成自然的褶皱
            num_segments = 7  # 增加控制点数量
            
            # 根据褶皱类型调整控制点的分布
            if fold_type == 'parallel':
                # 平行褶皱：所有点高度相同
                y_offsets = np.full(num_segments, fold_height)
            elif fold_type == 'tapered':
                # 渐细褶皱：两端较低，中间较高
                x = np.linspace(-1, 1, num_segments)
                y_offsets = fold_height * (1 - 0.3 * np.abs(x))
            else:  # 'natural'
                # 自然褶皱：使用正弦曲线创建更自然的形状
                x = np.linspace(0, np.pi, num_segments)
                y_offsets = fold_height * (0.8 + 0.2 * np.sin(x))
            
            # 生成控制点
            x_coords = np.linspace(min_x, max_x, num_segments)
            fold_points = []
            
            for i in range(num_segments):
                x = x_coords[i]
                # 计算y坐标（从上眼睑向上延伸）
                y = upper_lid_point[1] + y_offsets[i]
                fold_points.append([x, y])
            
            # 存储褶皱点
            if eye_indices is left_eye:
                left_fold_points = fold_points
            else:
                right_fold_points = fold_points
        
        # 应用对称性
        if symmetry > 0:
            # 计算左右眼的基准高度（上眼睑原始位置）
            left_base_y = result[left_eye[0], 1]
            right_base_y = result[right_eye[0], 1]
            
            # 计算左右眼的总高度
            left_height = left_base_y - result[left_eye[2], 1]  # 上眼睑到下眼睑的距离
            right_height = right_base_y - result[right_eye[2], 1]
            
            # 计算左右眼褶皱的相对高度比例
            left_ratio = [(left_base_y - p[1]) / left_height for p in left_fold_points]
            right_ratio = [(right_base_y - p[1]) / right_height for p in right_fold_points]
            
            # 计算平均比例
            avg_ratio = [(l + r) / 2 for l, r in zip(left_ratio, right_ratio)]
            
            # 根据对称度混合比例
            for i in range(len(left_fold_points)):
                # 计算混合后的比例
                if symmetry == 1.0:
                    # 完全对称：使用平均比例
                    left_final_ratio = right_final_ratio = avg_ratio[i]
                else:
                    # 部分对称：保持一定的不对称性
                    left_final_ratio = left_ratio[i] * (1 - symmetry) + avg_ratio[i] * symmetry * 0.8
                    right_final_ratio = right_ratio[i] * (1 - symmetry) + avg_ratio[i] * symmetry * 1.2
                
                # 应用比例到各自的基准高度
                left_fold_points[i][1] = left_base_y - left_final_ratio * left_height
                right_fold_points[i][1] = right_base_y - right_final_ratio * right_height
        
        # 使用插值来创建平滑的褶皱曲线
        for eye_indices, fold_points in [(left_eye, left_fold_points), (right_eye, right_fold_points)]:
            fold_points = np.array(fold_points)
            x_interp = np.linspace(fold_points[0, 0], fold_points[-1, 0], 100)
            # 使用三次样条插值获得更平滑的曲线
            from scipy.interpolate import CubicSpline
            cs = CubicSpline(fold_points[:, 0], fold_points[:, 1])
            y_interp = cs(x_interp)
            
            # 更新上眼睑点的位置
            upper_lid_idx = eye_indices[0]
            upper_lid_point = result[upper_lid_idx]
            # 找到最接近原始上眼睑点x坐标的插值点
            idx = np.argmin(np.abs(x_interp - upper_lid_point[0]))
            result[upper_lid_idx, 1] = y_interp[idx]
            
            logger.debug(f"Created double eyelid for eye {eye_indices}")
            logger.debug(f"Base fold height: {fold_height}")
            logger.debug(f"Original point: {upper_lid_point}")
            logger.debug(f"New point: {result[upper_lid_idx]}")
            logger.debug(f"Number of control points: {len(fold_points)}")
        
        return result

    def _get_transform_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert morphing parameters to transformation parameters.

        Args:
            params: Morphing parameters dictionary

        Returns:
            Transform parameters dictionary
        """
        return {
            'smoothness': params.get('smoothness', 0.5),
            'preserve_edges': params.get('preserve_edges', True),
            'interpolation_method': 'linear'
        }
