"""
AI Beauty - Landmark Processor Module
Author: AI Beauty Team
Date: 2025-01-17
"""

import numpy as np
from typing import List, Tuple, Dict

class LandmarkProcessor:
    """处理面部特征点的工具类"""
    
    def __init__(self):
        # 定义关键点索引
        self._landmark_indices = {
            'left_eye': list(range(36, 42)),
            'right_eye': list(range(42, 48)),
            'left_eyebrow': list(range(17, 22)),
            'right_eyebrow': list(range(22, 27)),
            'nose': list(range(27, 36)),
            'mouth_outer': list(range(48, 60)),
            'mouth_inner': list(range(60, 68)),
            'jaw': list(range(0, 17)),
            'face_boundary': [0, 16]  # 下巴到额头的轮廓线
        }
        
        # 特定点的索引
        self._special_points = {
            'hairline': 71,  # 发际线中点
            'glabella': 27,  # 眉间
            'nose_base': 33,  # 鼻底
            'chin': 8,       # 下巴
            'left_cheek': 1, # 左脸颊
            'right_cheek': 15 # 右脸颊
        }
    
    def get_eye_points(self, landmarks: np.ndarray, side: str) -> np.ndarray:
        """
        获取眼睛的特征点
        
        Args:
            landmarks: 面部特征点数组
            side: 'left' 或 'right'
            
        Returns:
            眼睛特征点数组
        """
        indices = self._landmark_indices[f'{side}_eye']
        return landmarks[indices]
    
    def get_eyebrow_points(self, landmarks: np.ndarray, side: str) -> np.ndarray:
        """获取眉毛的特征点"""
        indices = self._landmark_indices[f'{side}_eyebrow']
        return landmarks[indices]
    
    def get_mouth_points(self, landmarks: np.ndarray, inner: bool = False) -> np.ndarray:
        """获取嘴部的特征点"""
        key = 'mouth_inner' if inner else 'mouth_outer'
        return landmarks[self._landmark_indices[key]]
    
    def get_face_width(self, landmarks: np.ndarray) -> float:
        """计算面部宽度（两侧颧骨距离）"""
        left_cheek = landmarks[self._special_points['left_cheek']]
        right_cheek = landmarks[self._special_points['right_cheek']]
        return np.linalg.norm(right_cheek - left_cheek)
    
    def get_face_midline(self, landmarks: np.ndarray) -> Tuple[float, float, float]:
        """
        计算面部中线方程 (ax + by + c = 0)
        
        Returns:
            (a, b, c) 元组表示直线方程系数
        """
        # 使用眉间点和下巴点确定中线
        glabella = landmarks[self._special_points['glabella']]
        chin = landmarks[self._special_points['chin']]
        
        # 计算直线方程系数
        if glabella[0] == chin[0]:  # 垂直线
            return (1, 0, -glabella[0])
        else:
            a = (glabella[1] - chin[1]) / (glabella[0] - chin[0])
            b = -1
            c = glabella[1] - a * glabella[0]
            return (a, b, c)
    
    # Getter methods for indices
    def get_left_eye_indices(self) -> List[int]:
        return self._landmark_indices['left_eye']
    
    def get_right_eye_indices(self) -> List[int]:
        return self._landmark_indices['right_eye']
    
    def get_left_eyebrow_indices(self) -> List[int]:
        return self._landmark_indices['left_eyebrow']
    
    def get_right_eyebrow_indices(self) -> List[int]:
        return self._landmark_indices['right_eyebrow']
    
    def get_mouth_indices(self) -> List[int]:
        return self._landmark_indices['mouth_outer']
    
    # Getter methods for special points
    def get_hairline_point(self) -> int:
        return self._special_points['hairline']
    
    def get_glabella_point(self) -> int:
        return self._special_points['glabella']
    
    def get_nose_base_point(self) -> int:
        return self._special_points['nose_base']
    
    def get_chin_point(self) -> int:
        return self._special_points['chin']
    
    def get_feature_points(self, feature_name: str) -> List[int]:
        """获取指定面部特征的所有点的索引"""
        return self._landmark_indices.get(feature_name, [])
