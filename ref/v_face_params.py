"""V脸变形参数规范"""

# V脸变形参数规范
V_FACE_PARAMS = {
    'face_slim': {
        'description': "面部瘦身",
        'anatomical_basis': "基于颧骨和下颌骨结构",
        'max_value': 40,  # 增加最大值以获得更明显的效果
        'primary_points': [1, 2, 3, 4, 5, 6, 7, 8, 33, 32, 31, 30, 29, 28, 27, 26],  # 颧骨和下颌线区域
        'secondary_points': [9, 10, 25, 24],  # 过渡点
        'transform_sequence': [
            ('primary', 1.0),    # (点类型, 权重)
            ('secondary', 0.8)   # 增加过渡点权重以获得更平滑的效果
        ],
        'constraints': {
            'min_face_width': 0.7,   # 降低最小面部宽度比以允许更大程度的瘦脸
            'symmetry_tolerance': 0.98  # 保持严格的对称度要求
        }
    },
    
    'chin_width': {
        'description': "下巴宽度",
        'anatomical_basis': "基于下颌骨形态",
        'max_value': 30,  # 增加最大值以获得更明显的效果
        'primary_points': [5, 6, 7, 8, 29, 28, 27, 26],  # 下颌线区域
        'secondary_points': [9, 10, 25, 24],  # 过渡点
        'transform_sequence': [
            ('primary', 1.0),
            ('secondary', 0.7)   # 增加过渡点权重以获得更平滑的效果
        ],
        'constraints': {
            'min_angle': 105,    # 降低最小下颌角度以允许更大程度的变形
            'symmetry_tolerance': 0.98  # 提高对称度要求
        }
    },
    
    'chin_length': {
        'description': "下巴长度",
        'anatomical_basis': "基于下颌骨下缘",
        'max_value': 25,  # 增加最大值以获得更明显的效果
        'primary_points': [17],  # 下巴中心点
        'secondary_points': [16, 18],  # 两侧点
        'transform_sequence': [
            ('primary', 1.0),
            ('secondary', 0.6)   # 增加过渡点权重以获得更平滑的效果
        ],
        'constraints': {
            'max_extension': 30,  # 增加最大延伸距离
            'vertical_ratio': 0.18  # 增加占面部长度比例
        }
    }
}
