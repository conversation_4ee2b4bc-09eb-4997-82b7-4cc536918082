import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import '../../../utils/logger.dart';

/// 图片绘制器
class ImagePainter extends CustomPainter {
  final ui.Image image;
  final Size viewSize;

  ImagePainter({
    required this.image,
    required this.viewSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    try {
      // 计算缩放比例
      final scaleX = viewSize.width / image.width;
      final scaleY = viewSize.height / image.height;
      final scale = scaleX < scaleY ? scaleX : scaleY;

      // 计算偏移量，使图片居中
      final scaledWidth = image.width * scale;
      final scaledHeight = image.height * scale;
      final dx = (viewSize.width - scaledWidth) / 2;
      final dy = (viewSize.height - scaledHeight) / 2;

      // 设置绘制区域
      final rect = Rect.fromLTWH(dx, dy, scaledWidth, scaledHeight);
      
      // 绘制图片
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
        rect,
        Paint(),
      );

      Logger.v('图片绘制器', '绘制图片:');
      Logger.v('图片绘制器', '  - 原始尺寸: ${image.width}x${image.height}');
      Logger.v('图片绘制器', '  - 缩放尺寸: ${scaledWidth}x${scaledHeight}');
      Logger.v('图片绘制器', '  - 缩放比例: $scale');
    } catch (e) {
      Logger.e('图片绘制器', '绘制失败', e);
    }
  }

  @override
  bool shouldRepaint(covariant ImagePainter oldDelegate) {
    return image != oldDelegate.image ||
           viewSize != oldDelegate.viewSize;
  }
}
