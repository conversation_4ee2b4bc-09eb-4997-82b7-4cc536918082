import 'package:flutter/material.dart';

/// 特征点工具类
class FeaturePointUtils {
  /// 将特征点数据转换为屏幕坐标
  static List<Offset> transformPoints(
    List<List<double>> rawPoints,
    Size imageSize,
    Size displaySize,
  ) {
    if (rawPoints.isEmpty) return [];

    // 计算缩放比例
    final scaleX = displaySize.width / imageSize.width;
    final scaleY = displaySize.height / imageSize.height;
    final scale = scaleX < scaleY ? scaleX : scaleY;

    // 计算偏移量以居中显示
    final offsetX = (displaySize.width - imageSize.width * scale) / 2;
    final offsetY = (displaySize.height - imageSize.height * scale) / 2;

    return rawPoints.map((point) {
      return Offset(
        point[0] * scale + offsetX,
        point[1] * scale + offsetY,
      );
    }).toList();
  }

  /// 获取面部特征点之间的连接关系
  static List<List<int>> getConnections() {
    // 这里返回面部特征点的连接关系
    // 例如：[[0,1], [1,2], [2,3]] 表示点0和点1相连，点1和点2相连，点2和点3相连
    return [
      // 轮廓
      [0, 1], [1, 2], [2, 3], [3, 4], [4, 5], [5, 6], [6, 7], [7, 8],
      [8, 9], [9, 10], [10, 11], [11, 12], [12, 13], [13, 14], [14, 15],
      [15, 16],
      
      // 左眉毛
      [17, 18], [18, 19], [19, 20], [20, 21],
      
      // 右眉毛
      [22, 23], [23, 24], [24, 25], [25, 26],
      
      // 鼻梁
      [27, 28], [28, 29], [29, 30],
      
      // 鼻子下方
      [30, 31], [31, 32], [32, 33], [33, 34], [34, 35],
      
      // 左眼
      [36, 37], [37, 38], [38, 39], [39, 40], [40, 41], [41, 36],
      
      // 右眼
      [42, 43], [43, 44], [44, 45], [45, 46], [46, 47], [47, 42],
      
      // 嘴唇外轮廓
      [48, 49], [49, 50], [50, 51], [51, 52], [52, 53], [53, 54],
      [54, 55], [55, 56], [56, 57], [57, 58], [58, 59], [59, 48],
      
      // 嘴唇内轮廓
      [60, 61], [61, 62], [62, 63], [63, 64], [64, 65], [65, 66],
      [66, 67], [67, 60],
    ];
  }
}
