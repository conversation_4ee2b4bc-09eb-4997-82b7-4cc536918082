import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'dart:io';
import '../../../utils/logger.dart';
import 'face_analysis_controller.dart';
import 'scanning_effect_painter.dart';
import 'feature_points_painter.dart';
import 'image_painter.dart';

/// 面部分析视图
class FaceAnalysisView extends StatefulWidget {
  final FaceAnalysisController controller;
  final String imagePath;

  const FaceAnalysisView({
    Key? key,
    required this.controller,
    required this.imagePath,
  }) : super(key: key);

  @override
  State<FaceAnalysisView> createState() => _FaceAnalysisViewState();
}

class _FaceAnalysisViewState extends State<FaceAnalysisView> {
  ui.Image? _image;

  @override
  void initState() {
    super.initState();
    _loadImage();
    widget.controller.addListener(_onControllerUpdate);
  }

  void _onControllerUpdate() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadImage() async {
    try {
      Logger.i('面部分析视图', '加载图片:');
      Logger.i('面部分析视图', '  - 图片路径: ${widget.imagePath}');

      final file = File(widget.imagePath);
      final bytes = await file.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      
      setState(() {
        _image = frame.image;
      });

      Logger.i('面部分析视图', '图片加载完成');
    } catch (e) {
      Logger.e('面部分析视图', '图片加载失败', e);
    }
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onControllerUpdate);
    _image?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (_image == null) return const SizedBox.shrink();

        // 计算图片实际显示尺寸
        final imageAspectRatio = _image!.width / _image!.height;
        final viewAspectRatio = constraints.maxWidth / constraints.maxHeight;
        
        double displayWidth;
        double displayHeight;
        
        if (imageAspectRatio > viewAspectRatio) {
          // 图片比视图更宽，以宽度为基准
          displayWidth = constraints.maxWidth;
          displayHeight = constraints.maxWidth / imageAspectRatio;
        } else {
          // 图片比视图更高，以高度为基准
          displayHeight = constraints.maxHeight;
          displayWidth = constraints.maxHeight * imageAspectRatio;
        }

        final displaySize = Size(displayWidth, displayHeight);
        
        return Stack(
          alignment: Alignment.center,
          children: [
            // 扫描效果
            if (widget.controller.phase == AnimationPhase.scanning)
              CustomPaint(
                size: displaySize,
                painter: ScanningEffectPainter(
                  animation: widget.controller.scanningAnimation,
                  image: _image,
                ),
              ),

            // 特征点
            if (widget.controller.phase == AnimationPhase.showingPoints && 
                widget.controller.facialLandmarks.isNotEmpty)
              CustomPaint(
                size: displaySize,  // 使用实际显示尺寸
                painter: FeaturePointsPainter(
                  animation: widget.controller.pointsAnimation,
                  landmarks: widget.controller.facialLandmarks,
                  displaySize: displaySize,  // 传入实际显示尺寸
                ),
              ),
          ],
        );
      },
    );
  }
}
