import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import '../../../utils/logger.dart';

/// 扫描效果绘制器
class ScanningEffectPainter extends CustomPainter {
  final Animation<double> animation;
  final ui.Image? image;

  ScanningEffectPainter({
    required this.animation,
    this.image,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    // 绘制扫描线
    final scanLineY = size.height * animation.value;
    canvas.drawRect(
      Rect.fromLTWH(0, scanLineY - 2, size.width, 4),
      paint..color = Colors.blue.withOpacity(0.8),
    );

    // 绘制扫描区域
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, scanLineY),
      paint..color = Colors.blue.withOpacity(0.1),
    );

    // 绘制网格效果
    final gridPaint = Paint()
      ..color = Colors.blue.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // 横向网格
    for (var i = 0; i < size.height; i += 20) {
      canvas.drawLine(
        Offset(0, i.toDouble()),
        Offset(size.width, i.toDouble()),
        gridPaint,
      );
    }

    // 纵向网格
    for (var i = 0; i < size.width; i += 20) {
      canvas.drawLine(
        Offset(i.toDouble(), 0),
        Offset(i.toDouble(), size.height),
        gridPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant ScanningEffectPainter oldDelegate) {
    return oldDelegate.animation.value != animation.value;
  }
}
