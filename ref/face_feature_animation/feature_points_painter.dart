import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 特征点绘制器
class FeaturePointsPainter extends CustomPainter {
  final Animation<double> animation;
  final List<List<double>> landmarks;
  final Size displaySize;
  bool _hasLoggedInfo = false;
  
  // 特征点样式常量
  static const double PRIMARY_POINT_RADIUS = 2.0;    // 有特征描述的点
  static const double SECONDARY_POINT_RADIUS = 1.0;  // 无特征描述的点
  
  // 随机数生成器
  final _random = math.Random();
  late final List<double> _randomDelays;
  
  // 特征点索引列表（用于确定点的大小）
  static const List<int> primaryPoints = [
    33, 133, 362, 263,  // 眼睛
    61, 291,            // 瞳孔
    13, 14, 78, 308,    // 眉毛
    0, 17, 78, 292,     // 脸廓
    152, 148, 176, 149, // 唇线
    6, 197, 195, 248,   // 鼻子
  ];

  FeaturePointsPainter({
    required this.animation,
    required this.landmarks,
    required this.displaySize,
  }) : super(repaint: animation) {
    // 为每个特征点生成随机延迟值 (0.0 到 0.3 之间)
    _randomDelays = List.generate(
      landmarks.length,
      (_) => _random.nextDouble() * 0.3
    );
  }

  @override
  void paint(Canvas canvas, Size size) {
    if (landmarks.isEmpty) return;
    
    try {
      // 只在第一次绘制时输出日志
      if (!_hasLoggedInfo) {
        debugPrint('\n============ 特征点绘制详情 ============');
        debugPrint('1. 尺寸信息:');
        debugPrint('   - 画布尺寸: ${size.width.toStringAsFixed(1)} x ${size.height.toStringAsFixed(1)}');
        debugPrint('   - 显示尺寸: ${displaySize.width.toStringAsFixed(1)} x ${displaySize.height.toStringAsFixed(1)}');
        debugPrint('   - 特征点总数: ${landmarks.length}');
        debugPrint('   - 主要特征点数量: ${primaryPoints.length}');
        debugPrint('=======================================\n');
        _hasLoggedInfo = true;
      }

      // 绘制特征点
      for (var i = 0; i < landmarks.length; i++) {
        final point = landmarks[i];
        if (point.length >= 2) {
          // 计算当前点的动画进度（考虑随机延迟）
          final delayedProgress = (animation.value - _randomDelays[i]).clamp(0.0, 1.0);
          
          if (delayedProgress > 0) {  // 只绘制已经开始显示的点
            // 将归一化坐标直接转换为显示坐标（不需要额外缩放）
            final x = point[0] * displaySize.width;
            final y = point[1] * displaySize.height;

            // 根据点的类型决定大小
            final radius = primaryPoints.contains(i) 
                ? PRIMARY_POINT_RADIUS 
                : SECONDARY_POINT_RADIUS;

            final paint = Paint()
              ..color = Colors.blue.withOpacity(delayedProgress)
              ..strokeWidth = 2.0
              ..style = PaintingStyle.fill;

            canvas.drawCircle(
              Offset(x, y),
              radius * delayedProgress,  // 点大小也随动画变化
              paint,
            );
          }
        }
      }
    } catch (e) {
      debugPrint('错误: 特征点绘制失败');
      debugPrint('   - 原因: $e');
    }
  }

  @override
  bool shouldRepaint(FeaturePointsPainter oldDelegate) {
    return animation.value != oldDelegate.animation.value;
  }
}
