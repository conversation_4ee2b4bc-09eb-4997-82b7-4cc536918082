import 'package:flutter/material.dart';
import '../../../utils/logger.dart';
import '../../../services/face_mesh_service.dart';

enum AnimationPhase {
  initial,      // 初始状态
  scanning,     // 扫描中
  showingPoints // 显示特征点
}

/// 面部分析控制器
class FaceAnalysisController extends ChangeNotifier {
  final TickerProvider vsync;
  late final AnimationController _scanningController;
  late final AnimationController _pointsController;
  
  AnimationPhase _phase = AnimationPhase.initial;
  AnimationPhase get phase => _phase;

  List<List<double>> _facialLandmarks = [];
  List<List<double>> get facialLandmarks => _facialLandmarks;

  Animation<double> get scanningAnimation => _scanningController;
  Animation<double> get pointsAnimation => _pointsController;

  /// 主要特征点及其描述
  final Map<int, String> _primaryFeaturePoints = {
    33: '左眉毛外端',
    133: '左眉毛内端',
    362: '右眉毛外端',
    263: '右眉毛内端',
    78: '左眼外角',
    359: '右眼外角',
    1: '左眼内角',
    463: '右眼内角',
    4: '左眼上缘中点',
    386: '右眼上缘中点',
    2: '左眼下缘中点',
    374: '右眼下缘中点',
    49: '鼻尖',
    94: '鼻翼左端',
    324: '鼻翼右端',
    61: '嘴角左端',
    291: '嘴角右端',
    0: '嘴唇上缘中点',
    17: '嘴唇下缘中点',
    14: '下巴尖端',
    234: '左脸颊轮廓点',
    454: '右脸颊轮廓点',
    58: '左耳上端',
    288: '右耳上端',
  };

  FaceAnalysisController({required this.vsync}) {
    _setupAnimations();
  }

  void _setupAnimations() {
    Logger.i('面部分析控制器', '初始化动画');
    
    // 扫描动画控制器
    _scanningController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: vsync,
    );

    // 特征点动画控制器
    _pointsController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: vsync,
    );

    // 添加状态监听
    _scanningController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Logger.i('面部分析控制器', '扫描动画完成');
      }
    });

    _pointsController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Logger.i('面部分析控制器', '特征点动画完成');
      }
    });
  }

  Future<void> startAnalysis(String imagePath, {
    required VoidCallback onComplete,
    required Function(String) onError,
  }) async {
    Logger.i('面部分析控制器', '\n开始面部特征分析:');
    Logger.i('面部分析控制器', '  - 图片路径: $imagePath');
    Logger.i('面部分析控制器', '  - 开始时间: ${DateTime.now().toString()}');

    try {
      // 重置状态
      _phase = AnimationPhase.scanning;
      notifyListeners();

      // 开始扫描动画
      _scanningController.forward();

      // 获取特征点
      final features = await FaceMeshService.getFacialLandmarks(imagePath);
      if (features.isEmpty) {
        throw '未检测到面部特征点';
      }

      _facialLandmarks = features;

      // 记录特征点信息
      Logger.i('面部分析控制器', '\n特征点统计:');
      Logger.i('面部分析控制器', '  - 总特征点数: ${features.length}');
      
      // 记录带描述的特征点
      Logger.i('面部分析控制器', '\n带描述的特征点:');
      _primaryFeaturePoints.forEach((index, description) {
        if (index < features.length) {
          final point = features[index];
          Logger.i('面部分析控制器', '  - 索引 $index: $description');
          Logger.i('面部分析控制器', '    坐标: (${point[0].toStringAsFixed(3)}, ${point[1].toStringAsFixed(3)})');
        }
      });

      // 等待扫描动画完成
      if (_scanningController.status != AnimationStatus.completed) {
        await _scanningController.forward();
      }

      // 开始显示特征点
      _phase = AnimationPhase.showingPoints;
      notifyListeners();
      _pointsController.forward();

      // 完成回调
      onComplete();
      
    } catch (e) {
      Logger.e('面部分析控制器', '\n[错误] 特征分析失败');
      Logger.e('面部分析控制器', '  - 错误信息: $e');
      onError(e.toString());
      
      // 重置状态
      _phase = AnimationPhase.initial;
      _scanningController.reset();
      notifyListeners();
    }
  }

  void reset() {
    _phase = AnimationPhase.initial;
    _scanningController.reset();
    _pointsController.reset();
    _facialLandmarks = [];
    notifyListeners();
    Logger.i('面部分析控制器', '重置状态');
  }

  @override
  void dispose() {
    _scanningController.dispose();
    _pointsController.dispose();
    Logger.i('面部分析控制器', '释放资源');
    super.dispose();
  }
}
