"""V脸规范"""

from typing import Dict, Optional
from .transform_types import FeaturePointSpec, TransformVector
from .v_face_points import V_FACE_POINTS
from .v_face_params import V_FACE_PARAMS

class VFaceSpecs:
    """V脸规范类"""
    
    @staticmethod
    def get_point_spec(index: int) -> Optional[FeaturePointSpec]:
        """获取特征点规范"""
        return V_FACE_POINTS.get(index)
    
    @staticmethod
    def get_param_spec(param_name: str) -> Optional[Dict]:
        """获取参数规范"""
        return V_FACE_PARAMS.get(param_name)
    
    @staticmethod
    def get_transform_vector(point_index: int, param_name: str) -> Optional[TransformVector]:
        """获取变形向量"""
        point_spec = V_FACE_POINTS.get(point_index)
        if not point_spec:
            return None
        return point_spec.transform_vectors.get(param_name)
