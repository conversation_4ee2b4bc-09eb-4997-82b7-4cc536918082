"""V脸特征点规范"""

from .transform_types import FeaturePointSpec, TransformVector

# V脸关键特征点定义
V_FACE_POINTS = {
    # 下巴中心点
    17: FeaturePointSpec(
        index=17,
        name="chin_center",
        description="下巴中心点",
        anatomical_structure="下颌骨中点",
        primary_for=['chin_length'],
        secondary_for=['chin_width'],
        transform_vectors={
            'chin_length': TransformVector(
                direction=(0, 1),  # 垂直向下
                magnitude=1.0
            ),
            'chin_width': TransformVector(
                direction=(0, 0),  # 不参与宽度变形
                magnitude=0.0
            )
        },
        constraints={
            'max_vertical': 30.0,  # 增加最大垂直位移
            'max_horizontal': 5.0   # 保持水平位移限制
        }
    ),
    
    # 左侧颧骨区域点（1-4）
    1: FeaturePointSpec(
        index=1,
        name="cheekbone_left_top",
        description="左侧颧骨上点",
        anatomical_structure="颧骨体上部",
        primary_for=['face_slim'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.95, -0.1),  # 增加向内的比例
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0,  # 增加水平位移限制
            'min_width_ratio': 0.7   # 降低最小宽度比
        },
        symmetry_point=33
    ),
    
    2: FeaturePointSpec(
        index=2,
        name="cheekbone_left_middle",
        description="左侧颧骨中点",
        anatomical_structure="颧骨体中部",
        primary_for=['face_slim'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.9, 0),  # 向内
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0
        },
        symmetry_point=32
    ),
    
    3: FeaturePointSpec(
        index=3,
        name="cheekbone_left_bottom",
        description="左侧颧骨下点",
        anatomical_structure="颧骨体下部",
        primary_for=['face_slim'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.9, 0.1),  # 主要向内，略微向下
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0
        },
        symmetry_point=31
    ),
    
    4: FeaturePointSpec(
        index=4,
        name="cheekbone_left_front",
        description="左侧颧骨前点",
        anatomical_structure="颧骨体前部",
        primary_for=['face_slim'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.9, 0),  # 向内
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0
        },
        symmetry_point=30
    ),
    
    # 左侧下颌线点（5-8）
    5: FeaturePointSpec(
        index=5,
        name="jawline_left_top",
        description="左侧下颌线上点",
        anatomical_structure="下颌支上部",
        primary_for=['face_slim', 'chin_width'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.9, 0),  # 主要向内
                magnitude=1.0
            ),
            'chin_width': TransformVector(
                direction=(-0.95, 0),  # 主要向内
                magnitude=0.9
            )
        },
        constraints={
            'max_vertical': 8.0,
            'max_horizontal': 25.0,
            'min_angle': 105  # 最小下颌角度
        },
        symmetry_point=29
    ),
    
    6: FeaturePointSpec(
        index=6,
        name="jawline_left_middle_top",
        description="左侧下颌线中上点",
        anatomical_structure="下颌支中上部",
        primary_for=['face_slim', 'chin_width'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.9, 0.1),
                magnitude=0.95
            ),
            'chin_width': TransformVector(
                direction=(-0.95, 0.1),
                magnitude=0.95
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 25.0
        },
        symmetry_point=28
    ),
    
    7: FeaturePointSpec(
        index=7,
        name="jawline_left_middle_bottom",
        description="左侧下颌线中下点",
        anatomical_structure="下颌支中下部",
        primary_for=['face_slim', 'chin_width'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.85, 0.2),
                magnitude=0.9
            ),
            'chin_width': TransformVector(
                direction=(-0.9, 0.2),
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 12.0,
            'max_horizontal': 25.0
        },
        symmetry_point=27
    ),
    
    8: FeaturePointSpec(
        index=8,
        name="jawline_left_bottom",
        description="左侧下颌线下点",
        anatomical_structure="下颌支下部",
        primary_for=['face_slim', 'chin_width'],
        secondary_for=['chin_length'],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.8, 0.3),
                magnitude=0.85
            ),
            'chin_width': TransformVector(
                direction=(-0.85, 0.3),
                magnitude=1.0
            ),
            'chin_length': TransformVector(
                direction=(0, 1),
                magnitude=0.3
            )
        },
        constraints={
            'max_vertical': 15.0,
            'max_horizontal': 25.0
        },
        symmetry_point=26
    ),
    
    # 右侧对称点
    26: FeaturePointSpec(
        index=26,
        name="jawline_right_bottom",
        description="右侧下颌线下点",
        anatomical_structure="下颌支下部",
        primary_for=['face_slim', 'chin_width'],
        secondary_for=['chin_length'],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.8, 0.3),
                magnitude=0.85
            ),
            'chin_width': TransformVector(
                direction=(0.85, 0.3),
                magnitude=1.0
            ),
            'chin_length': TransformVector(
                direction=(0, 1),
                magnitude=0.3
            )
        },
        constraints={
            'max_vertical': 15.0,
            'max_horizontal': 25.0
        },
        symmetry_point=8
    ),
    
    27: FeaturePointSpec(
        index=27,
        name="jawline_right_middle_bottom",
        description="右侧下颌线中下点",
        anatomical_structure="下颌支中下部",
        primary_for=['face_slim', 'chin_width'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.85, 0.2),
                magnitude=0.9
            ),
            'chin_width': TransformVector(
                direction=(0.9, 0.2),
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 12.0,
            'max_horizontal': 25.0
        },
        symmetry_point=7
    ),
    
    28: FeaturePointSpec(
        index=28,
        name="jawline_right_middle_top",
        description="右侧下颌线中上点",
        anatomical_structure="下颌支中上部",
        primary_for=['face_slim', 'chin_width'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.9, 0.1),
                magnitude=0.95
            ),
            'chin_width': TransformVector(
                direction=(0.95, 0.1),
                magnitude=0.95
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 25.0
        },
        symmetry_point=6
    ),
    
    29: FeaturePointSpec(
        index=29,
        name="jawline_right_top",
        description="右侧下颌线上点",
        anatomical_structure="下颌支上部",
        primary_for=['face_slim', 'chin_width'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.9, 0),
                magnitude=1.0
            ),
            'chin_width': TransformVector(
                direction=(0.95, 0),
                magnitude=0.9
            )
        },
        constraints={
            'max_vertical': 8.0,
            'max_horizontal': 25.0,
            'min_angle': 105  # 最小下颌角度
        },
        symmetry_point=5
    ),
    
    # 右侧颧骨区域（30-33）的对称点定义
    30: FeaturePointSpec(
        index=30,
        name="cheekbone_right_front",
        description="右侧颧骨前点",
        anatomical_structure="颧骨体前部",
        primary_for=['face_slim'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.9, -0.1),
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0
        },
        symmetry_point=4
    ),
    
    31: FeaturePointSpec(
        index=31,
        name="cheekbone_right_bottom",
        description="右侧颧骨下点",
        anatomical_structure="颧骨体下部",
        primary_for=['face_slim'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.9, 0.1),
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0
        },
        symmetry_point=3
    ),
    
    32: FeaturePointSpec(
        index=32,
        name="cheekbone_right_middle",
        description="右侧颧骨中点",
        anatomical_structure="颧骨体中部",
        primary_for=['face_slim'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.9, 0),
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0
        },
        symmetry_point=2
    ),
    
    33: FeaturePointSpec(
        index=33,
        name="cheekbone_right_top",
        description="右侧颧骨上点",
        anatomical_structure="颧骨体上部",
        primary_for=['face_slim'],
        secondary_for=[],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.9, -0.1),
                magnitude=1.0
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0
        },
        symmetry_point=1
    )
}
