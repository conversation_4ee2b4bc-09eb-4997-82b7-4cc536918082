"""面部轮廓特征点规范"""

from .transform_types import FeaturePointSpec, TransformVector

# 面部轮廓特征点定义
FACE_CONTOUR_POINTS = {
    # 左侧颧骨区域点（1-4）
    1: FeaturePointSpec(
        index=1,
        name="cheekbone_left_top",
        description="左侧颧骨上点",
        anatomical_structure="颧骨体上部",
        primary_for=['face_slim'],
        secondary_for=['cheek_height'],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(-0.95, -0.1),  # 向内和略微向上
                magnitude=1.0,
                falloff=0.5,
                smoothing=0.3
            ),
            'cheek_height': TransformVector(
                direction=(0, 1),  # 垂直方向
                magnitude=0.7,
                falloff=0.6,
                smoothing=0.4
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0,
            'min_width_ratio': 0.7
        },
        symmetry_point=33,
        aesthetic_ratio=1.618  # 黄金分割比
    ),
    
    # 左侧下颌角点（7-11）
    7: FeaturePointSpec(
        index=7,
        name="jaw_angle_left",
        description="左侧下颌角点",
        anatomical_structure="下颌角",
        primary_for=['jaw_angle', 'face_slim'],
        secondary_for=['chin_width'],
        transform_vectors={
            'jaw_angle': TransformVector(
                direction=(-0.7, 0.7),  # 向内上方
                magnitude=1.0,
                angle=45,  # 45度旋转
                falloff=0.4,
                smoothing=0.3
            ),
            'face_slim': TransformVector(
                direction=(-1, 0),  # 水平向内
                magnitude=0.8,
                falloff=0.5,
                smoothing=0.4
            ),
            'chin_width': TransformVector(
                direction=(-0.8, 0.2),  # 主要水平向内
                magnitude=0.6,
                falloff=0.6,
                smoothing=0.4
            )
        },
        constraints={
            'max_vertical': 15.0,
            'max_horizontal': 25.0,
            'min_angle': 110.0
        },
        symmetry_point=27,
        aesthetic_ratio=1.414  # 根2比
    ),
    
    # 下巴中心点（17）
    17: FeaturePointSpec(
        index=17,
        name="chin_center",
        description="下巴中心点",
        anatomical_structure="下颌骨中点",
        primary_for=['chin_length'],
        secondary_for=['chin_width'],
        transform_vectors={
            'chin_length': TransformVector(
                direction=(0, 1),  # 垂直向下
                magnitude=1.0,
                falloff=0.4,
                smoothing=0.3
            ),
            'chin_width': TransformVector(
                direction=(0, 0),  # 不参与宽度变形
                magnitude=0.0
            )
        },
        constraints={
            'max_vertical': 30.0,
            'max_horizontal': 5.0
        },
        aesthetic_ratio=1.618,  # 黄金分割比
        aesthetic_guidelines="下巴长度应为面部总长的1/3"
    ),
    
    # 右侧对称点定义...（根据左侧特征点对称生成）
    33: FeaturePointSpec(
        index=33,
        name="cheekbone_right_top",
        description="右侧颧骨上点",
        anatomical_structure="颧骨体上部",
        primary_for=['face_slim'],
        secondary_for=['cheek_height'],
        transform_vectors={
            'face_slim': TransformVector(
                direction=(0.95, -0.1),  # 向内和略微向上（与左侧对称）
                magnitude=1.0,
                falloff=0.5,
                smoothing=0.3
            ),
            'cheek_height': TransformVector(
                direction=(0, 1),  # 垂直方向
                magnitude=0.7,
                falloff=0.6,
                smoothing=0.4
            )
        },
        constraints={
            'max_vertical': 10.0,
            'max_horizontal': 30.0,
            'min_width_ratio': 0.7
        },
        symmetry_point=1,
        aesthetic_ratio=1.618
    )
}
