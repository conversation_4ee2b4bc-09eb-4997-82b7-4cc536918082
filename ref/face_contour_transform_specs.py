"""面部轮廓变形规范"""

from typing import Dict, List, Tuple
import numpy as np
from .transform_types import TransformVector, FeaturePointSpec
from .face_contour_params import FACE_CONTOUR_PARAMS
from .face_contour_points import FACE_CONTOUR_POINTS

class FaceContourTransformSpecs:
    """面部轮廓变形规范类"""
    
    def __init__(self):
        self.params = FACE_CONTOUR_PARAMS
        self.points = FACE_CONTOUR_POINTS
        
    def get_transform_params(self, transform_type: str) -> Dict:
        """获取指定变形类型的参数
        
        Args:
            transform_type: 变形类型，如 'face_slim', 'chin_width' 等
            
        Returns:
            变形参数字典
        """
        if transform_type not in self.params:
            raise ValueError(f"不支持的变形类型: {transform_type}")
        return self.params[transform_type]
        
    def get_point_spec(self, point_index: int) -> FeaturePointSpec:
        """获取指定点的规范
        
        Args:
            point_index: 特征点索引
            
        Returns:
            特征点规范对象
        """
        if point_index not in self.points:
            raise ValueError(f"未定义的特征点: {point_index}")
        return self.points[point_index]
        
    def get_transform_points(self, transform_type: str) -> Tuple[List[int], List[int]]:
        """获取指定变形类型的主要点和次要点
        
        Args:
            transform_type: 变形类型
            
        Returns:
            (主要点列表, 次要点列表)
        """
        params = self.get_transform_params(transform_type)
        return params['primary_points'], params['secondary_points']
        
    def get_transform_vector(self, point_index: int, transform_type: str) -> TransformVector:
        """获取指定点在指定变形类型下的变形向量
        
        Args:
            point_index: 特征点索引
            transform_type: 变形类型
            
        Returns:
            变形向量对象
        """
        point_spec = self.get_point_spec(point_index)
        if transform_type not in point_spec.transform_vectors:
            raise ValueError(f"点 {point_index} 没有定义 {transform_type} 的变形向量")
        return point_spec.transform_vectors[transform_type]
        
    def validate_transform(self, transform_type: str, value: float) -> bool:
        """验证变形参数是否在安全范围内
        
        Args:
            transform_type: 变形类型
            value: 变形值
            
        Returns:
            是否在安全范围内
        """
        params = self.get_transform_params(transform_type)
        return (params['min_value'] <= value <= params['max_value'] and 
                abs(value) <= params['max_value'] * params['safety_threshold'])
        
    def calculate_transform_weights(self, transform_type: str) -> Dict[int, float]:
        """计算指定变形类型的所有点的权重
        
        Args:
            transform_type: 变形类型
            
        Returns:
            点索引到权重的映射
        """
        params = self.get_transform_params(transform_type)
        weights = {}
        
        # 设置主要点的权重
        for point in params['primary_points']:
            weights[point] = 1.0
            
        # 设置次要点的权重
        for point in params['secondary_points']:
            weights[point] = 0.7
            
        return weights
        
    def get_symmetry_points(self) -> Dict[int, int]:
        """获取所有对称点对
        
        Returns:
            点索引到其对称点索引的映射
        """
        symmetry_pairs = {}
        for point_index, point_spec in self.points.items():
            if point_spec.symmetry_point is not None:
                symmetry_pairs[point_index] = point_spec.symmetry_point
        return symmetry_pairs
        
    def get_aesthetic_ratios(self) -> Dict[int, float]:
        """获取所有点的美学比例
        
        Returns:
            点索引到美学比例的映射
        """
        ratios = {}
        for point_index, point_spec in self.points.items():
            if point_spec.aesthetic_ratio is not None:
                ratios[point_index] = point_spec.aesthetic_ratio
        return ratios
        
    def get_transform_constraints(self, transform_type: str) -> Dict:
        """获取指定变形类型的约束条件
        
        Args:
            transform_type: 变形类型
            
        Returns:
            约束条件字典
        """
        return self.get_transform_params(transform_type)['constraints']
