"""
特征提取和分析演示程序
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from src.feature_processor.feature_extractor import FacialFeatureExtractor
from src.feature_processor.feature_analyzer import FeatureAnalyzer

def visualize_features(image, features, title="Features"):
    """可视化特征点和区域"""
    vis_img = image.copy()
    
    # 绘制2D关键点
    landmarks = features['landmarks_2d']
    for point in landmarks:
        cv2.circle(vis_img, (int(point[0]), int(point[1])), 2, (0, 255, 0), -1)
    
    # 绘制区域
    regions = features['regions']
    colors = {
        'left_eye': (255, 0, 0),    # 蓝色
        'right_eye': (255, 0, 0),   # 蓝色
        'nose': (0, 255, 0),        # 绿色
        'mouth': (0, 0, 255)        # 红色
    }
    
    for region_name, color in colors.items():
        points = regions[region_name]['points']
        center = regions[region_name]['center']
        
        # 绘制区域点
        for point in points:
            cv2.circle(vis_img, (int(point[0]), int(point[1])), 2, color, -1)
            
        # 绘制中心点
        cv2.circle(vis_img, (int(center[0]), int(center[1])), 4, color, -1)
    
    # 绘制脸型轮廓
    contour_points = features['contours']['face']['points']
    contour_points = contour_points.astype(np.int32)
    cv2.polylines(vis_img, [contour_points], True, (255, 255, 0), 2)
    
    plt.figure(figsize=(10, 10))
    plt.imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
    plt.title(title)
    plt.axis('off')
    
def main():
    # 初始化提取器和分析器
    extractor = FacialFeatureExtractor()
    analyzer = FeatureAnalyzer()
    
    # 读取标准测试图片
    source_image = cv2.imread('test_images/a1.jpg')
    target_image = cv2.imread('test_images/a2.jpg')
    
    if source_image is None or target_image is None:
        print("Error: 无法读取测试图片，请确保 test_images/a1.jpg 和 test_images/a2.jpg 存在")
        return
    
    # 提取特征
    source_features = extractor.extract_features(source_image)
    target_features = extractor.extract_features(target_image)
    
    # 分析差异
    differences = analyzer.analyze_difference(source_features, target_features)
    
    # 生成中间特征（50%插值）
    middle_features = analyzer.interpolate_features(source_features, target_features, 0.5)
    
    # 可视化结果
    plt.figure(figsize=(15, 5))
    
    plt.subplot(131)
    visualize_features(source_image, source_features, "Source Features")
    
    plt.subplot(132)
    visualize_features(target_image, target_features, "Target Features")
    
    # 打印主要差异
    print("\n主要特征差异:")
    print(f"2D关键点差异大小: {differences['landmarks_2d']['magnitude']:.2f}")
    print(f"3D关键点差异大小: {differences['landmarks_3d']['magnitude']:.2f}")
    
    for region in ['left_eye', 'right_eye', 'nose', 'mouth']:
        print(f"{region}区域差异大小: {differences['regions'][region]['magnitude']:.2f}")
    
    plt.tight_layout()
    
    # 保存结果到输出目录
    output_dir = 'test_outputs'
    plt.savefig(f'{output_dir}/feature_analysis_result.png')
    plt.close()
    
    print(f"\n可视化结果已保存到 {output_dir}/feature_analysis_result.png")

if __name__ == "__main__":
    main()
