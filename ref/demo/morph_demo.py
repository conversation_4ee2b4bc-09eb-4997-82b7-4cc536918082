"""
面部渐变效果演示
基于 Delaunay 三角剖分和仿射变换实现面部特征的渐变
"""

import os
import cv2
import numpy as np
import scipy.spatial as spatial
from src.image_validator.feature_detector import FeatureDetector

def create_face_mask(img_shape, points, radius=15):
    """创建面部遮罩，使用凸包和羽化效果
    
    Args:
        img_shape: 图像形状 (h, w)
        points: 特征点坐标
        radius: 羽化半径
    """
    mask = np.zeros(img_shape[:2], dtype=np.uint8)
    hull = cv2.convexHull(points.astype(np.int32))
    cv2.fillConvexPoly(mask, hull, 255)
    mask = cv2.GaussianBlur(mask, (radius, radius), 0)
    return mask / 255.0

def bilinear_interpolate(img, coords):
    """双线性插值
    
    Args:
        img: 输入图像
        coords: 坐标点 (x, y)
    """
    int_coords = np.int32(coords)
    x0, y0 = int_coords
    dx, dy = coords - int_coords

    # 获取四个相邻像素
    q11 = img[y0, x0]
    q21 = img[y0, x0+1]
    q12 = img[y0+1, x0]
    q22 = img[y0+1, x0+1]

    # 双线性插值
    btm = q21.T * dx + q11.T * (1 - dx)
    top = q22.T * dx + q12.T * (1 - dx)
    return (top * dy + btm * (1 - dy)).T

def get_grid_coordinates(points):
    """获取特征点范围内的网格坐标"""
    xmin = int(np.min(points[:, 0]))
    xmax = int(np.max(points[:, 0])) + 1
    ymin = int(np.min(points[:, 1]))
    ymax = int(np.max(points[:, 1])) + 1
    
    return np.asarray([
        (x, y) for y in range(ymin, ymax)
        for x in range(xmin, xmax)
    ], np.uint32)

def get_triangle_affine_matrices(triangles, src_points, dst_points):
    """计算每个三角形的仿射变换矩阵"""
    matrices = []
    for tri_indices in triangles:
        src_tri = np.vstack((src_points[tri_indices].T, [1, 1, 1]))
        dst_tri = np.vstack((dst_points[tri_indices].T, [1, 1, 1]))
        mat = np.dot(src_tri, np.linalg.inv(dst_tri))[:2, :]
        matrices.append(mat)
    return np.array(matrices)

def warp_image(src_img, src_points, dst_points):
    """使用 Delaunay 三角剖分和仿射变换进行图像变形
    
    Args:
        src_img: 源图像
        src_points: 源特征点
        dst_points: 目标特征点
    """
    rows, cols = src_img.shape[:2]
    result_img = np.zeros_like(src_img)
    
    # Delaunay 三角剖分
    delaunay = spatial.Delaunay(dst_points)
    
    # 计算所有三角形的仿射变换矩阵
    tri_affines = get_triangle_affine_matrices(
        delaunay.simplices, src_points, dst_points
    )
    
    # 获取目标图像中需要处理的像素坐标
    roi_coords = get_grid_coordinates(dst_points)
    roi_tri_indices = delaunay.find_simplex(roi_coords)
    
    # 对每个三角形进行变形
    for simplex_index in range(len(delaunay.simplices)):
        coords = roi_coords[roi_tri_indices == simplex_index]
        if len(coords) > 0:
            # 计算源图像中的对应坐标
            out_coords = np.dot(
                tri_affines[simplex_index],
                np.vstack((coords.T, np.ones(len(coords))))
            )
            
            # 使用双线性插值获取像素值
            x, y = coords.T
            result_img[y, x] = bilinear_interpolate(src_img, out_coords)
    
    return result_img

def generate_morph_sequence(src_img, src_points, dst_points, num_frames=20):
    """生成渐变序列
    
    Args:
        src_img: 源图像
        src_points: 源特征点
        dst_points: 目标特征点
        num_frames: 生成的帧数
    """
    sequence = []
    for i in range(num_frames):
        # 使用 sine 函数使变化更平滑
        alpha = np.sin((i / (num_frames - 1)) * np.pi / 2)
        
        # 计算当前帧的特征点位置
        current_points = src_points + alpha * (dst_points - src_points)
        
        # 生成当前帧的遮罩
        mask = create_face_mask(src_img.shape, current_points)
        
        # 对图像进行变形
        warped = warp_image(src_img, src_points, current_points)
        
        # 使用遮罩混合结果
        mask = np.expand_dims(mask, axis=2)
        result = (warped * mask + src_img * (1 - mask)).astype(np.uint8)
        
        sequence.append(result)
    
    return sequence

def main():
    """主函数"""
    # 创建输出目录
    output_dir = "output/morph_demo"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 1. 加载并处理图片
        print("\n1. 加载图片...")
        detector = FeatureDetector()
        
        # 读取源图片
        src_img = cv2.imread("test_images/a1.jpg")
        valid, msg, src_info = detector.detect_features(src_img)
        if not valid:
            raise ValueError(f"源图片处理失败: {msg}")
            
        # 读取目标图片
        dst_img = cv2.imread("test_images/a2.jpg")
        valid, msg, dst_info = detector.detect_features(dst_img)
        if not valid:
            raise ValueError(f"目标图片处理失败: {msg}")
        
        # 保存原始图片
        cv2.imwrite(os.path.join(output_dir, "source.jpg"), src_img)
        
        # 2. 生成渐变序列
        print("\n2. 生成渐变序列...")
        sequence = generate_morph_sequence(
            src_img,
            src_info['landmarks'],
            dst_info['landmarks']
        )
        
        # 3. 保存结果
        print("\n3. 保存结果...")
        for i, morphed in enumerate(sequence):
            cv2.imwrite(
                os.path.join(output_dir, f"morphed_{i:02d}.jpg"),
                morphed
            )
        
        # 4. 生成预览视频
        print("\n4. 生成预览视频...")
        video_path = os.path.join(output_dir, "morph_preview.mp4")
        height, width = src_img.shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, 10.0, (width, height))
        
        # 写入源图像
        for _ in range(10):
            out.write(src_img)
        
        # 写入变形序列
        for frame in sequence:
            out.write(frame)
        
        # 写入最后一帧
        for _ in range(10):
            out.write(sequence[-1])
        
        out.release()
        print(f"\n处理完成！结果保存在: {output_dir}")
        
    except Exception as e:
        print(f"\nError: 处理过程中出错：{str(e)}")

if __name__ == "__main__":
    main()
