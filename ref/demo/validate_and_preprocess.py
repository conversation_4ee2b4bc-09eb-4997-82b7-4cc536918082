"""
图片验证与预处理演示脚本
用于可视化展示 A1 任务的完成效果
"""

import os
import cv2
import numpy as np
from src.image_validator.feature_detector import FeatureDetector
from src.image_validator.region_processor import RegionProcessor
from src.image_validator.image_processor import ImageProcessor

def create_visualization(title, images, texts=None):
    """创建可视化展示图，保持原始比例"""
    if texts is None:
        texts = ['' for _ in images]
        
    # 计算每张图片的显示尺寸，保持原始比例
    display_images = []
    max_height = 800  # 设置一个合理的最大高度
    
    for img in images:
        h, w = img.shape[:2]
        if h > max_height:
            # 等比例缩放到最大高度
            scale = max_height / h
            new_width = int(w * scale)
            resized = cv2.resize(img, (new_width, max_height), 
                               interpolation=cv2.INTER_LANCZOS4)
            display_images.append(resized)
        else:
            # 如果图片太小，也等比例放大
            scale = max_height / h
            new_width = int(w * scale)
            resized = cv2.resize(img, (new_width, max_height), 
                               interpolation=cv2.INTER_LANCZOS4)
            display_images.append(resized)
    
    # 计算画布大小
    total_width = sum(img.shape[1] for img in display_images)
    margin = 20
    text_height = 30
    canvas_height = max_height + text_height + 2*margin
    canvas_width = total_width + (len(images)+1)*margin
    
    # 创建画布
    canvas = np.ones((canvas_height, canvas_width, 3), 
                    dtype=np.uint8) * 255
    
    # 添加标题
    cv2.putText(canvas, title, (margin, margin + 20),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0,0,0), 2)
    
    # 放置图片和文字
    x = margin
    for img, text in zip(display_images, texts):
        # 放置图片
        h, w = img.shape[:2]
        y = margin + text_height
        # 确保图片区域大小完全一致
        canvas[y:y+h, x:x+w] = img
        
        # 添加文字
        text_x = x
        text_y = y + h + margin
        cv2.putText(canvas, text, (text_x, text_y),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,0,0), 1)
        
        x += w + margin
        
    return canvas

def visualize_validation_process(source_path, target_path):
    """可视化展示图片验证与预处理过程"""
    # 创建处理器
    feature_detector = FeatureDetector()
    region_processor = RegionProcessor()
    image_processor = ImageProcessor()
    
    # 读取图片
    source = cv2.imread(source_path)
    target = cv2.imread(target_path)
    if source is None or target is None:
        raise ValueError("无法读取图片")
        
    # 保存原始图片的可视化
    original_vis = create_visualization(
        "原始图片",
        [source, target],
        ["源图片", "目标图片"]
    )
    
    # 特征点检测
    valid1, msg1, info1 = feature_detector.detect_features(source)
    valid2, msg2, info2 = feature_detector.detect_features(target)
    if not (valid1 and valid2):
        raise ValueError(f"特征点检测失败: {msg1}, {msg2}")
        
    # 绘制特征点
    def draw_landmarks(image, landmarks):
        img = image.copy()
        for x, y in landmarks:
            cv2.circle(img, (int(x), int(y)), 2, (0,255,0), -1)
        return img
        
    source_landmarks = draw_landmarks(source, info1['landmarks'])
    target_landmarks = draw_landmarks(target, info2['landmarks'])
    
    # 保存特征点检测的可视化
    landmarks_vis = create_visualization(
        "特征点检测结果",
        [source_landmarks, target_landmarks],
        ["源图片特征点", "目标图片特征点"]
    )
    
    # 区域处理
    source_region = region_processor.calculate_face_region(source, info1['landmarks'])
    target_region = region_processor.calculate_face_region(target, info2['landmarks'])
    
    # 绘制区域
    def draw_region(image, region):
        img = image.copy()
        left = int(region['bounds']['left'])
        top = int(region['bounds']['top'])
        right = int(region['bounds']['right'])
        bottom = int(region['bounds']['bottom'])
        cv2.rectangle(img, (left, top), (right, bottom), (0,255,0), 2)
        return img
        
    source_region_vis = draw_region(source_landmarks, source_region)
    target_region_vis = draw_region(target_landmarks, target_region)
    
    # 保存区域处理的可视化
    regions_vis = create_visualization(
        "面部区域定位结果",
        [source_region_vis, target_region_vis],
        ["源图片区域", "目标图片区域"]
    )
    
    # 裁剪和调整
    source_cropped = region_processor.crop_image(source, source_region)
    target_cropped = region_processor.crop_image(target, target_region)
    
    # 保存裁剪结果的可视化
    cropped_vis = create_visualization(
        "最终处理结果",
        [source_cropped, target_cropped],
        ["源图片处理后", "目标图片处理后"]
    )
    
    # 创建输出目录
    output_dir = "demo/output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存可视化结果
    cv2.imwrite(os.path.join(output_dir, "1_original.jpg"), original_vis)
    cv2.imwrite(os.path.join(output_dir, "2_landmarks.jpg"), landmarks_vis)
    cv2.imwrite(os.path.join(output_dir, "3_regions.jpg"), regions_vis)
    cv2.imwrite(os.path.join(output_dir, "4_cropped.jpg"), cropped_vis)
    
    print("可视化结果已保存到 demo/output 目录")
    
if __name__ == "__main__":
    # 使用测试图片进行演示
    source_path = "test_images/a1.jpg"
    target_path = "test_images/a2.jpg"
    
    try:
        visualize_validation_process(source_path, target_path)
        print("演示完成！请查看 demo/output 目录下的可视化结果")
    except Exception as e:
        print(f"演示过程中出现错误: {str(e)}")
