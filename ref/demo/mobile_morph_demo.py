"""
移动端变形系统演示
"""

import os
import cv2
import numpy as np
from src.morph.mobile_morph import MobileMorphing
from src.image_validator.feature_detector import FeatureDetector

def main():
    """主函数"""
    # 创建输出目录
    output_dir = "output/mobile_morph_demo"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 1. 加载并处理图片
        print("\n1. 加载图片...")
        detector = FeatureDetector()
        
        # 读取源图片
        src_img = cv2.imread("test_images/a1.jpg")
        valid, msg, src_info = detector.detect_features(src_img)
        if not valid:
            raise ValueError(f"源图片处理失败: {msg}")
            
        # 读取目标图片
        dst_img = cv2.imread("test_images/a2.jpg")
        valid, msg, dst_info = detector.detect_features(dst_img)
        if not valid:
            raise ValueError(f"目标图片处理失败: {msg}")
        
        # 保存原始图片
        cv2.imwrite(os.path.join(output_dir, "source.jpg"), src_img)
        
        # 2. 生成渐变序列
        print("\n2. 生成渐变序列...")
        morpher = MobileMorphing()
        sequence = morpher.generate_morph_sequence(
            src_img,
            dst_img,
            src_info['landmarks'],
            dst_info['landmarks']
        )
        
        # 3. 保存结果
        print("\n3. 保存结果...")
        for i, morphed in enumerate(sequence):
            cv2.imwrite(
                os.path.join(output_dir, f"morphed_{i:02d}.jpg"),
                morphed
            )
        
        # 4. 生成预览视频
        print("\n4. 生成预览视频...")
        video_path = os.path.join(output_dir, "morph_preview.mp4")
        height, width = src_img.shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, 10.0, (width, height))
        
        # 写入源图像
        for _ in range(10):
            out.write(src_img)
        
        # 写入变形序列
        for frame in sequence:
            out.write(frame)
        
        # 写入最后一帧
        for _ in range(10):
            out.write(sequence[-1])
        
        out.release()
        print(f"\n处理完成！结果保存在: {output_dir}")
        
    except Exception as e:
        print(f"\nError: 处理过程中出错：{str(e)}")

if __name__ == "__main__":
    main()
