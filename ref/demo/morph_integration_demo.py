"""
面部变形系统集成演示
集成 A2.2 的特征提取和新的变形系统
"""

import sys
import cv2
import numpy as np
from PyQt5.QtWidgets import QApplication
from src.morph.mobile_morph import MobileMorphingWidget
from src.image_validator.feature_detector import FeatureDetector

def run_demo():
    """运行演示程序"""
    # 初始化检测器
    detector = FeatureDetector()
    
    # 读取测试图像
    source_img = cv2.imread("test_images/a1.jpg")
    target_img = cv2.imread("test_images/a2.jpg")
    
    if source_img is None or target_img is None:
        print("Error: 无法读取测试图像")
        return
        
    # 转换为 RGB 格式
    source_img = cv2.cvtColor(source_img, cv2.COLOR_BGR2RGB)
    target_img = cv2.cvtColor(target_img, cv2.COLOR_BGR2RGB)
    
    # 使用 A2.2 进行特征检测
    valid, msg, source_info = detector.detect_features(source_img)
    if not valid:
        print(f"Error: 源图像特征检测失败 - {msg}")
        return
        
    valid, msg, target_info = detector.detect_features(target_img)
    if not valid:
        print(f"Error: 目标图像特征检测失败 - {msg}")
        return
    
    # 创建 GUI 应用
    app = QApplication(sys.argv)
    
    # 创建变形系统界面
    widget = MobileMorphingWidget()
    
    # 显示原始图像
    widget.update_display(source_img, target_img)
    
    # 生成变形序列
    sequence = widget.morph_engine.generate_morph_sequence(
        source_img,
        target_img,
        source_info['landmarks'],
        target_info['landmarks']
    )
    
    # 显示第一帧变形结果
    if sequence:
        widget.update_display(source_img, target_img, sequence[0])
    
    # 显示窗口
    widget.setWindowTitle('面部变形演示')
    widget.resize(1200, 800)
    widget.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    run_demo()
