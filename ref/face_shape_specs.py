"""
面部轮廓特征点规格定义
基于InsightFace的106点标注系统
"""

class FaceShapeSpecs:
    """面部轮廓特征点规格"""
    
    def __init__(self):
        # 定义面部区域特征点分组
        self.regions = {
            # 脸部轮廓点 (1-33)
            'face_contour': {
                'left': list(range(1, 17)),      # 左侧轮廓 (1-16)
                'right': list(range(18, 34)),    # 右侧轮廓 (18-33)
                'chin': [17],                    # 下巴中心点
            },
            
            # 眉毛点 (34-51)
            'eyebrows': {
                'left_upper': list(range(34, 39)),    # 左眉上边缘 (34-38)
                'left_lower': list(range(39, 43)),    # 左眉下边缘 (39-42)
                'right_upper': list(range(43, 48)),   # 右眉上边缘 (43-47)
                'right_lower': list(range(48, 52)),   # 右眉下边缘 (48-51)
            },
            
            # 眼睛点 (67-83, 75, 84, 105, 106)
            'eyes': {
                'left_upper': [67, 68, 69, 70],      # 左眼上边缘
                'left_lower': [74, 73, 72, 71],      # 左眼下边缘
                'left_corners': [67, 71],            # 左眼角点(外眼角,内眼角)
                'left_extra': [75, 105],            # 左眼额外点
                'right_upper': [77, 78, 79, 80],     # 右眼上边缘
                'right_lower': [76, 83, 82, 81],     # 右眼下边缘
                'right_corners': [76, 80],           # 右眼角点(内眼角,外眼角)
                'right_extra': [84, 106],            # 右眼额外点
            },
            
            # 鼻子点 (52-66)
            'nose': {
                'bridge': [52, 53, 54, 55],          # 鼻梁中心线
                'left_wing': [56, 57, 58],           # 左鼻翼外侧
                'left_nostril': [59, 60],            # 左鼻孔外侧
                'right_wing': [66, 65, 64],          # 右鼻翼外侧
                'right_nostril': [62, 63],           # 右鼻孔外侧
                'bottom': [61],                      # 鼻底人中
            },
            
            # 嘴唇点 (85-104)
            'mouth': {
                'upper_left': [97, 86, 87],          # 上唇左上侧
                'upper_right': [89, 90, 101],        # 上唇右上侧
                'upper_middle': [88],                # 上唇上侧中心
                'upper_left_lower': [98],            # 上唇左下侧
                'upper_right_lower': [100],          # 上唇右下侧
                'upper_middle_lower': [99],          # 上唇下侧中心
                'lower_left': [96, 104],             # 下唇左上侧
                'lower_right': [102, 92],            # 下唇右上侧
                'lower_middle': [103],               # 下唇上侧中心
                'lower_left_bottom': [95],           # 下唇左下侧
                'lower_right_bottom': [93],          # 下唇右下侧
                'lower_middle_bottom': [94],         # 下唇下侧中心
                'corners': [85, 91],                 # 嘴角点(左,右)
            }
        }
        
        # 定义变形参数对应的特征点
        self.transform_points = {
            'face_slim': {
                'left': list(range(1, 12)),       # 左侧脸部 (1-11)
                'right': list(range(23, 34)),     # 右侧脸部 (23-33)
            },
            'chin_length': {
                'points': list(range(14, 21)),    # 下巴区域 (14-20)
            },
            'chin_width': {
                'left': list(range(11, 15)),      # 左侧下巴 (11-14)
                'right': list(range(20, 24)),     # 右侧下巴 (20-23)
            },
            'jaw_angle': {
                'left': list(range(7, 12)),       # 左侧下颌角 (7-11)
                'right': list(range(23, 28)),     # 右侧下颌角 (23-27)
            },
            'cheek_height': {
                'left': list(range(2, 7)),        # 左侧颧骨 (2-6)
                'right': list(range(28, 33)),     # 右侧颧骨 (28-32)
            },
            'cheek_width': {
                'left': list(range(3, 8)),        # 左侧脸颊 (3-7)
                'right': list(range(27, 32)),     # 右侧脸颊 (27-31)
            }
        }
        
        # 定义对称点映射
        self.symmetric_points = {
            # 脸部轮廓对称点
            1: 33, 2: 32, 3: 31, 4: 30, 5: 29,
            6: 28, 7: 27, 8: 26, 9: 25, 10: 24,
            11: 23, 12: 22, 13: 21, 14: 20, 15: 19,
            16: 18,
            
            # 眉毛对称点
            34: 47, 35: 46, 36: 45, 37: 44, 38: 43,  # 上边缘
            39: 51, 40: 50, 41: 49, 42: 48,          # 下边缘
            
            # 眼睛对称点
            67: 80, 68: 79, 69: 78, 70: 77,          # 上边缘
            74: 81, 73: 82, 72: 83, 71: 76,          # 下边缘
            75: 106, 105: 84,                        # 额外点
            
            # 鼻子对称点
            56: 66, 57: 65, 58: 64,                  # 鼻翼
            59: 63, 60: 62,                          # 鼻孔
            
            # 嘴唇对称点
            85: 91,                                  # 嘴角
            97: 101, 86: 90, 87: 89,                 # 上唇上侧
            98: 100,                                 # 上唇下侧
            96: 92, 104: 102,                        # 下唇上侧
            95: 93                                   # 下唇下侧
        }
        
        # 添加反向映射
        reverse_mapping = {v: k for k, v in self.symmetric_points.items()}
        self.symmetric_points.update(reverse_mapping)
        
        # 定义中线上的点（自对称点）
        self.midline_points = {
            17,     # 下巴尖
            52, 53, 54, 55,  # 鼻梁中心线
            61,     # 鼻底人中
            88,     # 上唇上侧中心
            99,     # 上唇下侧中心
            103,    # 下唇上侧中心
            94      # 下唇下侧中心
        }
        
        # 定义变形强度系数
        self.transform_scales = {
            'face_slim': 4.0,      # 瘦脸强度
            'chin_length': 3.0,    # 下巴长度
            'chin_width': 3.0,     # 下巴宽度
            'jaw_angle': 2.0,      # 下颌角度
            'cheek_height': 3.0,   # 颧骨高度
            'cheek_width': 3.0     # 颧骨宽度
        }
