import 'package:flutter/material.dart';
import '../../core/feature_points_data.dart';
import '../../utils/logger.dart';
import '../services/transformation_service.dart';
import 'nose_deformation_controller.dart';

/// 鼻部塑形管理器
/// 
/// 负责协调左侧面板与鼻部变形控制器的交互
class NoseDeformationManager extends ChangeNotifier {
  /// 日志标签
  static const String _logTag = 'NoseDeformationManager';
  
  /// 变形服务
  final TransformationService _transformationService;
  
  /// 鼻部变形控制器
  late final NoseDeformationController _controller;
  
  /// 鼻部参数映射
  final Map<String, Map<String, dynamic>> _parameters = {};
  
  /// 构造函数
  NoseDeformationManager({
    TransformationService? transformationService,
  }) : _transformationService = transformationService ?? TransformationService() {
    Logger.i(_logTag, '初始化鼻部塑形管理器');
    _initializeParameters();
    _controller = NoseDeformationController(
      landmarks: [], // 初始为空，后续会更新
      parameters: {}, // 初始为空，后续会更新
    );
  }
  
  /// 初始化参数
  void _initializeParameters() {
    Logger.i(_logTag, '初始化鼻部参数');
    
    // 从 feature_points_data.dart 获取鼻部参数配置
    final noseConfig = beautyAreaConfigs['nose'];
    if (noseConfig == null) {
      Logger.e(_logTag, '未找到鼻部区域配置');
      return;
    }
    
    // 初始化参数映射
    for (final entry in noseConfig.parameters.entries) {
      final paramName = entry.key;
      final paramConfig = entry.value;
      
      _parameters[paramName] = {
        'displayName': paramConfig.displayName,
        'value': 0, // 初始值为0
        'min': -10, // 最小值
        'max': 10, // 最大值
        'primaryPoints': paramConfig.primaryPoints,
        'secondaryPoints': paramConfig.secondaryPoints,
        'auxiliaryPoints': paramConfig.auxiliaryPoints,
      };
    }
    
    Logger.d(_logTag, '参数初始化完成，共${_parameters.length}个参数');
  }
  
  /// 获取所有参数
  Map<String, Map<String, dynamic>> getParameters() {
    return Map.from(_parameters);
  }
  
  /// 获取参数值
  int getParameterValue(String paramName) {
    if (!_parameters.containsKey(paramName)) {
      Logger.e(_logTag, '未找到参数: $paramName');
      return 0;
    }
    
    return _parameters[paramName]!['value'] as int;
  }
  
  /// 设置参数值
  void setParameterValue(String paramName, int value) {
    Logger.i(_logTag, '设置参数值');
    Logger.d(_logTag, '  • 参数名称: $paramName');
    Logger.d(_logTag, '  • 参数值: $value');
    
    // 检查参数是否存在
    if (!_parameters.containsKey(paramName)) {
      Logger.e(_logTag, '未找到参数: $paramName');
      return;
    }
    
    // 获取参数范围
    final min = _parameters[paramName]!['min'] as int;
    final max = _parameters[paramName]!['max'] as int;
    
    // 确保值在范围内
    final clampedValue = value.clamp(min, max);
    
    // 设置新值
    _parameters[paramName]!['value'] = clampedValue;
    
    // 应用变形
    _applyDeformation(paramName, clampedValue);
    
    // 通知监听器
    notifyListeners();
  }
  
  /// 调整参数值（增加或减少）
  void adjustParameterValue(String paramName, int adjustment) {
    Logger.i(_logTag, '调整参数值');
    Logger.d(_logTag, '  • 参数名称: $paramName');
    Logger.d(_logTag, '  • 调整量: $adjustment');
    
    // 检查参数是否存在
    if (!_parameters.containsKey(paramName)) {
      Logger.e(_logTag, '未找到参数: $paramName');
      return;
    }
    
    // 获取当前值
    final currentValue = _parameters[paramName]!['value'] as int;
    final min = _parameters[paramName]!['min'] as int;
    final max = _parameters[paramName]!['max'] as int;
    
    // 计算新值（确保在范围内）
    final newValue = (currentValue + adjustment).clamp(min, max);
    
    // 设置新值
    setParameterValue(paramName, newValue);
  }
  
  /// 应用变形
  void _applyDeformation(String paramName, int value) {
    Logger.i(_logTag, '应用变形');
    Logger.d(_logTag, '  • 参数名称: $paramName');
    Logger.d(_logTag, '  • 参数值: $value');
    
    // 根据参数名称选择相应的变形方法
    switch (paramName) {
      case 'bridge_height':
        _controller.applyBridgeHeightDeformation(value / 10.0);
        break;
      case 'tip_adjust':
        _controller.applyTipAdjustDeformation(value / 10.0);
        break;
      case 'nostril_width':
        _controller.applyNostrilWidthDeformation(value / 10.0);
        break;
      case 'base_height':
        _controller.applyBaseHeightDeformation(value / 10.0);
        break;
      default:
        Logger.w(_logTag, '未知的参数名称: $paramName');
    }
    
    // 更新变形服务
    _transformationService.updateLandmarks(_controller.deformedLandmarks);
  }
  
  /// 重置所有参数
  void resetAllParameters() {
    Logger.i(_logTag, '重置所有参数');
    
    // 遍历所有参数，将值重置为0
    for (final paramName in _parameters.keys) {
      _parameters[paramName]!['value'] = 0;
    }
    
    // 重置控制器
    _controller.resetAllParameters();
    
    // 更新变形服务
    _transformationService.updateLandmarks(_controller.deformedLandmarks);
    
    // 通知监听器
    notifyListeners();
  }
  
  /// 获取区域特征点
  List<int> getAreaPoints() {
    Logger.i(_logTag, '获取鼻部区域特征点');
    
    // 从 feature_points_data.dart 获取鼻部区域配置
    final noseConfig = beautyAreaConfigs['nose'];
    if (noseConfig == null) {
      Logger.e(_logTag, '未找到鼻部区域配置');
      return [];
    }
    
    // 收集所有鼻部区域的特征点
    final points = <int>{};
    
    // 遍历所有参数配置
    for (final paramConfig in noseConfig.parameters.values) {
      // 添加主要特征点
      points.addAll(paramConfig.primaryPoints);
      
      // 添加次要特征点
      points.addAll(paramConfig.secondaryPoints);
      
      // 添加辅助特征点
      points.addAll(paramConfig.auxiliaryPoints);
    }
    
    Logger.d(_logTag, '鼻部区域特征点数量: ${points.length}');
    
    return points.toList();
  }
  
  /// 获取参数特征点
  List<int> getParameterPoints(String paramName) {
    Logger.i(_logTag, '获取参数特征点');
    Logger.d(_logTag, '  • 参数名称: $paramName');
    
    // 检查参数是否存在
    if (!_parameters.containsKey(paramName)) {
      Logger.e(_logTag, '未找到参数: $paramName');
      return [];
    }
    
    // 收集该参数的所有特征点
    final points = <int>{};
    
    // 添加主要特征点
    points.addAll(_parameters[paramName]!['primaryPoints'] as List<int>);
    
    // 添加次要特征点
    points.addAll(_parameters[paramName]!['secondaryPoints'] as List<int>);
    
    // 添加辅助特征点
    points.addAll(_parameters[paramName]!['auxiliaryPoints'] as List<int>);
    
    Logger.d(_logTag, '参数特征点数量: ${points.length}');
    
    return points.toList();
  }
  
  /// 应用变形效果
  Future<void> applyDeformation() async {
    Logger.i(_logTag, '应用所有变形效果');
    
    // 收集所有非零参数
    final effectiveParams = <String, int>{};
    for (final entry in _parameters.entries) {
      final value = entry.value['value'] as int;
      if (value != 0) {
        effectiveParams[entry.key] = value;
      }
    }
    
    Logger.d(_logTag, '有效参数数量: ${effectiveParams.length}');
    
    if (effectiveParams.isEmpty) {
      Logger.w(_logTag, '没有需要应用的变形效果');
      return;
    }
    
    // 应用所有变形效果
    for (final entry in effectiveParams.entries) {
      _applyDeformation(entry.key, entry.value);
    }
    
    // 更新变形服务
    _transformationService.updateLandmarks(_controller.deformedLandmarks);
    
    Logger.i(_logTag, '变形效果应用完成');
  }
  
  /// 更新特征点
  void updateLandmarks(List<Map<String, dynamic>> landmarks) {
    Logger.i(_logTag, '更新特征点');
    Logger.d(_logTag, '  • 特征点数量: ${landmarks.length}');
    
    // 更新控制器的特征点
    _controller.updateLandmarks(landmarks);
    
    // 应用当前的变形效果
    applyDeformation();
  }
}
