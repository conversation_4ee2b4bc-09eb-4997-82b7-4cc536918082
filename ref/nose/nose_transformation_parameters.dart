import 'package:flutter/material.dart';
import '../../utils/logger.dart';

/// 鼻部参数类型
enum NoseParameterType {
  /// 鼻梁高度
  bridgeHeight,
  
  /// 鼻梁宽度
  bridgeWidth,
  
  /// 鼻尖长度
  tipLength,
  
  /// 鼻尖高度
  tipHeight,
  
  /// 鼻尖宽度
  tipWidth,
  
  /// 鼻孔大小
  nostrilSize,
  
  /// 鼻翼宽度
  nostrilWidth,
  
  /// 鼻基高度
  baseHeight,
  
  /// 鼻基宽度
  baseWidth,
}

/// 参数约束
class ParameterConstraint {
  /// 最小值
  final double min;
  
  /// 最大值
  final double max;
  
  /// 默认值
  final double defaultValue;
  
  /// 构造函数
  const ParameterConstraint({
    required this.min,
    required this.max,
    this.defaultValue = 0.0,
  });
  
  /// 创建默认约束
  factory ParameterConstraint.defaultConstraint() {
    return const ParameterConstraint(
      min: -10.0,
      max: 10.0,
      defaultValue: 0.0,
    );
  }
  
  /// 创建自定义约束
  factory ParameterConstraint.custom({
    double min = -10.0,
    double max = 10.0,
    double defaultValue = 0.0,
  }) {
    return ParameterConstraint(
      min: min,
      max: max,
      defaultValue: defaultValue,
    );
  }
}

/// 鼻部变形参数
class NoseTransformationParameter {
  /// 参数类型
  final NoseParameterType type;
  
  /// 参数值
  double _value;
  double get value => _value;
  
  /// 参数约束
  final ParameterConstraint constraint;
  
  /// 参数变化回调
  final ValueChanged<double>? onChanged;
  
  /// 构造函数
  NoseTransformationParameter({
    required this.type,
    double? value,
    ParameterConstraint? constraint,
    this.onChanged,
  }) : 
    constraint = constraint ?? ParameterConstraint.defaultConstraint(),
    _value = value ?? 0.0;
  
  /// 更新参数值
  void updateValue(double newValue) {
    // 确保值在约束范围内
    final clampedValue = newValue.clamp(constraint.min, constraint.max);
    
    // 如果值没有变化，直接返回
    if (_value == clampedValue) return;
    
    // 更新值
    _value = clampedValue;
    
    // 调用回调
    onChanged?.call(_value);
  }
  
  /// 重置参数值
  void reset() {
    updateValue(constraint.defaultValue);
  }
}

/// 鼻部变形参数集合
class NoseTransformationParameters extends ChangeNotifier {
  /// 日志标签
  static const String _logTag = 'NoseTransformationParameters';
  
  /// 参数映射
  final Map<NoseParameterType, NoseTransformationParameter> _parameters = {};
  
  /// 构造函数
  NoseTransformationParameters({
    Map<NoseParameterType, NoseTransformationParameter>? initialParameters,
  }) {
    Logger.i(_logTag, '初始化鼻部变形参数');
    
    // 如果提供了初始参数，则使用它们
    if (initialParameters != null) {
      _parameters.addAll(initialParameters);
    }
    
    Logger.i(_logTag, '鼻部变形参数初始化完成，共${_parameters.length}个参数');
  }
  
  /// 创建默认参数集合
  factory NoseTransformationParameters.createDefault() {
    Logger.i(_logTag, '创建默认参数集合');
    
    // 创建参数映射
    final parameters = <NoseParameterType, NoseTransformationParameter>{};
    
    // 添加所有参数
    for (final type in NoseParameterType.values) {
      parameters[type] = NoseTransformationParameter(
        type: type,
        value: 0.0,
        constraint: _getDefaultConstraintForType(type),
      );
    }
    
    return NoseTransformationParameters(
      initialParameters: parameters,
    );
  }
  
  /// 获取指定类型的默认约束
  static ParameterConstraint _getDefaultConstraintForType(NoseParameterType type) {
    switch (type) {
      case NoseParameterType.bridgeHeight:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      case NoseParameterType.bridgeWidth:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      case NoseParameterType.tipLength:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      case NoseParameterType.tipHeight:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      case NoseParameterType.tipWidth:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      case NoseParameterType.nostrilSize:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      case NoseParameterType.nostrilWidth:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      case NoseParameterType.baseHeight:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      case NoseParameterType.baseWidth:
        return ParameterConstraint.custom(min: -10.0, max: 10.0);
      default:
        return ParameterConstraint.defaultConstraint();
    }
  }
  
  /// 获取参数
  NoseTransformationParameter? getParameter(NoseParameterType type) {
    return _parameters[type];
  }
  
  /// 更新参数
  void updateParameter(NoseParameterType type, double value) {
    Logger.i(_logTag, '更新参数');
    Logger.d(_logTag, '  • 参数类型: ${type.toString().split('.').last}');
    Logger.d(_logTag, '  • 参数值: $value');
    
    // 检查参数是否存在
    if (!_parameters.containsKey(type)) {
      Logger.w(_logTag, '参数不存在，创建新参数');
      
      // 创建新参数
      _parameters[type] = NoseTransformationParameter(
        type: type,
        value: value,
        constraint: _getDefaultConstraintForType(type),
      );
    } else {
      // 更新现有参数
      _parameters[type]!.updateValue(value);
    }
    
    // 通知监听器
    notifyListeners();
  }
  
  /// 重置所有参数
  void resetAllParameters() {
    Logger.i(_logTag, '重置所有参数');
    
    // 重置所有参数
    for (final param in _parameters.values) {
      param.reset();
    }
    
    // 通知监听器
    notifyListeners();
  }
}
