import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../beautify_feature/models/nose_transformation_parameters.dart';
import '../../beautify_feature/services/nose_transformation_integration_service.dart';
import '../../utils/logger.dart';
import '../common/parameter_slider.dart';
import '../common/section_header.dart';

/// 鼻部变形控制面板
/// 
/// 提供用于调整鼻部变形参数的UI控件
class NoseTransformationControls extends StatefulWidget {
  /// 构造函数
  const NoseTransformationControls({Key? key}) : super(key: key);

  @override
  State<NoseTransformationControls> createState() => _NoseTransformationControlsState();
}

class _NoseTransformationControlsState extends State<NoseTransformationControls> {
  /// 日志标签
  static const String _logTag = 'NoseTransformationControls';
  
  /// 是否已初始化
  bool _isInitialized = false;
  
  /// 参数映射
  final Map<NoseParameterType, String> _parameterLabels = {
    NoseParameterType.bridgeHeight: '鼻梁高度',
    NoseParameterType.bridgeWidth: '鼻梁宽度',
    NoseParameterType.tipLength: '鼻尖长度',
    NoseParameterType.tipHeight: '鼻尖高度',
    NoseParameterType.tipWidth: '鼻尖宽度',
    NoseParameterType.nostrilSize: '鼻孔大小',
    NoseParameterType.nostrilWidth: '鼻翼宽度',
    NoseParameterType.baseHeight: '鼻基高度',
    NoseParameterType.baseWidth: '鼻基宽度',
  };
  
  @override
  void initState() {
    super.initState();
    Logger.i(_logTag, '初始化鼻部变形控制面板');
    
    // 延迟初始化，确保服务已准备好
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initialize();
    });
  }
  
  /// 初始化控制面板
  void _initialize() {
    if (_isInitialized) return;
    
    try {
      Logger.i(_logTag, '开始初始化控制面板');
      
      // 获取服务
      final service = Provider.of<NoseTransformationIntegrationService>(context, listen: false);
      
      // 记录所有参数
      for (final type in NoseParameterType.values) {
        final param = service.getParameterValue(type);
        if (param != null) {
          Logger.d(_logTag, '参数: ${type.toString().split('.').last}, 值: ${param.value}, 范围: [${param.constraint.min}, ${param.constraint.max}]');
        }
      }
      
      _isInitialized = true;
      Logger.i(_logTag, '控制面板初始化完成');
      
      // 强制更新UI
      if (mounted) setState(() {});
    } catch (e) {
      Logger.e(_logTag, '初始化控制面板失败: $e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<NoseTransformationIntegrationService>(
      builder: (context, service, child) {
        // 如果未初始化，显示加载指示器
        if (!_isInitialized) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              const SectionHeader(title: '鼻部塑形'),
              const SizedBox(height: 16.0),
              
              // 鼻梁部分
              _buildSectionHeader('鼻梁调整'),
              _buildParameterSlider(context, service, NoseParameterType.bridgeHeight),
              _buildParameterSlider(context, service, NoseParameterType.bridgeWidth),
              const SizedBox(height: 16.0),
              
              // 鼻尖部分
              _buildSectionHeader('鼻尖调整'),
              _buildParameterSlider(context, service, NoseParameterType.tipHeight),
              _buildParameterSlider(context, service, NoseParameterType.tipLength),
              _buildParameterSlider(context, service, NoseParameterType.tipWidth),
              const SizedBox(height: 16.0),
              
              // 鼻翼部分
              _buildSectionHeader('鼻翼调整'),
              _buildParameterSlider(context, service, NoseParameterType.nostrilWidth),
              _buildParameterSlider(context, service, NoseParameterType.nostrilSize),
              const SizedBox(height: 16.0),
              
              // 鼻基部分
              _buildSectionHeader('鼻基调整'),
              _buildParameterSlider(context, service, NoseParameterType.baseHeight),
              _buildParameterSlider(context, service, NoseParameterType.baseWidth),
              const SizedBox(height: 16.0),
              
              // 重置按钮
              _buildResetButton(context, service),
              const SizedBox(height: 16.0),
              
              // 医学建议
              if (service.medicalAdvice.isNotEmpty)
                _buildMedicalAdvice(service.medicalAdvice),
            ],
          ),
        );
      },
    );
  }
  
  /// 构建小节标题
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16.0,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  /// 构建参数滑块
  Widget _buildParameterSlider(
    BuildContext context,
    NoseTransformationIntegrationService service,
    NoseParameterType type,
  ) {
    // 获取参数
    final param = service.getParameterValue(type);
    if (param == null) {
      Logger.w(_logTag, '未找到参数: ${type.toString().split('.').last}');
      return const SizedBox.shrink();
    }
    
    // 获取参数标签
    final label = _parameterLabels[type] ?? type.toString().split('.').last;
    
    // 构建滑块
    return ParameterSlider(
      label: label,
      value: param.value,
      min: param.constraint.min,
      max: param.constraint.max,
      onChanged: (value) {
        Logger.d(_logTag, '参数变化: $label, 值: $value');
        service.updateParameter(type, value);
      },
    );
  }
  
  /// 构建重置按钮
  Widget _buildResetButton(
    BuildContext context,
    NoseTransformationIntegrationService service,
  ) {
    return Center(
      child: ElevatedButton.icon(
        onPressed: () {
          Logger.i(_logTag, '重置所有参数');
          service.resetAllParameters();
        },
        icon: const Icon(Icons.refresh),
        label: const Text('重置所有参数'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
        ),
      ),
    );
  }
  
  /// 构建医学建议
  Widget _buildMedicalAdvice(List<String> advice) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.amber),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.amber),
              SizedBox(width: 8.0),
              Text(
                '医学建议',
                style: TextStyle(
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8.0),
          ...advice.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                Expanded(child: Text(item)),
              ],
            ),
          )),
        ],
      ),
    );
  }
}
