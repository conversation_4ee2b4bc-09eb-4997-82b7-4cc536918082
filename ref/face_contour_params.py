"""面部轮廓变形参数规范"""

# 面部轮廓变形参数规范
FACE_CONTOUR_PARAMS = {
    'face_slim': {
        'description': "面部瘦身",
        'anatomical_basis': "基于颧骨和下颌骨结构",
        'max_value': 50,
        'min_value': -50,
        'default_value': 0,
        'primary_points': [1, 2, 3, 4, 5, 6, 7, 8, 33, 32, 31, 30, 29, 28, 27, 26],  # 颧骨和下颌线区域
        'secondary_points': [9, 10, 25, 24],  # 过渡点
        'transform_sequence': [
            ('primary', 1.0),    # (点类型, 权重)
            ('secondary', 0.8)   # 过渡点权重
        ],
        'constraints': {
            'min_face_width': 0.7,   # 最小面部宽度比
            'symmetry_tolerance': 0.98  # 对称度要求
        },
        'medical_reference': "面部轮廓改善术、面部注射瘦脸针",
        'safety_threshold': 0.8  # 安全阈值
    },
    
    'chin_width': {
        'description': "下巴宽度",
        'anatomical_basis': "基于下颌骨形态",
        'max_value': 30,
        'min_value': -30,
        'default_value': 0,
        'primary_points': [11, 12, 13, 14, 20, 21, 22, 23],  # 下颌线区域
        'secondary_points': [15, 16, 18, 19],  # 过渡点
        'transform_sequence': [
            ('primary', 1.0),
            ('secondary', 0.7)
        ],
        'constraints': {
            'min_angle': 105,    # 最小下颌角度
            'symmetry_tolerance': 0.98
        },
        'medical_reference': "下颌角截骨术",
        'safety_threshold': 0.85
    },
    
    'chin_length': {
        'description': "下巴长度",
        'anatomical_basis': "基于下颌骨下缘",
        'max_value': 30,
        'min_value': -30,
        'default_value': 0,
        'primary_points': [17],  # 下巴中心点
        'secondary_points': [16, 18],  # 两侧点
        'transform_sequence': [
            ('primary', 1.0),
            ('secondary', 0.6)
        ],
        'constraints': {
            'max_extension': 30,  # 最大延伸距离
            'vertical_ratio': 0.18  # 占面部长度比例
        },
        'medical_reference': "下巴截骨术、假体植入",
        'safety_threshold': 0.9
    },
    
    'jaw_angle': {
        'description': "下颌角度",
        'anatomical_basis': "基于下颌角结构",
        'max_value': 20,
        'min_value': -20,
        'default_value': 0,
        'primary_points': [7, 8, 9, 10, 11, 23, 24, 25, 26, 27],  # 下颌角区域
        'secondary_points': [6, 12, 22, 28],  # 过渡点
        'transform_sequence': [
            ('primary', 1.0),
            ('secondary', 0.75)
        ],
        'constraints': {
            'min_angle': 110,    # 最小角度
            'max_angle': 140,    # 最大角度
            'symmetry_tolerance': 0.98
        },
        'medical_reference': "下颌角截骨术、磨骨术",
        'safety_threshold': 0.85
    },
    
    'cheek_height': {
        'description': "颧骨高度",
        'anatomical_basis': "基于颧骨结构",
        'max_value': 30,
        'min_value': -30,
        'default_value': 0,
        'primary_points': [2, 3, 4, 5, 6, 28, 29, 30, 31, 32],  # 颧骨区域
        'secondary_points': [1, 7, 27, 33],  # 过渡点
        'transform_sequence': [
            ('primary', 1.0),
            ('secondary', 0.7)
        ],
        'constraints': {
            'max_displacement': 25,  # 最大位移
            'symmetry_tolerance': 0.98
        },
        'medical_reference': "颧骨降低术、颧骨内推术",
        'safety_threshold': 0.85
    },
    
    'cheek_width': {
        'description': "颧骨宽度",
        'anatomical_basis': "基于颧骨体积",
        'max_value': 30,
        'min_value': -30,
        'default_value': 0,
        'primary_points': [3, 4, 5, 6, 7, 27, 28, 29, 30, 31],  # 颧骨区域
        'secondary_points': [2, 8, 26, 32],  # 过渡点
        'transform_sequence': [
            ('primary', 1.0),
            ('secondary', 0.7)
        ],
        'constraints': {
            'min_width': 0.75,   # 最小宽度比
            'symmetry_tolerance': 0.98
        },
        'medical_reference': "颧骨内推术",
        'safety_threshold': 0.85
    }
}
