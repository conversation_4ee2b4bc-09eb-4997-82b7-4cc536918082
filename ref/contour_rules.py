"""面部轮廓调整规则和约束

本模块定义了基于医疗美容实践的面部轮廓调整规则，包括：
1. 各部位的安全调整范围
2. 不同脸型的特征参数
3. 调整建议和注意事项
"""

from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Tuple


class FeaturePoint(Enum):
    """面部特征点定义"""
    TEMPLE = "temple"          # 太阳穴
    CHEEK = "cheek"           # 脸颊
    JAW_ANGLE = "jaw_angle"   # 下颌角
    CHIN_SIDE = "chin_side"   # 下巴侧面


class FaceShape(Enum):
    """基础脸型定义"""
    V_SHAPE = "v_shape"       # V脸
    OVAL = "oval"             # 鹅蛋脸
    ROUND = "round"           # 圆脸
    SQUARE = "square"         # 方脸


@dataclass
class SafetyBoundary:
    """安全边界定义"""
    min_ratio: float  # 最小调整比例
    max_ratio: float  # 最大调整比例
    step_size: float  # 建议调整步长


class ContourAdjustmentRules:
    """面部轮廓调整规则集"""

    # 各部位的安全调整范围（基于医疗美容实践）
    SAFETY_BOUNDARIES: Dict[FeaturePoint, SafetyBoundary] = {
        FeaturePoint.TEMPLE: SafetyBoundary(
            min_ratio=-0.08,
            max_ratio=0.08,
            step_size=0.02
        ),
        FeaturePoint.CHEEK: SafetyBoundary(
            min_ratio=-0.15,
            max_ratio=0.15,
            step_size=0.03
        ),
        FeaturePoint.JAW_ANGLE: SafetyBoundary(
            min_ratio=-0.20,
            max_ratio=0.20,
            step_size=0.05
        ),
        FeaturePoint.CHIN_SIDE: SafetyBoundary(
            min_ratio=-0.12,
            max_ratio=0.12,
            step_size=0.03
        )
    }

    # 标准脸型参考参数
    FACE_SHAPE_PARAMS: Dict[FaceShape, Dict[FeaturePoint, float]] = {
        FaceShape.V_SHAPE: {
            FeaturePoint.TEMPLE: 0.08,    # 轻微内收，考虑颞部神经和血管
            FeaturePoint.CHEEK: 0.15,     # 通过软组织调整
            FeaturePoint.JAW_ANGLE: 0.20,  # 下颌角手术可达程度
            FeaturePoint.CHIN_SIDE: 0.12   # 考虑下颌骨结构限制
        },
        FaceShape.OVAL: {
            FeaturePoint.TEMPLE: 0.05,    # 最小调整
            FeaturePoint.CHEEK: 0.12,     # 适度收窄
            FeaturePoint.JAW_ANGLE: 0.15,  # 温和圆润
            FeaturePoint.CHIN_SIDE: 0.10   # 保持自然曲线
        },
        FaceShape.ROUND: {
            FeaturePoint.TEMPLE: -0.05,   # 轻微外扩
            FeaturePoint.CHEEK: -0.08,    # 适度丰满
            FeaturePoint.JAW_ANGLE: -0.10, # 圆润过渡
            FeaturePoint.CHIN_SIDE: -0.07  # 柔和线条
        },
        FaceShape.SQUARE: {
            FeaturePoint.TEMPLE: -0.06,   # 轻微外扩
            FeaturePoint.CHEEK: -0.10,    # 适度丰满
            FeaturePoint.JAW_ANGLE: -0.12, # 保持棱角
            FeaturePoint.CHIN_SIDE: -0.08  # 自然过渡
        }
    }

    # 调整注意事项
    ADJUSTMENT_NOTES: Dict[FeaturePoint, List[str]] = {
        FeaturePoint.TEMPLE: [
            "颞部包含重要神经和血管，调整需谨慎",
            "建议每次调整不超过2%",
            "注意与发际线的自然过渡"
        ],
        FeaturePoint.CHEEK: [
            "主要通过软组织调整实现",
            "需考虑面部脂肪分布",
            "保持与太阳穴的自然过渡",
            "避免过度收缩导致凹陷"
        ],
        FeaturePoint.JAW_ANGLE: [
            "调整幅度最大的区域",
            "需考虑咀嚼功能影响",
            "注意保持两侧对称",
            "渐进式调整更安全"
        ],
        FeaturePoint.CHIN_SIDE: [
            "需考虑下颌骨结构",
            "与下颌角协调调整",
            "注意保持下巴整体形态",
            "避免过度尖削"
        ]
    }

    @classmethod
    def validate_adjustment(cls, feature: FeaturePoint, ratio: float) -> bool:
        """验证调整参数是否在安全范围内"""
        boundary = cls.SAFETY_BOUNDARIES[feature]
        return boundary.min_ratio <= ratio <= boundary.max_ratio

    @classmethod
    def get_safe_ratio(cls, feature: FeaturePoint, ratio: float) -> float:
        """获取安全的调整比例"""
        boundary = cls.SAFETY_BOUNDARIES[feature]
        return max(boundary.min_ratio, min(boundary.max_ratio, ratio))

    @classmethod
    def get_adjustment_suggestions(cls, feature: FeaturePoint, current_ratio: float) -> List[str]:
        """获取调整建议"""
        boundary = cls.SAFETY_BOUNDARIES[feature]
        suggestions = cls.ADJUSTMENT_NOTES[feature].copy()
        
        if abs(current_ratio) > boundary.max_ratio * 0.8:
            suggestions.append("警告：接近安全调整上限")
        
        if abs(current_ratio) > boundary.step_size * 2:
            suggestions.append(f"建议分次调整，每次不超过{boundary.step_size * 100}%")
            
        return suggestions

    @classmethod
    def get_recommended_step(cls, feature: FeaturePoint, target_ratio: float, current_ratio: float) -> float:
        """获取推荐的单次调整步长"""
        boundary = cls.SAFETY_BOUNDARIES[feature]
        total_change = target_ratio - current_ratio
        
        if abs(total_change) <= boundary.step_size:
            return total_change
        
        return boundary.step_size * (1 if total_change > 0 else -1)

    @classmethod
    def get_face_shape_description(cls, shape: FaceShape) -> str:
        """获取脸型调整说明"""
        descriptions = {
            FaceShape.V_SHAPE: "V脸调整以下颌角收缩为主，强调下巴的尖削感，但需注意保持自然",
            FaceShape.OVAL: "鹅蛋脸追求温和的轮廓过渡，整体调整幅度适中",
            FaceShape.ROUND: "圆脸注重软组织丰满度，避免过度收缩导致不自然",
            FaceShape.SQUARE: "方脸保留适度棱角，同时确保轮廓线条的流畅性"
        }
        return descriptions[shape]
