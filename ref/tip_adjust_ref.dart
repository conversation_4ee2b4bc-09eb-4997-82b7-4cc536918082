import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../../utils/logger.dart';
import '../models/feature_point.dart';
import 'transformation_strategy.dart';
import '../deformation_cache_manager.dart';
import '../transform_type.dart';

/// 鼻尖调整变形策略实现
/// 专注于鼻尖和鼻孔区域的精确变形
class TipAdjustTransformation extends TransformationStrategy {
  static const String _logTag = 'TipAdjustTransformation';
  
  @override
  String get logTag => _logTag;
  
  @override
  String get parameterName => 'tip_adjust';
  
  // 保存上一次特征点变形的参数值，用于判断变形方向
  // 不再使用参数值来确定变形方向
  
  // 面部中心线X坐标缓存
  double? _facialCenterLineX;
  bool _facialCenterLineCalculated = false;

  // 鼻尖区域特征点索引列表
  // 这些索引需要根据实际面部特征点模型调整
  static const List<int> _noseTipPointIndexes = [
    18, 19,  // 鼻尖顶部点
    20, 21,  // 左右鼻孔
    22, 23,  // 鼻孔底部
    24, 25,  // 鼻翼底部
  ];
  
  @override
  void applyFeaturePointTransformation(
    List<FeaturePoint> featurePoints, 
    List<int> pointIndexes, 
    double value,
    double intensity,
    {double? facialCenterLineX, bool? isIncreasing}
  ) {
    Logger.flowStart(logTag, 'applyFeaturePointTransformation');
    Logger.flow(logTag, 'applyFeaturePointTransformation', '开始应用鼻尖调整变形');
    
    // 判断变形方向 - 唯一根据用户点击的是加号还是减号来确定
    // 如果点击加号，则isIncreasing为true；如果点击减号，则为false
    double direction = 0.0;
    
    // 检查是否提供了isIncreasing参数
    if (isIncreasing != null) {
      // 方向系数 - 唯一根据用户点击的按钮来确定
      direction = isIncreasing ? 1.0 : -1.0; // 点击加号为1.0，点击减号为-1.0
      Logger.flow(logTag, 'applyFeaturePointTransformation', '🔍 变形方向根据用户点击确定: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 预期效果: ${isIncreasing ? "鼻尖向前突出" : "鼻尖向后收缩"}');
    } else {
      // 如果没有提供用户点击信息，则报错并中止变形
      Logger.flowError(logTag, 'applyFeaturePointTransformation', '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(logTag, 'applyFeaturePointTransformation');
      return;
    }
    
    // 使用固定的步长值计算变形因子，确保每次变化都是一个步长
    final double stepSize = 0.2; // 固定步长
    // 使用方向系数和固定步长计算变形因子
    final deformationFactor = direction * stepSize * intensity * 2.0;
    Logger.flow(logTag, 'applyFeaturePointTransformation', '变形因子: $deformationFactor (方向: $direction, 步长: $stepSize, 强度: $intensity)');
    
    // 使用传入的面部中心线X坐标，如果没有传入则计算一次
    if (facialCenterLineX != null) {
      _facialCenterLineX = facialCenterLineX;
      _facialCenterLineCalculated = true;
      Logger.flow(logTag, 'applyFeaturePointTransformation', '使用传入的面部中心线X坐标: $_facialCenterLineX');
    } else if (!_facialCenterLineCalculated) {
      _facialCenterLineX = calculateFacialCenterLineX(featurePoints);
      _facialCenterLineCalculated = true;
      Logger.flow(logTag, 'applyFeaturePointTransformation', '计算面部中心线X坐标: $_facialCenterLineX');
    }
    
    final double facialCenterX = _facialCenterLineX ?? 0.0;
    Logger.flow(logTag, 'applyFeaturePointTransformation', '使用面部中心线X坐标: $facialCenterX');
    
    // 找出鼻尖最高点和鼻孔点
    FeaturePoint? noseTipTop;
    FeaturePoint? leftNostril;
    FeaturePoint? rightNostril;
    
    // 遍历特征点，找出鼻尖和鼻孔的特征点
    for (var point in featurePoints) {
      if (point.index == 18 || point.index == 19) {
        // 选择更靠近中心线的点作为鼻尖最高点
        if (noseTipTop == null || (point.x - facialCenterX).abs() < (noseTipTop.x - facialCenterX).abs()) {
          noseTipTop = point;
        }
      } else if (point.index == 20) {
        leftNostril = point;
      } else if (point.index == 21) {
        rightNostril = point;
      }
    }
    
    // 如果没有找到关键特征点，记录错误并退出
    if (noseTipTop == null) {
      Logger.flow(logTag, 'applyFeaturePointTransformation', '❌ [错误] 未找到鼻尖特征点，无法应用变形');
      Logger.flowEnd(logTag, 'applyFeaturePointTransformation');
      // 不再使用参数值
      return;
    }
    
    // 应用变形到所有鼻尖区域特征点
    for (var index in pointIndexes) {
      // 只处理鼻尖区域的特征点
      if (!_noseTipPointIndexes.contains(index)) {
        continue;
      }
      
      final pointIndex = featurePoints.indexWhere((p) => p.index == index);
      if (pointIndex != -1) {
        final oldPoint = featurePoints[pointIndex];
        
        // 计算点到中心线的距离
        final dx = oldPoint.x - facialCenterX;
        final dy = oldPoint.y - noseTipTop.y;
        
        // 计算到鼻尖的距离
        final distanceToTip = math.sqrt(dx * dx + dy * dy);
        
        // 计算变形系数，距离鼻尖越近，变形越明显
        // 使用非线性衰减确保变形平滑过渡
        final maxDistance = 100.0; // 最大影响距离
        final distanceRatio = math.min(distanceToTip / maxDistance, 1.0);
        final falloff = math.pow(math.cos(distanceRatio * math.pi / 2), 4.0); // 使用余弦衰减，指数为4使衰减更陡峭
        
        // 计算Y方向的位移量（主要变形方向）
        double offsetY = deformationFactor * 5.0 * falloff;
        
        // 对鼻尖点的Y方向变形最明显
        if (oldPoint.index == 18 || oldPoint.index == 19) {
          offsetY *= 1.2; // 鼻尖顶部点变形系数增强
        }
        
        // 对鼻孔点的变形稍弱，并且需要考虑X方向的偏移
        if (oldPoint.index == 20 || oldPoint.index == 21) {
          offsetY *= 0.8; // 鼻孔点Y方向变形系数减弱
          
          // 计算X方向的位移量（次要变形方向）
          // 当鼻尖向前突出时，鼻孔略微向外扩展
          // 当鼻尖向后收缩时，鼻孔略微向内收缩
          double offsetX = 0.0;
          if (dx != 0) {
            // 根据点在中心线左侧还是右侧决定X方向偏移
            offsetX = deformationFactor * 2.0 * falloff * (dx > 0 ? 1 : -1);
          }
          
          // 更新特征点X坐标
          featurePoints[pointIndex] = updateFeaturePoint(oldPoint, 
            newX: oldPoint.x + offsetX,
            newY: oldPoint.y + offsetY
          );
          continue;
        }
        
        // 更新特征点Y坐标
        featurePoints[pointIndex] = updateFeaturePoint(oldPoint, newY: oldPoint.y + offsetY);
      }
    }
    
    // 不再使用参数值进行记录
    
    Logger.flowEnd(logTag, 'applyFeaturePointTransformation');
  }
  
  @override
  ui.Image? applyImageTransformation(
    Canvas canvas, 
    Size size, 
    double centerX, 
    double centerY, 
    double radius, 
    double value,
    double intensity,
    ui.Image? image,
    bool showDeformationArea,
    double? facialCenterLineX,
    {bool? isIncreasing}
  ) {
    Logger.flowStart(logTag, 'applyImageTransformation');

    if (image == null) {
      Logger.flow(logTag, 'applyImageTransformation', '❌ [错误] 初始输入图像为空，无法应用变形');
      Logger.flowEnd(logTag, 'applyImageTransformation');
      return null;
    }

    // 捕获输入图像的真实原始尺寸，这将是本次变形操作输出的目标尺寸
    final int trueOriginalWidth = image.width;
    final int trueOriginalHeight = image.height;
    Logger.i(logTag, '🎯 目标输出尺寸已确定 (基于当前输入图像): ${trueOriginalWidth}x${trueOriginalHeight}');

    // 尝试从缓存加载累积变形图像
    ui.Image? accumulatedImage = DeformationCacheManager.getLatestDeformedImage();
    ui.Image baseImageForDeformation = image; // 默认使用当前输入图像作为变形基础

    if (accumulatedImage != null) {
      Logger.i(logTag, '📥 缓存中找到累积图像: ${accumulatedImage.width}x${accumulatedImage.height}, 哈希: ${accumulatedImage.hashCode}');
      // 严格检查缓存图像尺寸是否与真实原始图像尺寸一致
      if (accumulatedImage.width == trueOriginalWidth && accumulatedImage.height == trueOriginalHeight) {
        // 只有当缓存图像与当前输入图像不是同一个对象时，才记录并赋值，避免不必要的日志和赋值
        if (image != accumulatedImage) { 
          baseImageForDeformation = accumulatedImage;
          Logger.i(logTag, '✅ 缓存图像尺寸与目标输出尺寸一致，将使用缓存图像作为变形基础。哈希: ${baseImageForDeformation.hashCode}');
        } else {
            Logger.i(logTag, 'ℹ️ 缓存图像与当前输入图像是同一对象，无需替换。将使用当前输入图像作为变形基础。哈希: ${baseImageForDeformation.hashCode}');
        }
      } else {
        Logger.flowWarning(logTag, 'applyImageTransformation', 
          '⚠️ 缓存图像尺寸 (${accumulatedImage.width}x${accumulatedImage.height}) 与目标输出尺寸 (${trueOriginalWidth}x${trueOriginalHeight}) 不匹配。将丢弃缓存，使用当前输入图像作为变形基础。');
      }
    } else {
      Logger.i(logTag, 'ℹ️ 缓存中未找到累积图像，将使用当前输入图像作为变形基础。');
    }
    Logger.i(logTag, '🖼️ 本次变形操作的基准图像: ${baseImageForDeformation.width}x${baseImageForDeformation.height}, 哈希: ${baseImageForDeformation.hashCode}');

    // 再次检查 baseImageForDeformation，理论上此时它不应为null
    // if (baseImageForDeformation == null) { // 这层检查可以移除，因为image已检查，accumulatedImage即使为null，baseImageForDeformation也会是image
    //   Logger.flow(logTag, 'applyImageTransformation', '❌ [严重错误] baseImageForDeformation 为空，无法应用变形');
    //   Logger.flowEnd(logTag, 'applyImageTransformation');
    //   return null;
    // }
    
    // 后续所有对 image 的引用（用于获取尺寸或像素数据）都应替换为 baseImageForDeformation
    // 例如，计算 dstRect 和 srcRect 时
    if (baseImageForDeformation == null) { // 保留这个检查以防万一，虽然理论上不应该发生
      Logger.flow(logTag, 'applyImageTransformation', '❌ [错误] 图像为空，无法应用变形');
      Logger.flowEnd(logTag, 'applyImageTransformation');
      return null;
    }
    
    // 计算缩放后的目标矩形 - 无论参数值如何，都确保图像正确缩放
    // 使用 baseImageForDeformation 来计算矩形
    final Rect dstRect = _calculateScaledImageRect(baseImageForDeformation, size);
    final srcRect = Rect.fromLTWH(0, 0, baseImageForDeformation.width.toDouble(), baseImageForDeformation.height.toDouble());
    
    // 即使参数值为0，也要执行变形操作，确保变形连续性
    Logger.flow(logTag, 'applyImageTransformation', '无论参数值如何，都执行变形操作，保持变形连续性');
    
    // 使用传入的面部中心线X坐标，如果没有传入则使用画布中心
    final double facialCenterX = facialCenterLineX ?? centerX;
    Logger.flow(logTag, 'applyImageTransformation', '使用面部中心线X坐标: $facialCenterX');
    
    // 判断变形方向 - 唯一根据用户点击的是加号还是减号来确定
    // 如果点击加号，则isIncreasing为true；如果点击减号，则为false
    double direction = 0.0;
    
    // 检查是否提供了isIncreasing参数
    if (isIncreasing != null) {
      // 方向系数 - 唯一根据用户点击的按钮来确定
      direction = isIncreasing ? 1.0 : -1.0; // 点击加号为1.0，点击减号为-1.0
      Logger.flow(logTag, 'applyImageTransformation', '🔍 变形方向根据用户点击确定: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 预期效果: ${isIncreasing ? "鼻尖向前突出" : "鼻尖向后收缩"}');
    } else {
      // 如果没有提供用户点击信息，则报错并中止变形
      Logger.flowError(logTag, 'applyImageTransformation', '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(logTag, 'applyImageTransformation');
      return null;
    }
    
    // 使用固定的步长值计算变形因子，确保每次变化都是一个步长
    final double stepSize = 0.2; // 固定步长
    // 计算变形因子 - 使用方向系数而非参数值
    double deformationFactor = direction * stepSize * intensity;
    
    // 绘制面部中心线
    if (showDeformationArea) {
      drawFacialCenterLine(canvas, size, centerY, color: Colors.green);
    }
    
    // 计算有效半径（考虑参数值的影响）
    // 使用更小的半径确保变形更加局部化，避免影响嘴唇区域
    double effectiveRadius = radius * 0.25; // 减小半径比例，原来是0.35
    
    Logger.flow(logTag, 'applyImageTransformation', '变形半径已调整: $effectiveRadius (原半径: ${radius})');
    
    // 调整变形系数，使效果更加明显
    final double enhancedFactor = deformationFactor * 0.5; // 增强变形强度
    
    Logger.flow(logTag, 'applyImageTransformation', '变形参数 - 中心点: ($facialCenterX, $centerY), 半径: $effectiveRadius, 变形系数: $deformationFactor, 增强系数: $enhancedFactor');
    
    // 绘制原始图像作为背景，使用按比例缩放的矩形
    // 使用高质量绘制
    final paint = Paint()
      ..filterQuality = FilterQuality.high;
    
    canvas.drawImageRect(image, srcRect, dstRect, paint);
    
    // 创建一个离屏画布来绘制变形结果
    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);
    
    // 定义网格密度 - 增加网格密度以获得更平滑的变形效果
    final int gridSizeX = 150; // 增加网格密度
    final int gridSizeY = 150; // 增加网格密度
    
    // 计算网格单元大小 - 使用dstRect的尺寸而不是size，确保变形与缩放后的图像一致
    final double cellWidth = dstRect.width / gridSizeX;
    final double cellHeight = dstRect.height / gridSizeY;
    
    // 获取眼睛区域的Y坐标范围（假设眼睛区域在鼻子上方）
    final double eyeAreaBottomY = centerY - effectiveRadius * 0.5;
    
    // 获取嘴唇区域的Y坐标范围（假设嘴唇区域在鼻子下方）
    final double lipAreaTopY = centerY + effectiveRadius * 1.2;
    
    // 遍历网格
    for (int y = 0; y < gridSizeY; y++) {
      for (int x = 0; x < gridSizeX; x++) {
        // 计算网格单元的四个顶点（在目标图像上的坐标）
        // 使用dstRect的左上角作为起点，确保变形与缩放后的图像一致
        final double left = dstRect.left + x * cellWidth;
        final double top = dstRect.top + y * cellHeight;
        final double right = dstRect.left + (x + 1) * cellWidth;
        final double bottom = dstRect.top + (y + 1) * cellHeight;
        
        // 计算网格单元中心点
        final double centerGridX = (left + right) / 2;
        final double centerGridY = (top + bottom) / 2;
        
        // 计算中心点到面部中心线的距离
        final double dx = centerGridX - facialCenterX;
        final double distanceFromCenterLine = dx.abs();
        final double dy = centerGridY - centerY;
        final distance = math.sqrt(dx * dx + dy * dy);
        
        // 计算变形量
        double offsetX = 0.0;
        double offsetY = 0.0;
        
        // 如果在变形半径内且不在眼睛区域和嘴唇区域，应用变形
        if (distance < effectiveRadius && centerGridY > eyeAreaBottomY && centerGridY < lipAreaTopY) {
          // 计算变形系数，距离中心越近，变形越明显
          final double distanceRatio = distance / effectiveRadius;
          // 使用线性与余弦混合的衰减函数，使过渡更加平滑
          // 增加指数，使衰减更加陡峭，变形更加集中
          final smoothFactor = (1.0 - distanceRatio) * math.pow(math.cos(distanceRatio * math.pi / 2), 2.0);
          
          // 计算Y方向的位移量（鼻尖前后方向）
          // deformationFactor为正时（点击加号），鼻尖向前突出（Y方向位移为负）
          // deformationFactor为负时（点击减号），鼻尖向后收缩（Y方向位移为正）
          offsetY = deformationFactor > 0 ?
              -dy * smoothFactor * deformationFactor * 0.5 : // 向前突出（点击加号）
              dy * smoothFactor * -deformationFactor * 0.5;  // 向后收缩（点击减号）
          
          // 次要在X方向应用变形（鼻孔左右方向）
          // 仅在鼻孔区域应用X方向变形，进一步限制变形区域
          if (distance < effectiveRadius * 0.6 && dy.abs() < effectiveRadius * 0.4) {
            // 计算X方向的位移量
            // deformationFactor为正时（点击加号），鼻孔略微向外扩展
            // deformationFactor为负时（点击减号），鼻孔略微向内收缩
            if (dx != 0) {
              offsetX = deformationFactor > 0 ?
                  dx * smoothFactor * deformationFactor * 0.3 : // 向外扩展（点击加号）
                  -dx * smoothFactor * -deformationFactor * 0.3; // 向内收缩（点击减号）
            }
          }
          
          // 计算变形后的源坐标 - 考虑图像缩放和位置偏移
          // 将画布坐标映射回原始图像坐标
          final double relativeX = (centerGridX - dstRect.left) / dstRect.width;
          final double relativeY = (centerGridY - dstRect.top) / dstRect.height;
          
          // 计算变形后的相对位置
          final double relativeOffsetX = offsetX / dstRect.width;
          final double relativeOffsetY = offsetY / dstRect.height;
          
          // 应用变形偏移到相对坐标
          final double deformedRelativeX = relativeX - relativeOffsetX;
          final double deformedRelativeY = relativeY - relativeOffsetY;
          
          // 将相对坐标映射回原始图像坐标
          final double srcX = deformedRelativeX * baseImageForDeformation.width.toDouble();
          final double srcY = deformedRelativeY * baseImageForDeformation.height.toDouble();
          
          // 确保源坐标在图像范围内
          if (srcX >= 0 && srcX < baseImageForDeformation.width && srcY >= 0 && srcY < baseImageForDeformation.height) {
            // 计算源矩形的宽度和高度（与目标矩形保持相同的相对大小）
            final double srcUnitWidth = cellWidth / dstRect.width * baseImageForDeformation.width.toDouble();
            final double srcUnitHeight = cellHeight / dstRect.height * baseImageForDeformation.height.toDouble();
            
            // 绘制变形后的网格单元
            final srcUnitRect = Rect.fromLTWH(
              srcX - srcUnitWidth / 2,
              srcY - srcUnitHeight / 2,
              srcUnitWidth,
              srcUnitHeight
            );
            
            final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);
            
            offscreenCanvas.drawImageRect(baseImageForDeformation, srcUnitRect, dstUnitRect, paint);
          }
        } else {
          // 不在变形半径内或在眼睛区域，直接复制原始图像的对应部分
          // 使用与变形区域相同的坐标映射逻辑，确保一致性
          final double relativeX = (centerGridX - dstRect.left) / dstRect.width;
          final double relativeY = (centerGridY - dstRect.top) / dstRect.height;
          
          // 将相对坐标映射回原始图像坐标
          final double srcX = relativeX * baseImageForDeformation.width.toDouble();
          final double srcY = relativeY * baseImageForDeformation.height.toDouble();
          
          // 计算源矩形的宽度和高度（与目标矩形保持相同的相对大小）
          final double srcUnitWidth = cellWidth / dstRect.width * baseImageForDeformation.width.toDouble();
          final double srcUnitHeight = cellHeight / dstRect.height * baseImageForDeformation.height.toDouble();
          
          // 绘制原始图像的对应部分
          final srcUnitRect = Rect.fromLTWH(
            srcX - srcUnitWidth / 2,
            srcY - srcUnitHeight / 2,
            srcUnitWidth,
            srcUnitHeight
          );
          
          final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);
          
          offscreenCanvas.drawImageRect(baseImageForDeformation, srcUnitRect, dstUnitRect, paint);
        }
      }
    }
    
    // 将离屏画布的内容绘制到主画布上
    final picture = recorder.endRecording();
    canvas.drawPicture(picture);
    
    // 如果需要显示变形区域，绘制变形区域边界
    if (showDeformationArea) {
      final deformationAreaRect = Rect.fromCenter(
        center: Offset(centerX, centerY),
        width: effectiveRadius * 2,
        height: effectiveRadius * 2
      );
      
      final borderPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      canvas.drawOval(deformationAreaRect, borderPaint);
    }
    
    // 创建最终变形图像
    // 注意：这里的 image 变量是方法最初传入的图像，可能不是 baseImageForDeformation (如果缓存被成功加载并使用)
    // 但最终输出尺寸应基于 trueOriginalWidth 和 trueOriginalHeight
    if (baseImageForDeformation != null) { // 使用 baseImageForDeformation 进行检查和后续操作
      try {
        // 【关键修复】确保创建的变形图像与原始图像尺寸完全一致
        
        // ⭐️ 全尺寸画布创建 - 创建一个目标输出尺寸的画布
        final fullSizeRecorder = ui.PictureRecorder();
        final fullSizeCanvas = Canvas(fullSizeRecorder);
        
        // 首先在全尺寸画布上绘制本次变形操作的基准图像 (baseImageForDeformation)
        // 确保绘制时源是 baseImageForDeformation 的完整尺寸，目标是 trueOriginalWidth/Height
        fullSizeCanvas.drawImageRect(
          baseImageForDeformation, 
          Rect.fromLTWH(0, 0, baseImageForDeformation.width.toDouble(), baseImageForDeformation.height.toDouble()),
          Rect.fromLTWH(0, 0, trueOriginalWidth.toDouble(), trueOriginalHeight.toDouble()), 
          Paint()..filterQuality = FilterQuality.high
        );
        
        // 然后将离屏画布的变形结果绘制到全尺寸画布上
        fullSizeCanvas.drawPicture(picture); // 使用第一次录制的结果
        
        // 变形图像创建 - 创建保证与目标输出尺寸完全相同的变形图像
        final ui.Image finalImage = fullSizeRecorder.endRecording().toImageSync(trueOriginalWidth, trueOriginalHeight);
        
        Logger.flow(logTag, 'applyImageTransformation', '【关键修复】创建了原始尺寸的变形图像: ${finalImage.width}x${finalImage.height}');
        
        // 记录变形图像信息
        Logger.flow(logTag, 'applyImageTransformation', '【关键检查】变形结果图像信息:');
        Logger.flow(logTag, 'applyImageTransformation', '  • 本次变形基准图像: 哈希码=${baseImageForDeformation.hashCode}, 尺寸=${baseImageForDeformation.width}x${baseImageForDeformation.height}');
        Logger.flow(logTag, 'applyImageTransformation', '  • 最终输出变形图像: 哈希码=${finalImage.hashCode}, 尺寸=${finalImage.width}x${finalImage.height}');
        
        // 尺寸一致性检查 - 强制检查最终图像尺寸是否与目标输出尺寸一致
        if (finalImage.width != trueOriginalWidth || finalImage.height != trueOriginalHeight) {
          Logger.flowError(logTag, 'applyImageTransformation', '❌ 严重错误: 最终输出尺寸不一致 | 目标输出尺寸: ${trueOriginalWidth}x${trueOriginalHeight}, 变形后: ${finalImage.width}x${finalImage.height}');
          // 在尺寸不匹配时直接返回方法最初传入的 image (未经过当前变形步骤的原始输入)，避免使用尺寸错误的变形图像污染后续流程或缓存
          return DeformationCacheManager.getLatestDeformedImage() ?? image; // 尝试返回上一个状态的缓存，如果不行则返回原始输入
        }
        
        // 输出成功消息
        Logger.flow(logTag, 'applyImageTransformation', '✅ 图像尺寸一致性检查通过');
        Logger.flow(logTag, 'applyImageTransformation', '  • 图像尺寸: ${finalImage.width}x${finalImage.height}');
        
        // 获取当前特征点数据
        List<FeaturePoint>? currentFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
        
        // 确保特征点数据不为空
        if (currentFeaturePoints == null || currentFeaturePoints.isEmpty) {
          Logger.flowWarning(logTag, 'applyImageTransformation', '⚠️ 当前特征点数据为空，无法从其他来源获取');
        }
        
        // 设置最新的变形状态，包括图像和特征点数据
        DeformationCacheManager.setLatestDeformedState(finalImage, currentFeaturePoints);
        Logger.flow(logTag, 'applyImageTransformation', '✅ 变形后的图像和特征点数据已设置为DeformationCacheManager的最新状态');
        Logger.flow(logTag, 'applyImageTransformation', '  • 图像哈希码: ${finalImage.hashCode}');
        Logger.flow(logTag, 'applyImageTransformation', '  • 尺寸: ${finalImage.width}x${finalImage.height}');
        Logger.flow(logTag, 'applyImageTransformation', '  • 特征点数量: ${currentFeaturePoints?.length ?? 0}个');
        
        Logger.i(logTag, '✅ 鼻尖调整变形 | 最终输出图像已创建，尺寸=${finalImage.width}x${finalImage.height} (目标输出尺寸 ${trueOriginalWidth}x${trueOriginalHeight})');
        // 返回变形图像
        return finalImage;
        // 累积缓存的保存在applyCompleteTransformation方法中通过saveToCache回调实现
      } catch (e, s) {
        Logger.flow(logTag, 'applyImageTransformation', '创建变形后的图像失败: $e\nStackTrace: $s');
      }
    }
    
    Logger.flowEnd(logTag, 'applyImageTransformation');
  }
  
  /// 计算图像缩放矩形，确保图像按比例缩放并适应画布
  Rect _calculateScaledImageRect(ui.Image image, Size canvasSize) {
    final double imageAspectRatio = image.width / image.height;
    final double canvasAspectRatio = canvasSize.width / canvasSize.height;
    
    double scaledWidth;
    double scaledHeight;
    
    if (imageAspectRatio > canvasAspectRatio) {
      // 图像更宽，以宽度为基准缩放
      scaledWidth = canvasSize.width;
      scaledHeight = scaledWidth / imageAspectRatio;
    } else {
      // 图像更高，以高度为基准缩放
      scaledHeight = canvasSize.height;
      scaledWidth = scaledHeight * imageAspectRatio;
    }
    
    // 居中放置图像
    final double left = (canvasSize.width - scaledWidth) / 2;
    final double top = (canvasSize.height - scaledHeight) / 2;
    
    return Rect.fromLTWH(left, top, scaledWidth, scaledHeight);
  }
  
  @override
  bool handleSpecialCases(List<FeaturePoint> featurePoints, double value, bool isIncreasing) {
    return false;
  }
  
  @override
  TransformType getRequiredTransformType() {
    return TransformType.mesh;
  }
  
  @override
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints, [double scaleX = 1.0]) {
    Logger.flowStart(logTag, 'calculateFacialCenterLineX');
    
    // 找出左右眼睛的特征点
    FeaturePoint? leftEye;
    FeaturePoint? rightEye;
    
    // 找出左右鼻孔的特征点
    FeaturePoint? leftNostril;
    FeaturePoint? rightNostril;
    
    // 遍历特征点，找出眼睛和鼻孔的特征点
    for (var point in featurePoints) {
      // 根据特征点的ID或位置判断其类型
      if (point.id == 'left_eye' || point.index == 10) {
        leftEye = point;
      } else if (point.id == 'right_eye' || point.index == 11) {
        rightEye = point;
      } else if (point.id == 'left_nostril' || point.index == 20) {
        leftNostril = point;
      } else if (point.id == 'right_nostril' || point.index == 21) {
        rightNostril = point;
      }
    }
    
    // 计算眼睛中心线
    double eyeCenterX = 0.0;
    if (leftEye != null && rightEye != null) {
      eyeCenterX = (leftEye.x + rightEye.x) / 2 * scaleX;
      Logger.flow(logTag, 'calculateFacialCenterLineX', '眼睛中心线: $eyeCenterX (左眼: ${leftEye.x}, 右眼: ${rightEye.x})');
    }
    
    // 计算鼻孔中心线
    double nostrilCenterX = 0.0;
    if (leftNostril != null && rightNostril != null) {
      nostrilCenterX = (leftNostril.x + rightNostril.x) / 2 * scaleX;
      Logger.flow(logTag, 'calculateFacialCenterLineX', '鼻孔中心线: $nostrilCenterX (左鼻孔: ${leftNostril.x}, 右鼻孔: ${rightNostril.x})');
    }
    
    // 如果找到了眼睛和鼻孔的特征点，计算加权平均的中心线
    double facialCenterX;
    if (eyeCenterX > 0 && nostrilCenterX > 0) {
      // 给眼睛中心线更高的权重
      facialCenterX = (eyeCenterX * 0.7 + nostrilCenterX * 0.3);
      Logger.flow(logTag, 'calculateFacialCenterLineX', '基于加权平均计算的中心线: $facialCenterX');
    } else if (eyeCenterX > 0) {
      facialCenterX = eyeCenterX;
      Logger.flow(logTag, 'calculateFacialCenterLineX', '仅使用眼睛中心线: $facialCenterX');
    } else if (nostrilCenterX > 0) {
      facialCenterX = nostrilCenterX;
      Logger.flow(logTag, 'calculateFacialCenterLineX', '仅使用鼻孔中心线: $facialCenterX');
    } else {
      // 如果没有找到有效的特征点，使用图像中心作为面部中心线
      facialCenterX = 0.0;
      for (var point in featurePoints) {
        facialCenterX += point.x;
      }
      facialCenterX = (facialCenterX / featurePoints.length) * scaleX;
      Logger.flow(logTag, 'calculateFacialCenterLineX', '使用所有特征点的平均X坐标作为中心线: $facialCenterX');
    }
    
    Logger.flow(logTag, 'calculateFacialCenterLineX', '面部中心线已固定: $facialCenterX');
    Logger.flowEnd(logTag, 'calculateFacialCenterLineX');
    
    return facialCenterX;
  }
  
  @override
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint, {double? newX, double? newY}) {
    Logger.flow(_logTag, 'updateFeaturePoint', '更新特征点 ID=${oldPoint.index} 坐标: (${newX ?? oldPoint.x}, ${newY ?? oldPoint.y})');
    return oldPoint.copyWith(
      x: newX,
      y: newY,
    );
  }
  
  @override
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY, {Color color = Colors.red}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;
    
    // 绘制垂直中心线
    canvas.drawLine(
      Offset(size.width / 2, 0),
      Offset(size.width / 2, size.height),
      paint
    );
    
    // 绘制水平中心线
    canvas.drawLine(
      Offset(0, centerY),
      Offset(size.width, centerY),
      paint
    );
  }
  
  @override
  Future<Map<String, dynamic>?> applyCompleteTransformation(
    Canvas canvas,
    Size size,
    ui.Image baseImage,
    List<FeaturePoint> featurePoints,
    List<int> pointIndexes,
    double value,
    double intensity,
    double? facialCenterLineX,
    bool facialCenterLineCalculated,
    String valueChangeDirection,
    Function(ui.Image, List<FeaturePoint>)? onDeformationResultCallback,
    Function(Map<String, double>, ui.Image, List<FeaturePoint>, {double? facialCenterLineX, bool? facialCenterLineCalculated})? saveToCache,
    Map<String, double> allParameterValues
  ) {
    Logger.flowStart(logTag, 'applyCompleteTransformation');
    Logger.flow(logTag, 'applyCompleteTransformation', '开始应用鼻尖调整变形');
    
    // 根据变形方向确定是增大还是减小
    bool isIncreasing = valueChangeDirection == 'increase';
    
    // 使用传入的面部中心线X坐标，如果没有传入则计算一次
    double centerX = facialCenterLineX ?? calculateFacialCenterLineX(featurePoints);
    double centerY = size.height / 2; // 使用画布高度的一半作为中心Y坐标
    double radius = size.width / 4; // 使用画布宽度的四分之一作为半径
    
    // 应用特征点变形
    List<FeaturePoint> updatedFeaturePoints = List<FeaturePoint>.from(featurePoints);
    applyFeaturePointTransformation(
      updatedFeaturePoints, 
      pointIndexes, 
      value, 
      intensity,
      facialCenterLineX: centerX,
      isIncreasing: isIncreasing
    );
    
    // 应用图像变形
    applyImageTransformation(
      canvas, 
      size, 
      centerX, 
      centerY, 
      radius, 
      value, 
      intensity,
      baseImage,
      false,
      facialCenterLineX,
      isIncreasing: isIncreasing
    );
    
    // 获取最新的变形图像
    ui.Image? latestImage = DeformationCacheManager.getLatestDeformedImage();
    
    if (latestImage != null) {
      // 使用最新的变形图像而不是原始图像
      baseImage = latestImage;
      
      // 将变形后的图像设置为最新状态
      DeformationCacheManager.setLatestDeformedState(latestImage, updatedFeaturePoints, isButtonClick: true);
      Logger.i(logTag, '✅ 鼻尖调整变形 | 变形图像已保存到DeformationCacheManager');
      
      // 记录变形图像信息
      Logger.flow(logTag, 'applyCompleteTransformation', '变形图像信息: 哈希码=${latestImage.hashCode}, 尺寸=${latestImage.width}x${latestImage.height}');
    } else {
      Logger.flow(logTag, 'applyCompleteTransformation', '使用原始图像进行变形');
    }
    
    // 通过回调函数将变形结果回传
    if (onDeformationResultCallback != null) {
      onDeformationResultCallback(baseImage, updatedFeaturePoints);
      Logger.i(logTag, '✅ 鼻尖调整变形 | 变形结果回传成功');
    }
    
    // 重要：通知变形已完成，重置_isDeforming标志，允许下一次变形
    DeformationCacheManager.notifyDeformationCompleted();
    Logger.flow(logTag, 'applyCompleteTransformation', '✅ 已通知变形完成，允许下一次变形');
    
    // 保存到缓存
    if (saveToCache != null) {
      // 确保当前参数值是最新的，并且只保留小数点后1位数
      String fullParamName = 'tip_adjust';
      Map<String, double> updatedParams = Map<String, double>.from(allParameterValues);
      // 四舍五入到1位小数
      double roundedValue = double.parse(value.toStringAsFixed(1));
      updatedParams[fullParamName] = roundedValue;
      Logger.flow(logTag, 'applyCompleteTransformation', '参数值四舍五入: 原值=$value, 四舍五入后=$roundedValue');
      
      // 调用回调函数保存到缓存
      saveToCache(
        updatedParams, 
        baseImage, 
        updatedFeaturePoints,
        facialCenterLineX: facialCenterLineX,
        facialCenterLineCalculated: facialCenterLineCalculated
      );
      
      Logger.i(logTag, '✅ 鼻尖调整变形 | 变形结果已保存到缓存');
      
      // 设置最新的变形状态，确保可以在下一次变形中使用
      DeformationCacheManager.setLatestDeformedState(baseImage, updatedFeaturePoints);
      Logger.i(logTag, '✅ 鼻尖调整变形 | 变形结果已设置为最新状态');
    }
    
    Logger.flowEnd(logTag, 'applyCompleteTransformation');
    return Future.value({'deformedImage': baseImage, 'updatedFeaturePoints': updatedFeaturePoints}); // 返回变形结果
  }
}
