"""变形基础数据类型定义"""

from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional

@dataclass
class TransformVector:
    """变形向量"""
    direction: Tuple[float, float]  # (x, y)方向
    magnitude: float  # 变形强度
    angle: Optional[float] = None  # 旋转角度（如果需要）
    weight: Optional[float] = 1.0  # 权重系数
    falloff: Optional[float] = 0.5  # 衰减系数，控制影响范围
    smoothing: Optional[float] = 0.3  # 平滑系数，控制过渡自然度

@dataclass
class FeaturePointSpec:
    """特征点规范"""
    index: int
    name: str
    description: str
    anatomical_structure: str  # 解剖结构
    primary_for: List[str]  # 作为主要点的参数列表
    secondary_for: List[str]  # 作为次要点的参数列表
    transform_vectors: Dict[str, TransformVector]  # 每个参数的变形向量
    constraints: Dict[str, float]  # 解剖学约束
    symmetry_point: Optional[int] = None  # 对称点索引
    movement_constraints: Optional[Dict[str, float]] = None  # 移动约束
    aesthetic_ratio: Optional[float] = None  # 美学比例
    aesthetic_guidelines: Optional[str] = None  # 美学指南
    influence_radius: Optional[float] = None  # 影响半径
    transition_smoothness: Optional[float] = None  # 过渡平滑度
    tension: Optional[float] = None  # 张力系数，控制变形的紧实度
