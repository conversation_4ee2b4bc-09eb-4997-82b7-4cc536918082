"""V脸变形规范"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import numpy as np

@dataclass
class FeaturePointSpec:
    """特征点规范"""
    index: int                     # 点索引
    symmetry_point: Optional[int]  # 对称点索引
    transform_weight: float = 1.0  # 变形权重
    transition_weight: float = 1.0 # 过渡权重
    
@dataclass
class TransformVector:
    """变形向量"""
    direction: Tuple[float, float]  # 变形方向
    magnitude: float               # 变形强度
    angle: Optional[float] = None  # 旋转角度（可选）

class VFaceTransformSpecs:
    """V脸变形规范"""
    
    # 变形参数规范
    PARAM_SPECS = {
        'face_slim': {
            'max_value': 40,
            'primary_points': [1, 2, 3, 4, 32, 33],  # 颧骨区域（1-based索引）
            'secondary_points': [5, 6, 7, 8, 28, 29, 30, 31],  # 下颌线
            'transform_weight': 1.0,
            'transition_weight': 0.8
        },
        'chin_length': {
            'max_value': 20,
            'primary_points': [17],  # 下巴中心（1-based索引）
            'secondary_points': [15, 16, 18, 19],  # 下巴两侧
            'transform_weight': 1.0,
            'transition_weight': 0.9
        },
        'chin_width': {
            'max_value': 20,
            'primary_points': [14, 15, 16, 17, 18, 19, 20],  # 下巴区域
            'secondary_points': [13, 21],  # 过渡区域
            'transform_weight': 0.9,
            'transition_weight': 0.8
        },
        'jaw_angle': {
            'max_value': 15,
            'primary_points': [7, 8, 9, 10, 24, 25, 26, 27],  # 下颌角
            'secondary_points': [6, 11, 23, 28],  # 过渡区域
            'transform_weight': 0.8,
            'transition_weight': 0.7
        },
        'cheek_height': {
            'max_value': 20,
            'primary_points': [3, 4, 5, 29, 30, 31],  # 颧骨高度
            'secondary_points': [2, 6, 28, 32],  # 过渡区域
            'transform_weight': 0.9,
            'transition_weight': 0.8
        },
        'cheek_width': {
            'max_value': 20,
            'primary_points': [3, 4, 5, 29, 30, 31],  # 颧骨宽度
            'secondary_points': [2, 6, 28, 32],  # 过渡区域
            'transform_weight': 0.9,
            'transition_weight': 0.8
        }
    }
    
    # V脸预设参数
    V_FACE_PRESET = {
        'face_slim': 35,      # 明显瘦脸
        'chin_length': 15,    # 明显延长下巴
        'chin_width': -25,    # 显著收窄下巴
        'jaw_angle': -15,     # 下颌线明显内收
        'cheek_height': -15,  # 显著降低颧骨
        'cheek_width': -20    # 显著收窄脸颊
    }
    
    # V脸关键点定义
    V_FACE_POINTS = {
        'primary': {
            'left_cheekbone': [1, 2, 3, 4],      # 左侧颧骨区域 (strength 1.0)
            'right_cheekbone': [30, 31, 32, 33], # 右侧颧骨区域 (strength 1.0)
            'chin_center': [17]                   # 下巴中心点 (strength 1.0)
        },
        'secondary': {
            'left_jawline': [5, 6, 7, 8],        # 左侧下颌线 (strength 0.9)
            'right_jawline': [26, 27, 28, 29],   # 右侧下颌线 (strength 0.9)
            'chin_contour': [14, 15, 16, 18, 19, 20]  # 下巴轮廓
        }
    }
    
    # 变形点组
    TRANSFORM_POINTS = {
        'face_slim': [3, 4, 5, 6, 7, 25, 26, 27, 28, 29],  # 脸部瘦身
        'chin_length': [14, 15, 16, 17, 18, 19, 20],  # 下巴长度
        'chin_width': [11, 12, 13, 14, 20, 21, 22, 23],  # 下巴宽度
        'jaw_angle': [7, 8, 9, 10, 11, 23, 24, 25, 26, 27],  # 下颌角度
        'cheek_height': [1, 2, 3, 4, 28, 29, 30, 31],  # 颧骨高度
        'cheek_width': [2, 3, 4, 5, 27, 28, 29, 30]  # 颧骨宽度
    }
    
    # 变形参数的缩放系数
    TRANSFORM_SCALES = {
        'face_slim': 8.0,  # 脸部瘦身
        'chin_length': 6.0,  # 下巴长度
        'chin_width': 6.0,  # 下巴宽度
        'jaw_angle': 4.0,  # 下颌角度
        'cheek_height': 6.0,  # 颧骨高度
        'cheek_width': 6.0  # 颧骨宽度
    }
    
    # 对称点对
    SYMMETRIC_PAIRS = [
        (3, 29), (4, 28), (5, 27), (6, 26), (7, 25),
        (11, 23), (12, 22), (13, 21), (14, 20),
        (7, 27), (8, 26), (9, 25), (10, 24),
        (1, 31), (2, 30), (3, 29), (4, 28)
    ]
    
    @classmethod
    def get_param_spec(cls, param_name: str) -> Dict:
        """获取参数规范
        
        Args:
            param_name: 参数名称
            
        Returns:
            参数规范字典
        """
        if param_name not in cls.PARAM_SPECS:
            raise ValueError(f"未知的参数名称: {param_name}")
            
        spec = cls.PARAM_SPECS[param_name].copy()
        spec['min_value'] = -spec['max_value']
        return spec
        
    @classmethod
    def get_point_transform_vector(cls, point_idx: int, param_name: str) -> Optional[TransformVector]:
        """获取点的变形向量
        
        Args:
            point_idx: 点索引
            param_name: 参数名称
            
        Returns:
            变形向量
        """
        try:
            # 获取参数规范
            spec = cls.get_param_spec(param_name)
            if not spec:
                return None
                
            # 确定点的变形权重
            weight = spec['transform_weight']
            if point_idx in spec['primary_points']:
                pass  # 使用完整权重
            elif point_idx in spec['secondary_points']:
                weight *= 0.7  # 次要点使用较小的权重
            else:
                return None  # 不在变形范围内的点
                
            # 根据参数类型确定变形向量
            if param_name == 'face_slim':
                # 瘦脸：向内移动
                return TransformVector((-1.0, 0.0), weight * 1.0)
                
            elif param_name == 'chin_length':
                # 下巴长度：垂直方向
                return TransformVector((0.0, 1.0), weight * 0.8)
                
            elif param_name == 'chin_width':
                # 下巴宽度：水平方向
                return TransformVector((-1.0, 0.0), weight * 0.9)
                
            elif param_name == 'jaw_angle':
                # 下颌角：斜向内
                return TransformVector((-0.7, -0.7), weight * 0.7, angle=15)
                
            elif param_name == 'cheek_height':
                # 颧骨高度：垂直方向
                return TransformVector((0.0, -1.0), weight * 0.8)
                
            elif param_name == 'cheek_width':
                # 颧骨宽度：水平方向
                return TransformVector((-1.0, 0.0), weight * 1.0)
                
            return None
            
        except Exception as e:
            print(f"获取变形向量失败: {str(e)}")
            return None
            
    @classmethod
    def validate_transform(cls, point_idx: int, param_name: str, displacement: np.ndarray) -> bool:
        """验证变形是否合理
        
        Args:
            point_idx: 点索引
            param_name: 参数名称
            displacement: 位移向量
            
        Returns:
            变形是否合理
        """
        try:
            # 获取参数规范
            spec = cls.get_param_spec(param_name)
            if not spec:
                return False
                
            # 获取最大允许变形量
            max_value = spec['max_value']
            
            # 计算实际变形量
            actual_value = np.linalg.norm(displacement)
            
            # 检查变形量是否在合理范围内
            if actual_value > max_value:
                return False
                
            return True
            
        except Exception as e:
            print(f"变形验证失败: {str(e)}")
            return False
            
    @classmethod
    def apply_smoothing(cls, landmarks: np.ndarray, weights: Dict[int, float], smoothing_factor: float = 0.3) -> np.ndarray:
        """应用平滑处理
        
        Args:
            landmarks: 特征点数组
            weights: 点权重字典
            smoothing_factor: 平滑因子
            
        Returns:
            平滑后的特征点数组
        """
        try:
            # 复制特征点数组
            smoothed = landmarks.copy()
            
            # 对每个点应用平滑
            for idx in range(len(landmarks)):
                if idx not in weights:
                    continue
                    
                # 获取相邻点
                neighbors = []
                if idx > 0:
                    neighbors.append(landmarks[idx-1])
                if idx < len(landmarks)-1:
                    neighbors.append(landmarks[idx+1])
                    
                if not neighbors:
                    continue
                    
                # 计算平均位置
                avg_pos = np.mean(neighbors, axis=0)
                
                # 应用平滑
                weight = weights[idx]
                smoothed[idx] = (1 - smoothing_factor * weight) * landmarks[idx] + \
                              smoothing_factor * weight * avg_pos
                              
            return smoothed
            
        except Exception as e:
            print(f"平滑处理失败: {str(e)}")
            return landmarks
