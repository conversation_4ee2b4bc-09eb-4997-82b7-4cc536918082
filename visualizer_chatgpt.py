#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

import cv2
import mediapipe as mp
import numpy as np
from core.feature.parameter_mapping import CENTER_LINE_POINTS
from core.feature.symmetry_calibrator import (
    SymmetryCalibrator, 
    FacialRegion, 
    AnatomicalLayer,
    ANATOMICAL_CONSTRAINTS
)

# MediaPipe Face Mesh 初始化
mp_face_mesh = mp.solutions.face_mesh
face_mesh = mp_face_mesh.FaceMesh(
    static_image_mode=True,
    max_num_faces=1,
    refine_landmarks=True,
    min_detection_confidence=0.5
)

def process_image(image_path: str):
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图片: {image_path}")
        return
        
    image_height, image_width, _ = image.shape
    
    # 转换为RGB
    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 获取面部特征点
    results = face_mesh.process(rgb_image)
    
    if not results.multi_face_landmarks:
        print("未检测到人脸")
        return
    
    # 获取特征点坐标
    face_landmarks = results.multi_face_landmarks[0]
    landmarks_array = np.array([
        [int(point.x * image_width), int(point.y * image_height)]
        for point in face_landmarks.landmark
    ])
    
    # 初始化对称性校准器
    calibrator = SymmetryCalibrator(image_width)
    
    # 执行对称性校准
    symmetry_pairs = calibrator.calibrate_all_regions(landmarks_array)
    
    # 可视化结果
    visualization = image.copy()
    
    # 绘制中心线点
    for point_idx in CENTER_LINE_POINTS:
        x, y = landmarks_array[point_idx]
        cv2.circle(visualization, (x, y), 3, (0, 0, 255), -1)  # 红色
        cv2.putText(visualization, str(point_idx), (x+5, y), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1)
    
    # 为每个区域和层次使用不同的颜色
    region_colors = {
        FacialRegion.EYES: {
            AnatomicalLayer.UPPER: (0, 255, 0),     # 绿色
            AnatomicalLayer.MIDDLE: (0, 200, 0),    # 深绿
            AnatomicalLayer.LOWER: (0, 150, 0),     # 更深绿
        },
        FacialRegion.EYEBROWS: {
            AnatomicalLayer.UPPER: (255, 0, 0),     # 蓝色
            AnatomicalLayer.MIDDLE: (200, 0, 0),    # 深蓝
            AnatomicalLayer.LOWER: (150, 0, 0),     # 更深蓝
        },
        FacialRegion.NOSE: {
            AnatomicalLayer.UPPER: (0, 255, 255),   # 黄色
            AnatomicalLayer.MIDDLE: (0, 200, 200),  # 深黄
            AnatomicalLayer.LOWER: (0, 150, 150),   # 更深黄
        },
        FacialRegion.LIPS: {
            AnatomicalLayer.UPPER: (255, 0, 255),   # 紫色
            AnatomicalLayer.MIDDLE: (200, 0, 200),  # 深紫
            AnatomicalLayer.LOWER: (150, 0, 150),   # 更深紫
        }
    }
    
    # 绘制对称点对
    for region, pairs in symmetry_pairs.items():
        for pair in pairs:
            color = region_colors.get(region, {}).get(pair.layer, (255, 255, 255))
            
            # 左点
            x1, y1 = landmarks_array[pair.left_point]
            cv2.circle(visualization, (x1, y1), 2, color, -1)
            cv2.putText(visualization, str(pair.left_point), (x1+5, y1), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
            
            # 右点
            x2, y2 = landmarks_array[pair.right_point]
            cv2.circle(visualization, (x2, y2), 2, color, -1)
            cv2.putText(visualization, str(pair.right_point), (x2+5, y2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
            
            # 连接对称点（根据置信度调整线条透明度）
            alpha = int(pair.confidence * 255)
            overlay = visualization.copy()
            cv2.line(overlay, (x1, y1), (x2, y2), color, 1)
            cv2.addWeighted(overlay, alpha, visualization, 1 - alpha, 0, visualization)
            
            # 打印对称点对信息
            print(f"区域: {region.value}, 层次: {pair.layer.value}, "
                  f"对称点对: ({pair.left_point}, {pair.right_point}), "
                  f"置信度: {pair.confidence:.2f}")
    
    # 保存结果
    output_path = "testdata/face_landmarks_annotated.jpg"
    cv2.imwrite(output_path, visualization)
    print(f"\n已保存标注图片到: {output_path}")

if __name__ == "__main__":
    process_image("testdata/test_face.jpg")
