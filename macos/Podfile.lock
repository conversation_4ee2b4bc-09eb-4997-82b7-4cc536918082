PODS:
  - camera_macos (0.0.1):
    - FlutterMacOS
  - file_picker (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - window_size (0.0.2):
    - FlutterMacOS

DEPENDENCIES:
  - camera_macos (from `Flutter/ephemeral/.symlinks/plugins/camera_macos/macos`)
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - window_size (from `Flutter/ephemeral/.symlinks/plugins/window_size/macos`)

EXTERNAL SOURCES:
  camera_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/camera_macos/macos
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  window_size:
    :path: Flutter/ephemeral/.symlinks/plugins/window_size/macos

SPEC CHECKSUMS:
  camera_macos: 9fd766915a6c236c8b3fd902f7ccd215636cd07a
  file_picker: 7584aae6fa07a041af2b36a2655122d42f578c1a
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  window_size: 4bd15034e6e3d0720fd77928a7c42e5492cfece9

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
