import cv2
import numpy as np
import sys
import os

def compare_images(original_path, deformed_path, output_path):
    """
    创建一个并排比较原始图像和变形后图像的图像
    
    参数:
        original_path: 原始图像路径
        deformed_path: 变形后图像路径
        output_path: 输出比较图像路径
    """
    # 读取图像
    original = cv2.imread(original_path)
    deformed = cv2.imread(deformed_path)
    
    if original is None or deformed is None:
        print("无法读取图像文件")
        return False
    
    # 确保两个图像具有相同的高度
    h1, w1 = original.shape[:2]
    h2, w2 = deformed.shape[:2]
    
    # 使用较大的高度
    max_height = max(h1, h2)
    
    # 调整图像大小以匹配高度
    if h1 != max_height:
        original = cv2.resize(original, (int(w1 * max_height / h1), max_height))
    if h2 != max_height:
        deformed = cv2.resize(deformed, (int(w2 * max_height / h2), max_height))
    
    # 获取调整后的尺寸
    h1, w1 = original.shape[:2]
    h2, w2 = deformed.shape[:2]
    
    # 创建并排图像
    comparison = np.zeros((max_height, w1 + w2 + 10, 3), dtype=np.uint8)
    
    # 添加原始图像
    comparison[:h1, :w1] = original
    
    # 添加变形后图像
    comparison[:h2, w1+10:w1+10+w2] = deformed
    
    # 添加标签
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(comparison, "原始图像", (10, 30), font, 1, (255, 255, 255), 2)
    cv2.putText(comparison, "变形后图像", (w1+20, 30), font, 1, (255, 255, 255), 2)
    
    # 保存比较图像
    cv2.imwrite(output_path, comparison)
    print(f"比较图像已保存到: {output_path}")
    return True

if __name__ == "__main__":
    # 检查参数
    if len(sys.argv) < 3:
        print("用法: python compare_images.py <原始图像路径> <变形图像路径> [输出图像路径]")
        sys.exit(1)
    
    original_path = sys.argv[1]
    deformed_path = sys.argv[2]
    
    # 默认输出路径
    if len(sys.argv) > 3:
        output_path = sys.argv[3]
    else:
        # 从变形图像路径生成输出路径
        dirname = os.path.dirname(deformed_path)
        basename = os.path.basename(deformed_path)
        name, ext = os.path.splitext(basename)
        output_path = os.path.join(dirname, f"{name}_comparison{ext}")
    
    # 比较图像
    success = compare_images(original_path, deformed_path, output_path)
    
    if success:
        print("图像比较完成")
    else:
        print("图像比较失败")
        sys.exit(1)
