  @override
  ui.Image? applyImageTransformation(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double radius,
      double value,
      double intensity,
      ui.Image? image,
      bool showDeformationArea,
      double? facialCenterLineX,
      {bool? isIncreasing, List<FeaturePoint>? currentFeaturePoints}) {
    Logger.flowStart(_logTag, 'applyImageTransformation');
    
    if (image == null) {
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }
    
    // 简化实现，直接返回原图
    Logger.flow(_logTag, 'applyImageTransformation', '变形暂时返回原图');
    Logger.flowEnd(_logTag, 'applyImageTransformation');
    return image;
  }

  @override
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints,
      [double scaleX = 1.0]) {
    Logger.flowStart(_logTag, 'calculateFacialCenterLineX');

    // 使用鼻翼点计算面部中心线
    FeaturePoint? leftNostril;   // 左鼻翼点 - 索引129
    FeaturePoint? rightNostril;  // 右鼻翼点 - 索引358

    for (var point in featurePoints) {
      if (point.index == 129) {
        leftNostril = point;
      } else if (point.index == 358) {
        rightNostril = point;
      }
    }

    double facialCenterX;
    if (leftNostril != null && rightNostril != null) {
      facialCenterX = (leftNostril.x + rightNostril.x) / 2 * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用鼻翼点计算中心线: $facialCenterX');
    } else {
      // 备选方案：使用所有特征点的平均值
      facialCenterX = 0.0;
      for (var point in featurePoints) {
        facialCenterX += point.x;
      }
      facialCenterX = (facialCenterX / featurePoints.length) * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用所有特征点的平均X坐标作为中心线: $facialCenterX');
    }

    Logger.flowEnd(_logTag, 'calculateFacialCenterLineX');
    return facialCenterX;
  }

  @override
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint,
      {double? newX, double? newY}) {
    Logger.flow(_logTag, 'updateFeaturePoint',
        '更新特征点 ID=${oldPoint.index} 坐标: (${newX ?? oldPoint.x}, ${newY ?? oldPoint.y})');
    return FeaturePoint(
        id: oldPoint.id,
        index: oldPoint.index,
        x: newX ?? oldPoint.x,
        y: newY ?? oldPoint.y,
        z: oldPoint.z);
  }

  @override
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY,
      {Color color = Colors.red}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // 绘制垂直中心线
    canvas.drawLine(
        Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);

    // 绘制水平中心线
    canvas.drawLine(Offset(0, centerY), Offset(size.width, centerY), paint);
  }

  @override
  void resetState() {
    Logger.flow(_logTag, 'resetState', '重置变形状态');
  }