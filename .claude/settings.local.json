{"permissions": {"allow": ["Bash(flutter analyze:*)", "<PERSON><PERSON>(flutter run:*)", "Bash(rg:*)", "Bash(grep:*)", "Ba<PERSON>(flutter:*)", "Bash(find:*)", "Bash(dart analyze:*)", "Bash(timeout 30 flutter run -d macos --verbose 2 >& 1)", "Bash(timeout 60 flutter run -d macos 2 >& 1)", "Bash(ls:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(q)", "<PERSON><PERSON>(r)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(killall:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(mv:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(python3:*)", "Bash(rm:*)", "<PERSON><PERSON>(dart:*)", "Bash(cp:*)", "Bash(for:*)", "Bash(do)", "Bash(echo \"Updating $file\")", "Bash(done)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(true)"], "deny": []}}