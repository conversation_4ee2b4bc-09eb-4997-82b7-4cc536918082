变形系统缓存共享原则
#deformation_cache
#shared_instance
#accumulative_deformation
#design_principle
Edit
在变形系统中，所有的变形操作必须遵循以下原则：

1. 所有变形操作必须在同一个实例下进行
2. 所有变形操作必须共享同一个缓存（DeformationCacheManager）
3. 必须在唯一一个变形绘制器实例中实现跨参数项的累积变形
4. 所有参数项的变形操作都必须使用这个共享的缓存
5. 禁止为不同的变形操作创建多个缓存实例
6. 变形状态必须在整个变形生命周期内保持一致

这种设计确保了变形操作的连贯性和一致性，避免了由于多个缓存实例导致的状态不一致问题。


所有的功能设计以未来运行在安卓和iphone高端智能手机上为目标. 意味着默认手机具备优秀的图片处理能力. 
所有图片的处理, 均尽量保证最好的分辨率无损 


资源图片文件会放到assets目录下. 
测试图片均需要由我选择导入 . 
所有的测试文件, 均需要放置在test目录下. 

所有的文档统一放在docs目录, 
所有的共用和通用功能文件, 需经我批准, 然后统一放在core目录. 
项目目录，一旦确认以后, 任何新增目录必须由我批准. 任何新文件的建立，必须由我批准. 任何UI的改变，必须由我批准.
测试文件的建立必须由我批准后才可建立. 

所有的新文件创建, 都必须经我同意. 
所有的测试文件的建立,都必须经我同意. 
所有的UI的改变,都必须经我同意. 

所有的测试运行都必须在macOS上运行.
所有的程序错误, 输出错误日志后, 退出程序. 不可以继续绕过错误而继续运行.
用中文跟我交互。 


在新增函数或者功能时,必须设计相应的简洁的调试日志输出,由我确认. 
变形方向判断：
变形方向唯一根据点击的加号或减号来确定
当点击加号时，执行扩大变形（如鼻子变宽）
当点击减号时，执行缩小变形（如鼻子变窄）
这一逻辑应该在所有变形相关代码中保持一致

要求一定基于原图的尺寸, 整个变形生命周期内, 同一个缓存处理所有逻辑和计算, 同一个尺寸. 

明确要求:
1. 所有的变形在同一个实例下,共享同一个缓存, 存取DeformationCacheManager的累积状态, 在唯一一个变形绘制器的实例里实现跨参数项的累积变形, 供全部参数项的所有变形共享使用. 
2. 所有的变形生命周期中的任何有关图像, 变形后图像, 特征点等的处理过程中, 默认非空. 因为图像导入并且成功特征识别之后才会有后c续的累积变形.  一旦为空, 即刻报错退出,而不允许有任何的假数据, 或者备选方案等. 
3. 程序运行后, 导入图像并且特征识别正确之后, 立即计算出面部中心线并且保存后在同一实例下共享给所有的变形调用. 不允许任何重新计算,或者重置或者缓存清空. 

4. 程序运行测试是暂时只在macos. 也就是 flutter run -d macos





面部美化功能参数定义
#feature_areas
#parameters
#ui_definition
#naming_convention
Edit
根据 lib/widgets/panels/left_panel.dart 的定义，面部美化功能包含以下 5 个主要区域及其参数：

1. 面部轮廓 (face_contour)
   - 轮廓收紧 (contour_tighten)
   - V型下巴 (v_chin)
   - 脸型优化 (face_shape)

2. 鼻部塑形 (nose)
   - 鼻子长度 (nose_length)
   - 鼻梁高度 (bridge_height)
   - 鼻尖调整 (tip_adjust)
   - 鼻翼宽度 (nostril_width)
   - 鼻基抬高 (base_height)

3. 眼部美化 (eyes)
   - 双眼皮 (double_fold)
   - 开眼角 (canthal_tilt)
   - 去眼袋 (eye_bag_removal)
   - 提眼尾 (outer_corner_lift)

4. 唇部造型 (lips)
   - 唇形调整 (lip_shape)
   - 嘴唇厚度 (lip_thickness)
   - 嘴角上扬 (mouth_corner)
   - 唇色优化 (lip_color)

5. 抗衰冻龄 (anti_aging)
   - 法令纹 (nasolabial_folds)
   - 去皱纹 (wrinkle_removal)
   - 额头饱满 (forehead_fullness)
   - 面容紧致 (facial_firmness)

重要说明：
1. 这些区域和参数名称是固定的，作为系统的权威定义
2. 所有其他模块都必须严格遵循这些命名



禁止使用示例数据和假数据
#code_quality
#data_handling
#logging
#development_standards
Edit
在整个项目中严格禁止使用任何形式的示例数据、假数据或预设数据。所有功能必须基于实际数据运行，不得在代码中包含示例、演示或测试用的硬编码数据。

具体规则：
1. 所有日志输出不应包含"示例"、"演示"等字样
2. 不允许在代码中硬编码特征点数据、变形参数或其他可能被替换为真实数据的内容
3. 所有数据处理必须基于实际输入，不得使用默认值或预设值代替缺失数据
4. 调试信息应该反映实际状态，不应使用假设或示例来说明

这一规则适用于整个代码库，包括但不限于：特征点处理、图像变形、UI渲染和日志记录等所有功能模块。


# 最近修复记录
#recent_fixes
#image_scaling
#main_display_area

## 2025-06-30: 修复主图区变形图像缩放显示问题

**问题描述**: 
变形功能正常工作，右预览面板正确显示变形图像，但主图区只显示变形后图像的左上角部分。

**问题根源**: 
SimpleDeformationPainter中有6个位置使用了`canvas.drawImage(deformedImage, Offset.zero, Paint())`直接绘制变形图像，这种方式以1:1比例从左上角绘制，不进行缩放适配。

**解决方案**:
1. 创建了`_drawScaledImage`方法，使用与`_drawOriginalImage`相同的缩放逻辑
2. 替换所有直接绘制调用为正确的缩放显示方法
3. 确保变形图像能够正确缩放并居中显示在主图区

**修复位置**: 
- lib/core/simple_deformation_painter.dart: 第1824, 2101, 2208, 2321, 2569, 2871行

**修复效果**: 
- ✅ 主图区现在正确显示完整的变形图像
- ✅ 变形功能完全正常，未破坏任何变形代码  
- ✅ 图像正确缩放并居中显示，与变形前的原图显示方式一致
- ✅ 右预览面板继续正常工作

**重要提醒**: 变形系统的核心逻辑保持不变，只修复了显示层的缩放适配问题。

