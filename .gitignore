# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.dart_tool/package_config.json
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
testdata/
.DS_Store
.flutter-plugins-dependencies
temp_test/

.temp_test

# Python related
venv/
__pycache__/
*.py[cod]
*$py.class
.flutter-plugins-dependencies
.flutter-plugins-dependencies
.dart_tool/package_config.json
temp/
.temp

.dart_tool/package_config.json
.flutter-plugins-dependencies
.flutter-plugins-dependencies
.dart_tool/package_config.json
.DS_Store
.flutter-plugins-dependencies
macos/Flutter/ephemeral/flutter_export_environment.sh
macos/Flutter/ephemeral/Flutter-Generated.xcconfig
macos/Flutter/ephemeral/flutter_export_environment.sh
.flutter-plugins-dependencies
.flutter-plugins-dependencies
.dart_tool/package_config.json
.flutter-plugins-dependencies
.dart_tool/package_config.json
.flutter-plugins-dependencies
.dart_tool/package_config.json
.flutter-plugins-dependencies
