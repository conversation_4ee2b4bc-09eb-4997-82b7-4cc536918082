name: beautifun
description: <PERSON><PERSON><PERSON>un - AI美颜应用

publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  provider: ^6.0.0
  get: ^4.6.5
  window_size:
    git:
      url: https://github.com/google/flutter-desktop-embedding.git
      path: plugins/window_size
  file_selector: ^0.9.4
  file_selector_macos: ^0.9.4+2
  path: ^1.8.3
  path_provider: ^2.0.15
  flutter_animate: ^4.5.0
  intl: ^0.19.0
  process_run: ^0.14.2
  synchronized: ^3.1.0
  mutex: ^3.1.0
  logging: ^1.3.0
  file_picker: ^9.0.2
  vector_math: ^2.1.4
  # MediaPipe相关依赖
  google_ml_kit: ^0.16.3
  camera: ^0.10.5+9
  image: ^4.1.6
  collection: ^1.18.0
  camera_macos: ^0.0.9

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  mockito: ^5.4.5
  build_runner: ^2.4.15

flutter:
  uses-material-design: true
  
  assets:
    - assets/
    - assets/icons/
    - core/

platforms:
  macos:
    default: true
