#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
BeautiFun "炫"功能模块
这是"炫"功能的入口点，提供对外接口
"""

import logging
from .main import BeautifyFeature

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(module)s] [%(funcName)s] [%(type)s] | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S.%f'
)

# 导出主类，方便导入
__all__ = ['BeautifyFeature', 'initialize_beautify_feature']


def initialize_beautify_feature(image_path=None):
    """
    初始化并启动炫功能
    
    Args:
        image_path: 图像路径，可选
    
    Returns:
        BeautifyFeature实例
    """
    beautify = BeautifyFeature()
    
    if image_path:
        beautify.load_image(image_path)
    
    beautify.show_main_interface()
    
    return beautify
