#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
面部特征点检测处理器的包装脚本
用于处理参数并调用核心组件face_mesh_processor
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime

# 确保可以导入core目录下的模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入核心组件
from core.face_mesh_processor import FaceMeshProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(module)s] [%(funcName)s] | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S.%f'
)
logger = logging.getLogger(__name__)

def process_image(image_path, params=None):
    """处理图片并返回特征点信息
    
    Args:
        image_path: 图片路径
        params: 处理参数字典
        
    Returns:
        dict: 包含特征点信息的字典
    """
    try:
        # 记录输入参数
        logger.info(f"开始处理图片: {image_path}")
        logger.info(f"处理参数: {params}")
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            error_msg = f"图片文件不存在: {image_path}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
            
        # 创建处理器实例
        processor = FaceMeshProcessor()
        
        # 调用核心处理器处理图片
        logger.info("调用核心处理器")
        result = processor.process_image(image_path)
        
        # 解析处理结果
        try:
            if isinstance(result, str):
                result_data = json.loads(result)
            else:
                result_data = result
                
            # 确保landmarks可见性为1.0
            if 'landmarks' in result_data:
                for landmark in result_data['landmarks']:
                    if isinstance(landmark, dict) and 'visibility' in landmark:
                        landmark['visibility'] = 1.0
                        
            logger.info("处理完成")
            return result_data
            
        except json.JSONDecodeError as e:
            error_msg = f"结果解析失败: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
            
    except Exception as e:
        error_msg = f"处理失败: {str(e)}"
        logger.error(error_msg)
        return {"status": "error", "message": error_msg}

def main():
    """主函数，用于命令行调用"""
    parser = argparse.ArgumentParser(description='面部特征点检测处理器包装脚本')
    parser.add_argument('--params', required=True, help='处理参数，JSON格式字符串')
    
    try:
        args = parser.parse_args()
        
        # 解析参数
        try:
            params = json.loads(args.params)
        except json.JSONDecodeError:
            print(json.dumps({
                "status": "error",
                "message": "--params 必须是有效的JSON字符串"
            }))
            sys.exit(1)
            
        # 获取图片路径
        image_path = params.get('image_path')
        if not image_path:
            print(json.dumps({
                "status": "error",
                "message": "必须在params中指定image_path"
            }))
            sys.exit(1)
            
        # 处理图片
        result = process_image(image_path, params)
        print(json.dumps(result))
        
    except Exception as e:
        print(json.dumps({
            "status": "error",
            "message": str(e)
        }))
        sys.exit(1)

if __name__ == "__main__":
    main()
