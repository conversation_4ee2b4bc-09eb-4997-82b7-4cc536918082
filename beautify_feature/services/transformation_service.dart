import 'package:flutter/foundation.dart';
import '../../utils/logger.dart';

/// 变换服务
/// 负责管理变换参数和状态
class TransformationService extends ChangeNotifier {
  String? _selectedArea;
  String? _selectedParameter;
  double _parameterValue = 0.0;

  // 获取当前选中的区域
  String? get selectedArea => _selectedArea;

  // 获取当前选中的参数
  String? get selectedParameter => _selectedParameter;

  // 获取当前参数值
  double get parameterValue => _parameterValue;

  // 设置当前选中的区域
  void setSelectedArea(String area) {
    Logger.i('变换服务', '选择区域: $area');
    _selectedArea = area;
    notifyListeners();
  }

  // 设置当前选中的参数
  void setSelectedParameter(String parameter) {
    Logger.i('变换服务', '选择参数: $parameter');
    _selectedParameter = parameter;
    notifyListeners();
  }

  // 清除当前选中的参数
  void clearSelectedParameter() {
    Logger.i('变换服务', '清除选中参数');
    _selectedParameter = null;
    _parameterValue = 0.0;
    notifyListeners();
  }

  // 设置当前选中的参数
  void setSelectedParameter(String parameter) {
    Logger.i('变换服务', '选择参数: $parameter');
    _selectedParameter = parameter;
    notifyListeners();
  }

  // 清除当前选中的参数
  void clearSelectedParameter() {
    Logger.i('变换服务', '清除选中参数');
    _selectedParameter = null;
    _parameterValue = 0.0;
    notifyListeners();
  }

  // 设置参数值
  void setParameterValue(double value) {
    Logger.i('变换服务', '设置参数值: $value');
    _parameterValue = value;
    notifyListeners();
  }

  // 重置所有状态
  void reset() {
    Logger.i('变换服务', '重置所有状态');
    _selectedArea = null;
    _selectedParameter = null;
    _parameterValue = 0.0;
    notifyListeners();
  }
}
