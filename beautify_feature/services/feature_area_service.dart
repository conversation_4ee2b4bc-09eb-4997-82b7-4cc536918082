import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/feature_area_type.dart';
import '../../utils/logger.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;

/// 特征区域服务
/// 负责管理和显示特征点
class FeatureAreaService extends ChangeNotifier {
  FeatureAreaType? _selectedArea;
  Set<int> _visiblePoints = {};
  
  // Python服务器地址
  static const String _baseUrl = 'http://localhost:8000';

  // 获取当前选中的区域
  FeatureAreaType? get selectedArea => _selectedArea;

  // 设置当前选中的区域
  void selectArea(FeatureAreaType area) {
    Logger.i('特征区域服务', '选择区域: $area');
    _selectedArea = area;
    notifyListeners();
  }

  // 显示区域的所有特征点（合并该区域所有参数项的特征点）
  Future<void> showAreaPoints(String areaName, List<String> paramNames) async {
    Logger.i('特征区域服务', '显示区域特征点: $areaName, 参数: $paramNames');
    
    // 清除之前的点
    _visiblePoints.clear();
    
    // 获取该区域所有参数的特征点
    final points = await _getAreaFeaturePoints(areaName, paramNames);
    
    // 合并所有类型的点
    _visiblePoints.addAll(points['primary']!);
    _visiblePoints.addAll(points['secondary']!);
    _visiblePoints.addAll(points['auxiliary']!);
    
    Logger.i('特征区域服务', '显示特征点数量: ${_visiblePoints.length}');
    notifyListeners();
  }

  // 显示单个参数的特征点
  Future<void> showParameterPoints(String areaName, String paramName) async {
    Logger.i('特征区域服务', '显示参数特征点: $areaName - $paramName');
    
    // 清除之前的点
    _visiblePoints.clear();
    
    // 获取该参数的特征点
    final points = await _getParameterFeaturePoints(areaName, paramName);
    
    // 合并所有类型的点
    _visiblePoints.addAll(points['primary']!);
    _visiblePoints.addAll(points['secondary']!);
    _visiblePoints.addAll(points['auxiliary']!);
    
    Logger.i('特征区域服务', '显示特征点数量: ${_visiblePoints.length}');
    notifyListeners();
  }

  // 从Python服务获取区域特征点
  Future<Map<String, List<int>>> _getAreaFeaturePoints(String areaName, List<String> paramNames) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/get_area_feature_points'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'area_name': areaName,
          'param_names': paramNames,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return {
          'primary': List<int>.from(data['primary']),
          'secondary': List<int>.from(data['secondary']),
          'auxiliary': List<int>.from(data['auxiliary']),
        };
      } else {
        Logger.e('特征区域服务', '获取区域特征点失败: ${response.statusCode}');
        return {'primary': [], 'secondary': [], 'auxiliary': []};
      }
    } catch (e) {
      Logger.e('特征区域服务', '获取区域特征点异常: $e');
      return {'primary': [], 'secondary': [], 'auxiliary': []};
    }
  }

  // 从Python服务获取参数特征点
  Future<Map<String, List<int>>> _getParameterFeaturePoints(String areaName, String paramName) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/get_parameter_feature_points'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'area_name': areaName,
          'param_name': paramName,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return {
          'primary': List<int>.from(data['primary']),
          'secondary': List<int>.from(data['secondary']),
          'auxiliary': List<int>.from(data['auxiliary']),
        };
      } else {
        Logger.e('特征区域服务', '获取参数特征点失败: ${response.statusCode}');
        return {'primary': [], 'secondary': [], 'auxiliary': []};
      }
    } catch (e) {
      Logger.e('特征区域服务', '获取参数特征点异常: $e');
      return {'primary': [], 'secondary': [], 'auxiliary': []};
    }
  }

  // 显示区域的所有特征点（合并该区域所有参数项的特征点）
  Future<void> showAreaPoints(String areaName, List<String> paramNames) async {
    Logger.i('特征区域服务', '显示区域特征点: $areaName, 参数: $paramNames');
    
    // 清除之前的点
    _visiblePoints.clear();
    
    // 获取该区域所有参数的特征点
    final points = await _getAreaFeaturePoints(areaName, paramNames);
    
    // 合并所有类型的点
    _visiblePoints.addAll(points['primary']!);
    _visiblePoints.addAll(points['secondary']!);
    _visiblePoints.addAll(points['auxiliary']!);
    
    Logger.i('特征区域服务', '显示特征点数量: ${_visiblePoints.length}');
    notifyListeners();
  }

  // 显示单个参数的特征点
  Future<void> showParameterPoints(String areaName, String paramName) async {
    Logger.i('特征区域服务', '显示参数特征点: $areaName - $paramName');
    
    // 清除之前的点
    _visiblePoints.clear();
    
    // 获取该参数的特征点
    final points = await _getParameterFeaturePoints(areaName, paramName);
    
    // 合并所有类型的点
    _visiblePoints.addAll(points['primary']!);
    _visiblePoints.addAll(points['secondary']!);
    _visiblePoints.addAll(points['auxiliary']!);
    
    Logger.i('特征区域服务', '显示特征点数量: ${_visiblePoints.length}');
    notifyListeners();
  }

  // 隐藏所有特征点
  void hidePoints() {
    Logger.i('特征区域服务', '隐藏所有特征点');
    _visiblePoints.clear();
    notifyListeners();
  }

  // 获取当前可见的特征点
  Set<int> get visiblePoints => _visiblePoints;
}
