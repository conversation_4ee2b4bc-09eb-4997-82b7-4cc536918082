#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
BeautiFun "炫"功能 - 扫描动画
实现设计文档中定义的扫描动画效果
"""

import logging
import time

logger = logging.getLogger(__name__)

class ScanningAnimation:
    """
    扫描动画类
    实现设计规范中定义的扫描动画效果，持续时间300ms，缓动函数ease-in-out
    """
    
    def __init__(self):
        """初始化扫描动画"""
        logger.info("Initialize", "START", "初始化扫描动画")
        
        # 动画参数
        self.duration = 300  # 持续时间，单位毫秒
        self.easing = "ease-in-out"  # 缓动函数
        self.scan_line_color = "#4A90E2"  # 扫描线颜色，科技蓝
        self.scan_line_width = 2  # 扫描线宽度，单位像素
        self.glow_color = "#D4AF37"  # 发光效果颜色，香槟金
        self.glow_radius = 10  # 发光半径，单位像素
        
        # 动画状态
        self.is_playing = False
        self.start_time = 0
        self.progress = 0
        
        logger.info("Initialize", "END", "扫描动画初始化完成")
    
    def start(self):
        """开始播放扫描动画"""
        logger.info("Start", "START", "开始扫描动画")
        
        if self.is_playing:
            logger.info("Start", "PROCESS", "动画已在播放中，忽略此次调用")
            return
        
        self.is_playing = True
        self.start_time = time.time() * 1000  # 转换为毫秒
        self.progress = 0
        
        logger.info("Start", "END", "扫描动画开始播放")
    
    def update(self):
        """
        更新动画状态
        
        Returns:
            动画是否完成
        """
        if not self.is_playing:
            return True
        
        current_time = time.time() * 1000
        elapsed = current_time - self.start_time
        
        if elapsed >= self.duration:
            self.progress = 1.0
            self.is_playing = False
            logger.info("Update", "PROCESS", "扫描动画完成")
            return True
        
        # 计算进度，应用缓动函数
        raw_progress = elapsed / self.duration
        self.progress = self._apply_easing(raw_progress)
        
        logger.debug("Update", "PROCESS", f"扫描动画进度: {self.progress:.2f}")
        
        return False
    
    def _apply_easing(self, t):
        """
        应用缓动函数
        
        Args:
            t: 原始进度，0.0-1.0
        
        Returns:
            应用缓动后的进度，0.0-1.0
        """
        # 实现ease-in-out缓动函数
        # t < 0.5 ? 2*t*t : -1+(4-2*t)*t
        if t < 0.5:
            return 2 * t * t
        else:
            return -1 + (4 - 2 * t) * t
    
    def draw(self, canvas):
        """
        绘制扫描动画
        
        Args:
            canvas: 绘图画布
        """
        if not self.is_playing and self.progress == 0:
            return
        
        # 这里将来会实现实际的绘制逻辑
        # 根据self.progress计算扫描线位置和效果
        
        logger.debug("Draw", "PROCESS", "绘制扫描动画，进度: {0:.2f}".format(self.progress))
    
    def stop(self):
        """停止扫描动画"""
        if not self.is_playing:
            return
        
        logger.info("Stop", "START", "停止扫描动画")
        
        self.is_playing = False
        self.progress = 0
        
        logger.info("Stop", "END", "扫描动画已停止")


# 创建扫描动画的便捷函数
def create_scanning_animation():
    """
    创建扫描动画实例
    
    Returns:
        ScanningAnimation实例
    """
    return ScanningAnimation()
