#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
BeautiFun "炫"功能 - 色彩系统
定义并管理符合设计规范的色彩方案
"""

class Colors:
    """色彩系统类，定义所有UI元素使用的颜色"""
    
    # 主色调
    PRIMARY = "#4A90E2"  # 科技蓝
    ACCENT = "#D4AF37"   # 香槟金
    BACKGROUND = "#F5F5F5"  # 中性灰
    
    # 功能色
    SUCCESS = "#27AE60"  # 成功绿
    WARNING = "#F39C12"  # 警告黄
    ERROR = "#E74C3C"    # 错误红
    INFO = "#3498DB"     # 信息蓝
    
    # 文本色
    TEXT_PRIMARY = "#2C3E50"    # 主要文本
    TEXT_SECONDARY = "#7F8C8D"  # 次要文本
    TEXT_DISABLED = "#BDC3C7"   # 禁用文本
    
    # 边框和分隔线
    BORDER = "#ECF0F1"  # 边框色
    DIVIDER = "#D6DBDF"  # 分隔线
    
    # 特殊效果色
    GLOW = "#F1C40F"  # 发光效果
    SHADOW = "#2C3E50"  # 阴影色
    
    @classmethod
    def get_gradient(cls, type="primary"):
        """
        获取渐变色配置
        
        Args:
            type: 渐变类型，可选值：primary, accent, tech
        
        Returns:
            渐变色配置字典
        """
        gradients = {
            "primary": {
                "colors": [cls.PRIMARY, cls.PRIMARY + "33"],  # 33表示20%透明度
                "stops": [0.0, 1.0],
                "angle": 45
            },
            "accent": {
                "colors": [cls.ACCENT, cls.ACCENT + "33"],
                "stops": [0.0, 1.0],
                "angle": 45
            },
            "tech": {
                "colors": [cls.ACCENT + "1A", cls.PRIMARY + "33"],  # 1A表示10%透明度
                "stops": [0.3, 0.7],
                "angle": 135
            }
        }
        
        return gradients.get(type, gradients["primary"])
    
    @classmethod
    def get_shadow(cls, level=1):
        """
        获取阴影配置
        
        Args:
            level: 阴影级别，1-4，对应四个UI层级
        
        Returns:
            阴影配置字典
        """
        shadows = {
            1: {  # 背景层，无阴影
                "color": cls.SHADOW + "00",
                "offset_x": 0,
                "offset_y": 0,
                "blur_radius": 0,
                "spread_radius": 0
            },
            2: {  # 图像层，轻微阴影
                "color": cls.SHADOW + "40",  # 40表示25%透明度
                "offset_x": 0,
                "offset_y": 8,
                "blur_radius": 4,
                "spread_radius": 0
            },
            3: {  # 功能层，明显阴影
                "color": cls.SHADOW + "66",  # 66表示40%透明度
                "offset_x": 0,
                "offset_y": 16,
                "blur_radius": 8,
                "spread_radius": 0
            },
            4: {  # 悬浮提示层，强烈阴影
                "color": cls.SHADOW + "80",  # 80表示50%透明度
                "offset_x": 0,
                "offset_y": 24,
                "blur_radius": 12,
                "spread_radius": 0
            }
        }
        
        return shadows.get(level, shadows[1])
    
    @classmethod
    def get_risk_colors(cls):
        """
        获取风险等级颜色
        
        Returns:
            风险等级颜色字典
        """
        return {
            "low": cls.SUCCESS + "4D",     # 4D表示30%透明度
            "medium": cls.WARNING + "80",  # 80表示50%透明度
            "high": cls.ERROR + "B3"       # B3表示70%透明度
        }


# 导出颜色常量，方便导入使用
PRIMARY = Colors.PRIMARY
ACCENT = Colors.ACCENT
BACKGROUND = Colors.BACKGROUND
