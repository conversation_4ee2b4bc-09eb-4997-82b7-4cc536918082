import os
import cv2
import numpy as np
from src.feature_processor.feature_extractor import FacialFeatureExtractor
from src.feature_processor.feature_interpolator import FeatureInterpolator
from src.feature_processor.image_morpher import ImageMorpher
from src.morph.mobile_morph import MobileMorphingWidget
from PyQt5.QtWidgets import QApplication
import sys

def resize_image(img, max_size=800):
    """调整图片大小，保持宽高比，同时确保宽度和高度相同"""
    h, w = img.shape[:2]
    
    # 计算缩放比例
    scale = min(max_size / max(h, w), 1.0)
    
    # 计算新的尺寸
    new_h = int(h * scale)
    new_w = int(w * scale)
    
    # 确保宽高一致
    target_size = max(new_h, new_w)
    
    # 先调整到计算出的尺寸
    resized = cv2.resize(img, (new_w, new_h))
    
    # 创建一个正方形的黑色背景
    square_img = np.zeros((target_size, target_size, 3), dtype=np.uint8)
    
    # 计算居中的位置
    y_offset = (target_size - new_h) // 2
    x_offset = (target_size - new_w) // 2
    
    # 将调整后的图片放在正方形背景的中央
    square_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
    
    return square_img

def run_morph_test():
    """运行变形测试，使用已验证的高质量组件"""
    try:
        # 初始化已验证的组件
        print("初始化组件...")
        feature_extractor = FacialFeatureExtractor()  # 使用已验证的特征提取器
        feature_interpolator = FeatureInterpolator()  # 使用已验证的特征插值器
        image_morpher = ImageMorpher()  # 使用已验证的图像变形器
        
        # 读取测试图片
        source_path = os.path.join('assets', 'a1.jpg')
        target_path = os.path.join('assets', 'a2.jpg')
        
        print(f"读取图片:\n  源图片: {os.path.abspath(source_path)}\n  目标图片: {os.path.abspath(target_path)}")
        
        source_img = cv2.imread(source_path)
        target_img = cv2.imread(target_path)
        
        if source_img is None or target_img is None:
            raise ValueError(f"无法读取测试图片:\n  源图片: {os.path.abspath(source_path)}\n  目标图片: {os.path.abspath(target_path)}")
        
        print("成功读取测试图片")
        print(f"原始图片大小:\n  源图片: {source_img.shape}\n  目标图片: {target_img.shape}")
        
        # 调整图片大小，确保两张图片具有相同的尺寸
        print("\n调整图片大小...")
        source_img = resize_image(source_img, max_size=800)
        target_img = resize_image(target_img, max_size=800)
        
        print(f"处理后的图片大小:\n  源图片: {source_img.shape}\n  目标图片: {target_img.shape}")
        
        # 使用已验证的特征提取器提取特征
        print("\n提取面部特征...")
        source_features = feature_extractor.extract_features(source_img)
        if source_features is None:
            raise ValueError("无法从源图片提取特征，请确保图片中包含清晰的人脸")
            
        target_features = feature_extractor.extract_features(target_img)
        if target_features is None:
            raise ValueError("无法从目标图片提取特征，请确保图片中包含清晰的人脸")
        
        print("成功提取面部特征")
        print(f"特征点数量:\n  源图片: {len(source_features['landmarks_2d'])}\n  目标图片: {len(target_features['landmarks_2d'])}")
            
        # 使用已验证的特征插值器生成渐变特征序列
        print("\n生成特征序列...")
        try:
            feature_sequence = feature_interpolator.interpolate(
                source_features,
                target_features,
                steps=5
            )
            print(f"成功生成特征序列，共{len(feature_sequence)}帧")
        except Exception as e:
            print(f"Error: 特征插值失败 - {str(e)}")
            import traceback
            print(traceback.format_exc())
            raise
        
        # 使用已验证的图像变形器生成渐变图像序列
        print("\n生成变形序列...")
        try:
            morph_sequence = image_morpher.generate_morphed_sequence(
                source_img,
                target_img,
                feature_sequence,
                steps=5
            )
            print(f"成功生成变形序列，共{len(morph_sequence)}帧")
        except Exception as e:
            print(f"Error: 图像变形失败 - {str(e)}")
            import traceback
            print(traceback.format_exc())
            raise
        
        # 使用移动端优化的界面显示结果
        print("\n显示结果...")
        app = QApplication(sys.argv)
        widget = MobileMorphingWidget()
        widget.update_display(source_img, target_img, morph_sequence)
        widget.show()
        
        return app.exec_()
        
    except Exception as e:
        print(f"\nError: 测试过程中出现错误 - {str(e)}")
        import traceback
        print(traceback.format_exc())
        raise

if __name__ == "__main__":
    run_morph_test()
