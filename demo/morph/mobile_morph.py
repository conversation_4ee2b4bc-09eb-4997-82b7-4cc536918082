"""
移动端变形界面模块
支持源图像到目标图像的渐变变形显示
"""
from PyQt5.QtWidgets import QWidget, QHBoxLayout, QLabel, QVBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QImage, QPixmap
import cv2
import numpy as np

class MobileMorphingWidget(QWidget):
    def __init__(self, num_frames=5):
        """
        初始化变形显示部件
        Args:
            num_frames: 中间过渡帧数量
        """
        super().__init__()
        self.num_frames = num_frames
        self.source_image = None
        self.target_image = None
        self.morph_frames = []
        self.source_features = None
        self.target_features = None
        self.initUI()
        
    def initUI(self):
        """初始化UI"""
        self.setWindowTitle('Face Morphing Preview')
        self.setMinimumSize(1200, 300)
        
        # 创建水平布局
        layout = QHBoxLayout()
        layout.setSpacing(10)
        
        # 创建图像显示标签列表
        self.image_labels = []
        
        # 源图像
        source_container = QVBoxLayout()
        source_label = QLabel('Source')
        source_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.source_image_label = QLabel()
        self.source_image_label.setFixedSize(200, 200)
        self.source_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        source_container.addWidget(source_label)
        source_container.addWidget(self.source_image_label)
        layout.addLayout(source_container)
        self.image_labels.append(self.source_image_label)
        
        # 中间帧
        for i in range(self.num_frames):
            frame_container = QVBoxLayout()
            frame_label = QLabel(f'Frame {i+1}')
            frame_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label = QLabel()
            image_label.setFixedSize(200, 200)
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            frame_container.addWidget(frame_label)
            frame_container.addWidget(image_label)
            layout.addLayout(frame_container)
            self.image_labels.append(image_label)
        
        # 目标图像
        target_container = QVBoxLayout()
        target_label = QLabel('Target')
        target_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.target_image_label = QLabel()
        self.target_image_label.setFixedSize(200, 200)
        self.target_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        target_container.addWidget(target_label)
        target_container.addWidget(self.target_image_label)
        layout.addLayout(target_container)
        self.image_labels.append(self.target_image_label)
        
        self.setLayout(layout)
        
    def set_source_image(self, image: np.ndarray, features: dict = None):
        """设置源图像和特征"""
        self.source_image = image.copy()
        self.source_features = features
        self._update_display()
        
    def set_target_image(self, image: np.ndarray, features: dict = None):
        """设置目标图像和特征"""
        self.target_image = image.copy()
        self.target_features = features
        self._update_display()
        
    def set_morph_frames(self, frames: list):
        """设置变形过程的中间帧"""
        self.morph_frames = [frame.copy() for frame in frames]
        self._update_display()
        
    def _update_display(self):
        """更新所有图像的显示"""
        # 显示源图像
        if self.source_image is not None:
            self._show_image_with_features(
                self.source_image,
                self.source_features,
                self.image_labels[0]
            )
            
        # 显示中间帧
        for i, frame in enumerate(self.morph_frames):
            if i < len(self.image_labels) - 2:  # -2是因为首尾是源图和目标图
                self._show_image_with_features(
                    frame,
                    None,  # 中间帧暂时不显示特征点
                    self.image_labels[i + 1]
                )
                
        # 显示目标图像
        if self.target_image is not None:
            self._show_image_with_features(
                self.target_image,
                self.target_features,
                self.image_labels[-1]
            )
            
    def _show_image_with_features(self, image: np.ndarray, features: dict, label: QLabel):
        """在指定标签上显示图像和特征点"""
        if image is None:
            return
            
        display_image = image.copy()
        
        # 如果有特征点，绘制它们
        if features is not None and 'landmarks_2d' in features:
            landmarks = features['landmarks_2d']
            for point in landmarks:
                cv2.circle(display_image, tuple(point), 1, (0, 255, 0), -1)
        
        # 转换为Qt图像
        height, width = display_image.shape[:2]
        bytes_per_line = 3 * width
        qt_image = QImage(
            display_image.data,
            width,
            height,
            bytes_per_line,
            QImage.Format.Format_BGR888
        )
        
        # 设置到标签
        pixmap = QPixmap.fromImage(qt_image)
        scaled_pixmap = pixmap.scaled(
            label.size(),
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )
        label.setPixmap(scaled_pixmap)
