"""
面部特征分析模块
用于分析面部特征差异，支持特征插值
"""

import numpy as np
from typing import Dict, List, Tuple, Optional

class FeatureAnalyzer:
    """面部特征分析器"""
    
    def __init__(self):
        """初始化特征分析器"""
        # 设置特征权重
        self.feature_weights = {
            'landmarks_2d': 1.0,    # 2D关键点权重
            'landmarks_3d': 1.5,    # 3D关键点权重（更重要）
            'regions': 0.8,         # 区域特征权重
            'contours': 0.8         # 轮廓特征权重
        }
        
    def analyze_difference(self, source_features: Dict, target_features: Dict) -> Dict:
        """
        分析两组特征之间的差异
        
        Args:
            source_features: 源图片特征
            target_features: 目标图片特征
            
        Returns:
            特征差异映射
        """
        differences = {}
        
        # 1. 分析2D关键点差异
        differences['landmarks_2d'] = {
            'diff': target_features['landmarks_2d'] - source_features['landmarks_2d'],
            'magnitude': np.linalg.norm(
                target_features['landmarks_2d'] - source_features['landmarks_2d']
            )
        }
        
        # 2. 分析3D关键点差异
        differences['landmarks_3d'] = {
            'diff': target_features['landmarks_3d'] - source_features['landmarks_3d'],
            'magnitude': np.linalg.norm(
                target_features['landmarks_3d'] - source_features['landmarks_3d']
            )
        }
        
        # 3. 分析区域差异
        differences['regions'] = self._analyze_region_differences(
            source_features['regions'],
            target_features['regions']
        )
        
        # 4. 分析轮廓差异
        differences['contours'] = self._analyze_contour_differences(
            source_features['contours'],
            target_features['contours']
        )
        
        return differences
        
    def interpolate_features(self, source_features: Dict, target_features: Dict, 
                           steps: int) -> List[Dict]:
        """
        生成两组特征之间的插值序列
        
        Args:
            source_features: 源图片特征
            target_features: 目标图片特征
            steps: 生成的特征数量
            
        Returns:
            特征序列
        """
        feature_sequence = []
        steps = int(steps)  # 确保steps是整数
        
        for i in range(steps):
            step = i / (steps - 1)  # 从0到1的插值系数
            
            # 1. 插值2D关键点
            landmarks_2d = self._linear_interpolate(
                source_features['landmarks_2d'],
                target_features['landmarks_2d'],
                step
            )
            
            # 2. 插值3D关键点
            landmarks_3d = self._linear_interpolate(
                source_features['landmarks_3d'],
                target_features['landmarks_3d'],
                step
            )
            
            # 3. 插值区域特征
            regions = self._interpolate_regions(
                source_features['regions'],
                target_features['regions'],
                step
            )
            
            # 4. 插值轮廓特征
            contours = self._interpolate_contours(
                source_features['contours'],
                target_features['contours'],
                step
            )
            
            # 构建当前特征
            current_features = {
                'landmarks_2d': landmarks_2d,
                'landmarks_3d': landmarks_3d,
                'regions': regions,
                'contours': contours
            }
            
            feature_sequence.append(current_features)
            
        return feature_sequence
        
    def _analyze_region_differences(self, source_regions: Dict, 
                                  target_regions: Dict) -> Dict:
        """分析区域差异"""
        differences = {}
        
        for region in ['left_eye', 'right_eye', 'nose', 'mouth']:
            # 计算中心点偏移
            center_diff = target_regions[region]['center'] - source_regions[region]['center']
            # 计算大小变化
            size_diff = target_regions[region]['size'] - source_regions[region]['size']
            
            differences[region] = {
                'center_diff': center_diff,
                'size_diff': size_diff,
                'magnitude': np.linalg.norm(center_diff) + np.linalg.norm(size_diff)
            }
            
        return differences
        
    def _analyze_contour_differences(self, source_contours: Dict,
                                   target_contours: Dict) -> Dict:
        """分析轮廓差异"""
        differences = {}
        
        # 分析脸型差异
        face_width_diff = target_contours['face']['width'] - source_contours['face']['width']
        face_height_diff = target_contours['face']['height'] - source_contours['face']['height']
        
        differences['face'] = {
            'width_diff': face_width_diff,
            'height_diff': face_height_diff,
            'magnitude': np.sqrt(face_width_diff**2 + face_height_diff**2)
        }
        
        # 分析眉毛差异
        for brow in ['left_eyebrow', 'right_eyebrow']:
            angle_diff = target_contours[brow]['angle'] - source_contours[brow]['angle']
            center_diff = target_contours[brow]['center'] - source_contours[brow]['center']
            
            differences[brow] = {
                'angle_diff': angle_diff,
                'center_diff': center_diff,
                'magnitude': np.abs(angle_diff) + np.linalg.norm(center_diff)
            }
            
        return differences
        
    def _linear_interpolate(self, source: np.ndarray, target: np.ndarray,
                          step: float) -> np.ndarray:
        """线性插值"""
        return source + (target - source) * step
        
    def _interpolate_regions(self, source_regions: Dict, target_regions: Dict,
                           step: float) -> Dict:
        """插值区域特征"""
        interpolated = {}
        
        for region in ['left_eye', 'right_eye', 'nose', 'mouth']:
            interpolated[region] = {
                'points': self._linear_interpolate(
                    source_regions[region]['points'],
                    target_regions[region]['points'],
                    step
                ),
                'center': self._linear_interpolate(
                    source_regions[region]['center'],
                    target_regions[region]['center'],
                    step
                ),
                'size': self._linear_interpolate(
                    source_regions[region]['size'],
                    target_regions[region]['size'],
                    step
                )
            }
            
        return interpolated
        
    def _interpolate_contours(self, source_contours: Dict, target_contours: Dict,
                            step: float) -> Dict:
        """插值轮廓特征"""
        interpolated = {}
        
        # 插值脸型
        face_width = source_contours['face']['width'] + \
                    (target_contours['face']['width'] - source_contours['face']['width']) * step
        face_height = source_contours['face']['height'] + \
                     (target_contours['face']['height'] - source_contours['face']['height']) * step
        
        interpolated['face'] = {
            'points': self._linear_interpolate(
                source_contours['face']['points'],
                target_contours['face']['points'],
                step
            ),
            'center': self._linear_interpolate(
                source_contours['face']['center'],
                target_contours['face']['center'],
                step
            ),
            'width': face_width,
            'height': face_height
        }
        
        # 插值眉毛
        for brow in ['left_eyebrow', 'right_eyebrow']:
            angle = source_contours[brow]['angle'] + \
                    (target_contours[brow]['angle'] - source_contours[brow]['angle']) * step
                    
            interpolated[brow] = {
                'points': self._linear_interpolate(
                    source_contours[brow]['points'],
                    target_contours[brow]['points'],
                    step
                ),
                'center': self._linear_interpolate(
                    source_contours[brow]['center'],
                    target_contours[brow]['center'],
                    step
                ),
                'angle': angle
            }
            
        return interpolated
