"""
面部特征提取模块
用于提取和分析面部特征，支持渐变效果生成
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional
import insightface
from insightface.app import FaceAnalysis

class FacialFeatureExtractor:
    """面部特征提取器"""
    
    def __init__(self):
        """初始化面部特征提取器"""
        # 初始化InsightFace分析器
        self.face_analyzer = FaceAnalysis(
            name='buffalo_l',
            providers=['CPUExecutionProvider']
        )
        self.face_analyzer.prepare(ctx_id=0, det_size=(640, 640))
        
    def extract_features(self, face_image: np.ndarray) -> Dict:
        """
        提取面部特征
        
        Args:
            face_image: 输入的人脸图片
            
        Returns:
            包含各种面部特征的字典
        """
        try:
            # 验证输入图像
            if face_image is None:
                raise ValueError("输入图像为空")
                
            if not isinstance(face_image, np.ndarray):
                raise ValueError("输入图像必须是numpy数组")
                
            if len(face_image.shape) != 3:
                raise ValueError(f"输入图像必须是3通道彩色图像，当前shape={face_image.shape}")
                
            if face_image.shape[2] != 3:
                raise ValueError(f"输入图像必须是3通道彩色图像，当前通道数={face_image.shape[2]}")
                
            # 使用InsightFace进行基础分析
            print("开始检测人脸...")
            faces = self.face_analyzer.get(face_image)
            if not faces:
                raise ValueError("未检测到人脸，请确保图像中包含清晰的正面人脸")
                
            if len(faces) > 1:
                print(f"警告：检测到多个人脸（{len(faces)}个），将使用第一个检测到的人脸")
                
            face = faces[0]  # 使用第一个检测到的人脸
            print("成功检测到人脸")
            
            # 1. 提取关键点
            print("提取面部关键点...")
            landmarks = face.landmark_2d_106
            landmarks_3d = face.landmark_3d_68
            
            if landmarks is None or landmarks_3d is None:
                raise ValueError("无法提取面部关键点，请确保人脸清晰可见")
                
            print(f"成功提取关键点：\n  2D关键点：{landmarks.shape}\n  3D关键点：{landmarks_3d.shape}")
            
            # 验证关键点的有效性
            if not np.all(np.isfinite(landmarks)) or not np.all(np.isfinite(landmarks_3d)):
                raise ValueError("检测到无效的关键点坐标（NaN或Inf）")
                
            # 验证关键点是否在图像范围内
            h, w = face_image.shape[:2]
            if not (np.all(landmarks[:, 0] >= 0) and np.all(landmarks[:, 0] < w) and
                   np.all(landmarks[:, 1] >= 0) and np.all(landmarks[:, 1] < h)):
                print("警告：部分关键点超出图像范围，这可能影响变形效果")
            
            # 2. 提取基本特征
            print("提取面部特征...")
            features = {
                # 2D关键点
                'landmarks_2d': landmarks,
                # 3D关键点
                'landmarks_3d': landmarks_3d,
                # 面部姿态
                'pose': self._extract_pose(landmarks_3d),
                # 面部区域
                'regions': self._extract_regions(landmarks),
                # 轮廓特征
                'contours': self._extract_contours(landmarks),
                # 基础属性
                'attributes': {
                    'gender': face.gender,
                    'age': face.age,
                    'face_score': face.det_score,  # 人脸检测的置信度
                }
            }
            
            # 验证提取的特征
            required_keys = ['landmarks_2d', 'landmarks_3d', 'pose', 'regions', 'contours']
            for key in required_keys:
                if key not in features:
                    raise ValueError(f"缺少必要的特征：{key}")
                    
            print("特征提取完成")
            return features
            
        except Exception as e:
            print(f"特征提取失败：{str(e)}")
            import traceback
            print(traceback.format_exc())
            raise
            
    def _extract_pose(self, landmarks_3d: np.ndarray) -> Dict:
        """提取面部姿态信息"""
        # 使用鼻尖和眼睛位置估算姿态
        nose_tip = landmarks_3d[30]  # 鼻尖
        left_eye = np.mean(landmarks_3d[36:42], axis=0)  # 左眼中心
        right_eye = np.mean(landmarks_3d[42:48], axis=0)  # 右眼中心
        
        # 计算面部朝向
        face_normal = np.cross(right_eye - left_eye, nose_tip - left_eye)
        face_normal = face_normal / np.linalg.norm(face_normal)
        
        return {
            'normal': face_normal,
            'nose_tip': nose_tip,
            'left_eye': left_eye,
            'right_eye': right_eye
        }
        
    def _extract_regions(self, landmarks: np.ndarray) -> Dict:
        """提取面部区域信息"""
        # 眼睛区域
        left_eye = landmarks[60:68]   # 左眼轮廓点
        right_eye = landmarks[68:76]  # 右眼轮廓点
        
        # 鼻子区域
        nose = landmarks[84:96]  # 鼻子轮廓点
        
        # 嘴巴区域
        mouth = landmarks[96:106]  # 嘴巴轮廓点
        
        return {
            'left_eye': {
                'points': left_eye,
                'center': np.mean(left_eye, axis=0),
                'size': np.max(left_eye, axis=0) - np.min(left_eye, axis=0)
            },
            'right_eye': {
                'points': right_eye,
                'center': np.mean(right_eye, axis=0),
                'size': np.max(right_eye, axis=0) - np.min(right_eye, axis=0)
            },
            'nose': {
                'points': nose,
                'center': np.mean(nose, axis=0),
                'size': np.max(nose, axis=0) - np.min(nose, axis=0)
            },
            'mouth': {
                'points': mouth,
                'center': np.mean(mouth, axis=0),
                'size': np.max(mouth, axis=0) - np.min(mouth, axis=0)
            }
        }
        
    def _extract_contours(self, landmarks: np.ndarray) -> Dict:
        """提取面部轮廓特征"""
        # 脸型轮廓
        face_contour = landmarks[0:33]  # 脸型轮廓点
        
        # 眉毛轮廓
        left_eyebrow = landmarks[33:42]   # 左眉毛轮廓点
        right_eyebrow = landmarks[42:51]  # 右眉毛轮廓点
        
        return {
            'face': {
                'points': face_contour,
                'center': np.mean(face_contour, axis=0),
                'width': np.max(face_contour[:, 0]) - np.min(face_contour[:, 0]),
                'height': np.max(face_contour[:, 1]) - np.min(face_contour[:, 1])
            },
            'left_eyebrow': {
                'points': left_eyebrow,
                'center': np.mean(left_eyebrow, axis=0),
                'angle': self._calculate_angle(left_eyebrow)
            },
            'right_eyebrow': {
                'points': right_eyebrow,
                'center': np.mean(right_eyebrow, axis=0),
                'angle': self._calculate_angle(right_eyebrow)
            }
        }
        
    def _calculate_angle(self, points: np.ndarray) -> float:
        """计算点集的主方向角度"""
        # 使用PCA计算主方向
        centered = points - np.mean(points, axis=0)
        cov = np.cov(centered.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov)
        main_direction = eigenvectors[np.argmax(eigenvalues)]
        angle = np.arctan2(main_direction[1], main_direction[0])
        return np.degrees(angle)
