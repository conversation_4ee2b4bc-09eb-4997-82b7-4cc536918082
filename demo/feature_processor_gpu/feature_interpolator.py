"""特征插值模块"""
import numpy as np

class FeatureInterpolator:
    """特征插值器，用于生成特征的平滑过渡序列"""
    
    def __init__(self):
        """初始化特征插值器"""
        pass
        
    def interpolate(self, source_features: dict, target_features: dict, steps: int = 20):
        """
        在两组特征之间生成插值序列
        
        Args:
            source_features: 源特征
            target_features: 目标特征
            steps: 插值步数
            
        Returns:
            list: 插值特征序列
        """
        # 提取特征点
        source_points_2d = source_features.get('landmarks_2d', None)
        source_points_3d = source_features.get('landmarks_3d', None)
        target_points_2d = target_features.get('landmarks_2d', None)
        target_points_3d = target_features.get('landmarks_3d', None)
        
        # 验证特征点
        if source_points_2d is None or target_points_2d is None:
            raise ValueError("无法获取2D特征点")
            
        if not isinstance(source_points_2d, np.ndarray) or not isinstance(target_points_2d, np.ndarray):
            raise ValueError("特征点必须是numpy数组")
            
        if source_points_2d.shape != target_points_2d.shape:
            raise ValueError(f"源特征点和目标特征点的形状不一致：{source_points_2d.shape} vs {target_points_2d.shape}")
            
        # 验证特征点数量
        if len(source_points_2d) < 3:
            raise ValueError("特征点数量必须至少为3个")
            
        print(f"特征点数量：{len(source_points_2d)}")
        print(f"源特征点范围：{np.min(source_points_2d, axis=0)} - {np.max(source_points_2d, axis=0)}")
        print(f"目标特征点范围：{np.min(target_points_2d, axis=0)} - {np.max(target_points_2d, axis=0)}")
        
        # 生成插值序列
        feature_sequence = []
        for i in range(steps):
            try:
                # 计算当前步骤的权重（使用平滑的S形曲线）
                t = i / (steps - 1)
                alpha = self._smooth_step(t)
                
                # 线性插值2D特征点
                current_points_2d = source_points_2d * (1 - alpha) + target_points_2d * alpha
                
                # 如果有3D特征点，也进行插值
                current_points_3d = None
                if source_points_3d is not None and target_points_3d is not None:
                    if source_points_3d.shape == target_points_3d.shape:
                        current_points_3d = source_points_3d * (1 - alpha) + target_points_3d * alpha
                    else:
                        print("警告：3D特征点形状不一致，跳过3D特征点插值")
                
                # 创建当前特征字典
                current_features = {
                    'landmarks_2d': current_points_2d,           # 当前帧的2D特征点
                    'landmarks_3d': current_points_3d,           # 当前帧的3D特征点（如果有）
                    'source_landmarks_2d': source_points_2d,     # 用于变形的源点
                    'target_landmarks_2d': target_points_2d,     # 用于变形的目标点
                    'weight': alpha                              # 当前帧的权重
                }
                
                feature_sequence.append(current_features)
                
            except Exception as e:
                print(f"生成第{i}帧特征时出错：{str(e)}")
                continue
        
        if not feature_sequence:
            raise ValueError("未能生成任何有效的特征序列")
            
        return feature_sequence
        
    def _smooth_step(self, x):
        """平滑的S形插值函数"""
        # 使用三次Hermite插值多项式
        return x * x * (3 - 2 * x)
