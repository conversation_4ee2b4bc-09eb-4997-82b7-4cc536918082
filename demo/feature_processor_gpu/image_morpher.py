"""
图像变形模块，用于生成渐变图像序列（GPU加速版本）
"""

import cv2
import numpy as np
import torch
import torch.nn.functional as F
from scipy.spatial import Delaunay
from typing import Dict, List, Tuple, Optional, Callable

class ImageMorpherGPU:
    """
    图像变形器，用于生成基于特征的渐变图像序列（GPU加速版本）
    """
    
    def __init__(self, smooth_sigma: float = 0.5, device: str = "mps"):
        """
        初始化图像变形器
        
        Args:
            smooth_sigma: 平滑处理的高斯核标准差
            device: GPU设备，默认使用MPS（Mac上的Metal）
        """
        self.smooth_sigma = smooth_sigma
        self.device = device
        
    def generate_morphed_sequence(
        self,
        source_image: np.ndarray,
        target_image: np.ndarray,
        feature_sequence: List[Dict],
        steps: int = 5,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[np.ndarray]:
        """
        生成渐变图像序列（GPU加速版本）
        
        Args:
            source_image: 源图像
            target_image: 目标图像
            feature_sequence: 特征序列，每个元素包含特征点、区域和轮廓信息
            steps: 生成的图像数量
            progress_callback: 进度回调函数，接收当前步骤和总步骤数
            
        Returns:
            渐变图像序列
            
        Raises:
            ValueError: 当输入参数无效时
        """
        # 验证输入
        if len(feature_sequence) == 0:
            raise ValueError("特征序列不能为空")
            
        if source_image.shape != target_image.shape:
            raise ValueError("源图像和目标图像的尺寸必须相同")
            
        # 验证特征序列中的特征点
        for features in feature_sequence:
            required_keys = ['source_landmarks_2d', 'target_landmarks_2d', 'landmarks_2d']
            if not all(key in features for key in required_keys):
                raise ValueError("特征字典缺少必要的特征点数据")
        
        # 将图像转换为PyTorch张量并移到GPU
        source_tensor = torch.from_numpy(source_image).float().to(self.device)
        target_tensor = torch.from_numpy(target_image).float().to(self.device)
        
        # 初始化结果列表
        morphed_sequence = []
        
        # 对每个特征步骤生成对应的变形图像
        total_steps = len(feature_sequence)
        for i, features in enumerate(feature_sequence):
            # 更新进度
            if progress_callback:
                progress_callback(i + 1, total_steps)
            
            # 计算当前的权重
            weight = i / (total_steps - 1)
            
            # 生成当前帧的变形图像
            try:
                morphed_image = self._generate_morphed_image(
                    source_image,
                    target_image,
                    features['source_landmarks_2d'],
                    features['target_landmarks_2d'],
                    weight
                )
                
                # 应用颜色校正
                morphed_image = self._color_correct(
                    source_image,
                    target_image,
                    morphed_image,
                    weight
                )
                
                morphed_sequence.append(morphed_image)
            except Exception as e:
                raise ValueError(f"生成第{i}帧图像时出错：{str(e)}")
            
        return morphed_sequence
    
    def _generate_morphed_image(self, source_image, target_image, source_points, target_points, weight):
        """
        生成变形后的图像（改进版本）
        
        Args:
            source_image: 源图像
            target_image: 目标图像
            source_points: 源图像特征点
            target_points: 目标图像特征点
            weight: 混合权重 (0-1)
            
        Returns:
            变形后的图像
        """
        try:
            # 确保所有输入都是numpy数组并且是float32类型
            source_points = np.array(source_points, dtype=np.float32)
            target_points = np.array(target_points, dtype=np.float32)
            
            # 计算当前帧的特征点位置
            current_points = source_points + (target_points - source_points) * weight
            
            # 添加更密集的边界点以改善边缘处理
            h, w = source_image.shape[:2]
            border_count = 8  # 每边的点数
            
            # 生成边界点
            x_points = np.linspace(0, w-1, border_count, dtype=np.float32)
            y_points = np.linspace(0, h-1, border_count, dtype=np.float32)
            
            border_points = []
            # 上边界
            for x in x_points:
                border_points.append([x, 0])
            # 右边界
            for y in y_points[1:-1]:
                border_points.append([w-1, y])
            # 下边界
            for x in x_points[::-1]:
                border_points.append([x, h-1])
            # 左边界
            for y in y_points[1:-1][::-1]:
                border_points.append([0, y])
                
            border_points = np.array(border_points, dtype=np.float32)
            
            # 将边界点添加到特征点集合中
            source_points_with_border = np.vstack([source_points, border_points])
            target_points_with_border = np.vstack([target_points, border_points])
            current_points_with_border = np.vstack([current_points, border_points])
            
            # 创建Delaunay三角剖分
            try:
                delaunay = Delaunay(current_points_with_border)
                triangles = delaunay.simplices.astype(np.int32)
            except Exception as e:
                print(f"Delaunay三角剖分失败，尝试使用备用方法: {e}")
                # 备用方法：使用OpenCV的Subdiv2D
                rect = (0, 0, w, h)
                subdiv = cv2.Subdiv2D(rect)
                for point in current_points_with_border:
                    subdiv.insert((int(point[0]), int(point[1])))
                triangles = subdiv.getTriangleList()
                triangles = np.array([[self._find_point_index((t[0], t[1]), current_points_with_border),
                                     self._find_point_index((t[2], t[3]), current_points_with_border),
                                     self._find_point_index((t[4], t[5]), current_points_with_border)]
                                    for t in triangles if all(self._find_point_index((t[i], t[i+1]), 
                                        current_points_with_border) is not None for i in [0,2,4])], dtype=np.int32)
            
            # 将所有数据转移到GPU
            source_points_gpu = torch.tensor(source_points_with_border, dtype=torch.float32, device=self.device)
            target_points_gpu = torch.tensor(target_points_with_border, dtype=torch.float32, device=self.device)
            current_points_gpu = torch.tensor(current_points_with_border, dtype=torch.float32, device=self.device)
            triangles_gpu = torch.tensor(triangles, dtype=torch.int32, device=self.device)
            
            # 变形源图像和目标图像
            source_warped = self._warp_image_gpu(
                source_image,
                source_points_gpu,
                current_points_gpu,
                triangles_gpu
            )
            
            target_warped = self._warp_image_gpu(
                target_image,
                target_points_gpu,
                current_points_gpu,
                triangles_gpu
            )
            
            # 在GPU上进行图像混合
            source_warped = torch.tensor(source_warped, dtype=torch.float32, device=self.device)
            target_warped = torch.tensor(target_warped, dtype=torch.float32, device=self.device)
            
            morphed = ((1 - weight) * source_warped + weight * target_warped).cpu().numpy()
            
            # 应用边缘平滑
            if self.smooth_sigma > 0:
                morphed = cv2.GaussianBlur(morphed, (0, 0), self.smooth_sigma)
            
            return np.clip(morphed, 0, 255).astype(np.uint8)
            
        except Exception as e:
            raise ValueError(f"变形图像失败：{str(e)}")

    def _warp_image_gpu(self, img, src_points, dst_points, triangles):
        """
        使用GPU加速的图像变形算法（改进版本）
        
        Args:
            img: 输入图像
            src_points: 源特征点
            dst_points: 目标特征点
            triangles: 三角形索引
            
        Returns:
            变形后的图像
        """
        h, w = img.shape[:2]
        
        # 创建输出图像
        warped = np.zeros_like(img, dtype=np.float32)
        
        # 将图像转换为GPU tensor
        img_tensor = torch.tensor(img, dtype=torch.float32, device=self.device)
        
        # 对每个三角形进行处理
        for triangle in triangles:
            # 获取三角形顶点
            src_tri = src_points[triangle].cpu().numpy().astype(np.float32)
            dst_tri = dst_points[triangle].cpu().numpy().astype(np.float32)
            
            # 计算三角形的包围盒
            min_x = max(0, int(np.floor(dst_tri[:, 0].min())))
            max_x = min(w - 1, int(np.ceil(dst_tri[:, 0].max())))
            min_y = max(0, int(np.floor(dst_tri[:, 1].min())))
            max_y = min(h - 1, int(np.ceil(dst_tri[:, 1].max())))
            
            if min_x >= max_x or min_y >= max_y:
                continue
                
            # 创建网格点
            y, x = np.mgrid[min_y:max_y+1, min_x:max_x+1].astype(np.float32)
            points = np.stack((x.flatten(), y.flatten()), axis=1)
            
            # 检查点是否在三角形内
            def in_triangle(points, triangle):
                v0 = triangle[2] - triangle[0]
                v1 = triangle[1] - triangle[0]
                v2 = points - triangle[0]
                
                dot00 = np.dot(v0, v0)
                dot01 = np.dot(v0, v1)
                dot02 = np.dot(v0, v2.T)
                dot11 = np.dot(v1, v1)
                dot12 = np.dot(v1, v2.T)
                
                inv_denom = 1.0 / (dot00 * dot11 - dot01 * dot01)
                u = (dot11 * dot02 - dot01 * dot12) * inv_denom
                v = (dot00 * dot12 - dot01 * dot02) * inv_denom
                
                return (u >= 0) & (v >= 0) & (u + v <= 1)
            
            # 找到三角形内的点
            mask = in_triangle(points, dst_tri)
            points = points[mask]
            
            if len(points) == 0:
                continue
            
            # 计算仿射变换矩阵
            M = cv2.getAffineTransform(dst_tri, src_tri)
            
            # 应用变换
            src_x = M[0,0] * points[:,0] + M[0,1] * points[:,1] + M[0,2]
            src_y = M[1,0] * points[:,0] + M[1,1] * points[:,1] + M[1,2]
            
            # 使用双线性插值
            src_x = torch.tensor(src_x, dtype=torch.float32, device=self.device)
            src_y = torch.tensor(src_y, dtype=torch.float32, device=self.device)
            
            # 确保坐标在有效范围内
            src_x = torch.clamp(src_x, 0, w-1)
            src_y = torch.clamp(src_y, 0, h-1)
            
            # 计算插值权重
            x0 = torch.floor(src_x).long()
            x1 = torch.min(x0 + 1, torch.tensor(w-1, device=self.device))
            y0 = torch.floor(src_y).long()
            y1 = torch.min(y0 + 1, torch.tensor(h-1, device=self.device))
            
            x_weight = src_x - x0.float()
            y_weight = src_y - y0.float()
            
            # 双线性插值
            for c in range(3):
                img_c = img_tensor[..., c]
                warped[points[:,1].astype(int), points[:,0].astype(int), c] = (
                    (1 - x_weight) * (1 - y_weight) * img_c[y0, x0] +
                    x_weight * (1 - y_weight) * img_c[y0, x1] +
                    (1 - x_weight) * y_weight * img_c[y1, x0] +
                    x_weight * y_weight * img_c[y1, x1]
                ).cpu().numpy()
        
        return warped

    def _find_point_index(self, point, points_array, tolerance=5):
        """查找点在数组中的索引，允许一定的误差"""
        distances = np.sqrt(np.sum((points_array - point) ** 2, axis=1))
        min_dist_idx = np.argmin(distances)
        if distances[min_dist_idx] < tolerance:
            return min_dist_idx
        return None

    def _color_correct(
        self,
        source_image: np.ndarray,
        target_image: np.ndarray,
        morphed_image: np.ndarray,
        weight: float
    ) -> np.ndarray:
        """
        对变形后的图像进行颜色校正（GPU加速版本）
        
        Args:
            source_image: 源图像
            target_image: 目标图像
            morphed_image: 变形后的图像
            weight: 当前帧的权重
            
        Returns:
            颜色校正后的图像
        """
        # 将所有图像转换为GPU张量
        source_tensor = torch.from_numpy(source_image).float().to(self.device)
        target_tensor = torch.from_numpy(target_image).float().to(self.device)
        morphed_tensor = torch.from_numpy(morphed_image).float().to(self.device)
        
        # 计算源图像和目标图像的平均颜色
        source_mean = torch.mean(source_tensor.reshape(-1, 3), dim=0)
        target_mean = torch.mean(target_tensor.reshape(-1, 3), dim=0)
        
        # 计算目标颜色
        target_color = (1 - weight) * source_mean + weight * target_mean
        
        # 计算当前图像的平均颜色
        current_mean = torch.mean(morphed_tensor.reshape(-1, 3), dim=0)
        
        # 计算颜色校正系数
        correction = torch.where(
            current_mean > 0,
            target_color / current_mean,
            torch.ones_like(current_mean, device=self.device)
        )
        
        # 应用颜色校正（使用广播机制）
        corrected = torch.clamp(morphed_tensor * correction.view(1, 1, 3), 0, 255)
        
        # 转回CPU并转换为uint8
        return corrected.cpu().numpy().astype(np.uint8)

    def _points_in_triangle(self, points, triangle):
        """使用重心坐标判断点是否在三角形内（优化版本）"""
        # 计算三角形的边向量
        v0 = triangle[2] - triangle[0]
        v1 = triangle[1] - triangle[0]
        
        # 计算点相对于三角形第一个顶点的向量
        v2 = points - triangle[0].unsqueeze(0)
        
        # 计算点积
        d00 = torch.dot(v0, v0)
        d01 = torch.dot(v0, v1)
        d11 = torch.dot(v1, v1)
        
        # 计算重心坐标
        d20 = torch.sum(v2 * v0.unsqueeze(0), dim=1)
        d21 = torch.sum(v2 * v1.unsqueeze(0), dim=1)
        
        # 计算系数
        denom = d00 * d11 - d01 * d01
        v = (d11 * d20 - d01 * d21) / denom
        w = (d00 * d21 - d01 * d20) / denom
        u = 1.0 - v - w
        
        # 使用一个小的容差值来处理数值误差
        epsilon = 1e-5
        return (u >= -epsilon) & (v >= -epsilon) & (w >= -epsilon)

    def _smooth_transition(self, img_tensor, mask):
        """平滑过渡效果"""
        kernel_size = 3
        padding = kernel_size // 2
        
        # 创建高斯核
        sigma = 1.0
        x = torch.arange(-padding, padding + 1, dtype=torch.float32, device=self.device)
        kernel = torch.exp(-x.pow(2) / (2 * sigma ** 2))
        kernel = kernel / kernel.sum()
        
        # 分别对水平和垂直方向进行卷积
        for _ in range(2):  # 多次迭代以获得更好的效果
            # 水平方向
            img_tensor = torch.nn.functional.conv2d(
                img_tensor.permute(2, 0, 1).unsqueeze(0),
                kernel.view(1, 1, 1, -1).expand(3, 1, 1, -1),
                padding=(0, padding),
                groups=3
            ).squeeze(0).permute(1, 2, 0)
            
            # 垂直方向
            img_tensor = torch.nn.functional.conv2d(
                img_tensor.permute(2, 0, 1).unsqueeze(0),
                kernel.view(1, 1, -1, 1).expand(3, 1, -1, 1),
                padding=(padding, 0),
                groups=3
            ).squeeze(0).permute(1, 2, 0)
        
        return img_tensor

    def _find_nearest_filled(self, unfilled_batch, filled_points, k=4):
        """
        为未填充的点找到最近的已填充点（内存优化版本）
        """
        batch_size = unfilled_batch.shape[0]
        num_filled = filled_points.shape[0]
        k = min(k, num_filled)
        
        # 初始化结果
        distances = torch.full((batch_size, k), float('inf'), device=self.device)
        indices = torch.zeros((batch_size, k), dtype=torch.long, device=self.device)
        
        # 分批计算距离以节省内存
        chunk_size = 1000
        for i in range(0, num_filled, chunk_size):
            end_idx = min(i + chunk_size, num_filled)
            filled_chunk = filled_points[i:end_idx]
            
            # 计算当前批次的欧氏距离
            current_dists = torch.sum((unfilled_batch.unsqueeze(1) - filled_chunk.unsqueeze(0)) ** 2, dim=2)
            
            # 更新最近的k个点
            combined_dists = torch.cat([distances, current_dists], dim=1)
            combined_indices = torch.cat([indices, torch.arange(i, end_idx, device=self.device).expand(batch_size, -1)], dim=1)
            
            # 获取前k个最小值
            top_k_values, top_k_indices = torch.topk(combined_dists, k, dim=1, largest=False)
            
            # 更新结果
            distances = top_k_values
            indices = torch.gather(combined_indices, 1, top_k_indices)
        
        return distances, indices

    def _create_triangulation(self, points):
        """
        创建特征点的Delaunay三角剖分
        
        Args:
            points: 特征点坐标数组
            
        Returns:
            三角剖分对象
            
        Raises:
            ValueError: 当特征点数量不足或无效时
        """
        if len(points) < 3:
            raise ValueError("特征点数量必须大于等于3个才能进行三角剖分")
            
        try:
            # 添加图像边界点以确保完整覆盖
            rect = cv2.boundingRect(points.astype(np.float32))
            subdiv = Delaunay(points)
            return subdiv
        except Exception as e:
            raise ValueError(f"三角剖分失败：{str(e)}")

    def _get_affine_matrix_gpu(self, src_tri, dst_tri):
        """
        在GPU上计算仿射变换矩阵
        """
        # 构建方程组
        x = src_tri[:, 0]
        y = src_tri[:, 1]
        u = dst_tri[:, 0]
        v = dst_tri[:, 1]
        
        # 构建系数矩阵A
        A = torch.zeros((6, 6), dtype=torch.float32, device=self.device)
        A[0::2, 0] = x
        A[0::2, 1] = y
        A[0::2, 2] = 1
        A[1::2, 3] = x
        A[1::2, 4] = y
        A[1::2, 5] = 1
        
        # 构建b向量
        b = torch.zeros(6, dtype=torch.float32, device=self.device)
        b[0::2] = u
        b[1::2] = v
        
        # 求解线性方程组
        try:
            x = torch.linalg.solve(A, b)
            # 重塑为2x3矩阵
            return x.reshape(2, 3)
        except:
            # 如果矩阵奇异，使用伪逆
            x = torch.linalg.pinv(A) @ b
            return x.reshape(2, 3)
