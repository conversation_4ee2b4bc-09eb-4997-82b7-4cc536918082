#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
鼻部变形算法实现
用于根据特征点和变形参数对鼻部进行变形处理
"""

import os
import sys
import json
import logging
import cv2
import numpy as np
import mediapipe as mp
from datetime import datetime

# 导入变形配置
from deformation_config import get_deformation_scale

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 将当前目录添加到Python路径
sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('NoseTransformation')

# 鼻部特征点索引映射
NOSE_LANDMARKS = {
    # 基础特征点
    'bridge_top': 168,    # 鼻梁顶部
    'bridge_middle': 6,   # 鼻梁中部
    'bridge_bottom': 4,   # 鼻梁底部
    'tip': 1,             # 鼻尖
    'bottom': 2,          # 鼻底
    
    # 鼻翼复合体
    'left_alar_base': 129,    # 左鼻翼基底
    'right_alar_base': 358,   # 右鼻翼基底
    'left_alar_rim': 219,     # 左鼻翼外缘
    'right_alar_rim': 439,    # 右鼻翼外缘
    'left_alar_groove': 235,  # 左鼻翼沟
    'right_alar_groove': 455, # 右鼻翼沟
    
    # 鼻孔复合体
    'left_nostril_rim': 79,   # 左鼻孔外缘
    'right_nostril_rim': 309, # 右鼻孔外缘
    'left_nostril_base': 166, # 左鼻孔内缘
    'right_nostril_base': 392,# 右鼻孔内缘
    'left_nostril_floor': 98, # 左鼻孔底
    'right_nostril_floor': 327,# 右鼻孔底
    
    # 鼻尖复合体
    'left_tip_outer': 48,     # 左鼻尖外侧
    'right_tip_outer': 278,   # 右鼻尖外侧
    'left_tip_inner': 49,     # 左鼻尖内侧
    'right_tip_inner': 279,   # 右鼻尖内侧
}

# 鼻部区域定义
NOSE_REGION_INDICES = [
    # 鼻梁区域
    168, 6, 197, 195, 5, 4,
    # 鼻尖区域
    4, 1, 2,
    # 左鼻翼区域
    97, 99, 100, 101, 102, 103, 104, 105, 66, 107, 108, 109, 110, 
    # 右鼻翼区域
    327, 328, 329, 330, 331, 332, 333, 334, 296, 336, 337, 338, 339,
    # 鼻孔区域
    79, 166, 75, 77, 90, 180, 115, 
    309, 392, 305, 307, 320, 404, 344,
]

class NoseTransformer:
    """鼻部变形处理器"""
    
    def __init__(self):
        """初始化鼻部变形处理器"""
        logger.info("初始化NoseTransformer")
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        )
    
    def transform(self, image_path, parameters, output_path=None):
        """应用鼻部变形
        
        Args:
            image_path: 输入图像路径
            parameters: 变形参数字典
            output_path: 输出图像路径，如果为None则自动生成
            
        Returns:
            str: 输出图像路径
        """
        start_time = datetime.now()
        logger.info(f"开始鼻部变形处理 | 图像路径: {image_path}")
        logger.info(f"变形参数: {json.dumps(parameters)}")
        
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise Exception(f"无法读取图像: {image_path}")
            
            # 获取图像尺寸
            height, width = image.shape[:2]
            logger.info(f"图像尺寸: {width}x{height}")
            
            # 检测面部特征点
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(image_rgb)
            
            if not results.multi_face_landmarks:
                raise Exception("未检测到面部特征点")
            
            # 获取面部特征点
            face_landmarks = results.multi_face_landmarks[0]
            
            # 提取鼻部特征点
            nose_points = {}
            for name, idx in NOSE_LANDMARKS.items():
                landmark = face_landmarks.landmark[idx]
                nose_points[name] = {
                    'x': landmark.x * width,
                    'y': landmark.y * height,
                    'z': landmark.z
                }
            
            logger.info(f"提取到 {len(nose_points)} 个鼻部特征点")
            
            # 创建变形网格
            src_points = []
            dst_points = []
            
            # 添加鼻部特征点到变形网格
            for idx in NOSE_REGION_INDICES:
                landmark = face_landmarks.landmark[idx]
                x, y = landmark.x * width, landmark.y * height
                src_points.append([x, y])
                
                # 应用变形参数
                new_x, new_y = self._apply_transformation(x, y, nose_points, parameters, width, height)
                dst_points.append([new_x, new_y])
            
            # 添加面部边界点以保持面部其他区域不变
            for idx in range(0, 468):
                if idx not in NOSE_REGION_INDICES:
                    landmark = face_landmarks.landmark[idx]
                    x, y = landmark.x * width, landmark.y * height
                    src_points.append([x, y])
                    dst_points.append([x, y])
            
            # 转换为numpy数组
            src_points = np.array(src_points, dtype=np.float32)
            dst_points = np.array(dst_points, dtype=np.float32)
            
            # 计算变形三角剖分
            rect = (0, 0, width, height)
            subdiv = cv2.Subdiv2D(rect)
            
            for point in dst_points:
                subdiv.insert((int(point[0]), int(point[1])))
            
            triangles = subdiv.getTriangleList()
            triangles = np.array(triangles, dtype=np.int32)
            
            # 应用变形
            output_image = np.zeros_like(image)
            
            for t in triangles:
                pt1 = (t[0], t[1])
                pt2 = (t[2], t[3])
                pt3 = (t[4], t[5])
                
                # 查找三角形顶点在dst_points中的索引
                idx1 = self._find_point_index(dst_points, pt1)
                idx2 = self._find_point_index(dst_points, pt2)
                idx3 = self._find_point_index(dst_points, pt3)
                
                if idx1 != -1 and idx2 != -1 and idx3 != -1:
                    # 获取对应的源三角形顶点
                    src_tri = np.array([src_points[idx1], src_points[idx2], src_points[idx3]], dtype=np.float32)
                    dst_tri = np.array([dst_points[idx1], dst_points[idx2], dst_points[idx3]], dtype=np.float32)
                    
                    # 计算仿射变换
                    warp_mat = cv2.getAffineTransform(src_tri, dst_tri)
                    
                    # 计算三角形边界框
                    x, y, w, h = cv2.boundingRect(np.array([pt1, pt2, pt3], dtype=np.int32))
                    
                    # 应用仿射变换
                    warped_triangle = cv2.warpAffine(image, warp_mat, (width, height), None, 
                                                   flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT_101)
                    
                    # 创建掩码
                    mask = np.zeros((height, width), dtype=np.uint8)
                    cv2.fillConvexPoly(mask, np.array([pt1, pt2, pt3], dtype=np.int32), 255)
                    
                    # 应用掩码
                    warped_triangle = cv2.bitwise_and(warped_triangle, warped_triangle, mask=mask)
                    output_image = cv2.bitwise_and(output_image, output_image, mask=cv2.bitwise_not(mask))
                    output_image = cv2.add(output_image, warped_triangle)
            
            # 保存输出图像
            if output_path is None:
                filename, ext = os.path.splitext(image_path)
                output_path = f"{filename}_nose_transformed{ext}"
            
            cv2.imwrite(output_path, output_image)
            
            end_time = datetime.now()
            elapsed = (end_time - start_time).total_seconds()
            logger.info(f"鼻部变形处理完成 | 输出路径: {output_path} | 耗时: {elapsed:.2f}秒")
            
            return output_path
            
        except Exception as e:
            logger.error(f"鼻部变形处理失败: {str(e)}")
            raise
    
    def _apply_transformation(self, x, y, nose_points, parameters, width, height):
        """应用变形参数到坐标点
        
        Args:
            x, y: 原始坐标
            nose_points: 鼻部特征点字典
            parameters: 变形参数字典
            width, height: 图像尺寸
            
        Returns:
            tuple: 变形后的坐标 (x, y)
        """
        dx, dy = 0, 0
        
        # 对称性参数
        if 'alar_symmetry' in parameters:
            # 鼻翼对称性调整
            alar_points = [
                ('left_alar_base', 'right_alar_base', 1.0, 30),
                ('left_alar_rim', 'right_alar_rim', 0.8, 25),
                ('left_alar_groove', 'right_alar_groove', 0.6, 20)
            ]
            for left_name, right_name, weight, radius in alar_points:
                dx += self._apply_symmetry_transform(x, y, nose_points[left_name], 
                    nose_points[right_name], parameters['alar_symmetry'], weight, radius)
        
        if 'nostril_symmetry' in parameters:
            # 鼻孔对称性调整
            nostril_points = [
                ('left_nostril_rim', 'right_nostril_rim', 1.0, 20),
                ('left_nostril_base', 'right_nostril_base', 0.8, 15),
                ('left_nostril_floor', 'right_nostril_floor', 0.7, 15)
            ]
            for left_name, right_name, weight, radius in nostril_points:
                dx += self._apply_symmetry_transform(x, y, nose_points[left_name], 
                    nose_points[right_name], parameters['nostril_symmetry'], weight, radius)
        
        if 'tip_symmetry' in parameters:
            # 鼻尖对称性调整
            tip_points = [
                ('left_tip_outer', 'right_tip_outer', 1.0, 15),
                ('left_tip_inner', 'right_tip_inner', 0.8, 12)
            ]
            for left_name, right_name, weight, radius in tip_points:
                dx += self._apply_symmetry_transform(x, y, nose_points[left_name], 
                    nose_points[right_name], parameters['tip_symmetry'], weight, radius)
        
        # 原有变形参数
        if 'bridgeHeight' in parameters:
            # 鼻梁高度调整
            bridge_points = ['bridge_top', 'bridge_middle', 'bridge_bottom']
            for point_name in bridge_points:
                point = nose_points[point_name]
                dist = np.sqrt((x - point['x'])**2 + (y - point['y'])**2)
                if dist < 50:
                    weight = 1 - dist/50
                    dy -= parameters['bridgeHeight'] * weight * 20
        
        if 'tipHeight' in parameters:
            # 鼻尖高度调整
            tip_point = nose_points['tip']
            dist = np.sqrt((x - tip_point['x'])**2 + (y - tip_point['y'])**2)
            if dist < 30:
                weight = 1 - dist/30
                dy -= parameters['tipHeight'] * weight * 15
        
        return x + dx, y + dy
    
    def _apply_symmetry_transform(self, x, y, left_point, right_point, value, weight, radius):
        """应用对称性变形
        
        Args:
            x, y: 当前点坐标
            left_point: 左侧特征点
            right_point: 右侧特征点
            value: 变形强度
            weight: 特征点权重
            radius: 影响半径
            
        Returns:
            float: x方向偏移量
        """
        # 计算中点
        mid_x = (left_point['x'] + right_point['x']) / 2
        
        # 计算到左右点的距离
        left_dist = np.sqrt((x - left_point['x'])**2 + (y - left_point['y'])**2)
        right_dist = np.sqrt((x - right_point['x'])**2 + (y - right_point['y'])**2)
        
        # 如果在影响范围内
        if min(left_dist, right_dist) < radius:
            # 计算影响权重
            dist_weight = 1 - min(left_dist, right_dist)/radius
            
            # 计算相对于中线的位置
            rel_x = x - mid_x
            
            # 计算对称性调整
            dx = -rel_x * value * weight * dist_weight
            
            return dx
            
        return 0
        
        # 鼻梁高度
        if 'bridgeHeight' in parameters:
            bridge_effect = parameters['bridgeHeight'] * 20  # 缩放因子
            bridge_weight = weights['bridge_top'] + weights['bridge_middle'] + weights['bridge_bottom']
            dy -= bridge_effect * bridge_weight
        
        # 鼻梁宽度
        if 'bridgeWidth' in parameters:
            bridge_width_effect = parameters['bridgeWidth'] * 15
            if x < width / 2:  # 左侧
                dx -= bridge_width_effect * (weights['bridge_top'] + weights['bridge_middle'] + weights['bridge_bottom'])
            else:  # 右侧
                dx += bridge_width_effect * (weights['bridge_top'] + weights['bridge_middle'] + weights['bridge_bottom'])
        
        # 鼻尖长度
        if 'tipLength' in parameters:
            tip_length_effect = parameters['tipLength'] * 15
            tip_weight = weights['tip'] + weights['bottom']
            dy += tip_length_effect * tip_weight
        
        # 鼻尖高度
        if 'tipHeight' in parameters:
            tip_height_effect = parameters['tipHeight'] * 15
            dy -= tip_height_effect * weights['tip']
        
        # 鼻尖宽度
        if 'tipWidth' in parameters:
            tip_width_effect = parameters['tipWidth'] * 10
            if x < width / 2:  # 左侧
                dx -= tip_width_effect * weights['tip']
            else:  # 右侧
                dx += tip_width_effect * weights['tip']
        
        # 鼻孔大小
        if 'nostrilSize' in parameters:
            nostril_size_effect = parameters['nostrilSize'] * 8
            nostril_weight = weights['left_nostril'] + weights['right_nostril']
            
            # 水平方向
            if x < width / 2:  # 左侧
                dx -= nostril_size_effect * weights['left_nostril']
            else:  # 右侧
                dx += nostril_size_effect * weights['right_nostril']
            
            # 垂直方向
            dy += nostril_size_effect * nostril_weight
                # 鼻翼宽度 - 使用基于解剖学的区域限制变形
        if 'nostrilWidth' in parameters:
            # 使用统一的变形系数，负值缩小鼻翼，正值增宽鼻翼
            scale = get_deformation_scale('nostril_width')
            nostril_width_effect = parameters['nostrilWidth'] * scale
            
            # 打印参数值，用于调试
            logger.info(f"鼻翼宽度参数值: {parameters['nostrilWidth']}, 缩放后: {nostril_width_effect}")
            
            # 获取鼻部关键特征点
            nose_tip_x = nose_points['tip']['x']
            nose_tip_y = nose_points['tip']['y']
            
            # 获取左右鼻翼点
            left_alar_x = nose_points['left_alar_base']['x']
            left_alar_y = nose_points['left_alar_base']['y']
            right_alar_x = nose_points['right_alar_base']['x']
            right_alar_y = nose_points['right_alar_base']['y']
            
            # 获取鼻孔点
            left_nostril_x = nose_points['left_nostril_rim']['x']
            left_nostril_y = nose_points['left_nostril_rim']['y']
            right_nostril_x = nose_points['right_nostril_rim']['x']
            right_nostril_y = nose_points['right_nostril_rim']['y']
            
            # 计算鼻翼区域的中心点
            center_x = (left_alar_x + right_alar_x) / 2
            center_y = (left_alar_y + right_alar_y) / 2
            
            # 计算鼻翼宽度
            nose_width = right_alar_x - left_alar_x
            
            # 定义鼻翼区域的边界点 - 基于解剖学原理
            # 左侧鼻翼区域
            left_nostril_region = [
                (left_alar_x, left_alar_y),                      # 左鼻翼点
                (left_nostril_x, left_nostril_y),                # 左鼻孔点
                (nose_tip_x, nose_tip_y),                        # 鼻尖点
                (center_x - nose_width * 0.1, center_y)          # 鼻中线附近点
            ]
            
            # 右侧鼻翼区域
            right_nostril_region = [
                (right_alar_x, right_alar_y),                     # 右鼻翼点
                (right_nostril_x, right_nostril_y),               # 右鼻孔点
                (nose_tip_x, nose_tip_y),                         # 鼻尖点
                (center_x + nose_width * 0.1, center_y)           # 鼻中线附近点
            ]
            
            # 判断点是否在左侧鼻翼区域内
            in_left_nostril_region = self._point_in_polygon((x, y), left_nostril_region)
            
            # 判断点是否在右侧鼻翼区域内
            in_right_nostril_region = self._point_in_polygon((x, y), right_nostril_region)
            
            # 只对鼻翼区域内的点进行变形
            if in_left_nostril_region or in_right_nostril_region:
                # 计算变形强度
                deformation_strength = 0.3
                
                # 计算点到鼻中线的距离比例
                dist_to_center = abs(x - center_x)
                max_dist = nose_width / 2
                dist_ratio = min(dist_to_center / max_dist, 1.0)
                
                # 距离中心越远，变形越强
                weight = dist_ratio
                
                # 根据点在左侧还是右侧，应用相应的变形
                if in_left_nostril_region:  # 左侧鼻翼
                    if nostril_width_effect < 0:  # 缩小鼻翼
                        # 向右移动（向中心）
                        dx += abs(nostril_width_effect) * weight * deformation_strength
                    else:  # 增宽鼻翼
                        # 向左移动（向外）
                        dx -= nostril_width_effect * weight * deformation_strength
                elif in_right_nostril_region:  # 右侧鼻翼
                    if nostril_width_effect < 0:  # 缩小鼻翼
                        # 向左移动（向中心）
                        dx -= abs(nostril_width_effect) * weight * deformation_strength
                    else:  # 增宽鼻翼
                        # 向右移动（向外）
                        dx += nostril_width_effect * weight * deformation_strength
                
                # 记录变形信息（调试用）
                if abs(dx) > 0.01:
                    logger.debug(f"应用鼻翼变形: 点({x},{y}), 位移={dx:.2f}, 区域={'左鼻翼' if in_left_nostril_region else '右鼻翼'}, 权重={weight:.2f}")
        
        # 鼻基底高度
        if 'baseHeight' in parameters:
            base_height_effect = parameters['baseHeight'] * 10
            base_weight = weights['left_base'] + weights['right_base']
            dy += base_height_effect * base_weight
        
        # 鼻基底宽度
        if 'baseWidth' in parameters:
            base_width_effect = parameters['baseWidth'] * 12
            if x < width / 2:  # 左侧
                dx -= base_width_effect * weights['left_base']
            else:  # 右侧
                dx += base_width_effect * weights['right_base']
        
        return x + dx, y + dy
    
    def _point_in_polygon(self, point, polygon):
        """判断点是否在多边形内
        
        Args:
            point: 要判断的点，格式为 (x, y)
            polygon: 多边形顶点列表，格式为 [(x1, y1), (x2, y2), ...]
            
        Returns:
            bool: 如果点在多边形内返回 True，否则返回 False
        """
        # 实现射线法判断点是否在多边形内
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y) and y <= max(p1y, p2y) and x <= max(p1x, p2x):
                if p1y != p2y:
                    xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                if p1x == p2x or x <= xinters:
                    inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def _find_point_index(self, points, point):
        """查找点在点集中的索引
        
        Args:
            points: 点集数组
            point: 要查找的点
            
        Returns:
            int: 点的索引，如果未找到则返回-1
        """
        for i, p in enumerate(points):
            if abs(p[0] - point[0]) < 1 and abs(p[1] - point[1]) < 1:
                return i
        return -1
    
    def preview(self, image_path, parameters):
        """预览鼻部变形效果
        
        Args:
            image_path: 输入图像路径
            parameters: 变形参数字典
            
        Returns:
            bytes: 变形后的图像数据
        """
        try:
            # 应用变形
            temp_output = self.transform(image_path, parameters)
            
            # 读取变形后的图像
            with open(temp_output, 'rb') as f:
                image_data = f.read()
            
            # 删除临时文件
            os.remove(temp_output)
            
            return image_data
            
        except Exception as e:
            logger.error(f"预览鼻部变形失败: {str(e)}")
            raise

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python nose_transformation.py <图像路径> <参数JSON>")
        sys.exit(1)
    
    image_path = sys.argv[1]
    params_json = sys.argv[2]
    
    try:
        # 解析参数
        parameters = json.loads(params_json)
        
        # 创建变形处理器
        transformer = NoseTransformer()
        
        # 应用变形
        output_path = transformer.transform(image_path, parameters)
        
        # 输出结果
        result = {
            "status": "success",
            "output_path": output_path
        }
        print(json.dumps(result))
        
    except Exception as e:
        result = {
            "status": "error",
            "message": str(e)
        }
        print(json.dumps(result))
        sys.exit(1)

if __name__ == "__main__":
    main()
