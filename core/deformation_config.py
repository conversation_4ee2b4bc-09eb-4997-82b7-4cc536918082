#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
变形参数配置文件

该文件定义了所有变形效果的统一参数和系数，确保整个应用中变形效果的一致性
"""

# 全局变形系数 - 所有变形效果都应该使用这些系数
DEFORMATION_SCALE = {
    # 面部轮廓相关
    "contour_tighten": 3.0,
    "chin_adjust": 3.0,
    "cheekbone_adjust": 3.0,
    "face_shape": 3.0,
    
    # 鼻部塑形相关
    "bridge_height": 1.5,
    "tip_adjust": 1.5,
    "nostril_width": 2.0,  # 鼻翼宽度变形系数
    "base_height": 1.5,
    
    # 眼部美化相关
    "double_fold": 1.0,
    "canthal_tilt": 1.0,
    "eye_bag_removal": 1.0,
    "outer_corner_lift": 1.0,
    
    # 唇部造型相关
    "lip_shape": 1.5,
    "lip_thickness": 1.5,
    "mouth_corner": 1.5,
    "lip_color": 1.5,
    
    # 抗衰冻龄相关
    "nasolabial_folds": 1.5,
    "wrinkle_removal": 1.5,
    "forehead_fullness": 1.5,
    "facial_firmness": 1.5,
    
    # 默认值 - 当找不到特定参数时使用
    "default": 1.5
}

# 影响半径配置（基于面部特征尺寸的比例）
# 注意：这些值代表的是相对于相应面部特征尺寸的比例，而非图像宽度的百分比
INFLUENCE_RADIUS = {
    # 面部轮廓相关
    "face": {
        "contour_tighten": 0.5,    # 面部轮廓宽度的5成
        "chin_adjust": 0.5,        # 下巴宽度的5成
        "cheekbone_adjust": 0.5,    # 颜骨宽度的5成
        "face_shape": 0.6,         # 面部轮廓的6成
        "default": 0.5
    },
    
    # 鼻部塑形相关
    "nose": {
        "bridge_height": 0.4,       # 鼻梁长度的4成
        "tip_adjust": 0.6,         # 鼻尖到鼻小柱基部距离的6成
        "nostril_width": 0.3,       # 鼻翼宽度的3成（减小影响半径，确保变形效果更加局部化）
        "base_height": 0.5,        # 鼻基底高度的5成
        "default": 0.5
    },
    
    # 眼部美化相关
    "eyes": {
        "double_fold": 0.6,         # 眼睛宽度的6成
        "canthal_tilt": 0.7,       # 眼角间距的7成
        "eye_bag_removal": 0.5,     # 眼袋区域的5成
        "outer_corner_lift": 0.6,   # 眼角宽度的6成
        "default": 0.6
    },
    
    # 唇部造型相关
    "lips": {
        "lip_shape": 0.6,          # 嘴唇宽度的6成
        "lip_thickness": 0.6,      # 嘴唇高度的6成
        "mouth_corner": 0.5,       # 嘴角间距的5成
        "lip_color": 0.6,          # 嘴唇区域的6成
        "default": 0.6
    },
    
    # 默认值 - 当找不到特定区域或参数时使用
    "default": 0.5
}

# 权重因子配置
WEIGHT_FACTOR = {
    # 面部轮廓相关
    "face": 2.0,
    # 鼻部塑形相关
    "nose": 2.0,
    # 眼部美化相关
    "eyes": 2.0,
    # 唇部造型相关
    "lips": 2.0,
    # 默认值
    "default": 2.0
}

# 衰减因子配置
FALLOFF_FACTOR = {
    # 所有区域使用统一的衰减因子
    "default": 0.5
}

# 点影响力配置
POINT_POWER = {
    # 所有区域使用统一的点影响力
    "default": 2.0
}


def get_weight_factor(feature_type=None):
    """
    获取指定特征类型的权重因子
    
    参数:
        feature_type: 特征类型，如'nose', 'eyes', 'face'等
        
    返回:
        权重因子值
    """
    if feature_type and feature_type in WEIGHT_FACTOR:
        return WEIGHT_FACTOR[feature_type]
    return WEIGHT_FACTOR['default']


def get_falloff_factor():
    """
    获取衰减因子
    
    返回:
        衰减因子值
    """
    return FALLOFF_FACTOR['default']


def get_point_power():
    """
    获取点影响力
    
    返回:
        点影响力值
    """
    return POINT_POWER['default']

def get_deformation_scale(parameter_name):
    """
    获取指定参数的变形系数
    
    参数:
        parameter_name: 参数名称，如'nostril_width'
        
    返回:
        对应的变形系数，如果找不到则返回默认值
    """
    return DEFORMATION_SCALE.get(parameter_name, DEFORMATION_SCALE['default'])


def get_influence_radius(feature_type, parameter_name, image_width=None):
    """
    获取指定特征和参数的影响半径比例
    
    参数:
        feature_type: 特征类型，如'nose', 'eyes', 'face'等
        parameter_name: 参数名称，如'nostril_width'
        image_width: 图像宽度（像素），如果提供则返回实际像素值（兼容旧代码）
        
    返回:
        影响半径比例（相对于面部特征尺寸的比例）
    """
    # 首先尝试获取特定特征类型和参数的影响半径
    if feature_type in INFLUENCE_RADIUS and parameter_name in INFLUENCE_RADIUS[feature_type]:
        return INFLUENCE_RADIUS[feature_type][parameter_name]
    
    # 如果找不到特定参数，尝试返回该特征类型的默认值
    if feature_type in INFLUENCE_RADIUS and 'default' in INFLUENCE_RADIUS[feature_type]:
        return INFLUENCE_RADIUS[feature_type]['default']
    
    # 如果还是找不到，返回全局默认值
    return INFLUENCE_RADIUS['default']


def calculate_feature_based_influence_radius(feature_type, parameter_name, face_landmarks):
    """
    基于面部特征计算影响半径
    
    参数:
        feature_type: 特征类型，如'nose', 'eyes', 'face'等
        parameter_name: 参数名称，如'nostril_width'
        face_landmarks: 面部特征点字典
        
    返回:
        实际的影响半径像素值
    """
    # 获取影响半径比例
    radius_ratio = get_influence_radius(feature_type, parameter_name)
    
    # 基于不同的特征类型和参数计算特征尺寸
    if feature_type == 'nose':
        if parameter_name == 'nostril_width':
            # 鼻翼宽度变形 - 基于左右鼻翼外侧点之间的距离
            if 'left_alar' in face_landmarks['nose'] and 'right_alar' in face_landmarks['nose']:
                left_alar_x = face_landmarks['nose']['left_alar']['x']
                right_alar_x = face_landmarks['nose']['right_alar']['x']
                feature_size = right_alar_x - left_alar_x
                return feature_size * radius_ratio
        
        elif parameter_name == 'bridge_height':
            # 鼻梁高度变形 - 基于鼻梁长度
            if 'bridge_top' in face_landmarks['nose'] and 'bridge_bottom' in face_landmarks['nose']:
                bridge_top_y = face_landmarks['nose']['bridge_top']['y']
                bridge_bottom_y = face_landmarks['nose']['bridge_bottom']['y']
                feature_size = abs(bridge_bottom_y - bridge_top_y)
                return feature_size * radius_ratio
        
        elif parameter_name == 'tip_adjust':
            # 鼻尖调整 - 基于鼻尖到鼻小柱基部的距离
            if 'tip' in face_landmarks['nose'] and 'columella_base' in face_landmarks['nose']:
                tip_y = face_landmarks['nose']['tip']['y']
                columella_base_y = face_landmarks['nose']['columella_base']['y']
                feature_size = abs(columella_base_y - tip_y)
                return feature_size * radius_ratio
    
    elif feature_type == 'eyes':
        if parameter_name in ['double_fold', 'eye_bag_removal']:
            # 眼部变形 - 基于眼睛宽度
            if 'left_eye' in face_landmarks and 'right_eye' in face_landmarks:
                left_eye_width = face_landmarks['left_eye']['outer']['x'] - face_landmarks['left_eye']['inner']['x']
                right_eye_width = face_landmarks['right_eye']['outer']['x'] - face_landmarks['right_eye']['inner']['x']
                feature_size = (left_eye_width + right_eye_width) / 2  # 取平均
                return feature_size * radius_ratio
    
    elif feature_type == 'lips':
        if parameter_name in ['lip_shape', 'lip_thickness']:
            # 嘴唇变形 - 基于嘴唇宽度
            if 'mouth' in face_landmarks:
                mouth_width = face_landmarks['mouth']['right_corner']['x'] - face_landmarks['mouth']['left_corner']['x']
                return mouth_width * radius_ratio
    
    # 如果无法计算特定特征尺寸，返回一个合理的默认值
    # 这里我们使用面部宽度的一定比例作为默认值
    if 'face_width' in face_landmarks:
        return face_landmarks['face_width'] * 0.1  # 面部宽度的10%作为默认影响半径
    
    # 如果还是无法计算，返回一个固定值
    return 30  # 默认值，单位为像素
