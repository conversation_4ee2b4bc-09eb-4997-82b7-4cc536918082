import cv2
import numpy as np
from feature_points_validator import FeaturePointsValidator
from face_mesh_processor import get_all_landmarks

def draw_face_contour_symmetry(image_path: str, output_path: str):
    """绘制面部轮廓的对称性分析图
    
    Args:
        image_path: 输入图片路径
        output_path: 输出图片路径
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 面部轮廓的特征点配置
    face_contour_config = {
        'name': '面部轮廓',
        'regions': {
            'jawline': {  # 下颌线区域
                'pairs': [
                    {'left': 162, 'right': 389},  # 下颌角
                    {'left': 21, 'right': 251},   # 下颌线中段
                    {'left': 54, 'right': 284},   # 下颌线上段
                    {'left': 67, 'right': 297},   # 下颌上部
                    {'left': 109, 'right': 338},  # 面颊中部
                ],
                'color': (255, 0, 0)  # 蓝色
            },
            'cheekbone': {  # 颧骨区域
                'pairs': [
                    {'left': 123, 'right': 352},  # 颧骨下部
                    {'left': 147, 'right': 376},  # 颧骨中部
                    {'left': 187, 'right': 411},  # 颧骨上部
                    {'left': 207, 'right': 427},  # 颧骨外侧
                    {'left': 216, 'right': 436},  # 颧骨内侧
                ],
                'color': (0, 165, 255)  # 橙色
            },
            'temple': {  # 太阳穴区域
                'pairs': [
                    {'left': 139, 'right': 368},  # 太阳穴中心
                    {'left': 71, 'right': 301},   # 太阳穴上部
                    {'left': 68, 'right': 298},   # 额头外侧
                ],
                'color': (0, 255, 0)  # 绿色
            },
            'nose': {  # 鼻部区域
                'pairs': [
                    {'left': 98, 'right': 327},   # 鼻翼外侧
                    {'left': 129, 'right': 358},  # 鼻翼内侧
                    {'left': 64, 'right': 294},   # 鼻梁外侧
                ],
                'color': (128, 0, 128)  # 紫色
            }
        },
        'centerPoints': [152, 175]  # 下巴中心点和上部中心点
    }
    
    # 3. 计算面部中心线
    center_x = 664  # 根据实际情况调整
    height = img.shape[0]
    
    # 4. 绘制面部中心线
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 5. 绘制中心点
    for point_id in face_contour_config['centerPoints']:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            cv2.circle(img, (int(x), int(y)), 4, (0, 255, 255), -1)  # 黄色
            cv2.putText(img, str(point_id), (int(x), int(y)-5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 6. 按区域绘制对称点对和连线
    for region_name, region in face_contour_config['regions'].items():
        for pair in region['pairs']:
            left_id = pair['left']
            right_id = pair['right']
            if left_id in landmarks and right_id in landmarks:
                left_point = landmarks[left_id]
                right_point = landmarks[right_id]
                
                # 绘制特征点
                cv2.circle(img, (int(left_point[0]), int(left_point[1])), 3, 
                          region['color'], -1)
                cv2.circle(img, (int(right_point[0]), int(right_point[1])), 3, 
                          region['color'], -1)
                
                # 添加点ID标注
                cv2.putText(img, str(left_id), 
                          (int(left_point[0]), int(left_point[1])-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                cv2.putText(img, str(right_id), 
                          (int(right_point[0]), int(right_point[1])-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # 绘制对称连线
                cv2.line(img, 
                        (int(left_point[0]), int(left_point[1])),
                        (int(right_point[0]), int(right_point[1])),
                        region['color'], 1)
                
                # 绘制连线中点
                mid_x = int((left_point[0] + right_point[0]) / 2)
                mid_y = int((left_point[1] + right_point[1]) / 2)
                cv2.circle(img, (mid_x, mid_y), 2, (0, 255, 255), -1)
    
    # 7. 添加图例
    legend_y = 30
    cv2.putText(img, "图例:", (10, legend_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 中心线和中心点
    cv2.line(img, (10, legend_y+30), (60, legend_y+30), (0, 255, 255), 2)
    cv2.putText(img, "中心线和中心点", (70, legend_y+35), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 各区域图例
    region_colors = {
        "下颌线区域": (255, 0, 0),
        "颧骨区域": (0, 165, 255),
        "太阳穴区域": (0, 255, 0),
        "鼻部区域": (128, 0, 128)
    }
    
    for i, (name, color) in enumerate(region_colors.items()):
        y_offset = legend_y + 60 + i * 30
        cv2.circle(img, (20, y_offset), 3, color, -1)
        cv2.line(img, (35, y_offset), (60, y_offset), color, 1)
        cv2.putText(img, name, (70, y_offset+5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 8. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存对称性分析图到: {output_path}")

def validate_face_contour_symmetry(image_path: str):
    """验证面部轮廓特征点的对称性
    
    Args:
        image_path: 输入图片路径
    """
    # 1. 获取特征点
    landmarks = get_all_landmarks(image_path)
    validator = FeaturePointsValidator(landmarks)
    
    # 2. 验证各区域对称点对
    regions = {
        '下颌线区域': [
            (162, 389),  # 下颌角
            (21, 251),   # 下颌线中段
            (54, 284),   # 下颌线上段
            (67, 297),   # 下颌上部
            (109, 338),  # 面颊中部
        ],
        '颧骨区域': [
            (123, 352),  # 颧骨下部
            (147, 376),  # 颧骨中部
            (187, 411),  # 颧骨上部
            (207, 427),  # 颧骨外侧
            (216, 436),  # 颧骨内侧
        ],
        '太阳穴区域': [
            (139, 368),  # 太阳穴中心
            (71, 301),   # 太阳穴上部
            (68, 298),   # 额头外侧
        ],
        '鼻部区域': [
            (98, 327),   # 鼻翼外侧
            (129, 358),  # 鼻翼内侧
            (64, 294),   # 鼻梁外侧
        ]
    }
    
    print("\n=== 面部轮廓特征点对称性验证 ===")
    for region_name, pairs in regions.items():
        print(f"\n{region_name}:")
        for left_id, right_id in pairs:
            symmetry_score = validator.validate_point_symmetry(left_id, right_id)
            distance = validator.calculate_point_distance(left_id, right_id)
            print(f"点对 {left_id}-{right_id}:")
            print(f"  - 对称性得分: {symmetry_score:.3f}")
            print(f"  - 点间距离: {distance:.2f}")
            
            if symmetry_score < 0.85:  # 对称性阈值
                print("  ⚠️ 警告：对称性较差")
            if distance > 400:  # 距离阈值，根据实际情况调整
                print("  ⚠️ 警告：点间距离过大")
    
    # 3. 验证中心点的位置
    center_points = [152, 175]  # 下巴中心点和上部中心点
    print("\n中心点验证:")
    for point_id in center_points:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.2f}")
            if deviation > 10:  # 偏差阈值，根据实际情况调整
                print("  ⚠️ 警告：偏离中心线过大")

if __name__ == "__main__":
    validate_face_contour_symmetry("testdata/test_face.jpg")
    
    # 生成可视化结果
    draw_face_contour_symmetry(
        image_path="testdata/test_face.jpg",
        output_path="testdata/face_contour_symmetry_analysis.jpg"
    )
