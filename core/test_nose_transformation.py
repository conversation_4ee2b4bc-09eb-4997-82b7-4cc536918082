#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
鼻部变形算法测试脚本
用于测试鼻部变形算法的功能和性能
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 将当前目录添加到Python路径
sys.path.insert(0, current_dir)

# 导入鼻部变形处理器
from nose_transformation import NoseTransformer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('NoseTransformationTest')

def test_nose_transformation(image_path, output_dir=None):
    """测试鼻部变形算法
    
    Args:
        image_path: 测试图像路径
        output_dir: 输出目录，如果为None则使用当前目录
    """
    logger.info(f"开始测试鼻部变形算法 | 图像路径: {image_path}")
    
    try:
        # 创建输出目录
        if output_dir is None:
            output_dir = os.path.join(current_dir, 'testdata', 'output')
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建变形处理器
        transformer = NoseTransformer()
        
        # 测试用例
        test_cases = [
            # 对称性测试用例
            {
                'name': '鼻翼对称性调整_左侧',
                'parameters': {'alar_symmetry': 0.3}
            },
            {
                'name': '鼻翼对称性调整_右侧',
                'parameters': {'alar_symmetry': -0.3}
            },
            {
                'name': '鼻孔对称性调整_左侧',
                'parameters': {'nostril_symmetry': 0.3}
            },
            {
                'name': '鼻孔对称性调整_右侧',
                'parameters': {'nostril_symmetry': -0.3}
            },
            {
                'name': '鼻尖对称性调整_左侧',
                'parameters': {'tip_symmetry': 0.3}
            },
            {
                'name': '鼻尖对称性调整_右侧',
                'parameters': {'tip_symmetry': -0.3}
            },
            {
                'name': '组合对称性调整_1',
                'parameters': {
                    'alar_symmetry': 0.2,
                    'nostril_symmetry': 0.2,
                    'tip_symmetry': 0.2
                }
            },
            {
                'name': '组合对称性调整_2',
                'parameters': {
                    'alar_symmetry': -0.2,
                    'nostril_symmetry': -0.2,
                    'tip_symmetry': -0.2
                }
            },
            {
                'name': '混合对称性调整',
                'parameters': {
                    'alar_symmetry': 0.3,
                    'nostril_symmetry': -0.2,
                    'tip_symmetry': 0.1
                }
            },
            # 原有测试用例
            {
                'name': '鼻梁高度增加',
                'parameters': {'bridgeHeight': 0.5}
            },
            {
                'name': '鼻梁高度降低',
                'parameters': {'bridgeHeight': -0.5}
            },
            {
                'name': '鼻梁宽度增加',
                'parameters': {'bridgeWidth': 0.5}
            },
            {
                'name': '鼻梁宽度减小',
                'parameters': {'bridgeWidth': -0.5}
            },
            {
                'name': '鼻尖长度增加',
                'parameters': {'tipLength': 0.5}
            },
            {
                'name': '鼻尖长度减小',
                'parameters': {'tipLength': -0.5}
            },
            {
                'name': '鼻尖高度增加',
                'parameters': {'tipHeight': 0.5}
            },
            {
                'name': '鼻尖高度降低',
                'parameters': {'tipHeight': -0.5}
            },
            {
                'name': '鼻尖宽度增加',
                'parameters': {'tipWidth': 0.5}
            },
            {
                'name': '鼻尖宽度减小',
                'parameters': {'tipWidth': -0.5}
            },
            {
                'name': '鼻孔大小增加',
                'parameters': {'nostrilSize': 0.5}
            },
            {
                'name': '鼻孔大小减小',
                'parameters': {'nostrilSize': -0.5}
            },
            {
                'name': '鼻翼宽度增加',
                'parameters': {'nostrilWidth': 0.5}
            },
            {
                'name': '鼻翼宽度减小',
                'parameters': {'nostrilWidth': -0.5}
            },
            {
                'name': '鼻基底高度增加',
                'parameters': {'baseHeight': 0.5}
            },
            {
                'name': '鼻基底高度降低',
                'parameters': {'baseHeight': -0.5}
            },
            {
                'name': '鼻基底宽度增加',
                'parameters': {'baseWidth': 0.5}
            },
            {
                'name': '鼻基底宽度减小',
                'parameters': {'baseWidth': -0.5}
            },
            {
                'name': '组合变形1',
                'parameters': {
                    'bridgeHeight': 0.3,
                    'tipLength': 0.2,
                    'nostrilWidth': -0.2
                }
            },
            {
                'name': '组合变形2',
                'parameters': {
                    'bridgeWidth': -0.3,
                    'tipHeight': 0.4,
                    'baseWidth': -0.3
                }
            },
            {
                'name': '全面部对称性综合调整',
                'parameters': {
                    'alar_symmetry': 0.2,
                    'nostril_symmetry': 0.2,
                    'tip_symmetry': 0.2,
                    'bridgeHeight': 0.1,
                    'tipHeight': 0.1
                }
            }
        ]
        
        # 运行测试用例
        results = []
        
        for i, test_case in enumerate(test_cases):
            logger.info(f"运行测试用例 {i+1}/{len(test_cases)}: {test_case['name']}")
            
            # 构建输出路径
            output_path = os.path.join(output_dir, f"nose_transform_{i+1}_{test_case['name']}.jpg")
            
            # 记录开始时间
            start_time = time.time()
            
            # 应用变形
            try:
                transformer.transform(image_path, test_case['parameters'], output_path)
                
                # 记录结束时间
                end_time = time.time()
                elapsed = end_time - start_time
                
                # 记录结果
                results.append({
                    'test_case': test_case['name'],
                    'parameters': test_case['parameters'],
                    'output_path': output_path,
                    'success': True,
                    'elapsed_time': elapsed
                })
                
                logger.info(f"测试用例 {test_case['name']} 成功 | 耗时: {elapsed:.2f}秒")
                
            except Exception as e:
                logger.error(f"测试用例 {test_case['name']} 失败: {str(e)}")
                
                # 记录结果
                results.append({
                    'test_case': test_case['name'],
                    'parameters': test_case['parameters'],
                    'success': False,
                    'error': str(e)
                })
        
        # 生成测试报告
        report_path = os.path.join(output_dir, 'nose_transformation_test_report.json')
        
        with open(report_path, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'image_path': image_path,
                'test_cases': len(test_cases),
                'success_count': sum(1 for r in results if r['success']),
                'failure_count': sum(1 for r in results if not r['success']),
                'results': results
            }, f, indent=2)
        
        logger.info(f"测试完成 | 成功: {sum(1 for r in results if r['success'])}/{len(test_cases)} | 报告路径: {report_path}")
        
        return report_path
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试鼻部变形算法')
    parser.add_argument('--image', required=True, help='测试图像路径')
    parser.add_argument('--output-dir', help='输出目录')
    
    args = parser.parse_args()
    
    try:
        test_nose_transformation(args.image, args.output_dir)
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='鼻部变形算法测试脚本')
    parser.add_argument('--image', type=str, help='输入图像路径')
    parser.add_argument('--output-dir', type=str, help='输出目录路径（可选）')
    
    args = parser.parse_args()
    
    # 如果没有提供图像路径，使用默认测试图像
    if args.image is None:
        args.image = os.path.join(current_dir, 'testdata', 'test_face.jpg')
        logger.info(f'使用默认测试图像: {args.image}')
    
    test_nose_transformation(args.image, args.output_dir)

if __name__ == "__main__":
    main()
