#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
面部特征点检测处理器
用于检测和分析人脸特征点，支持实时和静态图片处理
"""

import os
import sys
import json
import cv2
import logging
import mediapipe as mp
from enum import Enum, auto
from typing import Dict, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger('FaceMeshProcessor')

class TransformDirection(Enum):
    """变形方向枚举类"""
    HORIZONTAL = 'horizontal'  # 水平方向
    VERTICAL = 'vertical'    # 垂直方向
    DEPTH = 'depth'       # 深度方向
    RADIAL = 'radial'      # 径向方向
    CUSTOM = 'custom'      # 自定义方向
    
    def __str__(self):
        return self.value
    
    def to_dict(self):
        return self.value


class FaceMeshProcessor:
    def __init__(self, min_detection_confidence=0.5, min_tracking_confidence=0.5, max_num_faces=1):
        """初始化人脸特征点检测器
        
        Args:
            min_detection_confidence: 最小检测置信度
            min_tracking_confidence: 最小跟踪置信度
            max_num_faces: 最大人脸数量
        """
        self.mp_face_mesh = mp.solutions.face_mesh
        # 设置更详细的日志
        logging.getLogger('mediapipe').setLevel(logging.INFO)
        
        # 创建FaceMesh实例，启用所有特征点检测选项
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,  # 静态图像模式，更适合单帧分析
            max_num_faces=max_num_faces,
            refine_landmarks=True,  # 启用精细特征点检测
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence
        )
        
        # 记录MediaPipe配置
        logger.info(f"[初始化] MediaPipe FaceMesh配置: static_image_mode=True, refine_landmarks=True, min_detection_confidence={min_detection_confidence}")

    def process_image(self, image_path, **kwargs):
        """处理图片并返回特征点信息
        
        Args:
            image_path: 图片路径（str）
            **kwargs: 额外的参数，如特征点检测的配置选项
            
        Returns:
            dict: 包含特征点信息的字典
        """
        try:
            # 从 kwargs 获取 image_path
            if image_path is None:
                image_path = kwargs.pop('image_path', None)
            
            if image_path is None:
                raise ValueError('缺少必需参数: image_path')
                
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                error_msg = f"无法读取图片: {image_path}"
                logger.error(f"[错误] {error_msg}")
                return {'status': 'error', 'message': error_msg}
            
            # 转换颜色空间并检测特征点
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            height, width = image.shape[:2]
            results = self.face_mesh.process(image)
            
            if not results.multi_face_landmarks:
                error_msg = "未检测到人脸"
                logger.error(f"[错误] {error_msg}")
                return {'status': 'error', 'message': error_msg}
            
            # 获取第一个人脸的特征点
            face_landmarks = results.multi_face_landmarks[0]
            if not face_landmarks or not face_landmarks.landmark:
                error_msg = "未检测到有效的特征点"
                logger.error(f"[错误] {error_msg}")
                return {'status': 'error', 'message': error_msg}
            
            # 处理每个特征点
            landmarks = []
            for idx, landmark in enumerate(face_landmarks.landmark):
                # 转换坐标为实际像素值
                x = int(landmark.x * width)
                y = int(landmark.y * height)
                z = landmark.z  # 保持z轴归一化值
                
                # 添加特征点信息
                landmarks.append({
                    'index': idx,
                    'x': x,
                    'y': y,
                    'z': z,
                    'visible': True,  # 默认可见
                    'active': True    # 默认激活
                })
            
            # 准备返回结果
            result_data = {
                'status': 'success',
                'image_info': {
                    'width': width,
                    'height': height,
                    'path': image_path
                },
                'detection': {
                    'total_points': len(landmarks),
                    'face_count': len(results.multi_face_landmarks)
                },
                'landmarks': landmarks
            }
            
            # 记录坐标信息
            x_coords = [landmark['x'] for landmark in landmarks]
            y_coords = [landmark['y'] for landmark in landmarks]
            z_coords = [landmark['z'] for landmark in landmarks]
            
            # 记录特征点索引范围
            indices = [landmark['index'] for landmark in landmarks]
            min_idx = min(indices) if indices else -1
            max_idx = max(indices) if indices else -1
            
            # 统计索引分布
            missing_indices = []
            for i in range(478):  # MediaPipe FaceMesh应该有478个点
                if i not in indices:
                    missing_indices.append(i)
            
            # 记录详细日志
            logger.info(f"[特征检测] 📊 图片尺寸: {width}x{height} | 人脸: {len(results.multi_face_landmarks)} | 点数: {len(landmarks)}")
            logger.info(f"[特征检测] 📏 坐标范围 X: {min(x_coords)}-{max(x_coords)} | Y: {min(y_coords)}-{max(y_coords)} | Z: {min(z_coords):.3f}-{max(z_coords):.3f}")
            logger.info(f"[特征检测] 🔢 索引范围: {min_idx}-{max_idx} | 缺失点数: {len(missing_indices)}")
            
            if missing_indices:
                if len(missing_indices) <= 20:
                    logger.info(f"[特征检测] ❌ 缺失的索引: {missing_indices}")
                else:
                    logger.info(f"[特征检测] ❌ 部分缺失的索引: {missing_indices[:10]}...{missing_indices[-10:]}")
            
            # 序列化并返回结果
            result_json = json.dumps(result_data, ensure_ascii=False)
            logger.info(f"[特征识别] 处理完成: 检测到 {len(landmarks)} 个特征点")
            return result_json
            
        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            logger.error(f"[错误] {error_msg}")
            error_response = {
                "status": "error",
                "message": error_msg
            }
            print(json.dumps(error_response))
            return error_response
    
    def __del__(self):
        """清理资源"""
        self.face_mesh.close()

def get_all_landmarks(image_path: str) -> Dict[int, Tuple[float, float]]:
    """获取图像中的所有面部特征点
    
    Args:
        image_path: 图像路径
        
    Returns:
        特征点字典 {point_id: (x, y)}
    """
    processor = FaceMeshProcessor()
    image = cv2.imread(image_path)
    results = processor.face_mesh.process(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    
    if not results.multi_face_landmarks:
        return {}
    
    # 获取第一个人脸的特征点
    face_landmarks = results.multi_face_landmarks[0]
    
    # 转换为字典格式
    points_dict = {}
    for idx, landmark in enumerate(face_landmarks.landmark):
        points_dict[idx] = (landmark.x * image.shape[1], 
                          landmark.y * image.shape[0])
    
    return points_dict

def main():
    """主函数，用于命令行调用"""
    import argparse
    
    try:
        # 记录Python版本和运行环境
        # 创建参数解析器
        parser = argparse.ArgumentParser(description='面部特征点检测器')
        parser.add_argument('--params', type=str, help='参数JSON字符串')
        parser.add_argument('--image_path', type=str, help='图像路径')
        parser.add_argument('--min_detection_confidence', type=float, default=0.5, help='最小检测置信度')
        parser.add_argument('--min_tracking_confidence', type=float, default=0.5, help='最小跟踪置信度')
        parser.add_argument('--max_num_faces', type=int, default=1, help='最大人脸数量')
        
        # 解析参数
        args = parser.parse_args()
        
        # 打印接收到的参数
        logger.info(f"接收到的参数: {args}")
        
        # 确定图像路径和其他参数
        image_path = None
        options = {}
        
        # 如果直接提供了image_path参数
        if args.image_path:
            image_path = args.image_path
            logger.info(f"使用直接提供的图像路径: {image_path}")
            
            # 收集命令行选项
            options = {
                'min_detection_confidence': args.min_detection_confidence,
                'min_tracking_confidence': args.min_tracking_confidence,
                'max_num_faces': args.max_num_faces
            }
            logger.info(f"使用命令行选项: {options}")
            
        # 如果提供了params参数
        elif args.params:
            try:
                # 打印原始参数字符串
                logger.info(f"原始params参数: {args.params}")
                params = json.loads(args.params)
                logger.info(f"解析后的params: {params}")
                
                if isinstance(params, dict):
                    image_path = params.get('image_path')
                    options = params.get('options', {})
                    logger.info(f"从params中获取图像路径: {image_path}")
                    logger.info(f"从params中获取选项: {options}")
                else:
                    error_msg = f"params不是有效的字典: {type(params)}"
                    logger.error(error_msg)
                    print(json.dumps({
                        'status': 'error',
                        'message': error_msg
                    }))
                    sys.exit(1)
            except Exception as e:
                error_msg = f"参数解析错误: {str(e)}"
                logger.error(error_msg)
                print(json.dumps({
                    'status': 'error',
                    'message': error_msg
                }))
                sys.exit(1)
        
        # 检查必需参数
        if not image_path:
            error_msg = '缺少必需参数: image_path'
            logger.error(error_msg)
            print(json.dumps({
                'status': 'error',
                'message': error_msg
            }))
            sys.exit(1)
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            error_msg = f"图片文件不存在: {image_path}"
            logger.error(error_msg)
            print(json.dumps({
                'status': 'error',
                'message': error_msg
            }))
            sys.exit(1)
        
        # 检查文件大小
        file_size = os.path.getsize(image_path)
        if file_size == 0:
            error_msg = f"图片文件为空: {image_path}"
            logger.error(error_msg)
            print(json.dumps({
                'status': 'error',
                'message': error_msg
            }))
            sys.exit(1)
        
        logger.info(f"开始处理图片: {image_path}")
        logger.debug(f"  • 文件大小: {file_size / 1024:.2f} KB")
        
        # 从选项中提取初始化参数
        min_detection_confidence = options.get('min_detection_confidence', 0.5)
        min_tracking_confidence = options.get('min_tracking_confidence', 0.5)
        max_num_faces = options.get('max_num_faces', 1)
        
        logger.info(f"初始化参数: detection={min_detection_confidence}, tracking={min_tracking_confidence}, faces={max_num_faces}")
        
        # 初始化处理器
        processor = FaceMeshProcessor(
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence,
            max_num_faces=max_num_faces
        )
        
        # 准备处理参数
        process_params = {
            'image_path': image_path,
        }
        
        logger.info(f"处理图像参数: {process_params}")
        
        # 调用处理方法
        result = processor.process_image(**process_params)
        
        # 输出结果
        print(result)
        
    except Exception as e:
        error_msg = f"处理失败: {str(e)}"
        logger.error(error_msg)
        print(json.dumps({
            'status': 'error',
            'message': error_msg
        }))
        sys.exit(1)

if __name__ == "__main__":
    main()
