import numpy as np
from typing import Dict, <PERSON><PERSON>, Optional

class FeaturePointsValidator:
    """面部特征点验证器"""
    
    def __init__(self, landmarks: Dict[int, Tuple[float, float]]):
        """初始化验证器
        
        Args:
            landmarks: 特征点字典，key为点ID，value为(x,y)坐标
        """
        self.landmarks = landmarks
        self.center_x = 664  # 保持原有的中心线位置
        
        # 更新验证标准
        self.SYMMETRY_THRESHOLD = 0.75  # 降低对称性要求
        self.MAX_DISTANCE = 450.0      # 略微放宽距离限制
        self.CENTER_DEVIATION_THRESHOLD = 5.0  # 中心点允许的偏差
        
    def validate_point_symmetry(self, left_id: int, right_id: int) -> float:
        """验证两个点的对称性
        
        Args:
            left_id: 左侧点ID
            right_id: 右侧点ID
            
        Returns:
            对称性得分 (0-1)
        """
        if left_id not in self.landmarks or right_id not in self.landmarks:
            return 0.0
            
        left_point = self.landmarks[left_id]
        right_point = self.landmarks[right_id]
        
        # 计算到中心线的距离
        left_dist = abs(left_point[0] - self.center_x)
        right_dist = abs(right_point[0] - self.center_x)
        
        # 计算y坐标差异
        y_diff = abs(left_point[1] - right_point[1])
        
        # 距离对称性得分 (0-1)
        max_dist = max(left_dist, right_dist)
        min_dist = min(left_dist, right_dist)
        if max_dist == 0:
            dist_score = 0
        else:
            dist_score = min_dist / max_dist
            
        # y坐标对称性得分 (0-1)
        y_score = 1.0 - min(1.0, y_diff / 80.0)  # 增加到80像素的容忍度
        
        # 总分：距离占60%，y坐标占40%
        return 0.6 * dist_score + 0.4 * y_score
        
    def calculate_point_distance(self, point1_id: int, point2_id: int) -> float:
        """计算两点之间的距离
        
        Args:
            point1_id: 第一个点的ID
            point2_id: 第二个点的ID
            
        Returns:
            两点之间的欧氏距离
        """
        if point1_id not in self.landmarks or point2_id not in self.landmarks:
            return float('inf')
            
        p1 = self.landmarks[point1_id]
        p2 = self.landmarks[point2_id]
        
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
        
    def calculate_center_deviation(self, point_id: int) -> float:
        """计算点到中心线的偏差
        
        Args:
            point_id: 点ID
            
        Returns:
            点到中心线的水平距离
        """
        if point_id not in self.landmarks:
            return float('inf')
            
        point = self.landmarks[point_id]
        return abs(point[0] - self.center_x)
