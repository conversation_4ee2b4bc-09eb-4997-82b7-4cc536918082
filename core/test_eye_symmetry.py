#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
眼部对称性连接线测试脚本
基于解剖学分层和MediaPipe Face Mesh特征点
"""

import os
import sys
import logging
import cv2
import numpy as np
import mediapipe as mp

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 将当前目录添加到Python路径
sys.path.insert(0, current_dir)

# 导入图像处理管道
from image_pipeline import preprocess_image

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('EyeSymmetryTest')

# 眼部解剖学中心线点（从上到下）
EYE_CENTER_LINES = {
    'left_eye': [
        282,  # 左眉弓中心
        257,  # 左眉心
        260,  # 左上眼睑中心
        259,  # 左眼中心
        258,  # 左下眼睑中心
        256   # 左眼眶下缘中心
    ],
    'right_eye': [
        52,   # 右眉弓中心
        27,   # 右眉心
        30,   # 右上眼睑中心
        29,   # 右眼中心
        28,   # 右下眼睑中心
        26    # 右眼眶下缘中心
    ]
}

# 解剖学复合体定义
ANATOMICAL_COMPLEXES = {
    # 眼睑复合体 - 包含上下眼睑的主要控制点和褶皱区
    'eyelid_complex': [
        (263, 33,  1.0),   # 内眼角（重要手术锚点）
        (466, 246, 0.8),   # 上睑内侧过渡区
        (388, 161, 1.0),   # 上睑中部（双眼皮手术关键点）
        (387, 160, 0.8),   # 上睑外侧过渡区
        (385, 158, 1.0),   # 外眼角上缘（手术重点）
        (362, 133, 1.0),   # 内眼角下缘
        (374, 145, 0.8),   # 下睑内侧
        (380, 153, 1.0),   # 下睑中部（眼袋手术参考点）
        (382, 155, 0.8),   # 下睑外部
    ],
    
    # 眼角和泪器复合体 - 手术重点区域
    'canthus_complex': [
        (263, 33,  1.0),   # 内眼角核心
        (466, 246, 0.8),   # 内眼角上缘
        (362, 133, 1.0),   # 内眼角下缘
        (385, 158, 1.0),   # 外眼角核心
        (387, 160, 0.8),   # 外眼角上缘
        (382, 155, 0.8),   # 外眼角下缘
    ],
    
    # 眼袋和眶隔复合体 - 新增
    'eyebag_complex': [
        (374, 145, 1.0),   # 泪沟起点（内侧）
        (380, 153, 1.0),   # 眼袋核心区（中央）
        (381, 154, 0.8),   # 眶隔过渡区（外侧）
        (382, 155, 1.0),   # 外侧过渡点（眼角）
    ],
    
    # 眉毛复合体 - 美学重点区域
    'brow_complex': [
        (276, 46,  1.0),   # 内眉起点（立体塑形）
        (283, 53,  0.8),   # 内眉核心过渡区
        (282, 52,  1.0),   # 眉弓中心（美学重点）
        (295, 65,  1.0),   # 眉峰（设计关键）
        (285, 55,  0.8),   # 外眉核心区
        (336, 107, 1.0),   # 眉尾（上扬设计）
    ]
}

# 构建对称点对列表
EYE_SYMMETRIC_PAIRS = []
for complex_name, points in ANATOMICAL_COMPLEXES.items():
    EYE_SYMMETRIC_PAIRS.extend(points)

def draw_eye_symmetry_lines(image_path):
    """绘制眼部对称连接线
    
    Args:
        image_path: 测试图像路径
    """
    logger.info(f"开始绘制眼部对称连接线 | 图像路径: {image_path}")
    
    try:
        # 使用image_pipeline进行预处理
        logger.info("调用image_pipeline进行图像预处理")
        processed_image = preprocess_image(image_path)
        if processed_image is None:
            raise Exception("图像预处理失败")
            
        # 初始化MediaPipe Face Mesh
        mp_face_mesh = mp.solutions.face_mesh
        face_mesh = mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.7
        )
        
        # MediaPipe需要RGB格式进行处理
        height, width = processed_image.shape[:2]
        image_rgb = cv2.cvtColor(processed_image, cv2.COLOR_BGR2RGB)
        results = face_mesh.process(image_rgb)
        
        if not results.multi_face_landmarks:
            raise Exception("未检测到人脸")
            
        # 获取第一个人脸的特征点
        face_landmarks = results.multi_face_landmarks[0]
        
        # 创建可视化图像（将RGB转回BGR格式）
        vis_image = cv2.cvtColor(processed_image, cv2.COLOR_RGB2BGR)
        
        # 绘制左眼中心线
        logger.info("绘制左眼中心线")
        for i in range(len(EYE_CENTER_LINES['left_eye']) - 1):
            start_idx = EYE_CENTER_LINES['left_eye'][i]
            end_idx = EYE_CENTER_LINES['left_eye'][i + 1]
            
            start_point = face_landmarks.landmark[start_idx]
            start_x = int(start_point.x * width)
            start_y = int(start_point.y * height)
            
            end_point = face_landmarks.landmark[end_idx]
            end_x = int(end_point.x * width)
            end_y = int(end_point.y * height)
            
            # 绘制中心线（红色，BGR格式）
            cv2.line(vis_image, (start_x, start_y), (end_x, end_y), (255, 0, 0), 2)
            cv2.circle(vis_image, (start_x, start_y), 3, (255, 0, 0), -1)
            
        # 绘制右眼中心线
        logger.info("绘制右眼中心线")
        for i in range(len(EYE_CENTER_LINES['right_eye']) - 1):
            start_idx = EYE_CENTER_LINES['right_eye'][i]
            end_idx = EYE_CENTER_LINES['right_eye'][i + 1]
            
            start_point = face_landmarks.landmark[start_idx]
            start_x = int(start_point.x * width)
            start_y = int(start_point.y * height)
            
            end_point = face_landmarks.landmark[end_idx]
            end_x = int(end_point.x * width)
            end_y = int(end_point.y * height)
            
            # 绘制中心线（红色，BGR格式）
            cv2.line(vis_image, (start_x, start_y), (end_x, end_y), (255, 0, 0), 2)
            cv2.circle(vis_image, (start_x, start_y), 3, (255, 0, 0), -1)
        
        # 绘制对称点连接线
        logger.info("绘制对称点连接线")
        for left_idx, right_idx, weight in EYE_SYMMETRIC_PAIRS:
            # 获取左侧点
            left_point = face_landmarks.landmark[left_idx]
            left_x = int(left_point.x * width)
            left_y = int(left_point.y * height)
            
            # 获取右侧点
            right_point = face_landmarks.landmark[right_idx]
            right_x = int(right_point.x * width)
            right_y = int(right_point.y * height)
            
            # 根据解剖学复合体设置不同的颜色（BGR格式）
            complex_colors = {
                'eyelid_complex': (0, 255, 0),    # 绿色 (BGR)
                'canthus_complex': (255, 0, 0),   # 红色 (BGR)
                'eyebag_complex': (0, 255, 255),  # 黄色 (BGR)
                'brow_complex': (128, 0, 128)     # 紫色 (BGR)
            }
            
            # 确定当前点对所属的复合体
            current_complex = None
            for complex_name, points in ANATOMICAL_COMPLEXES.items():
                if (left_idx, right_idx, weight) in points:
                    current_complex = complex_name
                    break
            
            # 设置线条颜色和线条宽度
            color = complex_colors.get(current_complex, (0, 255, 0))
            line_width = max(1, int(2 * weight))  # 根据权重调整线条宽度
            
            # 绘制对称连接线（颜色基于复合体，线宽基于权重）
            cv2.line(vis_image, (left_x, left_y), (right_x, right_y), 
                    color, line_width)
            
            # 绘制特征点（颜色基于复合体）
            cv2.circle(vis_image, (left_x, left_y), 3, color, -1)
            cv2.circle(vis_image, (right_x, right_y), 3, color, -1)
            
            # 添加点的索引标签（根据位置调整）
            label_offset = 5
            left_label_x = left_x - 15 if left_x > width/2 else left_x + 5
            left_label_y = left_y - 5 if left_y > height/2 else left_y + 15
            right_label_x = right_x - 15 if right_x > width/2 else right_x + 5
            right_label_y = right_y - 5 if right_y > height/2 else right_y + 15
            
            # 绘制带背景的标签
            label_bg_color = (0, 0, 0)
            cv2.putText(vis_image, str(left_idx), (left_label_x, left_label_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, label_bg_color, 2)
            cv2.putText(vis_image, str(left_idx), (left_label_x, left_label_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
            cv2.putText(vis_image, str(right_idx), (right_label_x, right_label_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, label_bg_color, 2)
            cv2.putText(vis_image, str(right_idx), (right_label_x, right_label_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        # 保存结果
        output_dir = os.path.join(os.path.dirname(image_path), 'output')
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, 'eye_symmetry_lines_test.jpg')
        cv2.imwrite(output_path, vis_image)
        
        logger.info(f"眼部对称连接线绘制完成 | 输出路径: {output_path}")
        
        return output_path
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise

def main():
    """主函数"""
    # 使用标准测试图片
    test_image = "/Users/<USER>/beautifun/testdata/images/face_samples/test_face.jpg"
    
    try:
        output_path = draw_eye_symmetry_lines(test_image)
        logger.info(f"测试成功完成，结果保存在: {output_path}")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
