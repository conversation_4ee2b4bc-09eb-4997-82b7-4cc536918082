import cv2
import numpy as np
import mediapipe as mp
from mediapipe.framework.formats import landmark_pb2

def test_lips_params():
    """测试唇部参数配置"""
    print("\n=== 可视化唇部关键点 ===")
    
    # 读取测试图片
    image = cv2.imread("testdata/test_face.jpg")
    if image is None:
        print("错误：无法读取测试图片")
        return
        
    # 使用 MediaPipe Face Mesh
    mp_face_mesh = mp.solutions.face_mesh
    face_mesh = mp_face_mesh.FaceMesh(
        static_image_mode=True,
        max_num_faces=1,
        min_detection_confidence=0.5
    )
    
    # 转换图片格式
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    results = face_mesh.process(image_rgb)
    
    if not results.multi_face_landmarks:
        print("错误：未检测到人脸")
        return
        
    # 获取第一个检测到的人脸
    face_landmarks = results.multi_face_landmarks[0]
    
    # 创建可视化图像副本
    vis_image = image.copy()
    
    # 定义唇部关键点组
    lip_points = {
        "上唇外轮廓": [61, 185, 40, 39, 37, 0, 267, 269, 270, 409, 291],
        "上唇内轮廓": [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308],
        "下唇外轮廓": [146, 91, 181, 84, 17, 314, 405, 321, 375],
        "下唇内轮廓": [76, 77, 90, 180, 85, 16, 315, 404, 320, 307, 306]
    }
    
    # 定义颜色映射
    colors = {
        "上唇外轮廓": (255, 0, 0),   # 蓝色
        "上唇内轮廓": (0, 255, 0),   # 绿色
        "下唇外轮廓": (0, 0, 255),   # 红色
        "下唇内轮廓": (255, 255, 0)  # 青色
    }
    
    # 绘制各组关键点
    height, width = image.shape[:2]
    for group_name, points in lip_points.items():
        print(f"\n验证{group_name}点")
        color = colors[group_name]
        
        # 绘制点和连线
        for i in range(len(points)):
            # 获取当前点
            pt = face_landmarks.landmark[points[i]]
            x1, y1 = int(pt.x * width), int(pt.y * height)
            
            # 绘制点
            cv2.circle(vis_image, (x1, y1), 2, color, -1)
            
            # 显示点的索引
            cv2.putText(vis_image, str(points[i]), (x1+5, y1+5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
            
            # 打印点的索引
            print(f"  • 点 {points[i]}")
            
            # 连接相邻点
            if i < len(points) - 1:
                next_pt = face_landmarks.landmark[points[i+1]]
                x2, y2 = int(next_pt.x * width), int(next_pt.y * height)
                cv2.line(vis_image, (x1, y1), (x2, y2), color, 1)
    
    # 保存结果
    cv2.imwrite("testdata/lips_points_visualization.jpg", vis_image)
    print("\n已保存唇部关键点可视化结果到: testdata/lips_points_visualization.jpg")
    
    # 验证对称点对
    print("\n=== 验证唇部对称点对 ===")
    symmetry_pairs = [
        (61, 291),   # 上唇外轮廓点对
        (40, 270),   # 上唇内轮廓点对
        (76, 306),   # 下唇内轮廓点对
        (87, 317),   # 下唇外轮廓点对
        (146, 375),  # 嘴角点对
        (91, 321),   # 唇部外侧控制点对
        (181, 405)   # 下唇中部控制点对
    ]
    
    # 创建对称点可视化图像
    sym_image = image.copy()
    
    for left, right in symmetry_pairs:
        print(f"\n验证点对: {left} - {right}")
        
        # 获取左右点的坐标
        left_pt = face_landmarks.landmark[left]
        right_pt = face_landmarks.landmark[right]
        
        # 转换为像素坐标
        left_x, left_y = int(left_pt.x * width), int(left_pt.y * height)
        right_x, right_y = int(right_pt.x * width), int(right_pt.y * height)
        
        # 绘制点和连线
        cv2.circle(sym_image, (left_x, left_y), 3, (0, 255, 0), -1)
        cv2.circle(sym_image, (right_x, right_y), 3, (0, 255, 0), -1)
        cv2.line(sym_image, (left_x, left_y), (right_x, right_y), (255, 255, 0), 1)
        
        # 标注点的索引
        cv2.putText(sym_image, str(left), (left_x-20, left_y-5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        cv2.putText(sym_image, str(right), (right_x+5, right_y-5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
    
    # 保存对称点结果
    cv2.imwrite("testdata/lips_symmetry_pairs.jpg", sym_image)
    print("\n已保存唇部对称点对可视化结果到: testdata/lips_symmetry_pairs.jpg")

if __name__ == "__main__":
    test_lips_params()
