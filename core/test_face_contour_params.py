import cv2
import numpy as np
from feature_points_validator import FeaturePointsValidator
from face_mesh_processor import get_all_landmarks

def draw_param_points(image_path: str, param_name: str, points_config: dict, output_path: str):
    """绘制参数的特征点分析图
    
    颜色标记：
    - 红色：主导点
    - 绿色：协同点
    - 蓝色：支撑点
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 绘制特征点
    # 主导点 - 红色
    for point_id in points_config['primary']:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            cv2.circle(img, (int(x), int(y)), 5, (0, 0, 255), -1)  # 红色
            cv2.putText(img, f"{point_id}(主导)", (int(x), int(y)-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    
    # 协同点 - 绿色
    for point_id in points_config['secondary']:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            cv2.circle(img, (int(x), int(y)), 5, (0, 255, 0), -1)  # 绿色
            cv2.putText(img, f"{point_id}(协同)", (int(x), int(y)-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 支撑点 - 蓝色
    for point_id in points_config['auxiliary']:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            cv2.circle(img, (int(x), int(y)), 5, (255, 0, 0), -1)  # 蓝色
            cv2.putText(img, f"{point_id}(支撑)", (int(x), int(y)-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
    
    # 3. 添加图例
    legend_y = 30
    cv2.putText(img, f"{param_name}特征点分布:", (10, legend_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 点类型图例
    point_types = {
        "主导点": (0, 0, 255),
        "协同点": (0, 255, 0),
        "支撑点": (255, 0, 0)
    }
    
    for i, (name, color) in enumerate(point_types.items()):
        y_offset = legend_y + 30 + i * 25
        cv2.circle(img, (20, y_offset), 5, color, -1)
        cv2.putText(img, name, (35, y_offset+5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 4. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存{param_name}特征点分析图到: {output_path}")

def draw_face_contour_params(image_path: str, output_path: str):
    """绘制面部轮廓四个参数项的特征点分析图
    
    Args:
        image_path: 输入图片路径
        output_path: 输出图片路径
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 面部轮廓参数配置
    face_contour_params = {
        'contour_tighten': {  # 轮廓收紧
            'pairs': [
                {'left': 67, 'right': 297},   # 下颌上部
                {'left': 109, 'right': 338},  # 面颊中部
                {'left': 216, 'right': 436},  # 颧骨内侧
            ],
            'color': (255, 0, 0)  # 蓝色
        },
        'chin_adjust': {  # 下巴调整
            'pairs': [
                {'left': 148, 'right': 377},  # 下巴外侧
                {'left': 149, 'right': 378},  # 下巴中段
                {'left': 150, 'right': 379},  # 下巴内侧
            ],
            'centerPoints': [152, 175],  # 下巴中心点和上部中心点
            'color': (0, 165, 255)  # 橙色
        },
        'cheekbone_adjust': {  # 颧骨调整
            'pairs': [
                {'left': 123, 'right': 352},  # 颧骨下部
                {'left': 50, 'right': 280},   # 颧骨中部
                {'left': 207, 'right': 427},  # 颧骨外侧
            ],
            'color': (0, 255, 0)  # 绿色
        },
        'face_shape': {  # 脸型优化
            'pairs': [
                {'left': 67, 'right': 297},   # 下颌上部
                {'left': 123, 'right': 352},  # 颧骨下部
                {'left': 216, 'right': 436},  # 颧骨内侧
            ],
            'color': (128, 0, 128)  # 紫色
        }
    }
    
    # 3. 计算面部中心线
    center_x = 664  # 根据实际情况调整
    height = img.shape[0]
    
    # 4. 绘制面部中心线
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 5. 绘制中心点
    if 'centerPoints' in face_contour_params['chin_adjust']:
        for point_id in face_contour_params['chin_adjust']['centerPoints']:
            if point_id in landmarks:
                x, y = landmarks[point_id]
                cv2.circle(img, (int(x), int(y)), 4, (0, 255, 255), -1)  # 黄色
                cv2.putText(img, str(point_id), (int(x), int(y)-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 6. 按参数项绘制对称点对和连线
    for param_name, param in face_contour_params.items():
        for pair in param['pairs']:
            left_id = pair['left']
            right_id = pair['right']
            if left_id in landmarks and right_id in landmarks:
                left_point = landmarks[left_id]
                right_point = landmarks[right_id]
                
                # 绘制特征点
                cv2.circle(img, (int(left_point[0]), int(left_point[1])), 3, 
                          param['color'], -1)
                cv2.circle(img, (int(right_point[0]), int(right_point[1])), 3, 
                          param['color'], -1)
                
                # 添加点ID标注
                cv2.putText(img, str(left_id), 
                          (int(left_point[0]), int(left_point[1])-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                cv2.putText(img, str(right_id), 
                          (int(right_point[0]), int(right_point[1])-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # 绘制对称连线
                cv2.line(img, 
                        (int(left_point[0]), int(left_point[1])),
                        (int(right_point[0]), int(right_point[1])),
                        param['color'], 1)
                
                # 绘制连线中点
                mid_x = int((left_point[0] + right_point[0]) / 2)
                mid_y = int((left_point[1] + right_point[1]) / 2)
                cv2.circle(img, (mid_x, mid_y), 2, (0, 255, 255), -1)
    
    # 7. 添加图例
    legend_y = 30
    cv2.putText(img, "图例:", (10, legend_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 中心线和中心点
    cv2.line(img, (10, legend_y+30), (60, legend_y+30), (0, 255, 255), 2)
    cv2.putText(img, "中心线和中心点", (70, legend_y+35), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 各参数项图例
    param_colors = {
        "轮廓收紧": (255, 0, 0),
        "下巴调整": (0, 165, 255),
        "颧骨调整": (0, 255, 0),
        "脸型优化": (128, 0, 128)
    }
    
    for i, (name, color) in enumerate(param_colors.items()):
        y_offset = legend_y + 60 + i * 30
        cv2.circle(img, (20, y_offset), 3, color, -1)
        cv2.line(img, (35, y_offset), (60, y_offset), color, 1)
        cv2.putText(img, name, (70, y_offset+5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 8. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存参数项分析图到: {output_path}")

def validate_face_contour_params(image_path: str):
    """验证面部轮廓四个参数项的特征点对称性
    
    Args:
        image_path: 输入图片路径
    """
    # 1. 获取特征点
    landmarks = get_all_landmarks(image_path)
    validator = FeaturePointsValidator(landmarks)
    
    # 2. 验证各参数项的对称点对
    params = {
        '轮廓收紧': [
            (67, 297),   # 下颌上部
            (109, 338),  # 面颊中部
            (216, 436),  # 颧骨内侧
        ],
        '下巴调整': [
            (148, 377),  # 下巴外侧
            (149, 378),  # 下巴中段
            (150, 379),  # 下巴内侧
        ],
        '颧骨调整': [
            (123, 352),  # 颧骨下部
            (50, 280),   # 颧骨中部
            (207, 427),  # 颧骨外侧
        ],
        '脸型优化': [
            (67, 297),   # 下颌上部
            (123, 352),  # 颧骨下部
            (216, 436),  # 颧骨内侧
        ]
    }
    
    print("\n=== 面部轮廓参数项特征点对称性验证 ===")
    for param_name, pairs in params.items():
        print(f"\n{param_name}:")
        for left_id, right_id in pairs:
            symmetry_score = validator.validate_point_symmetry(left_id, right_id)
            distance = validator.calculate_point_distance(left_id, right_id)
            print(f"点对 {left_id}-{right_id}:")
            print(f"  - 对称性得分: {symmetry_score:.3f}")
            print(f"  - 点间距离: {distance:.2f}")
            
            if symmetry_score < 0.85:  # 对称性阈值
                print("  ⚠️ 警告：对称性较差")
            if distance > 400:  # 距离阈值，根据实际情况调整
                print("  ⚠️ 警告：点间距离过大")
    
    # 3. 验证中心点的位置
    center_points = [152, 175]  # 下巴中心点和上部中心点
    print("\n中心点验证:")
    for point_id in center_points:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            deviation = abs(x - 664)  # 根据实际情况调整中心线位置
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.2f}")

if __name__ == "__main__":
    image_path = "testdata/test_face.jpg"
    
    # 1. 轮廓收紧
    contour_tighten_config = {
        'primary': [67, 297, 109, 338],  # 主导点：下颌角和面颊中部
        'secondary': [152, 175],         # 协同点：下巴区域
        'auxiliary': [145, 146, 147, 374, 375, 376]  # 支撑点：下颌线辅助点
    }
    draw_param_points(image_path, "轮廓收紧", contour_tighten_config, 
                     "testdata/contour_tighten_points.jpg")
    
    # 2. 下巴调整
    chin_adjust_config = {
        'primary': [152],  # 主导点：下巴中心点
        'secondary': [175],  # 协同点：下巴上部中心点
        'auxiliary': [148, 149, 150, 151, 377, 378, 379, 380]  # 支撑点：下巴两侧辅助点
    }
    draw_param_points(image_path, "下巴调整", chin_adjust_config,
                     "testdata/chin_adjust_points.jpg")
    
    # 3. 颧骨调整
    cheekbone_adjust_config = {
        'primary': [216, 436],  # 主导点：颧骨主点
        'secondary': [67, 297], # 协同点：颌角
        'auxiliary': [123, 352, 50, 280, 207, 427]  # 支撑点：颧骨周围辅助点
    }
    draw_param_points(image_path, "颧骨调整", cheekbone_adjust_config,
                     "testdata/cheekbone_adjust_points.jpg")
    
    # 4. 脸型优化
    face_shape_config = {
        'primary': [152, 67, 297],  # 主导点：下巴和下颌角
        'secondary': [109, 338],    # 协同点：下颌线
        'auxiliary': [216, 436]     # 支撑点：颧骨区域
    }
    draw_param_points(image_path, "脸型优化", face_shape_config,
                     "testdata/face_shape_points.jpg")
    
    output_path = "testdata/face_contour_params_analysis.jpg"
    validate_face_contour_params(image_path)
    draw_face_contour_params(image_path, output_path)
