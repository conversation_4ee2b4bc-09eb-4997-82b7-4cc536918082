import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional

class FaceSymmetricFinder:
    """面部对称点查找器
    
    基于几何原理查找面部特征点的对称点：
    1. 以A点为出发点连线垂直面部中心线
    2. A和B必须在面部中心线的两侧
    3. A和B到面部中心线的垂直距离应该接近相等
    4. 在理论位置附近寻找最近的实际特征点作为对称点B
    """
    
    def __init__(self, center_x: float, max_search_radius: float = 50.0):
        """初始化对称点查找器
        
        Args:
            center_x: 面部中心线的x坐标
            max_search_radius: 搜索对称点的最大半径（像素）
        """
        self.center_x = center_x
        self.max_search_radius = max_search_radius
        
    def find_symmetric_point(self, 
                           point_A: Tuple[float, float],
                           all_points: Dict[int, Tuple[float, float]],
                           max_dist_diff_ratio: float = 0.1) -> Optional[Tuple[int, Tuple[float, float], float]]:
        """查找目标点的最佳对称点
        
        Args:
            point_A: 目标点A的坐标 (x, y)
            all_points: 所有特征点的字典 {point_id: (x, y)}
            max_dist_diff_ratio: 允许的最大距离差异比例（默认0.1，即10%）
            
        Returns:
            如果找到对称点，返回 (point_id, coords, distance_to_theory)
            如果未找到，返回 None
        """
        # 1. 计算A点到中心线的垂直距离
        dist_A = abs(point_A[0] - self.center_x)
        
        # 2. 计算理论对称点B的坐标
        theory_x = self.center_x - dist_A if point_A[0] > self.center_x else self.center_x + dist_A
        theory_B = (theory_x, point_A[1])
        
        # 3. 在theory_B附近找最近的实际特征点
        min_dist = float('inf')
        best_point = None
        
        for point_id, coords in all_points.items():
            # 跳过A点自身
            if abs(coords[0] - point_A[0]) < 1 and abs(coords[1] - point_A[1]) < 1:
                continue
            
            # 确保在中心线的另一侧
            if (point_A[0] > self.center_x and coords[0] > self.center_x) or \
               (point_A[0] < self.center_x and coords[0] < self.center_x):
                continue
            
            # 计算到理论点的距离
            dist = np.sqrt((coords[0] - theory_x)**2 + 
                         (coords[1] - point_A[1])**2)
            
            # 如果超出最大搜索半径，跳过
            if dist > self.max_search_radius:
                continue
            
            # 计算到中心线距离的差异比例
            dist_B = abs(coords[0] - self.center_x)
            dist_diff_ratio = abs(dist_B - dist_A) / dist_A
            
            # 如果距离差异太大，跳过
            if dist_diff_ratio > max_dist_diff_ratio:
                continue
            
            if dist < min_dist:
                min_dist = dist
                best_point = (point_id, coords, dist)
        
        return best_point
    
    def visualize_symmetric_points(self,
                                 point_A: Tuple[float, float],
                                 point_B: Optional[Tuple[int, Tuple[float, float], float]],
                                 image: np.ndarray) -> np.ndarray:
        """可视化对称点分析结果
        
        Args:
            point_A: 目标点A的坐标
            point_B: 对称点B的信息 (point_id, coords, distance)
            image: 原始图像
            
        Returns:
            标注了分析结果的图像
        """
        height = image.shape[0]
        img = image.copy()
        
        # 1. 绘制中心线
        cv2.line(img, (int(self.center_x), 0), (int(self.center_x), height), (0, 255, 0), 2)
        cv2.putText(img, "中心线", (int(self.center_x)+5, 100), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 2. 绘制A点
        cv2.circle(img, (int(point_A[0]), int(point_A[1])), 5, (0, 0, 255), -1)
        dist_A = abs(point_A[0] - self.center_x)
        cv2.putText(img, f"A ({dist_A:.0f}px)", 
                    (int(point_A[0])+5, int(point_A[1])-5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # 3. 绘制A点到中心线的垂直距离
        cv2.line(img, (int(point_A[0]), int(point_A[1])), 
                (int(self.center_x), int(point_A[1])), (128, 128, 128), 1)
        
        if point_B:
            point_id, coords, dist = point_B
            
            # 4. 绘制B点
            cv2.circle(img, (int(coords[0]), int(coords[1])), 5, (255, 0, 255), -1)
            dist_B = abs(coords[0] - self.center_x)
            cv2.putText(img, f"B-{point_id} ({dist_B:.0f}px)", 
                        (int(coords[0])-100, int(coords[1])-5), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
            
            # 5. 绘制连线
            cv2.line(img, (int(point_A[0]), int(point_A[1])), 
                    (int(coords[0]), int(coords[1])), (255, 255, 0), 2)
            
            # 6. 绘制B点到中心线的垂直距离
            cv2.line(img, (int(coords[0]), int(coords[1])), 
                    (int(self.center_x), int(coords[1])), (128, 128, 128), 1)
            
            # 7. 标注距离差异
            dist_diff = abs(dist_B - dist_A)
            dist_diff_ratio = dist_diff / dist_A
            cv2.putText(img, f"差异: {dist_diff:.1f}px ({dist_diff_ratio:.1%})", 
                        (int((point_A[0] + coords[0])/2)-50, int(point_A[1])-20), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
        
        return img
