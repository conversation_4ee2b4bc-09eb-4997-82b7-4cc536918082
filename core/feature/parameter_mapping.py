#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

"""
特征参数映射模块
定义面部特征变形的参数配置和映射关系
"""

from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Optional, Tuple, Set

# 中心线点集合定义
CENTER_LINE_POINTS: Set[int] = {
    10,   # 发际线中心
    151,  # 前额中心
    168,  # 前额下部
    6,    # 鼻梁最高点
    197,  # 鼻梁上部
    195,  # 鼻梁中部
    5,    # 鼻梁下部
    4,    # 鼻尖上方
    1,    # 鼻尖
    2,    # 鼻底中点
    164,  # 人中上部
    0,    # 人中中部
    17,   # 下巴中点
    152   # 下巴底部
}

# 点的角色定义
class PointRole(Enum):
    PRIMARY = "primary"       # 主要控制点
    SECONDARY = "secondary"   # 次要控制点
    AUXILIARY = "auxiliary"   # 辅助点
    CONSTRAINT = "constraint" # 约束点
    SYMMETRIC = "symmetric"   # 对称点

# 变形方向定义
class TransformDirection(Enum):
    HORIZONTAL = "horizontal"   # 水平方向
    VERTICAL = "vertical"       # 垂直方向
    DEPTH = "depth"            # 深度方向
    RADIAL = "radial"          # 径向
    CUSTOM = "custom"          # 自定义方向

# 曲线类型定义
class CurveType(Enum):
    LINEAR = "linear"           # 线性
    QUADRATIC = "quadratic"    # 二次曲线
    CUBIC = "cubic"            # 三次曲线
    SINE = "sine"              # 正弦曲线
    CUSTOM = "custom"          # 自定义曲线

@dataclass
class TransformVector:
    """变形向量"""
    direction: TransformDirection
    magnitude: float = 1.0
    custom_direction: Optional[Tuple[float, float, float]] = None

@dataclass
class IntensityMapping:
    """强度映射"""
    input_range: Tuple[float, float]
    output_range: Tuple[float, float]
    curve_type: CurveType
    control_points: Optional[List[Tuple[float, float]]] = None

@dataclass
class PointTransformConfig:
    """点变形配置"""
    transform_vector: TransformVector
    weight: float = 1.0
    falloff_radius: float = 50.0
    constraint_strength: float = 1.0

@dataclass
class RegionConfig:
    """区域配置"""
    boundary_points: List[int]
    center_point: Optional[int] = None
    symmetric_axis: Optional[List[int]] = None

@dataclass
class ParameterConfig:
    """参数配置"""
    display_name: str
    primary_points: List[int]
    secondary_points: List[int]
    auxiliary_points: List[int]
    transform_configs: Dict[int, PointTransformConfig]
    intensity_mapping: IntensityMapping
    min_value: float = -1.0
    max_value: float = 1.0
    default_value: float = 0.0
    weight: float = 1.0
    region: Optional[RegionConfig] = None
    symmetric_points: Optional[List[Tuple[int, int]]] = None

@dataclass
class AreaConfig:
    """区域配置"""
    display_name: str
    parameters: Dict[str, ParameterConfig]
    region: RegionConfig

# 面部区域配置
FACIAL_AREA_CONFIGS: Dict[str, AreaConfig] = {
    # 面部调整模块
    'face': AreaConfig(
        display_name="面部",
        parameters={
            'slim': ParameterConfig(
                display_name="瘦脸",
                primary_points=[10, 338, 297],
                secondary_points=[332, 284, 251],
                auxiliary_points=[389, 356, 454],
                transform_configs={
                    10: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL)),
                    338: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL)),
                    297: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL))
                },
                intensity_mapping=IntensityMapping((-1, 1), (-30, 30), CurveType.CUBIC)
            ),
            'v_shape': ParameterConfig(
                display_name="V脸",
                primary_points=[152, 148, 176],
                secondary_points=[149, 150, 136],
                auxiliary_points=[172, 58, 132],
                transform_configs={
                    152: PointTransformConfig(TransformVector(TransformDirection.RADIAL)),
                    148: PointTransformConfig(TransformVector(TransformDirection.RADIAL)),
                    176: PointTransformConfig(TransformVector(TransformDirection.RADIAL))
                },
                intensity_mapping=IntensityMapping((-1, 1), (-25, 25), CurveType.QUADRATIC)
            )
        },
        region=RegionConfig([10, 338, 297, 332, 284, 251, 389, 356, 454])
    ),

    # 鼻部调整模块
    'nose': AreaConfig(
        display_name="鼻部",
        parameters={
            'alar_symmetry': ParameterConfig(
                display_name="鼻翼对称性",
                primary_points=[129, 358, 219, 439],  # 鼻翼基底点和外缘点
                secondary_points=[235, 455, 115, 344],  # 鼻翼沟上点和内侧点
                auxiliary_points=[220, 440, 114, 343, 217, 437],  # 软骨点和底部轮廓点
                symmetric_points=[
                    (129, 358),  # 鼻翼基底点对
                    (219, 439),  # 鼻翼外缘点对
                    (235, 455),  # 鼻翼沟上点对
                    (115, 344),  # 鼻翼内侧点对
                    (220, 440),  # 鼻翼软骨点对
                    (114, 343),  # 鼻翼底外侧点对
                    (217, 437)   # 鼻翼底内侧点对
                ],
                transform_configs={
                    129: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 1.0),
                        weight=1.0,
                        falloff_radius=30.0,
                        constraint_strength=0.8
                    ),
                    358: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 1.0),
                        weight=1.0,
                        falloff_radius=30.0,
                        constraint_strength=0.8
                    ),
                    219: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 0.8),
                        weight=1.0,
                        falloff_radius=25.0,
                        constraint_strength=0.7
                    ),
                    439: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 0.8),
                        weight=1.0,
                        falloff_radius=25.0,
                        constraint_strength=0.7
                    ),
                    235: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 0.6),
                        weight=0.8,
                        falloff_radius=20.0,
                        constraint_strength=0.6
                    ),
                    455: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 0.6),
                        weight=0.8,
                        falloff_radius=20.0,
                        constraint_strength=0.6
                    )
                },
                intensity_mapping=IntensityMapping((-1, 1), (-10, 10), CurveType.QUADRATIC),
                region=RegionConfig(
                    boundary_points=[129, 358, 219, 439, 235, 455, 115, 344],
                    center_point=1  # 鼻尖点作为中心参考
                )
            ),
            'nostril_symmetry': ParameterConfig(
                display_name="鼻孔对称性",
                primary_points=[79, 309, 166, 392],  # 鼻孔外缘点和内缘点
                secondary_points=[98, 327, 64, 294],  # 鼻孔底点和内侧点
                auxiliary_points=[97, 326, 167, 393],  # 顶点和内侧补充点
                symmetric_points=[
                    (79, 309),   # 鼻孔外缘点对
                    (166, 392),  # 鼻孔内缘点对
                    (98, 327),   # 鼻孔底点对
                    (64, 294),   # 鼻孔内侧点对
                    (97, 326),   # 鼻孔顶点对
                    (167, 393)   # 鼻孔内侧补充点对
                ],
                transform_configs={
                    79: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 1.0),
                        weight=1.0,
                        falloff_radius=20.0,
                        constraint_strength=0.9
                    ),
                    309: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 1.0),
                        weight=1.0,
                        falloff_radius=20.0,
                        constraint_strength=0.9
                    ),
                    166: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 0.8),
                        weight=1.0,
                        falloff_radius=15.0,
                        constraint_strength=0.8
                    ),
                    392: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 0.8),
                        weight=1.0,
                        falloff_radius=15.0,
                        constraint_strength=0.8
                    )
                },
                intensity_mapping=IntensityMapping((-1, 1), (-8, 8), CurveType.QUADRATIC),
                region=RegionConfig(
                    boundary_points=[79, 309, 166, 392, 98, 327, 64, 294],
                    center_point=2  # 鼻小柱顶点作为中心参考
                )
            ),
            'tip_symmetry': ParameterConfig(
                display_name="鼻尖对称性",
                primary_points=[48, 278, 49, 279],  # 鼻尖外侧点和内侧点
                secondary_points=[64, 294, 114, 343],  # 过渡点
                auxiliary_points=[],
                symmetric_points=[
                    (48, 278),   # 鼻尖外侧点对
                    (49, 279),   # 鼻尖内侧点对
                    (64, 294),   # 鼻尖-鼻翼过渡点对
                    (114, 343)   # 鼻尖-鼻孔过渡点对
                ],
                transform_configs={
                    48: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 1.0),
                        weight=1.0,
                        falloff_radius=15.0,
                        constraint_strength=0.9
                    ),
                    278: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 1.0),
                        weight=1.0,
                        falloff_radius=15.0,
                        constraint_strength=0.9
                    ),
                    49: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 0.8),
                        weight=1.0,
                        falloff_radius=12.0,
                        constraint_strength=0.8
                    ),
                    279: PointTransformConfig(
                        transform_vector=TransformVector(TransformDirection.HORIZONTAL, 0.8),
                        weight=1.0,
                        falloff_radius=12.0,
                        constraint_strength=0.8
                    )
                },
                intensity_mapping=IntensityMapping((-1, 1), (-5, 5), CurveType.QUADRATIC),
                region=RegionConfig(
                    boundary_points=[48, 278, 49, 279, 64, 294, 114, 343],
                    center_point=1  # 鼻尖点作为中心参考
                )
            ),
            'bridge_height': ParameterConfig(
                display_name="鼻梁高度",
                primary_points=[6, 197, 195, 4],  # 主要控制点：鼻梁最高点、上部、中部、下部
                secondary_points=[168, 5, 1, 19, 2],  # 次要控制点：前额、鼻梁下部、鼻尖、鼻底
                auxiliary_points=[98, 327, 97, 326],  # 辅助点：鼻孔辅助点
                symmetric_points=[
                    (98, 327),  # 左右鼻孔辅助点对
                    (97, 326)   # 左右鼻孔过渡点对
                ],
                transform_configs={
                    # 主要控制点
                    6: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 1.2)),    # 鼻梁最高点
                    197: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 1.0)),  # 鼻梁上部
                    195: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.8)),  # 鼻梁中部
                    4: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.6)),    # 鼻梁下部
                    # 次要控制点
                    168: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.8)),  # 前额过渡
                    5: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.7)),    # 鼻梁下部过渡
                    1: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.7)),    # 鼻尖过渡
                    19: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.6)),   # 鼻尖下部
                    2: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.5)),    # 鼻底中点
                    # 辅助点
                    98: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.4)),    # 左鼻孔辅助
                    327: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.4)),   # 右鼻孔辅助
                    97: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.3)),    # 左鼻孔过渡
                    326: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.3))    # 右鼻孔过渡
                },
                intensity_mapping=IntensityMapping((-1, 1), (-15, 15), CurveType.CUBIC),
                region=RegionConfig(
                    boundary_points=[168, 6, 197, 195, 5, 4, 1, 2],  # 完整的鼻梁中心线
                    center_point=197
                )
            ),
            'wing_width': ParameterConfig(
                display_name="鼻翼宽度",
                primary_points=[129, 358, 219, 439],  # 左右鼻翼主要点
                secondary_points=[235, 455, 240, 460, 114, 343, 217, 437, 45, 275],  # 左右对称的次要点
                auxiliary_points=[128, 357, 198, 418, 115, 220, 440, 344],  # 左右对称的辅助点
                symmetric_points=[
                    (129, 358),  # 鼻翼基底点对
                    (219, 439),  # 鼻翼外缘点对
                    (235, 455),  # 鼻翼沟上点对
                    (115, 344),  # 鼻翼内侧点对
                    (220, 440),  # 鼻翼软骨点对
                    (114, 343),  # 鼻翼底外侧点对
                    (217, 437),  # 鼻翼底内侧点对
                    # 从test_nose_symmetry.py补充的对称点对
                    (128, 357),  # 鼻翼辅助点对
                    (198, 418),  # 鼻翼过渡点对
                ],
                transform_configs={
                    # 左侧鼻翼点
                    129: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 1.0)),  # 鼻翼外缘主点
                    219: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.8)),  # 鼻翼中部主点
                    235: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.6)),  # 法令纹上部
                    240: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.5)),  # 法令纹中部
                    114: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.4)),  # 法令纹下部
                    217: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.3)),  # 过渡点
                    45: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.4)),   # 鼻翼与鼻尖过渡
                    115: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.3)),  # 鼻翼上部辅助点
                    220: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.2)),  # 鼻翼中部辅助点
                    # 右侧鼻翼点（对称）
                    358: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -1.0)),  # 鼻翼外缘主点
                    439: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.8)),  # 鼻翼中部主点
                    455: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.6)),  # 法令纹上部
                    460: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.5)),  # 法令纹中部
                    343: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.4)),  # 法令纹下部
                    437: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 过渡点
                    275: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.4)),  # 鼻翼与鼻尖过渡
                    440: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 鼻翼上部辅助点
                    344: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.2))   # 鼻翼中部辅助点
                },
                intensity_mapping=IntensityMapping((-1, 1), (-10, 10), CurveType.QUADRATIC),
                region=RegionConfig(
                    boundary_points=[
                        129, 219, 235, 240, 114,  # 左侧鼻翼轮廓
                        358, 439, 455, 460, 343,  # 右侧鼻翼轮廓
                        45, 275, 115, 220, 440, 344  # 过渡点
                    ],
                    center_point=4
                )
            ),
            'nostril_height': ParameterConfig(
                display_name="鼻孔高度",
                primary_points=[79, 309, 166, 392],  # 左右鼻孔主要点
                secondary_points=[2, 97, 326, 327, 167, 294],  # 鼻孔辅助点和内侧轮廓
                auxiliary_points=[48, 278],  # 外侧轮廓点
                symmetric_points=[
                    (79, 309), (166, 392),  # 主要点对
                    (97, 326),  # 辅助点对
                    (48, 278),  # 外侧轮廓点对
                    # 从test_nose_symmetry.py补充的对称点对
                    (64, 294),  # 鼻孔内侧点对
                    (167, 393)  # 鼻孔内侧补充点对
                ],
                transform_configs={
                    # 左侧鼻孔
                    79: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 1.0)),   # 鼻孔上缘
                    166: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.8)),  # 鼻孔下缘
                    167: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.7)),  # 鼻孔内侧
                    48: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.6)),   # 鼻孔外侧
                    97: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.5)),   # 鼻孔底部
                    # 右侧鼻孔（对称）
                    309: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 1.0)),  # 鼻孔上缘
                    392: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.8)),  # 鼻孔下缘
                    294: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.7)),  # 鼻孔内侧
                    278: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.6)),  # 鼻孔外侧
                    326: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.5))   # 鼻孔底部
                },
                intensity_mapping=IntensityMapping((-1, 1), (-8, 8), CurveType.QUADRATIC),
                region=RegionConfig(
                    boundary_points=[
                        79, 166, 167, 48, 97,    # 左侧鼻孔轮廓
                        309, 392, 294, 278, 326,  # 右侧鼻孔轮廓
                        2  # 鼻底中点
                    ],
                    center_point=2
                )
            ),
            'nose_tip': ParameterConfig(
                display_name="鼻尖",
                primary_points=[4, 19, 20],  # 鼻尖主要点
                secondary_points=[195, 2, 94, 393],  # 鼻梁、鼻底、左右鼻尖侧面
                auxiliary_points=[115, 220, 440, 344],  # 过渡点
                symmetric_points=[
                    (115, 344), (220, 440),  # 过渡点对
                    # 从test_nose_symmetry.py补充的对称点对
                    (48, 278),   # 鼻尖外侧点对
                    (49, 279),   # 鼻尖内侧点对
                    (64, 294),   # 鼻尖-鼻翼过渡点对
                    (94, 393)    # 鼻尖侧面点对
                ],
                transform_configs={
                    # 主要控制点
                    4: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 1.0)),   # 鼻尖主点
                    19: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.8)),  # 鼻尖上部
                    20: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.6)),  # 鼻尖下部
                    # 次要控制点
                    195: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.5)), # 鼻梁下部
                    2: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.4)),   # 鼻底中点
                    94: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.4)),  # 左侧鼻尖
                    393: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.4)), # 右侧鼻尖
                    # 辅助点
                    115: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.3)), # 左侧过渡点
                    220: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.3)), # 左侧辅助点
                    440: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.3)), # 右侧过渡点
                    344: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.3))  # 右侧辅助点
                },
                intensity_mapping=IntensityMapping((-1, 1), (-8, 8), CurveType.QUADRATIC),
                region=RegionConfig(
                    boundary_points=[
                        4, 19, 20,  # 鼻尖主要点
                        195, 2,     # 鼻梁和鼻底点
                        94, 393,    # 左右鼻尖点
                        115, 220, 440, 344  # 过渡点
                    ],
                    center_point=4
                )
            ),
            'nose_side': ParameterConfig(
                display_name="鼻翼侧面",
                primary_points=[94, 167, 164, 393, 391, 322],  # 左右鼻翼侧面主要点
                secondary_points=[115, 220, 45, 275, 440, 344, 278, 294],  # 过渡点和辅助点
                auxiliary_points=[48, 64, 98],  # 外侧轮廓点
                symmetric_points=[
                    (167, 391),  # 鼻翼侧面上部
                    (164, 322),  # 鼻翼侧面下部
                    (45, 275),   # 鼻翼与鼻尖过渡点
                    (115, 440),  # 过渡点对
                    (220, 344),  # 过渡点对
                    (48, 278),   # 外侧轮廓点对
                    (94, 393)    # 鼻尖侧面点
                ],
                transform_configs={
                    # 左侧鼻翼侧面点
                    94: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.8)),   # 左侧主点
                    167: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.8)),  # 左侧上部
                    164: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.6)),  # 左侧下部
                    # 右侧鼻翼侧面点（对称）
                    393: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.8)),  # 右侧主点
                    391: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.8)),  # 右侧上部
                    322: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.6)),  # 右侧下部
                    # 过渡点
                    115: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.4)),  # 左侧过渡点1
                    220: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.4)),  # 左侧过渡点2
                    45: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.3)),   # 左侧辅助点
                    275: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.4)),  # 右侧过渡点1
                    440: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.4)),  # 右侧过渡点2
                    344: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 右侧辅助点1
                    278: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 右侧辅助点2
                    294: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 右侧辅助点3
                    # 外侧轮廓点
                    48: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.2)),   # 左侧外轮廓点1
                    64: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.2)),   # 左侧外轮廓点2
                    98: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 0.2))    # 左侧外轮廓点3
                },
                intensity_mapping=IntensityMapping((-1, 1), (-8, 8), CurveType.QUADRATIC),
                region=RegionConfig(
                    boundary_points=[94, 167, 164, 393, 391, 322],  # 左右鼻翼侧面轮廓点
                    center_point=4  # 鼻尖中心点
                )
            )
        },
        region=RegionConfig(
            boundary_points=[6, 197, 195, 4, 1, 2, 129, 358, 94, 167, 164, 393, 391, 322],  # 完整的鼻部轮廓点
            center_point=4  # 鼻尖中心点
        )
    ),
    # 眼部调整模块
    'eyes': AreaConfig(
        display_name="眼部",
        parameters={
            'double_eyelid': ParameterConfig(
                display_name="双眼皮",
                primary_points=[157, 158, 386, 387],
                secondary_points=[159, 160, 388, 389],
                auxiliary_points=[161, 246, 390, 466],
                transform_configs={
                    157: PointTransformConfig(TransformVector(TransformDirection.VERTICAL)),
                    386: PointTransformConfig(TransformVector(TransformDirection.VERTICAL))
                },
                intensity_mapping=IntensityMapping((-1, 1), (-5, 5), CurveType.CUBIC)
            ),
            'eye_size': ParameterConfig(
                display_name="眼睛大小",
                primary_points=[33, 133, 362, 263],
                secondary_points=[157, 158, 386, 387],
                auxiliary_points=[159, 160, 388, 389],
                transform_configs={
                    33: PointTransformConfig(TransformVector(TransformDirection.RADIAL)),
                    362: PointTransformConfig(TransformVector(TransformDirection.RADIAL))
                },
                intensity_mapping=IntensityMapping((-1, 1), (-12, 12), CurveType.QUADRATIC)
            )
        },
        region=RegionConfig(
            boundary_points=[33, 133, 157, 158, 159, 160, 161, 246, 362, 263, 386, 387, 388, 389, 390, 466],  # 眼部轮廓点
            center_point=None  # 眼部没有单一中心点
        )
    ),

    # 唇部调整模块
    'lips': AreaConfig(
        display_name="唇部",
        parameters={
            'thickness': ParameterConfig(
                display_name="唇厚度",
                primary_points=[0, 17, 267, 314],  # 上下唇主要点
                secondary_points=[269, 270, 409, 291],  # 唇部辅助点
                auxiliary_points=[375, 321, 405],  # 唇角过渡点
                transform_configs={
                    # 上唇点
                    0: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 1.0)),   # 上唇中心
                    17: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.8)),  # 下唇中心
                    267: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.6)), # 上唇侧面
                    314: PointTransformConfig(TransformVector(TransformDirection.VERTICAL, 0.6))  # 下唇侧面
                },
                intensity_mapping=IntensityMapping((-1, 1), (-8, 8), CurveType.CUBIC),
                region=RegionConfig(
                    boundary_points=[0, 17, 267, 314],  # 唇部主要轮廓点
                    center_point=0  # 上唇中心点
                )
            ),
            'width': ParameterConfig(
                display_name="唇宽度",
                primary_points=[61, 291],  # 左右唇角主要点
                secondary_points=[146, 375],  # 唇角辅助点
                auxiliary_points=[91, 321],  # 过渡点
                transform_configs={
                    # 左右唇角点
                    61: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, 1.0)),   # 左唇角
                    291: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -1.0))  # 右唇角
                },
                intensity_mapping=IntensityMapping((-1, 1), (-15, 15), CurveType.LINEAR),
                region=RegionConfig(
                    boundary_points=[61, 291, 146, 375, 91, 321],  # 唇角和过渡点
                    center_point=0  # 上唇中心点
                )
            )
        },
        region=RegionConfig(
            boundary_points=[0, 267, 269, 270, 409, 291, 375, 321, 405, 314, 17],  # 唇部完整轮廓
            center_point=0  # 上唇中心点
        )
    ),

    # 抗衰模块
    'anti_aging': AreaConfig(
        display_name="抗衰",
        parameters={
            'nasolabial': ParameterConfig(
                display_name="法令纹",
                primary_points=[129, 358],
                secondary_points=[219, 439],
                auxiliary_points=[235, 455],
                transform_configs={
                    129: PointTransformConfig(TransformVector(TransformDirection.DEPTH)),
                    358: PointTransformConfig(TransformVector(TransformDirection.DEPTH))
                },
                intensity_mapping=IntensityMapping((-1, 1), (-10, 10), CurveType.CUBIC)
            ),
            'eye_wrinkles': ParameterConfig(
                display_name="眼纹",
                primary_points=[157, 386],  # 左右眼角纹主要点
                secondary_points=[158, 387],  # 眼纹深度点
                auxiliary_points=[159, 388],  # 过渡点
                transform_configs={
                    # 左侧眼纹
                    157: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 1.0)),  # 左眼角主点
                    158: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.8)),  # 左眼纹深度
                    159: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.6)),  # 左眼纹过渡
                    # 右侧眼纹（对称）
                    386: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 1.0)),  # 右眼角主点
                    387: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.8)),  # 右眼纹深度
                    388: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.6))   # 右眼纹过渡
                },
                intensity_mapping=IntensityMapping((-1, 1), (-5, 5), CurveType.QUADRATIC),
                region=RegionConfig(
                    boundary_points=[157, 158, 159, 386, 387, 388],  # 眼纹轮廓点
                    center_point=None  # 眼纹没有单一中心点
                )
            ),
            'forehead': ParameterConfig(
                display_name="抬头纹",
                primary_points=[10, 338],  # 左右额顶主要点
                secondary_points=[109, 297],  # 额顶深度点
                auxiliary_points=[67, 332],  # 过渡点
                transform_configs={
                    # 左侧额顶
                    10: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 1.0)),   # 左侧主点
                    109: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.8)),  # 左侧深度
                    67: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.6)),   # 左侧过渡
                    # 右侧额顶（对称）
                    338: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 1.0)),  # 右侧主点
                    297: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.8)),  # 右侧深度
                    332: PointTransformConfig(TransformVector(TransformDirection.DEPTH, 0.6))   # 右侧过渡
                },
                intensity_mapping=IntensityMapping((-1, 1), (-8, 8), CurveType.CUBIC),
                region=RegionConfig(
                    boundary_points=[10, 109, 67, 338, 297, 332],  # 额顶轮廓点
                    center_point=None  # 额顶没有单一中心点
                )
            )
        },
        region=RegionConfig(
            boundary_points=[129, 219, 235, 358, 439, 455],  # 法令纹和抬头纹轮廓点
            center_point=None  # 抗衬区域没有单一中心点
        )
    ),

    # 右眼调整模块
    'right_eye': AreaConfig(
        display_name="右眼",
        parameters={
            'eye_corner': ParameterConfig(
                display_name="眼角",
                primary_points=[359, 463, 414],  # 右眼角主要点
                secondary_points=[446, 445],  # 眼角辅助点
                auxiliary_points=[342, 445, 444],  # 过渡点
                transform_configs={
                    # 右眼角主要点
                    359: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.8)),  # 眼角主点
                    463: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.6)),  # 眼角上部
                    414: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.4)),  # 眼角下部
                    # 辅助点
                    446: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 上部辅助点
                    445: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 下部辅助点
                    # 过渡点
                    342: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.2)),  # 外侧过渡点
                    444: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.2))   # 内侧过渡点
                },
                intensity_mapping=IntensityMapping((-1, 1), (-10, 10), CurveType.LINEAR),
                region=RegionConfig(
                    boundary_points=[359, 463, 414, 446, 445],  # 眼角轮廓点
                    center_point=359  # 眼角中心点
                )
            )
        },
        region=RegionConfig(
            boundary_points=[359, 463, 414, 446, 445, 342],  # 右眼完整轮廓
            center_point=359  # 右眼中心点
        )
    ),

    # 下巴调整模块
    'jaw': AreaConfig(
        display_name="下巴",
        parameters={
            'jaw_line': ParameterConfig(
                display_name="下颜线",
                primary_points=[418, 419, 420, 421],  # 下颜线主要点
                secondary_points=[422, 423, 424],  # 下颜线辅助点
                auxiliary_points=[425, 426, 427],  # 过渡点
                transform_configs={
                    # 下颜线主要点
                    418: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.9)),  # 上部主点
                    419: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.7)),  # 中部主点
                    420: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.5)),  # 下部主点
                    421: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.4)),  # 下巴尖
                    # 辅助点
                    422: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 上部辅助
                    423: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.3)),  # 中部辅助
                    424: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.2)),  # 下部辅助
                    # 过渡点
                    425: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.2)),  # 上部过渡
                    426: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.2)),  # 中部过渡
                    427: PointTransformConfig(TransformVector(TransformDirection.HORIZONTAL, -0.1))   # 下部过渡
                },
                intensity_mapping=IntensityMapping((-1, 1), (-8, 8), CurveType.LINEAR),
                region=RegionConfig(
                    boundary_points=[418, 419, 420, 421, 422],  # 下颜线轮廓点
                    center_point=420  # 下颜线中心点
                )
            )
        },
        region=RegionConfig(
            boundary_points=[418, 419, 420, 421, 422, 423],  # 下巴完整轮廓
            center_point=420  # 下巴中心点
        )
    )
}

def get_area_config(area_name: str) -> Optional[AreaConfig]:
    """获取区域配置"""
    return FACIAL_AREA_CONFIGS.get(area_name)

def get_parameter_config(area_name: str, param_name: str) -> Optional[ParameterConfig]:
    """获取参数配置"""
    area_config = get_area_config(area_name)
    if area_config:
        return area_config.parameters.get(param_name)
    return None
