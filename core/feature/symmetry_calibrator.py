#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

"""
面部特征点对称性校准工具
基于中心线点进行分区域的对称点校准
"""

import numpy as np
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass
from enum import Enum
from .parameter_mapping import CENTER_LINE_POINTS

class FacialRegion(Enum):
    """面部区域定义"""
    EYES = "eyes"
    EYEBROWS = "eyebrows"
    NOSE = "nose"
    LIPS = "lips"
    FACE_CONTOUR = "face_contour"
    CHEEKS = "cheeks"
    NASOLABIAL = "nasolabial"

class AnatomicalLayer(Enum):
    """解剖学层次"""
    UPPER = "upper"
    MIDDLE = "middle"
    LOWER = "lower"

@dataclass
class SymmetryPair:
    """对称点对"""
    left_point: int
    right_point: int
    region: FacialRegion
    layer: AnatomicalLayer
    confidence: float

@dataclass
class AnatomicalConstraint:
    """解剖学约束"""
    region: FacialRegion
    layer: AnatomicalLayer
    left_points: Set[int]
    right_points: Set[int]
    
# 解剖学约束定义
ANATOMICAL_CONSTRAINTS = [
    # 眉毛约束
    AnatomicalConstraint(
        region=FacialRegion.EYEBROWS,
        layer=AnatomicalLayer.UPPER,
        left_points={63, 105},
        right_points={293, 282}
    ),
    
    # 眼睛约束
    AnatomicalConstraint(
        region=FacialRegion.EYES,
        layer=AnatomicalLayer.UPPER,
        left_points={159, 160, 161},  # 上眼睑
        right_points={385, 386, 387}
    ),
    AnatomicalConstraint(
        region=FacialRegion.EYES,
        layer=AnatomicalLayer.LOWER,
        left_points={157, 158},  # 下眼睑
        right_points={384, 385}
    ),
    
    # 鼻子约束 - 完整定义
    AnatomicalConstraint(
        region=FacialRegion.NOSE,
        layer=AnatomicalLayer.UPPER,
        left_points={193, 417},  # 鼻梁上部
        right_points={413, 441}
    ),
    AnatomicalConstraint(
        region=FacialRegion.NOSE,
        layer=AnatomicalLayer.MIDDLE,
        left_points={166, 239, 20},  # 鼻梁中部
        right_points={392, 419, 238}
    ),
    AnatomicalConstraint(
        region=FacialRegion.NOSE,
        layer=AnatomicalLayer.LOWER,
        left_points={79, 166, 75},  # 鼻翼和鼻孔区域
        right_points={309, 392, 305}
    ),
    
    # 嘴唇约束
    AnatomicalConstraint(
        region=FacialRegion.LIPS,
        layer=AnatomicalLayer.UPPER,
        left_points={37},  # 上唇
        right_points={267}
    ),
    AnatomicalConstraint(
        region=FacialRegion.LIPS,
        layer=AnatomicalLayer.LOWER,
        left_points={40},  # 下唇
        right_points={269}
    )
]

class SymmetryCalibrator:
    """对称性校准器"""
    
    def __init__(self, image_width: int, tolerance_ratio: float = 0.1):
        self.image_width = image_width
        self.tolerance = image_width * tolerance_ratio
        self.center_x = image_width / 2
        
    def _calculate_symmetry_confidence(self, 
                                    left_x: float, 
                                    right_x: float, 
                                    y1: float, 
                                    y2: float,
                                    layer: AnatomicalLayer) -> float:
        """
        计算对称性置信度，考虑解剖学层次
        """
        # 计算到中心线的距离差异
        x_diff = abs((left_x - self.center_x) + (right_x - self.center_x))
        # 计算y坐标差异，根据层次调整容差
        y_diff = abs(y1 - y2)
        
        # 不同层次的容差调整
        layer_tolerance = {
            AnatomicalLayer.UPPER: self.tolerance * 0.8,
            AnatomicalLayer.MIDDLE: self.tolerance,
            AnatomicalLayer.LOWER: self.tolerance * 1.2
        }
        
        adjusted_tolerance = layer_tolerance[layer]
        
        # 基于调整后的容差计算置信度
        x_confidence = max(0, 1 - (x_diff / adjusted_tolerance))
        y_confidence = max(0, 1 - (y_diff / adjusted_tolerance))
        
        return min(x_confidence, y_confidence)
    
    def calibrate_region(self, 
                        landmarks: np.ndarray, 
                        constraints: List[AnatomicalConstraint]) -> List[SymmetryPair]:
        """
        基于解剖学约束校准特定区域的对称点
        """
        pairs = []
        
        for constraint in constraints:
            # 确保左右点集合大小相等
            min_size = min(len(constraint.left_points), len(constraint.right_points))
            left_points = sorted(list(constraint.left_points))[:min_size]
            right_points = sorted(list(constraint.right_points))[:min_size]
            
            for left_idx, right_idx in zip(left_points, right_points):
                left_x, left_y = landmarks[left_idx]
                right_x, right_y = landmarks[right_idx]
                
                # 计算置信度
                confidence = self._calculate_symmetry_confidence(
                    left_x, right_x, left_y, right_y, constraint.layer
                )
                
                if confidence > 0.7:  # 置信度阈值
                    pairs.append(SymmetryPair(
                        left_idx,
                        right_idx,
                        constraint.region,
                        constraint.layer,
                        confidence
                    ))
        
        return pairs
    
    def calibrate_all_regions(self, landmarks: np.ndarray) -> Dict[FacialRegion, List[SymmetryPair]]:
        """
        校准所有区域的对称点
        """
        results = {}
        
        # 按区域分组约束
        constraints_by_region = {}
        for constraint in ANATOMICAL_CONSTRAINTS:
            if constraint.region not in constraints_by_region:
                constraints_by_region[constraint.region] = []
            constraints_by_region[constraint.region].append(constraint)
        
        # 按区域处理
        for region, constraints in constraints_by_region.items():
            results[region] = self.calibrate_region(landmarks, constraints)
        
        return results
