#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
特征点数据管理器
负责特征点数据的参数化组织和管理
"""

import os
import sys
import json
import logging
from typing import Dict, List, Set
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('FeatureDataManager')

class PointRole(Enum):
    """特征点角色"""
    PRIMARY = "primary"      # 主要控制点
    SECONDARY = "secondary"  # 次要控制点
    AUXILIARY = "auxiliary"  # 辅助点

@dataclass
class TransformParameter:
    """变形参数项"""
    param_name: str         # 参数名称
    primary_points: List[int]    # 该参数的主要控制点
    secondary_points: List[int]  # 该参数的次要控制点
    auxiliary_points: List[int]  # 该参数的辅助点
    weight: float = 1.0         # 参数权重

@dataclass
class FeaturePoint:
    """特征点基础数据结构"""
    index: int
    x: float
    y: float
    z: float
    visibility: float
    param_roles: Dict[str, Dict[str, PointRole]] = None  # {area: {param: role}}

    def __post_init__(self):
        if self.param_roles is None:
            self.param_roles = {}

    def add_param_role(self, area_name: str, param_name: str, role: PointRole):
        """添加点在特定区域特定参数中的角色"""
        if area_name not in self.param_roles:
            self.param_roles[area_name] = {}
        self.param_roles[area_name][param_name] = role

    def get_role_for_param(self, area_name: str, param_name: str) -> PointRole:
        """获取点在特定区域特定参数中的角色"""
        return self.param_roles.get(area_name, {}).get(param_name)

@dataclass
class AreaFeatures:
    """区域特征数据结构"""
    area_name: str
    parameters: Dict[str, TransformParameter]  # 该区域的所有变形参数
    points: Dict[int, FeaturePoint]           # 该区域涉及的所有特征点
    center: Dict[str, float]                  # 区域中心点
    boundary: Dict[str, float]                # 区域边界

class FeatureDataManager:
    # 面部区域参数映射表
    FACIAL_AREA_PARAMS = {
        'nose': {
            'bridge_height': TransformParameter(  # 鼻梁高度
                param_name='bridge_height',
                primary_points=[168, 197, 195],  # 鼻梁中心线点
                secondary_points=[5, 4, 45],     # 鼻梁两侧点
                auxiliary_points=[275, 49, 131]  # 周边辅助点
            ),
            'tip_length': TransformParameter(    # 鼻尖长度
                param_name='tip_length',
                primary_points=[1, 2, 98],      # 鼻尖核心点
                secondary_points=[327, 168],     # 鼻尖周边点
                auxiliary_points=[197, 195]      # 鼻梁底部点
            ),
            'width': TransformParameter(         # 鼻子宽度
                param_name='width',
                primary_points=[98, 327],       # 鼻翼主点
                secondary_points=[45, 275],      # 鼻翼外侧点
                auxiliary_points=[168, 197]      # 鼻梁辅助点
            )
        },
        'mouth': {
            'width': TransformParameter(         # 嘴巴宽度
                param_name='width',
                primary_points=[61, 291],       # 嘴角点
                secondary_points=[40, 39],       # 唇线点
                auxiliary_points=[37, 375]       # 辅助点
            ),
            'thickness': TransformParameter(     # 唇厚度
                param_name='thickness',
                primary_points=[0, 17],         # 上下唇中心点
                secondary_points=[267, 269],     # 唇线控制点
                auxiliary_points=[270, 409]      # 唇部边缘点
            )
        }
    }

    def __init__(self):
        """初始化数据管理器"""
        self._cache = {}
        self._last_process_time = None
        logger.info('[数据管理器] 初始化完成')

    def process_feature_data(self, face_mesh_data: Dict) -> Dict:
        """处理face_mesh_processor输出的特征点数据"""
        try:
            logger.info('[数据处理] 开始处理特征点数据')
            
            self._last_process_time = datetime.now()
            
            if face_mesh_data.get('status') != 'success':
                raise ValueError(f"输入数据状态错误: {face_mesh_data.get('status')}")
            
            landmarks = face_mesh_data.get('landmarks', [])
            if not landmarks:
                raise ValueError("未找到特征点数据")
            
            organized_data = self._organize_by_areas(landmarks)
            enhanced_data = self._calculate_area_metrics(organized_data)
            
            cache_key = self._generate_cache_key()
            self._cache[cache_key] = enhanced_data
            
            process_time = (datetime.now() - self._last_process_time).total_seconds() * 1000
            logger.info(f'[数据处理] 完成处理，耗时: {process_time:.2f}ms')
            
            return enhanced_data
            
        except Exception as e:
            logger.error(f'[数据处理] 处理失败: {str(e)}')
            raise

    def _organize_by_areas(self, landmarks: List[Dict]) -> Dict[str, AreaFeatures]:
        """按面部区域和参数组织特征点"""
        logger.info('[数据组织] 开始按区域和参数组织特征点')
        
        organized_data = {}
        
        # 创建所有特征点对象
        point_map = {}
        for landmark in landmarks:
            point = FeaturePoint(
                index=landmark['index'],
                x=landmark['x'],
                y=landmark['y'],
                z=landmark['z'],
                visibility=landmark['visibility']
            )
            point_map[point.index] = point
        
        # 按区域和参数组织点位
        for area_name, params in self.FACIAL_AREA_PARAMS.items():
            area_points = {}
            
            for param_name, param_info in params.items():
                # 处理主要点
                for idx in param_info.primary_points:
                    if idx in point_map:
                        point = point_map[idx]
                        point.add_param_role(area_name, param_name, PointRole.PRIMARY)
                        area_points[idx] = point
                
                # 处理次要点
                for idx in param_info.secondary_points:
                    if idx in point_map:
                        point = point_map[idx]
                        point.add_param_role(area_name, param_name, PointRole.SECONDARY)
                        area_points[idx] = point
                
                # 处理辅助点
                for idx in param_info.auxiliary_points:
                    if idx in point_map:
                        point = point_map[idx]
                        point.add_param_role(area_name, param_name, PointRole.AUXILIARY)
                        area_points[idx] = point
            
            organized_data[area_name] = AreaFeatures(
                area_name=area_name,
                parameters=params,
                points=area_points,
                center={},
                boundary={}
            )
            
            logger.info(f'[数据组织] {area_name} 区域参数统计：')
            for param_name in params:
                primary_count = len([p for p in area_points.values() 
                                   if p.get_role_for_param(area_name, param_name) == PointRole.PRIMARY])
                secondary_count = len([p for p in area_points.values() 
                                    if p.get_role_for_param(area_name, param_name) == PointRole.SECONDARY])
                auxiliary_count = len([p for p in area_points.values() 
                                    if p.get_role_for_param(area_name, param_name) == PointRole.AUXILIARY])
                
                logger.info(f'  - {param_name}: '
                          f'主要点 {primary_count}个, '
                          f'次要点 {secondary_count}个, '
                          f'辅助点 {auxiliary_count}个')
        
        return organized_data

    def _calculate_area_metrics(self, organized_data: Dict[str, AreaFeatures]) -> Dict[str, AreaFeatures]:
        """计算每个区域的中心点和边界"""
        logger.info('[指标计算] 开始计算区域指标')
        
        for area_name, area_data in organized_data.items():
            points = list(area_data.points.values())
            
            # 计算中心点
            center_x = sum(p.x for p in points) / len(points)
            center_y = sum(p.y for p in points) / len(points)
            center_z = sum(p.z for p in points) / len(points)
            
            # 计算边界
            x_coords = [p.x for p in points]
            y_coords = [p.y for p in points]
            z_coords = [p.z for p in points]
            
            area_data.center = {
                'x': center_x,
                'y': center_y,
                'z': center_z
            }
            
            area_data.boundary = {
                'x_min': min(x_coords),
                'x_max': max(x_coords),
                'y_min': min(y_coords),
                'y_max': max(y_coords),
                'z_min': min(z_coords),
                'z_max': max(z_coords)
            }
        
        logger.info('[指标计算] 完成区域指标计算')
        return organized_data

    def _generate_cache_key(self) -> str:
        """生成缓存键"""
        return f"feature_data_{self._last_process_time.strftime('%Y%m%d_%H%M%S_%f')}"

    def get_area_data(self, area_name: str, cache_key: str = None) -> Optional[AreaFeatures]:
        """获取指定区域的特征数据"""
        try:
            if cache_key and cache_key in self._cache:
                data = self._cache[cache_key]
                return data.get(area_name)
            return None
        except Exception as e:
            logger.error(f'[数据获取] 获取区域数据失败: {str(e)}')
            return None

    def clear_cache(self):
        """清除缓存数据"""
        self._cache.clear()
        logger.info('[缓存管理] 缓存已清除')
