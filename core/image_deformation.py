#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像变形处理脚本

用于将特征点变形应用到图像上，支持Delaunay三角剖分和仿射变换
"""

import argparse
import json
import os
import sys
import time
import cv2
import numpy as np
from scipy.spatial import Delaunay


def log(message):
    """输出日志信息"""
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {message}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='图像变形处理')
    parser.add_argument('--input', required=True, help='输入图像路径')
    parser.add_argument('--output', required=True, help='输出图像路径')
    parser.add_argument('--data', required=True, help='变形数据JSON字符串')
    return parser.parse_args()


def load_image(image_path):
    """加载图像"""
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图像文件不存在: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图像: {image_path}")
    
    return image


def prepare_landmarks(landmarks_data, image_shape):
    """准备特征点数据"""
    # 提取原始特征点和变形后的特征点
    src_points = []
    dst_points = []
    
    height, width = image_shape[:2]
    
    for landmark in landmarks_data:
        # 原始坐标（相对坐标转绝对坐标）
        x = landmark['x'] * width
        y = landmark['y'] * height
        src_points.append([x, y])
        
        # 变形后坐标
        dx = landmark.get('dx', 0.0) * width
        dy = landmark.get('dy', 0.0) * height
        dst_points.append([x + dx, y + dy])
    
    return np.array(src_points), np.array(dst_points)


def apply_deformation(image, src_points, dst_points):
    """应用变形"""
    height, width = image.shape[:2]
    
    # 添加边界点以确保整个图像被覆盖
    border_points = [
        [0, 0], [width//2, 0], [width, 0],
        [0, height//2], [width, height//2],
        [0, height], [width//2, height], [width, height]
    ]
    
    # 将边界点添加到特征点中
    src_with_border = np.vstack([src_points, border_points])
    dst_with_border = np.vstack([dst_points, border_points])
    
    # 计算Delaunay三角剖分
    tri = Delaunay(src_with_border)
    
    # 创建输出图像
    output = np.zeros_like(image)
    
    # 对每个三角形应用仿射变换
    for simplex in tri.simplices:
        # 获取三角形的三个顶点
        src_tri = src_with_border[simplex]
        dst_tri = dst_with_border[simplex]
        
        # 计算仿射变换矩阵
        warp_mat = cv2.getAffineTransform(
            np.float32(src_tri), 
            np.float32(dst_tri)
        )
        
        # 创建三角形掩码
        mask = np.zeros((height, width), dtype=np.uint8)
        cv2.fillConvexPoly(mask, np.int32(src_tri), 1)
        
        # 应用仿射变换
        warped = cv2.warpAffine(
            image, 
            warp_mat, 
            (width, height), 
            borderMode=cv2.BORDER_REFLECT
        )
        
        # 将变形后的三角形复制到输出图像
        output = np.where(
            np.stack([mask, mask, mask], axis=2) > 0,
            warped,
            output
        )
    
    return output


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 记录开始时间
        start_time = time.time()
        log(f"开始处理图像变形")
        log(f"输入图像: {args.input}")
        log(f"输出图像: {args.output}")
        
        # 加载图像
        image = load_image(args.input)
        log(f"图像加载成功: {image.shape}")
        
        # 解析变形数据
        try:
            data = json.loads(args.data)
            landmarks = data.get('landmarks', [])
            log(f"变形数据解析成功: {len(landmarks)}个特征点")
        except json.JSONDecodeError as e:
            log(f"变形数据解析失败: {e}")
            print(json.dumps({"status": "error", "message": f"变形数据解析失败: {e}"}))  
            return 1
        
        # 准备特征点数据
        src_points, dst_points = prepare_landmarks(landmarks, image.shape)
        log(f"特征点准备完成: {len(src_points)}个")
        
        # 应用变形
        output = apply_deformation(image, src_points, dst_points)
        log(f"变形应用完成")
        
        # 保存输出图像
        os.makedirs(os.path.dirname(args.output), exist_ok=True)
        cv2.imwrite(args.output, output)
        log(f"输出图像保存成功: {args.output}")
        
        # 记录处理时间
        elapsed_time = time.time() - start_time
        log(f"处理完成，耗时: {elapsed_time:.2f}秒")
        
        # 输出成功信息（JSON格式，供Dart代码解析）
        print(json.dumps({"status": "success", "output_path": args.output}))
        return 0
        
    except Exception as e:
        log(f"处理异常: {e}")
        print(json.dumps({"status": "error", "message": str(e)}))
        return 1


if __name__ == "__main__":
    sys.exit(main())