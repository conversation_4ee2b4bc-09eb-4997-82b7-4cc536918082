#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

"""
特征参数映射模块
定义面部特征变形的参数配置和映射关系
"""

import logging
import logging.config
from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Optional, Tuple, Set, Union
import logging
import logging.config

# 配置日志记录器
logger = logging.getLogger('beautifun.parameter_mapping')

# 日志配置
LOGGING_CONFIG = {
    'version': 1,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(funcName)s - %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'DEBUG',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'class': 'logging.FileHandler',
            'level': 'DEBUG',
            'formatter': 'detailed',
            'filename': 'beautifun.log',
            'mode': 'a',
            'encoding': 'utf-8'
        }
    },
    'loggers': {
        'beautifun': {  # root logger
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': True
        },
        'beautifun.core': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False
        },
        'beautifun.parameter_mapping': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False
        }
    }
}

# 应用日志配置
logging.config.dictConfig(LOGGING_CONFIG)

# 中心线点集合定义
CENTER_LINE_POINTS: Set[int] = {
    10,   # 发际线中心
    151,  # 前额中心
    168,  # 前额下部
    6,    # 鼻梁最高点
    197,  # 鼻梁上部
    195,  # 鼻梁中部
    5,    # 鼻梁下部
    4,    # 鼻尖上方
    1,    # 鼻尖
    2,    # 鼻底中点
    164,  # 人中上部
    0,    # 人中中部
    17,   # 下巴中点
    152   # 下巴底部
}

# 点的角色定义
class PointRole(Enum):
    PRIMARY = "primary"       # 主要控制点
    SECONDARY = "secondary"   # 次要控制点
    AUXILIARY = "auxiliary"   # 辅助点
    CONSTRAINT = "constraint" # 约束点
    SYMMETRIC = "symmetric"   # 对称点

# 变形方向定义
class TransformDirection(Enum):
    HORIZONTAL = "horizontal"   # 水平方向
    VERTICAL = "vertical"       # 垂直方向
    DEPTH = "depth"            # 深度方向
    RADIAL = "radial"          # 径向
    CUSTOM = "custom"          # 自定义方向
    DIAGONAL = "diagonal"      # 对角线

# 曲线类型定义
class CurveType(Enum):
    LINEAR = "linear"           # 线性
    QUADRATIC = "quadratic"    # 二次曲线
    CUBIC = "cubic"            # 三次曲线
    SINE = "sine"              # 正弦曲线
    CUSTOM = "custom"          # 自定义曲线

@dataclass
class TransformVector:
    """变形向量"""
    direction: TransformDirection
    magnitude: float = 1.0
    custom_direction: Optional[Tuple[float, float, float]] = None

@dataclass
class IntensityMapping:
    """强度映射"""
    input_range: Tuple[float, float]
    output_range: Tuple[float, float]
    curve_type: CurveType
    control_points: Optional[List[Tuple[float, float]]] = None

@dataclass
class PointTransformConfig:
    """点变形配置"""
    transform_vector: TransformVector
    weight: float = 1.0
    falloff_radius: float = 50.0
    constraint_strength: float = 1.0

@dataclass
class RegionConfig:
    """区域配置"""
    boundary_points: List[int]
    center_point: Optional[int] = None
    symmetric_pairs: Optional[List[Dict[str, int]]] = None
    contour_lines: Optional[List[List[int]]] = None

@dataclass
class ParameterConfig:
    """参数配置"""
    display_name: str
    primary_points: List[int]
    secondary_points: List[int]
    auxiliary_points: List[int]
    transform_configs: Dict[int, PointTransformConfig]
    intensity_mapping: IntensityMapping
    min_value: float = -1.0
    max_value: float = 1.0
    default_value: float = 0.0
    weight: float = 1.0
    region: Optional[RegionConfig] = None

@dataclass
class AreaConfig:
    """区域配置"""
    display_name: str
    parameters: Dict[str, ParameterConfig]
    region: RegionConfig

# 面部轮廓特征点区域定义
FACE_CONTOUR_REGION = {
    'name': 'face_contour',
    'description': '面部轮廓区域',
    # 点集合定义
    'points': {
        'chin': [152, 175, 148, 149, 150, 151, 377, 378, 379, 380],  # 下巴区域
        'jawline': [67, 297, 109, 338, 145, 146, 147, 374, 375, 376],  # 下颌线区域
        'cheekbone': [216, 436, 123, 352, 50, 280, 207, 427],  # 颧骨区域
    },
    # 对称点对定义
    'symmetric_pairs': [
        # 下颌线对称点对
        {'left': 67, 'right': 297},   # 颌角
        {'left': 109, 'right': 338},  # 面颊中部
        {'left': 145, 'right': 374},  # 下颌线辅助点1
        {'left': 146, 'right': 375},  # 下颌线辅助点2
        {'left': 147, 'right': 376},  # 下颌线辅助点3
        
        # 下巴区域对称点对
        {'left': 148, 'right': 377},  # 下巴辅助点1
        {'left': 149, 'right': 378},  # 下巴辅助点2
        {'left': 150, 'right': 379},  # 下巴辅助点3
        {'left': 151, 'right': 380},  # 下巴辅助点4
        
        # 颧骨区域对称点对
        {'left': 216, 'right': 436},  # 颧骨主点
        {'left': 123, 'right': 352},  # 颧骨内侧
        {'left': 50, 'right': 280},   # 颧骨上部
        {'left': 207, 'right': 427},  # 颧骨外侧
    ],
    # 中心点定义
    'center_points': [152, 175],  # 下巴中心线点
    
    # 参数配置
    'parameters': {
        'contour_tighten': {
            'name': '轮廓收紧',
            'primary_points': [67, 297, 109, 338],  # 主导点：下颌角和面颊中部
            'secondary_points': [152, 175],         # 协同点：下巴区域
            'auxiliary_points': [145, 146, 147, 374, 375, 376],  # 支撑点：下颌线辅助点
            'intensity_range': (0.0, 15.0),
            'default_value': 0.0,
        },
        'chin_adjust': {
            'name': '下巴调整',
            'primary_points': [152],  # 主导点：下巴中心点
            'secondary_points': [175],  # 协同点：下巴上部中心点
            'auxiliary_points': [148, 149, 150, 151, 377, 378, 379, 380],  # 支撑点：下巴两侧
            'intensity_range': (0.0, 18.0),
            'default_value': 0.0,
        },
        'cheekbone_adjust': {
            'name': '颧骨调整',
            'primary_points': [216, 436],  # 主导点：颧骨主点
            'secondary_points': [67, 297], # 协同点：颌角
            'auxiliary_points': [123, 352, 50, 280, 207, 427],  # 支撑点：颧骨周围
            'intensity_range': (0.0, 16.0),
            'default_value': 0.0,
        },
        'face_shape': {
            'name': '脸型优化',
            'primary_points': [152, 67, 297],  # 主导点：下巴和下颌角
            'secondary_points': [109, 338],    # 协同点：下颌线
            'auxiliary_points': [216, 436],    # 支撑点：颧骨区域
            'intensity_range': (0.0, 20.0),
            'default_value': 0.0,
        }
    }
}

# 鼻部参数配置
nose_params = {
    'bridge_height': {  # 鼻梁高度
        'pairs': [],  # 无对称点对
        'centerPoints': [6, 197, 195, 5, 4, 1],  # 鼻梁中心线点
        'auxiliaryPoints': [2, 19, 168],  # 鼻小柱顶点，鼻基底中心点和鼻梁起始点
        'color': (0, 255, 0)
    },
    'tip_adjust': {  # 鼻尖调整
        'pairs': [
            {'left': 114, 'right': 343},  # 鼻尖外侧点对称对
            {'left': 129, 'right': 358},  # 鼻翼点对称对
            {'left': 219, 'right': 439},  # 鼻翼外缘点对称对
        ],
        'centerPoints': [94, 19],  # 鼻尖中心点和鼻基底中心点
        'color': (0, 165, 255)
    },
    'nostril_width': {  # 鼻翼宽度
        'pairs': [
            {'left': 115, 'right': 344},  # 鼻翼内侧点对称对
            {'left': 220, 'right': 440},  # 鼻翼软骨点对称对
            {'left': 79, 'right': 309},   # 鼻孔外缘点对称对
        ],
        'color': (0, 255, 0)
    },
    'base_height': {  # 鼻基抬高
        'pairs': [],  # 无对称点对
        'centerPoints': [168, 2, 164],  # 鼻梁起始点，鼻小柱顶点，鼻基底中线点
        'color': (128, 0, 128)
    }
}

# 鼻部对称点对配置
nose_symmetry_pairs = [
    {'left': 114, 'right': 343},  # 鼻尖外侧点对称对
    {'left': 129, 'right': 358},  # 鼻翼点对称对
    {'left': 219, 'right': 439},  # 鼻翼外缘点对称对
    {'left': 115, 'right': 344},  # 鼻翼内侧点对称对
    {'left': 220, 'right': 440},  # 鼻翼软骨点对称对
    {'left': 79, 'right': 309},   # 鼻孔外缘点对称对
]

# 鼻部中心线点配置
nose_centerline_points = [
    195,  # 鼻梁中心点
    5,    # 鼻梁下部中心点
    164,  # 鼻基底中线点
]

# 眼部对称点对定义
eye_symmetry_pairs = [
    # 上眼睑点对
    {'left': 159, 'right': 386},  # 上眼睑外侧点
    {'left': 158, 'right': 385},  # 上眼睑中外侧点
    {'left': 157, 'right': 384},  # 上眼睑中点
    {'left': 156, 'right': 383},  # 上眼睑中内侧点
    {'left': 155, 'right': 382},  # 上眼睑内侧点
    
    # 下眼睑点对
    {'left': 145, 'right': 374},  # 下眼睑外侧点
    {'left': 144, 'right': 373},  # 下眼睑中外侧点
    {'left': 143, 'right': 372},  # 下眼睑中点
    {'left': 142, 'right': 371},  # 下眼睑中内侧点
    
    # 眼角点对
    {'left': 133, 'right': 362},  # 内眼角点
    {'left': 130, 'right': 359},  # 外眼角点
    
    # 眼袋区域点对
    {'left': 162, 'right': 389},  # 眼袋上缘点
    {'left': 163, 'right': 390},  # 眼袋中心点
]

# 眼部区域定义
EYE_REGION = {
    'name': 'eyes',
    'description': '眼部美化区域',
    # 点集合定义
    'points': {
        'upper_lid': [159, 158, 157, 156, 155, 386, 385, 384, 383, 382],  # 上眼睑
        'lower_lid': [145, 144, 143, 142, 374, 373, 372, 371],  # 下眼睑
        'corners': [133, 130, 362, 359],  # 眼角
        'eye_bag': [162, 163, 389, 390],  # 眼袋区域
    },
    # 对称点对定义
    'symmetric_pairs': eye_symmetry_pairs,
    # 中心点定义
    'center_point': 168,  # 两眼之间的中心点
    
    # 参数配置
    'parameters': {
        'double_fold': {
            'name': '双眼皮',
            'primary_points': [157, 384],  # 左右眼上眼睑中点
            'secondary_points': [156, 158, 383, 385],  # 上眼睑中内侧和中外侧点
            'auxiliary_points': [155, 159, 382, 386],  # 上眼睑内侧和外侧点
            'transform_configs': {
                157: PointTransformConfig(  # 左眼上眼睑中点
                    transform_vector=TransformVector(
                        direction=TransformDirection.VERTICAL,
                        magnitude=0.6,
                    ),
                ),
                384: PointTransformConfig(  # 右眼上眼睑中点
                    transform_vector=TransformVector(
                        direction=TransformDirection.VERTICAL,
                        magnitude=0.6,
                    ),
                ),
            },
            'intensity_mapping': IntensityMapping(
                input_range=(0.0, 1.0),
                output_range=(0.0, 8.0),
                curve_type=CurveType.QUADRATIC,
            ),
        },
        'canthal_tilt': {
            'name': '开眼角',
            'primary_points': [133, 362],  # 左右内眼角点
            'secondary_points': [155, 382],  # 左右上眼睑内侧点
            'auxiliary_points': [],  # 无辅助点
            'transform_configs': {
                133: PointTransformConfig(  # 左眼内眼角
                    transform_vector=TransformVector(
                        direction=TransformDirection.HORIZONTAL,
                        magnitude=-0.5,  # 向内侧移动
                    ),
                ),
                362: PointTransformConfig(  # 右眼内眼角
                    transform_vector=TransformVector(
                        direction=TransformDirection.HORIZONTAL,
                        magnitude=0.5,  # 向内侧移动
                    ),
                ),
            },
            'intensity_mapping': IntensityMapping(
                input_range=(0.0, 1.0),
                output_range=(0.0, 6.0),
                curve_type=CurveType.QUADRATIC,
            ),
        },
        'eye_bag_removal': {
            'name': '去眼袋',
            'primary_points': [162, 389],  # 左右眼袋上缘点
            'secondary_points': [163, 390],  # 左右眼袋中心点
            'auxiliary_points': [],  # 无辅助点
            'transform_configs': {
                162: PointTransformConfig(  # 左眼袋上缘点
                    transform_vector=TransformVector(
                        direction=TransformDirection.DEPTH,
                        magnitude=-0.7,  # 向内凹陷
                    ),
                ),
                389: PointTransformConfig(  # 右眼袋上缘点
                    transform_vector=TransformVector(
                        direction=TransformDirection.DEPTH,
                        magnitude=-0.7,  # 向内凹陷
                    ),
                ),
            },
            'intensity_mapping': IntensityMapping(
                input_range=(0.0, 1.0),
                output_range=(0.0, 10.0),
                curve_type=CurveType.QUADRATIC,
            ),
        },
        'outer_corner_lift': {
            'name': '提眼尾',
            'primary_points': [130, 359],  # 左右外眼角点
            'secondary_points': [145, 374],  # 左右下眼睑外侧点
            'auxiliary_points': [159, 386],  # 左右上眼睑外侧点
            'transform_configs': {
                130: PointTransformConfig(  # 左眼外眼角
                    transform_vector=TransformVector(
                        direction=TransformDirection.CUSTOM,
                        magnitude=0.8,
                        custom_direction=(1.0, 1.0, 0.0),  # 45度角向上外
                    ),
                ),
                359: PointTransformConfig(  # 右眼外眼角
                    transform_vector=TransformVector(
                        direction=TransformDirection.CUSTOM,
                        magnitude=0.8,
                        custom_direction=(-1.0, 1.0, 0.0),  # 135度角向上外
                    ),
                ),
            },
            'intensity_mapping': IntensityMapping(
                input_range=(0.0, 1.0),
                output_range=(0.0, 12.0),
                curve_type=CurveType.QUADRATIC,
            ),
        },
    },
}

# 医美项目区域配置
BEAUTY_AREA_CONFIGS: Dict[str, AreaConfig] = {
    # 1. 面部轮廓
    'face_contour': AreaConfig(
        display_name='面部轮廓',
        parameters={
            'contour_tighten': ParameterConfig(
                display_name='轮廓收紧',
                primary_points=[67, 297, 109, 338],  # 主导点：下颌角和面颊中部
                secondary_points=[152, 175],         # 协同点：下巴区域
                auxiliary_points=[145, 146, 147, 374, 375, 376],  # 支撑点：下颌线辅助点
                transform_configs={
                    67: PointTransformConfig(
                        TransformVector(TransformDirection.HORIZONTAL, -0.8),
                        weight=1.0,
                        falloff_radius=30.0
                    ),
                    297: PointTransformConfig(
                        TransformVector(TransformDirection.HORIZONTAL, 0.8),
                        weight=1.0,
                        falloff_radius=30.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'chin_adjust': ParameterConfig(
                display_name='下巴调整',
                primary_points=[152],  # 主导点：下巴中心点
                secondary_points=[175],  # 协同点：下巴上部中心点
                auxiliary_points=[148, 149, 150, 151, 377, 378, 379, 380],  # 支撑点：下巴两侧
                transform_configs={
                    152: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.6),
                        weight=1.0,
                        falloff_radius=25.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'cheekbone_adjust': ParameterConfig(
                display_name='颧骨调整',
                primary_points=[216, 436],  # 主导点：颧骨主点
                secondary_points=[67, 297], # 协同点：颌角
                auxiliary_points=[123, 352, 50, 280, 207, 427],  # 支撑点：颧骨周围
                transform_configs={
                    216: PointTransformConfig(
                        TransformVector(TransformDirection.HORIZONTAL, -0.6),
                        weight=1.0,
                        falloff_radius=30.0
                    ),
                    436: PointTransformConfig(
                        TransformVector(TransformDirection.HORIZONTAL, 0.6),
                        weight=1.0,
                        falloff_radius=30.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'face_shape': ParameterConfig(
                display_name='脸型优化',
                primary_points=[152, 67, 297],  # 主导点：下巴和下颌角
                secondary_points=[109, 338],    # 协同点：下颌线
                auxiliary_points=[216, 436],    # 支撑点：颧骨区域
                transform_configs={
                    152: PointTransformConfig(
                        TransformVector(TransformDirection.CUSTOM, 0.7),
                        weight=1.0,
                        falloff_radius=35.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            )
        },
        region=RegionConfig(
            boundary_points=[67, 297, 109, 338, 145, 146, 147, 374, 375, 376, 152, 175, 148, 149, 150, 151, 377, 378, 379, 380, 216, 436, 123, 352, 50, 280, 207, 427],
            center_point=152,
            symmetric_pairs=FACE_CONTOUR_REGION['symmetric_pairs'],
            contour_lines=[
                [67, 109, 145],     # 左侧下颌线
                [297, 338, 374],    # 右侧下颌线
                [152, 175, 148],    # 下巴中心线
                [216, 123, 50],     # 左侧颧骨线
                [436, 352, 280]     # 右侧颧骨线
            ]
        )
    ),
    # 2. 鼻部塑形
    'nose': AreaConfig(
        display_name='鼻部塑形',
        parameters={
            'bridge_height': ParameterConfig(
                display_name='鼻梁高度',
                primary_points=[6, 197, 195, 5, 4, 1],  # 鼻梁中心线点
                secondary_points=[2],  # 鼻小柱顶点
                auxiliary_points=[19, 168],  # 鼻基底中心点和鼻梁起始点
                transform_configs={
                    6: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.6),
                        weight=1.0,
                        falloff_radius=20.0
                    ),
                    197: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.6),
                        weight=1.0,
                        falloff_radius=20.0
                    ),
                    195: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.6),
                        weight=1.0,
                        falloff_radius=20.0
                    ),
                    5: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.6),
                        weight=1.0,
                        falloff_radius=20.0
                    ),
                    4: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.6),
                        weight=1.0,
                        falloff_radius=20.0
                    ),
                    1: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.6),
                        weight=1.0,
                        falloff_radius=20.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'tip_adjust': ParameterConfig(
                display_name='鼻尖调整',
                primary_points=[4, 1],  # 鼻尖主点
                secondary_points=[19, 20],  # 鼻尖两侧点
                auxiliary_points=[94],  # 鼻翼底部
                transform_configs={
                    4: PointTransformConfig(
                        TransformVector(TransformDirection.CUSTOM, 0.4),
                        weight=1.0,
                        falloff_radius=15.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'alar_width': ParameterConfig(
                display_name='鼻翼宽度',
                primary_points=[129, 358],  # 鼻翼最宽点
                secondary_points=[94],  # 鼻翼底部
                auxiliary_points=[164, 393],  # 鼻翼外侧
                transform_configs={
                    129: PointTransformConfig(
                        TransformVector(TransformDirection.HORIZONTAL, 0.6),
                        weight=1.0,
                        falloff_radius=20.0
                    ),
                    358: PointTransformConfig(
                        TransformVector(TransformDirection.HORIZONTAL, 0.6),
                        weight=1.0,
                        falloff_radius=20.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'base_height': ParameterConfig(
                display_name='鼻基抬高',
                primary_points=[94],  # 鼻基底点
                secondary_points=[94],  # 鼻基底点
                auxiliary_points=[94],  # 鼻翼底部
                transform_configs={
                    94: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.8),  # 鼻基底垂直变形
                        weight=1.0,
                        falloff_radius=20.0
                    )
                },
                boundary_points=[6, 197, 195, 4, 1, 129, 358, 94],
                symmetric_pairs=[{'left': 129, 'right': 358}],
                contour_lines=[[6, 197, 195, 4, 1], [129, 94], [358, 94]],
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            )
        },
        region=RegionConfig(
            boundary_points=[6, 197, 195, 4, 1, 129, 358, 94],
            center_point=1,  # 鼻尖中心点
            symmetric_pairs=nose_symmetry_pairs,
            contour_lines=[
                [6, 197, 195, 4, 1], 
                [129, 94], 
                [358, 94]
            ]
        )
    ),
    # 3. 眼部美化
    'eyes': AreaConfig(
        display_name='眼部美化',
        parameters={
            'double_fold': ParameterConfig(
                display_name='双眼皮',
                primary_points=[157, 384],  # 左右眼上眼睑中点
                secondary_points=[156, 158, 383, 385],  # 上眼睑中内侧和中外侧点
                auxiliary_points=[155, 159, 382, 386],  # 上眼睑内侧和外侧点
                transform_configs=EYE_REGION['parameters']['double_fold']['transform_configs'],
                intensity_mapping=EYE_REGION['parameters']['double_fold']['intensity_mapping'],
            ),
            'canthal_tilt': ParameterConfig(
                display_name='开眼角',
                primary_points=[133, 362],  # 左右内眼角点
                secondary_points=[155, 382],  # 左右上眼睑内侧点
                auxiliary_points=[],  # 无辅助点
                transform_configs=EYE_REGION['parameters']['canthal_tilt']['transform_configs'],
                intensity_mapping=EYE_REGION['parameters']['canthal_tilt']['intensity_mapping'],
            ),
            'eye_bag_removal': ParameterConfig(
                display_name='去眼袋',
                primary_points=[162, 389],  # 左右眼袋上缘点
                secondary_points=[163, 390],  # 左右眼袋中心点
                auxiliary_points=[],  # 无辅助点
                transform_configs=EYE_REGION['parameters']['eye_bag_removal']['transform_configs'],
                intensity_mapping=EYE_REGION['parameters']['eye_bag_removal']['intensity_mapping'],
            ),
            'outer_corner_lift': ParameterConfig(
                display_name='提眼尾',
                primary_points=[130, 359],  # 左右外眼角点
                secondary_points=[145, 374],  # 左右下眼睑外侧点
                auxiliary_points=[159, 386],  # 左右上眼睑外侧点
                transform_configs=EYE_REGION['parameters']['outer_corner_lift']['transform_configs'],
                intensity_mapping=EYE_REGION['parameters']['outer_corner_lift']['intensity_mapping'],
            ),
        },
        region=RegionConfig(
            boundary_points=[
                # 左眼轮廓点
                159, 158, 157, 156, 155,  # 上眼睑
                133,  # 内眼角
                142, 143, 144, 145,  # 下眼睑
                130,  # 外眼角
                
                # 右眼轮廓点
                386, 385, 384, 383, 382,  # 上眼睑
                362,  # 内眼角
                371, 372, 373, 374,  # 下眼睑
                359,  # 外眼角
            ],
            center_point=168,  # 两眼之间的中心点
            symmetric_pairs=eye_symmetry_pairs,
            contour_lines=[
                # 左眼轮廓线
                [159, 158, 157, 156, 155, 133],  # 上眼睑到内眼角
                [133, 142, 143, 144, 145, 130],  # 内眼角-下眼睑-外眼角
                [130, 159],  # 外眼角到上眼睑外侧
                
                # 右眼轮廓线
                [386, 385, 384, 383, 382, 362],  # 上眼睑到内眼角
                [362, 371, 372, 373, 374, 359],  # 内眼角-下眼睑-外眼角
                [359, 386],  # 外眼角到上眼睑外侧
            ],
        ),
    ),
    # 4. 唇部造型
    'lips': AreaConfig(
        display_name='唇部造型',
        parameters={
            'lip_shape': ParameterConfig(
                display_name='唇形调整',
                primary_points=[37, 0, 267],  # 上唇峰关键点（M形）
                secondary_points=[61, 185, 40, 39, 269, 270, 409, 291],  # 上唇其他轮廓点
                auxiliary_points=[78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308],  # 上唇内轮廓辅助点
                transform_configs={
                    0: PointTransformConfig(  # 人中点
                        TransformVector(TransformDirection.VERTICAL, 0.8),  # 适度上提
                        weight=1.0,
                        falloff_radius=15.0  # 小范围精确控制
                    ),
                    37: PointTransformConfig(  # 左唇峰
                        TransformVector(TransformDirection.DIAGONAL, 0.6, custom_direction=(0.866, 0.5, 0)),  # 120度角
                        weight=0.8,
                        falloff_radius=12.0
                    ),
                    267: PointTransformConfig(  # 右唇峰
                        TransformVector(TransformDirection.DIAGONAL, 0.6, custom_direction=(0.5, 0.866, 0)),  # 60度角
                        weight=0.8,
                        falloff_radius=12.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(0.0, 1.0),
                    output_range=(0.0, 6.0),
                    curve_type=CurveType.QUADRATIC  # 平滑过渡
                )
            ),
            'lip_thickness': ParameterConfig(
                display_name='嘴唇厚度',  # 修改显示名称
                primary_points=[88, 178, 87, 14, 317, 402, 318],  # 上唇中部红唇缘
                secondary_points=[90, 180, 85, 16, 315, 404, 320],  # 下唇中部红唇缘
                auxiliary_points=[76, 77, 307, 306],  # 唇部边缘控制点
                transform_configs={
                    178: PointTransformConfig(  # 上唇中部
                        TransformVector(TransformDirection.VERTICAL, 0.7),  # 适度下移
                        weight=1.0,
                        falloff_radius=20.0
                    ),
                    180: PointTransformConfig(  # 下唇中部
                        TransformVector(TransformDirection.VERTICAL, -0.7),  # 适度上移
                        weight=1.0,
                        falloff_radius=20.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(0.0, 1.0),
                    output_range=(0.0, 5.0),
                    curve_type=CurveType.QUADRATIC
                )
            ),
            'mouth_corner': ParameterConfig(
                display_name='嘴角上扬',
                primary_points=[146, 375],  # 左右嘴角
                secondary_points=[91, 321, 181, 405],  # 下唇外侧控制点
                auxiliary_points=[61, 291, 76, 306],  # 唇部边缘辅助点
                transform_configs={
                    146: PointTransformConfig(  # 左嘴角
                        TransformVector(TransformDirection.DIAGONAL, 0.8, custom_direction=(0.707, 0.707, 0)),  # 45度角
                        weight=1.0,
                        falloff_radius=18.0
                    ),
                    375: PointTransformConfig(  # 右嘴角
                        TransformVector(TransformDirection.DIAGONAL, 0.8, custom_direction=(-0.707, 0.707, 0)),  # 135度角
                        weight=1.0,
                        falloff_radius=18.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(0.0, 1.0),
                    output_range=(0.0, 4.0),
                    curve_type=CurveType.QUADRATIC  # 渐缓曲线
                )
            ),
            'lip_color': ParameterConfig(
                display_name='唇色优化',
                primary_points=[88, 178, 87, 14, 317, 402, 318],  # 上唇中部红唇缘
                secondary_points=[90, 180, 85, 16, 315, 404, 320],  # 下唇中部红唇缘
                auxiliary_points=[76, 77, 307, 306],  # 唇部边缘控制点
                transform_configs={
                    178: PointTransformConfig(  # 上唇中部
                        TransformVector(TransformDirection.DEPTH, 0.6),  # 适度凸显
                        weight=1.0,
                        falloff_radius=25.0  # 较大范围平滑过渡
                    ),
                    180: PointTransformConfig(  # 下唇中部
                        TransformVector(TransformDirection.DEPTH, 0.6),  # 适度凸显
                        weight=1.0,
                        falloff_radius=25.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(0.0, 1.0),
                    output_range=(0.0, 3.0),
                    curve_type=CurveType.LINEAR  # 线性渐变
                )
            )
        },
        region=RegionConfig(
            boundary_points=[
                61, 185, 40, 39, 37, 0, 267, 269, 270, 409, 291,  # 上唇外轮廓
                375, 321, 405, 314, 17, 84, 181, 91, 146          # 下唇外轮廓
            ],
            center_point=0,  # 人中点作为中心参考
            symmetric_pairs=[
                {'left': 61, 'right': 291},   # 上唇外轮廓点对
                {'left': 40, 'right': 270},   # 上唇内轮廓点对
                {'left': 76, 'right': 306},   # 下唇内轮廓点对
                {'left': 87, 'right': 317},   # 下唇外轮廓点对
                {'left': 146, 'right': 375},  # 嘴角点对
                {'left': 91, 'right': 321},   # 唇部外侧控制点对
                {'left': 181, 'right': 405}   # 下唇中部控制点对
            ],
            contour_lines=[
                [61, 185, 40, 39, 37, 0, 267, 269, 270, 409, 291],  # 上唇外轮廓
                [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308], # 上唇内轮廓
                [146, 91, 181, 84, 17, 314, 405, 321, 375],         # 下唇外轮廓
                [76, 77, 90, 180, 85, 16, 315, 404, 320, 307, 306]  # 下唇内轮廓
            ]
        )
    ),
    
    # 5. 抗衰冻龄
    'anti_aging': AreaConfig(
        display_name='抗衰冻龄',
        parameters={
            'skin_tightening': ParameterConfig(
                display_name='面部紧致度',
                primary_points=[123, 352, 147, 376],  # 面部关键点
                secondary_points=[162, 389, 21, 251],  # 面部轮廓点
                auxiliary_points=[54, 284, 103, 333],  # 过渡点
                transform_configs={
                    123: PointTransformConfig(
                        TransformVector(TransformDirection.RADIAL, 0.4),
                        weight=1.0,
                        falloff_radius=40.0
                    ),
                    352: PointTransformConfig(
                        TransformVector(TransformDirection.RADIAL, 0.4),
                        weight=1.0,
                        falloff_radius=40.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'wrinkle_improvement': ParameterConfig(
                display_name='细纹改善',
                primary_points=[133, 362, 130, 359],  # 眼部细纹区域
                secondary_points=[158, 387, 153, 382],  # 颜面细纹区域
                auxiliary_points=[144, 373, 163, 392],  # 过渡区域
                transform_configs={
                    133: PointTransformConfig(
                        TransformVector(TransformDirection.DEPTH, 0.3),
                        weight=1.0,
                        falloff_radius=25.0
                    ),
                    362: PointTransformConfig(
                        TransformVector(TransformDirection.DEPTH, 0.3),
                        weight=1.0,
                        falloff_radius=25.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'face_lifting': ParameterConfig(
                display_name='面部提拉',
                primary_points=[123, 352, 162, 389],  # 面部提拉点
                secondary_points=[147, 376, 187, 411],  # 辅助提拉点
                auxiliary_points=[54, 284, 103, 333],  # 过渡点
                transform_configs={
                    123: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.5),
                        weight=1.0,
                        falloff_radius=35.0
                    ),
                    352: PointTransformConfig(
                        TransformVector(TransformDirection.VERTICAL, 0.5),
                        weight=1.0,
                        falloff_radius=35.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            ),
            'skin_texture': ParameterConfig(
                display_name='肤质细腻',
                primary_points=[147, 376, 187, 411],  # 面部主要区域
                secondary_points=[123, 352, 162, 389],  # 辅助区域
                auxiliary_points=[54, 284, 103, 333],  # 过渡区域
                transform_configs={
                    147: PointTransformConfig(
                        TransformVector(TransformDirection.CUSTOM, 0.4),
                        weight=1.0,
                        falloff_radius=30.0
                    ),
                    376: PointTransformConfig(
                        TransformVector(TransformDirection.CUSTOM, 0.4),
                        weight=1.0,
                        falloff_radius=30.0
                    )
                },
                intensity_mapping=IntensityMapping(
                    input_range=(-1.0, 1.0),
                    output_range=(-1.0, 1.0),
                    curve_type=CurveType.LINEAR
                )
            )
        },
        region=RegionConfig(
            boundary_points=[
                123, 352,  # 面部主要点
                147, 376,  # 面部辅助点
                162, 389,  # 下颜面点
                54, 284,   # 过渡点
                103, 333,  # 边缘点
                133, 362   # 细纹区域点
            ],
            center_point=151,  # 颜面中心点
            symmetric_pairs=[
                {'left': 123, 'right': 352},  # 面部主要点对
                {'left': 147, 'right': 376},  # 面部辅助点对
                {'left': 162, 'right': 389},  # 下颜面点对
                {'left': 54, 'right': 284},   # 过渡点对
                {'left': 103, 'right': 333},  # 边缘点对
                {'left': 133, 'right': 362}   # 细纹区域点对
            ],
            contour_lines=[
                [123, 147, 162],    # 左侧面部线
                [352, 376, 389],    # 右侧面部线
                [54, 103, 133],     # 左侧过渡线
                [284, 333, 362]     # 右侧过渡线
            ]
        )
    )
} 

# 调试日志配置
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s - %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(funcName)s - %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'DEBUG',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'class': 'logging.FileHandler',
            'level': 'DEBUG',
            'formatter': 'detailed',
            'filename': 'beautifun.log',
            'mode': 'a',
            'encoding': 'utf-8'
        }
    },
    'loggers': {
        'beautifun': {  # root logger
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': True
        },
        'beautifun.core': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False
        },
        'beautifun.parameter_mapping': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False
        }
    }
}


def get_points_statistics(config: Union[AreaConfig, ParameterConfig]) -> Dict[str, List[int]]:
    """
    获取配置中的特征点统计信息

    Args:
        config: 区域或参数配置对象

    Returns:
        Dict[str, List[int]]: 包含主要点、次要点、辅助点的字典
    """
    if isinstance(config, AreaConfig):
        # 如果是区域配置，收集所有参数的点位
        primary_points = set()
        secondary_points = set()
        auxiliary_points = set()
        
        for param_config in config.parameters.values():
            primary_points.update(param_config.primary_points)
            secondary_points.update(param_config.secondary_points)
            auxiliary_points.update(param_config.auxiliary_points)
            
        logger.debug('区域特征点统计 - 主要点: %d个, 次要点: %d个, 辅助点: %d个',
                    len(primary_points), len(secondary_points), len(auxiliary_points))
    else:
        # 如果是参数配置，直接使用其点位
        primary_points = set(config.primary_points)
        secondary_points = set(config.secondary_points)
        auxiliary_points = set(config.auxiliary_points)
        
        logger.debug('参数特征点统计 - 主要点: %d个, 次要点: %d个, 辅助点: %d个',
                    len(primary_points), len(secondary_points), len(auxiliary_points))
    
    return {
        'primary': sorted(list(primary_points)),
        'secondary': sorted(list(secondary_points)),
        'auxiliary': sorted(list(auxiliary_points))
    }

def get_area_config(area_name: str) -> Optional[AreaConfig]:
    """
    获取区域配置
    
    Args:
        area_name: 区域名称
        
    Returns:
        Optional[AreaConfig]: 区域配置对象，如果不存在返回None
    """
    logger.debug('获取区域配置: %s', area_name)
    
    area_config = BEAUTY_AREA_CONFIGS.get(area_name)
    if not area_config:
        logger.warning('未找到区域配置: %s', area_name)
        return None
    
    # 获取并记录特征点统计信息
    points_stats = get_points_statistics(area_config)
    logger.info('区域 %s 特征点详情:', area_name)
    logger.info('- 主要点(%d个): %s', len(points_stats['primary']), points_stats['primary'])
    logger.info('- 次要点(%d个): %s', len(points_stats['secondary']), points_stats['secondary'])
    logger.info('- 辅助点(%d个): %s', len(points_stats['auxiliary']), points_stats['auxiliary'])
        
    logger.debug('成功获取区域配置: %s', area_name)
    return area_config

def get_parameter_config(area_name: str, param_name: str) -> Optional[ParameterConfig]:
    """
    获取参数配置
    
    Args:
        area_name: 区域名称
        param_name: 参数名称
        
    Returns:
        Optional[ParameterConfig]: 参数配置对象，如果不存在返回None
    """
    logger.debug('获取参数配置: 区域=%s, 参数=%s', area_name, param_name)
    
    area_config = get_area_config(area_name)
    if not area_config:
        logger.warning('未找到区域配置: %s', area_name)
        return None
        
    param_config = area_config.parameters.get(param_name)
    if not param_config:
        logger.warning('未找到参数配置: 区域=%s, 参数=%s', area_name, param_name)
        return None
    
    # 获取并记录特征点统计信息
    points_stats = get_points_statistics(param_config)
    logger.info('参数 %s (区域: %s) 特征点详情:', param_name, area_name)
    logger.info('- 主要点(%d个): %s', len(points_stats['primary']), points_stats['primary'])
    logger.info('- 次要点(%d个): %s', len(points_stats['secondary']), points_stats['secondary'])
    logger.info('- 辅助点(%d个): %s', len(points_stats['auxiliary']), points_stats['auxiliary'])
        
    logger.debug('成功获取参数配置: 区域=%s, 参数=%s', area_name, param_name)
    return param_config
