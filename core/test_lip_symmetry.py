#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
唇部对称性连接线测试脚本
基于解剖学分层和MediaPipe Face Mesh特征点
"""

import os
import sys
import logging
import cv2
import numpy as np
import mediapipe as mp

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 将当前目录添加到Python路径
sys.path.insert(0, current_dir)

# 导入图像处理管道
from image_pipeline import preprocess_image

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('LipSymmetryTest')

# 唇部解剖学中心线点（从上到下）
LIP_CENTER_LINES = {
    'upper_lip': [
        0,    # 人中中点
        17,   # 上唇珠中心
        13,   # 上唇中点
        14,   # 唇缘中点
        15,   # 下唇中点
        16    # 下颏中点
    ]
}

# 解剖学复合体定义
ANATOMICAL_COMPLEXES = {
    # 唇红缘复合体 - 定义唇部轮廓和手术锚点
    'vermilion_complex': [
        (61, 291, 1.0),   # 上唇红缘外侧点（手术锚点）
        (40, 270, 1.0),   # 上唇红缘中间点（M点）
        (37, 267, 1.0),   # 上唇珠两侧点（唇峰点）
        (84, 314, 1.0),   # 下唇红缘外侧点（过渡区）
        (88, 318, 1.0),   # 下唇中部点（唇下点）
        (78, 308, 0.8),   # 下唇侧部过渡点
        (81, 311, 0.8),   # 下唇外侧点
    ],
    
    # 唇弓复合体 - 控制上唇弓形态
    'bow_complex': [
        (0, 17, 1.0),     # 上唇珠中心点（唇弓顶点）
        (37, 267, 1.0),   # 上唇珠两侧点（唇弓峰点）
        (40, 270, 1.0),   # 上唇红缘中点（唇弓基底）
        (38, 268, 0.8),   # 上唇弓过渡点
    ],
    
    # 口角复合体 - 定义口角和微笑线
    'commissure_complex': [
        (61, 291, 1.0),   # 口角核心点（手术锚点）
        (76, 306, 0.8),   # 口角延伸点（微笑线起点）
        (87, 317, 0.7),   # 微笑线延伸点
    ],
    
    # 唇珠复合体 - 控制上唇中部形态
    'tubercle_complex': [
        (0, 17, 1.0),     # 唇珠中心点（美学中心）
        (39, 269, 1.0),   # 唇珠侧点（形态控制点）
        (40, 270, 0.8),   # 唇珠过渡点
    ]
}

# 构建对称点对列表
LIP_SYMMETRIC_PAIRS = []
for complex_name, points in ANATOMICAL_COMPLEXES.items():
    LIP_SYMMETRIC_PAIRS.extend(points)

def draw_lip_symmetry_lines(image_path):
    """绘制唇部对称连接线
    
    Args:
        image_path: 测试图像路径
    """
    logger.info(f"开始绘制唇部对称连接线 | 图像路径: {image_path}")
    
    try:
        # 调用图像预处理管道
        logger.info("调用image_pipeline进行图像预处理")
        image_rgb = preprocess_image(image_path)  # 已经是RGB格式
        height, width = image_rgb.shape[:2]
        
        # 创建可视化图像（转换为BGR格式）
        vis_image = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2BGR)
        
        # 初始化MediaPipe Face Mesh
        mp_face_mesh = mp.solutions.face_mesh
        with mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        ) as face_mesh:
            # 处理图像
            logger.info("开始MediaPipe Face Mesh处理")
            results = face_mesh.process(image_rgb)
            logger.info("MediaPipe Face Mesh处理完成")
            
            if not results.multi_face_landmarks:
                logger.error("未检测到人脸")
                return None
            
            face_landmarks = results.multi_face_landmarks[0]
            
            # 绘制中心线
            logger.info("绘制唇部中心线")
            for i in range(len(LIP_CENTER_LINES['upper_lip']) - 1):
                start_idx = LIP_CENTER_LINES['upper_lip'][i]
                end_idx = LIP_CENTER_LINES['upper_lip'][i + 1]
                
                start_point = face_landmarks.landmark[start_idx]
                start_x = int(start_point.x * width)
                start_y = int(start_point.y * height)
                
                end_point = face_landmarks.landmark[end_idx]
                end_x = int(end_point.x * width)
                end_y = int(end_point.y * height)
                
                # 绘制中心线（红色，BGR格式）
                cv2.line(vis_image, (start_x, start_y), (end_x, end_y), 
                        (255, 0, 0), 2)
                cv2.circle(vis_image, (start_x, start_y), 3, (255, 0, 0), -1)
            
            # 绘制对称点连接线
            logger.info("绘制对称点连接线")
            for left_idx, right_idx, weight in LIP_SYMMETRIC_PAIRS:
                # 获取左侧点
                left_point = face_landmarks.landmark[left_idx]
                left_x = int(left_point.x * width)
                left_y = int(left_point.y * height)
                
                # 获取右侧点
                right_point = face_landmarks.landmark[right_idx]
                right_x = int(right_point.x * width)
                right_y = int(right_point.y * height)
                
                # 根据解剖学复合体设置不同的颜色（BGR格式）
                complex_colors = {
                    'vermilion_complex': (0, 255, 0),    # 绿色 (BGR)
                    'bow_complex': (255, 0, 0),        # 红色 (BGR)
                    'commissure_complex': (0, 255, 255),  # 黄色 (BGR)
                    'tubercle_complex': (128, 0, 128)    # 紫色 (BGR)
                }
                
                # 确定当前点对所属的复合体
                current_complex = None
                for complex_name, points in ANATOMICAL_COMPLEXES.items():
                    if (left_idx, right_idx, weight) in points:
                        current_complex = complex_name
                        break
                
                # 设置线条颜色和线条宽度
                color = complex_colors.get(current_complex, (0, 255, 0))
                line_width = max(1, int(2 * weight))  # 根据权重调整线条宽度
                
                # 绘制对称连接线
                cv2.line(vis_image, (left_x, left_y), (right_x, right_y), 
                        color, line_width)
                
                # 绘制特征点
                cv2.circle(vis_image, (left_x, left_y), 3, color, -1)
                cv2.circle(vis_image, (right_x, right_y), 3, color, -1)
                
                # 只为重要的解剖学点添加标签
                if weight >= 1.0:  # 只显示权重为1.0的点的标签
                    # 设置标签位置
                    left_label_x = left_x - 10 if left_x > width/2 else left_x + 3
                    left_label_y = left_y - 3 if left_y > height/2 else left_y + 10
                    right_label_x = right_x - 10 if right_x > width/2 else right_x + 3
                    right_label_y = right_y - 3 if right_y > height/2 else right_y + 10
                    
                    # 绘制标签
                    cv2.putText(vis_image, str(left_idx), (left_label_x, left_label_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 0), 2)
                    cv2.putText(vis_image, str(left_idx), (left_label_x, left_label_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
                    cv2.putText(vis_image, str(right_idx), (right_label_x, right_label_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 0), 2)
                    cv2.putText(vis_image, str(right_idx), (right_label_x, right_label_y),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
            
            # 保存结果
            output_dir = os.path.join(os.path.dirname(image_path), 'output')
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, 'lip_symmetry_lines_test.jpg')
            cv2.imwrite(output_path, vis_image)
            
            logger.info(f"唇部对称连接线绘制完成 | 输出路径: {output_path}")
            
            return output_path
            
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise

def main():
    """主函数"""
    # 使用标准测试图片
    test_image = "/Users/<USER>/beautifun/testdata/images/face_samples/test_face.jpg"
    
    try:
        output_path = draw_lip_symmetry_lines(test_image)
        if output_path:
            logger.info(f"测试成功完成，结果保存在: {output_path}")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
