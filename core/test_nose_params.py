#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
鼻部塑形参数测试程序
用于验证鼻部特征点的定位、对称性和变形效果
"""

import os
import sys
import cv2
import numpy as np
from feature_points_validator import FeaturePointsValidator
from face_mesh_processor import get_all_landmarks

def draw_nose_params(image_path: str, output_path: str):
    """绘制鼻部四个参数项的特征点分析图
    
    Args:
        image_path: 输入图片路径
        output_path: 输出图片路径
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 鼻部参数配置
    nose_params = {
        'bridge_height': {  # 鼻梁高度
            'pairs': [
            ],
            'centerPoints': [168, 6, 197, 195, 5, 4, 1],  # 鼻梁中心线点
            'color': (0, 255, 0)  # 绿色
        },
        'tip_adjust': {  # 鼻尖调整
            'pairs': [
                {'left': 114, 'right': 343},  # 鼻尖外侧点对称对
                {'left': 129, 'right': 358},  # 鼻翼点对称对
                {'left': 219, 'right': 439},  # 鼻翼外缘点对称对
            ],
            'centerPoints': [94, 19],  # 鼻尖中心点和鼻基底中心点
            'color': (0, 165, 255)  # 橙色
        },
        'nostril_width': {  # 鼻翼宽度
            'pairs': [
                {'left': 115, 'right': 344},  # 鼻翼内侧点对称对
                {'left': 220, 'right': 440},  # 鼻翼软骨点对称对
                {'left': 79, 'right': 309},   # 鼻孔外缘点对称对
            ],
            'color': (0, 255, 0)  # 绿色
        },
        'base_height': {  # 鼻基抬高
            'pairs': [
            ],
            'centerPoints': [168, 2],  # 鼻梁起始点和鼻小柱顶点
            'color': (128, 0, 128)  # 紫色
        }
    }
    
    # 3. 计算面部中心线
    center_x = 664  # 根据实际情况调整
    height = img.shape[0]
    
    # 4. 绘制面部中心线
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 5. 绘制中心点
    for param_name, param in nose_params.items():
        if 'centerPoints' in param:
            for point_id in param['centerPoints']:
                if point_id in landmarks:
                    x, y = landmarks[point_id]
                    cv2.circle(img, (int(x), int(y)), 4, (0, 255, 255), -1)  # 黄色
                    cv2.putText(img, str(point_id), (int(x), int(y)-5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 6. 按参数项绘制对称点对和连线
    for param_name, param in nose_params.items():
        for pair in param['pairs']:
            left_id = pair['left']
            right_id = pair['right']
            if left_id in landmarks and right_id in landmarks:
                left_point = landmarks[left_id]
                right_point = landmarks[right_id]
                
                # 绘制特征点
                cv2.circle(img, (int(left_point[0]), int(left_point[1])), 3, 
                          param['color'], -1)
                cv2.circle(img, (int(right_point[0]), int(right_point[1])), 3, 
                          param['color'], -1)
                
                # 添加点ID标注
                cv2.putText(img, str(left_id), 
                          (int(left_point[0]), int(left_point[1])-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                cv2.putText(img, str(right_id), 
                          (int(right_point[0]), int(right_point[1])-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # 绘制对称连线
                cv2.line(img, 
                        (int(left_point[0]), int(left_point[1])),
                        (int(right_point[0]), int(right_point[1])),
                        param['color'], 1)
                
                # 绘制连线中点
                mid_x = int((left_point[0] + right_point[0]) / 2)
                mid_y = int((left_point[1] + right_point[1]) / 2)
                cv2.circle(img, (mid_x, mid_y), 2, (0, 255, 255), -1)
    
    # 7. 添加图例
    legend_y = 30
    cv2.putText(img, "图例:", (10, legend_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 中心线和中心点
    cv2.line(img, (10, legend_y+30), (60, legend_y+30), (0, 255, 255), 2)
    cv2.putText(img, "中心线和中心点", (70, legend_y+35), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 各参数项图例
    param_colors = {
        "鼻梁高度": (0, 255, 0),
        "鼻尖调整": (0, 165, 255),
        "鼻翼宽度": (0, 255, 0),
        "鼻基抬高": (128, 0, 128)
    }
    
    for i, (name, color) in enumerate(param_colors.items()):
        y_offset = legend_y + 60 + i * 30
        cv2.circle(img, (20, y_offset), 3, color, -1)
        cv2.line(img, (35, y_offset), (60, y_offset), color, 1)
        cv2.putText(img, name, (70, y_offset+5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 8. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存参数项分析图到: {output_path}")

def draw_param_points(image_path: str, param_name: str, points_config: dict, output_path: str):
    """绘制参数的特征点分析图
    
    颜色标记：
    - 红色：主导点
    - 绿色：协同点
    - 蓝色：支撑点
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 绘制特征点
    # 主导点 - 红色
    if 'centerPoints' in points_config:
        for point_id in points_config['centerPoints']:
            if point_id in landmarks:
                x, y = landmarks[point_id]
                cv2.circle(img, (int(x), int(y)), 5, (0, 0, 255), -1)  # 红色
                cv2.putText(img, f"{point_id}(主导)", (int(x), int(y)-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    
    # 协同点 - 绿色
    for pair in points_config['pairs']:
        left_id = pair['left']
        right_id = pair['right']
        for point_id in [left_id, right_id]:
            if point_id in landmarks:
                x, y = landmarks[point_id]
                cv2.circle(img, (int(x), int(y)), 5, (0, 255, 0), -1)  # 绿色
                cv2.putText(img, f"{point_id}(协同)", (int(x), int(y)-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 3. 绘制中心线
    center_x = 664  # 根据实际情况调整
    height = img.shape[0]
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 4. 绘制对称连线
    for pair in points_config['pairs']:
        left_id = pair['left']
        right_id = pair['right']
        if left_id in landmarks and right_id in landmarks:
            left_point = landmarks[left_id]
            right_point = landmarks[right_id]
            
            # 绘制连线
            cv2.line(img, 
                    (int(left_point[0]), int(left_point[1])),
                    (int(right_point[0]), int(right_point[1])),
                    points_config['color'], 1)
            
            # 绘制连线中点
            mid_x = int((left_point[0] + right_point[0]) / 2)
            mid_y = int((left_point[1] + right_point[1]) / 2)
            cv2.circle(img, (mid_x, mid_y), 2, (0, 255, 255), -1)
    
    # 5. 添加图例
    legend_y = 30
    cv2.putText(img, f"{param_name}特征点分布:", (10, legend_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 点类型图例
    point_types = {
        "主导点": (0, 0, 255),
        "协同点": (0, 255, 0)
    }
    
    for i, (name, color) in enumerate(point_types.items()):
        y_offset = legend_y + 30 + i * 25
        cv2.circle(img, (20, y_offset), 5, color, -1)
        cv2.putText(img, name, (35, y_offset+5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 6. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存{param_name}特征点分析图到: {output_path}")

def validate_nose_params(image_path: str):
    """验证鼻部参数项的特征点配置"""
    # 1. 获取特征点
    landmarks = get_all_landmarks(image_path)
    validator = FeaturePointsValidator(landmarks)
    
    # 2. 鼻部参数配置
    nose_params = {
        'bridge_height': {
            'centerPoints': [6, 197, 195, 5],
            'symmetricPairs': [],  
        },
        'tip_adjust': {
            'centerPoints': [94, 19],
            'symmetricPairs': [
                {'left': 114, 'right': 343},
                {'left': 129, 'right': 358},
                {'left': 219, 'right': 439},
            ],
        },
        'nostril_width': {
            'centerPoints': [],
            'symmetricPairs': [
                {'left': 115, 'right': 344},
                {'left': 220, 'right': 440},
                {'left': 79, 'right': 309},
            ],
        },
        'base_height': {
            'centerPoints': [168, 2, 164],  
            'symmetricPairs': [],  
        },
    }
    
    # 3. 验证中心点
    print("\n=== 鼻部参数项特征点对称性验证 ===\n")
    print("中心点验证:")
    for param_name, param in nose_params.items():
        if 'centerPoints' in param:
            for point_id in param['centerPoints']:
                if point_id in landmarks:
                    deviation = validator.calculate_center_deviation(point_id)
                    print(f"点 {point_id}:")
                    print(f"  - 距离中心线偏差: {deviation:.4f}")
                    if deviation > 5:  
                        print("  警告：中心点偏离中心线过大")
    
    # 4. 验证对称点对
    print("\n对称点对验证:")
    for param_name, param in nose_params.items():
        if 'symmetricPairs' in param:
            for pair in param['symmetricPairs']:
                left_id = pair['left']
                right_id = pair['right']
                
                if left_id in landmarks and right_id in landmarks:
                    symmetry_score = validator.validate_point_symmetry(left_id, right_id)
                    distance = validator.calculate_point_distance(left_id, right_id)
                    print(f"点对 {left_id}-{right_id}:")
                    print(f"  - 对称性得分: {symmetry_score:.3f}")
                    print(f"  - 点间距离: {distance:.2f}像素")
                    
                    if symmetry_score < 0.85:
                        print("  警告：对称性得分偏低")
                    if distance > 200:
                        print("  警告：点间距离过大")
                print()

def validate_nose_complex_systems(image_path: str):
    """验证鼻部复合体系统的特征点配置"""
    print("\n=== 鼻部复合体系统验证 ===\n")
    
    # 获取特征点
    landmarks = get_all_landmarks(image_path)
    validator = FeaturePointsValidator(landmarks)
    
    # 鼻梁复合体验证
    print("\n鼻梁复合体:")
    
    # 主导点验证
    print("\n主导点验证:")
    midline_points = [6, 197, 195, 5, 4, 1]  
    for point_id in midline_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 3:  
                print("  警告：主导点偏离中心线过大")
    
    # 次级点验证
    print("\n次级点验证:")
    secondary_points = [2]  
    for point_id in secondary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 5:
                print("  警告：次级点偏离中心线过大")
    
    # 辅助点验证
    print("\n辅助点验证:")
    auxiliary_points = [19, 168]  
    for point_id in auxiliary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 5:
                print("  警告：辅助点偏离中心线过大")
    
    # 鼻尖复合体验证
    print("\n鼻尖复合体:")
    
    # 主导点验证
    print("\n主导点验证:")
    tip_primary_points = [94, 19]  
    for point_id in tip_primary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 5:
                print("  警告：主导点偏离中心线过大")
    
    # 次级点验证
    print("\n次级点验证:")
    tip_secondary_points = [114, 343]  
    for point_id in tip_secondary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 10:
                print("  警告：次级点偏离中心线过大")
    
    # 辅助点验证
    print("\n辅助点验证:")
    tip_auxiliary_points = [129, 358, 219, 439]  
    for point_id in tip_auxiliary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 10:
                print("  警告：辅助点偏离中心线过大")
    
    # 对称点对验证
    print("\n对称点对验证:")
    tip_point_pairs = [
        {'left': 114, 'right': 343},  
        {'left': 129, 'right': 358},  
        {'left': 219, 'right': 439},  
    ]
    validate_point_pairs(validator, tip_point_pairs)
    
    # 鼻翼复合体验证
    print("\n鼻翼复合体:")
    
    # 主导点验证
    print("\n主导点验证:")
    alar_primary_points = [115, 344]  
    for point_id in alar_primary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 10:
                print("  警告：主导点偏离中心线过大")
    
    # 次级点验证
    print("\n次级点验证:")
    alar_secondary_points = [220, 440]  
    for point_id in alar_secondary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 10:
                print("  警告：次级点偏离中心线过大")
    
    # 辅助点验证
    print("\n辅助点验证:")
    alar_auxiliary_points = [79, 309]  
    for point_id in alar_auxiliary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 10:
                print("  警告：辅助点偏离中心线过大")
    
    # 对称点对验证
    print("\n对称点对验证:")
    alar_point_pairs = [
        {'left': 115, 'right': 344},  
        {'left': 220, 'right': 440},  
        {'left': 79, 'right': 309},   
    ]
    validate_point_pairs(validator, alar_point_pairs)
    
    # 鼻基底复合体验证
    print("\n鼻基底复合体:")
    
    # 主导点验证
    print("\n主导点验证:")
    base_primary_points = [168, 2]  
    for point_id in base_primary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 5:
                print("  警告：主导点偏离中心线过大")
    
    # 次级点验证
    print("\n次级点验证:")
    base_secondary_points = [164]  
    for point_id in base_secondary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 5:
                print("  警告：次级点偏离中心线过大")
    
    # 辅助点验证
    print("\n辅助点验证:")
    base_auxiliary_points = []  
    for point_id in base_auxiliary_points:
        if point_id in landmarks:
            deviation = validator.calculate_center_deviation(point_id)
            print(f"点 {point_id}:")
            print(f"  - 距离中心线偏差: {deviation:.4f}")
            if deviation > 5:
                print("  警告：辅助点偏离中心线过大")

def validate_point_pairs(validator: FeaturePointsValidator, point_pairs: list):
    """验证特征点对的对称性
    
    Args:
        validator: 特征点验证器实例
        point_pairs: 特征点对列表，每个点对为包含left和right键的字典
    """
    for pair in point_pairs:
        left_point = pair['left']
        right_point = pair['right']
        if left_point in validator.landmarks and right_point in validator.landmarks:
            symmetry_score = validator.validate_point_symmetry(left_point, right_point)
            distance = validator.calculate_point_distance(left_point, right_point)
            print(f"点对 {left_point}-{right_point}:")
            print(f"  - 对称性得分: {symmetry_score:.3f}")
            print(f"  - 点间距离: {distance:.2f}像素")
            if distance > 200:
                print("  警告：点间距离过大")

def visualize_specific_points(image_path: str, points: list, output_path: str):
    """可视化指定特征点的位置
    
    Args:
        image_path: 输入图片路径
        points: 要显示的特征点ID列表
        output_path: 输出图片路径
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 绘制中心线
    center_x = 664  # 根据实际情况调整
    height = img.shape[0]
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 3. 绘制特征点
    for point_id in points:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            # 绘制大一点的圆圈以便清晰查看
            cv2.circle(img, (int(x), int(y)), 5, (0, 0, 255), -1)
            # 添加点ID标注，位置稍微偏移以便清晰显示
            cv2.putText(img, str(point_id), (int(x)+10, int(y)-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 4. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存特征点位置分析图到: {output_path}")

def main():
    image_path = "testdata/test_face.jpg"
    
    # 1. 鼻部参数配置
    nose_params = {
        'bridge_height': {  # 鼻梁高度
            'pairs': [
            ],
            'centerPoints': [168, 6, 197, 195, 5, 4, 1],  
            'color': (0, 255, 0)  # 绿色
        },
        'tip_adjust': {  # 鼻尖调整
            'pairs': [
                {'left': 114, 'right': 343},  
                {'left': 129, 'right': 358},  
                {'left': 219, 'right': 439},  
            ],
            'centerPoints': [94, 19],  
            'color': (0, 165, 255)  # 橙色
        },
        'nostril_width': {  # 鼻翼宽度
            'pairs': [
                {'left': 115, 'right': 344},  
                {'left': 220, 'right': 440},  
                {'left': 79, 'right': 309},   
            ],
            'color': (0, 255, 0)  # 绿色
        },
        'base_height': {  # 鼻基抬高
            'pairs': [
            ],
            'centerPoints': [168, 2],  
            'color': (128, 0, 128)  # 紫色
        }
    }
    
    # 2. 为每个参数项生成单独的分析图
    param_names = {
        'bridge_height': '鼻梁高度',
        'tip_adjust': '鼻尖调整',
        'nostril_width': '鼻翼宽度',
        'base_height': '鼻基抬高'
    }
    
    for param_id, param_name in param_names.items():
        output_path = f"testdata/nose_{param_id}_points.jpg"
        draw_param_points(image_path, param_name, nose_params[param_id], output_path)
    
    # 3. 绘制组合分析图
    draw_nose_params(image_path, "testdata/nose_params_analysis.jpg")
    
    # 4. 验证参数项特征点
    validate_nose_params(image_path)
    
    # 5. 校准复合体系统特征点
    validate_nose_complex_systems(image_path)
    
    # 6. 分析特定点的位置
    points_to_analyze = [
        195, 5,    
        164,       
        168, 2     
    ]
    visualize_specific_points(image_path, points_to_analyze, 
                            "testdata/nose_points_analysis.jpg")

if __name__ == "__main__":
    main()
