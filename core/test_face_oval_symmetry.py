#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
面部轮廓对称性验证脚本
用于验证面部轮廓特征点的对称性
"""

import os
import sys
import logging
import cv2
import numpy as np
import mediapipe as mp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('FaceOvalSymmetryTest')

# 面部轮廓点定义
FACE_OVAL = [(10, 338), (338, 297), (297, 332), (332, 284), (284, 251), 
             (251, 389), (389, 356), (356, 454), (454, 323), (323, 361),
             (361, 288), (288, 397), (397, 365), (365, 379), (379, 378),
             (378, 400), (400, 377), (377, 152), (152, 148), (148, 176),
             (176, 149), (149, 150), (150, 136), (136, 172), (172, 58),
             (58, 132), (132, 93), (93, 234), (234, 127), (127, 162),
             (162, 21), (21, 54), (54, 103), (103, 67), (67, 109),
             (109, 10)]

def test_face_oval_symmetry(image_path):
    """测试面部轮廓对称性
    
    Args:
        image_path: 测试图像路径
    """
    logger.info(f"开始测试面部轮廓对称性 | 图像路径: {image_path}")
    
    try:
        # 初始化MediaPipe Face Mesh
        mp_face_mesh = mp.solutions.face_mesh
        face_mesh = mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.7
        )
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise Exception("无法读取图像")
            
        # 转换为RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        height, width = image_rgb.shape[:2]
        
        # 检测特征点
        results = face_mesh.process(image_rgb)
        
        if not results.multi_face_landmarks:
            raise Exception("未检测到人脸")
            
        # 获取第一个人脸的特征点
        face_landmarks = results.multi_face_landmarks[0]
        
        # 创建可视化图像
        vis_image = image.copy()
        
        # 绘制所有面部轮廓点
        for connection in FACE_OVAL:
            start_idx = connection[0]
            end_idx = connection[1]
            
            # 获取起点坐标
            start_point = face_landmarks.landmark[start_idx]
            start_x = int(start_point.x * width)
            start_y = int(start_point.y * height)
            
            # 获取终点坐标
            end_point = face_landmarks.landmark[end_idx]
            end_x = int(end_point.x * width)
            end_y = int(end_point.y * height)
            
            # 绘制连接线
            cv2.line(vis_image, (start_x, start_y), (end_x, end_y), (0, 255, 0), 2)
            
            # 绘制特征点
            cv2.circle(vis_image, (start_x, start_y), 3, (255, 0, 0), -1)
            cv2.circle(vis_image, (end_x, end_y), 3, (255, 0, 0), -1)
            
            # 添加点的索引标签
            cv2.putText(vis_image, str(start_idx), (start_x, start_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1)
            cv2.putText(vis_image, str(end_idx), (end_x, end_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1)
        
        # 保存结果
        output_dir = os.path.join(os.path.dirname(image_path), 'output')
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, 'face_oval_symmetry_test.jpg')
        cv2.imwrite(output_path, vis_image)
        
        logger.info(f"面部轮廓对称性测试完成 | 输出路径: {output_path}")
        
        return output_path
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise

def main():
    """主函数"""
    # 使用标准测试图片
    test_image = "/Users/<USER>/beautifun/testdata/images/face_samples/test_face.jpg"
    
    try:
        output_path = test_face_oval_symmetry(test_image)
        logger.info(f"测试成功完成，结果保存在: {output_path}")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
