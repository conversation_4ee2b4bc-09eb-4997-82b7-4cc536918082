#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
眼部美化参数测试程序
用于验证眼部特征点的定位、对称性和变形效果
"""

import os
import sys
import cv2
import numpy as np
from feature_points_validator import FeaturePointsValidator
from face_mesh_processor import get_all_landmarks

def draw_eye_params(image_path: str, output_path: str):
    """绘制眼部四个参数项的特征点分析图
    
    Args:
        image_path: 输入图片路径
        output_path: 输出图片路径
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 眼部参数配置
    eye_params = {
        'double_fold': {  # 双眼皮
            'pairs': [
                {'left': 159, 'right': 386},  # 上眼睑外侧点
                {'left': 158, 'right': 385},  # 上眼睑中外侧点
                {'left': 156, 'right': 383},  # 上眼睑中内侧点
                {'left': 155, 'right': 382},  # 上眼睑内侧点
            ],
            'centerPoints': [157, 384],  # 左右眼上眼睑中点
            'color': (0, 255, 0)  # 绿色
        },
        'canthal_tilt': {  # 开眼角
            'pairs': [
                {'left': 133, 'right': 362},  # 内眼角点
                {'left': 155, 'right': 382},  # 上眼睑内侧点
            ],
            'centerPoints': [133, 362],  # 左右内眼角点
            'color': (0, 165, 255)  # 橙色
        },
        'eye_bag_removal': {  # 去眼袋
            'pairs': [
                {'left': 162, 'right': 389},  # 眼袋上缘点对
            ],
            'centerPoints': [162, 389],  # 使用眼袋上缘点对作为主导点
            'color': (0, 255, 0)  # 绿色
        },
        'outer_corner_lift': {  # 提眼尾
            'pairs': [
                {'left': 130, 'right': 359},  # 外眼角点
                {'left': 145, 'right': 374},  # 下眼睑外侧点
                {'left': 159, 'right': 386},  # 上眼睑外侧点
            ],
            'centerPoints': [130, 359],  # 左右外眼角点
            'color': (128, 0, 128)  # 紫色
        }
    }
    
    # 3. 计算面部中心线
    center_x = 664  # 根据实际情况调整
    height = img.shape[0]
    
    # 4. 绘制面部中心线
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 5. 绘制中心点
    for param_name, param in eye_params.items():
        if 'centerPoints' in param:
            for point_id in param['centerPoints']:
                if point_id in landmarks:
                    x, y = landmarks[point_id]
                    cv2.circle(img, (int(x), int(y)), 4, (0, 255, 255), -1)  # 黄色
                    cv2.putText(img, str(point_id), (int(x), int(y)-5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 6. 按参数项绘制对称点对和连线
    for param_name, param in eye_params.items():
        for pair in param['pairs']:
            left_id = pair['left']
            right_id = pair['right']
            if left_id in landmarks and right_id in landmarks:
                left_point = landmarks[left_id]
                right_point = landmarks[right_id]
                
                # 绘制特征点
                cv2.circle(img, (int(left_point[0]), int(left_point[1])), 3, 
                          param['color'], -1)
                cv2.circle(img, (int(right_point[0]), int(right_point[1])), 3, 
                          param['color'], -1)
                
                # 添加点ID标注
                cv2.putText(img, str(left_id), 
                          (int(left_point[0]), int(left_point[1])-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                cv2.putText(img, str(right_id), 
                          (int(right_point[0]), int(right_point[1])-5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # 绘制对称连线
                cv2.line(img, 
                        (int(left_point[0]), int(left_point[1])),
                        (int(right_point[0]), int(right_point[1])),
                        param['color'], 1)
                
                # 绘制连线中点
                mid_x = int((left_point[0] + right_point[0]) / 2)
                mid_y = int((left_point[1] + right_point[1]) / 2)
                cv2.circle(img, (mid_x, mid_y), 2, (0, 255, 255), -1)
    
    # 7. 添加图例
    legend_y = 30
    cv2.putText(img, "图例:", (10, legend_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 中心线和中心点
    cv2.line(img, (10, legend_y+30), (60, legend_y+30), (0, 255, 255), 2)
    cv2.putText(img, "中心线和中心点", (70, legend_y+35), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 各参数项图例
    param_colors = {
        "双眼皮": (0, 255, 0),
        "开眼角": (0, 165, 255),
        "去眼袋": (0, 255, 0),
        "提眼尾": (128, 0, 128)
    }
    
    for i, (name, color) in enumerate(param_colors.items()):
        y_offset = legend_y + 60 + i * 30
        cv2.circle(img, (20, y_offset), 3, color, -1)
        cv2.line(img, (35, y_offset), (60, y_offset), color, 1)
        cv2.putText(img, name, (70, y_offset+5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 8. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存参数项分析图到: {output_path}")

def draw_param_points(image_path: str, param_name: str, points_config: dict, output_path: str):
    """绘制参数的特征点分析图
    
    颜色标记：
    - 红色：主导点
    - 绿色：协同点
    - 蓝色：支撑点
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 绘制特征点
    # 主导点 - 红色
    if 'centerPoints' in points_config:
        for point_id in points_config['centerPoints']:
            if point_id in landmarks:
                x, y = landmarks[point_id]
                cv2.circle(img, (int(x), int(y)), 5, (0, 0, 255), -1)  # 红色
                cv2.putText(img, f"{point_id}(主导)", (int(x), int(y)-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    
    # 协同点 - 绿色
    for pair in points_config['pairs']:
        left_id = pair['left']
        right_id = pair['right']
        for point_id in [left_id, right_id]:
            if point_id in landmarks:
                x, y = landmarks[point_id]
                cv2.circle(img, (int(x), int(y)), 5, (0, 255, 0), -1)  # 绿色
                cv2.putText(img, f"{point_id}(协同)", (int(x), int(y)-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 3. 绘制中心线
    center_x = 664  # 根据实际情况调整
    height = img.shape[0]
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 4. 绘制对称连线
    for pair in points_config['pairs']:
        left_id = pair['left']
        right_id = pair['right']
        if left_id in landmarks and right_id in landmarks:
            left_point = landmarks[left_id]
            right_point = landmarks[right_id]
            
            # 绘制连线
            cv2.line(img, 
                    (int(left_point[0]), int(left_point[1])),
                    (int(right_point[0]), int(right_point[1])),
                    points_config['color'], 1)
            
            # 绘制连线中点
            mid_x = int((left_point[0] + right_point[0]) / 2)
            mid_y = int((left_point[1] + right_point[1]) / 2)
            cv2.circle(img, (mid_x, mid_y), 2, (0, 255, 255), -1)
    
    # 5. 添加图例
    legend_y = 30
    cv2.putText(img, f"{param_name}特征点分布:", (10, legend_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 添加图例
    legend_y = 30
    cv2.putText(img, "图例:", (10, legend_y+60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 主导点
    cv2.circle(img, (20, legend_y+90), 5, (0, 0, 255), -1)
    cv2.putText(img, "主导点", (35, legend_y+95), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 协同点
    cv2.circle(img, (20, legend_y+120), 5, (0, 255, 0), -1)
    cv2.putText(img, "协同点", (35, legend_y+125), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 6. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存{param_name}特征点分析图到: {output_path}")

def validate_eye_params(image_path: str):
    """验证眼部参数项的特征点配置"""
    print("\n=== 验证眼部参数项特征点配置 ===")
    
    # 创建特征点验证器
    validator = FeaturePointsValidator(image_path)
    
    # 验证双眼皮参数
    print("\n1. 验证双眼皮参数")
    double_fold_pairs = [
        {'left': 159, 'right': 386},
        {'left': 158, 'right': 385},
        {'left': 156, 'right': 383},
        {'left': 155, 'right': 382}
    ]
    validate_point_pairs(validator, double_fold_pairs)
    
    # 验证开眼角参数
    print("\n2. 验证开眼角参数")
    canthal_tilt_pairs = [
        {'left': 133, 'right': 362},  # 内眼角点
        {'left': 155, 'right': 382}   # 上眼睑内侧点
    ]
    validate_point_pairs(validator, canthal_tilt_pairs)
    
    # 验证去眼袋参数
    print("\n3. 验证去眼袋参数")
    eye_bag_pairs = [
        {'left': 162, 'right': 389}
    ]
    validate_point_pairs(validator, eye_bag_pairs)
    
    # 验证提眼尾参数
    print("\n4. 验证提眼尾参数")
    outer_corner_pairs = [
        {'left': 130, 'right': 359},
        {'left': 145, 'right': 374},
        {'left': 159, 'right': 386}
    ]
    validate_point_pairs(validator, outer_corner_pairs)

def validate_point_pairs(validator: FeaturePointsValidator, point_pairs: list):
    """验证特征点对的对称性
    
    Args:
        validator: 特征点验证器实例
        point_pairs: 特征点对列表，每个点对为包含left和right键的字典
    """
    for pair in point_pairs:
        left_id = str(pair['left'])
        right_id = str(pair['right'])
        print(f"\n验证点对: {left_id} - {right_id}")
        validator.validate_point_symmetry(left_id, right_id)

def visualize_specific_points(image_path: str, points: list, output_path: str):
    """可视化指定特征点的位置
    
    Args:
        image_path: 输入图片路径
        points: 要显示的特征点ID列表
        output_path: 输出图片路径
    """
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    for point_id in points:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            cv2.circle(img, (int(x), int(y)), 5, (0, 0, 255), -1)
            cv2.putText(img, str(point_id), (int(x), int(y)-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    cv2.imwrite(output_path, img)
    print(f"已保存特征点可视化结果到: {output_path}")

def main():
    """主函数"""
    # 设置测试图片路径
    image_path = "testdata/test_face.jpg"
    
    # 单独可视化141-370点对
    print("\n=== 可视化141-370点对 ===")
    visualize_specific_points(
        image_path,
        [141, 370],
        "testdata/eye_points_141_370.jpg"
    )
    
    # 1. 绘制眼部参数总览图
    print("\n=== 绘制眼部参数总览图 ===")
    os.makedirs("testdata", exist_ok=True)
    draw_eye_params(image_path, "testdata/eye_params_overview.jpg")
    
    # 2. 验证眼部参数配置
    validate_eye_params(image_path)
    
    # 3. 绘制各参数项的特征点分布
    print("\n=== 绘制各参数项特征点分布 ===")
    
    # 眼部参数配置
    eye_params = {
        'double_fold': {
            'pairs': [
                {'left': 159, 'right': 386},
                {'left': 158, 'right': 385},
                {'left': 156, 'right': 383},
                {'left': 155, 'right': 382}
            ],
            'centerPoints': [157, 384],
            'color': (0, 255, 0)
        },
        'canthal_tilt': {
            'pairs': [
                {'left': 133, 'right': 362},  # 内眼角点
                {'left': 155, 'right': 382}   # 上眼睑内侧点
            ],
            'centerPoints': [133, 362],  # 内眼角点作为主导点
            'color': (0, 165, 255)
        },
        'eye_bag_removal': {
            'pairs': [
                {'left': 162, 'right': 389}  # 眼袋上缘点对
            ],
            'centerPoints': [162, 389],  # 使用眼袋上缘点对作为主导点
            'color': (0, 255, 0)
        },
        'outer_corner_lift': {
            'pairs': [
                {'left': 130, 'right': 359},
                {'left': 145, 'right': 374},
                {'left': 159, 'right': 386}
            ],
            'centerPoints': [130, 359],
            'color': (128, 0, 128)
        }
    }
    
    param_names = {
        'double_fold': '双眼皮',
        'canthal_tilt': '开眼角',
        'eye_bag_removal': '去眼袋',
        'outer_corner_lift': '提眼尾'
    }
    
    for param_id, param_name in param_names.items():
        draw_param_points(
            image_path,
            param_name,
            eye_params[param_id],
            f"testdata/eye_{param_id}_points.jpg"
        )

if __name__ == "__main__":
    main()
