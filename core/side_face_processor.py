#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
侧面图特征点识别处理器
负责识别侧面图片中的特征点
"""

import os
import sys
import json
import argparse
import cv2
import numpy as np
import mediapipe as mp
import time

# 设置日志格式
def log(level, message):
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] {level} [侧面特征点处理] {message}", file=sys.stderr)

def info(message):
    log("I", message)

def error(message):
    log("E", message)

def debug(message):
    log("D", message)

def process_side_profile(image_path):
    """处理侧面图片并识别特征点"""
    info(f"▶️ 开始: 处理侧面图片 {image_path}")
    
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")
        
        # 转换为RGB（MediaPipe需要RGB格式）
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 获取图片尺寸
        height, width = image.shape[:2]
        info(f"图片尺寸: {width}x{height}")
        
        # 初始化MediaPipe Face Mesh
        mp_face_mesh = mp.solutions.face_mesh
        
        # 使用MediaPipe进行特征点检测
        with mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            min_detection_confidence=0.5) as face_mesh:
            
            # 处理图片
            results = face_mesh.process(image_rgb)
            
            # 检查是否检测到人脸
            if not results.multi_face_landmarks:
                info("⚠️ 未检测到人脸，使用模拟数据")
                # 生成模拟数据
                landmarks = generate_mock_landmarks(width, height)
                return {
                    'status': 'no_face',
                    'message': '未检测到人脸',
                    'landmarks': landmarks
                }
            else:
                info("✅ 检测到人脸")
                # 提取特征点
                face_landmarks = results.multi_face_landmarks[0]
                landmarks = []
                
                for idx, landmark in enumerate(face_landmarks.landmark):
                    # 转换为像素坐标
                    x = landmark.x
                    y = landmark.y
                    z = landmark.z
                    
                    # 确定点的类型
                    is_primary = idx in PRIMARY_POINTS
                    is_secondary = idx in SECONDARY_POINTS
                    
                    landmarks.append({
                        "index": idx,
                        "x": x,
                        "y": y,
                        "z": z,
                        "visibility": 1.0,
                        "confidence": 0.95,
                        "primary": is_primary,
                        "secondary": is_secondary
                    })
        
        info(f"✅ 特征点识别完成，共 {len(landmarks)} 个点")
        
        # 返回结果
        return {
            'status': 'success',
            'landmarks': landmarks
        }
        
    except Exception as e:
        error(f"❌ 处理失败: {str(e)}")
        return {
            'status': 'error',
            'message': str(e)
        }

def generate_mock_landmarks(width, height):
    """生成模拟的特征点数据"""
    landmarks = []
    
    # 生成50个模拟特征点
    for i in range(50):
        x = 0.2 + (i % 10) * 0.06
        y = 0.2 + (i // 10) * 0.12
        
        is_primary = i % 5 == 0
        is_secondary = i % 7 == 0
        
        landmarks.append({
            "index": i,
            "x": x,
            "y": y,
            "z": 0.0,
            "visibility": 1.0,
            "confidence": 0.95,
            "primary": is_primary,
            "secondary": is_secondary
        })
    
    return landmarks

# 定义主要和次要控制点
PRIMARY_POINTS = [0, 17, 61, 291, 199, 33, 263, 61, 291, 199]
SECONDARY_POINTS = [10, 152, 172, 127, 93, 234, 454, 323, 361, 288]

def main():
    parser = argparse.ArgumentParser(description='侧面图特征点检测')
    parser.add_argument('image_path', type=str, nargs='?', help='图像文件路径')
    parser.add_argument('--params', type=str, help='JSON格式的参数')
    parser.add_argument('--output', type=str, help='输出文件路径')
    args = parser.parse_args()
    
    try:
        # 确定图像路径
        image_path = None
        output_file = None
        
        if args.image_path:
            # 直接从命令行参数获取图像路径
            image_path = args.image_path
            output_file = args.output
        elif args.params:
            # 从JSON参数中获取图像路径
            params = json.loads(args.params)
            image_path = params.get('image_path')
            output_file = params.get('output_file')
        
        if not image_path:
            error("缺少必要参数: 图像路径")
            result = {
                'status': 'error',
                'message': '缺少必要参数: 图像路径'
            }
            if output_file:
                with open(output_file, 'w') as f:
                    json.dump(result, f)
            else:
                print(json.dumps(result))
            sys.exit(1)
        
        # 处理侧面图片
        result = process_side_profile(image_path)
        
        # 输出结果
        if output_file:
            info(f"将结果写入文件: {output_file}")
            with open(output_file, 'w') as f:
                json.dump(result, f)
        else:
            print(json.dumps(result))
            
    except Exception as e:
        error(f"处理失败: {str(e)}")
        result = {
            'status': 'error',
            'message': str(e)
        }
        if 'output_file' in locals() and output_file:
            with open(output_file, 'w') as f:
                json.dump(result, f)
        else:
            print(json.dumps(result))
        sys.exit(1)

if __name__ == "__main__":
    main()
