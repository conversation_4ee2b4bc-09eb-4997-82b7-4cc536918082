#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Python 环境测试脚本

此脚本用于测试 Python 环境是否正常工作，
输出 Python 版本和系统信息。
"""

import sys
import platform
import json

def main():
    """主函数"""
    try:
        # 收集系统信息
        info = {
            "status": "success",
            "python_version": sys.version,
            "platform": platform.platform(),
            "system": platform.system(),
            "node": platform.node(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor()
        }
        
        # 输出 JSON 格式的结果
        print(json.dumps(info))
        return 0
    
    except Exception as e:
        # 输出错误信息
        error = {
            "status": "error",
            "message": str(e)
        }
        print(json.dumps(error))
        return 1

if __name__ == "__main__":
    sys.exit(main())
