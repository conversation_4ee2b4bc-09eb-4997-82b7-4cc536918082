#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
特征点可视化器
用于可视化显示人脸特征点和区域
"""

import logging
import cv2
import numpy as np
from typing import List, Dict, Any, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('LandmarkVisualizer')

class LandmarkVisualizer:
    """特征点可视化器类"""
    
    # 区域颜色映射
    REGION_COLORS = {
        # 医美项目区域
        'face_contour': (255, 200, 0),   # 蓝绿色
        'nose': (0, 255, 0),             # 绿色
        'eyes': (255, 0, 0),             # 蓝色
        'lips': (0, 0, 255),             # 红色
        # 标准区域
        'eyebrows': (255, 0, 255),       # 紫色
        'iris': (0, 255, 255),           # 黄色
        'face_oval': (200, 200, 0),      # 浅蓝绿色
        'other': (128, 128, 128)         # 灰色
    }
    
    # 点的大小映射
    POINT_SIZES = {
        'primary': 3,      # 主要点
        'secondary': 2,    # 次要点
        'standard': 2,     # 标准点
        'none': 1          # 其他点
    }
    
    def __init__(self):
        """初始化可视化器"""
        self.logger = logging.getLogger('LandmarkVisualizer')
        self.logger.info("初始化特征点可视化器")
        
    def _draw_point(self, image: np.ndarray, point: Tuple[int, int], 
                   color: Tuple[int, int, int], point_type: str = 'none') -> None:
        """绘制单个特征点
        
        Args:
            image: 图像数组
            point: 特征点坐标 (x, y)
            color: 颜色 (B, G, R)
            point_type: 点的类型 ('primary', 'secondary', 'standard', 'none')
        """
        size = self.POINT_SIZES.get(point_type, self.POINT_SIZES['none'])
        cv2.circle(image, point, size, color, -1)
        
        # 为主要点添加外圈
        if point_type == 'primary':
            cv2.circle(image, point, size + 1, (255, 255, 255), 1)
        
    def _draw_connection(self, image: np.ndarray, point1: Tuple[int, int], 
                        point2: Tuple[int, int], color: Tuple[int, int, int], 
                        thickness: int = 1) -> None:
        """绘制两点之间的连接线
        
        Args:
            image: 图像数组
            point1: 第一个点的坐标 (x, y)
            point2: 第二个点的坐标 (x, y)
            color: 颜色 (B, G, R)
            thickness: 线条粗细
        """
        cv2.line(image, point1, point2, color, thickness)
        
    def _draw_region_connections(self, image: np.ndarray, 
                               points: List[Tuple[int, int]], 
                               color: Tuple[int, int, int], 
                               thickness: int = 1) -> None:
        """绘制区域连接线
        
        Args:
            image: 图像数组
            points: 区域点坐标列表
            color: 颜色 (B, G, R)
            thickness: 线条粗细
        """
        for i in range(len(points) - 1):
            self._draw_connection(image, points[i], points[i + 1], color, thickness)
        
    def draw_landmarks(self, image: np.ndarray, landmarks: List[Dict[str, Any]], 
                      output_path: str = None) -> np.ndarray:
        """绘制所有特征点和连接
        
        Args:
            image: 输入图像
            landmarks: 特征点列表，每个特征点包含坐标和区域信息
            output_path: 输出图像路径，如果为None则不保存
            
        Returns:
            np.ndarray: 绘制了特征点的图像
        """
        try:
            if landmarks is None or len(landmarks) == 0:
                self.logger.warning("没有可绘制的特征点")
                return image
                
            self.logger.info(f"开始绘制特征点，共 {len(landmarks)} 个点")
            self.logger.info(f"图像尺寸: {image.shape}")
            
            # 创建图像副本
            height, width = image.shape[:2]
            vis_image = image.copy()
            
            # 按区域组织特征点
            region_points = {}
            for landmark in landmarks:
                try:
                    region = landmark.get('region', 'other')
                    if region not in region_points:
                        region_points[region] = []
                        
                    # 转换相对坐标为绝对坐标
                    # 检查坐标值是否已经是绝对值
                    x_val = float(landmark['x'])
                    y_val = float(landmark['y'])
                    
                    # 如果坐标已经是绝对值（大于1），则直接使用
                    if x_val > 1.0 and y_val > 1.0:
                        x = int(x_val)
                        y = int(y_val)
                        self.logger.debug(f"使用绝对坐标: ({x}, {y})")
                    else:
                        # 否则将相对坐标转换为绝对坐标
                        x = int(x_val * width)
                        y = int(y_val * height)
                        self.logger.debug(f"转换相对坐标: ({x_val}, {y_val}) -> ({x}, {y})")
                    
                    point_type = landmark.get('region_type', 'none')
                    
                    # 添加点信息
                    region_points[region].append({
                        'point': (x, y),
                        'type': point_type,
                        'index': landmark.get('index', -1)
                    })
                    
                    self.logger.debug(f"处理特征点: 区域={region}, 类型={point_type}, 坐标=({x}, {y})")
                except (KeyError, ValueError, TypeError) as e:
                    self.logger.warning(f"特征点数据格式错误: {str(e)}")
                    continue
            
            # 绘制每个区域的特征点和连接
            region_stats = {}
            for region, points_data in region_points.items():
                if not points_data:  # 跳过空区域
                    continue
                    
                color = self.REGION_COLORS.get(region, self.REGION_COLORS['other'])
                points = [p['point'] for p in points_data]
                
                # 统计信息
                region_stats[region] = {
                    'total': len(points),
                    'primary': len([p for p in points_data if p['type'] == 'primary']),
                    'secondary': len([p for p in points_data if p['type'] == 'secondary']),
                    'standard': len([p for p in points_data if p['type'] == 'standard'])
                }
                
                # 绘制连接线
                if len(points) > 1:
                    try:
                        self._draw_region_connections(vis_image, points, color, 1)
                        self.logger.debug(f"绘制区域 {region} 的连接线")
                    except Exception as e:
                        self.logger.warning(f"绘制区域 {region} 连接线失败: {str(e)}")
                
                # 绘制特征点
                for point_data in points_data:
                    try:
                        self._draw_point(vis_image, point_data['point'], color, point_data['type'])
                    except Exception as e:
                        self.logger.warning(f"绘制特征点失败: {str(e)}")
                        
            # 记录区域统计信息
            for region, stats in region_stats.items():
                self.logger.info(f"[区域统计] {region}: 总点数={stats['total']}, "
                                f"主要点={stats['primary']}, 次要点={stats['secondary']}, "
                                f"标准点={stats['standard']}")
                    
            self.logger.info(f"特征点绘制完成，共处理 {len(landmarks)} 个特征点")
            
            # 保存结果
            if output_path:
                try:
                    # 添加水印，使结果更明显
                    cv2.putText(vis_image, f"Points: {len(landmarks)}", (10, 30), 
                                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    
                    # 确保输出目录存在
                    import os
                    output_dir = os.path.dirname(output_path)
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)
                        self.logger.info(f"创建输出目录: {output_dir}")
                    
                    # 保存图像
                    result = cv2.imwrite(output_path, cv2.cvtColor(vis_image, cv2.COLOR_RGB2BGR))
                    
                    if result:
                        self.logger.info(f"可视化结果已成功保存至: {output_path}")
                        # 检查文件大小
                        file_size = os.path.getsize(output_path)
                        self.logger.info(f"输出文件大小: {file_size} 字节")
                    else:
                        self.logger.error(f"保存图像失败: {output_path}")
                except Exception as e:
                    self.logger.error(f"保存结果图像失败: {str(e)}")
            
            return vis_image
            
        except Exception as e:
            self.logger.error(f"特征点绘制失败: {str(e)}")
            return image


# 主函数部分
if __name__ == "__main__":
    import argparse
    import json
    import sys
    
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='特征点可视化工具')
    parser.add_argument('--input', type=str, required=True, help='输入图像路径')
    parser.add_argument('--output', type=str, required=True, help='输出图像路径')
    parser.add_argument('--landmarks', type=str, required=True, help='特征点数据（JSON格式）')
    parser.add_argument('--json', type=str, help='JSON格式的参数')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logger.setLevel(logging.DEBUG)
    
    # 创建可视化器实例
    visualizer = LandmarkVisualizer()
    
    try:
        # 处理参数
        if args.json:
            # 从JSON参数中解析
            params = json.loads(args.json)
            input_path = params.get('input')
            output_path = params.get('output')
            landmarks_data = params.get('landmarks', [])
        else:
            # 直接从命令行参数解析
            input_path = args.input
            output_path = args.output
            landmarks_data = json.loads(args.landmarks)
        
        logger.info(f"处理输入图像: {input_path}")
        logger.info(f"输出路径: {output_path}")
        logger.info(f"特征点数量: {len(landmarks_data)}")
        
        # 检查输入文件是否存在
        try:
            image = cv2.imread(input_path)
            if image is None:
                logger.error(f"无法读取输入图像: {input_path}")
                result = {
                    "status": "error",
                    "message": f"无法读取输入图像: {input_path}"
                }
                print(json.dumps(result))
                sys.exit(1)
            
            # 转换为RGB格式
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 处理特征点数据格式
            processed_landmarks = []
            for landmark in landmarks_data:
                # 添加区域信息，如果没有则默认为'other'
                if 'region' not in landmark:
                    landmark['region'] = 'other'
                processed_landmarks.append(landmark)
            
            # 绘制特征点
            result_image = visualizer.draw_landmarks(image, processed_landmarks, output_path)
            
            # 返回成功状态
            result = {
                "status": "success",
                "message": f"已成功处理 {len(processed_landmarks)} 个特征点",
                "output_path": output_path
            }
            print(json.dumps(result))
            sys.exit(0)
            
        except Exception as e:
            logger.error(f"处理图像失败: {str(e)}")
            result = {
                "status": "error",
                "message": f"处理图像失败: {str(e)}"
            }
            print(json.dumps(result))
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"参数解析失败: {str(e)}")
        result = {
            "status": "error",
            "message": f"参数解析失败: {str(e)}"
        }
        print(json.dumps(result))
        sys.exit(1)
