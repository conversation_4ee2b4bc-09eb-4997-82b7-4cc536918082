#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
抗衰老参数测试程序
用于验证抗衰老相关特征点的定位、对称性和变形效果
"""

import os
import sys
import cv2
import numpy as np
from feature_points_validator import FeaturePointsValidator
from face_mesh_processor import get_all_landmarks

def draw_anti_aging_params(image_path: str, output_path: str):
    """绘制抗衰老四个参数项的特征点分析图
    
    Args:
        image_path: 输入图片路径
        output_path: 输出图片路径
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 调整特征点坐标，使鼻尖点作为面部中心线
    nose_tip_x = int(landmarks[168][0])  # 使用鼻尖作为面部中心参考点
    x_offset = 664 - nose_tip_x  # 计算需要的偏移量
    
    # 创建新的landmarks字典，调整x坐标
    adjusted_landmarks = {}
    for point_id, (x, y) in landmarks.items():
        adjusted_landmarks[point_id] = (int(x + x_offset), int(y))
    
    # 3. 抗衰老参数配置
    anti_aging_params = {
        'nasolabial_folds': {  # 法令纹 - 使用验证过的对称点对
            'pairs': [
                {'left': 129, 'right': 358},  # 法令纹最上部 (0.98)
                {'left': 130, 'right': 359},  # 法令纹上部 (0.97)
                {'left': 131, 'right': 360},  # 法令纹中部 (0.97)
                {'left': 132, 'right': 361},  # 法令纹下部 (0.94)
                {'left': 133, 'right': 362},  # 法令纹过渡 (0.98)
            ],
            'centerPoints': [131, 360],  # 使用中部点作为主导点
            'color': (0, 255, 0)  # 绿色
        },
        'forehead_fullness': {  # 额头饱满
            'pairs': [
                {'left': 21, 'right': 251},   # 额头中部
                {'left': 71, 'right': 301},   # 额头外侧
                {'left': 162, 'right': 389},  # 太阳穴区域
                {'left': 68, 'right': 298},   # 额头过渡
            ],
            'centerPoints': [21, 251],  # 额头中部作为主导点
            'color': (0, 165, 255)  # 橙色
        },
        'facial_firmness': {  # 面容紧致 - 使用新验证的对称点对
            'pairs': [
                {'left': 54, 'right': 284},   # 上颊
                {'left': 104, 'right': 333},  # 中颊点对1 (0.96)
                {'left': 105, 'right': 334},  # 中颊点对2 (0.92)
                {'left': 67, 'right': 297},   # 下颊
            ],
            'centerPoints': [54, 284],  # 上颊作为主导点
            'color': (0, 255, 0)  # 绿色
        },
        'wrinkle_removal': {  # 去皱纹
            'pairs': [
                {'left': 107, 'right': 336},  # 主要皱纹区
                {'left': 108, 'right': 337},  # 皱纹过渡区
                {'left': 148, 'right': 377},  # 嘴角区域
            ],
            'centerPoints': [107, 336],  # 主要皱纹区作为主导点
            'color': (128, 0, 128)  # 紫色
        }
    }
    
    # 4. 绘制面部中心线
    center_x = 664  # 固定中心线位置
    height = img.shape[0]
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 5. 绘制中心点（主导点）
    for param_name, param in anti_aging_params.items():
        if 'centerPoints' in param:
            for point_id in param['centerPoints']:
                if point_id in adjusted_landmarks:
                    x, y = adjusted_landmarks[point_id]
                    cv2.circle(img, (int(x), int(y)), 4, (0, 0, 255), -1)  # 红色
                    cv2.putText(img, f"{point_id}(主导)", (int(x), int(y)-5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    
    # 6. 按参数项绘制对称点对和连线（协同点）
    for param_name, param in anti_aging_params.items():
        for pair in param['pairs']:
            left_id = pair['left']
            right_id = pair['right']
            if left_id in adjusted_landmarks and right_id in adjusted_landmarks:
                left_point = adjusted_landmarks[left_id]
                right_point = adjusted_landmarks[right_id]
                
                # 绘制特征点
                for point_id, point in [(left_id, left_point), (right_id, right_point)]:
                    if point_id not in param.get('centerPoints', []):  # 如果不是主导点
                        cv2.circle(img, (int(point[0]), int(point[1])), 3, 
                                 (0, 255, 0), -1)  # 绿色
                        cv2.putText(img, f"{point_id}(协同)", 
                                  (int(point[0]), int(point[1])-5), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                # 绘制对称连线
                cv2.line(img, 
                        (int(left_point[0]), int(left_point[1])),
                        (int(right_point[0]), int(right_point[1])),
                        param['color'], 1)
                
                # 绘制连线中点
                mid_x = int((left_point[0] + right_point[0]) / 2)
                mid_y = int((left_point[1] + right_point[1]) / 2)
                cv2.circle(img, (mid_x, mid_y), 2, (0, 255, 255), -1)
    
    # 7. 添加图例
    legend_y = 30
    cv2.putText(img, "图例:", (10, legend_y), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 点类型图例
    point_types = {
        "主导点": (0, 0, 255),    # 红色
        "协同点": (0, 255, 0),    # 绿色
        "中心线": (0, 255, 255)   # 黄色
    }
    
    for i, (name, color) in enumerate(point_types.items()):
        y_offset = legend_y + 30 + i * 30
        cv2.circle(img, (20, y_offset), 3, color, -1)
        cv2.putText(img, name, (70, y_offset+5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 参数项图例
    param_names = {
        "法令纹": (0, 255, 0),
        "额头饱满": (0, 165, 255),
        "面容紧致": (0, 255, 0),
        "去皱纹": (128, 0, 128)
    }
    
    for i, (name, color) in enumerate(param_names.items()):
        y_offset = legend_y + 120 + i * 30
        cv2.circle(img, (20, y_offset), 3, color, -1)
        cv2.line(img, (35, y_offset), (60, y_offset), color, 1)
        cv2.putText(img, name, (70, y_offset+5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
    
    # 8. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存参数项分析图到: {output_path}")

def draw_param_points(image_path: str, param_name: str, points_config: dict, output_path: str):
    """绘制参数的特征点分析图
    
    颜色标记：
    - 红色：主导点
    - 绿色：协同点
    - 黄色：中心线和中点
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 调整特征点坐标，使鼻尖点作为面部中心线
    nose_tip_x = int(landmarks[168][0])  # 使用鼻尖作为面部中心参考点
    x_offset = 664 - nose_tip_x  # 计算需要的偏移量
    
    # 创建新的landmarks字典，调整x坐标
    adjusted_landmarks = {}
    for point_id, (x, y) in landmarks.items():
        adjusted_landmarks[point_id] = (int(x + x_offset), int(y))
    
    # 3. 绘制特征点
    # 主导点 - 红色
    if 'centerPoints' in points_config:
        for point_id in points_config['centerPoints']:
            if point_id in adjusted_landmarks:
                x, y = adjusted_landmarks[point_id]
                cv2.circle(img, (int(x), int(y)), 5, (0, 0, 255), -1)  # 红色
                cv2.putText(img, f"{point_id}(主导)", (int(x), int(y)-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    
    # 协同点 - 绿色
    for pair in points_config['pairs']:
        left_id = pair['left']
        right_id = pair['right']
        for point_id in [left_id, right_id]:
            if point_id not in points_config.get('centerPoints', []):  # 如果不是主导点
                if point_id in adjusted_landmarks:
                    x, y = adjusted_landmarks[point_id]
                    cv2.circle(img, (int(x), int(y)), 5, (0, 255, 0), -1)  # 绿色
                    cv2.putText(img, f"{point_id}(协同)", (int(x), int(y)-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 4. 绘制中心线
    center_x = 664  # 固定中心线位置
    height = img.shape[0]
    cv2.line(img, (center_x, 0), (center_x, height), (0, 255, 255), 2)
    
    # 5. 绘制对称连线
    for pair in points_config['pairs']:
        left_id = pair['left']
        right_id = pair['right']
        if left_id in adjusted_landmarks and right_id in adjusted_landmarks:
            left_point = adjusted_landmarks[left_id]
            right_point = adjusted_landmarks[right_id]
            
            # 绘制连线
            cv2.line(img, 
                    (int(left_point[0]), int(left_point[1])),
                    (int(right_point[0]), int(right_point[1])),
                    points_config['color'], 1)
            
            # 绘制连线中点
            mid_x = int((left_point[0] + right_point[0]) / 2)
            mid_y = int((left_point[1] + right_point[1]) / 2)
            cv2.circle(img, (mid_x, mid_y), 2, (0, 255, 255), -1)
    
    # 6. 添加标题
    cv2.putText(img, f"{param_name}特征点分析", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 7. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存{param_name}特征点分析图到: {output_path}")

def validate_anti_aging_params(image_path: str):
    """验证抗衰老参数项的特征点配置"""
    # 1. 获取特征点
    landmarks = get_all_landmarks(image_path)
    
    # 2. 调整特征点坐标，使鼻尖点作为面部中心线
    nose_tip_x = int(landmarks[168][0])  # 使用鼻尖作为面部中心参考点
    x_offset = 664 - nose_tip_x  # 计算需要的偏移量
    
    # 创建新的landmarks字典，调整x坐标
    adjusted_landmarks = {}
    for point_id, (x, y) in landmarks.items():
        adjusted_landmarks[point_id] = (int(x + x_offset), int(y))
    
    # 3. 初始化验证器
    validator = FeaturePointsValidator(adjusted_landmarks)
    
    # 为104寻找对称点
    print("\n为104寻找对称点:")
    left_pairs = [
        (104, 330),
        (104, 331),
        (104, 332),
        (104, 333),
        (104, 335)
    ]
    validate_point_pairs(validator, left_pairs)
    
    # 为334寻找对称点
    print("\n为334寻找对称点:")
    right_pairs = [
        (100, 334),
        (101, 334),
        (102, 334),
        (103, 334),
        (105, 334)
    ]
    validate_point_pairs(validator, right_pairs)

def validate_point_pairs(validator: FeaturePointsValidator, point_pairs: list):
    """验证特征点对的对称性
    
    Args:
        validator: 特征点验证器
        point_pairs: 要验证的特征点对列表，每个元素是一个(left_id, right_id)元组
    """
    for left_id, right_id in point_pairs:
        score = validator.validate_point_symmetry(left_id, right_id)
        print(f"点对 {left_id}-{right_id} 对称性得分: {score:.2f}")

def visualize_specific_points(image_path: str, points: list, output_path: str):
    """可视化指定特征点的位置
    
    Args:
        image_path: 输入图片路径
        points: 要显示的特征点ID列表
        output_path: 输出图片路径
    """
    # 1. 读取图片和特征点
    img = cv2.imread(image_path)
    landmarks = get_all_landmarks(image_path)
    
    # 2. 绘制特征点
    for point_id in points:
        if point_id in landmarks:
            x, y = landmarks[point_id]
            cv2.circle(img, (int(x), int(y)), 3, (0, 255, 0), -1)
            cv2.putText(img, str(point_id), (int(x), int(y)-5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 3. 保存结果
    cv2.imwrite(output_path, img)
    print(f"已保存特征点可视化图到: {output_path}")

def main():
    """主函数"""
    # 1. 设置输入输出路径
    test_image = "testdata/test_face.jpg"
    output_dir = "testdata/output"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 2. 生成总体参数分析图
    params_output = os.path.join(output_dir, "anti_aging_params.jpg")
    draw_anti_aging_params(test_image, params_output)
    
    # 3. 生成各参数项的单独分析图
    anti_aging_params = {
        'nasolabial_folds': {  # 法令纹 - 使用验证过的对称点对
            'pairs': [
                {'left': 129, 'right': 358},  # 法令纹最上部 (0.98)
                {'left': 130, 'right': 359},  # 法令纹上部 (0.97)
                {'left': 131, 'right': 360},  # 法令纹中部 (0.97)
                {'left': 132, 'right': 361},  # 法令纹下部 (0.94)
                {'left': 133, 'right': 362},  # 法令纹过渡 (0.98)
            ],
            'centerPoints': [131, 360],  # 使用中部点作为主导点
            'color': (0, 255, 0)  # 绿色
        },
        'forehead_fullness': {  # 额头饱满 - 保持原有配置
            'pairs': [
                {'left': 21, 'right': 251},   # 额头中部
                {'left': 71, 'right': 301},   # 额头外侧
                {'left': 162, 'right': 389},  # 太阳穴区域
                {'left': 68, 'right': 298},   # 额头过渡
            ],
            'centerPoints': [21, 251],  # 额头中部作为主导点
            'color': (0, 165, 255)  # 橙色
        },
        'facial_firmness': {  # 面容紧致 - 需要继续测试新的点对
            'pairs': [
                {'left': 54, 'right': 284},   # 上颊
                {'left': 104, 'right': 333},  # 中颊点对1 (0.96)
                {'left': 105, 'right': 334},  # 中颊点对2 (0.92)
                {'left': 67, 'right': 297},   # 下颊
            ],
            'centerPoints': [54, 284],  # 上颊作为主导点
            'color': (0, 255, 0)  # 绿色
        },
        'wrinkle_removal': {  # 去皱纹
            'pairs': [
                {'left': 107, 'right': 336},  # 主要皱纹区
                {'left': 108, 'right': 337},  # 皱纹过渡区
                {'left': 148, 'right': 377},  # 嘴角区域
            ],
            'centerPoints': [107, 336],  # 主要皱纹区作为主导点
            'color': (128, 0, 128)  # 紫色
        }
    }
    
    param_names = {
        'nasolabial_folds': '法令纹',
        'forehead_fullness': '额头饱满',
        'facial_firmness': '面容紧致',
        'wrinkle_removal': '去皱纹'
    }
    
    for param_id, param_name in param_names.items():
        output_path = os.path.join(output_dir, f"anti_aging_{param_id}.jpg")
        draw_param_points(test_image, param_name, anti_aging_params[param_id], output_path)
    
    # 4. 验证特征点配置
    validate_anti_aging_params(test_image)

if __name__ == "__main__":
    main()
