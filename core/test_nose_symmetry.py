#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
鼻部解剖学特征点对称性分析脚本
基于面部解剖学特征和复合体系统进行鼻部对称性分析
"""

import os
import sys
import logging
import cv2
import numpy as np
import mediapipe as mp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('NoseSymmetryTest')

# 鼻部解剖学特征点定义
NOSE_LANDMARKS = {
    # 鼻梁中线（定义鼻子的解剖学中轴）
    'nasal_midline': [
        168,  # 鼻根点（眉间点）
        6,    # 鼻梁最高点
        197,  # 鼻梁上1/3点
        195,  # 鼻梁中点
        5,    # 鼻梁下1/3点
        4,    # 鼻背点
        1,    # 鼻尖点
        2,    # 鼻小柱顶点
        19,   # 人中上点
        94    # 人中点
    ],
    
    # 鼻背复合体（定义鼻梁轮廓）
    'dorsum_complex': [
        (6, 1.2),    # 鼻梁最高点（权重1.2）
        (197, 1.0),  # 鼻梁上1/3（权重1.0）
        (195, 0.8),  # 鼻梁中点（权重0.8）
        (4, 0.6)     # 鼻背点（权重0.6）
    ],
    
    # 鼻翼复合体（定义鼻翼形态）
    'alar_complex': [
        # 外侧轮廓（主要控制点）
        (129, 358, 1.0),  # 鼻翼基底点
        (219, 439, 1.0),  # 鼻翼外缘点
        (235, 455, 0.8),  # 鼻翼沟上点
        
        # 内侧轮廓（次要控制点）
        (115, 344, 0.8),  # 鼻翼内侧点
        (220, 440, 0.6),  # 鼻翼软骨点
        
        # 底部轮廓（参考点）
        (114, 343, 0.5),  # 鼻翼底外侧点
        (217, 437, 0.5)   # 鼻翼底内侧点
    ],
    
    # 鼻孔复合体（定义鼻孔形态）
    'nostril_complex': [
        # 外侧轮廓
        (79, 309, 1.0),   # 鼻孔外缘点
        (166, 392, 1.0),  # 鼻孔内缘点
        
        # 内侧轮廓
        (98, 327, 0.8),   # 鼻孔底点
        (64, 294, 0.8),   # 鼻孔内侧点
        
        # 顶部轮廓
        (97, 326, 0.6),   # 鼻孔顶点
        (167, 393, 0.6)   # 鼻孔内侧补充点
    ],
    
    # 鼻尖复合体（定义鼻尖形态）
    'tip_complex': [
        # 主要轮廓点
        (48, 278, 1.0),   # 鼻尖外侧点
        (49, 279, 1.0),   # 鼻尖内侧点
        
        # 过渡区域点
        (64, 294, 0.8),   # 鼻尖-鼻翼过渡点
        (114, 343, 0.8)   # 鼻尖-鼻孔过渡点
    ],
    
    # 鼻小柱复合体（定义鼻小柱形态）
    'columella_complex': [
        (2, 1.0),    # 鼻小柱顶点
        (19, 0.8),   # 鼻小柱基底点
        (94, 0.6)    # 人中过渡点
    ]
}

def verify_nose_symmetry(landmarks, width, height, tolerance=5):
    """验证鼻部各复合体的对称性
    
    Args:
        landmarks: MediaPipe面部特征点
        width: 图像宽度
        height: 图像高度
        tolerance: 基准对称容差（像素）
        
    Returns:
        dict: 各复合体的对称性分析结果
    """
    results = {}
    
    # 获取鼻尖点作为中心参考
    nose_tip = landmarks.landmark[1]
    center_x = nose_tip.x * width
    
    # 需要验证对称性的复合体
    symmetry_complexes = [
        'alar_complex',
        'nostril_complex',
        'tip_complex'
    ]
    
    for complex_name in symmetry_complexes:
        results[complex_name] = {
            'total_points': 0,
            'symmetric_points': 0,
            'weighted_symmetry': 0.0,
            'points_detail': []
        }
        
        # 验证每个复合体中的点对
        for point_data in NOSE_LANDMARKS[complex_name]:
            left_idx, right_idx, weight = point_data
            results[complex_name]['total_points'] += 1
            
            # 获取点位坐标
            left_point = landmarks.landmark[left_idx]
            right_point = landmarks.landmark[right_idx]
            
            # 计算到中心线的距离
            left_dist = abs((left_point.x * width) - center_x)
            right_dist = abs((right_point.x * width) - center_x)
            dist_diff = abs(left_dist - right_dist)
            
            # 根据权重调整容差
            weighted_tolerance = tolerance * weight
            is_symmetric = dist_diff <= weighted_tolerance
            
            # 计算对称度分数 (0-1之间)
            symmetry_score = max(0, 1 - (dist_diff / weighted_tolerance))
            weighted_symmetry = symmetry_score * weight
            
            # 保存点位验证结果
            point_detail = {
                'left_index': left_idx,
                'right_index': right_idx,
                'weight': weight,
                'distance_diff': dist_diff,
                'symmetry_score': symmetry_score,
                'weighted_symmetry': weighted_symmetry,
                'is_symmetric': is_symmetric
            }
            results[complex_name]['points_detail'].append(point_detail)
            
            if is_symmetric:
                results[complex_name]['symmetric_points'] += 1
            results[complex_name]['weighted_symmetry'] += weighted_symmetry
        
        # 计算复合体的总体对称性得分
        total_weights = sum(p[2] for p in NOSE_LANDMARKS[complex_name])
        results[complex_name]['weighted_symmetry'] /= total_weights
        
        # 记录日志
        logger.info(f"\n{complex_name} 对称性分析:")
        logger.info(f"  总点数: {results[complex_name]['total_points']}")
        logger.info(f"  对称点数: {results[complex_name]['symmetric_points']}")
        logger.info(f"  加权对称度: {results[complex_name]['weighted_symmetry']:.2f}")
        
        # 输出各点位的详细信息
        for detail in results[complex_name]['points_detail']:
            status = '对称' if detail['is_symmetric'] else '不对称'
            logger.info(f"  点对 ({detail['left_index']}, {detail['right_index']})")
            logger.info(f"    权重: {detail['weight']}")
            logger.info(f"    偏差: {detail['distance_diff']:.2f}像素")
            logger.info(f"    对称度: {detail['symmetry_score']:.2f}")
            logger.info(f"    状态: {status}")
    
    return results

def analyze_nose_symmetry(image_path):
    """分析鼻部对称性并生成可视化结果
    
    Args:
        image_path: 输入图像路径
    
    Returns:
        str: 输出图像路径
    """
    logger.info(f"开始鼻部解剖学特征分析 | 图像路径: {image_path}")
    
    try:
        # 初始化MediaPipe Face Mesh
        mp_face_mesh = mp.solutions.face_mesh
        face_mesh = mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.7
        )
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise Exception("无法读取图像")
        
        # 转换为RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        height, width = image_rgb.shape[:2]
        
        # 检测特征点
        results = face_mesh.process(image_rgb)
        
        if not results.multi_face_landmarks:
            raise Exception("未检测到人脸")
        
        # 获取第一个人脸的特征点
        face_landmarks = results.multi_face_landmarks[0]
        
        # 创建可视化图像
        vis_image = image.copy()
        
        # 绘制鼻梁中线
        for i in range(len(NOSE_LANDMARKS['nasal_midline']) - 1):
            start_idx = NOSE_LANDMARKS['nasal_midline'][i]
            end_idx = NOSE_LANDMARKS['nasal_midline'][i + 1]
            
            start_point = face_landmarks.landmark[start_idx]
            start_x = int(start_point.x * width)
            start_y = int(start_point.y * height)
            
            end_point = face_landmarks.landmark[end_idx]
            end_x = int(end_point.x * width)
            end_y = int(end_point.y * height)
            
            # 绘制中线（红色）
            cv2.line(vis_image, (start_x, start_y), (end_x, end_y), (0, 0, 255), 1)
            cv2.circle(vis_image, (start_x, start_y), 2, (0, 0, 255), -1)
        
        # 验证各复合体的对称性
        symmetry_results = verify_nose_symmetry(face_landmarks, width, height)
        
        # 绘制各复合体的点对和连接线
        complex_colors = {
            'alar_complex': [(0, 255, 0), (144, 238, 144)],      # 绿色系：鼻翼
            'nostril_complex': [(255, 0, 0), (255, 182, 193)],   # 红色系：鼻孔
            'tip_complex': [(255, 165, 0), (255, 218, 185)]      # 橙色系：鼻尖
        }
        
        for complex_name, results in symmetry_results.items():
            primary_color, secondary_color = complex_colors[complex_name]
            
            for point_detail in results['points_detail']:
                left_idx = point_detail['left_index']
                right_idx = point_detail['right_index']
                weight = point_detail['weight']
                is_symmetric = point_detail['is_symmetric']
                
                # 获取点位坐标
                left_point = face_landmarks.landmark[left_idx]
                right_point = face_landmarks.landmark[right_idx]
                
                left_x = int(left_point.x * width)
                left_y = int(left_point.y * height)
                right_x = int(right_point.x * width)
                right_y = int(right_point.y * height)
                
                # 选择颜色（根据权重和对称性）
                color = primary_color if weight >= 0.8 else secondary_color
                if not is_symmetric:
                    color = tuple(max(0, c - 100) for c in color)  # 降低不对称点的颜色亮度
                
                # 绘制点对和连接线
                cv2.line(vis_image, (left_x, left_y), (right_x, right_y), color, 1)
                cv2.circle(vis_image, (left_x, left_y), 3, color, -1)
                cv2.circle(vis_image, (right_x, right_y), 3, color, -1)
                
                # 添加点的索引标签
                cv2.putText(vis_image, str(left_idx), (left_x-10, left_y-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
                cv2.putText(vis_image, str(right_idx), (right_x+5, right_y-5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        # 添加总体对称性评分
        total_symmetry = sum(r['weighted_symmetry'] for r in symmetry_results.values()) / len(symmetry_results)
        cv2.putText(vis_image, f"Overall Symmetry: {total_symmetry:.2f}", 
                    (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 保存结果
        output_dir = os.path.join(os.path.dirname(image_path), 'output')
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, 'nose_anatomical_analysis.jpg')
        cv2.imwrite(output_path, vis_image)
        
        logger.info(f"\n鼻部解剖学特征分析完成")
        logger.info(f"总体对称度: {total_symmetry:.2f}")
        logger.info(f"结果已保存至: {output_path}")
        
        return output_path
        
    except Exception as e:
        logger.error(f"分析失败: {str(e)}")
        raise

def main():
    """主函数"""
    # 使用标准测试图片
    test_image = "/Users/<USER>/beautifun/testdata/images/face_samples/test_face.jpg"
    
    try:
        output_path = analyze_nose_symmetry(test_image)
        logger.info(f"分析成功完成，结果保存在: {output_path}")
    except Exception as e:
        logger.error(f"分析失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
