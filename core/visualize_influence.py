#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import cv2
import numpy as np
import argparse
from PIL import Image
import json
import tempfile

def log(message):
    """打印日志信息"""
    print(f"[VISUALIZE] {message}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='生成参数影响区域可视化')
    parser.add_argument('--image', required=True, help='输入图像路径')
    parser.add_argument('--param', required=True, help='参数类型')
    parser.add_argument('--value', required=True, type=float, help='参数值')
    return parser.parse_args()

def get_nose_region(image_path):
    """获取鼻子区域的多边形点"""
    # 这里简化处理，使用固定的鼻子区域
    # 实际应用中应该根据面部特征点检测来确定鼻子区域
    img = cv2.imread(image_path)
    height, width = img.shape[:2]
    
    # 根据图像尺寸计算鼻子区域的大致位置
    center_x = width // 2
    center_y = height // 2
    
    # 鼻尖位置
    nose_tip_x = center_x
    nose_tip_y = center_y - height // 10
    
    # 鼻翼区域 - 左侧
    left_nostril_region = [
        (center_x - width // 8, center_y - height // 15),  # 左鼻翼外侧上方
        (center_x - width // 10, center_y),                # 左鼻翼外侧中间
        (center_x - width // 12, center_y + height // 20), # 左鼻翼外侧下方
        (center_x - width // 20, center_y + height // 15), # 左鼻孔下方
        (center_x, center_y),                              # 鼻子中心
        (center_x, center_y - height // 10),               # 鼻尖
    ]
    
    # 鼻翼区域 - 右侧
    right_nostril_region = [
        (center_x + width // 8, center_y - height // 15),  # 右鼻翼外侧上方
        (center_x + width // 10, center_y),                # 右鼻翼外侧中间
        (center_x + width // 12, center_y + height // 20), # 右鼻翼外侧下方
        (center_x + width // 20, center_y + height // 15), # 右鼻孔下方
        (center_x, center_y),                              # 鼻子中心
        (center_x, center_y - height // 10),               # 鼻尖
    ]
    
    # 鼻梁区域
    bridge_region = [
        (center_x - width // 15, center_y - height // 6),  # 左眉间下方
        (center_x + width // 15, center_y - height // 6),  # 右眉间下方
        (center_x + width // 20, center_y - height // 10), # 右鼻梁下方
        (center_x, center_y - height // 10),               # 鼻尖
        (center_x - width // 20, center_y - height // 10), # 左鼻梁下方
    ]
    
    # 鼻尖区域
    tip_region = [
        (center_x - width // 20, center_y - height // 10), # 左鼻尖
        (center_x + width // 20, center_y - height // 10), # 右鼻尖
        (center_x + width // 25, center_y - height // 20), # 右鼻尖下方
        (center_x, center_y),                              # 鼻子中心
        (center_x - width // 25, center_y - height // 20), # 左鼻尖下方
    ]
    
    # 鼻基底区域
    base_region = [
        (center_x - width // 10, center_y + height // 20), # 左鼻基底外侧
        (center_x + width // 10, center_y + height // 20), # 右鼻基底外侧
        (center_x + width // 12, center_y + height // 15), # 右鼻基底下方
        (center_x, center_y + height // 12),               # 鼻基底中心
        (center_x - width // 12, center_y + height // 15), # 左鼻基底下方
    ]
    
    regions = {
        'nostril_width': [left_nostril_region, right_nostril_region],
        'nostril_height': [left_nostril_region, right_nostril_region],
        'tip_width': [tip_region],
        'tip_height': [tip_region],
        'bridge_width': [bridge_region],
        'bridge_height': [bridge_region],
        'base_width': [base_region],
    }
    
    return regions

def create_influence_mask(image_path, param_type, param_value):
    """创建参数影响区域的掩码"""
    img = cv2.imread(image_path)
    if img is None:
        log(f"无法读取图像: {image_path}")
        return None
        
    height, width = img.shape[:2]
    
    # 创建空白掩码
    mask = np.zeros((height, width), dtype=np.uint8)
    
    # 获取鼻子区域
    regions = get_nose_region(image_path)
    
    if param_type not in regions:
        log(f"不支持的参数类型: {param_type}")
        return None
    
    # 填充区域
    for region in regions[param_type]:
        points = np.array(region, dtype=np.int32)
        cv2.fillPoly(mask, [points], 255)
    
    # 根据参数值调整颜色
    # 正值使用绿色（增大），负值使用红色（减小）
    color = (0, 255, 0) if param_value >= 0 else (0, 0, 255)
    
    # 创建彩色掩码
    color_mask = np.zeros((height, width, 4), dtype=np.uint8)
    
    # 设置RGB通道
    color_mask[mask > 0, 0] = color[2]  # R
    color_mask[mask > 0, 1] = color[1]  # G
    color_mask[mask > 0, 2] = color[0]  # B
    
    # 设置Alpha通道，使用渐变效果
    # 创建径向渐变
    center_x = width // 2
    center_y = height // 2
    
    for y in range(height):
        for x in range(width):
            if mask[y, x] > 0:
                # 计算到中心的距离
                dx = x - center_x
                dy = y - center_y
                distance = np.sqrt(dx*dx + dy*dy)
                
                # 根据距离设置透明度
                max_distance = np.sqrt(width*width + height*height) / 4
                alpha = int(128 * (1 - min(distance / max_distance, 1)))
                
                # 设置Alpha通道
                color_mask[y, x, 3] = alpha
    
    return color_mask

def main():
    """主函数"""
    args = parse_args()
    
    log(f"处理图像: {args.image}")
    log(f"参数类型: {args.param}")
    log(f"参数值: {args.value}")
    
    # 创建影响区域掩码
    influence_mask = create_influence_mask(args.image, args.param, args.value)
    if influence_mask is None:
        log("创建影响区域掩码失败")
        sys.exit(1)
    
    # 保存掩码为PNG图像（保留Alpha通道）
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
        output_path = temp_file.name
        cv2.imwrite(output_path, influence_mask)
        log(f"已保存影响区域掩码: {output_path}")
    
    # 输出图像路径，供Dart代码读取
    print(f"OUTPUT_IMAGE_PATH:{output_path}")

if __name__ == "__main__":
    main()
