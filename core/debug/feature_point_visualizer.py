#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

"""
特征点可视化工具

用于调试和验证面部特征点检测和参数映射
"""

import os
import sys
import cv2
import json
import logging
import tkinter as tk
from PIL import Image, ImageTk
from pathlib import Path
from tkinter import ttk
from datetime import datetime
import numpy as np

# 检查Python版本
if not (sys.version_info.major == 3 and sys.version_info.minor == 11):
    print("错误：此程序需要Python 3.11")
    sys.exit(1)

# 设置项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from core.face_mesh_processor import FaceMeshProcessor, FACIAL_AREAS
from core.feature.parameter_mapping import (
    FACIAL_AREA_CONFIGS,
    PointRole,
    TransformDirection,
    get_area_config,
    get_parameter_config
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 特征点类型定义
PRIMARY_POINTS = [
    33, 133, 362, 263,  # 眼睛
    61, 291,            # 瞳孔
    13, 14, 78, 308,    # 眉毛
    0, 17, 78, 292,     # 脸廓
    152, 148, 176, 149, # 唇线
    6, 197, 195, 248,   # 鼻子
]

# 特征点样式常量
POINT_STYLES = {
    'primary': {  # 主要特征点（红色，大）
        'color': (0, 0, 255),  # BGR格式
        'radius': 2,
        'thickness': -1,  # -1表示填充
        'glow_color': (0, 100, 255),  # 浅红色光晕
        'glow_radius': 4,
        'glow_thickness': 1
    },
    'secondary': {  # 次要控制点（绿色，中）
        'color': (0, 255, 0),
        'radius': 2,
        'thickness': -1,
        'glow_color': (100, 255, 100),
        'glow_radius': 3,
        'glow_thickness': 1
    },
    'auxiliary': {  # 辅助点（蓝色，小）
        'color': (255, 0, 0),
        'radius': 1,
        'thickness': -1,
        'glow_color': None,  # 无光晕
        'glow_radius': 0,
        'glow_thickness': 0
    }
}

class FeaturePointVisualizer:
    def __init__(self):
        """初始化可视化工具"""
        self.root = tk.Tk()
        self.root.title("面部特征调整调试工具")
        
        # 初始化FaceMeshProcessor
        self.face_processor = FaceMeshProcessor()
        self.landmarks = None
        self.facial_areas = None
        
        # 当前选择的区域和参数
        self.current_area = None  # 当前选中的区域
        self.current_parameter = None  # 当前选中的参数
        self.scale_factor = 1.0
        self.offset_x = 0
        self.offset_y = 0
        
        # 创建界面
        self.setup_ui()
        
        # 加载测试图片
        self.test_image = None
        self.original_image = None
        self.load_test_image()
        
    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="5")
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建区域名称到配置的映射
        self.area_name_to_key = {
            config.display_name: key 
            for key, config in FACIAL_AREA_CONFIGS.items()
        }
        
        # 区域选择
        ttk.Label(control_frame, text="选择区域:").grid(row=0, column=0, sticky=tk.W)
        self.area_var = tk.StringVar()
        self.area_combo = ttk.Combobox(control_frame, textvariable=self.area_var)
        self.area_combo['values'] = list(self.area_name_to_key.keys())
        self.area_combo.set("选择区域")
        self.area_combo.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.area_combo.bind('<<ComboboxSelected>>', self.on_area_selected)
        
        # 参数选择
        ttk.Label(control_frame, text="选择参数:").grid(row=2, column=0, sticky=tk.W, pady=(10,0))
        self.parameter_var = tk.StringVar()
        self.parameter_combo = ttk.Combobox(control_frame, textvariable=self.parameter_var)
        self.parameter_combo.set("选择参数")
        self.parameter_combo.grid(row=3, column=0, sticky=(tk.W, tk.E))
        self.parameter_combo.bind('<<ComboboxSelected>>', self.on_parameter_selected)

        # 预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="预览区域", padding="5")
        preview_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5)
        
        self.canvas = tk.Canvas(preview_frame, width=600, height=600)
        self.canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 右侧信息面板
        info_frame = ttk.LabelFrame(main_frame, text="特征点信息", padding="5")
        info_frame.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5)
        
        self.info_text = tk.Text(info_frame, width=40, height=30)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="调试日志", padding="5")
        log_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        self.log_text = tk.Text(log_frame, width=100, height=10)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def on_area_selected(self, *args):
        """处理区域选择事件"""
        selected_name = self.area_var.get()
        if selected_name == "选择区域":
            self.current_area = None
            self.current_parameter = None
            self.parameter_combo['values'] = []
            self.parameter_var.set("选择参数")
            self.update_preview()
            return
            
        # 通过显示名称找到区域键名
        area_key = self.area_name_to_key.get(selected_name)
        
        if area_key:
            # 更新当前区域
            self.current_area = area_key
            
            # 更新参数下拉框
            area_config = FACIAL_AREA_CONFIGS[area_key]
            self.parameter_combo['values'] = [
                param_config.display_name 
                for param_config in area_config.parameters.values()
            ]
            self.parameter_var.set("选择参数")  # 重置参数选择
            self.current_parameter = None
            self.log_message(f"选择区域: {selected_name}")
            
            # 只为当前选中的区域生成特征点图片
            self.generate_area_specific_image(area_key)
        else:
            self.parameter_combo['values'] = []
            self.parameter_var.set("选择参数")
            self.log_message(f"未找到区域: {selected_name}")
            
        self.update_preview()

    def on_parameter_selected(self, *args):
        """处理参数选择事件"""
        if not self.current_area:
            return
            
        selected_name = self.parameter_var.get()
        if selected_name == "选择参数":
            self.current_parameter = None
            self.update_preview()
            return
            
        # 查找参数配置
        area_config = FACIAL_AREA_CONFIGS[self.current_area]
        for key, param_config in area_config.parameters.items():
            if param_config.display_name == selected_name:
                self.current_parameter = key
                self.log_message(f"选择参数: {selected_name}")
                
                # 生成该参数的特征点图片
                self.generate_area_specific_image(self.current_area, self.current_parameter)
                break
                
        self.update_preview()

    def get_point_role(self, point_index):
        """获取特征点的角色"""
        if not self.current_area or not self.current_parameter:
            return None
            
        param_config = get_parameter_config(self.current_area, self.current_parameter)
        if point_index in param_config.primary_points:
            return PointRole.PRIMARY
        elif point_index in param_config.secondary_points:
            return PointRole.SECONDARY
        elif point_index in param_config.auxiliary_points:
            return PointRole.AUXILIARY
        return None

    def get_point_style(self, point_index):
        """获取特征点的样式"""
        # 如果选择了参数，根据点的角色显示不同样式
        if self.current_parameter:
            role = self.get_point_role(point_index)
            if role == PointRole.PRIMARY:
                return POINT_STYLES['primary']
            elif role == PointRole.SECONDARY:
                return POINT_STYLES['secondary']
            elif role == PointRole.AUXILIARY:
                return POINT_STYLES['auxiliary']
            return POINT_STYLES['auxiliary']  # 默认使用辅助点样式而不是返回 None
            
        # 如果只选择了区域，该区域的点用次要样式
        if self.current_area:
            area_config = FACIAL_AREA_CONFIGS[self.current_area]
            all_area_points = []
            # 添加区域边界点
            all_area_points.extend(area_config.region.boundary_points)
            # 添加所有参数相关的点
            for param_config in area_config.parameters.values():
                all_area_points.extend(param_config.primary_points)
                all_area_points.extend(param_config.secondary_points)
                all_area_points.extend(param_config.auxiliary_points)
                
            if point_index in all_area_points:
                return POINT_STYLES['secondary']
            
        # 主要特征点用主要样式
        if point_index in PRIMARY_POINTS:
            return POINT_STYLES['primary']
            
        # 其他点用辅助样式
        return POINT_STYLES['auxiliary']

    def should_show_point(self, point_index):
        """判断是否应该显示某个特征点"""
        # 如果没有选择区域，显示所有点
        if not self.current_area:
            return True
            
        # 如果选择了区域但没有选择参数，显示该区域的所有点
        if self.current_area and not self.current_parameter:
            area_config = FACIAL_AREA_CONFIGS[self.current_area]
            all_area_points = set()
            # 添加区域边界点
            all_area_points.update(area_config.region.boundary_points)
            # 添加中心点
            if area_config.region.center_point is not None:
                all_area_points.add(area_config.region.center_point)
            # 添加对称轴点
            if area_config.region.symmetric_axis is not None:
                all_area_points.update(area_config.region.symmetric_axis)
            # 添加所有参数相关的点
            for param_config in area_config.parameters.values():
                all_area_points.update(param_config.primary_points)
                all_area_points.update(param_config.secondary_points)
                all_area_points.update(param_config.auxiliary_points)
            return point_index in all_area_points
            
        # 如果选择了区域和参数，显示该参数对应的点和区域的基准点
        if self.current_area and self.current_parameter:
            area_config = FACIAL_AREA_CONFIGS[self.current_area]
            param_config = area_config.parameters[self.current_parameter]
            all_points = set()
            # 添加参数相关的点
            all_points.update(param_config.primary_points)
            all_points.update(param_config.secondary_points)
            all_points.update(param_config.auxiliary_points)
            # 添加区域的基准点
            if area_config.region.symmetric_axis is not None:
                all_points.update(area_config.region.symmetric_axis)
            return point_index in all_points
            
        return False

    def load_test_image(self):
        """加载测试图片并处理特征点"""
        self.log_message("开始加载测试图片和特征点数据...")
        
        try:
            test_image_path = os.path.join(project_root, "testdata", "test_face.jpg")
            self.log_message(f"尝试加载测试图片: {test_image_path}")
            
            if not os.path.exists(test_image_path):
                self.log_message(f"错误：测试图片不存在: {test_image_path}")
                return
            
            # 加载原始图片
            self.test_image = cv2.imread(test_image_path)
            if self.test_image is None:
                self.log_message(f"错误：无法读取图片: {test_image_path}")
                return

            # 获取原始图片尺寸
            original_height, original_width = self.test_image.shape[:2]
                
            # 调整图片大小以适应显示
            max_size = 600
            
            # 计算缩放比例，保持宽高比
            scale_x = max_size / original_width
            scale_y = max_size / original_height
            self.scale_factor = min(scale_x, scale_y)
            
            # 计算新尺寸
            new_width = int(original_width * self.scale_factor)
            new_height = int(original_height * self.scale_factor)
            
            # 调整图片大小
            resized_image = cv2.resize(self.test_image, (new_width, new_height))
            
            # 计算居中偏移
            self.offset_x = (max_size - new_width) // 2
            self.offset_y = (max_size - new_height) // 2
            
            # 保存原始图片和调整大小后的图片
            self.original_image = self.test_image.copy()  # 保存原始图片副本
            self.test_image = resized_image  # 更新为调整大小后的图片
                
            # 使用FaceMeshProcessor处理图片
            result = self.face_processor.process_image(test_image_path)
            
            if result["status"] != "success":
                self.log_message(f"错误：{result['message']}")
                return
                
            # 保存特征点数据，并调整坐标以匹配显示尺寸
            self.landmarks = []
            
            for landmark in result["landmarks"]:
                # 与 Flutter 端完全相同的坐标转换逻辑
                x = landmark['x'] * original_width * self.scale_factor + self.offset_x
                y = landmark['y'] * original_height * self.scale_factor + self.offset_y
                
                self.landmarks.append({
                    'index': landmark['index'],
                    'x': int(x),
                    'y': int(y),
                    'z': landmark['z'],
                    'visibility': landmark['visibility']
                })
            
            self.facial_areas = result["facial_areas"]
            
            self.log_message(f"成功加载测试图片，原始尺寸: {original_width}x{original_height}")
            self.log_message(f"缩放后尺寸: {new_width}x{new_height}, 缩放比例: {self.scale_factor:.2f}")
            self.log_message(f"偏移量: x={self.offset_x}, y={self.offset_y}")
            self.log_message(f"检测到 {len(self.landmarks)} 个特征点")
            self.update_preview()
            
        except Exception as e:
            self.log_message(f"加载图片时发生错误: {str(e)}")

    def update_preview(self):
        """更新预览窗口"""
        if self.test_image is None or not self.landmarks:
            return
            
        # 创建一个黑色背景
        max_size = 600
        preview = np.zeros((max_size, max_size, 3), dtype=np.uint8)
        
        # 将调整后的图片放在中心位置
        h, w = self.test_image.shape[:2]
        preview[self.offset_y:self.offset_y+h, self.offset_x:self.offset_x+w] = self.test_image
        
        # 绘制特征点
        for landmark in self.landmarks:
            # 检查是否应该显示这个点
            if not self.should_show_point(landmark['index']):
                continue
                
            x, y = landmark['x'], landmark['y']
            point_style = self.get_point_style(landmark['index'])
                
            # 绘制特征点
            cv2.circle(
                preview, 
                (x, y), 
                point_style['radius'], 
                point_style['color'], 
                point_style['thickness']
            )
                
            # 绘制光晕效果（如果有）
            if point_style['glow_radius'] > 0:
                cv2.circle(
                    preview,
                    (x, y),
                    point_style['glow_radius'],
                    point_style['glow_color'],
                    point_style['glow_thickness']
                )
            
        # 转换为RGB显示
        preview = cv2.cvtColor(preview, cv2.COLOR_BGR2RGB)
        
        # 转换为TK图像
        image = Image.fromarray(preview)
        photo = ImageTk.PhotoImage(image)
        
        # 更新画布
        self.canvas.config(width=max_size, height=max_size)
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=photo)
        self.canvas.image = photo  # 保持引用
        
    def log_message(self, message):
        """记录日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        logger.info(message)
        
    def run(self):
        """运行可视化工具"""
        self.root.mainloop()
        
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'face_processor'):
            logger.info("清理FaceMeshProcessor资源")

    def generate_landmark_index_image(self, output_path=None):
        """生成带有所有特征点索引标注的图片

        Args:
            output_path: 输出图片路径，默认为 testdata/landmark_indices.jpg
        """
        if self.test_image is None:
            self.log_message("错误：未加载测试图片")
            return

        # 如果未指定输出路径，使用默认路径
        if output_path is None:
            output_path = os.path.join(project_root, "testdata", "landmark_indices.jpg")

        # 创建图片副本
        image = self.test_image.copy()
        
        # 放大图片以便更好地显示标注
        scale = 2.0  # 放大2倍
        width = int(image.shape[1] * scale)
        height = int(image.shape[0] * scale)
        image = cv2.resize(image, (width, height))

        # 处理图片获取特征点
        results = self.face_processor.process_image(os.path.join(project_root, "testdata", "test_face.jpg"))
        
        if results['status'] != 'success':
            self.log_message(f"错误：特征点检测失败 - {results.get('message', '未知错误')}")
            return

        # 获取特征点
        landmarks = results['landmarks']
        
        # 在图片上标注每个特征点
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.3
        font_thickness = 1
        
        for landmark in landmarks:
            # 计算放大后的坐标
            x = int(landmark['x'] * width)
            y = int(landmark['y'] * height)
            
            # 绘制特征点
            cv2.circle(image, (x, y), 1, (0, 0, 255), -1)
            
            # 计算文本框大小以优化标签位置
            text = str(landmark['index'])
            (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, font_thickness)
            
            # 根据点的位置调整文本位置，避免重叠
            text_x = x - text_width // 2
            text_y = y - 5  # 文本显示在点的上方
            
            # 绘制白色背景提高可读性
            cv2.rectangle(image, 
                        (text_x - 1, text_y - text_height - 1),
                        (text_x + text_width + 1, text_y + 1),
                        (255, 255, 255),
                        -1)
            
            # 绘制索引号
            cv2.putText(image, text, (text_x, text_y),
                       font, font_scale, (0, 0, 0), font_thickness)

        # 保存图片
        cv2.imwrite(output_path, image)
        self.log_message(f"已生成特征点索引图片：{output_path}")
        return output_path

    def generate_area_specific_image(self, area_name: str, parameter_name: str = None, output_path=None):
        """生成特定区域的特征点图像"""
        # 确保测试图片已加载
        if self.original_image is None:
            self.load_test_image()
            if self.original_image is None:
                self.log_message("错误：无法加载测试图片")
                return

        # 检查landmarks是否已加载
        if not self.landmarks:
            self.log_message("错误：未加载特征点数据")
            return
        self.log_message(f"已加载 {len(self.landmarks)} 个特征点")

        # 创建图片副本
        image = self.original_image.copy()
        original_height, original_width = image.shape[:2]

        # 放大图片以便更清晰地显示标注
        scale = 2.0
        width = int(original_width * scale)
        height = int(original_height * scale)
        image = cv2.resize(image, (width, height))

        # 确定输出路径
        if output_path is None:
            # 使用固定的文件名，这样每次生成都会覆盖之前的图片
            filename = "current_feature_points.jpg"
            output_path = os.path.join(project_root, "testdata", "debug", filename)
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 获取需要显示的特征点
        display_points = []
        if area_name and area_name in FACIAL_AREA_CONFIGS:
            area_config = FACIAL_AREA_CONFIGS[area_name]
            self.log_message(f"找到区域配置：{area_name}")
            
            if parameter_name and parameter_name in area_config.parameters:
                # 显示特定参数的点
                param_config = area_config.parameters[parameter_name]
                points = param_config.primary_points + param_config.secondary_points + param_config.auxiliary_points
                self.log_message(f"参数 {parameter_name} 包含 {len(points)} 个特征点")
                display_points = [p for p in self.landmarks if p['index'] in points]
            else:
                # 显示整个区域的点
                points = []
                for param_name, param_config in area_config.parameters.items():
                    param_points = param_config.primary_points + param_config.secondary_points + param_config.auxiliary_points
                    points.extend(param_points)
                    self.log_message(f"参数 {param_name} 包含 {len(param_points)} 个特征点")
                display_points = [p for p in self.landmarks if p['index'] in points]

        self.log_message(f"找到 {len(display_points)} 个需要显示的特征点")
        if not display_points:
            self.log_message("错误：未找到要显示的特征点")
            return

        # 在图片上标注特征点
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        font_thickness = 2

        for point in display_points:
            # 计算放大后的坐标（移除偏移量的影响）
            x = int((point['x'] - self.offset_x) / self.scale_factor * scale)
            y = int((point['y'] - self.offset_y) / self.scale_factor * scale)
            
            self.log_message(f"绘制特征点 {point['index']} 在位置 ({x}, {y})")
            
            # 绘制特征点（红色，更大的圆点）
            cv2.circle(image, (x, y), 3, (0, 0, 255), -1)
            
            # 绘制索引号（带白色背景）
            text = str(point['index'])
            (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, font_thickness)
            
            # 绘制白色背景
            margin = 4
            cv2.rectangle(image, 
                        (x - text_width//2 - margin, y - text_height - margin - 5),
                        (x + text_width//2 + margin, y - margin),
                        (255, 255, 255),
                        -1)
            
            # 绘制黑色文本
            cv2.putText(image, text,
                       (x - text_width//2, y - margin - 3),
                       font, font_scale, (0, 0, 0), font_thickness)

        # 添加标题
        title = f"区域: {area_name}"
        if parameter_name:
            title += f" - 参数: {parameter_name}"
        cv2.putText(image, title, (20, 40), font, 1.0, (0, 0, 0), 2)

        # 保存图片
        cv2.imwrite(output_path, image)
        self.log_message(f"已生成区域特征点图片：{output_path}")
        return output_path

if __name__ == "__main__":
    visualizer = FeaturePointVisualizer()
    
    # 加载测试图片和特征点数据
    visualizer.load_test_image()
    
    # 生成鼻子区域的特征点图片
    visualizer.generate_area_specific_image("nose")
    
    # 生成眼睛区域的特征点图片
    visualizer.generate_area_specific_image("eyes")
    
    # 运行可视化工具
    visualizer.run()
