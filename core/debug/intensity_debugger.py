#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

"""
变形烈度调试工具
提供GUI界面用于实时调整和测试变形参数
"""

import sys
import logging
import platform
from typing import Dict, Optional
from pathlib import Path

# 严格的版本检查
def check_python_version():
    """检查 Python 版本，确保是 3.11"""
    version_info = sys.version_info
    if version_info.major != 3 or version_info.minor != 11:
        error_msg = f"""
错误：需要 Python 3.11
当前版本: Python {platform.python_version()}
执行路径: {sys.executable}

请使用以下命令运行此程序：
python3.11 {__file__}
"""
        logging.error(error_msg)
        sys.exit(1)
    
    logging.info(f"Python 版本验证通过: {platform.python_version()}")
    logging.info(f"Python 路径: {sys.executable}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 在导入任何其他模块之前检查版本
check_python_version()

# 尝试导入必要的模块
try:
    import tkinter as tk
    from tkinter import ttk
    import json
except ImportError as e:
    logger.error(f"导入错误: {e}")
    logger.error("请确保使用 Python 3.11 安装了所有必要的包")
    sys.exit(1)

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 尝试导入项目模块
try:
    from core.feature.parameter_mapping import (
        FACIAL_AREA_CONFIGS,
        get_area_config,
        get_parameter_config,
        CurveType
    )
except ImportError as e:
    logger.error(f"项目模块导入错误: {e}")
    logger.error("请确保在正确的项目目录中运行此程序")
    sys.exit(1)

class IntensityDebugger:
    """变形烈度调试器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("变形参数调试工具")
        self.root.geometry("1000x800")
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="20")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 左侧区域选择和参数选择框架
        left_frame = ttk.LabelFrame(self.main_frame, text="区域和参数选择", padding="10")
        left_frame.grid(row=0, column=0, padx=10, pady=5, sticky=(tk.N, tk.W))
        
        # 区域选择
        ttk.Label(left_frame, text="选择区域:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.area_combobox = ttk.Combobox(left_frame, state="readonly", width=20)
        self.area_combobox.grid(row=0, column=1, sticky=tk.W, pady=5)
        self.area_combobox["values"] = self.get_area_names()
        self.area_combobox.bind("<<ComboboxSelected>>", self.on_area_selected)
        
        # 参数选择
        ttk.Label(left_frame, text="选择参数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.param_combobox = ttk.Combobox(left_frame, state="readonly", width=20)
        self.param_combobox.grid(row=1, column=1, sticky=tk.W, pady=5)
        self.param_combobox.bind("<<ComboboxSelected>>", self.on_param_selected)
        
        # 右侧参数调整框架
        right_frame = ttk.LabelFrame(self.main_frame, text="参数调整", padding="10")
        right_frame.grid(row=0, column=1, padx=10, pady=5, sticky=(tk.N, tk.W))
        
        # 步进值调整
        ttk.Label(right_frame, text="步进值:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.step_size = tk.StringVar(value="0.01")
        self.step_entry = ttk.Entry(right_frame, textvariable=self.step_size, width=10)
        self.step_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # 当前值调整
        ttk.Label(right_frame, text="当前值:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.current_value = tk.StringVar(value="0.0")
        self.value_spinbox = ttk.Spinbox(
            right_frame,
            from_=-1.0,
            to=1.0,
            increment=0.01,
            textvariable=self.current_value,
            width=10
        )
        self.value_spinbox.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 曲线类型选择
        ttk.Label(right_frame, text="曲线类型:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.curve_type = tk.StringVar(value="LINEAR")
        curve_combobox = ttk.Combobox(
            right_frame,
            textvariable=self.curve_type,
            values=["线性", "缓入", "缓出", "S型", "指数", "对数"],
            state="readonly",
            width=10
        )
        curve_combobox.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 帮助说明
        help_frame = ttk.LabelFrame(self.main_frame, text="使用说明", padding="10")
        help_frame.grid(row=1, column=0, columnspan=2, padx=10, pady=10, sticky=(tk.W, tk.E))
        
        help_text = """使用说明：
1. 选择要调试的面部区域（如：鼻子、眼睛等）
2. 选择该区域下的具体参数（如：鼻梁高度、鼻尖长度等）
3. 调整步进值：
   · 值越大，每次点击变化越明显
   · 值越小，调整越精细
   · 根据参数特性选择合适的步进值
4. 使用增减按钮或直接输入调整当前值
5. 选择合适的曲线类型以获得不同的变化效果

注意：每个参数都有独立的步进值设置，请根据实际视觉效果调整"""
        
        ttk.Label(
            help_frame,
            text=help_text,
            justify=tk.LEFT,
            font=('SimHei', 12)
        ).grid(row=0, column=0, sticky=tk.W)
        
        # 初始化状态
        self.current_area = None
        self.current_param = None
        self.param_configs = {}
        
    def on_area_selected(self, event):
        """区域选择事件处理"""
        area = self.area_combobox.get()
        self.current_area = area
        self.param_configs = self.get_area_parameters(area)
        self.param_combobox["values"] = [param.display_name for param in self.param_configs.values()]
        self.param_combobox.set("")  # 清空当前选择
        
    def on_param_selected(self, event):
        """参数选择事件处理"""
        if not self.current_area or not self.param_combobox.get():
            return
            
        param_name = self.param_combobox.get()
        param_config = next(
            (p for p in self.param_configs.values() if p.display_name == param_name),
            None
        )
        
        if param_config:
            self.current_param = param_config
            # 更新值范围和步进值
            self.value_spinbox.configure(
                from_=param_config.min_value,
                to=param_config.max_value
            )
            # 设置默认步进值（可以根据参数特性设置不同的默认值）
            default_step = (param_config.max_value - param_config.min_value) / 100
            self.step_size.set(f"{default_step:.3f}")
            
    def get_area_names(self):
        """获取所有区域名称"""
        return [config.display_name for config in FACIAL_AREA_CONFIGS.values()]
        
    def get_area_parameters(self, area_name):
        """获取指定区域的参数配置"""
        area_config = next(
            (config for config in FACIAL_AREA_CONFIGS.values() 
             if config.display_name == area_name),
            None
        )
        return area_config.parameters if area_config else {}

    def run(self):
        """运行调试器"""
        logger.info("启动变形烈度调试工具")
        self.root.mainloop()

def main():
    """主函数"""
    debugger = IntensityDebugger()
    debugger.run()

if __name__ == '__main__':
    main()
