#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 MediaPipe 面部特征点检测

此脚本用于测试 MediaPipe 面部特征点检测功能，
可以直接检测图片中的面部特征点，也可以生成模拟数据用于测试。
"""

import os
import sys
import json
import random
import argparse
from typing import List, Dict, Any, Optional

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='测试 MediaPipe 面部特征点检测')
    parser.add_argument('--image', type=str, help='输入图片路径')
    parser.add_argument('--width', type=float, help='图片宽度')
    parser.add_argument('--height', type=float, help='图片高度')
    parser.add_argument('--mock', action='store_true', help='使用模拟数据')
    
    return parser.parse_args()

def generate_mock_landmarks(width: float, height: float) -> List[Dict[str, Any]]:
    """生成模拟的面部特征点数据
    
    Args:
        width: 图片宽度
        height: 图片高度
        
    Returns:
        模拟的面部特征点数据列表
    """
    landmarks = []
    
    # 关键特征点描述
    key_landmarks = {
        0: '额头中心',
        1: '左眉外侧',
        2: '左眉内侧',
        3: '鼻尖',
        4: '右眉内侧',
        5: '右眉外侧',
        6: '左眼外角',
        7: '左眼内角',
        8: '右眼内角',
        9: '右眼外角',
        10: '左脸颊',
        11: '右脸颊',
        12: '嘴左角',
        13: '嘴右角',
        14: '上唇中心',
        15: '下唇中心',
        16: '下巴',
        17: '下颌线'
    }
    
    # 关键特征点位置（相对坐标，范围 0.0-1.0）
    key_positions = [
        (0.5, 0.2),    # 额头中心
        (0.35, 0.3),   # 左眉外侧
        (0.45, 0.3),   # 左眉内侧
        (0.5, 0.4),    # 鼻尖
        (0.55, 0.3),   # 右眉内侧
        (0.65, 0.3),   # 右眉外侧
        (0.35, 0.35),  # 左眼外角
        (0.45, 0.35),  # 左眼内角
        (0.55, 0.35),  # 右眼内角
        (0.65, 0.35),  # 右眼外角
        (0.3, 0.5),    # 左脸颊
        (0.7, 0.5),    # 右脸颊
        (0.4, 0.6),    # 嘴左角
        (0.6, 0.6),    # 嘴右角
        (0.5, 0.55),   # 上唇中心
        (0.5, 0.65),   # 下唇中心
        (0.5, 0.8),    # 下巴
        (0.3, 0.7)     # 下颌线
    ]
    
    # 生成关键特征点
    for i, (x, y) in enumerate(key_positions):
        landmarks.append({
            'x': x * width,
            'y': y * height,
            'z': random.uniform(-5, 5),
            'visibility': random.uniform(0.9, 1.0),
            'isPrimary': True,
            'description': key_landmarks.get(i)
        })
    
    # 生成次要特征点
    for i in range(50):
        landmarks.append({
            'x': random.uniform(0.2, 0.8) * width,
            'y': random.uniform(0.2, 0.8) * height,
            'z': random.uniform(-10, 10),
            'visibility': random.uniform(0.5, 1.0),
            'isPrimary': False,
            'description': None
        })
    
    return landmarks

def detect_facial_features(image_path: str, width: float, height: float) -> List[Dict[str, Any]]:
    """检测图片中的面部特征点
    
    Args:
        image_path: 图片路径
        width: 图片宽度
        height: 图片高度
        
    Returns:
        面部特征点数据列表
    """
    # 这里应该调用 MediaPipe 进行实际的特征点检测
    # 但由于这只是一个测试脚本，我们直接返回模拟数据
    return generate_mock_landmarks(width, height)

def main() -> None:
    """主函数"""
    args = parse_arguments()
    
    try:
        # 如果使用模拟数据
        if args.mock:
            if not args.width or not args.height:
                width = 1000
                height = 1000
            else:
                width = args.width
                height = args.height
            
            landmarks = generate_mock_landmarks(width, height)
            print(json.dumps(landmarks))
            return
        
        # 如果检测图片中的特征点
        if not args.image or not args.width or not args.height:
            print(json.dumps([]))
            return
        
        # 检测特征点
        landmarks = detect_facial_features(args.image, args.width, args.height)
        print(json.dumps(landmarks))
    
    except Exception as e:
        # 输出错误信息
        error_data = {
            'error': str(e),
            'args': vars(args)
        }
        print(json.dumps(error_data), file=sys.stderr)
        sys.exit(1)

if __name__ == '__main__':
    main()
