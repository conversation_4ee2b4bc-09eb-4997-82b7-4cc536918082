  /// 水平变形后更新特征点位置，返回更新后的特征点列表
  List<FeaturePoint>? _updateFeaturePointsAfterHorizontalDeformation(double centerX, double centerY, double radius, 
      double deformationFactor, double scaleX, double scaleY) {
    Logger.flowStart(_logTag, '_updateFeaturePointsAfterHorizontalDeformation');
    
    if (_featurePointManager == null) {
      Logger.flowWarning(_logTag, '_updateFeaturePointsAfterHorizontalDeformation', '特征点管理器为空，无法更新特征点');
      Logger.flowEnd(_logTag, '_updateFeaturePointsAfterHorizontalDeformation');
      return null;
    }
    
    List<FeaturePoint>? featurePoints = _featurePointManager.getFeaturePoints();
    if (featurePoints == null || featurePoints.isEmpty) {
      Logger.flowWarning(_logTag, '_updateFeaturePointsAfterHorizontalDeformation', '特征点列表为空，无法更新特征点');
      Logger.flowEnd(_logTag, '_updateFeaturePointsAfterHorizontalDeformation');
      return null;
    }
    
    // 使用传入的鼻翼中心点和半径
    Logger.flow(_logTag, '_updateFeaturePointsAfterHorizontalDeformation', '使用鼻翼中心点: ($centerX, $centerY), 半径: $radius');
    
    // 调整变形系数，使效果更加自然
    final double enhancedFactor = deformationFactor * 0.1;
    Logger.flow(_logTag, '_updateFeaturePointsAfterHorizontalDeformation', '变形系数: $deformationFactor, 增强系数: $enhancedFactor');
    
    // 创建特征点的副本
    List<FeaturePoint> updatedFeaturePoints = [];
    for (final point in featurePoints) {
      updatedFeaturePoints.add(FeaturePoint(
        index: point.index,
        x: point.x,
        y: point.y,
        id: point.id,
      ));
    }
    
    // 更新特征点位置
    bool anyPointUpdated = false;
    for (int i = 0; i < updatedFeaturePoints.length; i++) {
      final point = updatedFeaturePoints[i];
      
      // 将特征点坐标转换为画布坐标
      final scaledX = point.x * scaleX;
      final scaledY = point.y * scaleY;
      
      // 计算点到鼻翼中心的距离
      final dx = scaledX - centerX;
      final dy = scaledY - centerY;
      final distance = math.sqrt(dx * dx + dy * dy);
      
      // 如果特征点在变形区域内
      if (distance < radius) {
        // 计算基于距离的衰减因子 (0-1)，距离越远衰减越大
        final double distanceRatio = distance / radius;
        final double distanceFactor = math.max(0, 1 - distanceRatio);
        
        // 使用平方衰减，使变形更加集中在鼻翼区域
        final double smoothFactor = distanceFactor * distanceFactor;
        
        // 计算点到鼻翼中心的水平距离
        final double horizontalDistance = dx.abs();
        
        // 计算水平方向的位移量
        double offsetX = 0.0;
        
        // 根据点的位置（左侧或右侧）决定变形方向
        if (dx > 0) { // 在中心点右侧
          // 正值缩小，负值扩大
          offsetX = deformationFactor > 0 ? 
              -horizontalDistance * smoothFactor * enhancedFactor : // 缩小（向中心线移动）
              horizontalDistance * smoothFactor * enhancedFactor;   // 扩大（远离中心线）
        } else if (dx < 0) { // 在中心点左侧
          // 正值缩小，负值扩大
          offsetX = deformationFactor > 0 ? 
              horizontalDistance * smoothFactor * enhancedFactor :  // 缩小（向中心线移动）
              -horizontalDistance * smoothFactor * enhancedFactor;  // 扩大（远离中心线）
        }
        
        // 计算新的X坐标（画布坐标）
        final newScaledX = scaledX + offsetX;
        
        // 转换回原始图像坐标
        final newX = newScaledX / scaleX;
        
        // 更新特征点位置
        updatedFeaturePoints[i] = FeaturePoint(
          index: point.index,
          x: newX,
          y: point.y,
          id: point.id,
        );
        
        // 如果是当前参数相关的特征点，记录日志
        if (_currentParameterPointIndexes.contains(point.index)) {
          Logger.flow(_logTag, '_updateFeaturePointsAfterHorizontalDeformation', 
              '更新特征点 ${point.index}: (${point.x}, ${point.y}) -> ($newX, ${point.y}), 偏移: ${offsetX / scaleX}, 衰减因子: $smoothFactor');
          anyPointUpdated = true;
        }
      }
    }
    
    if (!anyPointUpdated) {
      Logger.flowWarning(_logTag, '_updateFeaturePointsAfterHorizontalDeformation', '没有特征点被更新');
    } else {
      Logger.flow(_logTag, '_updateFeaturePointsAfterHorizontalDeformation', '特征点更新完成');
    }
    
    Logger.flowEnd(_logTag, '_updateFeaturePointsAfterHorizontalDeformation');
    return updatedFeaturePoints;
  }
