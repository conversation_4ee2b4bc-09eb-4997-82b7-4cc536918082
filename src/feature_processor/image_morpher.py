"""
图像变形模块，用于生成渐变图像序列
"""

import cv2
import numpy as np
from scipy.spatial import Delaunay
from typing import Dict, List, Tuple, Optional, Callable

class ImageMorpher:
    """
    图像变形器，用于生成基于特征的渐变图像序列
    """
    
    def __init__(self, smooth_sigma: float = 0.5):
        """
        初始化图像变形器
        
        Args:
            smooth_sigma: 平滑处理的高斯核标准差
        """
        self.smooth_sigma = smooth_sigma
        
    def generate_morphed_sequence(
        self,
        source_image: np.ndarray,
        target_image: np.ndarray,
        feature_sequence: List[Dict],
        steps: int = 5,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[np.ndarray]:
        """
        生成渐变图像序列
        
        Args:
            source_image: 源图像
            target_image: 目标图像
            feature_sequence: 特征序列，每个元素包含特征点、区域和轮廓信息
            steps: 生成的图像数量
            progress_callback: 进度回调函数，接收当前步骤和总步骤数
            
        Returns:
            渐变图像序列
            
        Raises:
            ValueError: 当输入参数无效时
        """
        # 验证输入
        if len(feature_sequence) == 0:
            raise ValueError("特征序列不能为空")
            
        if source_image.shape != target_image.shape:
            raise ValueError("源图像和目标图像的尺寸必须相同")
            
        # 验证特征序列中的特征点
        for features in feature_sequence:
            required_keys = ['source_landmarks_2d', 'target_landmarks_2d', 'landmarks_2d']
            if not all(key in features for key in required_keys):
                raise ValueError("特征字典缺少必要的特征点数据")
        
        # 初始化结果列表
        morphed_sequence = []
        
        # 对每个特征步骤生成对应的变形图像
        total_steps = len(feature_sequence)
        for i, features in enumerate(feature_sequence):
            # 更新进度
            if progress_callback:
                progress_callback(i + 1, total_steps)
            
            # 计算当前的权重
            weight = i / (total_steps - 1)
            
            # 生成当前帧的变形图像
            try:
                morphed_image = self._generate_morphed_image(
                    source_image,
                    target_image,
                    features,
                    weight
                )
                
                # 应用颜色校正
                morphed_image = self._color_correct(
                    source_image,
                    target_image,
                    morphed_image,
                    weight
                )
                
                # 应用边缘平滑
                if self.smooth_sigma > 0:
                    morphed_image = cv2.GaussianBlur(
                        morphed_image,
                        (0, 0),
                        self.smooth_sigma
                    )
                
                morphed_sequence.append(morphed_image)
            except Exception as e:
                raise ValueError(f"生成第{i}帧图像时出错：{str(e)}")
            
        return morphed_sequence
    
    def _generate_morphed_image(
        self,
        source_image: np.ndarray,
        target_image: np.ndarray,
        features: Dict,
        weight: float
    ) -> np.ndarray:
        """
        生成单帧变形图像
        
        Args:
            source_image: 源图像
            target_image: 目标图像
            features: 当前帧的特征信息
            weight: 当前帧的权重（0-1之间）
            
        Returns:
            变形后的图像
            
        Raises:
            ValueError: 当特征点数据无效时
        """
        # 验证权重范围
        if not 0 <= weight <= 1:
            raise ValueError("权重必须在0到1之间")
            
        # 验证特征字典
        required_keys = ['source_landmarks_2d', 'target_landmarks_2d', 'landmarks_2d']
        for key in required_keys:
            if key not in features:
                raise ValueError(f"特征字典缺少必要的键：{key}")
        
        # 提取特征点
        source_points = features['source_landmarks_2d']
        target_points = features['target_landmarks_2d']
        current_points = features['landmarks_2d']
        
        # 验证特征点是否为numpy数组
        if not all(isinstance(points, np.ndarray) for points in [source_points, target_points, current_points]):
            raise ValueError("特征点必须是numpy数组")
            
        # 验证特征点形状
        if not all(points.ndim == 2 and points.shape[1] == 2 for points in [source_points, target_points, current_points]):
            raise ValueError("特征点数组必须是Nx2的形状")
        
        # 验证特征点数量一致
        if len(source_points) != len(target_points) or len(source_points) != len(current_points):
            raise ValueError(f"特征点数量不一致：源点={len(source_points)}, 目标点={len(target_points)}, 当前点={len(current_points)}")
            
        # 验证特征点数量足够进行三角剖分
        if len(current_points) < 3:
            raise ValueError("特征点数量必须至少为3个才能进行三角剖分")
        
        # 获取图像尺寸
        height, width = source_image.shape[:2]
        
        # 添加图像边界点以改善边缘效果
        border_points = np.array([
            [0, 0], [width//2, 0], [width-1, 0],  # 上边界
            [width-1, height//2], [width-1, height-1],  # 右边界
            [width//2, height-1], [0, height-1],  # 下边界
            [0, height//2]  # 左边界
        ])
        
        # 将边界点添加到特征点中
        source_points_with_border = np.vstack([source_points, border_points])
        target_points_with_border = np.vstack([target_points, border_points])
        current_points_with_border = np.vstack([current_points, border_points])
        
        # 创建三角剖分
        try:
            triangulation = self._create_triangulation(current_points_with_border)
            print(f"三角形数量：{len(triangulation.simplices)}")
        except Exception as e:
            raise ValueError(f"创建三角剖分失败：{str(e)}")
        
        # 初始化结果图像和权重图
        morphed_image = np.zeros((height, width, 3), dtype=np.float32)
        weight_map = np.zeros((height, width), dtype=np.float32)
        
        # 对每个三角形进行变形
        for i, triangle in enumerate(triangulation.simplices):
            try:
                # 获取三角形顶点
                src_tri = source_points_with_border[triangle]
                dst_tri = target_points_with_border[triangle]
                curr_tri = current_points_with_border[triangle]
                
                # 计算三角形的包围盒
                min_x = int(max(0, np.floor(np.min(curr_tri[:, 0]))))
                max_x = int(min(width - 1, np.ceil(np.max(curr_tri[:, 0]))))
                min_y = int(max(0, np.floor(np.min(curr_tri[:, 1]))))
                max_y = int(min(height - 1, np.ceil(np.max(curr_tri[:, 1]))))
                
                # 计算仿射变换矩阵
                src_mat = cv2.getAffineTransform(src_tri.astype(np.float32), curr_tri.astype(np.float32))
                dst_mat = cv2.getAffineTransform(dst_tri.astype(np.float32), curr_tri.astype(np.float32))
                
                # 对当前三角形区域进行变形
                for y in range(min_y, max_y + 1):
                    for x in range(min_x, max_x + 1):
                        point = np.array([x, y])
                        if self._point_in_triangle(point, curr_tri):
                            # 计算源图像和目标图像中的对应点
                            src_point = self._transform_point(point, cv2.invertAffineTransform(src_mat))
                            dst_point = self._transform_point(point, cv2.invertAffineTransform(dst_mat))
                            
                            # 如果点在图像范围内，进行插值
                            if (0 <= src_point[0] < width and 0 <= src_point[1] < height and
                                0 <= dst_point[0] < width and 0 <= dst_point[1] < height):
                                # 双线性插值获取源图像和目标图像的颜色
                                src_color = self._bilinear_interpolate(source_image, src_point)
                                dst_color = self._bilinear_interpolate(target_image, dst_point)
                                
                                # 计算当前点到三角形边缘的距离作为权重
                                dist = self._point_to_triangle_edge_distance(point, curr_tri)
                                w = min(1.0, dist / 10.0)  # 10像素的过渡区域
                                
                                # 根据权重混合颜色
                                color = (1 - weight) * src_color + weight * dst_color
                                
                                # 累积颜色和权重
                                morphed_image[y, x] += color * w
                                weight_map[y, x] += w
                                
            except Exception as e:
                print(f"处理第{i}个三角形时出错：{str(e)}")
                continue
        
        # 标准化图像
        valid_mask = weight_map > 0
        morphed_image[valid_mask] /= weight_map[valid_mask, np.newaxis]
        morphed_image[~valid_mask] = 0
        
        # 应用平滑处理
        morphed_image = cv2.GaussianBlur(morphed_image, (3, 3), self.smooth_sigma)
        
        return morphed_image.astype(np.uint8)
        
    def _point_in_triangle(self, point, triangle):
        """判断点是否在三角形内"""
        def sign(p1, p2, p3):
            return (p1[0] - p3[0]) * (p2[1] - p3[1]) - (p2[0] - p3[0]) * (p1[1] - p3[1])
            
        d1 = sign(point, triangle[0], triangle[1])
        d2 = sign(point, triangle[1], triangle[2])
        d3 = sign(point, triangle[2], triangle[0])
        
        has_neg = (d1 < 0) or (d2 < 0) or (d3 < 0)
        has_pos = (d1 > 0) or (d2 > 0) or (d3 > 0)
        
        return not (has_neg and has_pos)
        
    def _transform_point(self, point, matrix):
        """使用仿射变换矩阵变换点的坐标"""
        x, y = point
        new_x = matrix[0,0] * x + matrix[0,1] * y + matrix[0,2]
        new_y = matrix[1,0] * x + matrix[1,1] * y + matrix[1,2]
        return np.array([new_x, new_y])
        
    def _bilinear_interpolate(self, img, point):
        """双线性插值获取图像中任意点的颜色"""
        x, y = point
        x1, y1 = int(x), int(y)
        x2, y2 = x1 + 1, y1 + 1
        
        # 计算插值权重
        wa = (x2 - x) * (y2 - y)
        wb = (x - x1) * (y2 - y)
        wc = (x2 - x) * (y - y1)
        wd = (x - x1) * (y - y1)
        
        # 获取周围四个点的颜色
        if x2 >= img.shape[1]: x2 = x1
        if y2 >= img.shape[0]: y2 = y1
        
        color = (wa * img[y1,x1] + wb * img[y1,x2] + 
                wc * img[y2,x1] + wd * img[y2,x2])
                
        return color

    def _point_to_triangle_edge_distance(self, point, triangle):
        """计算点到三角形边的最小距离"""
        def point_to_line_distance(p, a, b):
            # 计算点到线段的距离
            ab = b - a
            ap = p - a
            proj = np.dot(ap, ab) / np.dot(ab, ab)
            if proj < 0:
                return np.linalg.norm(p - a)
            elif proj > 1:
                return np.linalg.norm(p - b)
            else:
                return np.linalg.norm(ap - proj * ab)
                
        # 计算到三条边的距离
        d1 = point_to_line_distance(point, triangle[0], triangle[1])
        d2 = point_to_line_distance(point, triangle[1], triangle[2])
        d3 = point_to_line_distance(point, triangle[2], triangle[0])
        
        return min(d1, d2, d3)

    def _create_triangulation(self, points: np.ndarray) -> Delaunay:
        """
        创建特征点的Delaunay三角剖分
        
        Args:
            points: 特征点坐标数组
            
        Returns:
            三角剖分对象
            
        Raises:
            ValueError: 当特征点数量不足或无效时
        """
        if len(points) < 3:
            raise ValueError("特征点数量必须大于等于3个才能进行三角剖分")
            
        try:
            # 添加图像边界点以确保完整覆盖
            rect = cv2.boundingRect(points.astype(np.float32))
            subdiv = Delaunay(points)
            return subdiv
        except Exception as e:
            raise ValueError(f"三角剖分失败：{str(e)}")
            
    def _color_correct(
        self,
        source_image: np.ndarray,
        target_image: np.ndarray,
        morphed_image: np.ndarray,
        weight: float
    ) -> np.ndarray:
        """
        对变形后的图像进行颜色校正
        
        Args:
            source_image: 源图像
            target_image: 目标图像
            morphed_image: 变形后的图像
            weight: 当前帧的权重
            
        Returns:
            颜色校正后的图像
        """
        # 计算源图像和目标图像的平均颜色
        source_mean = cv2.mean(source_image)[:3]
        target_mean = cv2.mean(target_image)[:3]
        
        # 计算目标颜色
        target_color = tuple(
            s * (1 - weight) + t * weight
            for s, t in zip(source_mean, target_mean)
        )
        
        # 计算当前图像的平均颜色
        current_mean = cv2.mean(morphed_image)[:3]
        
        # 计算颜色校正系数
        correction = tuple(
            t / c if c > 0 else 1.0
            for t, c in zip(target_color, current_mean)
        )
        
        # 应用颜色校正
        corrected = morphed_image.copy()
        for i in range(3):
            corrected[:, :, i] = cv2.multiply(
                morphed_image[:, :, i].astype(float),
                correction[i]
            ).clip(0, 255).astype(np.uint8)
        
        return corrected

    def warp_image(self, img, src_points, dst_points, triangles):
        """
        使用 Delaunay 三角剖分和仿射变换进行图像变形
        
        Args:
            img: 输入图像
            src_points: 源特征点
            dst_points: 目标特征点
            triangles: 三角形索引
            
        Returns:
            变形后的图像
        """
        height, width = img.shape[:2]
        result = np.zeros_like(img)
        
        for tri_indices in triangles:
            # 获取三角形顶点
            src_tri = src_points[tri_indices].astype(np.float32)
            dst_tri = dst_points[tri_indices].astype(np.float32)
            
            # 计算仿射变换矩阵
            warp_mat = cv2.getAffineTransform(src_tri, dst_tri)
            
            # 变形当前三角形区域
            warped = cv2.warpAffine(img, warp_mat, (width, height))
            
            # 创建掩码
            mask = np.zeros((height, width), dtype=np.uint8)
            cv2.fillConvexPoly(mask, dst_tri.astype(np.int32), 1)
            
            # 将变形后的三角形区域复制到结果图像
            result = cv2.add(result, warped * mask[:, :, np.newaxis])
            
        return result

    def generate_sequence(self, source_image, target_image, feature_sequence, steps=5):
        """
        生成渐变图像序列
        
        Args:
            source_image: 源图像
            target_image: 目标图像
            feature_sequence: 特征序列
            steps: 生成的图像数量
            
        Returns:
            渐变图像序列
        """
        morph_sequence = []
        
        for i, features in enumerate(feature_sequence):
            # 计算当前阶段的权重
            alpha = i / (steps - 1)
            
            # 获取特征点
            source_points = features['source_landmarks_2d']
            target_points = features['target_landmarks_2d']
            current_points = features['landmarks_2d']
            
            # 使用 Delaunay 三角剖分
            tri = Delaunay(current_points)
            
            # 变形源图像和目标图像
            warped_source = self.warp_image(source_image, source_points, current_points, tri.simplices)
            warped_target = self.warp_image(target_image, target_points, current_points, tri.simplices)
            
            # 混合两个变形后的图像
            morphed = cv2.addWeighted(warped_source, 1 - alpha, warped_target, alpha, 0)
            
            morph_sequence.append(morphed)
            
        return morph_sequence
