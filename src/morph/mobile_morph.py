"""
面部渐变效果实现
基于 Delaunay 三角剖分和仿射变换实现面部特征的渐变
"""

import cv2
import numpy as np
from PyQt5.QtWidgets import QWidget, QLabel, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QImage, QPixmap
from scipy.spatial import Delaunay

class MobileMorphingWidget(QWidget):
    """移动端优化的面部变形系统界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        # 设置布局
        layout = QVBoxLayout()
        
        # 创建图像显示区域
        self.image_layout = QHBoxLayout()
        
        # 源图像标签
        self.source_label = QLabel()
        self.source_label.setFixedSize(300, 300)
        self.source_label.setAlignment(Qt.AlignCenter)
        self.source_label.setText("源图像")
        
        # 目标图像标签
        self.target_label = QLabel()
        self.target_label.setFixedSize(300, 300)
        self.target_label.setAlignment(Qt.AlignCenter)
        self.target_label.setText("目标图像")
        
        self.image_layout.addWidget(self.source_label)
        self.image_layout.addWidget(self.target_label)
        
        # 添加图像布局
        layout.addLayout(self.image_layout)
        
        # 创建渐变图像显示区域
        self.morph_layout = QHBoxLayout()
        self.morph_labels = []
        for i in range(5):
            label = QLabel()
            label.setFixedSize(200, 200)
            label.setAlignment(Qt.AlignCenter)
            label.setText(f"渐变 {i+1}")
            self.morph_labels.append(label)
            self.morph_layout.addWidget(label)
            
        # 添加渐变图像布局
        layout.addLayout(self.morph_layout)
        
        # 设置主布局
        self.setLayout(layout)
        
    def update_display(self, source_img, target_img, morph_sequence):
        """更新显示的图像"""
        # 转换源图像
        source_img = cv2.cvtColor(source_img, cv2.COLOR_BGR2RGB)
        h, w = source_img.shape[:2]
        bytes_per_line = 3 * w
        q_img = QImage(source_img.data, w, h, bytes_per_line, QImage.Format_RGB888)
        self.source_label.setPixmap(QPixmap.fromImage(q_img).scaled(
            300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
        # 转换目标图像
        target_img = cv2.cvtColor(target_img, cv2.COLOR_BGR2RGB)
        h, w = target_img.shape[:2]
        bytes_per_line = 3 * w
        q_img = QImage(target_img.data, w, h, bytes_per_line, QImage.Format_RGB888)
        self.target_label.setPixmap(QPixmap.fromImage(q_img).scaled(
            300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
        # 转换渐变序列图像
        for i, morph_img in enumerate(morph_sequence):
            if i < len(self.morph_labels):
                morph_img = cv2.cvtColor(morph_img, cv2.COLOR_BGR2RGB)
                h, w = morph_img.shape[:2]
                bytes_per_line = 3 * w
                q_img = QImage(morph_img.data, w, h, bytes_per_line, QImage.Format_RGB888)
                self.morph_labels[i].setPixmap(QPixmap.fromImage(q_img).scaled(
                    200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation))
                
        # 调整窗口大小以适应内容
        self.adjustSize()
        self.show()
        
    def convert_to_qimage(self, img):
        """将OpenCV图像转换为QImage"""
        if img is None:
            return None
            
        height, width, channel = img.shape
        bytes_per_line = 3 * width
        return QImage(img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

class MobileMorphing:
    """移动端优化的面部变形系统"""
    
    def __init__(self):
        self.max_image_size = 800  # 最大图像尺寸限制
        
    def create_face_mask(self, img_shape, points, radius=15):
        """创建面部遮罩，使用凸包和羽化效果"""
        mask = np.zeros(img_shape[:2], dtype=np.uint8)
        hull = cv2.convexHull(points.astype(np.int32))
        cv2.fillConvexPoly(mask, hull, 255)
        mask = cv2.GaussianBlur(mask, (radius, radius), 0)
        return mask / 255.0
        
    def bilinear_interpolate(self, img, coords):
        """双线性插值"""
        int_coords = np.int32(coords)
        x0, y0 = int_coords
        dx, dy = coords - int_coords

        # 获取四个相邻像素
        q11 = img[y0, x0]
        q21 = img[y0, x0+1]
        q12 = img[y0+1, x0]
        q22 = img[y0+1, x0+1]

        # 双线性插值
        btm = q21.T * dx + q11.T * (1 - dx)
        top = q22.T * dx + q12.T * (1 - dx)
        return (top * dy + btm * (1 - dy)).T
        
    def get_grid_coordinates(self, points):
        """获取特征点范围内的网格坐标"""
        xmin = int(np.min(points[:, 0]))
        xmax = int(np.max(points[:, 0])) + 1
        ymin = int(np.min(points[:, 1]))
        ymax = int(np.max(points[:, 1])) + 1
        
        return np.asarray([
            (x, y) for y in range(ymin, ymax)
            for x in range(xmin, xmax)
        ], np.uint32)
        
    def get_triangle_affine_matrices(self, triangles, src_points, dst_points):
        """计算每个三角形的仿射变换矩阵"""
        matrices = []
        for tri_indices in triangles:
            src_tri = np.vstack((src_points[tri_indices].T, [1, 1, 1]))
            dst_tri = np.vstack((dst_points[tri_indices].T, [1, 1, 1]))
            mat = np.dot(src_tri, np.linalg.inv(dst_tri))[:2, :]
            matrices.append(mat)
        return np.array(matrices)
        
    def warp_image(self, src_img, src_points, dst_points):
        """使用 Delaunay 三角剖分和仿射变换进行图像变形"""
        rows, cols = src_img.shape[:2]
        result_img = np.zeros_like(src_img)
        
        # Delaunay 三角剖分
        delaunay = Delaunay(dst_points)
        
        # 计算所有三角形的仿射变换矩阵
        tri_affines = self.get_triangle_affine_matrices(
            delaunay.simplices, src_points, dst_points
        )
        
        # 获取目标图像中需要处理的像素坐标
        roi_coords = self.get_grid_coordinates(dst_points)
        roi_tri_indices = delaunay.find_simplex(roi_coords)
        
        # 对每个三角形进行变形
        for simplex_index in range(len(delaunay.simplices)):
            coords = roi_coords[roi_tri_indices == simplex_index]
            if len(coords) > 0:
                # 计算源图像中的对应坐标
                out_coords = np.dot(
                    tri_affines[simplex_index],
                    np.vstack((coords.T, np.ones(len(coords))))
                )
                
                # 使用双线性插值获取像素值
                x, y = coords.T
                result_img[y, x] = self.bilinear_interpolate(src_img, out_coords)
        
        return result_img
        
    def generate_morph_sequence(self, source_img, target_img, source_points, target_points, steps=20):
        """生成渐变序列"""
        sequence = []
        for i in range(steps):
            # 使用 sine 函数使变化更平滑
            alpha = np.sin((i / (steps - 1)) * np.pi / 2)
            
            # 计算当前帧的特征点位置
            current_points = source_points + alpha * (target_points - source_points)
            
            # 生成当前帧的遮罩
            mask = self.create_face_mask(source_img.shape, current_points)
            
            # 对图像进行变形
            warped = self.warp_image(source_img, source_points, current_points)
            
            # 使用遮罩混合结果
            mask = np.expand_dims(mask, axis=2)
            result = (warped * mask + source_img * (1 - mask)).astype(np.uint8)
            
            sequence.append(result)
        
        return sequence

if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    widget = MobileMorphingWidget()
    widget.show()
    sys.exit(app.exec_())
