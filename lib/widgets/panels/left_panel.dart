import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import '../preview_area/image_display.dart' hide Logger; // 使用 hide 关键字避免 Logger 重复导入
import 'package:provider/provider.dart';
import '../../utils/logger.dart'; // 主要的 Logger 导入
import '../../core/feature_points_helper.dart';
import '../../core/feature_points_data.dart';
import '../../core/models/feature_point.dart';
import '../../beautify_feature/services/transformation_service.dart';
import '../../core/deformation_area_type.dart';
import '../../core/transformations/transformation_factory.dart';
import 'dart:math' as math;
import 'dart:async';
// 只从一个地方导入FeatureAreaType以避免冲突
// import '../../core/models/feature_area_type.dart';
import '../../config/app_config.dart';
// 添加TransformType的导入
import '../../core/transform_type.dart';

/// 左侧面板组件
/// 
/// 显示美容整形功能模块，包括面部轮廓、鼻部塑形、眼部调整、唇部塑形和抗衰老
class LeftPanel extends StatefulWidget {
  final GlobalKey<State<ImageDisplay>> imageDisplayKey;

  const LeftPanel({
    Key? key,
    required this.imageDisplayKey,
  }) : super(key: key);

  @override
  State<LeftPanel> createState() => _LeftPanelState();
}

class _LeftPanelState extends State<LeftPanel> with TickerProviderStateMixin {
  int _selectedIndex = -1;
  
  // 跟踪当前选中的参数项
  int _selectedFeatureIndex = -1;
  int _selectedParamIndex = -1;
  
  // 动画控制器列表，用于控制每个功能项的显示动画
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _animations;
  
  /// 标准化区域名称
  String _normalizeAreaName(dynamic areaType) {
    // 使用字符串比较而不是枚举比较，避免类型冲突
    final String typeStr = areaType.toString();
    if (typeStr.contains('face_contour')) {
      return 'face_contour';
    } else if (typeStr.contains('nose')) {
      return 'nose';
    } else if (typeStr.contains('eyes')) {
      return 'eyes';
    } else if (typeStr.contains('lips')) {
      return 'lips';
    } else if (typeStr.contains('anti_aging')) {
      return 'anti_aging';
    } else {
      return 'unknown';
    }
  }
  
  /// 获取区域名称
  String _getAreaName(dynamic areaType) {
    return _normalizeAreaName(areaType);
  }
  
  // 功能项配置
  List<Map<String, dynamic>> _features = [];

  @override
  void initState() {
    super.initState();
    _initFeatures();
    
    // 初始化动画控制器
    _animationControllers = List.generate(
      _features.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 300 + index * 100),
        vsync: this,
      ),
    );
    
    _animations = _animationControllers.map((controller) => 
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      ),
    ).toList();
    
    // 逐个启动动画
    Future.delayed(const Duration(milliseconds: 100), () {
      for (var controller in _animationControllers) {
        controller.forward();
      }
    });
  }
  
  @override
  void dispose() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }
  
  // 选择功能
  Future<void> _selectFeature(int index) async {
    if (index < 0 || index >= _features.length) {
      Logger.e('左侧面板', '功能索引越界: $index');
      return;
    }
    
    final feature = _features[index];
    final featureArea = feature['area'] as FeatureAreaType;
    final areaName = _getAreaName(featureArea);
    
    Logger.i('左侧面板', '开始选择区域');
    Logger.d('左侧面板', '当前状态:');
    Logger.d('左侧面板', '  • 功能标题: ${feature['title']}');
    Logger.d('左侧面板', '  • 区域类型: $featureArea');
    Logger.d('左侧面板', '  • 区域名称: $areaName');
    
    setState(() {
      _selectedIndex = index;
      // 切换功能区域时，清除参数项选中状态
      _selectedFeatureIndex = -1;
      _selectedParamIndex = -1;
    });
    
    // 使用 TransformationService 设置选中区域
    final transformationService = TransformationService.instance; // 使用单例实例
    transformationService.setSelectedArea(areaName);
    
    // 获取并显示区域特征点
    final points = FeaturePointsHelper().getAreaPoints(featureArea);
    Logger.d('左侧面板', '获取到特征点数量: ${points.length}');
    
    // 通知图片显示组件更新
    Logger.d('左侧面板', '准备调用showAreaPoints方法');
    final imageDisplay = widget.imageDisplayKey.currentState;
    if (imageDisplay != null) {
      (imageDisplay as dynamic).showAreaPoints(points);
      Logger.d('左侧面板', 'showAreaPoints方法调用完成');
    } else {
      Logger.e('左侧面板', '未找到ImageDisplay组件');
    }
    
    Logger.i('左侧面板', '区域选择完成');
  }

  // 初始化功能项配置
  void _initFeatures() {
    _features = [
      {
        'title': beautyAreaConfigs['face_contour']!.displayName,
        'icon': 'assets/icons/face.jpg',
        'area': FeatureAreaType.face_contour,
        'params': beautyAreaConfigs['face_contour']!.parameters.entries.map((e) => {
          'name': e.key,
          'displayName': e.value.displayName,
          'value': 0,
        }).toList(),
      },
      {
        'title': beautyAreaConfigs['nose']!.displayName,
        'icon': 'assets/icons/nose.jpg',
        'area': FeatureAreaType.nose,
        'params': beautyAreaConfigs['nose']!.parameters.entries.map((e) => {
          'name': e.key,
          'displayName': e.value.displayName,
          'value': 0,
        }).toList(),
      },
      {
        'title': beautyAreaConfigs['eyes']!.displayName,
        'icon': 'assets/icons/eye.jpg',
        'area': FeatureAreaType.eyes,
        'params': beautyAreaConfigs['eyes']!.parameters.entries.map((e) => {
          'name': e.key,
          'displayName': e.value.displayName,
          'value': 0,
        }).toList(),
      },
      {
        'title': beautyAreaConfigs['lips']!.displayName,
        'icon': 'assets/icons/lip.jpg',
        'area': FeatureAreaType.lips,
        'params': beautyAreaConfigs['lips']!.parameters.entries.map((e) => {
          'name': e.key,
          'displayName': e.value.displayName,
          'value': 0,
        }).toList(),
      },
      /* 抗衰老功能暂时注释，因为相关配置已被移除
      {
        'title': beautyAreaConfigs['anti_aging']?.displayName ?? '抗衰老',
        'icon': 'assets/icons/age.jpg',
        'area': FeatureAreaType.anti_aging,
        'params': beautyAreaConfigs['anti_aging']?.parameters.entries.map((e) => {
          'name': e.key,
          'displayName': e.value.displayName,
          'value': 0,
        }).toList() ?? [],
      },
      */
    ];
  }

  // 更新单个功能的配置
  void _updateFeature(int index) {
    final feature = _features[index];
    final featureArea = feature['area'] as FeatureAreaType;
    final areaName = _getAreaName(featureArea);
    
    // 从 feature_points_data.dart 获取最新配置
    feature['params'] = beautyAreaConfigs[areaName]!.parameters.entries.map((e) => {
      'name': e.key,
      'displayName': e.value.displayName,
      'value': feature['params'].firstWhere(
        (p) => p['name'] == e.key,
        orElse: () => {'value': 0},
      )['value'],
    }).toList();
    
    Logger.d('左侧面板', '更新功能配置:');
  }
  
  // 处理参数调整
  Future<void> _adjustParameter(int featureIndex, int paramIndex, int adjustment) async {
    try {
      if (featureIndex < 0 || featureIndex >= _features.length) return;
      if (paramIndex < 0 || paramIndex >= _features[featureIndex]['params'].length) return;
      
      setState(() {
        // 获取当前参数值
        int currentValue = _features[featureIndex]['params'][paramIndex]['value'];
        // 应用调整
        int newValue = currentValue + adjustment;
        // 确保值在范围内（-5到5）
        newValue = math.max(-5, math.min(newValue, 5));
        // 更新参数值
        _features[featureIndex]['params'][paramIndex]['value'] = newValue;
      });
      
      // 获取区域类型和参数名称
      final featureType = _features[featureIndex]['area'] as FeatureAreaType;
      final areaName = _normalizeAreaName(featureType);
      final paramName = _features[featureIndex]['params'][paramIndex]['name'];
      
      Logger.i('左侧面板', '开始调整参数');
      Logger.d('左侧面板', '当前状态:');
      Logger.d('左侧面板', '  • 功能标题: ${_features[featureIndex]['title']}');
      Logger.d('左侧面板', '  • 区域类型: $featureType');
      Logger.d('左侧面板', '  • 区域名称: $areaName');
      Logger.d('左侧面板', '  • 参数名称: $paramName');
      Logger.d('左侧面板', '  • 调整值: $adjustment');
      
      // 获取新值
      final newValue = _features[featureIndex]['params'][paramIndex]['value'];
      final oldValue = newValue - adjustment;
      Logger.d('左侧面板', '当前值: $oldValue');
      Logger.d('左侧面板', '新值: $newValue');
      
      // 更新变换服务
      Logger.d('左侧面板', '更新变换服务');
      final transformationService = TransformationService.instance;
      transformationService.setSelectedArea(areaName);
      transformationService.setSelectedParameter(paramName);
      
      // 从配置获取变形步长
      final double stepSize = AppConfig.instance.transformationStepSize;
      
      // 计算变形值（使用滑块实际值）
      final double transformationValue = newValue * stepSize;
      
      Logger.i('左侧面板', '应用变形: 步长=$stepSize, 变形值=$transformationValue');
      
      // 确定用户点击的是加号还是减号
      final bool isIncreasing = adjustment > 0; // 如果adjustment为1，则点击的是加号；如果为-1，则点击的是减号
      Logger.i('左侧面板', '用户点击的是${isIncreasing ? "加号" : "减号"}按钮');
      
      // 【修复】不再传递区域信息，只使用参数名
      transformationService.updateTransformationParam('', paramName, transformationValue, isIncreasing: isIncreasing);
      
      // 确保变形区域组件可见 - 这是关键部分
      final deformationRenderer = transformationService.getSimpleDeformationRenderer();
      if (deformationRenderer != null) {
        Logger.i('左侧面板', '设置变形区域组件可见性为 true');
        deformationRenderer.setVisible(true);
        
        // 设置特征点显示为 true，确保呼吸效果能正确显示
        deformationRenderer.setShowFeaturePoints(true);
        Logger.i('左侧面板', '设置特征点显示为 true');
        
        // 确保变形类型设置正确
        if (paramName == 'nostril_width') {
          Logger.i('左侧面板', '设置变形类型为 local (局部变形)');
          // 使用正确的导入路径
          deformationRenderer.setTransformTypeDirectly(TransformType.local);
        }
        
        // 触发重绘
        deformationRenderer.repaint();
        Logger.i('左侧面板', '已触发变形区域渲染器重绘');
      } else {
        Logger.e('左侧面板', '变形区域渲染器为空，无法设置可见性');
      }
      
      // 获取特征点
      final helper = FeaturePointsHelper();
      final paramPoints = helper.getParameterPoints(areaName, paramName);
      
      Logger.i('左侧面板', '获取到参数特征点数量: ${paramPoints.length}');
      Logger.d('左侧面板', '特征点列表: $paramPoints');
      
      // 显示特征点
      Logger.i('左侧面板', '显示参数特征点: ${paramPoints.length} 个点');
      final imageDisplay = widget.imageDisplayKey.currentState;
      if (imageDisplay != null) {
        (imageDisplay as dynamic).showParameterPoints(paramPoints);
      }
      
      // 调用TransformationService更新参数值 - 这里已经在前面调用过了，不需要重复调用
      Logger.i('左侧面板', '参数值已更新: 区域=$areaName, 参数=$paramName, 值=$transformationValue');
      
      // 应用变形
      Logger.i('左侧面板', '应用变形');
      helper.logSymmetryPairsForArea(areaName, paramName);
      
      // 显示参数特征点，带动画效果
      if (imageDisplay != null) {
        (imageDisplay as dynamic).showParameterPointsWithAnimation(
          paramPoints,
          const Duration(seconds: 2),
        );
        Logger.d('左侧面板', 'showParameterPointsWithAnimation方法调用完成');
      }
      
      Logger.i('左侧面板', '_adjustParameter: 参数调整完成');
    } catch (e) {
      Logger.e('左侧面板', '❗ [ERROR] 调整参数异常: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFF2A2A2A),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 面板标题
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            alignment: Alignment.center,
            child: const Text(
              '美容整形功能',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          // 功能项列表
          Expanded(
            child: ListView.separated(
              itemCount: _features.length,
              padding: const EdgeInsets.symmetric(vertical: 8),
              // 使用彩虹发光线作为分隔
              separatorBuilder: (context, index) {
                return _buildRainbowDivider();
              },
              itemBuilder: (context, index) {
                return AnimatedBuilder(
                  animation: _animations[index],
                  builder: (context, child) {
                    return Opacity(
                      opacity: _animations[index].value,
                      child: Transform.translate(
                        offset: Offset(-20 * (1 - _animations[index].value), 0),
                        child: _buildFeatureItem(index),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
  
  // 构建彩虹发光线
  Widget _buildRainbowDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      height: 2,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF00FFFF), // 青色
            Color(0xFF00FF00), // 绿色
            Color(0xFFFFFF00), // 黄色
            Color(0xFFFF7F00), // 橙色
            Color(0xFFFF0000), // 红色
            Color(0xFF8B00FF), // 紫色
          ],
        ),
        borderRadius: BorderRadius.circular(1),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.5),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeatureItem(int index) {
    final isSelected = index == _selectedIndex;
    final feature = _features[index];
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Material(
        color: isSelected ? Colors.white.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () async => await _selectFeature(index),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 左侧：图标和标题
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 图标 - 大大方方地显示
                    Container(
                      width: 90,
                      height: 90,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        image: DecorationImage(
                          image: AssetImage(feature['icon']),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const SizedBox(height: 6),
                    // 标题 - 放在图片下方
                    Text(
                      feature['title'],
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(width: 16),
                
                // 右侧：参数项
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: List.generate(
                      (feature['params'] as List).length,
                      (paramIndex) {
                        final param = feature['params'][paramIndex];
                        return _buildParameterItem(index, paramIndex, param);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // 构建参数项 - 每个参数项在一行，包含参数名称、数值显示和加减按钮
  Widget _buildParameterItem(int featureIndex, int paramIndex, Map<String, dynamic> param) {
    final feature = _features[featureIndex];
    final featureArea = feature['area'] as FeatureAreaType;
    final areaName = _getAreaName(featureArea);
    final paramName = param['name'] as String;
    
    // 检查当前参数项是否被选中
    final isSelected = featureIndex == _selectedFeatureIndex && paramIndex == _selectedParamIndex;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: GestureDetector(
        onTap: () async {
          Logger.flowStart('左侧面板', '参数项点击');
          Logger.flow('左侧面板', '参数项点击', '区域=$areaName, 参数=$paramName');
          
          // 获取参数特征点
          final helper = FeaturePointsHelper();
          final paramPoints = helper.getParameterPoints(areaName, paramName);
          
          Logger.flow('左侧面板', '参数项点击', '获取到参数特征点: ${paramPoints.length}个点, 索引: $paramPoints');
          
          // 更新选中状态
          setState(() {
            _selectedFeatureIndex = featureIndex;
            _selectedParamIndex = paramIndex;
          });
          
          // 输出对称点对信息
          helper.logSymmetryPairsForArea(areaName, paramName);
          
          // 先通知图片显示组件更新，显示参数特征点
          final imageDisplay = widget.imageDisplayKey.currentState;
          if (imageDisplay != null) {
            // 显示参数特征点，带2秒动画
            Logger.flow('左侧面板', '参数项点击', '调用showParameterPointsWithAnimation方法');
            (imageDisplay as dynamic).showParameterPointsWithAnimation(
              paramPoints,
              const Duration(seconds: 2),
            );
            Logger.flow('左侧面板', '参数项点击', 'showParameterPointsWithAnimation方法调用完成');
          } else {
            Logger.flowError('左侧面板', '参数项点击', '图片显示组件状态为空');
          }
          
          // 然后再更新变形渲染器的当前参数名称，但不触发变形操作
          final transformationService = TransformationService.instance;
          final deformationRenderer = transformationService.getSimpleDeformationRenderer();
          if (deformationRenderer != null) {
            // 先设置区域和参数名称，确保特征点管理器更新当前参数索引
            Logger.flow('左侧面板', '参数项点击', '设置变形渲染器区域和参数名称');
            deformationRenderer?.setArea(areaName);
            deformationRenderer?.setParameterName(paramName);
          Logger.flow('左侧面板', '参数项点击', '设置变形渲染器参数名称，确保变形状态正确准备');
            
            // 确保特征点管理器更新当前参数索引
            final featurePointManager = transformationService.featurePointManager;
            featurePointManager.setAreaType(areaName);
            featurePointManager.setParameterName(paramName);
            featurePointManager.updateCurrentParameterPointIndexes();
            
            // 强制设置变形渲染器的特征点显示为关闭，使用LandmarksOverlay显示特征点
            deformationRenderer.setShowFeaturePoints(false);
            Logger.flow('左侧面板', '参数项点击', '设置变形渲染器特征点显示关闭，使用LandmarksOverlay显示特征点');
            
            // 再次调用以确保变形渲染器的特征点显示关闭，使用LandmarksOverlay显示特征点
            // 直接调用，不使用延迟
            deformationRenderer.setShowFeaturePoints(false);
            Logger.flow('左侧面板', '参数项点击', '强制确保使用LandmarksOverlay显示特征点');
          }
          
          // 记录参数项点击信息，但不触发变形相关操作
          if (areaName == 'nose') {
            Logger.flow('左侧面板', '参数项点击', '鼻部区域参数项点击完成');
          } else {
            Logger.flow('左侧面板', '参数项点击', '其他区域参数项点击完成');
          }
          
          // 注意：根据面部中心线设计原则，参数项点击时不应触发任何变形相关操作
          // 变形操作应该只在用户调整参数值时执行，而不是在参数项被点击选中时
          
          Logger.flowEnd('左侧面板', '参数项点击');
          
          // 以下代码已被注释掉，以避免触发不必要的变形操作
          /*
          final transformationService = TransformationService.instance;
          if (areaName == 'nose') {
            // 不再使用已删除的方法
            // final paramType = _convertToNoseParameterType(paramName);
            /* 注释掉对paramType的引用
            if (paramType != null) {
              final paramValue = _features[featureIndex]['params'][paramIndex]['value'] is int 
                  ? (_features[featureIndex]['params'][paramIndex]['value'] as int).toDouble()
                  : _features[featureIndex]['params'][paramIndex]['value'] as double;
              transformationService.visualizeParameterInfluenceByName('nose', paramName, paramValue);
            }
            */
          } else {
            // 其他区域参数处理
            transformationService.visualizeParameterInfluenceForArea(areaName, paramName);
          }
          */
        },
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? Colors.white.withOpacity(0.15) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              // 参数名称 - 选中时显示为黄色
              SizedBox(
                width: 75,
                child: Text(
                  param['displayName'],
                  style: TextStyle(
                    color: isSelected ? Colors.yellow : Colors.white70,
                    fontSize: 15,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
              
              // 减少按钮 - 选中时高亮
              _buildCircleButton(
                icon: Icons.remove,
                onTap: () {
                  _adjustParameter(featureIndex, paramIndex, -1);
                },
                isHighlighted: isSelected,
              ),
              
              // 数值显示 - 选中时背景更亮、文字黄色
              Container(
                width: 45,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white.withOpacity(0.2) : Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: isSelected ? Border.all(color: Colors.yellow.withOpacity(0.5), width: 1.5) : null,
                ),
                padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                margin: const EdgeInsets.symmetric(horizontal: 6),
                child: Text(
                  '${param['value'] > 0 ? "+" : ""}${param['value']}',
                  style: TextStyle(
                    color: isSelected ? Colors.yellow : Colors.white.withOpacity(0.8),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              
              // 增加按钮 - 选中时高亮
              _buildCircleButton(
                icon: Icons.add,
                onTap: () {
                  _adjustParameter(featureIndex, paramIndex, 1);
                },
                isHighlighted: isSelected,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // 构建圆形按钮
  // 构建圆形按钮，用于增加/减少参数值
  Widget _buildCircleButton({required IconData icon, required Function() onTap, bool isHighlighted = false}) {
    return Material(
      color: isHighlighted ? Colors.white.withOpacity(0.2) : Colors.white.withOpacity(0.1),
      shape: const CircleBorder(),
      child: InkWell(
        onTap: () {
          // 调用传入的onTap函数
          onTap();
        },
        customBorder: const CircleBorder(),
        child: Container(
          width: 36,
          height: 36,
          alignment: Alignment.center,
          decoration: isHighlighted ? BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.yellow.withOpacity(0.5), width: 1.5),
          ) : null,
          child: Icon(
            icon,
            color: isHighlighted ? Colors.yellow : Colors.white,
            size: 20,
          ),
        ),
      ),
    );
  }

  // 此方法已不再使用，相关类型定义已移除
  /*
  String? _convertToNoseParameterType(String paramName) {
    print('🔍 [左侧面板] 转换参数名称为鼻子参数类型: $paramName');
    return paramName;
  }
  */
}
