import 'package:flutter/material.dart';
import '../core/simple_deformation_renderer.dart';
import '../core/simple_deformation_painter.dart' hide Logger;
import '../utils/logger.dart';
import '../widgets/target_point_overlay.dart';

/// 简化版变形区域显示组件
/// 用于在UI中显示变形区域
class SimpleDeformationAreaWidget extends StatefulWidget {
  final SimpleDeformationRenderer renderer;
  
  const SimpleDeformationAreaWidget({
    super.key,
    required this.renderer,
  });

  @override
  State<SimpleDeformationAreaWidget> createState() => _SimpleDeformationAreaWidgetState();
}

class _SimpleDeformationAreaWidgetState extends State<SimpleDeformationAreaWidget> {
  // 日志标签
  static const String _logTag = '变形区域组件';
  
  // 记录上次日志输出的时间戳，用于限制日志输出频率
  static DateTime? _lastLogTime;
  static const Duration _minimumLogInterval = Duration(milliseconds: 1000);
  
  // 缓存上一次的参数值，用于判断是否需要重建
  String? _lastAreaType;
  String? _lastParameterName;
  double? _lastParameterValue;
  bool? _lastIsVisible;
  
  // 【已废弃】不再需要缓存绘制器，因为绘制器已改为单例模式
  // SimpleDeformationPainter? _cachedPainter;
  
  // 【单例模式】标记是否已设置回调，避免重复设置
  bool _callbackSet = false;
  
  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final shouldLog = _lastLogTime == null || 
                     now.difference(_lastLogTime!) > _minimumLogInterval;
    
    // 检查关键参数是否发生变化
    final areaTypeChanged = _lastAreaType != widget.renderer.areaType;
    final parameterNameChanged = _lastParameterName != widget.renderer.parameterName;
    final parameterValueChanged = _lastParameterValue != widget.renderer.parameterValue;
    final visibilityChanged = _lastIsVisible != widget.renderer.isVisible;
    
    // 更新缓存的参数值
    _lastAreaType = widget.renderer.areaType;
    _lastParameterName = widget.renderer.parameterName;
    _lastParameterValue = widget.renderer.parameterValue;
    _lastIsVisible = widget.renderer.isVisible;
    
    // 只有在关键参数发生变化且满足日志间隔要求时才输出日志
    if (shouldLog && (areaTypeChanged || parameterNameChanged || parameterValueChanged || visibilityChanged)) {
      _lastLogTime = now;
      Logger.flowStart(_logTag, 'build');
      Logger.flow(_logTag, 'build', '🔄 [更新] 区域: ${widget.renderer.areaType} | 参数: ${widget.renderer.parameterName} | 值: ${widget.renderer.parameterValue} | 可见: ${widget.renderer.isVisible}');
    }
    
    // 使用 ListenableBuilder 替代 AnimatedBuilder，但限制重建频率
    return ListenableBuilder(
      listenable: widget.renderer,
      builder: (context, child) {
        // 【优化】避免因频繁通知而重建Widget，只在关键参数变化时重建
        final currentAreaType = widget.renderer.areaType;
        final currentParameterName = widget.renderer.parameterName;
        final currentParameterValue = widget.renderer.parameterValue;
        final currentIsVisible = widget.renderer.isVisible;
        
        // 如果关键参数没有变化，直接返回上次构建的内容
        if (currentAreaType == _lastAreaType && 
            currentParameterName == _lastParameterName && 
            currentParameterValue == _lastParameterValue && 
            currentIsVisible == _lastIsVisible) {
          // 参数没有变化，直接使用缓存的绘制器重建内容
          return _buildDeformationContent();
        }
        
        // 更新缓存参数
        _lastAreaType = currentAreaType;
        _lastParameterName = currentParameterName;
        _lastParameterValue = currentParameterValue;
        _lastIsVisible = currentIsVisible;
        
        return _buildDeformationContent();
      },
    );
  }
  
  /// 构建变形内容的核心方法
  Widget _buildDeformationContent() {
    // 仅在渲染器可见且有有效的区域类型和参数名称时显示
    if (!widget.renderer.isVisible) {
      return const SizedBox.shrink();
    }
    
    // 检查区域类型和参数名称是否有效
    if (widget.renderer.areaType.isEmpty || widget.renderer.areaType == 'none' || 
        widget.renderer.parameterName == null || widget.renderer.parameterName!.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // 使用 RepaintBoundary 优化性能
    return RepaintBoundary(
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 基础变形绘制
          SizedBox.expand(
            child: CustomPaint(
              painter: _getOrCreatePainter(), // 使用缓存或创建新的绘制器
              isComplex: true, // 告诉Flutter这是一个复杂的绘制，优化重绘
              willChange: true, // 告诉Flutter绘制可能会变化
            ),
          ),
          
          // 目标点覆盖层
          TargetPointOverlay(
            imageSize: widget.renderer.imageSize,
          ),
          // 特征点可视化覆盖层已移除
        ],
      ),
    );
  }
  
  // 缓存的绘制器实例
  SimpleDeformationPainter? _cachedPainter;
  DateTime? _lastPainterCreateTime;
  
  /// 【优化】获取绘制器实例，避免因呼吸动画频繁重建
  SimpleDeformationPainter _getOrCreatePainter() {
    final now = DateTime.now();
    
    // 检查参数是否有变化
    final areaTypeChanged = _lastAreaType != widget.renderer.areaType;
    final parameterNameChanged = _lastParameterName != widget.renderer.parameterName;
    final parameterValueChanged = _lastParameterValue != widget.renderer.parameterValue;
    final visibilityChanged = _lastIsVisible != widget.renderer.isVisible;
    
    // 检查是否有加减号点击事件
    final hasButtonClick = widget.renderer.isParameterIncreasing != null;
    
    // 如果有缓存的绘制器且参数没有变化，直接复用
    if (_cachedPainter != null && _lastPainterCreateTime != null &&
        !areaTypeChanged && !parameterNameChanged && !parameterValueChanged && !visibilityChanged && !hasButtonClick) {
      // 限制绘制器创建频率：每200ms最多创建一次
      if (now.difference(_lastPainterCreateTime!).inMilliseconds < 200) {
        return _cachedPainter!;
      }
    }
    
    // 只有在参数发生变化时才输出日志
    if (areaTypeChanged || parameterNameChanged || parameterValueChanged || visibilityChanged) {
      Logger.i(_logTag, '🔨 创建绘制器，区域类型: ${widget.renderer.areaType}, 参数: ${widget.renderer.parameterName}=${widget.renderer.parameterValue}');
      if (areaTypeChanged) Logger.i(_logTag, '🔄 [变化] 区域类型: $_lastAreaType -> ${widget.renderer.areaType}');
      if (parameterNameChanged) Logger.i(_logTag, '🔄 [变化] 参数名称: $_lastParameterName -> ${widget.renderer.parameterName}');
      if (parameterValueChanged) Logger.i(_logTag, '🔄 [变化] 参数值: $_lastParameterValue -> ${widget.renderer.parameterValue}');
      if (visibilityChanged) Logger.i(_logTag, '🔄 [变化] 可见性: $_lastIsVisible -> ${widget.renderer.isVisible}');
    }
    
    // 通过渲染器获取绘制器（渲染器内部有缓存机制）
    final painter = widget.renderer.createPainter(showDebugInfo: true);
    
    // 缓存绘制器实例和创建时间
    _cachedPainter = painter;
    _lastPainterCreateTime = now;
    
    // 更新缓存的参数值
    _lastAreaType = widget.renderer.areaType;
    _lastParameterName = widget.renderer.parameterName;
    _lastParameterValue = widget.renderer.parameterValue;
    _lastIsVisible = widget.renderer.isVisible;
    
    return painter;
  }
}
