import 'package:flutter/material.dart';
import '../../beautify_feature/ui/left_panel/control_panel.dart';
import '../../beautify_feature/ui/center_panel/preview_panel.dart';
import '../../beautify_feature/ui/right_panel/interaction_panel.dart';
import '../../beautify_feature/services/transformation_service.dart';
import 'package:provider/provider.dart';

/// 可扩展布局组件
/// 
/// 初始只显示中心内容，点击"炫"按钮后扩展并滑出左右面板
class ExpandableLayout extends StatefulWidget {
  /// 中心内容组件
  final Widget centerContent;
  
  /// 是否处于扩展状态
  final bool expanded;
  
  /// 扩展状态变化回调
  final Function(bool) onExpandChanged;

  const ExpandableLayout({
    Key? key,
    required this.centerContent,
    required this.expanded,
    required this.onExpandChanged,
  }) : super(key: key);

  @override
  State<ExpandableLayout> createState() => _ExpandableLayoutState();
}

class _ExpandableLayoutState extends State<ExpandableLayout> with SingleTickerProviderStateMixin {
  /// 动画控制器
  late AnimationController _controller;
  
  /// 动画曲线
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    
    if (widget.expanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(ExpandableLayout oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.expanded != oldWidget.expanded) {
      if (widget.expanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        // 获取屏幕尺寸
        final screenWidth = MediaQuery.of(context).size.width;
        
        // 计算当前宽度
        final centerWidth = screenWidth * 0.4;
        final leftWidth = screenWidth * 0.3 * _animation.value;
        final rightWidth = screenWidth * 0.3 * _animation.value;
        
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 左侧面板 - 从左侧滑入
            SizeTransition(
              sizeFactor: _animation,
              axis: Axis.horizontal,
              axisAlignment: -1,
              child: SizedBox(
                width: leftWidth,
                child: const ControlPanel(),
              ),
            ),
            
            // 中央面板 - 始终显示
            SizedBox(
              width: centerWidth,
              child: widget.centerContent,
            ),
            
            // 右侧面板 - 从右侧滑入
            SizeTransition(
              sizeFactor: _animation,
              axis: Axis.horizontal,
              axisAlignment: 1,
              child: SizedBox(
                width: rightWidth,
                child: const InteractionPanel(),
              ),
            ),
          ],
        );
      },
    );
  }
}
