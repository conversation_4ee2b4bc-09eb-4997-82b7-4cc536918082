import 'package:flutter/material.dart';
import '../../painters/landmark_painter.dart';
import '../../utils/logger.dart';

/// 特征点绘制可重用组件
/// 封装特征点绘制逻辑，避免整个UI重建
class LandmarksOverlay extends StatefulWidget {
  final Size containerSize;
  final List<Map<String, dynamic>> landmarks;
  final double scale;
  final Offset offset;
  final Size imageSize;
  final Set<int> highlightIndexes;
  final double initialBlinkValue;
  final List<Map<String, dynamic>>? symmetricPairs;
  final bool showSymmetryLines;
  final bool showIndexes;
  final Map<int, Offset>? deformations; // 变形偏移量
  final bool isPointAnimating; // 是否正在进行特征点动画
  final Color pointAnimationColor; // 特征点动画颜色
  final double pointAnimationOpacity; // 特征点动画不透明度
  final bool showPointsAfterDeformation; // 是否显示变形后的特征点

  const LandmarksOverlay({
    Key? key,
    required this.containerSize,
    required this.landmarks,
    required this.scale,
    required this.offset,
    required this.imageSize,
    required this.highlightIndexes,
    required this.initialBlinkValue,
    this.symmetricPairs,
    this.showSymmetryLines = true,
    this.showIndexes = true,
    this.deformations,
    this.isPointAnimating = false,
    this.pointAnimationColor = Colors.white,
    this.pointAnimationOpacity = 1.0,
    this.showPointsAfterDeformation = false,
  }) : super(key: key);

  @override
  LandmarksOverlayState createState() => LandmarksOverlayState();
}

class LandmarksOverlayState extends State<LandmarksOverlay> {
  // 当前闪烁值
  late double _blinkValue;
  // 特征点动画颜色
  Color _pointAnimationColor = Colors.red;
  // 特征点动画不透明度
  double _pointAnimationOpacity = 1.0;
  // 是否正在进行特征点动画
  bool _isPointAnimating = false;
  // 是否显示变形后的特征点
  bool _showPointsAfterDeformation = false;
  // 变形偏移量
  Map<int, Offset>? _deformations;

  @override
  void initState() {
    super.initState();
    _blinkValue = widget.initialBlinkValue;
    _pointAnimationColor = widget.pointAnimationColor;
    _pointAnimationOpacity = widget.pointAnimationOpacity;
    _isPointAnimating = widget.isPointAnimating;
    _showPointsAfterDeformation = widget.showPointsAfterDeformation;
    _deformations = widget.deformations;
  }

  // 更新闪烁值的方法，由外部调用
  void updateBlinkValue(double value) {
    Logger.flow('特征点覆盖层', 'updateBlinkValue', '更新闪烁值: $value');
    if (_blinkValue != value) {
      setState(() {
        _blinkValue = value;
      });
    }
  }

  // 更新特征点动画状态
  void updatePointAnimation({
    bool? isAnimating,
    Color? animationColor,
    double? animationOpacity,
    bool? showAfterDeformation,
    Map<int, Offset>? deformations,
  }) {
    setState(() {
      if (isAnimating != null) _isPointAnimating = isAnimating;
      if (animationColor != null) _pointAnimationColor = animationColor;
      if (animationOpacity != null) _pointAnimationOpacity = animationOpacity;
      if (showAfterDeformation != null) _showPointsAfterDeformation = showAfterDeformation;
      if (deformations != null) _deformations = deformations;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 添加详细的调试日志，跟踪特征点的显示情况
    Logger.flowStart('特征点覆盖层', 'build');
    Logger.flow('特征点覆盖层', 'build', '特征点数量: ${widget.landmarks.length}, 高亮特征点数量: ${widget.highlightIndexes.length}, 闪烁值: $_blinkValue');
    Logger.flow('特征点覆盖层', 'build', '动画状态: isPointAnimating=$_isPointAnimating, 动画颜色=${_pointAnimationColor.toString()}, 不透明度=$_pointAnimationOpacity');
    Logger.flow('特征点覆盖层', 'build', '容器尺寸: ${widget.containerSize}, 缩放比例: ${widget.scale}, 偏移量: ${widget.offset}');
    
    // 强制设置动画状态为激活，确保特征点始终处于动画状态
    if (!_isPointAnimating) {
      Logger.flow('特征点覆盖层', 'build', '强制设置动画状态为激活');
      _isPointAnimating = true;
    }
    
    // 强制设置动画颜色为白色，确保特征点始终为白色
    if (_pointAnimationColor != Colors.white) {
      Logger.flow('特征点覆盖层', 'build', '强制设置动画颜色为白色');
      _pointAnimationColor = Colors.white;
    }
    
    // 强制设置动画不透明度为1.0，确保特征点完全可见
    if (_pointAnimationOpacity != 1.0) {
      Logger.flow('特征点覆盖层', 'build', '强制设置动画不透明度为1.0');
      _pointAnimationOpacity = 1.0;
    }
    
    if (widget.landmarks.isEmpty) {
      Logger.flowError('特征点覆盖层', 'build', '特征点数据为空，无法显示特征点');
      Logger.flowEnd('特征点覆盖层', 'build');
      return Container(); // 返回空容器，避免绘制错误
    } 
    
    if (widget.highlightIndexes.isEmpty) {
      Logger.flowWarning('特征点覆盖层', 'build', '高亮特征点数据为空，无法显示呼吸特征点');
    } else {
      // 输出所有高亮特征点的索引，用于调试
      final highlightList = widget.highlightIndexes.toList();
      Logger.flow('特征点覆盖层', 'build', '所有高亮特征点索引: $highlightList');
      
      // 检查高亮特征点是否存在于特征点数据中
      final landmarkIndexes = widget.landmarks.map((lm) => lm['index'] as int).toSet();
      final existingHighlights = highlightList.where((index) => landmarkIndexes.contains(index)).toList();
      final missingHighlights = highlightList.where((index) => !landmarkIndexes.contains(index)).toList();
      
      Logger.flow('特征点覆盖层', 'build', '高亮点存在情况: 存在=${existingHighlights.length}个, 缺失=${missingHighlights.length}个');
      
      if (existingHighlights.isNotEmpty) {
        Logger.flow('特征点覆盖层', 'build', '存在的高亮点: $existingHighlights');
      }
      
      if (missingHighlights.isNotEmpty) {
        Logger.flowWarning('特征点覆盖层', 'build', '缺失的高亮点: $missingHighlights');
      }
    }
    
    return CustomPaint(
      size: widget.containerSize,
      painter: LandmarksPainter(
        landmarks: widget.landmarks,
        scale: widget.scale,
        offset: widget.offset,
        imageSize: widget.imageSize,
        highlightIndexes: widget.highlightIndexes,
        blinkValue: _blinkValue,
        symmetricPairs: widget.symmetricPairs,
        showSymmetryLines: widget.showSymmetryLines,
        showIndexes: widget.showIndexes,
        deformations: _deformations,
        isPointAnimating: _isPointAnimating,
        pointAnimationColor: _pointAnimationColor,
        pointAnimationOpacity: _pointAnimationOpacity,
        showPointsAfterDeformation: _showPointsAfterDeformation,
      ),
    );
  }
}
