import 'package:flutter/material.dart';
import '../../core/models/feature_point.dart';
import '../../utils/logger.dart';
import '../../painters/side_landmark_painter.dart';

/// 侧面图特征点覆盖层
/// 负责在侧面图上绘制特征点
class SideProfileLandmarksOverlay extends StatefulWidget {
  final List<FeaturePoint>? featurePoints;
  final double width;
  final double height;
  
  const SideProfileLandmarksOverlay({
    Key? key,
    required this.featurePoints,
    required this.width,
    required this.height,
  }) : super(key: key);

  @override
  State<SideProfileLandmarksOverlay> createState() => SideProfileLandmarksOverlayState();
}

class SideProfileLandmarksOverlayState extends State<SideProfileLandmarksOverlay> with SingleTickerProviderStateMixin {
  // 闪烁动画控制器
  late AnimationController _blinkController;
  // 闪烁动画值
  double _blinkValue = 1.0;
  
  // 高亮显示的特征点索引
  Set<int> _highlightIndexes = {};
  
  @override
  void initState() {
    super.initState();
    
    // 初始化闪烁动画控制器
    _blinkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    
    _blinkController.addListener(() {
      setState(() {
        _blinkValue = (_blinkController.value < 0.5) ? 
          1.0 - _blinkController.value * 2 : 
          (_blinkController.value - 0.5) * 2;
      });
    });
    
    _blinkController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _blinkController.reset();
        _blinkController.forward();
      }
    });
  }
  
  @override
  void dispose() {
    _blinkController.dispose();
    super.dispose();
  }
  
  /// 高亮显示指定索引的特征点
  void highlightPoints(List<int> indexes) {
    Logger.i('侧面特征点覆盖层', '高亮显示特征点: $indexes');
    setState(() {
      _highlightIndexes = Set<int>.from(indexes);
    });
    
    if (!_blinkController.isAnimating) {
      _blinkController.reset();
      _blinkController.forward();
    }
  }
  
  /// 清除高亮显示
  void clearHighlight() {
    setState(() {
      _highlightIndexes = {};
    });
    _blinkController.stop();
  }
  
  @override
  Widget build(BuildContext context) {
    // 不显示特征点，直接返回空容器
    return SizedBox(
      width: widget.width,
      height: widget.height,
    );
    
    // 以下代码被注释掉，不再显示特征点
    /*
    // 如果没有特征点数据，返回空容器
    if (widget.featurePoints == null || widget.featurePoints!.isEmpty) {
      return SizedBox(
        width: widget.width,
        height: widget.height,
      );
    }
    
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: CustomPaint(
        painter: SideLandmarkPainter(
          featurePoints: widget.featurePoints!,
          highlightIndexes: _highlightIndexes,
          blinkValue: _blinkValue,
        ),
      ),
    );
    */
  }
}
