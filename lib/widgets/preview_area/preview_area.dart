import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/image_state.dart';
import 'top_navigation_bar.dart';
import 'image_display.dart' hide Logger; // 使用 hide 关键字避免 Logger 重复导入
import 'bottom_toolbar.dart';
import 'style_preview.dart';
import '../../utils/logger.dart'; // 主要的 Logger 导入


class PreviewArea extends StatefulWidget {
  /// 是否显示"炫"按钮
  final bool showBeautifyButton;
  
  /// "炫"按钮点击回调
  final VoidCallback? onBeautifyPressed;

  /// 图片显示区域的全局键
  final GlobalKey<State<ImageDisplay>>? imageDisplayKey;

  const PreviewArea({
    Key? key, 
    this.showBeautifyButton = true,
    this.onBeautifyPressed,
    this.imageDisplayKey,
  }) : super(key: key);

  @override
  PreviewAreaState createState() => PreviewAreaState();
}

class PreviewAreaState extends State<PreviewArea> {
  // 使用正确的类型引用，ImageDisplay的State类型是私有的_ImageDisplayState
  // 所以我们使用通用的State<ImageDisplay>类型
  final GlobalKey<State<ImageDisplay>> _imageDisplayKey = GlobalKey();

  /// 触发缩略图淡出动画
  void startThumbnailFadeAnimation() {
    Logger.i('预览区域', '触发缩略图淡出动画');
    
    // 直接获取ImageState并隐藏按钮
    final imageState = Provider.of<ImageState>(context, listen: false);
    imageState.hideButtons();
    Logger.i('预览区域', '直接隐藏美旅启程按钮');
    
    // 然后再触发缩略图淡出动画
    final imageDisplayState = _imageDisplayKey.currentState;
    if (imageDisplayState != null) {
      // 使用dynamic类型调用方法，因为我们知道这个方法存在
      (imageDisplayState as dynamic).startThumbnailFadeAnimation();
      
      // 隐藏所有特征点
      (imageDisplayState as dynamic).hideAllPoints();
      Logger.i('预览区域', '隐藏所有特征点');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // 顶部导航栏
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            child: const SizedBox(
              height: 60,
              child: TopNavigationBar(),
            ),
          ),
          // 图片显示区
          Expanded(
            child: ImageDisplay(
              key: widget.imageDisplayKey ?? _imageDisplayKey,
            ),
          ),
          // 底部工具栏
          ClipRRect(
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
            child: SizedBox(
              height: 60,
              child: BottomToolbar(
                showBeautifyButton: widget.showBeautifyButton,
                onBeautifyPressed: widget.onBeautifyPressed,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
