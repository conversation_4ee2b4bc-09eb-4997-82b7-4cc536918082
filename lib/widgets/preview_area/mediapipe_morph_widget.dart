import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../services/mediapipe_transformation_service.dart';

/// MediaPipe变形预览组件
/// 
/// 用于展示使用MediaPipe实现的面部变形效果
class MediapipeMorphWidget extends StatefulWidget {
  const MediapipeMorphWidget({Key? key}) : super(key: key);

  @override
  State<MediapipeMorphWidget> createState() => _MediapipeMorphWidgetState();
}

class _MediapipeMorphWidgetState extends State<MediapipeMorphWidget> {
  /// 变形服务
  final MediapipeTransformationService _transformationService = MediapipeTransformationService();
  
  /// 源图像路径
  String? _sourceImagePath;
  
  /// 变形后的图像路径
  String? _morphedImagePath;
  
  /// 加载状态
  bool _isLoading = false;
  
  /// 当前选中的区域
  String _selectedRegion = 'nose';
  
  /// 当前选中的参数
  String _selectedParam = 'bridge_height';
  
  /// 参数值
  double _paramValue = 0.0;
  
  /// 区域参数映射
  final Map<String, List<String>> _regionParams = {
    'face_contour': ['contour_tighten', 'chin_adjust', 'cheekbone_adjust', 'face_shape'],
    'nose': ['bridge_height', 'tip_adjust', 'nostril_width', 'base_height'],
    'eyes': ['double_fold', 'canthal_tilt', 'eye_bag_removal', 'outer_corner_lift'],
    'lips': ['lip_shape', 'mouth_corner'],
    'anti_aging': ['nasolabial_folds', 'wrinkle_removal', 'forehead_fullness', 'facial_firmness'],
  };
  
  /// 区域名称映射
  final Map<String, String> _regionNames = {
    'face_contour': '面部轮廓',
    'nose': '鼻部塑形',
    'eyes': '眼部美化',
    'lips': '唇部造型',
    'anti_aging': '抗衰冻龄',
  };
  
  /// 参数名称映射
  final Map<String, String> _paramNames = {
    'contour_tighten': '轮廓收紧',
    'chin_adjust': '下巴调整',
    'cheekbone_adjust': '颧骨调整',
    'face_shape': '脸型优化',
    'bridge_height': '鼻梁高度',
    'tip_adjust': '鼻尖调整',
    'nostril_width': '鼻翼宽度',
    'base_height': '鼻基抬高',
    'double_fold': '双眼皮',
    'canthal_tilt': '开眼角',
    'eye_bag_removal': '去眼袋',
    'outer_corner_lift': '提眼尾',
    'lip_shape': '唇形调整',
    'mouth_corner': '嘴角上扬',
    'nasolabial_folds': '法令纹',
    'wrinkle_removal': '去皱纹',
    'forehead_fullness': '额头饱满',
    'facial_firmness': '面容紧致',
  };
  
  @override
  void initState() {
    super.initState();
    _loadTestImage();
  }
  
  @override
  void dispose() {
    _transformationService.dispose();
    super.dispose();
  }
  
  /// 加载测试图像
  Future<void> _loadTestImage() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // 使用测试图像
      final String testImagePath = path.join(
        Directory.current.path, 'testdata', 'test_face.jpg');
      
      // 设置图像
      final bool success = await _transformationService.setImage(testImagePath);
      
      if (success) {
        setState(() {
          _sourceImagePath = testImagePath;
        });
      } else {
        print('加载测试图像失败');
      }
    } catch (e) {
      print('加载测试图像时出错: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 应用变形
  Future<void> _applyTransformation() async {
    if (_sourceImagePath == null) {
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // 更新变形参数
      _transformationService.updateTransformationParam(
        _selectedRegion, _selectedParam, _paramValue);
      
      // 应用变形
      final String? outputPath = await _transformationService.applyTransformation();
      
      if (outputPath != null) {
        setState(() {
          _morphedImagePath = outputPath;
        });
      } else {
        print('应用变形失败');
      }
    } catch (e) {
      print('应用变形时出错: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 选择图像
  Future<void> _selectImage() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // 这里可以使用file_picker或file_selector插件选择图像
      // 为简化示例，我们使用固定的测试图像
      await _loadTestImage();
    } catch (e) {
      print('选择图像时出错: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MediaPipe面部变形'),
        actions: [
          IconButton(
            icon: const Icon(Icons.photo),
            onPressed: _selectImage,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // 图像显示区域
                Expanded(
                  child: Row(
                    children: [
                      // 源图像
                      Expanded(
                        child: _buildImageContainer(
                          '原始图像',
                          _sourceImagePath,
                        ),
                      ),
                      
                      // 变形后的图像
                      Expanded(
                        child: _buildImageContainer(
                          '变形后图像',
                          _morphedImagePath,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 控制面板
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 区域选择
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: '选择区域',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedRegion,
                        items: _regionNames.entries.map((entry) {
                          return DropdownMenuItem<String>(
                            value: entry.key,
                            child: Text(entry.value),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedRegion = value;
                              _selectedParam = _regionParams[value]!.first;
                              _paramValue = 0.0;
                            });
                          }
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 参数选择
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: '选择参数',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedParam,
                        items: _regionParams[_selectedRegion]!.map((param) {
                          return DropdownMenuItem<String>(
                            value: param,
                            child: Text(_paramNames[param] ?? param),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedParam = value;
                              _paramValue = 0.0;
                            });
                          }
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 参数值滑块
                      Row(
                        children: [
                          Expanded(
                            child: Slider(
                              min: -1.0,
                              max: 1.0,
                              divisions: 20,
                              value: _paramValue,
                              label: _paramValue.toStringAsFixed(2),
                              onChanged: (value) {
                                setState(() {
                                  _paramValue = value;
                                });
                              },
                            ),
                          ),
                          Text(_paramValue.toStringAsFixed(2)),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 应用按钮
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _sourceImagePath != null
                              ? _applyTransformation
                              : null,
                          child: const Text('应用变形'),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
  
  /// 构建图像容器
  Widget _buildImageContainer(String title, String? imagePath) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(8),
            child: Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          Expanded(
            child: Center(
              child: imagePath != null
                  ? Image.file(
                      File(imagePath),
                      fit: BoxFit.contain,
                    )
                  : const Text('无图像'),
            ),
          ),
        ],
      ),
    );
  }
}
