import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/image_state.dart';
import '../../utils/image_manager.dart';

class TopNavigationBar extends StatelessWidget {
  const TopNavigationBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧导入按钮
          Consumer<ImageState>(
            builder: (context, imageState, child) {
              return IconButton(
                icon: const Icon(
                  Icons.add_photo_alternate_outlined,
                  color: Color(0xFF7B61FF),
                  size: 28,
                ),
                tooltip: '导入新图片',
                onPressed: () async {
                  debugPrint('开始导入新图片...');
                  
                  // 重置所有状态
                  imageState.reset();
                  debugPrint('已重置所有状态');
                  
                  // 导入新图片
                  final imagePath = await ImageManager.importImage();
                  if (imagePath != null) {
                    debugPrint('选择了新图片: $imagePath');
                    imageState.setFrontImage(imagePath);
                    debugPrint('已设置为正面照片');
                  } else {
                    debugPrint('用户取消了图片选择');
                  }
                },
              );
            },
          ),
          
          // 中间品牌标题
          Flexible(
            child: Center(
              child: const Text(
                'BeautiFun',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF7B61FF),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          
          // 右侧用户图标
          IconButton(
            icon: const Icon(
              Icons.account_circle_outlined,
              color: Color(0xFF7B61FF),
              size: 28,
            ),
            onPressed: () {
              // 用户账号功能待实现
            },
          ),
        ],
      ),
    );
  }
}
