import 'package:flutter/material.dart';

class ThumbnailAnimationController extends ChangeNotifier {
  static const double NORMAL_SCALE = 1.0;
  static const double SHRINK_SCALE = 2.0 / 3.0;
  
  // 动画控制器
  late AnimationController _shrinkController;
  late AnimationController _breathingController;
  
  // 缩放动画
  late Animation<double> _shrinkAnimation;
  late Animation<double> _breathingAnimation;
  
  // 当前选中的索引
  int? _selectedIndex;
  bool _isAnimating = false;
  
  // 获取当前缩放值
  double getScale(int index) {
    if (_selectedIndex == null) return NORMAL_SCALE;
    
    if (_isAnimating) {
      // 所有缩略图执行缩小动画
      return _shrinkAnimation.value;
    } else if (index == _selectedIndex && _breathingController.isAnimating) {
      // 选中的缩略图执行呼吸动画
      return _breathingAnimation.value;
    } else {
      // 未选中的缩略图保持缩小状态
      return SHRINK_SCALE;
    }
  }
  
  void initialize(TickerProvider vsync) {
    // 初始化缩小动画控制器
    _shrinkController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: vsync,
    );
    
    // 初始化呼吸动画控制器
    _breathingController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: vsync,
    );
    
    // 设置缩小动画
    _shrinkAnimation = Tween<double>(
      begin: NORMAL_SCALE,
      end: SHRINK_SCALE,
    ).animate(
      CurvedAnimation(
        parent: _shrinkController,
        curve: Curves.easeInOut,
      ),
    );
    
    // 设置呼吸动画
    _breathingAnimation = Tween<double>(
      begin: SHRINK_SCALE,
      end: NORMAL_SCALE,
    ).animate(
      CurvedAnimation(
        parent: _breathingController,
        curve: Curves.easeInOut,
      ),
    );
    
    // 添加动画状态监听
    _shrinkController.addStatusListener(_onShrinkAnimationStatus);
    _breathingController.addStatusListener(_onBreathingAnimationStatus);
  }
  
  void dispose() {
    _shrinkController.dispose();
    _breathingController.dispose();
    super.dispose();
  }
  
  void selectThumbnail(int index) {
    if (_selectedIndex == index) return;
    
    // 停止当前的呼吸动画
    _breathingController.stop();
    
    _selectedIndex = index;
    _isAnimating = true;
    
    // 开始缩小动画
    _shrinkController
      ..reset()
      ..forward();
      
    notifyListeners();
  }
  
  void _onShrinkAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      _isAnimating = false;
      
      // 缩小动画完成后，开始呼吸动画
      _breathingController
        ..reset()
        ..repeat(reverse: true);
        
      notifyListeners();
    }
  }
  
  void _onBreathingAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed || status == AnimationStatus.dismissed) {
      notifyListeners();
    }
  }
  
  bool get isAnimating => _isAnimating;
  int? get selectedIndex => _selectedIndex;
  
  // 添加动画值监听器
  void addListener(VoidCallback listener) {
    _shrinkController.addListener(listener);
    _breathingController.addListener(listener);
    super.addListener(listener);
  }
  
  void removeListener(VoidCallback listener) {
    _shrinkController.removeListener(listener);
    _breathingController.removeListener(listener);
    super.removeListener(listener);
  }
}
