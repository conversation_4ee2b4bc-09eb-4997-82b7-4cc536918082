import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/logger.dart';
import '../../utils/image_manager.dart';
import '../../models/image_state.dart';
import '../../beautify_feature/services/transformation_service.dart';

/// 侧面照片覆盖层组件
/// 在预览区右下角显示一个半透明的侧面人像图标，中间有➕号
class ProfilePhotoOverlay extends StatelessWidget {
  final double containerWidth;
  final double containerHeight;
  
  const ProfilePhotoOverlay({
    Key? key,
    required this.containerWidth,
    required this.containerHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final imageState = Provider.of<ImageState>(context);
    final transformationService = Provider.of<TransformationService>(context);
    
    // 计算侧面图层的尺寸（预览区的1/5大小）
    final width = containerWidth / 5;
    final height = containerHeight / 5;
    
    // 注释掉侧面图相关日志
    // Logger.log('ProfilePhotoOverlay', 'build', '构建侧面照片覆盖层，尺寸: ${width}x$height');
    
    // 只在显示侧面图时才将特征点显示设置为 false
    // 这样在变形后和参数项切换后，特征点显示不会被强制设置为 false
    // 注释掉这段代码，避免影响特征点的呼吸效果显示
    // if (transformationService.getSimpleDeformationRenderer() != null) {
    //   transformationService.getSimpleDeformationRenderer()!.setShowFeaturePoints(false);
    // }
    
    return Positioned(
      right: 16,
      bottom: 16,
      width: width,
      height: height,
      child: GestureDetector(
        onTap: () async {
          // 注释掉侧面图相关日志
          // Logger.log('ProfilePhotoOverlay', 'onTap', '点击侧面照片覆盖层');
          
          // 如果已经有侧脸图片，则切换显示状态
          if (imageState.hasSideImage) {
            imageState.toggleSideImage();
            return;
          }
          
          // 导入侧脸图片
          final imagePath = await ImageManager.importImage();
          if (imagePath != null) {
            // 注释掉侧面图相关日志
            // Logger.log('ProfilePhotoOverlay', 'onTap', '选择了侧脸图片: $imagePath');
            imageState.setSideImage(imagePath);
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.white.withOpacity(0.5),
              width: 1.0,
            ),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 侧面人像图片
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(7),
                  child: imageState.hasSideImage && imageState.showSideImage
                      ? Image.file(
                          File(imageState.sideImagePath!),
                          fit: BoxFit.cover,
                          opacity: const AlwaysStoppedAnimation<double>(0.7),
                        )
                      : Opacity(
                          opacity: 0.7,
                          child: Image.asset(
                            'assets/sideface.jpg',
                            fit: BoxFit.cover,
                          ),
                        ),
                ),
              ),
              
              // 如果没有侧脸图片或不显示侧脸图片，则显示加号
              if (!imageState.hasSideImage || !imageState.showSideImage)
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.7),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.add,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
