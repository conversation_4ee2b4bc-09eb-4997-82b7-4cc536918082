import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';

// 核心组件和工具
import '../../utils/logger.dart';
import '../../models/image_state.dart';

// 特征点相关
import '../../core/models/feature_point.dart';
import '../../core/feature_points_data.dart';

// 变形相关
import '../../core/side_profile_processor.dart';
import '../../core/simple_deformation_renderer.dart';
import '../../core/deformation_cache_manager.dart';
import '../../beautify_feature/models/nose_parameter.dart';
import '../../beautify_feature/services/transformation_service.dart';

// UI组件
import '../buttons/animated_action_button.dart';
import 'style_preview.dart';
import 'animated_door_background.dart';
import '../../painters/landmark_painter.dart';
import '../../widgets/simple_deformation_area_widget.dart';
import 'profile_photo_overlay.dart'; 
import 'side_profile_landmarks_overlay.dart'; 
import 'landmarks_overlay.dart';

class ImageDisplay extends StatefulWidget {
  // 使用key参数，不使用const构造函数
  const ImageDisplay({Key? key}) : super(key: key);

  @override
  State<ImageDisplay> createState() => _ImageDisplayState();
}

class _ImageDisplayState extends State<ImageDisplay> with TickerProviderStateMixin {
  // 特征点数据
  List<Map<String, dynamic>>? _landmarks;
  // 当前需要显示的特征点索引集合
  Set<int> _visibleIndexes = {};
  // 需要高亮闪烁的特征点索引集合
  Set<int> _highlightIndexes = {};
  
  // 对称点对列表
  List<Map<String, dynamic>> _symmetryPairs = [];
  
  // 闪烁动画控制器
  late AnimationController _blinkController;
  // 闪烁动画值
  double _blinkValue = 1.0;
  
  // 使用单独的重绘控制器，避免触发整个UI重建
  final GlobalKey<LandmarksOverlayState> _landmarksKey = GlobalKey<LandmarksOverlayState>();
  
  // 图片尺寸
  ui.Size? _imageSize;
  // 容器尺寸
  ui.Size? _containerSize;
  // 图片缩放比例
  double _scale = 1.0;
  // 图片偏移
  Offset _offset = Offset.zero;

  bool _forceHideJourneyButton = false;

  // 动画相关
  late AnimationController _imageAnimationController;
  late Animation<double> _imageOpacityAnimation;
  late Animation<double> _buttonOpacityAnimation;
  late Animation<Offset> _buttonSlideAnimation;
  late Animation<double> _breathingAnimation;
  late AnimationController _breathingController;
  
  // 样式预览组件的Key
  final GlobalKey<StylePreviewState> _stylePreviewKey = GlobalKey();
  
  // 缩略图相关
  final double _thumbnailSpacing = 8.0;
  final double _thumbnailAspectRatio = 1.2;

  // 简化版变形区域渲染器
  SimpleDeformationRenderer? _simpleDeformationRenderer;

  @override
  void initState() {
    super.initState();
    
    _setupInitialAnimations();
    
    // 将LandmarksOverlay的引用传递给TransformationService
    final transformationService = Provider.of<TransformationService>(context, listen: false);
    transformationService.setLandmarksKey(_landmarksKey);
    Logger.log('ImageDisplay', 'initState', '🔑 [设置] 将特征点覆盖层引用传递给变形服务');
    
    // 获取简化版变形区域渲染器
    _simpleDeformationRenderer = transformationService.getSimpleDeformationRenderer();
    Logger.log('ImageDisplay', 'initState', '🔑 [设置] 获取简化版变形区域渲染器');
    
    // 不再自动加载测试图片
    // Future.delayed(const Duration(milliseconds: 1000), () {
    //   _loadTestImage();
    // });
  }
  
  /// 自动加载测试图片
  void _loadTestImage() async {
    Logger.i('图片显示', '自动加载测试图片');
    final testImagePath = 'testdata/test_face.jpg';
    
    // 检查测试图片是否存在
    final file = File(testImagePath);
    if (await file.exists()) {
      Logger.i('图片显示', '找到测试图片: $testImagePath');
      
      // 更新ImageState
      final imageState = Provider.of<ImageState>(context, listen: false);
      imageState.setFrontImage(testImagePath);
      
      // 处理图片
      await _processImage(testImagePath);
    } else {
      Logger.e('图片显示', '测试图片不存在: $testImagePath');
    }
  }

  void _setupInitialAnimations() {
    // 初始化闪烁动画控制器
    _blinkController = AnimationController(
      duration: const Duration(milliseconds: 500),  // 闪烁周期500ms
      vsync: this,
    );
    
    // 添加闪烁动画监听 - 优化为只更新闪烁部分
    _blinkController.addListener(() {
      // 使用局部更新而不是整个UI重建
      _blinkValue = _blinkController.value;
      
      // 只更新特征点绘制器
      final landmarksState = _landmarksKey.currentState;
      if (landmarksState != null) {
        landmarksState.updateBlinkValue(_blinkValue);
      }
    });

    _imageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    
    _breathingController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat(reverse: true);

    _breathingAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _breathingController,
      curve: Curves.easeInOut,
    ));

    _imageOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _imageAnimationController,
      curve: const Interval(0.0, 0.3, curve: Curves.easeOut),
    ));

    _buttonOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _imageAnimationController,
      curve: const Interval(0.3, 0.6, curve: Curves.easeIn),
    ));

    _buttonSlideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0),
      end: const Offset(0.0, 0),  // 移动到中心点
    ).animate(CurvedAnimation(
      parent: _imageAnimationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOutQuart),
    ));
  }

  void _startJourneyAnimation() async {
    // 获取当前图片路径
    final imageState = Provider.of<ImageState>(context, listen: false);
    
    // 直接设置强制隐藏标志
    setState(() {
      _forceHideJourneyButton = true;
    });
    
    if (imageState.frontImagePath != null) {
      // 处理图片并检测特征点
      await _processImage(imageState.frontImagePath!);
    }
    
    // 启动动画
    _stylePreviewKey.currentState?.startAnimation();
  }

  // 计算缩略图尺寸
  Map<String, double> _calculateThumbnailSize(double containerWidth) {
    // 使用原始宽度，不进行缩放
    final thumbnailWidth = (containerWidth - (_thumbnailSpacing * 3)) / 4;
    final thumbnailHeight = thumbnailWidth * _thumbnailAspectRatio;
    
    return {
      'width': thumbnailWidth,
      'height': thumbnailHeight,
      'totalWidth': containerWidth,  // 使用原始宽度
    };
  }

  @override
  void dispose() {
    _imageAnimationController.dispose();
    _breathingController.dispose();
    _blinkController.dispose();
    
    super.dispose();
  }
  
  /// 显示区域特征点
  void showAreaPoints(List<int> areaPoints) {
    Logger.i('图片显示', '开始显示区域特征点');
    Logger.d('图片显示', '收到特征点数量: ${areaPoints.length}');
    Logger.d('图片显示', '特征点索引: $areaPoints');
    
    setState(() {
      Logger.d('图片显示', '清除现有特征点');
      // 先隐藏所有点
      _visibleIndexes.clear();
      _highlightIndexes.clear();
      _symmetryPairs.clear(); // 清除所有对称点对
      _blinkController.stop();
      
      Logger.d('图片显示', '添加新的特征点');
      // 显示区域点
      _visibleIndexes.addAll(areaPoints);
      Logger.d('图片显示', '当前可见点数量: ${_visibleIndexes.length}');
    });
    
    Logger.i('图片显示', '区域特征点显示完成');
  }

  /// 高亮显示参数特征点
  void highlightParameterPoints(List<int> areaPoints, List<int> parameterPoints) {
    // 获取对称点对
    _getSymmetryPairsForCurrentArea();
    
    setState(() {
      // 先隐藏所有点
      _visibleIndexes.clear();
      _highlightIndexes.clear();
      _symmetryPairs.clear(); // 清除所有对称点对
      
      // 显示区域点和高亮点
      _visibleIndexes.addAll(areaPoints);
      _highlightIndexes.addAll(parameterPoints);
      
      // 只为当前参数点添加对称线
      _addSymmetryPairsForPoints(parameterPoints);
      
      // 开始闪烁动画
      _blinkController.repeat(reverse: true);
    });
    
    Logger.d('图像显示', '高亮参数点并显示对称点对连接线');
  }

  /// 显示参数点并添加动画效果
  void showParameterPointsWithAnimation(List<int> parameterPoints, Duration duration) {
    // 记录参数点显示信息
    Logger.i('图像显示', '开始显示参数点 | 参数点数量: ${parameterPoints.length} | 持续时间: $duration');
    
    if (parameterPoints.isEmpty) {
      Logger.w('图像显示', '参数点列表为空，无法显示参数点');
      return;
    }
    
    // 直接从DeformationCacheManager获取最新的特征点位置数据
    Logger.i('图像显示', '从DeformationCacheManager获取最新特征点数据');
    
    // 获取最新的特征点数据
    List<FeaturePoint>? latestFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
    
    if (latestFeaturePoints != null && latestFeaturePoints.isNotEmpty) {
      Logger.i('图像显示', '成功获取最新特征点数据，共 ${latestFeaturePoints.length} 个点');
      
      // 记录几个关键特征点的位置，用于调试
      if (parameterPoints.isNotEmpty) {
        for (final index in parameterPoints) {
          final point = latestFeaturePoints.firstWhere(
            (p) => p.index == index,
            orElse: () => FeaturePoint(index: -1, x: 0, y: 0, z: 0, visibility: 0, confidence: 0, isPrimary: false),
          );
          
          if (point.index != -1) {
            Logger.i('图像显示', '高亮特征点[$index] 当前位置: (x=${point.x.toStringAsFixed(2)}, y=${point.y.toStringAsFixed(2)})');
          }
        }
      }
      
      // 更新内部特征点数据
      _updateLandmarksFromFeaturePoints(latestFeaturePoints);
    } else {
      Logger.w('图像显示', '无法从缓存获取特征点数据，尝试从变形服务获取');
      
      // 如果缓存中没有特征点数据，则从变形服务获取
      final transformationService = Provider.of<TransformationService>(context, listen: false);
      final featurePoints = transformationService.getFeaturePoints();
      
      if (featurePoints != null && featurePoints.isNotEmpty) {
        Logger.i('图像显示', '成功从变形服务获取特征点数据，共 ${featurePoints.length} 个点');
        _updateLandmarksFromFeaturePoints(featurePoints);
      } else {
        Logger.w('图像显示', '无法获取任何特征点数据');
      }
    }
    
    setState(() {
      // 先隐藏所有点
      _visibleIndexes.clear();
      _highlightIndexes.clear();
      _symmetryPairs.clear(); // 清除所有对称点对      
      // 显示高亮点
      _highlightIndexes.addAll(parameterPoints);
      
      // 确保可见点包含高亮点，这样才能正确显示对称线
      _visibleIndexes.addAll(parameterPoints);
      
      // 只为当前参数点添加对称线
      _addSymmetryPairsForPoints(parameterPoints);
      
      // 开始闪烁动画，使用指定的持续时间
      _blinkController.reset();
      _blinkController.repeat(reverse: true, period: duration);
    });
    
    Logger.i('图像显示', '显示参数点并添加动画效果完成，持续时间：$duration');
  }
  
  /// 从特征点列表更新内部特征点数据
  void _updateLandmarksFromFeaturePoints(List<FeaturePoint> featurePoints) {
    if (featurePoints.isEmpty) {
      Logger.w('图像显示', '特征点列表为空，无法更新内部特征点数据');
      return;
    }
    
    // 记录原始特征点数据的一些关键位置信息，用于比较
    Map<int, Map<String, double>> oldPositions = {};
    if (_landmarks != null && _landmarks!.isNotEmpty) {
      // 选取几个关键特征点记录位置
      final keyIndices = [1, 4, 8, 33, 61, 199, 200, 331];
      for (final lm in _landmarks!) {
        final index = lm['index'] as int;
        if (keyIndices.contains(index)) {
          oldPositions[index] = {
            'x': (lm['x'] is int) ? (lm['x'] as int).toDouble() : (lm['x'] as double),
            'y': (lm['y'] is int) ? (lm['y'] as int).toDouble() : (lm['y'] as double),
          };
        }
      }
    }
    
    // 创建新的特征点数据列表
    final newLandmarks = <Map<String, dynamic>>[];
    
    // 将FeaturePoint对象转换为内部特征点格式
    for (final point in featurePoints) {
      newLandmarks.add({
        'index': point.index,
        'x': point.x,
        'y': point.y,
        'z': point.z,
        'visibility': point.visibility,
        'confidence': point.confidence,
        'primary': point.isPrimary,
        'secondary': false, // 默认为false，因为我们没有这个信息
      });
    }
    
    // 比较新旧特征点位置
    Map<int, Map<String, double>> newPositions = {};
    for (final lm in newLandmarks) {
      final index = lm['index'] as int;
      if (oldPositions.containsKey(index)) {
        newPositions[index] = {
          'x': (lm['x'] is int) ? (lm['x'] as int).toDouble() : (lm['x'] as double),
          'y': (lm['y'] is int) ? (lm['y'] as int).toDouble() : (lm['y'] as double),
        };
      }
    }
    
    // 输出位置变化信息
    if (oldPositions.isNotEmpty && newPositions.isNotEmpty) {
      for (final index in oldPositions.keys) {
        if (newPositions.containsKey(index)) {
          final oldPos = oldPositions[index]!;
          final newPos = newPositions[index]!;
          final deltaX = newPos['x']! - oldPos['x']!;
          final deltaY = newPos['y']! - oldPos['y']!;
          
          // 计算位置变化的距离
          final distance = sqrt(deltaX * deltaX + deltaY * deltaY);
          
          if (distance > 0.001) { // 只输出有明显变化的特征点
            Logger.i('图像显示', '特征点[$index] 位置变化: (∆x=${deltaX.toStringAsFixed(2)}, ∆y=${deltaY.toStringAsFixed(2)}, 距离=${distance.toStringAsFixed(2)})');
          }
        }
      }
    }
    
    // 更新内部特征点数据
    _landmarks = newLandmarks;
    Logger.i('图像显示', '已更新内部特征点数据，共 ${newLandmarks.length} 个点');
  }
  
  /// 显示参数点（用于左侧面板点击参数项）
  void showParameterPoints(List<int> parameterPoints) {
    // 记录参数点数量和内容，用于调试
    Logger.i('图像显示', '准备显示参数点: ${parameterPoints.length} 个点');
    Logger.d('图像显示', '参数点列表: $parameterPoints');
    
    // 获取最新的特征点数据
    final transformationService = Provider.of<TransformationService>(context, listen: false);
    
    // 检查是否有变形后的图像
    final isDeforming = transformationService.isDeforming;
    final hasTransformedImage = transformationService.transformedImagePath != null;
    
    // 如果有变形后的图像并且不在变形过程中，获取最新的特征点数据
    if (hasTransformedImage && !isDeforming) {
      Logger.i('图像显示', '获取最新的特征点数据');
      
      // 获取最新的特征点数据
      final featurePointManager = transformationService.featurePointManager;
      if (featurePointManager != null) {
        final latestFeaturePoints = featurePointManager.getFeaturePoints();
        if (latestFeaturePoints != null && latestFeaturePoints.isNotEmpty) {
          // 更新特征点数据
          _updateLandmarksFromFeaturePoints(latestFeaturePoints);
          Logger.i('图像显示', '已更新特征点数据，共 ${latestFeaturePoints.length} 个点');
        } else {
          Logger.w('图像显示', '最新的特征点数据为空');
        }
      } else {
        Logger.w('图像显示', '特征点管理器为空');
      }
    }
    
    setState(() {
      // 先隐藏所有点
      _visibleIndexes.clear();
      _highlightIndexes.clear();
      _symmetryPairs.clear(); // 清除所有对称点对      
      // 只显示参数点，不显示其他点
      _highlightIndexes.addAll(parameterPoints);
      _visibleIndexes.addAll(parameterPoints);
      
      // 只为当前参数点添加对称线
      _addSymmetryPairsForPoints(parameterPoints);
      
      // 开始吸气动画（呼吸效果）
      _blinkController.reset();
      _blinkController.repeat(reverse: true, period: const Duration(milliseconds: 1500));
    });
    
    Logger.i('图像显示', '显示参数点完成: ${parameterPoints.length} 个点');
  }
  
  /// 只为指定的点添加对称线
  void _addSymmetryPairsForPoints(List<int> points) {
    // 创建一个集合存储所有点
    final Set<int> pointSet = points.toSet();
    
    // 清除现有的对称点对
    _symmetryPairs.clear();
    
    // 获取当前选中的区域
    final transformationService = Provider.of<TransformationService>(context, listen: false);
    final currentArea = transformationService.selectedArea;
    
    Logger.i('图像显示', '添加对称点对，当前区域: $currentArea');
    
    // 记录每个区域添加的对称点对数量
    final Map<String, int> pairCountByCategory = {
      'face_contour': 0,
      'eyes': 0,
      'nose': 0,
      'lips': 0,
      'anti_aging': 0,
    };
    
    // 1. 处理面部轮廓对称点对 - 仅当当前区域是面部轮廓时
    if (currentArea == 'face_contour') {
      final faceContourConfig = beautyAreaConfigs['face_contour'];
      final symmetryPairs = faceContourConfig?.region.symmetricPairs ?? [];
      
      for (final pair in symmetryPairs) {
        final int leftPoint = pair['left'] as int;
        final int rightPoint = pair['right'] as int;
        
        // 只有当左点或右点在指定的点集合中时，才添加这对对称点
        if (pointSet.contains(leftPoint) || pointSet.contains(rightPoint)) {
          _symmetryPairs.add({
            'point1': leftPoint,
            'point2': rightPoint,
            'category': 'face_contour',
          });
          
          // 确保两个点都是可见的
          _visibleIndexes.add(leftPoint);
          _visibleIndexes.add(rightPoint);
          
          // 将对称点对中的点也添加到高亮点集合，使它们也有动画效果
          _highlightIndexes.add(leftPoint);
          _highlightIndexes.add(rightPoint);
          
          pairCountByCategory['face_contour'] = pairCountByCategory['face_contour']! + 1;
        }
      }
    }
    
    // 2. 处理眼部对称点对 - 仅当当前区域是眼部时
    if (currentArea == 'eyes') {
      final eyesConfig = beautyAreaConfigs['eyes'];
      final symmetryPairs = eyesConfig?.region.symmetricPairs ?? [];
      
      for (final pair in symmetryPairs) {
        final int leftPoint = pair['left'] as int;
        final int rightPoint = pair['right'] as int;
        
        if (pointSet.contains(leftPoint) || pointSet.contains(rightPoint)) {
          _symmetryPairs.add({
            'point1': leftPoint,
            'point2': rightPoint,
            'category': 'eyes',
          });
          
          _visibleIndexes.add(leftPoint);
          _visibleIndexes.add(rightPoint);
          
          // 将对称点对中的点也添加到高亮点集合
          _highlightIndexes.add(leftPoint);
          _highlightIndexes.add(rightPoint);
          
          pairCountByCategory['eyes'] = pairCountByCategory['eyes']! + 1;
        }
      }
    }
    
    // 3. 处理鼻部对称点对 - 仅当当前区域是鼻部时
    if (currentArea == 'nose') {
      // 使用feature_points_data.dart中的鼻部对称配置
      final noseConfig = beautyAreaConfigs['nose'];
      final symmetryPairs = noseConfig?.region.symmetricPairs ?? [];
      
      for (final pair in symmetryPairs) {
        final int leftPoint = pair['left'] as int;
        final int rightPoint = pair['right'] as int;
        
        if (pointSet.contains(leftPoint) || pointSet.contains(rightPoint)) {
          _symmetryPairs.add({
            'point1': leftPoint,
            'point2': rightPoint,
            'category': 'nose',
          });
          
          _visibleIndexes.add(leftPoint);
          _visibleIndexes.add(rightPoint);
          
          // 将对称点对中的点也添加到高亮点集合
          _highlightIndexes.add(leftPoint);
          _highlightIndexes.add(rightPoint);
          
          pairCountByCategory['nose'] = pairCountByCategory['nose']! + 1;
        }
      }
    }
    
    // 4. 处理嘴唇对称点对 - 仅当当前区域是嘴唇时
    if (currentArea == 'lips') {
      final lipsConfig = beautyAreaConfigs['lips'];
      final symmetryPairs = lipsConfig?.region.symmetricPairs ?? [];
      
      for (final pair in symmetryPairs) {
        final int leftPoint = pair['left'] as int;
        final int rightPoint = pair['right'] as int;
        
        if (pointSet.contains(leftPoint) || pointSet.contains(rightPoint)) {
          _symmetryPairs.add({
            'point1': leftPoint,
            'point2': rightPoint,
            'category': 'lips',
          });
          
          _visibleIndexes.add(leftPoint);
          _visibleIndexes.add(rightPoint);
          
          // 将对称点对中的点也添加到高亮点集合
          _highlightIndexes.add(leftPoint);
          _highlightIndexes.add(rightPoint);
          
          pairCountByCategory['lips'] = pairCountByCategory['lips']! + 1;
        }
      }
    }
    
    // 5. 处理抗衰对称点对 - 对所有区域都生效
    if (currentArea == 'anti_aging') {
      final antiAgingConfig = beautyAreaConfigs['anti_aging'];
      final symmetryPairs = antiAgingConfig?.region.symmetricPairs ?? [];
      
      for (final pair in symmetryPairs) {
        final int leftPoint = pair['left'] as int;
        final int rightPoint = pair['right'] as int;
        
        if (pointSet.contains(leftPoint) || pointSet.contains(rightPoint)) {
          _symmetryPairs.add({
            'point1': leftPoint,
            'point2': rightPoint,
            'category': 'anti_aging',
          });
          
          _visibleIndexes.add(leftPoint);
          _visibleIndexes.add(rightPoint);
          
          // 将对称点对中的点也添加到高亮点集合
          _highlightIndexes.add(leftPoint);
          _highlightIndexes.add(rightPoint);
          
          pairCountByCategory['anti_aging'] = pairCountByCategory['anti_aging']! + 1;
        }
      }
    }
    
    // 输出每个区域的对称点对数量
    for (final category in pairCountByCategory.keys) {
      final count = pairCountByCategory[category]!;
      if (count > 0) {
        Logger.d('图像显示', '$category 区域: 添加了 $count 对对称线');
      }
    }
    
    Logger.i('图像显示', '为参数点添加了共 ${_symmetryPairs.length} 对对称线');
  }

  /// 获取对称点对
  void _getSymmetryPairsForCurrentArea() {
    try {
      // 使用feature_points_data.dart中定义的对称点对
      final List<Map<String, dynamic>> formattedPairs = [];
      
      // 获取当前选中的区域
      final transformationService = Provider.of<TransformationService>(context, listen: false);
      final currentArea = transformationService.selectedArea;
      
      Logger.i('图像显示', '获取对称点对，当前区域: $currentArea');
      
      // 如果没有选中区域，直接返回
      if (currentArea == null || currentArea.isEmpty) {
        Logger.w('图像显示', '未选中任何区域，不添加对称点对');
        setState(() {
          _symmetryPairs.clear();
        });
        return;
      }
      
      // 记录所有特征点索引，用于调试
      final Set<int> allPointIndexes = _landmarks != null ? 
          _landmarks!.map((lm) => lm['index'] as int).toSet() : 
          <int>{};
      
      final String indexRange = allPointIndexes.isEmpty ? "空" : 
          "${allPointIndexes.isNotEmpty ? allPointIndexes.reduce((a, b) => a < b ? a : b) : 0}-${allPointIndexes.isNotEmpty ? allPointIndexes.reduce((a, b) => a > b ? a : b) : 0}";
      
      Logger.i('图像显示', '当前特征点总数: ${allPointIndexes.length}, 索引范围: $indexRange');
      
      // 检查_landmarks与_visibleIndexes的差异
      final Set<int> visibleSet = _visibleIndexes;
      final Set<int> landmarkIndices = _landmarks != null ? 
          _landmarks!.map((lm) => lm['index'] as int).toSet() : 
          <int>{};
      
      final Set<int> onlyInLandmarks = landmarkIndices.difference(visibleSet);
      final Set<int> onlyInVisible = visibleSet.difference(landmarkIndices);
      
      Logger.i('图像显示', '可见特征点总数: ${visibleSet.length}, 只在landmarks中: ${onlyInLandmarks.length}, 只在visible中: ${onlyInVisible.length}');
      
      // 根据当前选中的区域添加对称点对
      switch (currentArea) {
        case 'anti_aging':
          final antiAgingConfig = beautyAreaConfigs['anti_aging'];
          final antiAgingSymmetryPairs = antiAgingConfig?.region.symmetricPairs ?? [];
          if (antiAgingSymmetryPairs.isNotEmpty) {
            _addSymmetryPairsForArea(antiAgingSymmetryPairs, 'anti_aging', allPointIndexes, formattedPairs);
          }
          break;
        case 'eyes':
          final eyesConfig = beautyAreaConfigs['eyes'];
          final eyesSymmetryPairs = eyesConfig?.region.symmetricPairs ?? [];
          if (eyesSymmetryPairs.isNotEmpty) {
            _addSymmetryPairsForArea(eyesSymmetryPairs, 'eyes', allPointIndexes, formattedPairs);
          }
          break;
        case 'nose':
          final noseConfig = beautyAreaConfigs['nose'];
          final noseSymmetryPairs = noseConfig?.region.symmetricPairs ?? [];
          if (noseSymmetryPairs.isNotEmpty) {
            _addSymmetryPairsForArea(noseSymmetryPairs, 'nose', allPointIndexes, formattedPairs);
          }
          break;
        case 'lips':
          final lipsConfig = beautyAreaConfigs['lips'];
          final lipsSymmetryPairs = lipsConfig?.region.symmetricPairs ?? [];
          if (lipsSymmetryPairs.isNotEmpty) {
            _addSymmetryPairsForArea(lipsSymmetryPairs, 'lips', allPointIndexes, formattedPairs);
          }
          break;
        case 'face_contour':
          final faceContourConfig = beautyAreaConfigs['face_contour'];
          final faceContourSymmetryPairs = faceContourConfig?.region.symmetricPairs ?? [];
          if (faceContourSymmetryPairs.isNotEmpty) {
            _addSymmetryPairsForArea(faceContourSymmetryPairs, 'face_contour', allPointIndexes, formattedPairs);
          }
          break;
        default:
          Logger.w('图像显示', '未知区域: $currentArea，不添加对称点对');
          break;
      }
      
      // 更新对称点对
      setState(() {
        _symmetryPairs = formattedPairs;
      });
      
      Logger.i('图像显示', '当前区域 $currentArea 的对称点对更新完成: ${formattedPairs.length} 对');
    } catch (e) {
      Logger.e('图像显示', '获取对称点对时出错: $e');
      setState(() {
        _symmetryPairs = [];
      });
    }
  }
  
  // 为指定区域添加对称点对
  void _addSymmetryPairsForArea(List<Map<String, dynamic>> symmetryPairs, String category, Set<int> allPointIndexes, List<Map<String, dynamic>> formattedPairs) {
    final List<String> missingPoints = [];
    int pairCount = 0;
    
    for (final pair in symmetryPairs) {
      final int leftPoint = pair['left'] as int;
      final int rightPoint = pair['right'] as int;
      
      // 检查点是否存在于特征点数据中
      final bool leftExists = allPointIndexes.contains(leftPoint);
      final bool rightExists = allPointIndexes.contains(rightPoint);
      
      if (!leftExists || !rightExists) {
        missingPoints.add('$leftPoint-$rightPoint');
        Logger.w('图像显示', '$category 对称点对缺失: [$leftPoint-$rightPoint], '
              '左点=${leftExists ? "存在" : "不存在"}, '
              '右点=${rightExists ? "存在" : "不存在"}');
      }
      
      formattedPairs.add({
        'point1': leftPoint,
        'point2': rightPoint,
        'category': category,
      });
      
      pairCount++;
    }
    
    Logger.i('图像显示', '$category 区域对称点对: $pairCount 对, 缺失: ${missingPoints.length} 对');
  }
  
  /// 隐藏所有特征点
  void hideAllPoints() {
    Logger.i('图像显示', '🔄 hideAllPoints: 开始隐藏所有特征点');
    
    setState(() {
      // 清除可见索引和高亮索引
      _visibleIndexes.clear();
      _highlightIndexes.clear();
      // 清除对称点对
      _symmetryPairs?.clear();
      // 停止闪烁动画
      _blinkController.stop();
    });
    
    Logger.i('图像显示', '✅ hideAllPoints: 已隐藏所有特征点');
  }



  /// 获取图片尺寸
  Future<void> _loadImageSize(String imagePath) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      final image = await decodeImageFromList(bytes);
      setState(() {
        _imageSize = Size(image.width.toDouble(), image.height.toDouble());
        _updateScaleAndOffset();
      });

    } catch (e) {
      Logger.e('图片显示', '加载失败: $e');
    }
  }

  /// 处理图片并检测特征点
  Future<void> _processImage(String imagePath) async {
    try {
      Logger.i('特征点检测', '开始处理图片: $imagePath');
      
      // 调用Python程序检测特征点
      final process = await Process.run('python3', [
        'core/face_mesh_processor.py',
        '--params',
        jsonEncode({'image_path': imagePath})
      ]);
      
      if (process.exitCode != 0) {
        throw Exception('特征点检测失败: ${process.stderr}');
      }
      
      final result = jsonDecode(process.stdout as String);
      if (result['status'] == 'error') {
        throw Exception(result['message']);
      }
      
      final landmarks = result['landmarks'] as List<dynamic>;
      
      // 获取当前激活的区域和参数配置
      final areaConfig = result['area_config'] as Map<String, dynamic>?;
      final paramConfig = result['param_config'] as Map<String, dynamic>?;
      
      // 处理特征点类型
      final processedLandmarks = landmarks.map((landmark) {
        final Map<String, dynamic> point = Map<String, dynamic>.from(landmark as Map);
        final int index = point['index'] as int;
        
        // 检查是否是主要控制点
        if (paramConfig != null && 
            paramConfig['primary_points'] != null && 
            (paramConfig['primary_points'] as List).contains(index)) {
          point['primary'] = true;
        }
        // 检查是否是次要控制点
        else if (paramConfig != null && 
                 paramConfig['secondary_points'] != null && 
                 (paramConfig['secondary_points'] as List).contains(index)) {
          point['secondary'] = true;
        }
        
        return point;
      }).toList();
      
      // 记录原始特征点数据的详细信息
      final List<int> originalIndices = landmarks.map((lm) => (lm['index'] as int)).toList();
      originalIndices.sort();
      final int minOriginalIndex = originalIndices.first;
      final int maxOriginalIndex = originalIndices.last;
      
      // 检查是否有缺失的索引
      final List<int> missingIndices = [];
      for (int i = 0; i < 478; i++) {
        if (!originalIndices.contains(i)) {
          missingIndices.add(i);
        }
      }
      
      Logger.i('特征点检测', '原始特征点索引范围: $minOriginalIndex-$maxOriginalIndex | 总数: ${originalIndices.length} | 缺失点数: ${missingIndices.length}');
      
      if (missingIndices.isNotEmpty) {
        if (missingIndices.length <= 20) {
          Logger.i('特征点检测', '缺失的索引: $missingIndices');
        } else {
          final firstTen = missingIndices.take(10).toList();
          final lastTen = missingIndices.skip(missingIndices.length - 10).take(10).toList();
          Logger.i('特征点检测', '部分缺失的索引: $firstTen...${lastTen}');
        }
      }
      
      setState(() {
        _landmarks = processedLandmarks;
        _updateScaleAndOffset();
        
        // 检查当前是否应该显示特征点
        // 如果是初次加载图像，显示特征点；如果用户已经点击了“炫”按钮，不显示特征点
        if (_visibleIndexes.isEmpty) {
          // 初次加载图像，显示特征点
          _visibleIndexes = _landmarks!.map((p) => p['index'] as int).toSet();
          Logger.i('特征点检测', '初次加载图像，显示所有特征点');
        } else {
          // 用户已经点击了“炫”按钮，保持特征点隐藏状态
          Logger.i('特征点检测', '保持特征点隐藏状态，不显示特征点');
          _visibleIndexes.clear();
        }
        
        // 记录处理后的特征点信息
        Logger.i('特征点检测', '处理后特征点总数: ${_landmarks!.length}, 可见特征点数量: ${_visibleIndexes.length}');
        
        if (_visibleIndexes.isNotEmpty) {
          final List<int> processedIndices = _visibleIndexes.toList();
          processedIndices.sort();
          Logger.i('特征点检测', '可见特征点索引范围: ${processedIndices.first}-${processedIndices.last}');
        }
      });
      
      // 记录特征点统计信息
      final primaryCount = _landmarks!.where((p) => p['primary'] == true).length;
      final secondaryCount = _landmarks!.where((p) => p['secondary'] == true).length;
      final auxiliaryCount = _landmarks!.length - primaryCount - secondaryCount;
      
      Logger.i('特征点检测', 'ℹ️ [信息] 特征点分类统计: 主要$primaryCount | 次要$secondaryCount | 辅助$auxiliaryCount');
      
      // 将特征点数据和图像路径传递给TransformationService
      final transformationService = Provider.of<TransformationService>(context, listen: false);
      
      // 将 Map 转换为 FeaturePoint 对象
      final featurePoints = _landmarks!.map((map) => FeaturePoint(
        index: map['index'] as int,
        x: (map['x'] as num).toDouble(),
        y: (map['y'] as num).toDouble(),
        z: map['z'] != null ? (map['z'] as num).toDouble() : 0.0,
        visibility: map['visibility'] != null ? (map['visibility'] as num).toDouble() : 1.0,
        confidence: map['confidence'] != null ? (map['confidence'] as num).toDouble() : 1.0,
        isPrimary: map['primary'] as bool? ?? false,
      )).toList();
      
      transformationService.setFeaturePoints(featurePoints, imagePath: imagePath);
      Logger.i('特征点检测', '✅ 已将特征点数据和图像路径传递给TransformationService: $imagePath');
      
      // 直接将特征点数据保存到DeformationCacheManager中，确保首次图像导入时特征点数据被正确缓存
      DeformationCacheManager.setLatestDeformedState(null, featurePoints);
      Logger.i('特征点检测', '✅ 已将特征点数据保存到DeformationCacheManager缓存中');
      
      // 主动调用对称点对处理方法，以查看日志
      Logger.i('特征点检测', '开始获取对称点对...');
      _getSymmetryPairsForCurrentArea();
      Logger.i('特征点检测', '对称点对获取完成');
      
    } catch (e) {
      Logger.e('特征点检测', '处理失败: $e');
    }
  }

  /// 更新图片缩放和偏移以适应容器
  /// 设置要显示的特征点索引
  void showFeaturePoints(List<int> indexes) {
    setState(() {
      _visibleIndexes = indexes.toSet();
    });

  }

  /// 清除所有可见的特征点
  void hideFeaturePoints() {
    setState(() {
      _visibleIndexes.clear();
    });

  }

  void _updateScaleAndOffset() {
    if (_imageSize == null || _containerSize == null) {
      return;
    }
    
    // 计算缩放比例，保持宽高比
    final scaleX = _containerSize!.width / _imageSize!.width;
    final scaleY = _containerSize!.height / _imageSize!.height;
    _scale = scaleX < scaleY ? scaleX : scaleY;
    
    // 计算居中偏移
    final scaledWidth = _imageSize!.width * _scale;
    final scaledHeight = _imageSize!.height * _scale;
    _offset = Offset(
      (_containerSize!.width - scaledWidth) / 2,
      (_containerSize!.height - scaledHeight) / 2
    );
  }

  /// 根据索引过滤特征点
  List<Map<String, dynamic>> _filterLandmarksByIndexes(List<int> indexes) {
    if (_landmarks == null) {
      return [];
    }
    
    return _landmarks!.where((landmark) {
      final index = landmark['index'] as int;
      return indexes.contains(index);
    }).toList();
  }

  // 用于跟踪上次绘制的特征点数量，减少日志输出
  static int _lastVisibleCount = 0;
  static int _lastRequiredCount = 0;
  static int _lastLandmarksCount = 0;
  static DateTime _lastLogTime = DateTime.now();
  static DateTime _lastBuildLogTime = DateTime.now();
  
  /// 绘制特征点 - 使用单独的StatefulWidget封装特征点绘制逻辑
  Widget _buildLandmarksOverlay() {
    // 如果特征点数据为空或图像尺寸为空，返回空容器
    if (_landmarks == null || _imageSize == null) {
      return Container();
    }
    
    // 如果可见索引为空，说明特征点已被隐藏，返回空容器
    if (_visibleIndexes.isEmpty) {
      Logger.i('图像显示', '特征点已隐藏，不绘制特征点覆盖层');
      return Container();
    }
    
    // 创建一个包含所有必要特征点的集合
    Set<int> requiredIndexes = Set<int>.from(_visibleIndexes);
    
    // 确保所有对称点对中引用的点都被包含
    if (_symmetryPairs != null && _symmetryPairs!.isNotEmpty) {
      for (final pair in _symmetryPairs!) {
        requiredIndexes.add(pair['point1'] as int);
        requiredIndexes.add(pair['point2'] as int);
      }
    }
    
    // 如果没有需要显示的特征点，返回空容器
    if (requiredIndexes.isEmpty) {
      Logger.i('图像显示', '没有需要显示的特征点，不绘制特征点覆盖层');
      return Container();
    }
    
    // 根据需要的索引过滤特征点
    final requiredLandmarks = _filterLandmarksByIndexes(requiredIndexes.toList());
    
    // 如果过滤后的特征点为空，返回空容器
    if (requiredLandmarks.isEmpty) {
      Logger.i('图像显示', '过滤后的特征点为空，不绘制特征点覆盖层');
      return Container();
    }
    
    // 减少日志输出频率，只在数量变化或间隔较长时输出
    final now = DateTime.now();
    final countChanged = _lastVisibleCount != _visibleIndexes.length || 
                        _lastRequiredCount != requiredIndexes.length || 
                        _lastLandmarksCount != requiredLandmarks.length;
    final timeThresholdMet = now.difference(_lastLogTime).inSeconds >= 30; // 30秒间隔
    
    if (countChanged || timeThresholdMet) {
      Logger.d('图像显示', '绘制特征点: 可见点数量=${_visibleIndexes.length}, 对称点对引用点数量=${requiredIndexes.length}, 最终传递点数量=${requiredLandmarks.length}');
      
      // 更新计数器和时间戳
      _lastVisibleCount = _visibleIndexes.length;
      _lastRequiredCount = requiredIndexes.length;
      _lastLandmarksCount = requiredLandmarks.length;
      _lastLogTime = now;
    }
    
    // 使用单独的StatefulWidget来封装特征点绘制，避免整个UI重建
    return LandmarksOverlay(
      key: _landmarksKey,
      containerSize: _containerSize ?? Size.zero,
      landmarks: requiredLandmarks,
      scale: _scale,
      offset: _offset,
      imageSize: _imageSize!,
      highlightIndexes: _highlightIndexes,
      initialBlinkValue: _blinkValue,
      symmetricPairs: _symmetryPairs,
      showSymmetryLines: false, // 暂时屏蔽对称线显示
      showIndexes: false, // 屏蔽特征点索引值显示
      isPointAnimating: true, // 强制启用动画
      pointAnimationColor: Colors.white, // 设置动画颜色为白色
      pointAnimationOpacity: 1.0, // 设置动画不透明度为1.0
    );
  }



  /// 触发缩略图淡出动画并直接隐藏按钮
  void startThumbnailFadeAnimation() {
    Logger.i('图片显示', '触发缩略图淡出动画并直接隐藏按钮');
    
    // 触发缩略图淡出动画
    final stylePreviewState = _stylePreviewKey.currentState;
    if (stylePreviewState != null) {
      stylePreviewState.startFadeOutAnimation();
    } else {
      Logger.w('图片显示', '无法触发缩略图淡出动画：StylePreview尚未初始化');
    }
    
    // 直接修改UI状态强制隐藏按钮
    setState(() {
      _forceHideJourneyButton = true;
    });
    
    // 同时通过ImageState隐藏按钮
    Logger.i('图片显示', '直接隐藏美旅启程按钮');
    final imageState = Provider.of<ImageState>(context, listen: false);
    imageState.hideButtons();
    imageState.startBeautyJourney();
  }
  
  String? _lastProcessedPath;
  
  @override
  void didUpdateWidget(ImageDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    final imageState = Provider.of<ImageState>(context, listen: false);
    final imagePath = imageState.frontImagePath;
    
    // 只在图片路径变化或首次加载时处理
    if (imagePath != null && 
        (imagePath != _lastProcessedPath || _imageSize == null)) {
      Logger.i('图片状态', '图片路径变化: $imagePath');
      _lastProcessedPath = imagePath;
      
      _loadImageSize(imagePath);
      Future.microtask(() {
        if (mounted) {
          _processImage(imagePath);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ImageState>(
      builder: (context, imageState, child) {
        // 当图片状态重置时，重置所有动画控制器和状态
        if (imageState.frontState == ImageDisplayState.initial) {
          _imageAnimationController.reset();
          _imageSize = null;
          _landmarks = null;
          _scale = 1.0;
          _offset = Offset.zero;
        }
        
        // 当图片加载完成时开始动画
        if (imageState.hasFrontImage && !_imageAnimationController.isAnimating) {
          _imageAnimationController.forward();
        }

        return LayoutBuilder(
          builder: (context, constraints) {
            final thumbnailSizes = _calculateThumbnailSize(constraints.maxWidth);
            
            return Container(
              constraints: BoxConstraints(
                maxWidth: constraints.maxWidth,
                maxHeight: constraints.maxHeight,
              ),
              child: Stack(
                children: [
                  // 主图区域
                  Positioned.fill(
                    child: _buildImageArea(
                      context,
                      imageState,
                      imageOpacity: _imageOpacityAnimation,
                    ),
                  ),


                  // 风格预览区域
                  // 【2025-05-05】修改条件，只在没有变形图像时显示风格预览，并添加详细注释
                  // 原因：当有变形图像时，风格预览会导致底部出现重复的图像预览
                  // 影响：用户在变形过程中将不会看到风格预览选项
                  // 如需恢复原始行为：删除下面的条件中的transformedImagePath判断
                  if (imageState.journeyStarted && 
                      Provider.of<TransformationService>(context, listen: false).transformedImagePath == null)
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: StylePreview(
                        key: _stylePreviewKey,
                        imagePath: imageState.frontImagePath!,
                      ),
                    ),

                  // 操作按钮
                  if (imageState.buttonsVisible && !imageState.journeyStarted && !_forceHideJourneyButton)
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 16,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: FadeTransition(
                          opacity: _buttonOpacityAnimation,
                          child: SlideTransition(
                            position: _buttonSlideAnimation,
                            child: Container(
                              alignment: Alignment.center,
                              width: constraints.maxWidth * 0.4, // 手机屏幕宽度
                              child: AnimatedActionButton(
                                onPressed: () {
                                  imageState.startBeautyJourney();
                                  _startJourneyAnimation();
                                },
                                text: '美旅启程',
                                iconRight: Icons.auto_awesome,
                                isPrimary: true,
                                visible: true,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  // 侧脸照片覆盖层
                  if (_containerSize != null && _imageSize != null)
                    Selector<ImageState, bool>(
                      selector: (_, state) => state.showSideImage && state.hasSideImage,
                      builder: (context, showSideImage, _) {
                        if (showSideImage) {
                          // 计算图片在容器中的实际显示尺寸和位置
                          final imageWidth = _imageSize!.width * _scale;
                          final imageHeight = _imageSize!.height * _scale;
                          
                          // 计算图片在容器中的实际位置
                          final imageLeft = _offset.dx;
                          final imageTop = _offset.dy;
                          
                          // 侧脸图片尺寸为图片区域的1/5
                          final sideWidth = imageWidth / 5;
                          final sideHeight = imageHeight / 5;
                          
                          // 计算侧脸图片在图片右下角的位置
                          // 注意：这里使用图片的右下角坐标，而不是容器的右下角
                          final rightPosition = _containerSize!.width - (imageLeft + imageWidth - 16);
                          final bottomPosition = _containerSize!.height - (imageTop + imageHeight - 16);
                          
                          Logger.log('ImageDisplay', '_buildImageArea', 
                            '图片位置: 左${imageLeft}px, 上${imageTop}px, 尺寸: ${imageWidth}x${imageHeight}');
                          Logger.log('ImageDisplay', '_buildImageArea', 
                            '侧脸图片位置: 右${rightPosition}px, 下${bottomPosition}px, 尺寸: ${sideWidth}x${sideHeight}');
                          
                          // 侧脸图片显示逻辑 - 直接定位在图片的右下角
                          return Positioned(
                            left: imageLeft + imageWidth - sideWidth - 16,
                            top: imageTop + imageHeight - sideHeight - 16,
                            width: sideWidth,
                            height: sideHeight,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.7),
                                  width: 1.0,
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(7),
                                child: Stack(
                                  children: [
                                    // 侧脸图片
                                    Positioned.fill(
                                      child: Image.file(
                                        File(Provider.of<ImageState>(context).sideImagePath!),
                                        fit: BoxFit.cover,
                                        opacity: const AlwaysStoppedAnimation<double>(0.7),
                                      ),
                                    ),
                                    // 半透明遮罩
                                    Positioned.fill(
                                      child: Container(
                                        color: Colors.black.withOpacity(0.2),
                                      ),
                                    ),
                                    // 特征点覆盖层
                                    Builder(
                                      builder: (context) {
                                        // 获取变形服务
                                        final transformationService = TransformationService();
                                        
                                        // 获取鼻梁高度和鼻尖调整参数
                                        final bridgeHeightValue = transformationService.getParameterValue('nose', 'bridge_height') ?? 0.0;
                                        final tipAdjustValue = transformationService.getParameterValue('nose', 'tip_adjust') ?? 0.0;
                                        
                                        // 获取侧面特征点
                                        final imageState = Provider.of<ImageState>(context, listen: false);
                                        final sideFeatures = imageState.sideFeatures;
                                        
                                        // 如果没有特征点数据，返回空容器
                                        if (sideFeatures == null || sideFeatures['landmarks'] == null) {
                                          return const SizedBox.shrink();
                                        }
                                        
                                        // 将Map转换为FeaturePoint列表
                                        final landmarks = sideFeatures['landmarks'] as List<dynamic>;
                                        final featurePoints = landmarks.map((map) => FeaturePoint(
                                          index: map['index'] as int,
                                          x: (map['x'] as num).toDouble(),
                                          y: (map['y'] as num).toDouble(),
                                          z: map['z'] != null ? (map['z'] as num).toDouble() : 0.0,
                                          visibility: map['visibility'] != null ? (map['visibility'] as num).toDouble() : 1.0,
                                          confidence: map['confidence'] != null ? (map['confidence'] as num).toDouble() : 1.0,
                                          isPrimary: map['primary'] as bool? ?? false,
                                          isSecondary: map['secondary'] as bool? ?? false,
                                        )).toList();
                                        
                                        // 创建变形后的特征点列表
                                        List<FeaturePoint> transformedPoints = List.from(featurePoints);
                                        
                                        // 应用鼻梁高度变形
                                        if (bridgeHeightValue != 0.0) {
                                          // 定义鼻部特征点索引
                                          final nosePoints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
                                          
                                          // 应用变形
                                          for (int i = 0; i < transformedPoints.length; i++) {
                                            final point = transformedPoints[i];
                                            
                                            // 只变形鼻部特征点
                                            if (nosePoints.contains(point.index)) {
                                              // 计算变形后的x坐标（鼻梁高度主要影响x方向）
                                              // 正值：鼻梁变高（向右移动），负值：鼻梁变低（向左移动）
                                              final newX = point.x + bridgeHeightValue * 0.05 * sin((point.y - 0.2) * pi / 0.3);
                                              transformedPoints[i] = point.copyWith(x: newX);
                                            }
                                          }
                                        }
                                        
                                        // 应用鼻尖调整变形
                                        if (tipAdjustValue != 0.0) {
                                          // 定义鼻尖特征点索引（鼻部特征点的后半部分）
                                          final tipPoints = [5, 6, 7, 8, 9, 10];
                                          
                                          // 应用变形
                                          for (int i = 0; i < transformedPoints.length; i++) {
                                            final point = transformedPoints[i];
                                            
                                            // 只变形鼻尖特征点
                                            if (tipPoints.contains(point.index)) {
                                              // 计算变形后的坐标
                                              // 正值：鼻尖上翘，负值：鼻尖下垂
                                              final factor = (point.index - 5) / 5.0; // 0到1的因子，越靠近鼻尖越大
                                              final newY = point.y - tipAdjustValue * 0.02 * factor;
                                              final newX = point.x + tipAdjustValue * 0.01 * factor;
                                              
                                              transformedPoints[i] = point.copyWith(x: newX, y: newY);
                                            }
                                          }
                                        }
                                        
                                        // 记录日志（每5秒记录一次，避免日志过多）
                                        final now = DateTime.now().millisecondsSinceEpoch;
                                        if (now % 5000 < 100) {  // 每5秒记录一次
                                          Logger.d('侧面特征点显示', '应用变形: 鼻梁高度=$bridgeHeightValue, 鼻尖调整=$tipAdjustValue');
                                        }
                                        
                                        // 返回特征点覆盖层
                                        return Positioned.fill(
                                          child: SideProfileLandmarksOverlay(
                                            featurePoints: transformedPoints,
                                            width: sideWidth,
                                            height: sideHeight,
                                          ),
                                        );
                                      },
                                    ),
                                    // 加载指示器
                                    if (Provider.of<ImageState>(context).sideImageProcessing)
                                      Positioned.fill(
                                        child: Container(
                                          color: Colors.black.withOpacity(0.5),
                                          child: const Center(
                                            child: CircularProgressIndicator(
                                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                              strokeWidth: 2.0,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        } else {
                          return ProfilePhotoOverlay(
                            containerWidth: _containerSize!.width,
                            containerHeight: _containerSize!.height,
                          );
                        }
                      },
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildImageArea(
    BuildContext context,
    ImageState imageState, {
    required Animation<double> imageOpacity,
  }) {
    // 减少日志输出频率
    final now = DateTime.now();
    final imageLogThresholdMet = now.difference(_lastBuildLogTime).inSeconds >= 5; // 5秒间隔
    
    if (imageLogThresholdMet) {
      _lastBuildLogTime = now;
      Logger.d('图片显示', '开始构建图片区域');
      Logger.d('图片显示', '  • 图片状态: ${imageState.hasFrontImage ? '有图片' : '无图片'}');
      if (imageState.frontImagePath != null) {
        Logger.d('图片显示', '  • 图片路径: ${imageState.frontImagePath}');
      }
    }
    
    return LayoutBuilder(
      builder: (context, constraints) {
        _containerSize = Size(constraints.maxWidth, constraints.maxHeight);
        // 减少日志输出频率
        if (imageLogThresholdMet) {
          Logger.d('图片显示', '  • 容器尺寸: ${_containerSize!.width}x${_containerSize!.height}');
        }
        
        if (!imageState.hasFrontImage) {
          if (imageLogThresholdMet) {
            Logger.d('图片显示', '  • 无图片，显示动画背景图');
          }
          return const AnimatedDoorBackground();
        }
        
        // 当图片路径改变时，加载图片尺寸
        if (imageState.frontImagePath != null && _imageSize == null) {
          Logger.i('图片显示', '开始加载新图片');
          _loadImageSize(imageState.frontImagePath!);
          // 使用Future.microtask处理特征点
          Future.microtask(() {
            if (mounted) {
              _processImage(imageState.frontImagePath!);
            }
          });
        }
        
        // 如果图片尺寸还未加载完成，显示加载指示器
        if (_imageSize == null) {
          Logger.d('图片显示', '  • 图片尺寸未加载完成，显示加载指示器');
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        // 更新缩放和偏移
        _updateScaleAndOffset();
        
        // 减少日志输出频率
        if (imageLogThresholdMet) {
          Logger.d('图片显示', '  • 图片尺寸: ${_imageSize!.width}x${_imageSize!.height}');
          Logger.d('图片显示', '  • 缩放比例: $_scale');
          Logger.d('图片显示', '  • 偏移量: $_offset');
          Logger.d('图片显示', '  • 特征点数量: ${_landmarks?.length ?? 0}');
        }
        
        return FadeTransition(
          opacity: imageOpacity,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Container(
              width: _containerSize!.width,
              height: _containerSize!.height,
              color: Colors.black.withOpacity(0.05),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // 图片层
                  Positioned(
                    left: _offset.dx,
                    top: _offset.dy,
                    width: _imageSize!.width * _scale,
                    height: _imageSize!.height * _scale,
                    child: Consumer<TransformationService>(
                      builder: (context, transformationService, child) {
                        // 检查是否有变形后的图像路径
                        final transformedPath = transformationService.transformedImagePath;
                        final isDeforming = transformationService.isDeforming;
                        final renderer = transformationService.getSimpleDeformationRenderer();
                        
                        // 【修复图像重叠问题】如果SimpleDeformationRenderer正在渲染，隐藏Image层
                        final isCustomPaintActive = renderer != null && renderer.isVisible;
                        
                        // 记录图像路径信息 - 使用时间阈值控制日志频率
                        final now = DateTime.now();
                        final imageLogThresholdMet = now.difference(_lastBuildLogTime).inSeconds >= 5; // 5秒间隔
                        
                        if (imageLogThresholdMet) {
                          _lastBuildLogTime = now;
                          Logger.d('图片显示', '当前图像路径: ${imageState.frontImagePath}');
                          Logger.d('图片显示', '变形图像路径: $transformedPath');
                          Logger.d('图片显示', '变形状态: ${isDeforming ? '进行中' : '完成'}');
                          Logger.d('图片显示', 'CustomPaint活跃: $isCustomPaintActive');
                        }
                        
                        // 【修复图像重叠问题】如果CustomPaint正在渲染，返回透明容器让CustomPaint显示
                        if (isCustomPaintActive) {
                          if (imageLogThresholdMet) {
                            Logger.i('图片显示', '🎨 CustomPaint正在渲染，隐藏Image层防止重叠');
                          }
                          return const SizedBox.expand(); // 透明占位，让CustomPaint层显示
                        }
                        
                        // 如果有变形后的图像路径并且不在变形过程中，显示变形后的图像
                        if (transformedPath != null && !isDeforming) {
                          final transformedFile = File(transformedPath);
                          if (transformedFile.existsSync()) {
                            if (imageLogThresholdMet) {
                              Logger.i('图片显示', '显示变形后的图像: $transformedPath');
                            }
                            return FadeInImage(
                              placeholder: FileImage(File(imageState.frontImagePath!)),
                              image: FileImage(transformedFile),
                              fit: BoxFit.contain,  // 【修复缩放问题】使用contain保持长宽比
                              fadeInDuration: const Duration(milliseconds: 300),
                              fadeInCurve: Curves.easeInOut,
                            );
                          } else {
                            if (imageLogThresholdMet) {
                              Logger.w('图片显示', '变形图像文件不存在: $transformedPath');
                            }
                          }
                        }
                        
                        // 否则显示原始图像
                        return Image.file(
                          File(imageState.frontImagePath!),
                          fit: BoxFit.contain,  // 【修复缩放问题】使用contain保持长宽比
                        );
                      },
                    ),
                  ),
                  // 简化版变形区域层 - 使用 Selector 替代 Consumer 以减少不必要的重建
                  Selector<TransformationService, SimpleDeformationRenderer?>(
                    // 只选择渲染器，而不是整个服务
                    selector: (_, service) => service.getSimpleDeformationRenderer(),
                    // 只有当渲染器实例变化时才重建
                    shouldRebuild: (previous, next) => previous != next,
                    builder: (context, renderer, child) {
                      if (renderer != null) {
                        return Positioned(
                          left: _offset.dx,
                          top: _offset.dy,
                          width: _imageSize!.width * _scale,
                          height: _imageSize!.height * _scale,
                          child: ClipRect(  // 添加ClipRect确保内容不会溢出
                            child: SimpleDeformationAreaWidget(
                              renderer: renderer,
                            ),
                          ),
                        );
                      } else {
                        return const SizedBox.shrink();
                      }
                    },
                  ),
                  // 特征点层 - 确保在变形渲染器层之上
                  if (_landmarks != null)
                    Positioned.fill(
                      child: _buildLandmarksOverlay(),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
