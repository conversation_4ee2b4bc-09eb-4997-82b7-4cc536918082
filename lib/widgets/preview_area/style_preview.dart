import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/image_state.dart';
import '../../utils/logger.dart';
import 'style_thumbnail.dart';
import 'thumbnail_animation_controller.dart';
import '../../beautify_feature/animations/thumbnail_fade_controller.dart';


/// 风格预览组件
class StylePreview extends StatefulWidget {
  final String imagePath;
  const StylePreview({
    Key? key,
    required this.imagePath,
  }) : super(key: key);

  @override
  StylePreviewState createState() => StylePreviewState();
}

class StylePreviewState extends State<StylePreview> with TickerProviderStateMixin {
  late ThumbnailAnimationController _animationController;
  late AnimationController _thumbnailsController;
  late List<Animation<double>> _thumbnailAnimations;
  late ThumbnailFadeController _thumbnailFadeController;

  // 预设风格列表
  final List<Map<String, dynamic>> _styles = [
    {'title': '国人\n审美', 'color': const Color(0xFFE57373)},
    {'title': '亚洲\n流行', 'color': const Color(0xFF64B5F6)},
    {'title': '西方\n风格', 'color': const Color(0xFF81C784)},
    {'title': '自然\n微调', 'color': const Color(0xFFFFB74D)},
  ];

  // 获取缩略图淡出控制器的公共方法
  ThumbnailFadeController getThumbnailFadeController() {
    return _thumbnailFadeController;
  }

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _animationController = ThumbnailAnimationController()..initialize(this);
    
    // 初始化缩略图动画控制器
    _thumbnailsController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    
    // 初始化缩略图淡出控制器
    _thumbnailFadeController = ThumbnailFadeController();
    _thumbnailFadeController.initialize(this, onCompleted: () {
      Logger.i('风格预览', '缩略图淡出动画完成');
    });

    // 初始化缩略图动画
    _thumbnailAnimations = List.generate(
      4,
      (index) => CurvedAnimation(
        parent: _thumbnailsController,
        curve: Interval(
          0.2 + (index * 0.15),
          0.2 + (index * 0.15) + 0.3,
          curve: Curves.easeOutBack,
        ),
      ),
    );
    
    // 添加动画监听器以触发重建
    _animationController.addListener(_onAnimationChanged);

    // 开始缩略图动画
    _thumbnailsController.forward();
  }

  void _onAnimationChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _thumbnailsController.dispose();
    _animationController.dispose();
    _thumbnailFadeController.dispose();
    super.dispose();
  }

  void startAnimation() {
    _thumbnailsController.forward();
  }

  // 开始缩略图淡出动画
  void startFadeOutAnimation() {
    Logger.i('风格预览', '开始缩略图淡出动画');
    _thumbnailFadeController.startAnimation();
  }

  void _onStyleSelected(int index, ImageState imageState) {
    Logger.i('风格预览', '==================== 开始风格选择 ====================');
    Logger.i('风格预览', '选择风格详情:');
    Logger.i('风格预览', '  - 标题: ${_styles[index]['title']}');
    Logger.i('风格预览', '  - 索引: $index');
    Logger.i('风格预览', '  - 当前时间: ${DateTime.now().toString()}');

    // 1. 更新UI状态
    Logger.i('风格预览', '\n>> 第1步: 更新UI状态');
    Logger.i('风格预览', '  - 更新缩略图选中状态');
    _animationController.selectThumbnail(index);
    Logger.i('风格预览', '  - 更新风格状态');
    imageState.selectStyle(index);

    // 2. 设置扫描状态
    Logger.i('风格预览', '\n>> 第2步: 设置扫描状态');
    imageState.setFrontState(ImageDisplayState.scanning);

  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final scaledWidth = constraints.maxWidth;
        final spacing = 8.0;
        final thumbnailWidth = (scaledWidth - (spacing * 3)) / 4;
        final thumbnailHeight = thumbnailWidth * 1.2;

        return Consumer<ImageState>(
          builder: (context, imageState, child) {
        return Container(
          width: scaledWidth,
          child: Stack(
            children: [
              // 原有的缩略图列表
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: List.generate(
                  4,
                  (index) {
                    return Container(
                      width: thumbnailWidth,
                      child: SlideTransition(
                        position: _thumbnailFadeController.getSlideAnimation(index),
                        child: FadeTransition(
                          opacity: _thumbnailFadeController.getFadeAnimation(index),
                          child: StyleThumbnail(
                            imagePath: widget.imagePath,
                            title: _styles[index]['title'],
                            filterColor: _styles[index]['color'],
                            width: thumbnailWidth,
                            height: thumbnailHeight,
                            scale: _animationController.getScale(index),
                            animation: _thumbnailAnimations[index],
                            onTap: () => _onStyleSelected(index, imageState),
                            isSelected: _animationController.selectedIndex == index,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
          },
        );
      },
    );
  }
}
