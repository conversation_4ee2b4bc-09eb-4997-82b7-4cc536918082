import 'dart:io';
import 'package:flutter/material.dart';
import '../../beautify_feature/services/transformation_service.dart';
import '../../utils/logger.dart';

class BottomToolbar extends StatelessWidget {
  /// 是否显示"炫"按钮
  final bool showBeautifyButton;
  
  /// "炫"按钮点击回调
  final VoidCallback? onBeautifyPressed;

  const BottomToolbar({
    Key? key,
    this.showBeautifyButton = true,
    this.onBeautifyPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFE8DBFF),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ToolbarButton(
            icon: Icons.bug_report,
            label: '导入图片',
            onPressed: () {
              // 导航到图片导入页面
              Navigator.of(context).pushNamed('/image_import');
            },
          ),
          const SizedBox(width: 40),
          if (showBeautifyButton) ToolbarButton(
            icon: Icons.auto_awesome,  
            label: '炫',
            onPressed: onBeautifyPressed ?? () {
              // 如果没有提供回调，则初始化变形服务
              final transformationService = TransformationService.instance;
              transformationService.initialize().then((_) {
                Logger.i('BottomToolbar', '✅ 变形服务初始化完成');
                
                // 确保变形渲染器被初始化
                transformationService.ensureDeformationRendererInitialized().then((_) {
                  Logger.i('BottomToolbar', '✅ 变形渲染器初始化完成');
                  
                  // 设置默认区域类型和参数
                  final renderer = transformationService.getSimpleDeformationRenderer();
                  if (renderer != null) {
                    // 设置默认区域类型为鼻子
                    renderer.setArea('nose');
                    Logger.i('BottomToolbar', '✅ 设置默认区域类型: nose');
                    
                    // 设置默认参数
                    renderer.setParameter('nose_height');
                    Logger.i('BottomToolbar', '✅ 设置默认参数: nose_height');
                    
                    // 设置默认参数值
                    renderer.setParameterValue(0.0);
                    Logger.i('BottomToolbar', '✅ 设置默认参数值: 0.0');
                    
                    // 设置可见性
                    renderer.setVisible(true);
                    Logger.i('BottomToolbar', '✅ 设置可见性: true');
                    
                    // 强制重绘
                    renderer.forceRepaint();
                    Logger.i('BottomToolbar', '✅ 强制重绘完成');
                  } else {
                    Logger.e('BottomToolbar', '❌ 获取变形渲染器失败');
                  }
                });
              });
            },
            primary: true,  
            size: 32,     
          ),
          const SizedBox(width: 40),
          ToolbarButton(
            icon: Icons.share_outlined,
            label: '分享',
            onPressed: () {},
          ),
        ],
      ),
    );
  }
}

class ToolbarButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final bool primary;
  final double size;

  const ToolbarButton({
    Key? key,
    required this.icon,
    required this.label,
    required this.onPressed,
    this.primary = false,
    this.size = 24,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        constraints: const BoxConstraints(maxHeight: 70),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: size,
              color: primary ? const Color(0xFF7F4CFF) : const Color(0xFF666666),
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: primary ? const Color(0xFF7F4CFF) : const Color(0xFF666666),
                fontWeight: primary ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
