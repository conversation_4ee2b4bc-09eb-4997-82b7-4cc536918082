import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../utils/logger.dart';

/// 动画背景组件
/// 实现图片顺时针缓慢旋转的效果
class AnimatedDoorBackground extends StatefulWidget {
  const AnimatedDoorBackground({Key? key}) : super(key: key);

  @override
  State<AnimatedDoorBackground> createState() => _AnimatedDoorBackgroundState();
}

class _AnimatedDoorBackgroundState extends State<AnimatedDoorBackground> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _imageLoaded = false;
  
  // 背景图片中圆圈的相对位置（相对于整个图像的高度）
  final double _circleRelativeY = 0.45; // 圆心垂直位置（相对于图像高度的比例）

  @override
  void initState() {
    super.initState();
    
    // 创建一个持续时间为30秒的动画控制器，速度提升一倍
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 30),
    )..repeat(); // 设置为重复播放
    
    Logger.i('动画背景', '初始化旋转动画控制器');
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        image: const DecorationImage(
          image: AssetImage('assets/door.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 计算圆心位置
          final centerY = constraints.maxHeight * _circleRelativeY;
          
          return Stack(
            children: [
              // 将logo放置在屏幕下1/5的中心位置
              Positioned(
                left: 0,
                right: 0,
                bottom: constraints.maxHeight * 0.1, // 从底部向上1/10的位置（屏幕下1/5的中心）
                child: Center(
                  child: AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _controller.value * 2 * math.pi, // 完整的一圈旋转
                        alignment: Alignment.center,
                        child: child,
                      );
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width / 24, // 光晕容器大小
                      height: MediaQuery.of(context).size.width / 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.amber.withOpacity(0.3),
                            blurRadius: 15,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Opacity(
                        opacity: 0.0875, // 透明度再降低50%（从0.175降低到0.0875）
                        child: Image.asset(
                          'assets/hesi.png',
                          width: MediaQuery.of(context).size.width / 48, // 图片宽度为屏幕宽度的1/48
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        }
      ),
    );
  }
}
