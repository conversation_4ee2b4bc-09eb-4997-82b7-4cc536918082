import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class AnimatedActionButton extends StatelessWidget {
  final String text;
  final IconData? iconLeft;
  final IconData? iconRight;
  final VoidCallback onPressed;
  final bool isPrimary;
  final bool visible;
  final bool slideFromLeft;
  
  const AnimatedActionButton({
    Key? key,
    required this.text,
    this.iconLeft,
    this.iconRight,
    required this.onPressed,
    this.isPrimary = false,
    this.visible = true,
    this.slideFromLeft = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: visible ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: Container(
        height: 40, // 减小高度
        constraints: BoxConstraints(
          maxWidth: 120, // 限制最大宽度
        ),
        decoration: BoxDecoration(
          gradient: isPrimary ? const LinearGradient(
            colors: [Color(0xFF2196F3), Color(0xFF00BCD4)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ) : null,
          color: isPrimary ? null : Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(20),
          boxShadow: isPrimary ? [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(20),
            onTap: onPressed,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min, // 让按钮宽度适应内容
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (iconLeft != null) ...[
                    Icon(
                      iconLeft,
                      color: Colors.white,
                      size: 14, // 减小图标大小
                    ),
                    const SizedBox(width: 4), // 减小图标间距
                  ],
                  Text(
                    text,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14, // 减小字体大小
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (iconRight != null) ...[
                    const SizedBox(width: 4), // 减小图标间距
                    Icon(
                      iconRight,
                      color: Colors.white,
                      size: 14, // 减小图标大小
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ).animate(
        target: visible ? 1 : 0,
      ).moveX(
        begin: slideFromLeft ? -50 : 50,
        end: 0,
        duration: 500.ms,
        curve: Curves.easeOutQuad,
      ),
    );
  }
}
