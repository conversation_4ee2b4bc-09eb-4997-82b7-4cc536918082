import 'package:flutter/material.dart';
import '../utils/logger.dart';
import '../core/deformation_processing/target_point_painter.dart';
import '../core/simple_deformation_renderer.dart';

/// 目标点覆盖层，用于在变形区域上显示目标点
class TargetPointOverlay extends StatelessWidget {
  /// 日志标签
  static const _logTag = 'TargetPointOverlay';
  
  /// 图片尺寸
  final Size imageSize;
  
  /// 构造函数
  const TargetPointOverlay({
    Key? key,
    required this.imageSize,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    Logger.flowStart(_logTag, 'build');
    
    // 使用SimpleDeformationRenderer替代DeformationProcessor
    // 注意：由于移除了DeformationProcessor，此处不再显示目标点
    // 如果需要目标点功能，请在SimpleDeformationRenderer中实现
    Logger.flow(_logTag, 'build', '已移除DeformationProcessor依赖，不再显示目标点');
    
    // 返回一个透明容器，不显示任何内容
    return Container();
  }
}
