import 'package:flutter/material.dart';

class AIExpertArea extends StatelessWidget {
  const AIExpertArea({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // AI专家头像
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              border: Border.all(
                color: const Color(0xFFE8DBFF),
                width: 4,
              ),
            ),
            child: const Icon(
              Icons.smart_toy_outlined,
              size: 60,
              color: Color(0xFF7B61FF),
            ),
          ),
          const SizedBox(height: 16),
          // AI专家名称
          const Text(
            'AI美容专家',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF7B61FF),
            ),
          ),
          const SizedBox(height: 8),
          // AI专家描述
          const Text(
            '您的个人美容顾问',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
        ],
      ),
    );
  }
}
