import 'package:flutter/material.dart';

class OperationHints extends StatelessWidget {
  const OperationHints({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFE8DBFF),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '操作提示',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView(
              children: const [
                HintMessage(
                  message: '请选择一个风格预设开始美化',
                  type: HintType.info,
                ),
                SizedBox(height: 12),
                HintMessage(
                  message: '可以随时调整参数来微调效果',
                  type: HintType.tip,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

enum HintType { info, tip, warning }

class HintMessage extends StatelessWidget {
  final String message;
  final HintType type;

  const HintMessage({
    Key? key,
    required this.message,
    required this.type,
  }) : super(key: key);

  Color get _backgroundColor {
    switch (type) {
      case HintType.info:
        return const Color(0xFFF5F0FF);
      case HintType.tip:
        return const Color(0xFFF0F8FF);
      case HintType.warning:
        return const Color(0xFFFFF0F0);
    }
  }

  Color get _borderColor {
    switch (type) {
      case HintType.info:
        return const Color(0xFF7B61FF);
      case HintType.tip:
        return const Color(0xFF61A5FF);
      case HintType.warning:
        return const Color(0xFFFF6161);
    }
  }

  IconData get _icon {
    switch (type) {
      case HintType.info:
        return Icons.info_outline;
      case HintType.tip:
        return Icons.lightbulb_outline;
      case HintType.warning:
        return Icons.warning_amber_outlined;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _borderColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _icon,
            color: _borderColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: 14,
                color: _borderColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
