import 'package:flutter/material.dart';

class AnalysisArea extends StatelessWidget {
  const AnalysisArea({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '方案解析',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFFE8DBFF),
                  width: 1,
                ),
              ),
              child: ListView(
                children: const [
                  AnalysisItem(
                    title: '面部轮廓',
                    description: '根据黄金比例优化脸型轮廓，保持自然美感',
                    progress: 0.3,
                  ),
                  SizedBox(height: 16),
                  AnalysisItem(
                    title: '五官调整',
                    description: '微调五官位置和大小，突出个人特色',
                    progress: 0.6,
                  ),
                  SizedBox(height: 16),
                  AnalysisItem(
                    title: '肤质美化',
                    description: '改善肤质，提亮肤色，保持细腻质感',
                    progress: 0.8,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class AnalysisItem extends StatelessWidget {
  final String title;
  final String description;
  final double progress;

  const AnalysisItem({
    Key? key,
    required this.title,
    required this.description,
    required this.progress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          description,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 12),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: const Color(0xFFE8DBFF),
          valueColor: const AlwaysStoppedAnimation<Color>(
            Color(0xFF7B61FF),
          ),
        ),
      ],
    );
  }
}
