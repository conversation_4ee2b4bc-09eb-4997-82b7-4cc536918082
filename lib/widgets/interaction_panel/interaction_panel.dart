import 'package:flutter/material.dart';
import 'ai_expert_area.dart';
import 'analysis_area.dart';
import 'operation_hints.dart';

class InteractionPanel extends StatelessWidget {
  const InteractionPanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // AI专家区域 (30%)
          Container(
            height: MediaQuery.of(context).size.height * 0.3,
            padding: const EdgeInsets.all(16),
            child: const AIExpertArea(),
          ),
          const Divider(height: 1, color: Color(0xFFE8DBFF)),
          // 方案解析区 (50%)
          Container(
            height: MediaQuery.of(context).size.height * 0.5,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: const AnalysisArea(),
          ),
          const Divider(height: 1, color: Color(0xFFE8DBFF)),
          // 操作提示区 (20%)
          Container(
            height: MediaQuery.of(context).size.height * 0.2,
            padding: const EdgeInsets.all(16),
            child: const OperationHints(),
          ),
        ],
      ),
    );
  }
}
