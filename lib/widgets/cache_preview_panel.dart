import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'dart:ui' as ui;
import '../core/deformation_cache_manager.dart';
import '../utils/logger.dart';

/// 缓存预览面板，用于显示变形缓存的图片
class CachePreviewPanel extends StatefulWidget {
  const CachePreviewPanel({Key? key}) : super(key: key);

  @override
  State<CachePreviewPanel> createState() => _CachePreviewPanelState();
}

class _CachePreviewPanelState extends State<CachePreviewPanel> {
  final String _logTag = 'CachePreviewPanel';
  final List<CacheImageItem> _cacheImages = [];
  
  @override
  void initState() {
    super.initState();
    Logger.flowStart(_logTag, 'initState');
    
    // 注册监听器，当缓存更新时刷新UI
    DeformationCacheManager.addCacheUpdateListener(_onCacheUpdated);
    
    Logger.flowEnd(_logTag, 'initState');
  }
  
  @override
  void dispose() {
    Logger.flowStart(_logTag, 'dispose');
    
    // 移除监听器
    DeformationCacheManager.removeCacheUpdateListener(_onCacheUpdated);
    
    Logger.flowEnd(_logTag, 'dispose');
    super.dispose();
  }
  
  /// 当缓存更新时的回调函数
  void _onCacheUpdated(ui.Image image, String cacheKey, Map<String, double> parameterValues) {
    Logger.flowStart(_logTag, '_onCacheUpdated');
    
    // 记录开始时间，用于计算耗时
    final startTime = DateTime.now();
    
    print('======== 缓存预览面板收到缓存更新 ========');
    print('📷 缓存预览面板收到缓存更新:');
    print('  - 缓存键: $cacheKey');
    print('  - 图像哈希码: ${image.hashCode}');
    print('  - 图像尺寸: ${image.width}x${image.height}');
    print('  - 图像类型: ${image.runtimeType}');
    print('  - 内存地址: ${image.toString()}');
    print('  - 估算内存占用: ${(image.width * image.height * 4 / 1024).toStringAsFixed(2)} KB');
    print('  - 当前缓存图像数量: ${_cacheImages.length}');
    // 检查是否所有参数值都为0
    bool allZero = parameterValues.entries
      .where((e) => !e.key.startsWith('_'))
      .every((e) => e.value == 0.0);
    
    if (allZero) {
      print('  - 参数值: zero_state');
    } else {
      print('  - 参数值: ${parameterValues.entries.where((e) => e.value != 0.0).map((e) => '${e.key}=${e.value}').join(', ')}');
    }
    print('============================');
    
    // 检查是否已存在相同缓存键的图片
    final existingIndex = _cacheImages.indexWhere((item) => item.cacheKey == cacheKey);
    
    // 创建要更新的缓存项
    final newItem = CacheImageItem(
      image: image,
      cacheKey: cacheKey,
      parameterValues: Map<String, double>.from(parameterValues),
      timestamp: DateTime.now(),
    );
    
    // 仅记录收到缓存更新的日志，不进行任何UI更新
    Logger.flow(_logTag, '_onCacheUpdated', '收到缓存更新，将在帧渲染完成后处理: $cacheKey');
    
    // 使用SchedulerBinding.instance.addPostFrameCallback确保UI更新在当前帧渲染完成后进行
    // 这样可以避免"Build scheduled during frame"异常
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // 在帧渲染完成后安全地更新UI
      if (existingIndex != -1) {
        // 如果已存在，则更新
        setState(() {
          _cacheImages[existingIndex] = newItem;
        });
        Logger.flow(_logTag, '_onCacheUpdated', '在帧渲染完成后更新现有缓存图片: $cacheKey');
      } else {
        // 如果不存在，则添加
        setState(() {
          _cacheImages.add(newItem);
        });
        Logger.flow(_logTag, '_onCacheUpdated', '在帧渲染完成后添加新缓存图片: $cacheKey');
      }
      
      // 记录更新后的缓存图片数量和时间
      Logger.flow(_logTag, '_onCacheUpdated', '更新后的缓存图片数量: ${_cacheImages.length}');
      
      // 记录缓存更新耗时
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;
      Logger.flow(_logTag, '_onCacheUpdated', '通知缓存更新总耗时: ${duration}ms');
    });
    
    Logger.flowEnd(_logTag, '_onCacheUpdated');
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(bottom: 8.0),
            child: Text(
              '缓存图片预览',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
                color: Colors.white,
              ),
            ),
          ),
          if (_cacheImages.isEmpty)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  '暂无缓存图片',
                  style: TextStyle(color: Colors.white70),
                ),
              ),
            )
          else
            Expanded(
              child: ListView.builder(
                itemCount: _cacheImages.length,
                itemBuilder: (context, index) {
                  final item = _cacheImages[_cacheImages.length - 1 - index]; // 倒序显示，最新的在最上面
                  return CacheImageCard(item: item);
                },
              ),
            ),
        ],
      ),
    );
  }
}

/// 缓存图片卡片，用于显示单个缓存图片及其参数信息
class CacheImageCard extends StatelessWidget {
  final CacheImageItem item;
  
  const CacheImageCard({Key? key, required this.item}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图片预览
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4.0),
              ),
              child: AspectRatio(
                aspectRatio: item.image.width / item.image.height,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4.0),
                  child: RawImage(
                    image: item.image,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8.0),
            // 图像信息
            Text(
              '图像信息:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12.0,
              ),
            ),
            Text(
              '尺寸: ${item.image.width}x${item.image.height}',
              style: TextStyle(fontSize: 10.0),
            ),
            Text(
              '哈希码: ${item.image.hashCode}',
              style: TextStyle(fontSize: 10.0),
            ),
            const SizedBox(height: 4.0),
            // 参数信息
            Text(
              '参数配置:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12.0,
              ),
            ),
            ...item.parameterValues.entries
                .where((entry) => entry.value != 0.0) // 只显示非零参数
                .map((entry) => Text(
                      '${entry.key}: ${entry.value.toStringAsFixed(1)}',
                      style: TextStyle(fontSize: 10.0),
                    )),
            const SizedBox(height: 4.0),
            Text(
              '时间: ${_formatTime(item.timestamp)}',
              style: TextStyle(
                fontSize: 10.0,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}:${time.second.toString().padLeft(2, '0')}';
  }
}

/// 缓存图片项，包含图片和相关信息
class CacheImageItem {
  final ui.Image image;
  final String cacheKey;
  final Map<String, double> parameterValues;
  final DateTime timestamp;
  
  CacheImageItem({
    required this.image,
    required this.cacheKey,
    required this.parameterValues,
    required this.timestamp,
  });
}
