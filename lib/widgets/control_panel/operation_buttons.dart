import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../beautify_feature/services/transformation_service.dart';
import '../../beautify_feature/ui/center_panel/comparison_controller.dart';
import '../../utils/logger.dart';

class OperationButtons extends StatelessWidget {
  const OperationButtons({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final transformationService = Provider.of<TransformationService>(context);
    final comparisonController = Provider.of<ComparisonController>(context);
    
    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFE8DBFF),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: OperationButton(
              label: '重置',
              onPressed: () {
                transformationService.resetAllTransformations();
                Logger.log('OperationButtons', 'reset', '重置所有变形参数');
                
                // 调试输出
                debugPrint('===== 重置操作 =====');
                debugPrint('已重置所有变形参数');
                debugPrint('所有特征点已恢复原始状态');
                debugPrint('=====================');
              },
              type: OperationButtonType.secondary,
            ),
          ),
          Expanded(
            child: OperationButton(
              label: '对比',
              onPressed: () {
                comparisonController.toggleComparison();
                Logger.log('OperationButtons', 'compare', '切换对比状态: ${comparisonController.isComparing}');
                
                // 调试输出
                debugPrint('===== 对比操作 =====');
                debugPrint('对比状态: ${comparisonController.isComparing ? "开启" : "关闭"}');
                debugPrint('显示: ${comparisonController.isComparing ? "原始图像" : "变形后图像"}');
                debugPrint('=====================');
              },
              type: OperationButtonType.secondary,
              isActive: comparisonController.isComparing,
            ),
          ),
          Expanded(
            child: OperationButton(
              label: '应用',
              onPressed: () {
                // 这里应该将变形效果应用到图像，并保存结果
                Logger.log('OperationButtons', 'apply', '应用变形效果');
                
                // 调试输出
                debugPrint('===== 应用变形 =====');
                debugPrint('变形效果已永久应用到图像');
                debugPrint('变形参数:');
                
                // 获取所有变形参数
                final areas = ['面部', '鼻部', '眼部', '唇部', '抗衰'];
                for (final area in areas) {
                  final params = transformationService.getAreaParams(area);
                  if (params.isNotEmpty) {
                    debugPrint('  $area:');
                    for (final paramName in params) {
                      debugPrint('    $paramName');
                    }
                  }
                }
                
                debugPrint('变形结果: 成功');
                debugPrint('=====================');
                
                // 显示应用成功提示
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('变形效果已应用'),
                    backgroundColor: Color(0xFF7B61FF),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              type: OperationButtonType.primary,
            ),
          ),
        ],
      ),
    );
  }
}

enum OperationButtonType { primary, secondary }

class OperationButton extends StatelessWidget {
  final String label;
  final VoidCallback onPressed;
  final OperationButtonType type;
  final bool isActive;

  const OperationButton({
    Key? key,
    required this.label,
    required this.onPressed,
    required this.type,
    this.isActive = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: _getBackgroundColor(),
          foregroundColor: _getForegroundColor(),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: _getBorderColor(),
              width: 1,
            ),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: _getForegroundColor(),
          ),
        ),
      ),
    );
  }
  
  Color _getBackgroundColor() {
    if (isActive) {
      return const Color(0xFFF0EAFF);
    }
    
    switch (type) {
      case OperationButtonType.primary:
        return const Color(0xFF7B61FF);
      case OperationButtonType.secondary:
        return Colors.white;
    }
  }
  
  Color _getForegroundColor() {
    if (isActive) {
      return const Color(0xFF7B61FF);
    }
    
    switch (type) {
      case OperationButtonType.primary:
        return Colors.white;
      case OperationButtonType.secondary:
        return const Color(0xFF7B61FF);
    }
  }
  
  Color _getBorderColor() {
    if (isActive) {
      return const Color(0xFF7B61FF);
    }
    
    switch (type) {
      case OperationButtonType.primary:
        return const Color(0xFF7B61FF);
      case OperationButtonType.secondary:
        return const Color(0xFFE8DBFF);
    }
  }
}
