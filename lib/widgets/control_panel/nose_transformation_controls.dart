import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// 暂时注释掉这个导入，使用下面的导入代替
// import '../../beautify_feature/models/nose_transformation_parameters.dart';
// 使用与集成服务相同的NoseParameterType定义
// 以避免类型冲突
import '../../beautify_feature/models/nose_parameter.dart';
import '../../beautify_feature/services/nose_transformation_integration_service.dart';
import '../../utils/logger.dart';

/// 鼻部变形控制组件
/// 
/// 提供鼻部变形的参数控制界面
class NoseTransformationControls extends StatefulWidget {
  const NoseTransformationControls({Key? key}) : super(key: key);

  @override
  State<NoseTransformationControls> createState() => _NoseTransformationControlsState();
}

class _NoseTransformationControlsState extends State<NoseTransformationControls> {
  late NoseTransformationIntegrationService _integrationService;
  
  @override
  void initState() {
    super.initState();
    _integrationService = NoseTransformationIntegrationService();
    Logger.log('NoseTransformationControls', 'initState', '初始化鼻部变形控制组件');
  }
  
  @override
  void dispose() {
    _integrationService.dispose();
    Logger.log('NoseTransformationControls', 'dispose', '释放鼻部变形控制组件资源');
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _integrationService,
      child: Consumer<NoseTransformationIntegrationService>(
        builder: (context, service, child) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    const Icon(
                      Icons.face,
                      color: Color(0xFF7B61FF),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      '鼻部变形',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF7B61FF),
                      ),
                    ),
                    const Spacer(),
                    if (service.medicalAdvice.isNotEmpty)
                      Tooltip(
                        message: service.medicalAdvice.join('\n'),
                        child: const Icon(
                          Icons.medical_services,
                          color: Colors.orange,
                        ),
                      ),
                  ],
                ),
              ),
              
              // 参数控制
              _buildParameterSlider(
                context, 
                '鼻梁高度', 
                NoseParameterType.bridgeHeight,
                service,
              ),
              _buildParameterSlider(
                context, 
                '鼻梁宽度', 
                NoseParameterType.bridgeWidth,
                service,
              ),
              _buildParameterSlider(
                context, 
                '鼻尖长度', 
                NoseParameterType.tipLength,
                service,
              ),
              _buildParameterSlider(
                context, 
                '鼻尖高度', 
                NoseParameterType.tipHeight,
                service,
              ),
              _buildParameterSlider(
                context, 
                '鼻尖宽度', 
                NoseParameterType.tipWidth,
                service,
              ),
              _buildParameterSlider(
                context, 
                '鼻孔大小', 
                NoseParameterType.nostrilSize,
                service,
              ),
              _buildParameterSlider(
                context, 
                '鼻孔宽度', 
                NoseParameterType.nostrilWidth,
                service,
              ),
              _buildParameterSlider(
                context, 
                '鼻基高度', 
                NoseParameterType.baseHeight,
                service,
              ),
              _buildParameterSlider(
                context, 
                '鼻基宽度', 
                NoseParameterType.baseWidth,
                service,
              ),
              
              // 操作按钮
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: service.isProcessing 
                        ? null 
                        : () {
                            service.resetAllParameters();
                            Logger.log('NoseTransformationControls', 'resetParameters', '重置参数');
                          },
                      icon: const Icon(Icons.refresh),
                      label: const Text('重置'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[200],
                        foregroundColor: Colors.black87,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: service.isProcessing 
                        ? null 
                        : () {
                            service.applyTransformation();
                            Logger.log('NoseTransformationControls', 'applyTransformation', '应用变形');
                          },
                      icon: const Icon(Icons.check),
                      label: const Text('应用'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF7B61FF),
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
  
  /// 构建参数滑块
  Widget _buildParameterSlider(
    BuildContext context, 
    String label, 
    NoseParameterType type,
    NoseTransformationIntegrationService service,
  ) {
    // 获取参数对象
    final parameter = service.getParameterValue(type);
    
    if (parameter == null) {
      return const SizedBox.shrink();
    }
    
    // 将NoseParameterType转换为参数名称字符串
    String parameterName = _getParameterNameFromType(type);
    
    // 检查当前是否是选中的参数
    bool isSelected = service.selectedParameterType == type;
    
    return GestureDetector(
      onTap: () {
        // 点击参数项时，设置为当前选中的参数
        service.selectParameter(type);
        
        // 更新变形渲染器的参数名称
        service.updateDeformationRendererParameter(parameterName);
        
        Logger.log(
          'NoseTransformationControls', 
          'selectParameter', 
          '选择参数: ${type.toString()}, 参数名称: $parameterName',
        );
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: isSelected ? const Color(0xFF7B61FF) : Colors.black87,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                const Spacer(),
                Text(
                  parameter.value.toStringAsFixed(1),
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? const Color(0xFF7B61FF) : Colors.black54,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            SliderTheme(
              data: SliderThemeData(
                trackHeight: 4,
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                overlayShape: const RoundSliderOverlayShape(overlayRadius: 14),
                activeTrackColor: isSelected ? const Color(0xFF7B61FF) : Theme.of(context).primaryColor,
                inactiveTrackColor: Colors.grey[300],
                thumbColor: isSelected ? const Color(0xFF7B61FF) : Theme.of(context).primaryColor,
              ),
              child: Slider(
                value: (parameter.value - parameter.constraint.min) / 
                      (parameter.constraint.max - parameter.constraint.min),
                onChanged: (value) {
                  if (service.isProcessing) return;
                  
                  setState(() {
                    final newValue = parameter.constraint.min + 
                                   value * (parameter.constraint.max - parameter.constraint.min);
                    
                    // 确定变形方向 - 根据新值与当前值的对比确定
                    // 如果新值大于当前值，则为增大（加号）；否则为减小（减号）
                    bool isIncreasing = newValue > parameter.value;
                    Logger.log(
                      'NoseTransformationControls', 
                      'updateParameter', 
                      '变形方向: ${isIncreasing ? "增大" : "减小"} | 当前值: ${parameter.value}, 新值: $newValue',
                    );
                    
                    // 传递isIncreasing参数
                    service.updateParameter(type, newValue, isIncreasing: isIncreasing);
                    
                    // 如果调整滑块，也设置为当前选中的参数
                    if (service.selectedParameterType != type) {
                      service.selectParameter(type);
                      
                      // 更新变形渲染器的参数名称
                      service.updateDeformationRendererParameter(parameterName);
                    }
                    
                    // 记录参数更新日志
                    Logger.log(
                      'NoseTransformationControls', 
                      'updateParameter', 
                      '更新参数: ${type.toString()}, 值: ${newValue.toStringAsFixed(2)}',
                    );
                  });
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 将NoseParameterType转换为参数名称字符串
  String _getParameterNameFromType(NoseParameterType type) {
    switch (type) {
      case NoseParameterType.bridgeHeight:
        return 'nose_bridge_height';
      case NoseParameterType.bridgeWidth:
        return 'nose_bridge_width';
      case NoseParameterType.tipLength:
        return 'nose_tip_length';
      case NoseParameterType.tipHeight:
        return 'nose_tip_height';
      case NoseParameterType.tipWidth:
        return 'nose_tip_width';
      case NoseParameterType.nostrilSize:
        return 'nose_nostril_size';
      case NoseParameterType.nostrilWidth:
        return 'nose_nostril_width';
      case NoseParameterType.baseHeight:
        return 'nose_base_height';
      case NoseParameterType.baseWidth:
        return 'nose_base_width';
      default:
        return 'nose_height';
    }
  }
}
