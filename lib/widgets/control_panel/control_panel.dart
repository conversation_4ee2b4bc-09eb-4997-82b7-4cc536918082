import 'package:flutter/material.dart';
import 'style_presets.dart';
import 'parameter_controls.dart';
import 'operation_buttons.dart';

class ControlPanel extends StatelessWidget {
  const ControlPanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 风格预设区 (40%)
          Container(
            height: MediaQuery.of(context).size.height * 0.4,
            padding: const EdgeInsets.all(16),
            child: const StylePresets(),
          ),
          const Divider(height: 1, color: Color(0xFFE8DBFF)),
          // 参数调整区 (50%)
          Container(
            height: MediaQuery.of(context).size.height * 0.5,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: const ParameterControls(),
          ),
          const Divider(height: 1, color: Color(0xFFE8DBFF)),
          // 操作按钮区 (10%)
          Container(
            height: MediaQuery.of(context).size.height * 0.1,
            child: const OperationButtons(),
          ),
        ],
      ),
    );
  }
}
