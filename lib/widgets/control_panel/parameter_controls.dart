import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../beautify_feature/services/transformation_service.dart';
import '../../utils/logger.dart';
import 'nose_transformation_controls.dart';

class ParameterControls extends StatelessWidget {
  const ParameterControls({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final transformationService = Provider.of<TransformationService>(context);
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ParameterGroup(
              title: '面部调整',
              groupId: '面部',
              parameters: const [
                ParameterSlider(
                  label: '脸型优化',
                  paramName: 'face_shape',
                  value: 0.5,
                ),
                ParameterSlider(
                  label: '下巴调整',
                  paramName: 'chin_adjust',
                  value: 0.5,
                ),
              ],
              onTap: () {
                transformationService.selectArea('面部');
                Logger.log('ParameterControls', 'onTap', '选择面部区域');
              },
              isSelected: transformationService.selectedArea == '面部',
            ),
            const SizedBox(height: 20),
            ParameterGroup(
              title: '鼻部调整',
              groupId: '鼻部',
              parameters: const [
                ParameterSlider(
                  label: '鼻梁高度',
                  paramName: 'bridge_height',
                  value: 0.5,
                ),
                ParameterSlider(
                  label: '鼻尖调整',
                  paramName: 'tip_adjust',
                  value: 0.5,
                ),
              ],
              onTap: () {
                transformationService.selectArea('鼻部');
                Logger.log('ParameterControls', 'onTap', '选择鼻部区域');
              },
              isSelected: transformationService.selectedArea == '鼻部',
              extraContent: const NoseTransformationControls(),
            ),
            const SizedBox(height: 20),
            ParameterGroup(
              title: '眼部调整',
              groupId: '眼部',
              parameters: const [
                ParameterSlider(
                  label: '双眼皮',
                  paramName: 'double_fold',
                  value: 0.5,
                ),
                ParameterSlider(
                  label: '开眼角',
                  paramName: 'canthal_tilt',
                  value: 0.5,
                ),
              ],
              onTap: () {
                transformationService.selectArea('眼部');
                Logger.log('ParameterControls', 'onTap', '选择眼部区域');
              },
              isSelected: transformationService.selectedArea == '眼部',
            ),
            const SizedBox(height: 20),
            ParameterGroup(
              title: '唇部调整',
              groupId: '唇部',
              parameters: const [
                ParameterSlider(
                  label: '唇形调整',
                  paramName: 'lip_shape',
                  value: 0.5,
                ),
              ],
              onTap: () {
                transformationService.selectArea('唇部');
                Logger.log('ParameterControls', 'onTap', '选择唇部区域');
              },
              isSelected: transformationService.selectedArea == '唇部',
            ),
            const SizedBox(height: 20),
            ParameterGroup(
              title: '抗衰调整',
              groupId: '抗衰',
              parameters: const [
                ParameterSlider(
                  label: '去皱纹',
                  paramName: 'wrinkle_removal',
                  value: 0.5,
                ),
                ParameterSlider(
                  label: '法令纹',
                  paramName: 'nasolabial_folds',
                  value: 0.5,
                ),
              ],
              onTap: () {
                transformationService.selectArea('抗衰');
                Logger.log('ParameterControls', 'onTap', '选择抗衰区域');
              },
              isSelected: transformationService.selectedArea == '抗衰',
            ),
          ],
        ),
      ),
    );
  }
}

class ParameterGroup extends StatelessWidget {
  final String title;
  final String groupId;
  final List<Widget> parameters;
  final VoidCallback onTap;
  final bool isSelected;
  final Widget? extraContent;

  const ParameterGroup({
    Key? key,
    required this.title,
    required this.groupId,
    required this.parameters,
    required this.onTap,
    this.isSelected = false,
    this.extraContent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFF0EAFF) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? const Color(0xFF7B61FF) : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.adjust,
                  color: isSelected ? const Color(0xFF7B61FF) : Colors.grey.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? const Color(0xFF7B61FF) : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...parameters,
            if (extraContent != null) ...[
              const SizedBox(height: 12),
              extraContent!,
            ],
          ],
        ),
      ),
    );
  }
}

class ParameterSlider extends StatefulWidget {
  final String label;
  final String paramName;
  final double value;

  const ParameterSlider({
    Key? key,
    required this.label,
    required this.paramName,
    required this.value,
  }) : super(key: key);

  @override
  State<ParameterSlider> createState() => _ParameterSliderState();
}

class _ParameterSliderState extends State<ParameterSlider> {
  late double _value;

  @override
  void initState() {
    super.initState();
    _value = widget.value;
  }

  @override
  Widget build(BuildContext context) {
    final transformationService = Provider.of<TransformationService>(context);
    final selectedArea = transformationService.selectedArea;
    
    // 如果当前参数所在的区域被选中，则从变形服务获取参数值
    if (selectedArea != null && selectedArea.isNotEmpty) {
      final areaParams = transformationService.getAreaParams(selectedArea);
      // 检查参数名称是否在区域参数列表中
      if (areaParams.contains(widget.paramName)) {
        // 这里我们只检查参数是否存在，但不从列表中获取值
        // 因为列表只包含参数名称，不包含值
        // 实际值应该从变形服务的其他方法获取
        _value = 0.0; // 设置为默认值
      }
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
            Text(
              '${(_value * 100).round()}%',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
              ),
            ),
          ],
        ),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: const Color(0xFF7B61FF),
            inactiveTrackColor: const Color(0xFFE8DBFF),
            thumbColor: const Color(0xFF7B61FF),
            overlayColor: const Color(0x297B61FF),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(
              enabledThumbRadius: 10,
            ),
            overlayShape: const RoundSliderOverlayShape(
              overlayRadius: 20,
            ),
          ),
          child: Slider(
            value: _value,
            onChanged: (value) {
              setState(() {
                _value = value;
              });
              
              // 记录参数调整前的值
              final oldValue = _value;
              
              // 更新变形服务中的参数值
              if (selectedArea != null && selectedArea.isNotEmpty) {
                transformationService.updateTransformationParam(
                  selectedArea, 
                  widget.paramName, 
                  value
                );
                Logger.log('ParameterSlider', 'onChanged', 
                  '调整参数: 区域=$selectedArea, 参数=${widget.paramName}, 值=$value'
                );
                
                // 调试输出
                debugPrint('参数调整: ${widget.label} (${oldValue.toStringAsFixed(1)} -> ${value.toStringAsFixed(1)})');
              }
            },
          ),
        ),
      ],
    );
  }
}
