import 'package:flutter/material.dart';

class StylePresets extends StatelessWidget {
  const StylePresets({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '风格预设',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 0.8,
              children: const [
                StylePresetCard(
                  title: '国人审美',
                  description: '温婉东方美',
                  image: 'assets/style_chinese.jpg',
                ),
                StylePresetCard(
                  title: '亚洲流行',
                  description: '韩日精致风',
                  image: 'assets/style_asian.jpg',
                ),
                StylePresetCard(
                  title: '西方风格',
                  description: '欧美立体感',
                  image: 'assets/style_western.jpg',
                ),
                StylePresetCard(
                  title: '自然风格',
                  description: '最小程度修改',
                  image: 'assets/style_natural.jpg',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class StylePresetCard extends StatelessWidget {
  final String title;
  final String description;
  final String image;

  const StylePresetCard({
    Key? key,
    required this.title,
    required this.description,
    required this.image,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE8DBFF),
          width: 1,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            flex: 3,
            child: Image.asset(
              image,
              fit: BoxFit.cover,
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(8),
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
