import 'dart:io';
import 'package:flutter/material.dart';

/// 图像对比小部件
/// 
/// 用于显示原始图像和变形后的图像的对比
class ImageComparisonWidget extends StatefulWidget {
  final String originalImagePath;
  final String transformedImagePath;
  final double dividerPosition;
  final Color dividerColor;
  final double dividerWidth;
  
  const ImageComparisonWidget({
    Key? key,
    required this.originalImagePath,
    required this.transformedImagePath,
    this.dividerPosition = 0.5,
    this.dividerColor = Colors.white,
    this.dividerWidth = 2.0,
  }) : super(key: key);

  @override
  State<ImageComparisonWidget> createState() => _ImageComparisonWidgetState();
}

class _ImageComparisonWidgetState extends State<ImageComparisonWidget> {
  late double _dividerPosition;
  bool _isDragging = false;
  
  @override
  void initState() {
    super.initState();
    _dividerPosition = widget.dividerPosition;
  }
  
  @override
  void didUpdateWidget(ImageComparisonWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.dividerPosition != widget.dividerPosition) {
      _dividerPosition = widget.dividerPosition;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;
        final dividerX = width * _dividerPosition;
        
        return Stack(
          children: [
            // 原始图像（左侧）
            Positioned.fill(
              child: ClipRect(
                child: Align(
                  alignment: Alignment.centerLeft,
                  widthFactor: _dividerPosition,
                  child: Image.file(
                    File(widget.originalImagePath),
                    fit: BoxFit.cover,
                    width: width,
                    height: height,
                  ),
                ),
              ),
            ),
            
            // 变形后图像（右侧）
            Positioned.fill(
              child: ClipRect(
                clipper: _RightClipper(_dividerPosition),
                child: Image.file(
                  File(widget.transformedImagePath),
                  fit: BoxFit.cover,
                  width: width,
                  height: height,
                ),
              ),
            ),
            
            // 分隔线
            Positioned(
              top: 0,
              bottom: 0,
              left: dividerX - widget.dividerWidth / 2,
              width: widget.dividerWidth,
              child: Container(
                color: widget.dividerColor,
              ),
            ),
            
            // 拖动手柄
            Positioned(
              top: height / 2 - 15,
              left: dividerX - 15,
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: widget.dividerColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.compare_arrows,
                  color: Colors.black87,
                  size: 20,
                ),
              ),
            ),
            
            // 拖动区域
            Positioned.fill(
              child: GestureDetector(
                onHorizontalDragStart: (details) {
                  setState(() {
                    _isDragging = true;
                  });
                },
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    _dividerPosition = (details.localPosition.dx / width)
                        .clamp(0.1, 0.9);
                  });
                },
                onHorizontalDragEnd: (details) {
                  setState(() {
                    _isDragging = false;
                  });
                },
              ),
            ),
            
            // 标签
            Positioned(
              top: 16,
              left: dividerX / 2 - 40,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '原图',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            
            Positioned(
              top: 16,
              right: (width - dividerX) / 2 - 40,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '变形后',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

/// 右侧裁剪器
class _RightClipper extends CustomClipper<Rect> {
  final double dividerPosition;
  
  _RightClipper(this.dividerPosition);
  
  @override
  Rect getClip(Size size) {
    return Rect.fromLTRB(
      size.width * dividerPosition,
      0,
      size.width,
      size.height,
    );
  }
  
  @override
  bool shouldReclip(_RightClipper oldClipper) {
    return dividerPosition != oldClipper.dividerPosition;
  }
}
