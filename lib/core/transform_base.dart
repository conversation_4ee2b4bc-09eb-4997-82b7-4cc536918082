import 'dart:math';
import 'package:flutter/material.dart';
import 'transform_type.dart';  // 导入 TransformType 枚举

/// 变形方向枚举
enum TransformDirection {
  horizontal,  // 水平方向
  vertical,    // 垂直方向
  depth,       // 深度方向
  radial,      // 径向
  diagonal,    // 斜向
  custom       // 自定义方向
}

/// 曲线类型枚举
enum CurveType {
  linear,      // 线性
  quadratic,   // 二次曲线
  cubic,       // 三次曲线
  sine,        // 正弦曲线
  custom       // 自定义曲线
}

/// 变形向量
class TransformVector {
  final TransformDirection direction;
  final double magnitude;
  final List<double>? customDirection;
  final double? angle;  // 用于斜向变形

  const TransformVector({
    required this.direction,
    this.magnitude = 1.0,
    this.customDirection,
    this.angle,
  });
}

/// 变形配置类
class TransformConfig {
  final TransformDirection direction;
  final double weight;
  final double? angle;       // 用于自定义方向
  final double? startPoint;  // 变形起始点
  final double? endPoint;    // 变形结束点
  
  const TransformConfig({
    required this.direction,
    required this.weight,
    this.angle,
    this.startPoint,
    this.endPoint,
  });
}

/// 点变形配置
class PointTransformConfig {
  final TransformVector transformVector;
  final double weight;
  final double falloffRadius;
  final double constraintStrength;

  const PointTransformConfig({
    required this.transformVector,
    this.weight = 1.0,
    this.falloffRadius = 50.0,
    this.constraintStrength = 1.0,
  });
}

/// 强度映射
class IntensityMapping {
  final (double, double) inputRange;
  final (double, double) outputRange;
  final CurveType curveType;
  final List<(double, double)>? controlPoints;

  const IntensityMapping({
    required this.inputRange,
    required this.outputRange,
    required this.curveType,
    this.controlPoints,
  });
}

/// 特征点样式配置
const Map<String, Map<String, dynamic>> pointStyles = {
  'primary': {
    'size': 2.0,
    'color': Color(0xFF0000FF),  // 蓝色
    'opacity': 1.0,
    'hasGlow': true,
    'glowColor': Color(0xFF6464FF),  // 浅蓝色光晕
    'glowRadius': 4.0,
    'glowThickness': 1.0,
  },
  'secondary': {
    'size': 2.0,
    'color': Color(0xFF1E90FF),  // 道奇蓝
    'opacity': 1.0,
    'hasGlow': true,
    'glowColor': Color(0xFF87CEFA),  // 浅蓝色光晕
    'glowRadius': 3.0,
    'glowThickness': 1.0,
  },
  'auxiliary': {
    'size': 1.0,
    'color': Color(0xFF6495ED),  // 浅蓝色
    'opacity': 1.0,
    'hasGlow': false,
  },
};

/// 点的动画配置
const Map<String, double> pointAnimationConfig = {
  'minPointSize': 0.5,     // 最小点大小
  'maxPointSize': 2.0,     // 高亮点大小
  'minOpacity': 0.2,       // 最小透明度
  'maxOpacity': 1.0,       // 最大透明度
  'connectionThickness': 1.0, // 连接线粗细
};

/// 变形阶段
class TransformStage {
  final String name;
  final double intensity;
  final List<int> affectedPoints;
  final TransformType transformType;
  
  const TransformStage({
    required this.name,
    required this.intensity,
    required this.affectedPoints,
    required this.transformType,
  });
}

/// 高级参数配置
class AdvancedParameterConfig {
  final String name;
  final TransformType primaryTransformType;
  final TransformDirection primaryDirection;
  final double baseIntensity;
  final List<TransformStage> stages;
  final Map<String, dynamic> anatomicalConstraints;
  
  const AdvancedParameterConfig({
    required this.name,
    required this.primaryTransformType,
    required this.primaryDirection,
    required this.baseIntensity,
    required this.stages,
    required this.anatomicalConstraints,
  });
}

/// 复合体系统
class ComplexSystem {
  final String name;
  final List<int> primaryPoints;
  final List<int> secondaryPoints;
  final List<int> auxiliaryPoints;
  final double elasticity;
  final double resistance;
  final double recovery;
  final List<Map<String, int>> pointPairs;
  
  const ComplexSystem({
    required this.name,
    required this.primaryPoints,
    required this.secondaryPoints,
    required this.auxiliaryPoints,
    this.elasticity = 1.0,
    this.resistance = 0.5,
    this.recovery = 0.8,
    this.pointPairs = const [],
  });
  
  List<int> getAllPoints() {
    final allPoints = <int>{};
    allPoints.addAll(primaryPoints);
    allPoints.addAll(secondaryPoints);
    allPoints.addAll(auxiliaryPoints);
    return allPoints.toList();
  }
}

/// 高级区域配置
class AdvancedAreaConfig {
  final String name;
  final Map<String, ComplexSystem> complexSystems;
  final Map<String, AdvancedParameterConfig> parameters;
  
  const AdvancedAreaConfig({
    required this.name,
    required this.complexSystems,
    required this.parameters,
  });
}

/// 变形计算辅助类
class DeformationCalculator {
  /// 计算平滑曲线变形
  static Map<int, Offset> calculateSmoothCurveDeformation({
    required List<int> points,
    required TransformDirection direction,
    required double intensity,
    required double baseWeight,
    Map<int, double>? pointWeights,
  }) {
    final deformations = <int, Offset>{};
    
    // 根据点的位置和权重计算变形量
    for (int i = 0; i < points.length; i++) {
      final point = points[i];
      final weight = pointWeights?[point] ?? baseWeight;
      
      // 计算相对位置 (0.0 - 1.0)
      final position = i / (points.length - 1);
      
      // 使用二次曲线计算变形强度
      final curveIntensity = intensity * (1 - (2 * position - 1) * (2 * position - 1));
      
      // 根据方向设置变形向量
      switch (direction) {
        case TransformDirection.vertical:
          deformations[point] = Offset(0, curveIntensity * weight);
          break;
        case TransformDirection.horizontal:
          deformations[point] = Offset(curveIntensity * weight, 0);
          break;
        case TransformDirection.depth:
          // 深度变形在2D中表现为垂直和水平的组合
          // 使用更小的系数，使变形效果更自然
          deformations[point] = Offset(curveIntensity * weight * 0.2, curveIntensity * weight * 0.7);
          break;
        case TransformDirection.diagonal:
          // 对角线变形
          final angle = 45.0 * (3.14159 / 180.0); // 默认45度角
          // 使用0.7倍的减弱因子使变形效果更自然
          deformations[point] = Offset(
            curveIntensity * weight * cos(angle) * 0.7,
            curveIntensity * weight * sin(angle) * 0.7
          );
          break;
        case TransformDirection.radial:
          // 径向变形，从中心点向外扩散
          // 使用0.4倍的减弱因子使变形效果更自然
          deformations[point] = Offset(curveIntensity * weight * 0.4, curveIntensity * weight * 0.4);
          break;
        case TransformDirection.custom:
          // 自定义方向，默认使用水平方向
          deformations[point] = Offset(curveIntensity * weight, 0);
          break;
      }
    }
    
    return deformations;
  }
  
  /// 计算自然过渡变形
  static Map<int, Offset> calculateNaturalTransitionDeformation({
    required List<int> points,
    required TransformDirection direction,
    required double intensity,
    required double baseWeight,
    Map<int, double>? pointWeights,
    double? transitionPoint,
  }) {
    final deformations = <int, Offset>{};
    final transition = transitionPoint ?? 0.5; // 默认在中点过渡
    
    // 根据点的位置和权重计算变形量
    for (int i = 0; i < points.length; i++) {
      final point = points[i];
      final weight = pointWeights?[point] ?? baseWeight;
      
      // 计算相对位置 (0.0 - 1.0)
      final position = i / (points.length - 1);
      
      // 使用S形曲线计算变形强度，实现自然过渡
      double curveIntensity;
      if (position < transition) {
        // 前半段使用加速曲线
        curveIntensity = intensity * (position / transition) * (position / transition);
      } else {
        // 后半段使用减速曲线
        final normalizedPos = (position - transition) / (1 - transition);
        curveIntensity = intensity * (1 - (1 - normalizedPos) * (1 - normalizedPos));
      }
      
      // 根据方向设置变形向量
      switch (direction) {
        case TransformDirection.vertical:
          deformations[point] = Offset(0, curveIntensity * weight);
          break;
        case TransformDirection.horizontal:
          deformations[point] = Offset(curveIntensity * weight, 0);
          break;
        case TransformDirection.depth:
          deformations[point] = Offset(curveIntensity * weight * 0.3, curveIntensity * weight);
          break;
        case TransformDirection.diagonal:
          final angle = 45.0 * (3.14159 / 180.0);
          deformations[point] = Offset(
            curveIntensity * weight * cos(angle),
            curveIntensity * weight * sin(angle)
          );
          break;
        case TransformDirection.radial:
          deformations[point] = Offset(curveIntensity * weight * 0.5, curveIntensity * weight * 0.5);
          break;
        case TransformDirection.custom:
          deformations[point] = Offset(curveIntensity * weight, 0);
          break;
      }
    }
    
    return deformations;
  }
  
  /// 计算对称径向变形 - 极简版本
  static Map<int, Offset> calculateSymmetricRadialDeformation({
    required List<int> points,
    required List<Map<String, int>> symmetricPairs,
    required int centerPoint,
    required double intensity,
    required double baseWeight,
    Map<int, double>? pointWeights,
  }) {
    final deformations = <int, Offset>{};
    final pairMap = <int, int>{};
    
    // 构建对称点对映射
    for (final pair in symmetricPairs) {
      final left = pair['left'];
      final right = pair['right'];
      if (left != null && right != null) {
        pairMap[left] = right;
        pairMap[right] = left;
      }
    }
    
    // 处理每个点的变形
    for (final point in points) {
      // 简化权重计算
      final weight = pointWeights?[point] ?? baseWeight;
      final symmetricPoint = pairMap[point];
      
      // 判断方向：使用对称点对信息来决定左右
      double direction = 0.0;
      
      // 首先检查这个点是否在对称点对中
      bool isLeftPoint = false;
      bool isRightPoint = false;
      
      for (final pair in symmetricPairs) {
        if (pair['left'] == point) {
          isLeftPoint = true;
          break;
        } else if (pair['right'] == point) {
          isRightPoint = true;
          break;
        }
      }
      
      // 根据对称点对信息设置方向
      if (isLeftPoint) {
        direction = -1.0;  // 左侧点为负
      } else if (isRightPoint) {
        direction = 1.0;   // 右侧点为正
      } else if (point == centerPoint) {
        direction = 0.0;   // 中心点不变形
      } else {
        // 如果不是对称点对中的点，使用与中心点的相对位置判断
        // 这里我们需要访问特征点的实际坐标信息，但在当前上下文中没有这些信息
        // 我们可以使用一个简化的方法：假设左侧特征点ID范围大致在100-200之间，右侧在300-400之间
        // 这不是完全准确的方法，但可以作为临时解决方案
        if (point < 200) {
          direction = -1.0;  // 假设小于200的点在左侧
        } else if (point >= 300) {
          direction = 1.0;   // 假设大于等于300的点在右侧
        } else {
          direction = 0.0;   // 其他点不确定方向，不变形
        }
      }
      
      // 计算变形量
      final horizontalComponent = direction * intensity * weight * 3.0;
      final verticalComponent = intensity * weight * 0.3 * (point == centerPoint ? 0 : 1);
      
      deformations[point] = Offset(horizontalComponent, verticalComponent);
    }
    
    return deformations;
  }
  
  /// 计算弹性变形
  static Map<int, Offset> calculateElasticDeformation({
    required List<int> points,
    required TransformDirection direction,
    required double intensity,
    required double baseWeight,
    Map<int, double>? pointWeights,
  }) {
    final Map<int, Offset> deformations = {};
    
    for (final point in points) {
      final weight = (pointWeights != null && pointWeights.containsKey(point))
          ? pointWeights[point]! * baseWeight
          : baseWeight;
      
      final magnitude = intensity * weight;
      
      switch (direction) {
        case TransformDirection.horizontal:
          deformations[point] = Offset(magnitude, 0);
          break;
        case TransformDirection.vertical:
          deformations[point] = Offset(0, magnitude);
          break;
        case TransformDirection.diagonal:
          deformations[point] = Offset(magnitude * 0.7, magnitude * 0.7);
          break;
        case TransformDirection.depth:
          // 深度在2D上表现为水平和垂直的组合
          deformations[point] = Offset(magnitude * 0.3, magnitude * 0.7);
          break;
        case TransformDirection.radial:
          // 径向变形，从中心点向外扩散
          deformations[point] = Offset(magnitude * 0.5, magnitude * 0.5);
          break;
        case TransformDirection.custom:
          // 自定义方向，默认使用水平方向
          deformations[point] = Offset(magnitude, 0);
          break;
      }
    }
    
    return deformations;
  }
}
