import 'dart:ui' as ui;
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'dart:io';
import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;
import '../utils/logger.dart';
import 'models/feature_point.dart';
import '../beautify_feature/services/transformation_service.dart';
import '../services/mediapipe_transformation_service.dart';
import 'feature_point_manager.dart';
import 'deformation_utils.dart';
import 'simple_deformation_painter.dart' hide Logger;
// 【架构清理】移除通用特征点辅助工具，所有功能已整合到策略类中
import 'deformation_area_type.dart';
import 'transformations/transformation_factory.dart';
import 'deformation_cache_manager.dart';
import 'breathing_animation_controller.dart';
import 'transform_type.dart';
import 'package:beautifun/core/parameter_value_manager.dart';

/// 简化版变形区域渲染器
/// 支持使用真实特征点进行精准渲染
class SimpleDeformationRenderer extends ChangeNotifier {
  static const String _logTag = 'SimpleDeformationRenderer';
  
  // 用于避免重复输出缓存命中日志
  static String? _lastCacheHitParam;
  static int _lastCacheHitFeaturePointCount = 0;
  
  // 用于避免重复输出缓存未命中日志
  static String? _lastCacheMissParam;
  
  // 缓存查询锁定机制
  bool _isCacheQueryLocked = false;
  DateTime? _lastCacheQueryTime;
  final Duration _minCacheQueryInterval = Duration(milliseconds: 500);
  
  /// 单例实例
  static SimpleDeformationRenderer? _instance;
  
  /// 获取单例实例
  static SimpleDeformationRenderer get instance {
    if (_instance == null) {
      // 创建特征点管理器
      final featurePointManager = FeaturePointManager();
      
      // 创建单例实例，使用私有构造函数
      _instance = SimpleDeformationRenderer._private(
        featurePointManager: featurePointManager,
      );
      Logger.i(_logTag, '初始化单例实例');
    }
    return _instance!;
  }
  
  /// 检查是否应该执行缓存查询
  /// 这个方法用于防止短时间内重复查询缓存
  /// 返回 true 表示应该执行查询，false 表示应该跳过查询
  bool _shouldPerformCacheQuery() {
    final now = DateTime.now();
    
    // 如果已经锁定，直接跳过
    if (_isCacheQueryLocked) {
      Logger.flow(_logTag, '_shouldPerformCacheQuery', '⚠️ 缓存查询已锁定，跳过查询');
      return false;
    }
    
    // 检查上次查询时间
    if (_lastCacheQueryTime != null) {
      final timeSinceLastQuery = now.difference(_lastCacheQueryTime!);
      if (timeSinceLastQuery < _minCacheQueryInterval) {
        Logger.flow(_logTag, '_shouldPerformCacheQuery', '⚠️ 防止频繁缓存查询: 在${timeSinceLastQuery.inMilliseconds}毫秒内尝试重复查询，已跳过');
        return false;
      }
    }
    
    // 更新上次查询时间
    _lastCacheQueryTime = now;
    return true;
  }
  
  // 初始化标志
  bool _initialized = false;
  bool get isInitialized => _initialized;
  
  // 变形区域类型
  String _areaType = 'none';
  
  // 参数名称
  String _parameterName = '';
  
  // 参数值
  double _paramValue = 0.0;
  
  // 在参数切换和变形时不需要使用标志位，简化逻辑
  
  // 参数值是否在增加（点击加号按钮）
  bool? _isParameterIncreasing;
  
  /// 获取参数值是否在增加
  bool? get isParameterIncreasing => _isParameterIncreasing;
  
  // 绘制器缓存
  SimpleDeformationPainter? _cachedPainter;
  DateTime? _lastPainterCreateTime;
  
  // 强度
  double _intensity = 1.0;
  
  // 变形区域可见性
  bool _isVisible = false;
  
  // 变形区域透明度
  double _opacity = 0.5;
  
  // 是否显示变形区域可视化（与实际变形效果无关）
  bool _showDeformationArea = false;
  
  // 图像尺寸
  Size _imageSize = Size.zero;
  
  // 图像对象
  ui.Image? _image;
  
  // 侧面图像对象
  ui.Image? _sideImage;
  
  /// 获取图像尺寸
  Size get imageSize => _imageSize;
  
  /// 获取图像对象
  ui.Image? get image => _image;
  
  /// 获取侧面图像对象
  ui.Image? get sideImage => _sideImage;
  
  // 是否显示坐标系
  bool _showCoordinateSystem = false;
  
  // 是否显示特征点
  bool _showFeaturePoints = true;
  
  // 面部中心线是否已计算
  bool _facialCenterLineCalculated = false;

  // 特征点管理器
  final FeaturePointManager _featurePointManager;
  
  // 变形缓存管理器
  final DeformationCacheManager _deformationCacheManager = DeformationCacheManager();
  
  /// 更新特征点
  FeaturePoint _updateFeaturePoint(FeaturePoint oldPoint, {double? newX, double? newY}) {
    Logger.flow(_logTag, '_updateFeaturePoint', '更新特征点 ID=${oldPoint.index} 坐标: (${newX ?? oldPoint.x}, ${newY ?? oldPoint.y})');
    return oldPoint.copyWith(
      x: newX,
      y: newY,
    );
  }
  
  /// 计算面部中心线X坐标
  double _calculateFacialCenterLineX(List<FeaturePoint> featurePoints) {
    Logger.flowStart(_logTag, '_calculateFacialCenterLineX');
    
    // 如果面部中心线已经计算过，直接返回缓存的值
    if (_facialCenterLineCalculated && _cachedFacialCenterLineX != null) {
      Logger.flow(_logTag, '_calculateFacialCenterLineX', '使用已固定的面部中心线: $_cachedFacialCenterLineX');
      Logger.flowEnd(_logTag, '_calculateFacialCenterLineX');
      return _cachedFacialCenterLineX!;
    }
    
    Logger.flow(_logTag, '_calculateFacialCenterLineX', '计算面部中心线X坐标');
    
    if (featurePoints.isEmpty) {
      Logger.flow(_logTag, '_calculateFacialCenterLineX', '特征点列表为空，无法计算面部中心线');
      Logger.flowEnd(_logTag, '_calculateFacialCenterLineX');
      return 0;
    }
    
    // 计算所有特征点的X坐标平均值
    double sumX = 0;
    int validPoints = 0;
    
    for (var point in featurePoints) {
      sumX += point.x;
      validPoints++;
    }
    
    if (validPoints == 0) {
      Logger.flow(_logTag, '_calculateFacialCenterLineX', '没有有效的特征点，无法计算面部中心线');
      Logger.flowEnd(_logTag, '_calculateFacialCenterLineX');
      return 0;
    }
    
    // 计算平均值作为面部中心线X坐标
    final centerX = sumX / validPoints;
    
    // 保存计算结果并标记为已计算
    _cachedFacialCenterLineX = centerX;
    _facialCenterLineCalculated = true;
    
    Logger.flow(_logTag, '_calculateFacialCenterLineX', '面部中心线X坐标已计算并固定: $centerX');
    Logger.flowEnd(_logTag, '_calculateFacialCenterLineX');
    
    return centerX;
  }
  
  // 缓存最后创建的绘制器，避免频繁创建新实例
  // 【已废弃】不再需要缓存绘制器，因为绘制器已改为单例模式
  // SimpleDeformationPainter? _cachedPainter;
  
  /// 当前参数值的缓存查询结果
  DeformationStateValue? _currentCachedResult;
  
  // 记录上次创建绘制器时的参数
  String? _lastAreaType;
  String? _lastParameterName;
  double _lastParameterValue = 0.0;
  bool _lastIsVisible = true;
  bool _lastShowCoordinateSystem = false;
  bool _lastShowFeaturePoints = false;
  bool _lastShowDebugInfo = false;
  TransformType _lastTransformType = TransformType.local;
  double _lastBreathingValue = 0.0;
  List<int> _lastHighlightPointIds = [];
  List<int> _lastCurrentParameterPointIndexes = [];
  
  /// 上一次变形周期的唯一标识，用于避免重复保存缓存
  String? _lastDeformationCycleId;

  // 变形操作锁定标志，避免短时间内重复执行变形操作
  bool _isDeformationLocked = false;
  
  // 最后一次变形操作的时间
  DateTime? _lastDeformationTime;
  
  // 变形操作锁定的时间间隔（毫秒）
  final int _deformationLockTimeMillis = 200;

  // 记录上次日志输出的时间戳，用于限制日志输出频率
  static DateTime? _lastLogTime;
  static const Duration _minimumLogInterval = Duration(milliseconds: 1000);
  
  // 记录上次通知时间，用于限制notifyListeners调用频率
  DateTime _lastNotifyTime = DateTime.now();
  
  // 防止频繁创建绘制器的时间间隔（毫秒）
  static const Duration _minPainterCreateInterval = Duration(milliseconds: 500);
  
  // 最后一次创建绘制器的时间（已在第108行声明）

  // 高亮显示的特征点索引列表
  List<int> _highlightPointIds = [];
  
  // 呼吸动画控制器
  BreathingAnimationController? _breathingController;
  
  // 变形类型
  TransformType _transformType = TransformType.vectorField;
  
  /// 获取当前变形类型
  TransformType get transformType => _transformType;

  // 变形服务
  TransformationService? _transformationService;

  // 自动隐藏定时器
  
  // 自动隐藏定时器
  Timer? _autoHideTimer;

  // 缓存的面部中心线
  double? _cachedFacialCenterLineX;

  // 缓存变形后的图像和特征点，用于变形累积
  ui.Image? _accumulatedImage;
  List<FeaturePoint>? _accumulatedFeaturePoints;
  
  // 参数值管理器单例
  final ParameterValueManager _parameterValueManager = ParameterValueManager();
  

  // 变形缓存管理器
  final DeformationCacheManager _cacheManager = DeformationCacheManager();

  /// 私有构造函数，只能通过单例模式获取实例
  SimpleDeformationRenderer._private({
    required FeaturePointManager featurePointManager,
    TransformationService? transformationService,
  }) : _featurePointManager = featurePointManager {
    Logger.flow(_logTag, '构造函数', 'ℹ️ 🔧 [创建] 变形区域渲染器 (单例实例)');
    _transformationService = transformationService;
    _initBreathingController();
  }
  
  /// 当前实现中保留的公开构造函数，仅用于兼容现有代码
  /// 在实际使用中应该使用 instance 获取单例实例
  SimpleDeformationRenderer({
    required FeaturePointManager featurePointManager,
    TransformationService? transformationService,
  }) : _featurePointManager = featurePointManager {
    Logger.flow(_logTag, '构造函数', '⚠️ 警告: 直接创建了新的变形区域渲染器实例，应该使用 SimpleDeformationRenderer.instance 获取单例实例');
    _transformationService = transformationService;
    _initBreathingController();
  }
  
  /// 初始化呼吸动画控制器
  void _initBreathingController() {
    Logger.flowStart(_logTag, '_initBreathingController');
    
    // 创建呼吸动画控制器
    _breathingController = BreathingAnimationController();
    
    // 初始化控制器
    _breathingController!.initialize();
    
    // 【修复】移除呼吸动画监听器，避免无限循环创建绘制器
    // 呼吸动画值的变化应该只影响绘制器的渲染，不应该触发绘制器的重建
    // 绘制器会在需要时主动获取最新的呼吸值，无需通过监听器触发重建
    
    Logger.flow(_logTag, '_initBreathingController', '呼吸动画控制器已初始化（已移除监听器避免无限循环）');
    Logger.flowEnd(_logTag, '_initBreathingController');
  }
  
  /// 获取当前呼吸动画值
  /// 返回当前呼吸动画值，范围为 0.0-1.0
  double getBreathingValue() {
    if (_breathingController != null) {
      return _breathingController!.getBreathingValue();
    }
    return 1.0;
  }
  
  /// 设置是否启用呼吸动画
  /// 参数 enable 表示是否启用呼吸动画
  void setEnableBreathingAnimation(bool enable) {
    Logger.flowStart(_logTag, 'setEnableBreathingAnimation');
    
    if (_breathingController != null) {
      _breathingController!.setEnableBreathingAnimation(enable);
      Logger.flow(_logTag, 'setEnableBreathingAnimation', '🌬️ 呼吸动画${enable ? "已启用" : "已禁用"}');
    } else {
      Logger.flowError(_logTag, 'setEnableBreathingAnimation', '⚠️ 呼吸动画控制器为空，无法设置呼吸动画状态');
    }
    
    Logger.flowEnd(_logTag, 'setEnableBreathingAnimation');
  }
  
  /// 获取呼吸动画启用状态
  bool isBreathingAnimationEnabled() {
    if (_breathingController != null) {
      return _breathingController!.isBreathingAnimationEnabled();
    }
    return false;
  }
  
  /// 获取特征点管理器
  FeaturePointManager getFeaturePointManager() {
    return _featurePointManager;
  }
  
  /// 释放资源
  @override
  void dispose() {
    // 释放呼吸动画控制器资源
    if (_breathingController != null) {
      _breathingController!.dispose();
      _breathingController = null;
      Logger.flow(_logTag, 'dispose', '🗑️ 呼吸动画控制器已释放');
    }
    
    // 释放定时器资源
    if (_autoHideTimer != null) {
      _autoHideTimer!.cancel();
      _autoHideTimer = null;
      Logger.flow(_logTag, 'dispose', '🗑️ 自动隐藏定时器已释放');
    }
    
    // 【单例模式】清空缓存，不再清空_cachedPainter因为绘制器已改为单例
    // _cachedPainter = null;  // 不再需要，因为绘制器是单例
    _accumulatedImage = null;
    _accumulatedFeaturePoints = null;
    Logger.flow(_logTag, 'dispose', '🗑️ 缓存资源已清空（绘制器单例保留）');
    
    super.dispose();
    Logger.flow(_logTag, 'dispose', '🗑️ 变形区域渲染器已释放');
  }

  /// 设置参数值
  /// 设置参数值
  /// 
  /// [value] 参数值
  /// [isIncreasing] 用户是否点击了加号按钮，true表示点击加号，false表示点击减号
  /// 如果不提供该参数，则会通过比较参数值的变化来推断方向（不推荐）
  void setParameterValue(double value, {bool? isIncreasing}) {
    Logger.flowStart(_logTag, 'setParameterValue');
    
    // 添加流程日志
    print('🔄 [流程5] SimpleDeformationRenderer.setParameterValue - 参数值: $value, 方向: ${isIncreasing != null ? (isIncreasing ? "增大" : "减小") : "未指定"}');
    print('🔄 [流程6] 查找变形策略: ${_parameterName}');
    
    // 1. 获取当前区域和参数项
    if (_parameterName == null || _parameterName!.isEmpty) {
      Logger.flowWarning(_logTag, 'setParameterValue', '⚠️ 参数名称为空，无法设置参数值');
      Logger.flowEnd(_logTag, 'setParameterValue');
      return;
    }
    // 【修复】直接使用参数名称，不添加区域前缀
    final paramName = _parameterName!;
    
    // 2. 从参数值管理器获取当前值
    double currentValue = 0.0;
    if (_parameterValueManager.containsParameter(paramName)) {
      currentValue = _parameterValueManager.getValue(paramName);
    }
    
    // 确定变形方向
    if (isIncreasing == null) {
      Logger.flowError(_logTag, 'setParameterValue', '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'setParameterValue');
      return;
    }
    _isParameterIncreasing = isIncreasing;
    
    // 3. 更新参数值
    _paramValue = value;
    _parameterValueManager.setValue(paramName, value);
    
    // 记录关键信息
    String directionText = _isParameterIncreasing! ? "increase" : "decrease";
    String directionDisplay = _isParameterIncreasing! ? "增大" : "减小";
    String areaDisplay = _areaType == "lip" ? "唇形" : _areaType ?? "未知";
    
    // 输出参数和变形方向信息
    Logger.i(_logTag, '⟹ 【参数项】$paramName (当前值: $currentValue)');
    Logger.i(_logTag, '⤵ 【变形方向】${areaDisplay}变形: $directionDisplay ($directionText), 值: $currentValue → $value');
    
    // 检查特征点管理器
    if (_featurePointManager != null) {
      // 更新当前参数特征点索引
      _featurePointManager.updateCurrentParameterPointIndexes();
      
      // 记录当前特征点索引
      List<int> currentIndexes = _featurePointManager.getCurrentParameterPointIndexes();
      if (currentIndexes.isNotEmpty) {
        Logger.flow(_logTag, 'setParameterValue', '📈 当前特征点索引: $currentIndexes');
      } else {
        Logger.flowWarning(_logTag, 'setParameterValue', '⚠️ 当前参数没有关联的特征点');
      }
    }
    
    // 更新变形时间戳
    _lastDeformationTime = DateTime.now();
    
    // 【修复主图区显示问题】立即查询缓存并设置_currentCachedResult
    Map<String, double> allParams = _parameterValueManager.getAllParameters();
    _currentCachedResult = _deformationCacheManager.find(allParams);
    
    if (_currentCachedResult != null && _currentCachedResult!.deformedImage != null) {
      Logger.flow(_logTag, 'setParameterValue', '✅ 找到缓存结果: 图像哈希=${_currentCachedResult!.deformedImage.hashCode}');
      // 立即更新累积状态，确保主图区能正确显示
      _accumulatedImage = _currentCachedResult!.deformedImage;
      if (_currentCachedResult!.deformedFeaturePoints.isNotEmpty) {
        _accumulatedFeaturePoints = List<FeaturePoint>.from(_currentCachedResult!.deformedFeaturePoints);
      }
    } else {
      Logger.flow(_logTag, 'setParameterValue', '❌ 没有找到缓存结果，将执行新的变形计算');
    }
    
    // 记录全局状态
    List<FeaturePoint>? featurePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
    if (featurePoints != null) {
      Logger.flow(_logTag, 'setParameterValue', '👁️ 当前变形特征点总数: ${featurePoints.length}个');
    }
    
    Logger.flow(_logTag, 'setParameterValue', '✅ 完成: ${_parameterName}=$value');
    Logger.flowEnd(_logTag, 'setParameterValue');
  }

  /// 设置参数
  void setParameter(String parameterName) {
    // 检查是否在短时间内重复调用
    final now = DateTime.now();
    if (_lastPainterCreateTime != null) {
      final timeSinceLastCreate = now.difference(_lastPainterCreateTime!);
      if (timeSinceLastCreate < _minPainterCreateInterval) {
        Logger.flow(_logTag, 'setParameter', '⚠️ 防止频繁切换参数: 在${timeSinceLastCreate.inMilliseconds}毫秒内尝试重复切换参数，已跳过');
        return;
      }
    }
    
    Logger.flowStart(_logTag, 'setParameter');
    
    // 如果参数名称没有变化，直接返回
    if (_parameterName == parameterName) {
      Logger.flow(_logTag, 'setParameter', '参数未变化，保持当前参数: $parameterName');
      Logger.flowEnd(_logTag, 'setParameter');
      return;
    }
    
    // 记录参数变化
    Logger.flow(_logTag, 'setParameter', '🔄 ${_parameterName} -> $parameterName');
    
    // 记录当前累积状态，确保在参数切换时不丢失
    Logger.flow(_logTag, 'setParameter', '📷 参数切换前累积状态: ${_accumulatedImage != null ? "图像✅" : "图像❌"} ${_accumulatedFeaturePoints != null ? "特征点✅(${_accumulatedFeaturePoints!.length}个)" : "特征点❌"}');
    
    // 保存当前状态作为累积基础
    Logger.flow(_logTag, 'setParameter', '💾 保存当前状态作为累积基础');
    
    // 输出当前参数值管理器中的所有参数值，用于调试
    Logger.i(_logTag, '❗ 参数切换前的参数值管理器状态:');
    _parameterValueManager.logAllParameters();

    // 记录当前参数值，用于日志输出
    final oldParamValue = _paramValue;

    // 【修复】直接使用参数名，不包含区域前缀
    final fullParamName = parameterName;
    Logger.flow(_logTag, 'setParameter', '🔍 尝试从参数值管理器中获取参数值，参数名: $fullParamName');
    
    // 记录参数值管理器中的当前值
    final hasExistingValue = _parameterValueManager.containsParameter(fullParamName);
    final existingValue = hasExistingValue ? _parameterValueManager.getValue(fullParamName) : 0.0;
    
    if (hasExistingValue) {
      // 如果参数值管理器中存在值，使用该值作为当前参数值
      _paramValue = existingValue;
      Logger.i(_logTag, '❗ 切换到参数 $fullParamName，使用历史值: $existingValue');
      Logger.flow(_logTag, 'setParameter', '📊 使用参数值管理器中的值: $existingValue');
      // 重要：不要从参数值管理器中移除该参数，确保累积变形效果
      Logger.flow(_logTag, 'setParameter', '✅ 保留参数值管理器中的值，确保累积变形效果');
    } else {
      // 如果参数值管理器中不存在值，明确设置为默认值0.0
      _paramValue = 0.0; // 明确设置为0.0，避免继承前一个参数的值
      Logger.i(_logTag, '❗ 切换到参数 $fullParamName，没有历史值，使用默认值0.0');
      Logger.flow(_logTag, 'setParameter', '🔄 参数项 "$fullParamName" 没有历史值，使用默认值0.0');
      
      // 重要：将新参数的默认值0.0添加到参数值管理器中
      // 这样可以确保在切换参数时，新参数的值被正确设置为0.0
      _parameterValueManager.setValue(fullParamName, 0.0);
      Logger.flow(_logTag, 'setParameter', '📊 将新参数的默认值0.0添加到参数值管理器中: $fullParamName = 0.0');
    }
    
    // 重要：确保在参数切换时保留累积图像和特征点
    if (_accumulatedImage == null || _accumulatedFeaturePoints == null || _accumulatedFeaturePoints!.isEmpty) {
      Logger.flowError(_logTag, 'setParameter', '❌ [错误] 参数切换前的累积状态为空，无法继续执行');
      throw Exception('参数切换前的累积状态为空，无法继续执行');
    }
    
    // 更新参数名称和相关设置
    _parameterName = parameterName;
    _featurePointManager.setAreaType(_areaType, updateIndexes: false);
    _featurePointManager.setParameterName(_parameterName, updateIndexes: false);
    _featurePointManager.updateCurrentParameterPointIndexes();
    
    // 重要：在参数切换时，直接使用DeformationCacheManager中的最新变形状态
    // 不需要查找缓存，因为我们希望保留当前的累积变形状态
    ui.Image? latestDeformedImage = DeformationCacheManager.getLatestDeformedImage();
    List<FeaturePoint>? latestDeformedFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
    
    if (latestDeformedImage != null && latestDeformedFeaturePoints != null && latestDeformedFeaturePoints.isNotEmpty) {
      // 使用最新的变形状态作为累积基础
      _accumulatedImage = latestDeformedImage;
      _accumulatedFeaturePoints = List<FeaturePoint>.from(latestDeformedFeaturePoints);
      Logger.flow(_logTag, 'setParameter', '✅ 参数切换：使用最新的变形状态作为累积基础');
      Logger.flow(_logTag, 'setParameter', '📷 最新变形图像信息: 哈希码=${latestDeformedImage.hashCode}, 尺寸=${latestDeformedImage.width}x${latestDeformedImage.height}');
      
      // 确保特征点管理器使用最新的特征点数据
      _featurePointManager.updateFeaturePoints(_accumulatedFeaturePoints!);
      Logger.flow(_logTag, 'setParameter', '👁️ 更新特征点管理器数据: ${_accumulatedFeaturePoints!.length} 个点');
    } else {
      // 如果没有最新的变形状态，直接报错退出
      Logger.flowError(_logTag, 'setParameter', '❌ [错误] 参数切换时没有最新的变形状态，无法继续执行');
      throw Exception('参数切换时没有最新的变形状态，无法继续执行');
    }
    
    // ===== 开始特征点显示和呼吸效果重置流程 =====
    Logger.flow(_logTag, 'setParameter', '🔍 开始特征点显示和呼吸效果重置流程');
    
    // 1. 首先确保特征点可见
    _showFeaturePoints = true;
    Logger.flow(_logTag, 'setParameter', '👁️ 设置特征点可见: $_showFeaturePoints');
    
    // 2. 启用呼吸动画
    if (_breathingController != null) {
      _breathingController!.setEnableBreathingAnimation(true);
      Logger.flow(_logTag, 'setParameter', '🌬️ 启用呼吸动画');
    }
    
    // 5. 更新特征点管理器，确保特征点正确显示
    // 重要：使用累积特征点数据，而不是重新获取特征点
    if (_accumulatedFeaturePoints != null && _accumulatedFeaturePoints!.isNotEmpty) {
      _featurePointManager.updateFeaturePoints(_accumulatedFeaturePoints!);
      _featurePointManager.setAreaType(_areaType, updateIndexes: true);
      _featurePointManager.setParameterName(_parameterName, updateIndexes: true);
      Logger.flow(_logTag, 'setParameter', '👁️ 使用累积特征点更新特征点管理器: ${_accumulatedFeaturePoints!.length} 个点');
    } else {
      Logger.flowError(_logTag, 'setParameter', '❌ [错误] 累积特征点数据为空，无法更新特征点管理器');
      throw Exception('累积特征点数据为空，无法继续执行');
    }
    
    // 6. 无效化缓存的绘制器，确保创建新的绘制器
    _invalidateCachedPainter();
    Logger.flow(_logTag, 'setParameter', '🎨 无效化缓存的绘制器');
    
    // 7. 强制重绘，确保变形区域正确显示
    forceRepaint();
    Logger.flow(_logTag, 'setParameter', '🖌️ 强制重绘变形区域');
    
    // 8. 立即通知监听器，确保呼吸动画效果正确显示
    // 直接触发UI更新，不使用延迟
    notifyListeners();
    Logger.flow(_logTag, 'setParameter', '🔄 立即触发重绘，更新特征点显示');
    
    Logger.flow(_logTag, 'setParameter', '✅ 特征点显示和呼吸效果重置流程完成');
    // ===== 结束特征点显示和呼吸效果重置流程 =====
    
    // 记录参数切换后的累积状态
    Logger.flow(_logTag, 'setParameter', '📷 参数切换后累积状态: ${_accumulatedImage != null ? "图像✅" : "图像❌"} ${_accumulatedFeaturePoints != null ? "特征点✅(${_accumulatedFeaturePoints!.length}个)" : "特征点❌"}');
    
    // 设置变形类型，但不触发额外操作
    setTransformTypeByParameter(_areaType, parameterName);
    
    // 直接设置参数名称，避免调用setParameterName可能导致的参数值覆盖
    // 注意：这里不调用setParameterName，而是直接设置参数名称和更新特征点管理器
    
    // 重要：在更新参数名称之前，先保存旧参数名称，用于日志输出
    final oldParameterName = _parameterName;
    
    // 更新参数名称
    _parameterName = parameterName;
    
    // 更新特征点管理器
    _featurePointManager.setAreaType(_areaType, updateIndexes: false);
    _featurePointManager.setParameterName(_parameterName, updateIndexes: false);
    _featurePointManager.updateCurrentParameterPointIndexes();
    
    // 设置标志，表示当前不是仅更新参数名称模式
    // 已移除_isNameOnlyUpdate标志
    
    // 参数值已经在前面设置过了，这里不需要重复设置
    // 记录参数切换信息，用于日志输出
    Logger.i(_logTag, '❗ 参数切换: $oldParameterName -> $parameterName，当前参数值: $_paramValue');
    Logger.flow(_logTag, 'setParameter', '📊 参数切换完成，当前参数值: $_paramValue');
    
    // 更新最后创建时间
    _lastPainterCreateTime = now;
    Logger.flowEnd(_logTag, 'setParameterName');
    return;
  }

  /// 设置参数名称
  void setParameterName(String parameterName) {
    Logger.flowStart(_logTag, 'setParameterName');
    Logger.flow(_logTag, 'setParameterName', '🔹 开始更新参数名称: $parameterName');

    // 如果参数名称没有变化，直接返回
    if (_parameterName == parameterName) {
      Logger.flow(_logTag, 'setParameterName', '参数未变化，保持当前名称: $parameterName');
      Logger.flowEnd(_logTag, 'setParameterName');
      return;
    }

    // 记录参数名称变化
    Logger.flow(_logTag, 'setParameterName', '🔄 参数切换: ${_parameterName} -> $parameterName');

    // 参数切换需要完整准备变形状态
    Logger.flow(_logTag, 'setParameterName', '🟢 参数切换并完整准备变形状态');

    // 记录当前累积状态，确保在参数切换时不丢失
    Logger.flow(_logTag, 'setParameterName', '📷 参数切换前累积状态: ${_accumulatedImage != null ? "图像✅" : "图像❌"} ${_accumulatedFeaturePoints != null ? "特征点✅(${_accumulatedFeaturePoints!.length}个)" : "特征点❌"}');
    
    // 保存当前累积图像，用于特征点校准
    final currentImage = _accumulatedImage;
    if (currentImage != null) {
      // 触发特征点校准（异步调用，不等待结果）
      Logger.flow(_logTag, 'setParameterName', '🔍 参数切换，触发特征点校准');
      _calibrateFeaturePointsOnParameterChange(currentImage).then((_) {
        Logger.flow(_logTag, 'setParameterName', '✅ 特征点校准完成后的回调');
        // 校准完成后，无效化缓存的绘制器并通知监听器
        _invalidateCachedPainter();
        notifyListeners();
      }).catchError((error) {
        Logger.flowError(_logTag, 'setParameterName', '❌ 特征点校准过程中发生错误: $error');
      });
    }
    
    // 更新参数名称
    _parameterName = parameterName;

    // 更新特征点管理器中的参数名称，但不更新索引
    _featurePointManager.setAreaType(_areaType, updateIndexes: false);
    _featurePointManager.setParameterName(_parameterName, updateIndexes: false);
    // 手动更新当前参数的特征点索引
    _featurePointManager.updateCurrentParameterPointIndexes();

    // 【修复】从参数值管理器中恢复参数值，直接使用参数名
    final fullParamName = _parameterName;
    Logger.flow(_logTag, 'setParameterName', '🔍 尝试从参数值管理器中获取参数值，参数名: $fullParamName');
    
    if (_parameterValueManager.containsParameter(fullParamName)) {
      _paramValue = _parameterValueManager.getValue(fullParamName);
      Logger.flow(_logTag, 'setParameterName', '📊 从参数值管理器恢复参数值: $_paramValue');
      Logger.flow(_logTag, 'setParameterName', '🔍 参数值来源: 参数值管理器中的历史值');
      
      // 输出当前参数值管理器中的所有参数值，用于调试
      Logger.flow(_logTag, 'setParameterName', '📋 当前参数值管理器状态:');
      _parameterValueManager.logAllParameters();
    } else {
      // 如果参数值管理器中没有该参数，重置参数值为0
      _paramValue = 0.0;
      Logger.flow(_logTag, 'setParameterName', '📊 重置参数值为0，参数项 "$fullParamName" 没有历史值');
      Logger.flow(_logTag, 'setParameterName', '🔍 参数值来源: 默认值0.0（参数值管理器中不存在该参数）');
    }

    // 根据区域类型和参数名称设置变形类型
    setTransformTypeByParameter(_areaType, parameterName);
    
    // 无效化缓存的绘制器，确保创建新的绘制器
    _invalidateCachedPainter();

    // 通知监听器更新UI
    notifyListeners();

    // 记录参数切换后的累积状态
    Logger.flow(_logTag, 'setParameterName', '📷 参数切换后累积状态: ${_accumulatedImage != null ? "图像✅" : "图像❌"} ${_accumulatedFeaturePoints != null ? "特征点✅(${_accumulatedFeaturePoints!.length}个)" : "特征点❌"}');

    Logger.flowEnd(_logTag, 'setParameterName');
  }

  /// 设置区域类型
  void setArea(String area) {
    Logger.flowStart(_logTag, 'setArea');
  
    // 如果区域类型没有变化，直接返回
    if (_areaType == area) {
      Logger.flow(_logTag, 'setArea', '区域未变化，保持当前区域: $area');
      Logger.flowEnd(_logTag, 'setArea');
      return;
    }
  
    // 记录区域变化
    final oldAreaType = _areaType;
    Logger.flow(_logTag, 'setArea', '🔄 区域切换: $oldAreaType -> $area');
  
    // 记录当前累积状态，确保在区域切换时不丢失
    Logger.flow(_logTag, 'setArea', '📷 区域切换前累积状态: ${_accumulatedImage != null ? "图像✅" : "图像❌"} ${_accumulatedFeaturePoints != null ? "特征点✅(${_accumulatedFeaturePoints!.length}个)" : "特征点❌"}');
  
    // 更新区域类型
    _areaType = area;
  
    // 更新特征点管理器中的区域类型
    _featurePointManager.setAreaType(_areaType);
  
    // 根据区域类型和参数名称设置变形类型
    setTransformTypeByParameter(area, _parameterName);
  
    // 无效化缓存的绘制器，确保创建新的绘制器
    _invalidateCachedPainter();
  
    // 通知监听器
    notifyListeners();
  
    // 记录区域切换后的累积状态
    Logger.flow(_logTag, 'setArea', '📷 区域切换后累积状态: ${_accumulatedImage != null ? "图像✅" : "图像❌"} ${_accumulatedFeaturePoints != null ? "特征点✅(${_accumulatedFeaturePoints!.length}个)" : "特征点❌"}');
  
    Logger.flowEnd(_logTag, 'setArea');
  }

  /// 设置可见性
  void setVisible(bool visible) {
    Logger.flowStart(_logTag, 'setVisible');
  
    // 如果可见性没有变化，直接返回
    if (_isVisible == visible) {
      Logger.flow(_logTag, 'setVisible', '可见性未变化，保持当前状态: $visible');
      Logger.flowEnd(_logTag, 'setVisible');
      return;
    }
  
    // 更新可见性
    _isVisible = visible;
    Logger.flow(_logTag, 'setVisible', '可见性已更新: $visible');
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'setVisible');
  }

  /// 设置是否显示变形区域
  void setShowDeformationArea(bool show) {
    Logger.flowStart(_logTag, 'setShowDeformationArea');
  
    // 如果显示状态没有变化，直接返回
    if (_showDeformationArea == show) {
      Logger.flow(_logTag, 'setShowDeformationArea', '显示状态未变化，保持当前状态: $show');
      Logger.flowEnd(_logTag, 'setShowDeformationArea');
      return;
    }
  
    // 更新显示状态
    _showDeformationArea = show;
    Logger.flow(_logTag, 'setShowDeformationArea', '显示状态已更新: $show');
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'setShowDeformationArea');
  }

  /// 设置是否显示坐标系
  void setShowCoordinateSystem(bool show) {
    Logger.flowStart(_logTag, 'setShowCoordinateSystem');
  
    // 如果显示状态没有变化，直接返回
    if (_showCoordinateSystem == show) {
      Logger.flow(_logTag, 'setShowCoordinateSystem', '显示状态未变化，保持当前状态: $show');
      Logger.flowEnd(_logTag, 'setShowCoordinateSystem');
      return;
    }
  
    // 更新显示状态
    _showCoordinateSystem = show;
    Logger.flow(_logTag, 'setShowCoordinateSystem', '显示状态已更新: $show');
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'setShowCoordinateSystem');
  }

  /// 直接设置变形类型
  void setTransformTypeDirectly(TransformType type) {
    Logger.flowStart(_logTag, 'setTransformTypeDirectly');
  
    // 如果变形类型没有变化，直接返回
    if (_transformType == type) {
      Logger.flow(_logTag, 'setTransformTypeDirectly', '变形类型未变化，保持当前类型: $type');
      Logger.flowEnd(_logTag, 'setTransformTypeDirectly');
      return;
    }
  
    // 更新变形类型
    _transformType = type;
    Logger.flow(_logTag, 'setTransformTypeDirectly', '变形类型已直接更新: $type');
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'setTransformTypeDirectly');
  }

  /// 更新特征点
  void updateFeaturePoints(List<FeaturePoint> featurePoints) {
    Logger.flowStart(_logTag, 'updateFeaturePoints');
    Logger.flow(_logTag, 'updateFeaturePoints', '更新特征点数据: ${featurePoints.length}个点');
    Logger.flowError(_logTag, 'updateFeaturePoints', '🔍🔍🔍 进入updateFeaturePoints方法 - 准备检查和创建zero_state缓存 🔍🔍🔍');
  
    if (_featurePointManager != null) {
      _featurePointManager.updateFeaturePoints(featurePoints);
      Logger.flow(_logTag, 'updateFeaturePoints', '特征点数据已更新');

      // 将原始图像和特征点数据缓存到"zero_state"中 - 这是唯一创建zero_state缓存的地方
      if (_image != null) {
        Logger.flowError(_logTag, 'updateFeaturePoints', '🔄🔄🔄 开始创建zero_state缓存检查流程 - 图像有效(尺寸: ${_image!.width}x${_image!.height}) 🔄🔄🔄');
        Logger.flowError(_logTag, 'updateFeaturePoints', '📋 开始创建zero_state缓存 - 这是唯一允许的创建点');

        // 创建零状态参数映射
        final zeroStateParams = {'zero_state': 0.0};

        // 检查是否已存在zero_state缓存
        final existingCache = _cacheManager.find(zeroStateParams);
        Logger.flowError(_logTag, 'updateFeaturePoints', '🔍 检查zero_state缓存: ${existingCache != null ? "已存在" : "不存在"}');
        if (existingCache != null) {
          Logger.flowError(_logTag, 'updateFeaturePoints', '⚠️⚠️⚠️ zero_state缓存已存在，不重复创建 ⚠️⚠️⚠️');
          Logger.flowError(_logTag, 'updateFeaturePoints', '⚠️ 现有zero_state缓存图像尺寸: ${existingCache.deformedImage?.width ?? 0}x${existingCache.deformedImage?.height ?? 0}');
        } else {
          // 保存零状态缓存
          Logger.flowError(_logTag, 'updateFeaturePoints', '💾 正在创建zero_state缓存... 图像尺寸: ${_image!.width}x${_image!.height}, 特征点数: ${featurePoints.length}');  
          _cacheManager.save(
            zeroStateParams, 
            _image!, 
            featurePoints,
            facialCenterLineX: _cachedFacialCenterLineX,
            facialCenterLineCalculated: _facialCenterLineCalculated
          );
          Logger.flowError(_logTag, 'updateFeaturePoints', '💾 zero_state缓存保存操作完成，准备验证');
        
          // 验证缓存是否创建成功
          final verifyCache = _cacheManager.find(zeroStateParams);
          if (verifyCache != null) {
            Logger.flowError(_logTag, 'updateFeaturePoints', '✅✅✅ ZERO_STATE缓存创建成功! 图像尺寸: ${verifyCache.deformedImage?.width ?? 0}x${verifyCache.deformedImage?.height ?? 0}, 特征点数: ${verifyCache.deformedFeaturePoints?.length ?? 0} ✅✅✅');
            // 打印到控制台确保可见
            print('✅✅✅ ZERO_STATE缓存创建成功! 图像尺寸: ${verifyCache.deformedImage?.width ?? 0}x${verifyCache.deformedImage?.height ?? 0}, 特征点数: ${verifyCache.deformedFeaturePoints?.length ?? 0} ✅✅✅');
          } else {
            Logger.flowError(_logTag, 'updateFeaturePoints', '❌❌❌ ZERO_STATE缓存创建失败 ❌❌❌');
            print('❌❌❌ ZERO_STATE缓存创建失败 ❌❌❌');
            throw Exception('ZERO_STATE缓存创建失败，这是关键错误');
          }
        }
        
        // 将原始图像和特征点数据设置为DeformationCacheManager的最新状态
        DeformationCacheManager.setLatestDeformedState(_image!, featurePoints);
        Logger.flow(_logTag, 'updateFeaturePoints', '✅ 原始图像和特征点数据已设置为DeformationCacheManager的最新状态');
      } else {
        Logger.flowWarning(_logTag, 'updateFeaturePoints', '特征点数据为空，无法缓存原始图像');
      }
    } else {
      Logger.flowError(_logTag, 'updateFeaturePoints', '特征点管理器为空，无法更新特征点');
    }
  
    Logger.flowError(_logTag, 'updateFeaturePoints', '🏁🏁🏁 updateFeaturePoints方法执行完毕 🏁🏁🏁');
    Logger.flowEnd(_logTag, 'updateFeaturePoints');
  }

  /// 更新侧面特征点
  void updateSideFeaturePoints(List<FeaturePoint> sideFeaturePoints) {
    Logger.flowStart(_logTag, 'updateSideFeaturePoints');
    Logger.flow(_logTag, 'updateSideFeaturePoints', '更新侧面特征点数据: ${sideFeaturePoints.length}个点');
  
    if (_featurePointManager != null) {
      _featurePointManager.updateSideFeaturePoints(sideFeaturePoints);
      Logger.flow(_logTag, 'updateSideFeaturePoints', '侧面特征点数据已更新');
  
      // 通知监听器
      notifyListeners();
    } else {
      Logger.flowError(_logTag, 'updateSideFeaturePoints', '特征点管理器为空，无法更新侧面特征点');
    }
  
    Logger.flowEnd(_logTag, 'updateSideFeaturePoints');
  }

  /// 设置图像
  void setImage(ui.Image? image) {
    Logger.flowStart(_logTag, 'setImage');
  
    if (image == null) {
      Logger.flowWarning(_logTag, 'setImage', '图像为空，无法设置');
      Logger.flowEnd(_logTag, 'setImage');
      return;
    }
  
    // 设置图像
    _image = image;
    Logger.flow(_logTag, 'setImage', '图像已设置');
  
    // 更新图像尺寸
    _imageSize = Size(image.width.toDouble(), image.height.toDouble());
    Logger.flow(_logTag, 'setImage', '图像尺寸已更新: $_imageSize');
  
    // 清除缓存
    _facialCenterLineCalculated = false;
    _cachedFacialCenterLineX = null;
    Logger.flow(_logTag, 'setImage', '面部中心线缓存已清除');
  
    // 清除参数值管理器中的所有参数值
    _parameterValueManager.clearAllParameters();
    Logger.flow(_logTag, 'setImage', '✅ 参数值管理器中的所有参数值已清除');
    
    // 【单例模式】重置绘制器单例，因为图像已重新导入
    // 不再使用单例模式，直接重置变形状态
    // SimpleDeformationPainter.resetInstance();
    Logger.flow(_logTag, 'setImage', '✅ 绘制器单例已重置');
  
    // 注意：zero_state缓存仅在updateFeaturePoints方法中创建，此处不再创建
  if (_featurePointManager != null) {
    final featurePoints = _featurePointManager.getFeaturePoints();
    if (featurePoints.isNotEmpty) {
      Logger.flow(_logTag, 'setImage', '设置图像：已有特征点，但zero_state缓存仅在updateFeaturePoints方法中创建');
      
      // 将原始图像和特征点数据设置为DeformationCacheManager的最新状态
      // 确保在第一次变形之前，全局缓存管理器中已经有了有效的初始数据
      DeformationCacheManager.setLatestDeformedState(image, featurePoints);
      Logger.flow(_logTag, 'setImage', '✅ 原始图像和特征点数据已设置为DeformationCacheManager的最新状态');
      } else {
        Logger.flowWarning(_logTag, 'setImage', '特征点数据为空，无法缓存原始图像');
      }
    }
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'setImage');
  }
  
  /// 设置侧面图像
  void setSideImage(ui.Image sideImage) {
    Logger.flowStart(_logTag, 'setSideImage');
  
    // 设置侧面图像
    _sideImage = sideImage;
    Logger.flow(_logTag, 'setSideImage', '侧面图像已设置');
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'setSideImage');
  }

  /// 设置图像尺寸
  void setImageSize(Size size) {
    Logger.flowStart(_logTag, 'setImageSize');
  
    // 如果图像尺寸没有变化，直接返回
    if (_imageSize == size) {
      Logger.flow(_logTag, 'setImageSize', '图像尺寸未变化，保持当前尺寸: $size');
      Logger.flowEnd(_logTag, 'setImageSize');
      return;
    }
  
    // 更新图像尺寸
    _imageSize = size;
    Logger.flow(_logTag, 'setImageSize', '图像尺寸已更新: $size');
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'setImageSize');
  }

  /// 设置是否显示特征点
  void setShowFeaturePoints(bool show) {
    // 强制禁用特征点显示，始终返回false，使用LandmarksOverlay显示特征点
    if (_showFeaturePoints != false) {
      Logger.flow(_logTag, 'setShowFeaturePoints', '强制禁用特征点显示，使用LandmarksOverlay显示特征点');
      _showFeaturePoints = false;
      notifyListeners();
    }
  }
  
  /// 获取是否显示特征点
  bool getShowFeaturePoints() {
    return _showFeaturePoints;
  }
  
  /// 检查是否显示特征点（与 getShowFeaturePoints 功能相同）
  bool isShowingFeaturePoints() {
    Logger.flow(_logTag, 'isShowingFeaturePoints', '特征点显示状态: $_showFeaturePoints');
    return _showFeaturePoints;
  }
  
  /// 获取缓存管理器实例
  DeformationCacheManager getCacheManager() {
    return _cacheManager;
  }
  
  /// 获取图像状态信息，仅用于诊断目的
  Map<String, dynamic> getImageStatus() {
    return {
      'hasImage': _image != null,
      'width': _image?.width ?? 0,
      'height': _image?.height ?? 0
    };
  }
  
  /// 属性访问器
  String get areaType => _areaType;
  String get parameterName => _parameterName;
  double get parameterValue => _paramValue;
  bool get isVisible => _isVisible;
  
  /// 是否显示变形区域可视化
  bool get showDeformationArea => _showDeformationArea;
  
  /// 初始化渲染器
  Future<void> initialize() async {
    Logger.flowStart(_logTag, 'initialize');
  
    // 标记为已初始化
    _initialized = true;
    Logger.flow(_logTag, 'initialize', '渲染器已初始化');
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'initialize');
  }
  
  /// 重绘
  void repaint() {
    Logger.flowStart(_logTag, 'repaint');
  
    // 无效化缓存的绘制器
    _invalidateCachedPainter();
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'repaint');
  }
  
  /// 【单例模式】无效化缓存的绘制器 - 不再需要，因为绘制器是单例
  void _invalidateCachedPainter() {
    // _cachedPainter = null;  // 不再需要，因为绘制器是单例
    Logger.flow(_logTag, '_invalidateCachedPainter', '🔄 绘制器单例模式下无需无效化缓存');
  }
  
  /// 强制重绘变形区域
  void forceRepaint() {
    Logger.flowStart(_logTag, 'forceRepaint');
  
    // 无效化缓存的绘制器
    _invalidateCachedPainter();
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'forceRepaint');
  }
  
  /// 根据区域类型和参数名称设置变形类型
  void setTransformTypeByParameter(String areaType, String parameterName) {
    Logger.flowStart(_logTag, 'setTransformTypeByParameter');
  
    // 检查变形工厂是否有对应的变形策略
    final transformationFactory = TransformationFactory();
    final hasStrategy = transformationFactory.hasStrategy(parameterName);
    
    if (hasStrategy) {
      // 获取变形策略
      final strategy = transformationFactory.getStrategy(parameterName);
      
      if (strategy != null) {
        // 使用变形策略的getRequiredTransformType方法自动获取变形类型
        final requiredType = strategy.getRequiredTransformType();
        _transformType = requiredType;
        
        Logger.flow(_logTag, 'setTransformTypeByParameter', '✅ 使用变形策略自动设置变形类型: $parameterName -> ${_transformType.toString()}');
      } else {
        // 策略为空，使用默认变形类型
        _transformType = TransformType.default_;
        Logger.flow(_logTag, 'setTransformTypeByParameter', '❌ 变形策略为空，使用默认变形类型: $parameterName -> ${_transformType.toString()}');
      }
    } else {
      // 没有对应的变形策略，使用默认变形类型
      _transformType = TransformType.default_;
      Logger.flow(_logTag, 'setTransformTypeByParameter', '❌ 区域 $areaType 的参数 $parameterName 未实现变形策略，点击按钮不会执行任何代码');
    }

    Logger.flow(_logTag, 'setTransformTypeByParameter', '变形类型已设置: $_transformType');
  
    // 通知监听器
    notifyListeners();
  
    Logger.flowEnd(_logTag, 'setTransformTypeByParameter');
  }
  
  /// 清除缓存
  /// 参数 onlyWhenImageChanged 表示是否只在图像变化时才清除累积状态
  /// 默认为 true，表示只有在图像重新导入时才清除累积状态
  void clearCache({bool onlyWhenImageChanged = true}) {
    Logger.flowStart(_logTag, 'clearCache');
    Logger.flow(_logTag, 'clearCache', '⚠️ 缓存清除操作已禁用，仅在图像重新导入时才允许清除缓存');
    
    // 仅在图像重新导入时重置累积状态
    if (onlyWhenImageChanged && _accumulatedImage == null) {
      Logger.flow(_logTag, 'clearCache', '图像已重新导入，重置累积状态');
      _accumulatedImage = null;
      _accumulatedFeaturePoints = null;
    }
    
    // 通知监听器
    notifyListeners();
    
    Logger.flowEnd(_logTag, 'clearCache');
  }
  
  /// 处理变形结果
  void _onDeformationResult(ui.Image? deformedImage, List<FeaturePoint>? deformedFeaturePoints) {
    Logger.flowStart(_logTag, '_onDeformationResult');
    Logger.flow(_logTag, '_onDeformationResult', '✅ 收到变形结果');
    
    // 检查变形图像
    if (deformedImage == null) {
      Logger.flowError(_logTag, '_onDeformationResult', '❌ 变形图像为空');
      Logger.flowEnd(_logTag, '_onDeformationResult');
      return;
    }
    
    if (deformedImage != null) {
      _accumulatedImage = deformedImage;
      Logger.flow(_logTag, '_onDeformationResult', '📷 更新累积变形图像: 哈希码=${deformedImage.hashCode}');
      
      // 记录变形图像信息
      DeformationCacheManager.logDeformedImageInfo(deformedImage, '新变形结果图像');
    }
    
    // 检查变形特征点
    if (deformedFeaturePoints == null || deformedFeaturePoints.isEmpty) {
      Logger.flowError(_logTag, '_onDeformationResult', '❌ 变形特征点为空，无法继续变形');
      Logger.flowEnd(_logTag, '_onDeformationResult');
      return; // 特征点数据为空，无法继续变形
    } else {
      Logger.flow(_logTag, '_onDeformationResult', '✅ 变形特征点: ${deformedFeaturePoints.length}个点');
      
      // 更新累积特征点数据，确保与变形图像一致
      _accumulatedFeaturePoints = List<FeaturePoint>.from(deformedFeaturePoints);
      
      // 确保特征点管理器使用最新的特征点数据
      _featurePointManager.updateFeaturePoints(deformedFeaturePoints);
      
      // 更新当前参数对应的特征点索引，确保呼吸动效显示正确的特征点
      _featurePointManager.updateCurrentParameterPointIndexes();
      Logger.flow(_logTag, '_onDeformationResult', '✅ 更新特征点管理器和当前参数特征点索引');
    }
    
    // 更新缓存管理器中的最新变形状态
    DeformationCacheManager.setLatestDeformedState(deformedImage, deformedFeaturePoints);
    
    // 在变形完成后，使用最新的变形图像重新校准特征点
    _recalibrateFeaturePointsAfterDeformation(deformedImage);
    
    // 【优化】减少不必要的UI通知，避免频繁重建
    // 只在参数实际变化时才通知UI更新，避免因变形结果回调导致的无限重建
    // 使用SchedulerBinding.instance.addPostFrameCallback确保UI更新在当前帧渲染完成后进行
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // 防抖机制：限制通知频率，每100ms最多通知一次
      final now = DateTime.now();
      if (now.difference(_lastNotifyTime).inMilliseconds > 100) {
        notifyListeners();
        _lastNotifyTime = now;
      }
    });
    
    Logger.flowEnd(_logTag, '_onDeformationResult');
  }

  /// 在变形完成后重新校准特征点
  Future<void> _recalibrateFeaturePointsAfterDeformation(ui.Image deformedImage) async {
    Logger.flowStart(_logTag, '_recalibrateFeaturePointsAfterDeformation');
    Logger.flow(_logTag, '_recalibrateFeaturePointsAfterDeformation', '▶️ 变形后特征点校准开始');
    
    try {
      // 步骤1: 特征点识别
      Logger.flow(_logTag, '_recalibrateFeaturePointsAfterDeformation', '1️⃣ 识别变形图像特征点');
      final transformationService = TransformationService.instance;
      final recognizedPoints = await transformationService.recognizeFeaturePoints(deformedImage);
      
      if (recognizedPoints == null || recognizedPoints.isEmpty) {
        Logger.flowError(_logTag, '_recalibrateFeaturePointsAfterDeformation', '❌ 特征点识别失败或结果为空');
        Logger.flowEnd(_logTag, '_recalibrateFeaturePointsAfterDeformation');
        return;
      }
      
      Logger.flow(_logTag, '_recalibrateFeaturePointsAfterDeformation', '✓ 识别成功: ${recognizedPoints.length}个特征点');
      
      // 步骤2: 特征点对比分析
      final currentFeaturePoints = _featurePointManager.getFeaturePoints();
      final pointCountDiff = recognizedPoints.length - (currentFeaturePoints?.length ?? 0);
      Logger.flow(_logTag, '_recalibrateFeaturePointsAfterDeformation', '2️⃣ 特征点对比: 原${currentFeaturePoints?.length ?? 0}个 → 新${recognizedPoints.length}个 (${pointCountDiff > 0 ? "+$pointCountDiff" : pointCountDiff})');
      
      // 步骤3: 特征点数据更新
      Logger.flow(_logTag, '_recalibrateFeaturePointsAfterDeformation', '3️⃣ 更新特征点数据');
      updateAccumulatedFeaturePoints(recognizedPoints);
      _featurePointManager.updateFeaturePoints(recognizedPoints);
      
      // 更新当前参数对应的特征点索引
      _featurePointManager.updateCurrentParameterPointIndexes();
      
      // 确保特征点显示被设置为 true
      setShowFeaturePoints(true);
      
      // 确保呼吸动画已启用
      if (_breathingController != null) {
        _breathingController!.setEnableBreathingAnimation(true);
      }
      
      Logger.flow(_logTag, '_recalibrateFeaturePointsAfterDeformation', '✓ 特征点数据和索引更新完成，并启用特征点显示和呼吸动画');
      
      // 步骤4: UI刷新
      Logger.flow(_logTag, '_recalibrateFeaturePointsAfterDeformation', '4️⃣ 刷新UI显示');
      notifyListeners();
      
      Logger.flow(_logTag, '_recalibrateFeaturePointsAfterDeformation', '✅ 变形后特征点校准完成');
    } catch (e) {
      Logger.flowError(_logTag, '_recalibrateFeaturePointsAfterDeformation', '❌ 特征点校准过程中发生异常: $e');
    }
    
    Logger.flowEnd(_logTag, '_recalibrateFeaturePointsAfterDeformation');
  }
  
  /// 比较两个列表是否相等
  bool _compareListsEqual(List<int>? list1, List<int>? list2) {
    if (list1 == null && list2 == null) {
      return true;
    }
  
    if (list1 == null || list2 == null) {
      return false;
    }
  
    if (list1.length != list2.length) {
      return false;
    }
  
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) {
        return false;
      }
    }
  
    return true;
  }
  
  /// 将特征点保存到缓存
  void _saveFeaturePointsToCache(List<FeaturePoint> featurePoints, double parameterValue) {
    Logger.flowStart(_logTag, '_saveFeaturePointsToCache');
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '🔍 [开始] 保存特征点到缓存');
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '特征点数量: ${featurePoints.length}, 参数值: $parameterValue');
  
    // 检查参数是否有效
    if (_areaType == null || _parameterName == null) {
      Logger.flowError(_logTag, '_saveFeaturePointsToCache', '❌ 区域类型或参数名称为空，无法保存到缓存');
      Logger.flowEnd(_logTag, '_saveFeaturePointsToCache');
      return;
    }
  
    // 检查特征点数据是否有效
    if (featurePoints.isEmpty) {
      Logger.flowError(_logTag, '_saveFeaturePointsToCache', '❌ 特征点数据为空，无法保存到缓存');
      Logger.flowEnd(_logTag, '_saveFeaturePointsToCache');
      return;
    }
  
    // 更新当前参数的值（确保参数值管理器是最新的）
    Map<String, double> allParams = _parameterValueManager.getAllParameters();
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '使用参数值管理器生成缓存键，参数数量: ${allParams.length}');
  
    // 检查累积图像是否为空
    if (_accumulatedImage == null) {
      Logger.flowError(_logTag, '_saveFeaturePointsToCache', '❌ 累积图像为空，无法保存到缓存');
      Logger.flowEnd(_logTag, '_saveFeaturePointsToCache');
      return;
    }
    
    // 更新累积特征点数据，确保与变形图像一致
    _accumulatedFeaturePoints = List<FeaturePoint>.from(featurePoints);
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '✅ 更新累积特征点数据，确保与变形图像一致');
    
    // 确保特征点管理器使用最新的特征点数据
    _featurePointManager.updateFeaturePoints(featurePoints);
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '✅ 特征点管理器已更新最新特征点数据');
    
    // 更新当前参数特征点索引，确保呼吸动效显示正确的特征点
    _featurePointManager.updateCurrentParameterPointIndexes();
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '✅ 当前参数特征点索引已更新');
  
    // 保存到缓存，使用参数值管理器的参数映射
    _cacheManager.save(
      allParams,
      _accumulatedImage!, // 使用非空断言，因为我们已经检查了_accumulatedImage不为空
      featurePoints,
      facialCenterLineX: _cachedFacialCenterLineX,
      facialCenterLineCalculated: _facialCenterLineCalculated
    );
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '✅ 变形结果已保存到缓存');
    
    // 更新共享变形状态，确保其他组件能够访问到最新的变形结果和特征点
    DeformationCacheManager.setLatestDeformedState(_accumulatedImage, _accumulatedFeaturePoints, isButtonClick: true);
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '✅ 共享变形状态已更新: 图像和特征点数据已同步');
  
    // 添加简洁的日志输出，显示参数项、特征点数量和缓存状态
    Logger.i(_logTag, '✅ 特征点可视化: 参数[$_areaType.$_parameterName=$parameterValue] 已更新 | 特征点[${featurePoints.length}个] | 缓存已更新');
  
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '特征点已保存到缓存，使用全量参数项');
    Logger.flow(_logTag, '_saveFeaturePointsToCache', '✅ [完成] 保存特征点到缓存');
    Logger.flowEnd(_logTag, '_saveFeaturePointsToCache');
  }
  
  /// 应用参数值到特征点
  /// 
  /// [参数值] 要应用的参数值
  /// [featurePoints] 特征点列表
  /// [pointIndexes] 特征点索引列表
  /// [value] 参数值
  void _applyParameterValueToFeaturePoints(List<FeaturePoint> featurePoints, List<int> pointIndexes, double value) {
    Logger.flowStart(_logTag, '_applyParameterValueToFeaturePoints');
    
    // 检查特征点和索引是否为空
    if (featurePoints.isEmpty || pointIndexes.isEmpty) {
      Logger.flowWarning(_logTag, '_applyParameterValueToFeaturePoints', '特征点或索引为空');
      Logger.flowEnd(_logTag, '_applyParameterValueToFeaturePoints');
      return;
    }
  
    Logger.flow(_logTag, '_applyParameterValueToFeaturePoints', '特征点数量: ${featurePoints.length}, 索引数量: ${pointIndexes.length}');
  
    // 获取变形策略
    final transformationFactory = TransformationFactory();
    final strategy = transformationFactory.getStrategy(_parameterName);
  
    // 如果没有对应的变形策略，记录日志并直接返回
    if (strategy == null) {
      Logger.flow(_logTag, '_applyParameterValueToFeaturePoints', '⚠️ 未找到参数 $_parameterName 的变形策略，跳过变形处理');
      Logger.flowEnd(_logTag, '_applyParameterValueToFeaturePoints');
      return;
    }
  
    // 如果没有累积特征点，初始化为当前特征点
    if (_accumulatedFeaturePoints == null) {
      _accumulatedFeaturePoints = List<FeaturePoint>.from(featurePoints);
      Logger.flow(_logTag, '_applyParameterValueToFeaturePoints', '初始化累积特征点');
    }
  
    // 使用策略应用变形
    Logger.flow(_logTag, '_applyParameterValueToFeaturePoints', '传递面部中心线给变形策略: $_cachedFacialCenterLineX (${_facialCenterLineCalculated ? "已缓存" : "未缓存"})');
  
    // 记录变形前的特征点状态（用于日志）
    if (_accumulatedFeaturePoints != null && _accumulatedFeaturePoints!.isNotEmpty && pointIndexes.isNotEmpty) {
      final sampleIndex = pointIndexes[0];
      if (sampleIndex < _accumulatedFeaturePoints!.length) {
        final beforePoint = _accumulatedFeaturePoints![sampleIndex];
        Logger.flow(_logTag, '_applyParameterValueToFeaturePoints', '变形前示例点 ID=${beforePoint.index} 坐标: (${beforePoint.x}, ${beforePoint.y})');
      }
    }
  
    // 应用策略进行变形 - 直接使用当前参数值，而不是增量
    // 这确保变形效果是基于当前滑块值的绝对值，而不仅仅是增量
    // 对鼻基抬高变形添加特殊日志
    if (_parameterName == 'base_height') {
      String direction = _isParameterIncreasing == true ? "增大" : (_isParameterIncreasing == false ? "减小" : "未知");
      Logger.i(_logTag, '📍 鼻基抬高变形 | 开始执行变形 | 参数值=$value | 变形方向=$direction');
    }
    
    // 【关键成功验证日志】鼻尖调整变形调用验证
    if (_parameterName == 'tip_adjust') {
      print('═══════════════════════════════════════════════════════════');
      print('🎯 【关键验证】SimpleDeformationRenderer鼻尖调整变形调用');
      print('   ✅ 渲染器状态: 运行正常');
      print('   ✅ 变形策略: ${strategy.runtimeType}');
      print('   ✅ 参数名称: $_parameterName');
      print('   ✅ 变形方向: ${_isParameterIncreasing == true ? "增大" : (_isParameterIncreasing == false ? "减小" : "未知")}');
      print('   ✅ 特征点数量: ${_accumulatedFeaturePoints?.length ?? 0}');
      print('   ✅ 索引数量: ${pointIndexes.length}');
      print('   ✅ 面部中心线: $_cachedFacialCenterLineX');
      print('   ✅ 即将调用: strategy.applyTransformation');
      print('═══════════════════════════════════════════════════════════');
    }
    
    // 【关键验证日志】嘴角上扬变形调用验证
    if (_parameterName == 'mouth_corner') {
      print('═══════════════════════════════════════════════════════════');
      print('🎯 【关键验证】SimpleDeformationRenderer嘴角上扬变形调用');
      print('   ✅ 渲染器状态: 运行正常');
      print('   ✅ 变形策略: ${strategy.runtimeType}');
      print('   ✅ 参数名称: $_parameterName');
      print('   ✅ 变形方向: ${_isParameterIncreasing == true ? "增大" : (_isParameterIncreasing == false ? "减小" : "未知")}');
      print('   ✅ 特征点数量: ${_accumulatedFeaturePoints?.length ?? 0}');
      print('   ✅ 索引数量: ${pointIndexes.length}');
      print('   ✅ 面部中心线: $_cachedFacialCenterLineX');
      print('   ✅ 即将调用: strategy.applyTransformation');
      print('═══════════════════════════════════════════════════════════');
    }
    
    strategy.applyTransformation(
      _accumulatedFeaturePoints!,
      pointIndexes,
      value,
      _intensity,
      facialCenterLineX: _cachedFacialCenterLineX,
      isIncreasing: _isParameterIncreasing
    );

    // 【关键成功验证日志】鼻尖调整变形完成验证
    if (_parameterName == 'tip_adjust') {
      print('═══════════════════════════════════════════════════════════');
      print('🎯 【关键验证】SimpleDeformationRenderer鼻尖调整变形完成');
      print('   ✅ 变形执行状态: 成功完成');
      print('   ✅ 策略调用: 已成功执行');
      print('   ✅ 特征点更新: 已完成');
      print('   ✅ 累积变形: 已应用');
      print('   ✅ 渲染器状态: 正常运行');
      print('   📊 执行结果: 鼻尖调整变形已成功应用到特征点');
      print('═══════════════════════════════════════════════════════════');
    }
  
    // 记录变形后的特征点状态（用于日志）
    if (_accumulatedFeaturePoints != null && _accumulatedFeaturePoints!.isNotEmpty && pointIndexes.isNotEmpty) {
      final sampleIndex = pointIndexes[0];
      if (sampleIndex < _accumulatedFeaturePoints!.length) {
        final afterPoint = _accumulatedFeaturePoints![sampleIndex];
        Logger.flow(_logTag, '_applyParameterValueToFeaturePoints', '变形后示例点 ID=${afterPoint.index} 坐标: (${afterPoint.x}, ${afterPoint.y})');
      }
    }
  
    // 更新特征点管理器中的特征点 - 使用累积后的特征点
    _featurePointManager.updateFeaturePoints(_accumulatedFeaturePoints!);
  
    // 不再在这里保存缓存，缓存保存已移至setParameterValue中处理
    Logger.flow(_logTag, '_applyParameterValueToFeaturePoints', '✅ 变形策略应用完成，特征点已更新');
  
    // 添加简洁的日志输出，显示变形应用信息
    Logger.i(_logTag, '✅ 特征点变形: 策略[${strategy.runtimeType}] | 参数[$_areaType.$_parameterName=$value] | 特征点[${pointIndexes.length}个]');
  
    Logger.flow(_logTag, '_applyParameterValueToFeaturePoints', '特征点变形已应用');
    Logger.flowEnd(_logTag, '_applyParameterValueToFeaturePoints');
  }

  /// 获取所有特征点
  List<FeaturePoint> getFeaturePoints() {
    if (_featurePointManager == null) {
      Logger.flowWarning(_logTag, 'getFeaturePoints', '特征点管理器为空，返回空列表');
      return [];
    }
    return _featurePointManager.getFeaturePoints();
  }

  /// 获取当前参数对应的特征点索引
  List<int> getCurrentParameterPointIndexes() {
    if (_featurePointManager == null) {
      Logger.flowWarning(_logTag, 'getCurrentParameterPointIndexes', '特征点管理器为空，返回空列表');
      return [];
    }
    return _featurePointManager.getCurrentParameterPointIndexes();
  }

  /// 更新累积特征点数据
  /// 
  /// [newFeaturePoints] 新的特征点数据
  void updateAccumulatedFeaturePoints(List<FeaturePoint> newFeaturePoints) {
    Logger.flowStart(_logTag, 'updateAccumulatedFeaturePoints');
    Logger.flow(_logTag, 'updateAccumulatedFeaturePoints', '🔄 开始更新累积特征点数据，新特征点数量: ${newFeaturePoints.length}');
    
    // 更新累积特征点数据
    _accumulatedFeaturePoints = List<FeaturePoint>.from(newFeaturePoints);
    
    // 更新特征点管理器中的特征点数据
    _featurePointManager.updateFeaturePoints(newFeaturePoints);
    
    // 标记需要重绘
    _invalidateCachedPainter();
    
    Logger.flow(_logTag, 'updateAccumulatedFeaturePoints', '✅ 更新累积特征点数据完成');
    Logger.flowEnd(_logTag, 'updateAccumulatedFeaturePoints');
  }

  /// 在参数切换时触发特征点校准
  Future<void> _calibrateFeaturePointsOnParameterChange(ui.Image image) async {
    Logger.flowStart(_logTag, '_calibrateFeaturePointsOnParameterChange');
    Logger.flow(_logTag, '_calibrateFeaturePointsOnParameterChange', '🔍 参数切换，开始特征点校准');
    
    try {
      // 获取当前特征点数据作为基础
      final currentFeaturePoints = _featurePointManager.getFeaturePoints();
      if (currentFeaturePoints.isEmpty) {
        Logger.flowError(_logTag, '_calibrateFeaturePointsOnParameterChange', '❌ 当前特征点数据为空，无法进行校准');
        Logger.flowEnd(_logTag, '_calibrateFeaturePointsOnParameterChange');
        return;
      }
      
      Logger.flow(_logTag, '_calibrateFeaturePointsOnParameterChange', '📊 当前特征点数量: ${currentFeaturePoints.length}');
      
      // 使用TransformationService进行特征点识别
      final transformationService = TransformationService.instance;
      Logger.flow(_logTag, '_calibrateFeaturePointsOnParameterChange', '🔄 调用特征点识别服务...');
      
      final recognizedPoints = await transformationService.recognizeFeaturePoints(image);
      
      if (recognizedPoints == null || recognizedPoints.isEmpty) {
        Logger.flowError(_logTag, '_calibrateFeaturePointsOnParameterChange', '❌ 特征点识别失败或结果为空');
        Logger.flowEnd(_logTag, '_calibrateFeaturePointsOnParameterChange');
        return;
      }
      
      // 更新累积特征点数据
      Logger.flow(_logTag, '_calibrateFeaturePointsOnParameterChange', '✅ 特征点识别成功，识别到 ${recognizedPoints.length} 个特征点');
      
      // 计算校准前后特征点数量变化
      final pointCountDiff = recognizedPoints.length - currentFeaturePoints.length;
      Logger.flow(_logTag, '_calibrateFeaturePointsOnParameterChange', '📊 校准前特征点: ${currentFeaturePoints.length}个, 校准后特征点: ${recognizedPoints.length}个, 变化: ${pointCountDiff > 0 ? "+$pointCountDiff" : pointCountDiff}个');
      
      // 如果特征点数量相同，计算坐标变化
      if (currentFeaturePoints.length == recognizedPoints.length && currentFeaturePoints.isNotEmpty) {
        double totalXDiff = 0;
        double totalYDiff = 0;
        int comparedPoints = 0;
        
        for (int i = 0; i < currentFeaturePoints.length; i++) {
          if (currentFeaturePoints[i].index == recognizedPoints[i].index) {
            final xDiff = (currentFeaturePoints[i].x - recognizedPoints[i].x).abs();
            final yDiff = (currentFeaturePoints[i].y - recognizedPoints[i].y).abs();
            totalXDiff += xDiff;
            totalYDiff += yDiff;
            comparedPoints++;
          }
        }
        
        if (comparedPoints > 0) {
          final avgXDiff = totalXDiff / comparedPoints;
          final avgYDiff = totalYDiff / comparedPoints;
          Logger.flow(_logTag, '_calibrateFeaturePointsOnParameterChange', '📊 特征点坐标平均变化: X=${avgXDiff.toStringAsFixed(4)}, Y=${avgYDiff.toStringAsFixed(4)}');
        }
      }
      
      // 更新累积特征点数据
      updateAccumulatedFeaturePoints(recognizedPoints);
      
      Logger.flow(_logTag, '_calibrateFeaturePointsOnParameterChange', '✅ 特征点校准完成');
    } catch (e, stackTrace) {
      Logger.flowError(_logTag, '_calibrateFeaturePointsOnParameterChange', '❌ 特征点校准过程中发生异常: $e');
      Logger.flowError(_logTag, '_calibrateFeaturePointsOnParameterChange', '堆栈跟踪: $stackTrace');
    }
    
    Logger.flowEnd(_logTag, '_calibrateFeaturePointsOnParameterChange');
  }
  
  /// 创建变形区域绘制器
  // 上次呼吸值，用于避免因呼吸动画触发重绘
  double _lastBreathingValueForPainter = 1.0;
  
  SimpleDeformationPainter createPainter({bool showDebugInfo = false}) {
    final now = DateTime.now();
    
    // 获取当前呼吸值
    final currentBreathingValue = getBreathingValue();
    
    // 检查参数是否有变化（在更新缓存参数之前检查）
    final breathingValueChanged = (currentBreathingValue - _lastBreathingValue).abs() > 0.05;
    final otherParametersChanged = _areaType != _lastAreaType || 
                                 _parameterName != _lastParameterName || 
                                 _paramValue != _lastParameterValue ||
                                 _isVisible != _lastIsVisible;
    
    // 检查是否可以复用缓存的绘制器
    if (_cachedPainter != null && _lastPainterCreateTime != null) {
      // 检查是否需要创建新的绘制器（限制创建频率：每200ms最多创建一次）
      if (now.difference(_lastPainterCreateTime!).inMilliseconds < 200) {
        // 如果只是呼吸值变化，直接更新并复用绘制器
        if (breathingValueChanged && !otherParametersChanged) {
          _cachedPainter!.breathingValue = currentBreathingValue;
          _lastBreathingValue = currentBreathingValue;
          return _cachedPainter!;
        }
        
        // 如果没有任何参数变化，复用绘制器
        if (!otherParametersChanged && !breathingValueChanged) {
          return _cachedPainter!;
        }
      }
    }
    
    // 更新缓存的参数（仅在创建新绘制器时更新）
    _lastAreaType = _areaType;
    _lastParameterName = _parameterName;
    _lastParameterValue = _paramValue;
    _lastIsVisible = _isVisible;
    _lastShowFeaturePoints = _showFeaturePoints;
    _lastShowCoordinateSystem = _showCoordinateSystem;
    _lastBreathingValue = currentBreathingValue;
    _lastBreathingValueForPainter = currentBreathingValue;
    
    // 控制日志输出频率 - 只在参数实际变化时输出日志
    bool shouldLogDetails = false;
    
    if (otherParametersChanged && (_lastLogTime == null || now.difference(_lastLogTime!) > _minimumLogInterval)) {
      _lastLogTime = now;
      shouldLogDetails = true;
      Logger.flowStart(_logTag, 'createPainter');
    }
  
    // 首先检查是否有缓存命中的结果
    ui.Image? imageToUse = _accumulatedImage;
    List<FeaturePoint>? featurePointsToUse = _accumulatedFeaturePoints;
    bool hasAccumulatedState = _accumulatedImage != null || (_accumulatedFeaturePoints != null && _accumulatedFeaturePoints!.isNotEmpty);
  
    // 如果有缓存命中的结果，优先使用它
    if (_currentCachedResult != null && _currentCachedResult!.deformedImage != null) {
      if (shouldLogDetails) {
        Logger.flow(_logTag, 'createPainter', '📷 使用缓存命中的变形图像: 哈希码=${_currentCachedResult!.deformedImage.hashCode}');
      }
      imageToUse = _currentCachedResult!.deformedImage;
      hasAccumulatedState = true;
  
      // 添加更详细的日志，但控制频率
      if (shouldLogDetails) {
        print('======== 缓存命中图像详细信息 ========');
        print('📷 缓存命中图像详细信息:');
        print('  - 哈希码: ${_currentCachedResult!.deformedImage.hashCode}');
        print('  - 宽度: ${_currentCachedResult!.deformedImage!.width}');
        print('  - 高度: ${_currentCachedResult!.deformedImage!.height}');
        print('  - 内存地址: [${_currentCachedResult!.deformedImage!.width}×${_currentCachedResult!.deformedImage!.height}]');
        // 检查是否所有参数值都为0
        Map<String, double> allParams = _parameterValueManager.getAllParameters();
        bool allZero = allParams.isEmpty || allParams.entries
          .where((e) => !e.key.startsWith('_'))
          .every((e) => e.value == 0.0);
        
        if (allZero) {
          print('  - 参数值: zero_state');
        } else {
          // 输出当前参数值
          if (_paramValue != 0.0) {
            print('  - 当前参数: ${_parameterName}=${_paramValue}');
          }
          
          // 输出所有非零参数值
          final nonZeroParams = allParams.entries
            .where((e) => !e.key.startsWith('_') && e.value != 0.0)
            .map((e) => '${e.key}=${e.value}')
            .join(', ');
          
          if (nonZeroParams.isNotEmpty) {
            print('  - 所有非零参数: $nonZeroParams');
          }
        }
        print('===============================');
      }
  
      if (_currentCachedResult!.deformedFeaturePoints.isNotEmpty) {
        if (shouldLogDetails) {
          Logger.flow(_logTag, 'createPainter', '📷 使用缓存命中的变形特征点: ${_currentCachedResult!.deformedFeaturePoints.length}个点');
        }
        featurePointsToUse = _currentCachedResult!.deformedFeaturePoints;
      }
    } 
    // 如果没有缓存命中的结果，检查是否有共享的变形图像和特征点
    else {
      ui.Image? sharedImage = DeformationCacheManager.getLatestDeformedImage();
      List<FeaturePoint>? sharedFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
  
      if (sharedImage != null) {
        if (shouldLogDetails) {
          Logger.flow(_logTag, 'createPainter', '📷 使用共享变形图像: 哈希码=${sharedImage.hashCode}');
        }
        imageToUse = sharedImage;
        hasAccumulatedState = true;
  
        // 更新累积图像，确保下次创建绘制器时能使用最新的变形图像
        _accumulatedImage = sharedImage;
  
        if (sharedFeaturePoints != null) {
          if (shouldLogDetails) {
            Logger.flow(_logTag, 'createPainter', '📷 使用共享变形特征点: ${sharedFeaturePoints.length}个点');
          }
          featurePointsToUse = sharedFeaturePoints;
  
          // 更新累积特征点，确保下次创建绘制器时能使用最新的变形特征点
          _accumulatedFeaturePoints = List<FeaturePoint>.from(sharedFeaturePoints);
        }
      }
    }
    
    // 获取当前参数的特征点索引
    final parameterPointIndexes = _featurePointManager.getCurrentParameterPointIndexes();
    
    // 控制日志输出频率
    if (shouldLogDetails) {
      Logger.flow(_logTag, 'createPainter', '创建新的绘制器实例，区域类型: $_areaType, 参数: $_parameterName=$_paramValue');
    }
    
    // 直接创建新的绘制器实例
    final painter = SimpleDeformationPainter(
      areaType: _areaType,
      parameterName: _parameterName,
      parameterValue: _paramValue,
      intensity: _intensity,
      isVisible: _isVisible,
      showDeformationArea: _showDeformationArea,
      transformationService: _transformationService ?? TransformationService(),
      featurePointManager: _featurePointManager,
      image: _image,
      sideImage: _parameterName == 'nostril_width' ? null : _sideImage, // 鼻翼宽度变形时不显示侧面图像
      showCoordinateSystem: _showCoordinateSystem,
      showFeaturePoints: _showFeaturePoints,
      showDebugInfo: showDebugInfo,
      transformType: _transformType,
      breathingValue: _lastBreathingValueForPainter,
      enableBreathingAnimation: _breathingController != null ? _breathingController!.isBreathingAnimationEnabled() : false,
      highlightPointIds: _highlightPointIds,
      currentParameterPointIndexes: parameterPointIndexes,
      facialCenterLineX: _cachedFacialCenterLineX,
      facialCenterLineCalculated: _facialCenterLineCalculated,
      parameterValueManager: _parameterValueManager,
      deformedImage: imageToUse,
      deformedFeaturePoints: featurePointsToUse,
      hasAccumulatedState: hasAccumulatedState,
      onDeformationResultCallback: _onDeformationResult,
      cachedResult: _currentCachedResult,
      skipSaveCache: false, // 【修复】允许保存缓存，确保右预览面板能收到变形后图像
      isParameterIncreasing: _isParameterIncreasing,
    );
  
    // 不需要重置标志，简化逻辑
  
    // 缓存绘制器和创建时间
    _cachedPainter = painter;
    _lastPainterCreateTime = now;
  
    if (shouldLogDetails) {
      Logger.flow(_logTag, 'createPainter', '创建新的绘制器，并设置变形结果回调');
      Logger.flowEnd(_logTag, 'createPainter');
    }
  
    return painter;
  }

  /// 获取面部中心线X坐标
  /// 
  /// 如果面部中心线尚未计算，则计算并返回
  /// 返回面部中心线X坐标
  double getFacialCenterLineX() {
    Logger.flowStart(_logTag, 'getFacialCenterLineX');
    
    // 如果面部中心线已经计算过，直接返回缓存的值
    if (_facialCenterLineCalculated && _cachedFacialCenterLineX != null) {
      Logger.flow(_logTag, 'getFacialCenterLineX', '返回已计算的面部中心线: $_cachedFacialCenterLineX');
      Logger.flowEnd(_logTag, 'getFacialCenterLineX');
      return _cachedFacialCenterLineX!;
    }
    
    // 如果面部中心线尚未计算，则计算并返回
    final featurePoints = _featurePointManager.getFeaturePoints();
    final centerX = _calculateFacialCenterLineX(featurePoints);
    
    Logger.flow(_logTag, 'getFacialCenterLineX', '计算并返回面部中心线: $centerX');
    Logger.flowEnd(_logTag, 'getFacialCenterLineX');
    
    return centerX;
  }
}
