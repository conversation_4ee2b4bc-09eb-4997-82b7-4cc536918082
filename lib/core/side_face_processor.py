#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
侧面特征点检测处理器
用于检测侧面图像中的面部特征点

使用MediaPipe Face Mesh模型进行特征点检测
输出JSON格式的特征点数据
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import cv2
import mediapipe as mp
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('侧面特征点检测')

# MediaPipe Face Mesh 初始化
mp_face_mesh = mp.solutions.face_mesh
mp_drawing = mp.solutions.drawing_utils
mp_drawing_styles = mp.solutions.drawing_styles

# 侧面特征点索引定义
# 这些是MediaPipe Face Mesh模型中对应于侧面轮廓的特征点索引
SIDE_PROFILE_INDICES = {
    # 鼻梁线
    'nose_bridge': [6, 197, 195, 5, 4, 45, 51, 275, 44, 2],
    # 鼻尖和鼻翼
    'nose_tip': [19, 20, 21, 22, 78, 79, 80, 81, 82, 13, 14],
    # 鼻基部
    'nose_base': [2, 98, 327, 331, 97, 98, 99, 102, 332, 333, 334],
    # 下巴轮廓
    'chin': [200, 199, 175, 152, 148, 176, 149, 150, 136, 172, 58, 132, 93, 234, 127, 162, 21, 54, 103, 67, 109],
    # 额头轮廓
    'forehead': [251, 284, 332, 297, 338, 10, 109, 67, 103, 54, 21, 162, 127],
    # 嘴唇轮廓
    'lips': [61, 185, 40, 39, 37, 0, 267, 269, 270, 409, 291, 375, 321, 405, 314, 17, 84, 181, 91, 146, 61],
    # 眼睛轮廓
    'eye': [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246],
}

def log_info(message):
    """记录信息日志"""
    logger.info(message)
    print(f"[INFO] {message}")

def log_error(message):
    """记录错误日志"""
    logger.error(message)
    print(f"[ERROR] {message}")

def detect_side_profile_landmarks(image_path):
    """
    检测侧面图像中的特征点
    
    参数:
        image_path: 图像文件路径
        
    返回:
        landmarks_data: 特征点数据列表
    """
    log_info(f"开始处理侧面图像: {image_path}")
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        log_error(f"图像文件不存在: {image_path}")
        return []
    
    try:
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            log_error(f"无法读取图像: {image_path}")
            return []
        
        # 转换为RGB格式
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        height, width, _ = image.shape
        
        log_info(f"图像尺寸: {width}x{height}")
        
        # 使用MediaPipe Face Mesh检测特征点
        with mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        ) as face_mesh:
            # 处理图像
            results = face_mesh.process(image_rgb)
            
            # 检查是否检测到面部
            if not results.multi_face_landmarks:
                log_error("未检测到面部")
                return []
            
            # 获取第一个检测到的面部
            face_landmarks = results.multi_face_landmarks[0]
            
            # 提取特征点数据
            landmarks_data = []
            
            # 遍历所有特征点
            for idx, landmark in enumerate(face_landmarks.landmark):
                # 转换为像素坐标
                x, y, z = landmark.x * width, landmark.y * height, landmark.z
                
                # 确定点的类型（主要或次要）
                is_primary = False
                is_secondary = False
                
                # 检查是否为侧面轮廓的关键点
                for category, indices in SIDE_PROFILE_INDICES.items():
                    if idx in indices:
                        if category in ['nose_bridge', 'nose_tip', 'nose_base']:
                            is_primary = True
                        else:
                            is_secondary = True
                        break
                
                # 添加特征点数据
                landmarks_data.append({
                    'index': idx,
                    'x': float(x),
                    'y': float(y),
                    'z': float(z),
                    'visibility': 1.0,  # MediaPipe不提供可见性，默认为1.0
                    'confidence': 0.9,  # MediaPipe不提供置信度，默认为0.9
                    'primary': is_primary,
                    'secondary': is_secondary,
                })
            
            log_info(f"检测到 {len(landmarks_data)} 个特征点")
            return landmarks_data
            
    except Exception as e:
        log_error(f"处理图像时出错: {str(e)}")
        return []

def save_landmarks_to_json(landmarks_data, output_path):
    """
    将特征点数据保存为JSON文件
    
    参数:
        landmarks_data: 特征点数据列表
        output_path: 输出文件路径
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(landmarks_data, f, ensure_ascii=False, indent=2)
        log_info(f"特征点数据已保存到: {output_path}")
        return True
    except Exception as e:
        log_error(f"保存特征点数据时出错: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='侧面特征点检测处理器')
    parser.add_argument('--image', type=str, required=True, help='输入图像路径')
    parser.add_argument('--output', type=str, help='输出JSON文件路径')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.debug:
        logger.setLevel(logging.DEBUG)
    
    # 检测特征点
    landmarks_data = detect_side_profile_landmarks(args.image)
    
    # 如果未检测到特征点，退出
    if not landmarks_data:
        log_error("未检测到有效的特征点，退出程序")
        sys.exit(1)
    
    # 如果指定了输出路径，保存为JSON文件
    if args.output:
        if save_landmarks_to_json(landmarks_data, args.output):
            log_info("处理完成")
        else:
            log_error("保存特征点数据失败")
            sys.exit(1)
    else:
        # 否则，将结果打印到标准输出
        print(json.dumps(landmarks_data, ensure_ascii=False))
        log_info("处理完成，结果已输出到标准输出")
    
    sys.exit(0)

if __name__ == "__main__":
    start_time = datetime.now()
    log_info("程序开始执行")
    
    try:
        main()
    except Exception as e:
        log_error(f"程序执行出错: {str(e)}")
        sys.exit(1)
    
    end_time = datetime.now()
    execution_time = (end_time - start_time).total_seconds()
    log_info(f"程序执行完成，耗时: {execution_time:.2f}秒")
