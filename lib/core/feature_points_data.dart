/// BeautiFun 面部美化特征点数据定义
/// 
/// 【统一的科学特征点三层分类系统】
/// 基于V型下巴和鼻长变形的成功实践，所有面部美化参数必须严格按照
/// 科学的三层分类系统进行定义，确保变形效果的解剖学准确性和可预测性。
/// 
/// 三层分类标准：
/// 1. 主特征点 (Primary Points)：
///    - 作用：直接承担主要变形责任，变形强度100%
///    - 选择原则：直接影响目标效果的关键解剖标志点
///    - 数量控制：通常2-4个点，确保精确控制
/// 
/// 2. 次特征点 (Secondary Points)：
///    - 作用：协同主特征点产生自然过渡效果，变形强度60-80%
///    - 选择原则：与主特征点相邻的渐进变形点
///    - 数量控制：通常4-8个点，确保平滑过渡
/// 
/// 3. 辅助特征点 (Auxiliary Points)：
///    - 作用：提供变形支撑和边界约束，变形强度30-50%
///    - 选择原则：维持面部比例和对称性的稳定点
///    - 数量控制：根据需要，通常6-12个点
/// 
/// 验证标准：
/// ✅ 解剖学准确性：特征点对应真实面部解剖结构
/// ✅ 功能层次性：三类点的变形强度呈递减关系
/// ✅ 对称性保障：所有非中线点必须有对称点对
/// ✅ 边界清晰性：不同类别功能明确，无重叠
/// ✅ 效果可预测性：基于分类能准确预测变形效果
/// 
/// 作者：BeautiFun开发团队
/// 创建时间：2025-07-19
/// 版本：2.0.0 - 统一科学分类标准

import 'package:flutter/material.dart';
import 'dart:math';
import 'package:vector_math/vector_math.dart';
import 'feature_points_config.dart';
import 'transform_base.dart';
import '../utils/logger.dart';
import 'transform_type.dart';

/// 眼部对称点对定义
const List<Map<String, int>> eyeSymmetryPairs = [
  // 上眼睑点对
  {'left': 159, 'right': 386},  // 上眼睑外侧点
  {'left': 158, 'right': 385},  // 上眼睑中外侧点
  {'left': 157, 'right': 384},  // 上眼睑中点
  {'left': 156, 'right': 383},  // 上眼睑中内侧点
  {'left': 155, 'right': 382},  // 上眼睑内侧点
  
  // 下眼睑点对
  {'left': 145, 'right': 374},  // 下眼睑外侧点
  {'left': 144, 'right': 373},  // 下眼睑中外侧点
  {'left': 143, 'right': 372},  // 下眼睑中点
  {'left': 142, 'right': 371},  // 下眼睑中内侧点
  
  // 眼角点对
  {'left': 133, 'right': 362},  // 内眼角点
  {'left': 130, 'right': 359},  // 外眼角点
];

/// 面部轮廓对称点对定义 - 基于解剖学原理和医美手术实现效果
const List<Map<String, int>> faceContourSymmetryPairs = [
  // 下颌线复合体对称点对 - 对应下颌角磨骨、下颌缘改形手术
  {'left': 67, 'right': 297},   // 颌角
  {'left': 109, 'right': 338},  // 面颊中部
  {'left': 145, 'right': 374},  // 下颌线辅助点 1
  {'left': 146, 'right': 375},  // 下颌线辅助点 2
  {'left': 147, 'right': 376},  // 下颌线辅助点 3
  {'left': 162, 'right': 389},  // 下巴轮廓起点
  {'left': 21, 'right': 251},   // 下颌线中间点 1
  {'left': 54, 'right': 284},   // 下颌线中间点 2
  {'left': 104, 'right': 333},  // 中颊点对（修正：104与333为对称点）
  
  // 颧骨复合体对称点对 - 对应颧骨降低、颧骨内推手术
  {'left': 216, 'right': 436},  // 颧骨主点
  {'left': 123, 'right': 352},  // 颧骨辅助点 1
  {'left': 50, 'right': 280},   // 颧骨辅助点 2
  {'left': 207, 'right': 427},  // 颧骨辅助点 3
  {'left': 187, 'right': 411},  // 颧骨辅助点 4
  
  // 下巴复合体对称点对 - 对应垫下巴、下巴整形手术
  {'left': 148, 'right': 377},  // 下巴辅助点 1
  {'left': 149, 'right': 378},  // 下巴辅助点 2
  {'left': 150, 'right': 379},  // 下巴辅助点 3
  // 注意：151是中心线点，不存在对称点
  
  // 太阳穴区域对称点对 - 对应颜面提升手术
  {'left': 139, 'right': 368},  // 太阳穴主点
  {'left': 71, 'right': 301},   // 太阳穴辅助点 1
  {'left': 68, 'right': 298},   // 太阳穴辅助点 2
  {'left': 103, 'right': 332},  // 根据坐标计算，103与332为对称点（误差16.09像素）
  
  // V脸塑形对称点对 - 对应轮廓收紧和面部提升
  {'left': 132, 'right': 361},  // 法令纹下部
  {'left': 169, 'right': 394},  // 面部下部轮廓点
  {'left': 58, 'right': 288},   // 面颊支撑点
];

/// 抗衰老区域对称点对定义 - 基于解剖学特征和美学原理
const List<Map<String, int>> antiAgingSymmetryPairs = [
  // 法令纹对称点对
  {'left': 129, 'right': 358},  // 法令纹最上部 (0.98)
  {'left': 130, 'right': 359},  // 法令纹上部 (0.97)
  {'left': 131, 'right': 360},  // 法令纹中部 (0.97)
  {'left': 133, 'right': 362},  // 法令纹过渡 (0.98)
  {'left': 167, 'right': 393},  // 法令纹主要点（误差3.12像素）
  
  // 额头对称点对
  {'left': 21, 'right': 251},   // 额头中部
  {'left': 71, 'right': 301},   // 额头外侧
  {'left': 162, 'right': 389},  // 太阳穴区域
  {'left': 68, 'right': 298},   // 额头过渡
  
  // 面部紧致对称点对
  {'left': 54, 'right': 284},   // 上颊主导点对
  {'left': 104, 'right': 333},  // 中颊点对1（对称性0.96）
  {'left': 103, 'right': 332},  // 中颊点对2（根据坐标计算，103与332为对称点）
  {'left': 67, 'right': 297},   // 下颊支撑点对
  
  // 去皱纹对称点对
  {'left': 107, 'right': 336},  // 主要皱纹区
  {'left': 108, 'right': 337},  // 皱纹过渡区
  {'left': 148, 'right': 377},  // 嘴角区域
];

/// 特征点样式配置
const Map<String, Map<String, dynamic>> pointStyles = {
  'primary': {
    'size': 1.5,
    'color': Color(0xFFFFFFFF),  // 白色
    'opacity': 1.0,
    'hasGlow': true,
    'glowColor': Color(0xFFE0E0E0),  // 浅灰色光晕
    'glowRadius': 3.0,
    'glowThickness': 1.0,
  },
  'secondary': {
    'size': 1.5,
    'color': Color(0xFFF0F0F0),  // 灰白色
    'opacity': 1.0,
    'hasGlow': true,
    'glowColor': Color(0xFFD3D3D3),  // 浅灰色光晕
    'glowRadius': 2.5,
    'glowThickness': 1.0,
  },
  'auxiliary': {
    'size': 1.5,  // 缩小大小
    'color': Color(0xFFE0E0E0),  // 浅灰白色
    'opacity': 1.0,
    'hasGlow': true,  // 添加光晕效果
    'glowColor': Color(0xFFC0C0C0),  // 灰色光晕
    'glowRadius': 2.5,
    'glowThickness': 1.0,
  },
};

/// 点的动画配置
const Map<String, double> pointAnimationConfig = {
  'minPointSize': 0.5,     // 最小点大小
  'maxPointSize': 2.0,     // 高亮点大小
  'minOpacity': 0.2,       // 最小透明度
  'maxOpacity': 1.0,       // 最大透明度
  'connectionThickness': 1.0, // 连接线粗细
};

/// 面部特征点区域和参数配置
final Map<String, AreaConfig> beautyAreaConfigs = {
  // 1. 面部轮廓 - 使用新的参数为中心的设计
  'face_contour': AreaConfig(
    displayName: '面部轮廓',
    parameters: {
      'contour_tighten': ParameterConfig(
        displayName: '轮廓收紧',
        // 主特征点：直接承担轮廓收紧的关键解剖点（变形强度100%）
        primaryPoints: [67, 297, 109, 338],  // 左右下颌角 + 左右面颊中部
        // 次特征点：协同主特征点产生自然过渡（变形强度60-80%）
        secondaryPoints: [162, 389, 71, 301, 68, 298, 104, 169, 394],  // 轮廓过渡点
        // 辅助特征点：提供变形支撑和边界约束（变形强度30-50%）
        auxiliaryPoints: [145, 146, 147, 374, 375, 376, 123, 352, 50, 280, 207, 427, 187, 411, 58, 288],
        transformConfigs: {
          67: PointTransformConfig(  // 左颌角
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: 1.0,
            ),
          ),
          297: PointTransformConfig(  // 右颌角
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: 1.0,
            ),
          ),
          109: PointTransformConfig(  // 左面颊中部
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: 0.8,
            ),
          ),
          338: PointTransformConfig(  // 右面颊中部
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: 0.8,
            ),
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 15.0),
          curveType: CurveType.quadratic,
        ),
      ),
      'v_chin': ParameterConfig(
        displayName: 'V型下巴',
        // 【简化配置】移除详细的特征点配置，让transformation类作为唯一定义源
        primaryPoints: [172, 136, 148, 377],    // 主要下颌角和轮廓点
        secondaryPoints: [176, 149, 150, 378, 379], // 下颌轮廓渐进点  
        auxiliaryPoints: [175, 18, 152],         // 下巴中心和辅助点
        transformConfigs: {
          // 【统一管理】移除详细配置，由VChinTransformation类统一管理
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 20.0), // 增强输出范围
          curveType: CurveType.quadratic,
        ),
      ),
      'face_shape': ParameterConfig(
        displayName: '脸型优化',
        primaryPoints: [152, 67, 297],  // 主导点：下巴和下颌角
        secondaryPoints: [109, 338],    // 协同点：下颌线
        auxiliaryPoints: [216, 436],    // 支撑点：颧骨区域
        transformConfigs: {
          152: PointTransformConfig(  // 下巴中心点
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: 1.0,
            ),
          ),
          67: PointTransformConfig(  // 左下颌角
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: 0.9,
            ),
          ),
          297: PointTransformConfig(  // 右下颌角
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: 0.9,
            ),
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 20.0),
          curveType: CurveType.quadratic,
        ),
      ),
    },
    region: RegionConfig(
      boundaryPoints: [
        // 下巴区域
        152, 175,  // 下巴中心线
        148, 149, 150, 151,  // 左侧下巴轮廓
        377, 378, 379,  // 右侧下巴轮廓
        
        // 下颌线区域
        67, 297,   // 左右下颌角
        109, 338,  // 左右面颊中部
        145, 146, 147,  // 左侧下颌线辅助点
        374, 375, 376,  // 右侧下颌线辅助点
        
        // 颧骨区域
        216, 436,  // 左右颧骨主点
        123, 352,  // 左右颧骨内侧
        50, 280,   // 左右颧骨上部
        207, 427,  // 左右颧骨外侧
      ],
      centerPoint: 152,  // 下巴中心点作为区域中心
      symmetricPairs: faceContourSymmetryPairs,
    ),
  ),
  
  // 2. 鼻部塑形 - 统一的科学特征点分类定义
  'nose': AreaConfig(
    displayName: '鼻部塑形',
    region: RegionConfig(
      boundaryPoints: [6, 197, 195, 5, 4, 1, 2, 19, 168, 94, 114, 343, 129, 358, 219, 439],
      centerPoint: 2, // 鼻尖中点
      symmetricPairs: [
        {'left': 114, 'right': 343}, // 左右鼻尖外侧点
        {'left': 129, 'right': 358}, // 左右鼻翼点
        {'left': 219, 'right': 439}, // 左右鼻翼外缘点
      ],
    ),
    parameters: {
      // 2.1 鼻子长度 - 基于成功实践的科学分类
      'nose_length': ParameterConfig(
        displayName: '鼻子长度',
        // 主特征点：直接影响鼻长的关键点（变形强度100%）
        primaryPoints: [216, 436],  // 左右颜骨主点
        // 次特征点：协同主特征点产生自然过渡（变形强度60-80%）
        secondaryPoints: [67, 297],  // 左右下颌角
        // 辅助特征点：提供变形支撑和边界约束（变形强度30-50%）
        auxiliaryPoints: [123, 352, 50, 280, 207, 427],  // 颜骨辅助点
        transformConfigs: <int, PointTransformConfig>{}, // 空配置，使用策略模式
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 12.0),
          curveType: CurveType.quadratic,
        ),
      ),
      
      // 2.2 鼻梁高度
      'bridge_height': ParameterConfig(
        displayName: '鼻梁高度',
        // 主特征点：鼻梁中心线关键点（变形强度100%）
        primaryPoints: [6, 197, 195, 5],  // 鼻梁中心线主点
        // 次特征点：鼻背和鼻尖协同点（变形强度60-80%）
        secondaryPoints: [4, 1, 2],  // 鼻背点、鼻尖和鼻小柱顶点
        transformConfigs: <int, PointTransformConfig>{}, // 空配置，使用策略模式
        // 辅助特征点：鼻基底支撑点（变形强度30-50%）
        auxiliaryPoints: [19, 168],  // 鼻基底中心点和鼻梁起始点
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 15.0),
          curveType: CurveType.quadratic,
        ),
      ),
      
      // 2.3 鼻尖调整
      'tip_adjust': ParameterConfig(
        displayName: '鼻尖调整',
        // 主特征点：鼻尖中心关键点（变形强度100%）
        primaryPoints: [94, 19],  // 鼻尖中心点、鼻基底中心点
        // 次特征点：鼻尖外侧协同点（变形强度60-80%）
        secondaryPoints: [114, 343],  // 左右鼻尖外侧点
        // 辅助特征点：鼻翼支撑点（变形强度30-50%）
        auxiliaryPoints: [129, 358, 219, 439],  // 左右鼻翼点、左右鼻翼外缘点
        transformConfigs: <int, PointTransformConfig>{}, // 空配置，使用策略模式
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 10.0),
          curveType: CurveType.quadratic,
        ),
      ),
      
      // 2.4 鼻翼宽度
      'nostril_width': ParameterConfig(
        displayName: '鼻翼宽度',
        // 主特征点：鼻翼宽度直接控制点（变形强度100%）
        primaryPoints: [129, 358],  // 左右鼻翼主点
        // 次特征点：鼻翼周围协同点（变形强度60-80%）
        secondaryPoints: [219, 439, 94],  // 左右鼻翼外缘点、鼻尖中心点
        // 辅助特征点：鼻尖和鼻基支撑点（变形强度30-50%）
        auxiliaryPoints: [114, 343, 19],  // 左右鼻尖外侧点、鼻基底中心点
        transformConfigs: <int, PointTransformConfig>{}, // 空配置，使用策略模式
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 8.0),
          curveType: CurveType.quadratic,
        ),
      ),
      
      // 2.5 鼻基抬高
      'base_height': ParameterConfig(
        displayName: '鼻基抬高',
        // 主特征点：鼻基底中心关键点（变形强度100%）
        primaryPoints: [168, 2],  // 鼻梁起始点和鼻小柱顶点
        // 次特征点：鼻基底协同点（变形强度60-80%）
        secondaryPoints: [164],  // 鼻基底中线点
        // 辅助特征点：无需辅助点（变形强度30-50%）
        auxiliaryPoints: [],  // 鼻基底变形无需辅助点
        transformConfigs: <int, PointTransformConfig>{}, // 空配置，使用策略模式
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 12.0),
          curveType: CurveType.quadratic,
        ),
      ),
    },
  ),
  
  // 3. 眼部美化 - 使用新的参数为中心的设计
  'eyes': AreaConfig(
    displayName: '眼部美化',
    region: RegionConfig(
      boundaryPoints: [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246, 362, 398, 384, 385, 386, 387, 388, 466, 263, 249, 390, 373, 374, 380, 381, 382],
      centerPoint: 168, // 鼻梁起始点（眼间中点）
      symmetricPairs: [
        {'left': 33, 'right': 263}, // 左右外眼角
        {'left': 133, 'right': 362}, // 左右下眼睑
        {'left': 157, 'right': 384}, // 左右上眼睑
      ],
    ),
    parameters: {
      // 1. 双眼皮
      'double_fold': ParameterConfig(
        displayName: '双眼皮',
        primaryPoints: const [157, 384],  // 左右眼上眼睑中点
        secondaryPoints: const [156, 158, 383, 385],  // 上眼睑中内侧和中外侧点
        auxiliaryPoints: const [155, 159, 382, 386],  // 上眼睑内侧和外侧点
        transformConfigs: {
          157: PointTransformConfig(  // 左眼上眼睑中点
            transformVector: const TransformVector(
              direction: TransformDirection.vertical,
              magnitude: 0.6,
            ),
          ),
          384: PointTransformConfig(  // 右眼上眼睑中点
            transformVector: const TransformVector(
              direction: TransformDirection.vertical,
              magnitude: 0.6,
            ),
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 8.0),
          curveType: CurveType.quadratic,
        ),
      ),

      // 2. 开眼角
      'canthal_tilt': ParameterConfig(
        displayName: '开眼角',
        primaryPoints: const [133, 362],  // 左右内眼角点
        secondaryPoints: const [155, 382],  // 左右上眼睑内侧点
        auxiliaryPoints: const [130, 359],  // 左右外眼角点
        transformConfigs: {
          133: PointTransformConfig(  // 左眼内眼角
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: -0.5,  // 向内侧移动
            ),
          ),
          362: PointTransformConfig(  // 右眼内眼角
            transformVector: const TransformVector(
              direction: TransformDirection.horizontal,
              magnitude: 0.5,  // 向内侧移动
            ),
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 6.0),
          curveType: CurveType.quadratic,
        ),
      ),

      // 3. 去眼袋
      'eye_bag_removal': ParameterConfig(
        displayName: '去眼袋',
        primaryPoints: const [143, 372],  // 下眼睑中点
        secondaryPoints: const [144, 373],  // 下眼睑外侧点
        auxiliaryPoints: const [145, 374],  // 下眼睑外侧点
        transformConfigs: {
          143: PointTransformConfig(  // 左眼下眼睑中点
            transformVector: const TransformVector(
              direction: TransformDirection.depth,
              magnitude: -0.7,  // 向内凹陷
            ),
          ),
          372: PointTransformConfig(  // 右眼下眼睑中点
            transformVector: const TransformVector(
              direction: TransformDirection.depth,
              magnitude: -0.7,  // 向内凹陷
            ),
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 10.0),
          curveType: CurveType.quadratic,
        ),
      ),

      // 4. 提眼尾
      'outer_corner_lift': ParameterConfig(
        displayName: '提眼尾',
        primaryPoints: const [130, 359],  // 左右外眼角点
        secondaryPoints: const [145, 374],  // 左右下眼睑外侧点
        auxiliaryPoints: const [159, 386],  // 左右上眼睑外侧点
        transformConfigs: {
          130: PointTransformConfig(  // 左眼外眼角
            transformVector: const TransformVector(
              direction: TransformDirection.diagonal,
              magnitude: 0.8,
              angle: 45.0,  // 向上外方向提升
            ),
          ),
          359: PointTransformConfig(  // 右眼外眼角
            transformVector: const TransformVector(
              direction: TransformDirection.diagonal,
              magnitude: 0.8,
              angle: 135.0,  // 向上外方向提升
            ),
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 12.0),
          curveType: CurveType.quadratic,
        ),
      ),
    },
  ),

  // 4. 唇部造型 - 统一的科学特征点分类定义
  'lips': AreaConfig(
    displayName: '唇部造型',
    region: RegionConfig(
      boundaryPoints: [61, 185, 40, 39, 269, 270, 409, 291, 78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308],
      centerPoint: 13, // 上唇中点
      symmetricPairs: [
        {'left': 61, 'right': 291}, // 左右嘴角
        {'left': 40, 'right': 270}, // 左右上唇边缘
        {'left': 39, 'right': 269}, // 左右上唇边缘
      ],
    ), // 唇部区域配置
    parameters: {
      // 4.1 唇形调整
      'lip_shape': ParameterConfig(
        displayName: '唇形调整',
        // 主特征点：上唇峰关键点（变形强度100%）
        primaryPoints: [37, 0, 267],  // 上唇峰关键点（M形）
        // 次特征点：上唇轮廓协同点（变形强度60-80%）
        secondaryPoints: [61, 185, 40, 39, 269, 270, 409, 291],  // 上唇其他轮廓点
        // 辅助特征点：上唇内轮廓支撑点（变形强度30-50%）
        auxiliaryPoints: [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308],  // 上唇内轮廓辅助点
        transformConfigs: <int, PointTransformConfig>{}, // 空配置，使用策略模式
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 6.0),
          curveType: CurveType.quadratic,
        ),
      ),
      
      // 4.3 嘴角上扬
      'mouth_corner': ParameterConfig(
        displayName: '嘴角上扬',
        // 主特征点：嘴角关键点（变形强度100%）
        primaryPoints: [61, 291],  // 左右嘴角点
        // 次特征点：嘴角周围协同点（变形强度60-80%）
        secondaryPoints: [40, 270, 39, 269],  // 嘴角周围轮廓点
        // 辅助特征点：下唇边缘支撑点（变形强度30-50%）
        auxiliaryPoints: [84, 17, 314, 405],  // 下唇边缘支撑点
        transformConfigs: <int, PointTransformConfig>{}, // 空配置，使用策略模式
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 10.0),
          curveType: CurveType.quadratic,
        ),
      ),
      
    },
  ),
  
  // 5. 抗衰冻龄 - 完整的参数配置
  'anti_aging': AreaConfig(
    displayName: '抗衰冻龄',
    parameters: {
      // 1. 法令纹
      'nasolabial_folds': ParameterConfig(
        displayName: '法令纹',
        primaryPoints: [129, 358, 167, 393],  // 法令纹主要点
        secondaryPoints: [130, 359, 131, 360, 133, 362],  // 法令纹辅助点
        auxiliaryPoints: [132, 361, 146, 375],  // 周边支撑点
        transformConfigs: {
          129: PointTransformConfig(  // 左侧法令纹上部
            transformVector: const TransformVector(
              direction: TransformDirection.depth,
              magnitude: -0.8,  // 向内凹陷减淡
            ),
            weight: 1.0,
            falloffRadius: 20.0,
          ),
          358: PointTransformConfig(  // 右侧法令纹上部
            transformVector: const TransformVector(
              direction: TransformDirection.depth,
              magnitude: -0.8,
            ),
            weight: 1.0,
            falloffRadius: 20.0,
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 8.0),
          curveType: CurveType.quadratic,
        ),
      ),

      // 2. 去皱纹
      'wrinkle_removal': ParameterConfig(
        displayName: '去皱纹',
        primaryPoints: [107, 336, 108, 337],  // 主要皱纹区域
        secondaryPoints: [148, 377, 54, 284],  // 皱纹过渡区
        auxiliaryPoints: [21, 251, 71, 301],  // 额头皱纹区
        transformConfigs: {
          107: PointTransformConfig(  // 左侧皱纹区
            transformVector: const TransformVector(
              direction: TransformDirection.depth,
              magnitude: -0.6,  // 平滑皱纹
            ),
            weight: 0.9,
            falloffRadius: 18.0,
          ),
          336: PointTransformConfig(  // 右侧皱纹区
            transformVector: const TransformVector(
              direction: TransformDirection.depth,
              magnitude: -0.6,
            ),
            weight: 0.9,
            falloffRadius: 18.0,
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 6.0),
          curveType: CurveType.quadratic,
        ),
      ),

      // 3. 额头饱满
      'forehead_fullness': ParameterConfig(
        displayName: '额头饱满',
        primaryPoints: [21, 251, 71, 301],  // 额头主要区域
        secondaryPoints: [162, 389, 68, 298],  // 太阳穴区域
        auxiliaryPoints: [139, 368, 103, 332],  // 额头边缘点
        transformConfigs: {
          21: PointTransformConfig(  // 左额头中部
            transformVector: const TransformVector(
              direction: TransformDirection.depth,
              magnitude: 0.7,  // 向外凸起
            ),
            weight: 1.0,
            falloffRadius: 25.0,
          ),
          251: PointTransformConfig(  // 右额头中部
            transformVector: const TransformVector(
              direction: TransformDirection.depth,
              magnitude: 0.7,
            ),
            weight: 1.0,
            falloffRadius: 25.0,
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 10.0),
          curveType: CurveType.quadratic,
        ),
      ),

      // 4. 面容紧致
      'facial_firmness': ParameterConfig(
        displayName: '面容紧致',
        primaryPoints: [54, 284, 104, 333, 67, 297],  // 面部主要支撑点
        secondaryPoints: [103, 332, 109, 338, 216, 436],  // 面颊和颧骨点
        auxiliaryPoints: [123, 352, 50, 280, 207, 427],  // 面部轮廓辅助点
        transformConfigs: {
          54: PointTransformConfig(  // 左上颊支撑点
            transformVector: const TransformVector(
              direction: TransformDirection.diagonal,
              magnitude: 0.8,
              angle: 45.0,  // 向上外方向提升
            ),
            weight: 1.0,
            falloffRadius: 22.0,
          ),
          284: PointTransformConfig(  // 右上颊支撑点
            transformVector: const TransformVector(
              direction: TransformDirection.diagonal,
              magnitude: 0.8,
              angle: 135.0,
            ),
            weight: 1.0,
            falloffRadius: 22.0,
          ),
        },
        intensityMapping: const IntensityMapping(
          inputRange: (0.0, 1.0),
          outputRange: (0.0, 12.0),
          curveType: CurveType.quadratic,
        ),
      ),
    },
    region: RegionConfig(
      boundaryPoints: [
        // 法令纹区域
        129, 358, 167, 393, 130, 359, 131, 360,
        // 额头区域  
        21, 251, 71, 301, 162, 389, 68, 298,
        // 面颊紧致区域
        54, 284, 104, 333, 67, 297, 109, 338,
        // 皱纹区域
        107, 336, 108, 337, 148, 377,
      ],
      centerPoint: 168,  // 面部中心点
      symmetricPairs: antiAgingSymmetryPairs,
    ),
  ),
};

/// 高级区域配置 - 新的参数为中心的设计
final Map<String, AdvancedAreaConfig> advancedAreaConfigs = {
  'face_contour': faceContourAdvancedAreaConfig,
  // 'nose': noseAdvancedAreaConfig,  // 暂时注释，修复编译错误
  // 'eyes': eyesAdvancedAreaConfig,  // 已迁移到新的参数为中心的设计
  // 'lips': lipsAdvancedAreaConfig,  // 已迁移到新的参数为中心的设计
  // 'anti_aging': antiAgingAdvancedAreaConfig, // 抗衰老区域高级配置 - 暂时注释掉，修复编译错误
};

/// 面部轮廓高级区域配置
final faceContourAdvancedAreaConfig = AdvancedAreaConfig(
  name: '面部轮廓',
  // 复合系统配置
  complexSystems: {
    'contour_tighten': ComplexSystem(
      name: '轮廓收紧',
      primaryPoints: [67, 297, 109, 338],
      secondaryPoints: [152, 175, 216, 436],
      auxiliaryPoints: [145, 146, 147, 374, 375, 376, 123, 352, 148, 149, 150, 151, 377, 378, 379],
      elasticity: 1.2,
      resistance: 0.6,
      recovery: 0.9,
      pointPairs: [
        {'left': 67, 'right': 297},   // 下颌角对称点
        {'left': 109, 'right': 338},  // 面颊对称点
        {'left': 216, 'right': 436},  // 颧骨对称点
        {'left': 123, 'right': 352},  // 颧骨内侧对称点
      ],
    ),
    'face_shape': ComplexSystem(
      name: '脸型优化',
      primaryPoints: [152, 67, 297],
      secondaryPoints: [109, 338],
      auxiliaryPoints: [216, 436],
      elasticity: 1.0,
      resistance: 0.5,
      recovery: 0.8,
      pointPairs: [
        {'left': 67, 'right': 297},   // 下颌角对称点
        {'left': 109, 'right': 338},  // 面颊对称点
        {'left': 216, 'right': 436},  // 颧骨对称点
      ],
    ),
  },
  // 高级参数配置
  parameters: {
    'contour_tighten': AdvancedParameterConfig(
      name: '轮廓收紧',
      primaryTransformType: TransformType.local,
      primaryDirection: TransformDirection.horizontal,
      baseIntensity: 16.0,
      stages: [
        TransformStage(
          name: '初始收紧',
          intensity: 0.5,
          affectedPoints: [67, 297, 109, 338],
          transformType: TransformType.local,
        ),
        TransformStage(
          name: '深度收紧',
          intensity: 1.0,
          affectedPoints: [67, 297, 109, 338, 123, 352],
          transformType: TransformType.local,
        ),
      ],
      anatomicalConstraints: {
        'maxDeformation': 20.0,
        'preserveSymmetry': true,
        'preserveProportions': true,
      },
    ),
    'face_shape': AdvancedParameterConfig(
      name: '脸型优化',
      primaryTransformType: TransformType.local,
      primaryDirection: TransformDirection.horizontal,
      baseIntensity: 20.0,
      stages: [
        TransformStage(
          name: '初始优化',
          intensity: 0.5,
          affectedPoints: [67, 297, 109, 338],
          transformType: TransformType.local,
        ),
        TransformStage(
          name: '完全优化',
          intensity: 1.0,
          affectedPoints: [67, 297, 109, 338, 152, 216, 436],
          transformType: TransformType.local,
        ),
      ],
      anatomicalConstraints: {
        'maxDeformation': 25.0,
        'preserveSymmetry': true,
        'preserveProportions': true,
      },
    ),
  },
);

/// 获取特定区域的配置
AreaConfig getAreaConfig(String areaName) {
  return beautyAreaConfigs[areaName] ?? AreaConfig(
    displayName: '未知区域',
    parameters: {},
    region: RegionConfig(
      boundaryPoints: [],
      symmetricPairs: [],
    ),
  );
}

/// 获取特定区域的高级配置
AdvancedAreaConfig? getAdvancedAreaConfig(String areaName) {
  return advancedAreaConfigs[areaName];
}

/// 获取特定区域的特定参数配置
ParameterConfig? getParameterConfig(String areaName, String paramName) {
  final areaConfig = beautyAreaConfigs[areaName];
  if (areaConfig == null) return null;
  return areaConfig.parameters[paramName];
}

/// 获取特定区域的特定高级参数配置
AdvancedParameterConfig? getAdvancedParameterConfig(String areaName, String paramName) {
  final areaConfig = advancedAreaConfigs[areaName];
  if (areaConfig == null) return null;
  return areaConfig.parameters[paramName];
}

/// 获取所有区域名称
List<String> getAllAreaNames() {
  return beautyAreaConfigs.keys.toList();
}

/// 获取特定区域的所有参数名称
List<String> getAreaParameterNames(String areaName) {
  final areaConfig = beautyAreaConfigs[areaName];
  if (areaConfig == null) return [];
  return areaConfig.parameters.keys.toList();
}

/// 计算特定区域特定参数的变形
/// 简化版本：减少日志输出和不必要的处理步骤
Map<int, Offset> calculateDeformation(String areaName, String paramName, double paramValue) {
  // 记录开始时间
  final startTime = DateTime.now();
  
  // 简化日志记录
  Logger.log('FeaturePointsData', 'calculateDeformation', '🔄 计算变形 | 区域: $areaName | 参数: $paramName | 值: ${paramValue.toStringAsFixed(2)}');
  
  // 检查参数值是否有效并限制在范围内
  if (paramValue.isNaN || paramValue.isInfinite) {
    return {};
  }
  
  // 将参数值限制在[-1.0, 1.0]范围内
  paramValue = paramValue.clamp(-1.0, 1.0);
  
  // 使用高级变形计算（如果可用）
  final advancedConfig = advancedAreaConfigs[areaName];
  if (advancedConfig != null) {
    // 检查参数配置
    final paramConfig = getParameterConfig(areaName, paramName);
    if (paramConfig == null) {
      return {}; // 如果没有参数配置，返回空结果
    }
    
    // 根据区域和参数返回相应的变形结果
    Map<int, Offset> result;
    
    switch (areaName) {
      case 'nose':
        switch (paramName) {
          case 'bridge_height':
          case 'tip_adjust':
          case 'nostril_width':
          case 'base_height':
          case 'nose_length':
            // 变形计算由策略模式处理，这里返回空映射
            result = <int, Offset>{};
            break;
          default:
            return {};
        }
        
        // 简化的结果统计
        int nonZeroDeformations = 0;
        
        // 只计算非零变形的数量，不计算其他统计信息
        for (final offset in result.values) {
          final magnitude = sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
          if (magnitude > 0.01) { // 忽略非常小的变形
            nonZeroDeformations++;
          }
        }
        
        // 简化的日志输出
        Logger.log('FeaturePointsData', 'calculateDeformation', '✅ 鼻部变形计算完成 | 有效点数: $nonZeroDeformations / ${result.length}');
        
        // 在正式版本中不需要输出样本数据
        
        return result;
        
      case 'face_contour':
        switch (paramName) {
          case 'contour_tighten':
            result = FaceContourTransformCalculator.calculateContourTightenDeformation(paramValue);
            break;
          case 'chin_adjust':
            result = FaceContourTransformCalculator.calculateChinAdjustDeformation(paramValue);
            break;
          case 'face_shape':
            result = FaceContourTransformCalculator.calculateFaceShapeDeformation(paramValue);
            break;
          default:
            return {};
        }
        // 简化的结果统计
        int nonZeroDeformations = 0;
        
        // 只计算非零变形的数量
        for (final offset in result.values) {
          final magnitude = sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
          if (magnitude > 0.01) {
            nonZeroDeformations++;
          }
        }
        
        // 简化的日志输出
        Logger.log('FeaturePointsData', 'calculateDeformation', '✅ 面部轮廓变形计算完成 | 区域: $areaName | 参数: $paramName | 有效点数: $nonZeroDeformations / ${result.length}');
        
        return result;
      // case 'eyes':
      //   switch (paramName) {
      //     case 'double_fold':
      //       return EyesTransformCalculator.calculateDoubleFoldDeformation(paramValue);
      //     case 'canthal_tilt':
      //       return EyesTransformCalculator.calculateCanthalTiltDeformation(paramValue);
      //     case 'eye_bag_removal':
      //       return EyesTransformCalculator.calculateEyeBagRemovalDeformation(paramValue);
      //     case 'outer_corner_lift':
      //       return EyesTransformCalculator.calculateOuterCornerLiftDeformation(paramValue);
      //   }
      case 'lips':
        switch (paramName) {
          case 'lip_shape':
          case 'mouth_corner':
            // 变形计算由策略模式处理，这里返回空映射
            return <int, Offset>{};
        }
      // TODO: 'anti_aging' 后续更新
    }
  }
  
  // 回退到基本变形计算
  Logger.log('FeaturePointsData', 'calculateDeformation', '📊 [DEBUG] 回退到基本变形计算 | 区域: $areaName | 参数: $paramName | 参数值: ${paramValue.toStringAsFixed(3)}');
  
  final paramConfig = getParameterConfig(areaName, paramName);
  if (paramConfig == null) {
    Logger.log('FeaturePointsData', 'calculateDeformation', '⚠️ [ERROR] 未找到参数配置: $areaName.$paramName');
    final duration = DateTime.now().difference(startTime);
    Logger.log('FeaturePointsData', 'calculateDeformation', '❌ [失败] 变形计算失败 | 耗时: ${duration.inMilliseconds}ms');
    return {};
  }
  
  // 记录参数配置的详细信息
  final inputRange = paramConfig.intensityMapping.inputRange;
  final outputRange = paramConfig.intensityMapping.outputRange;
  Logger.log('FeaturePointsData', 'calculateDeformation', '📊 [DEBUG] 参数映射信息:');
  Logger.log('FeaturePointsData', 'calculateDeformation', '  • 输入范围: ${inputRange.$1} 到 ${inputRange.$2}');
  Logger.log('FeaturePointsData', 'calculateDeformation', '  • 输出范围: ${outputRange.$1} 到 ${outputRange.$2}');
  Logger.log('FeaturePointsData', 'calculateDeformation', '  • 当前参数值: ${paramValue.toStringAsFixed(3)}');
  
  Logger.log('FeaturePointsData', 'calculateDeformation', '📊 [DEBUG] 参数配置信息:');
  Logger.log('FeaturePointsData', 'calculateDeformation', '  • 主要点数量: ${paramConfig.primaryPoints.length}');
  Logger.log('FeaturePointsData', 'calculateDeformation', '  • 次要点数量: ${paramConfig.secondaryPoints.length}');
  Logger.log('FeaturePointsData', 'calculateDeformation', '  • 辅助点数量: ${paramConfig.auxiliaryPoints.length}');
  Logger.log('FeaturePointsData', 'calculateDeformation', '  • 变形配置数量: ${paramConfig.transformConfigs.length}');
  Logger.log('FeaturePointsData', 'calculateDeformation', '  • 变形配置点索引: ${paramConfig.transformConfigs.keys.toList()}');
  
  final Map<int, Offset> deformations = {};
  
  // 应用变形配置
  paramConfig.transformConfigs.forEach((pointIndex, config) {
    final mapping = paramConfig.intensityMapping;
    final normalizedValue = (paramValue - mapping.inputRange.$1) / (mapping.inputRange.$2 - mapping.inputRange.$1);
    final mappedValue = mapping.outputRange.$1 + normalizedValue * (mapping.outputRange.$2 - mapping.outputRange.$1);
    
    final direction = config.transformVector.direction;
    final magnitude = config.transformVector.magnitude * mappedValue;
    
    Logger.log('FeaturePointsData', 'calculateDeformation', '📊 [DEBUG] 点变形计算 | 点索引: $pointIndex | 方向: $direction | 强度: ${magnitude.toStringAsFixed(2)}');
    
    switch (direction) {
      case TransformDirection.horizontal:
        deformations[pointIndex] = Offset(magnitude, 0);
        break;
      case TransformDirection.vertical:
        deformations[pointIndex] = Offset(0, magnitude);
        break;
      case TransformDirection.diagonal:
        deformations[pointIndex] = Offset(magnitude * 0.7, magnitude * 0.7);
        break;
      case TransformDirection.depth:
        // 深度在2D上表现为水平和垂直的组合
        deformations[pointIndex] = Offset(magnitude * 0.3, magnitude * 0.7);
        break;
      case TransformDirection.radial:
        // 径向变形，从中心点向外扩散
        deformations[pointIndex] = Offset(magnitude * 0.5, magnitude * 0.5);
        break;
      case TransformDirection.custom:
        // 自定义方向，默认使用水平方向
        deformations[pointIndex] = Offset(magnitude, 0);
        break;
    }
  });
  
  // 简化的结果统计
  int nonZeroDeformations = 0;
  
  // 只计算非零变形的数量
  for (final offset in deformations.values) {
    final magnitude = sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
    if (magnitude > 0.01) { // 忽略非常小的变形
      nonZeroDeformations++;
    }
  }
  
  // 简化的日志输出
  final duration = DateTime.now().difference(startTime);
  Logger.log('FeaturePointsData', 'calculateDeformation', '✅ 基本变形计算完成 | 区域: $areaName | 参数: $paramName | 有效点数: $nonZeroDeformations / ${deformations.length} | 耗时: ${duration.inMilliseconds}ms');
  
  // 简化版本不再输出变形数据样本
  
  return deformations;
}

/// 面部轮廓变形计算器
/// 负责计算不同面部轮廓参数的变形数值
class FaceContourTransformCalculator {
  /// 计算轮廓收紧变形
  static Map<int, Offset> calculateContourTightenDeformation(double paramValue) {
    // 日志输出
    Logger.log('FaceContourTransformCalculator', 'calculateContourTightenDeformation', '⏱️ 开始计算轮廓收紧变形 | 参数值: $paramValue');
    
    // 变形强度映射
    final intensity = _mapIntensity(paramValue, 0.0, 16.0);
    
    // 返回变形结果
    final result = {
      // 下颌角
      67: Offset(-intensity * 0.9, 0),  // 左下颌角
      297: Offset(intensity * 0.9, 0),  // 右下颌角
      
      // 面颊中部
      109: Offset(-intensity * 0.8, 0),  // 左面颊
      338: Offset(intensity * 0.8, 0),   // 右面颊
      
      // 下巴区域
      152: Offset(0, 0),  // 下巴中心点保持不变
    };
    
    Logger.log('FaceContourTransformCalculator', 'calculateContourTightenDeformation', '✅ 完成轮廓收紧变形计算 | 点数: ${result.length}');
    return result;
  }
  
  /// 计算下巴调整变形
  static Map<int, Offset> calculateChinAdjustDeformation(double paramValue) {
    // 日志输出
    Logger.log('FaceContourTransformCalculator', 'calculateChinAdjustDeformation', '⏱️ 开始计算下巴调整变形 | 参数值: $paramValue');
    
    // 变形强度映射
    final intensity = _mapIntensity(paramValue, 0.0, 12.0);
    
    // 返回变形结果
    final result = {
      // 下巴区域
      152: Offset(0, intensity),  // 下巴中心点
      175: Offset(0, intensity * 0.8),  // 下巴上部
      
      // 下巴轮廓
      148: Offset(-intensity * 0.3, intensity * 0.7),  // 左侧下巴轮廓
      149: Offset(-intensity * 0.2, intensity * 0.8),
      150: Offset(-intensity * 0.1, intensity * 0.9),
      151: Offset(0, intensity),
      
      377: Offset(intensity * 0.3, intensity * 0.7),  // 右侧下巴轮廓
      378: Offset(intensity * 0.2, intensity * 0.8),
      379: Offset(intensity * 0.1, intensity * 0.9),
    };
    
    Logger.log('FaceContourTransformCalculator', 'calculateChinAdjustDeformation', '✅ 完成下巴调整变形计算 | 点数: ${result.length}');
    return result;
  }
  
  
  /// 计算脸型优化变形
  static Map<int, Offset> calculateFaceShapeDeformation(double paramValue) {
    // 日志输出
    Logger.log('FaceContourTransformCalculator', 'calculateFaceShapeDeformation', '⏱️ 开始计算脸型优化变形 | 参数值: $paramValue');
    
    // 变形强度映射
    final intensity = _mapIntensity(paramValue, 0.0, 20.0);
    
    // 返回变形结果
    final result = {
      // 下巴中心点
      152: Offset(0, intensity * 0.5),
      
      // 下颌角
      67: Offset(-intensity * 0.9, 0),  // 左下颌角
      297: Offset(intensity * 0.9, 0),  // 右下颌角
      
      // 面颊中部
      109: Offset(-intensity * 0.7, 0),  // 左面颊
      338: Offset(intensity * 0.7, 0),   // 右面颊
      
      // 颧骨区域
      216: Offset(-intensity * 0.6, 0),  // 左颧骨主点
      436: Offset(intensity * 0.6, 0),   // 右颧骨主点
    };
    
    Logger.log('FaceContourTransformCalculator', 'calculateFaceShapeDeformation', '✅ 完成脸型优化变形计算 | 点数: ${result.length}');
    return result;
  }
  
  /// 映射参数值到变形强度
  static double _mapIntensity(double paramValue, double minOutput, double maxOutput) {
    // 参数值范围：-1.0 到 1.0
    // 只处理正向变形（瘦脸效果）
    if (paramValue <= 0) return 0;
    
    // 二次曲线映射，使变形更自然
    final normalizedValue = paramValue;  // 已经在 0-1 范围内
    final quadraticValue = normalizedValue * normalizedValue;
    
    // 映射到输出范围
    return minOutput + (maxOutput - minOutput) * quadraticValue;
  }
}
