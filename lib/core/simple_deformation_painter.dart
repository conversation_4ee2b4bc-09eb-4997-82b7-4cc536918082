import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:math' show sqrt, min, max, pi, sin, cos;
import '../utils/logger.dart';
import 'models/feature_point.dart';  
import '../beautify_feature/services/transformation_service.dart';
import 'deformation_utils.dart';
import 'feature_point_manager.dart';
import 'deformation_area_visualizer.dart';
import 'deformation_area_visualizer_nose.dart';
import 'transformations/transformation_factory.dart';
// 【架构清理】移除直接导入特定策略类，统一通过TransformationFactory获取
import 'deformation_cache_manager.dart';
import 'deformation_direction_handler.dart'; // 导入DeformationDirectionHandler类
import 'parameter_value_manager.dart'; // 导入参数值管理器
import 'image_size_manager.dart'; // 导入图像尺寸管理器
import 'transform_type.dart'; // 导入变形类型枚举
// 【架构清理】移除通用特征点辅助工具，所有功能已整合到策略类中

/// 简化版变形区域绘制器
class SimpleDeformationPainter extends CustomPainter {
  static const String _logTag = 'SimpleDeformationPainter';
  
  // 区域类型
  String? _areaType;
  String? get areaType => _areaType;
  set areaType(String? value) {
    if (_areaType != value) {
      _areaType = value;
      // 【修复】移除强制变形触发，区域切换不应该触发变形计算
      // _hasUpdatedFeaturePoints = false;  // 这会意外触发变形
      _log('设置区域类型', '🔧 [设置] 区域类型: $value');
    }
  }
  
  // 参数切换标志，用于标识当前是否处于参数切换状态
  bool _isParameterSwitching = false;
  
  // 移除不必要的状态标志，简化变形逻辑
  
  // 是否跳过保存缓存，避免重复保存
  bool _skipSaveCache = false;

  // 参数值管理器 - 管理所有参数的当前值
  late ParameterValueManager _parameterValueManager;

  // 参数名称
  String? _parameterName;
  String? get parameterName => _parameterName;
  set parameterName(String? value) {
    if (_parameterName != value) {
      // 保存旧参数名称
      String? oldParameterName = _parameterName;
      
      _parameterName = value;
      
      // 从参数值管理器中恢复参数值
      double initialValue = 0.0;
      // 【修复】直接使用参数名称，不包含区域信息
      // 这样确保相同参数名在不同区域间共享历史值，避免参数切换时的意外变形
      
      if (value != null && _parameterValueManager.containsParameter(value)) {
        // 如果参数值管理器中存在该参数的历史值，则使用该值
        _parameterValue = _parameterValueManager.getValue(value);
        initialValue = _parameterValue;
      } else {
        // 如果参数值管理器中不存在该参数的历史值，则设为0.0
        _parameterValue = 0.0;
        initialValue = 0.0;
      }
      
      // 重要：将历史值设置为初始值，确保第一次调整时方向判断正确
      _parameterHistoryValue = initialValue;
      
      // 更新变形方向处理器中的当前参数和历史记录
      DeformationDirectionHandler deformationDirectionHandler = DeformationDirectionHandler();
      deformationDirectionHandler.setCurrentParameter(
        _areaType, 
        value, 
        initialValue: initialValue
      );
      
      // 不清空特征点缓存，确保已有变形效果不丢失
      
      _log('设置参数名称', '🔧 [设置] 参数名称: $oldParameterName -> $value');
      
      // 【修复】仅标记需要重绘，不强制触发变形计算
      // 变形计算应该只在用户实际点击加减号按钮时才触发
      _needsRepaint = true;
      // 移除强制变形触发：_hasUpdatedFeaturePoints = false;
    }
  }
  
  // 参数值
  double _parameterValue = 0.0;
  // 当前参数项的历史值，用于判断变形方向
  double _parameterHistoryValue = 0.0;
  // 移除不必要的状态标志 _isFirstValueAfterSwitch
  // 参数值变化方向（增大或减小）
  String _valueChangeDirection = 'no_change'; // 变形方向使用英文标识，与其他变形策略保持一致
  
  double get parameterValue => _parameterValue;
  
  /// 设置参数值
  /// 注意：由于 Dart 中的 setter 不能有命名参数，所以如果需要指定变形方向，请使用 setParameterValueWithDirection 方法
  set parameterValue(double value) {
    // 调用带方向参数的方法，但不指定方向（兼容旧代码）
    setParameterValueWithDirection(value);
  }
  
  /// 设置参数值，并指定变形方向
  /// @param value 参数值
  /// @param isIncreasing 可选参数，表示用户点击的是加号还是减号按钮，true 表示点击加号，false 表示点击减号
  void setParameterValueWithDirection(double value, {bool? isIncreasing}) {
    if (_parameterValue != value) {
      // 记录当前值作为历史值，用于日志记录和变化量计算
      _parameterHistoryValue = _parameterValue;
      
      // 【修复】变形方向判断 - 唯一根据用户实际点击的加号还是减号来确定
      if (isIncreasing != null) {
        // 如果提供了 isIncreasing 参数，说明是用户真实的点击操作
        _valueChangeDirection = isIncreasing ? 'increase' : 'decrease'; // 使用英文标识变形方向
        Logger.flow(_logTag, 'parameterValue', '✅ 用户点击操作: ${isIncreasing ? "加号" : "减号"}, 设置变形方向为: $_valueChangeDirection');
      } else {
        // 【关键修复】如果没有提供 isIncreasing 参数，说明不是用户点击操作
        // 这种情况下不应该触发变形，设为 'no_change'
        _valueChangeDirection = 'no_change';
        Logger.flow(_logTag, 'parameterValue', '⚠️ 非用户点击操作，不设置变形方向，保持为: no_change');
      }
      
      // 更新参数值
      _parameterValue = value;
      
      // 更新参数值管理器
    if (_parameterName != null && _parameterName!.isNotEmpty) {
      // 【修复】直接使用参数名称，不包含区域信息
      // 这样确保相同参数名在不同区域间共享值，避免重复存储
      
      // 只有当参数值非零时才保存到参数值管理器中
      if (value != 0.0) {
        _parameterValueManager.setValue(_parameterName!, value);
      } else {
        // 如果参数值为零，从参数值管理器中移除
        if (_parameterValueManager.containsParameter(_parameterName!)) {
          _parameterValueManager.removeValue(_parameterName!);
        }
      }
      
      // 查询缓存，避免重复变形计算
      // 使用缓存检查标记避免同一帧重复查询
      if (!_cacheCheckedThisFrame) {
        Map<String, double> queryParams = _parameterValueManager.getAllParameters();
        
        // 查询缓存
        _cachedResult = _deformationCacheManager.find(queryParams);
        _cacheCheckedThisFrame = true;
        
        if (_cachedResult != null) {
          // 标记特征点需要更新
          _featurePointsNeedUpdate = true;
        }
      }
        
        // 更新变形方向处理器中的参数值记录
        DeformationDirectionHandler deformationDirectionHandler = DeformationDirectionHandler();
        deformationDirectionHandler.checkParameterSwitching(
          _parameterName ?? '',
          value,
          _parameterHistoryValue,
          _valueChangeDirection
        );
      }
      
      // 标记特征点需要更新，触发变形计算
      _hasUpdatedFeaturePoints = false;
      _needsRepaint = true; // 强制重绘
      _log('设置参数值', '🔧 [设置] 参数值: $value');
    }
  }
  
  // 强度
  double _intensity = 1.0;
  double get intensity => _intensity;
  set intensity(double value) {
    if (_intensity != value) {
      _intensity = value;
      _log('设置强度', '🔧 [设置] 强度: $value');
    }
  }
  
  // 可见性
  bool _isVisible = false;
  bool get isVisible => _isVisible;
  set isVisible(bool value) {
    if (_isVisible != value) {
      _isVisible = value;
      _needsRepaint = true;
      _log('设置可见性', '🔧 [设置] 可见性: $value');
    }
  }
  
  // 是否显示变形区域可视化（与实际变形效果无关）
  bool _showDeformationArea = true;  // 修改默认值为 true，确保变形区域可视化显示
  bool get showDeformationArea => _showDeformationArea;
  set showDeformationArea(bool value) {
    if (_showDeformationArea != value) {
      _showDeformationArea = value;
      _needsRepaint = true;
      _log('设置显示变形区域可视化', '🔧 [设置] 显示变形区域可视化: $value');
    }
  }
  
  // 图像尺寸
  Size _imageSize = Size.zero;
  
  // 图像尺寸管理器，确保变形过程中图像尺寸保持不变
  final ImageSizeManager _imageSizeManager = ImageSizeManager();
  
  // 图像
  final ui.Image? _image;
  
  // 侧面图像
  final ui.Image? _sideImage;
  
  // 是否显示坐标系
  bool _showCoordinateSystem = false;
  bool get showCoordinateSystem => _showCoordinateSystem;
  set showCoordinateSystem(bool value) {
    if (_showCoordinateSystem != value) {
      _showCoordinateSystem = value;
      _log('设置显示坐标系', '🔧 [设置] 显示坐标系: $value');
    }
  }
  
  // 是否显示特征点
  bool _showFeaturePoints = true;
  bool get showFeaturePoints => _showFeaturePoints;
  set showFeaturePoints(bool value) {
    if (_showFeaturePoints != value) {
      _showFeaturePoints = value;
      _log('设置显示特征点', '🔧 [设置] 显示特征点: $value');
    }
  }
  
  // 是否显示调试信息
  bool _showDebugInfo = false;
  bool get showDebugInfo => _showDebugInfo;
  set showDebugInfo(bool value) {
    if (_showDebugInfo != value) {
      _showDebugInfo = value;
      _log('设置显示调试信息', '🔧 [设置] 显示调试信息: $value');
    }
  }
  
  // 变形类型
  TransformType _transformType = TransformType.default_;
  TransformType get transformType => _transformType;
  set transformType(TransformType value) {
    if (_transformType != value) {
      _transformType = value;
      _hasUpdatedFeaturePoints = false;  // 重置标志，确保在变形类型变化时更新特征点
      _log('设置变形类型', '🔧 [设置] 变形类型: $value');
    }
  }
  
  // 特征点管理器
  final FeaturePointManager _featurePointManager;
  
  // 变形服务
  final TransformationService _transformationService;
  

  
  // 是否已经更新特征点
  bool _hasUpdatedFeaturePoints = false;
  
  // 特征点是否需要更新
  bool _featurePointsNeedUpdate = true;
  
  // 特征点缓存
  List<FeaturePoint>? _featurePoints;
  List<FeaturePoint>? _deformedFeaturePoints; // 变形后的特征点
  
  // 累积变形图像 - 保存所有参数变形累积后的图像
  ui.Image? _deformedImage;
  
  // 标记是否有累积变形状态
  bool _hasAccumulatedState = false; // 存储累积变形后的图像
  
  // 变形结果回调函数，用于将变形结果回传给SimpleDeformationRenderer
  final Function(ui.Image? deformedImage, List<FeaturePoint>? deformedFeaturePoints)? _onDeformationResultCallback;
  
  // 高亮特征点列表
  List<int> _highlightPointIds = [];
  List<int> get highlightPointIds => _highlightPointIds;
  set highlightPointIds(List<int> value) {
    if (!_areListsEqual(_highlightPointIds, value)) {
      _highlightPointIds = List<int>.from(value);
      _needsRepaint = true;
      _log('设置高亮点ID', '🔧 [设置] 高亮点ID: $value');
    }
  }
  
  // 呼吸动画值
  double _breathingValue = 0.5;
  double get breathingValue => _breathingValue;
  set breathingValue(double value) {
    // 添加阈值检查，减少不必要的重绘
    const double threshold = 0.01; // 1%的变化阈值
    if ((_breathingValue - value).abs() > threshold) {
      _breathingValue = value;
      // 限制重绘频率：每100ms最多重绘一次
      final now = DateTime.now();
      if (now.difference(_lastBreathingUpdateTime).inMilliseconds > 100) {
        _needsRepaint = true;
        _lastBreathingUpdateTime = now;
      }
    }
  }
  
  // 上次呼吸动画更新时间
  DateTime _lastBreathingUpdateTime = DateTime.now();

  /// 呼吸动画方向，true为增大，false为减小
  bool _breathingDirection = true;

  /// 更新呼吸动画值 - 只在必要时更新，不触发重绘
  void _updateBreathingValue() {
    // 如果呼吸动画未启用，直接返回
    if (!_enableBreathingAnimation) {
      if (_breathingValue != 1.0) {
        _breathingValue = 1.0;
      }
      return;
    }

    // 获取当前时间
    final now = DateTime.now();

    // 使用正弦函数模拟呼吸效果，实现从完全透明到完全可见的变化
    // 周期为2秒，让变化更加丰富和明显
    final double period = 2000.0; // 2秒周期
    final double normalizedTime = (now.millisecondsSinceEpoch % period.toInt()) / period;
    final double sinValue = math.sin(normalizedTime * 2 * math.pi);

    // 映射到 0.2 - 1.0 的范围，确保最暗时也有轻微可见度，最亮时完全不透明
    // 调整为0.2到1.0的范围，确保特征点始终有一定可见度
    final double newValue = 0.2 + 0.8 * ((sinValue + 1) / 2);

    // 确保值在有效范围内
    final double clampedValue = math.max(0.2, math.min(1.0, newValue));

    // 只有当值发生较大变化时才更新
    // 增加变化阈值，显著减少更新频率
    if ((_breathingValue - clampedValue).abs() > 0.1) {
      _breathingValue = clampedValue;
    }

    // 不需要在这里输出日志，避免频繁输出

    // 更新时间戳
    _lastBreathingUpdateTime = now;
  }
  
  // 是否启用呼吸动画 - 默认启用
  bool _enableBreathingAnimation = true;
  
  // 是否需要重绘
  bool _needsRepaint = false;
  
  // 最后一次请求重绘的时间
  DateTime? _lastRepaintRequestTime;
  
  // 缓存检查标记，避免同一帧内重复查询缓存
  bool _cacheCheckedThisFrame = false;
  
  // 当前参数特征点索引
  List<int> _currentParameterPointIndexes = [];
  List<int> get currentParameterPointIndexes => _currentParameterPointIndexes;
  set currentParameterPointIndexes(List<int> value) {
    if (!_areListsEqual(_currentParameterPointIndexes, value)) {
      _currentParameterPointIndexes = List<int>.from(value);
      Logger.flow(_logTag, '设置当前参数特征点索引', '当前参数特征点索引: ${value.length}个');
      // 当特征点索引变化时，标记特征点需要更新
      invalidateFeaturePoints();
      _needsRepaint = true;
      _log('设置当前参数特征点索引', '🔧 [设置] 当前参数特征点索引: $value');
    }
  }
  
  // 面部中心线X坐标
  double? _facialCenterLineX;
  bool _facialCenterLineCalculated = false;
  double? _previousCenterLineX;
  
  // 上一次的参数值
  double? _previousParameterValue;
  
  // 参数值是否在增加（点击加号按钮）
  bool? _isParameterIncreasing;
  
  // 变形缓存管理器 - 使用单例实例
  final DeformationCacheManager _deformationCacheManager = DeformationCacheManager();
  
  // 预先查询的缓存结果，避免重复查询
  DeformationStateValue? _cachedResult;
  
  // 设置缓存结果
  set cachedResult(DeformationStateValue? value) {
    if (_cachedResult != value) {
      _cachedResult = value;
      if (_cachedResult != null) {
        Logger.flow(_logTag, 'setCachedResult', '✅ 更新缓存结果: 图像=${_cachedResult?.deformedImage != null ? "有效" : "无效"}, 特征点=${_cachedResult?.deformedFeaturePoints != null ? _cachedResult?.deformedFeaturePoints?.length : 0}个');
      } else {
        Logger.flow(_logTag, 'setCachedResult', '❌ 清除缓存结果');
      }
    }
  }
  
  /// 初始化图像尺寸管理器
  void _initializeImageSizeManager(ui.Image? image) {
    if (image != null) {
      _imageSizeManager.setOriginalImageSize(image);
      Logger.flow(_logTag, '_initializeImageSizeManager', '📏 初始化图像尺寸管理器: ${image.width}x${image.height}');
    }
  }

  /// 获取当前时间戳 HH:MM:SS 格式
  String _getTimeStamp() {
    final now = DateTime.now();
    return '[${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}]';
  }
  
  /// 构造函数
  SimpleDeformationPainter({
    required String? areaType,
    required String? parameterName,
    required double parameterValue,
    required double intensity,
    required bool isVisible,
    required bool showDeformationArea,
    required TransformationService transformationService,
    required FeaturePointManager featurePointManager,
    required ui.Image? image,
    required ui.Image? sideImage,
    required bool showCoordinateSystem,
    required bool showFeaturePoints,
    required bool showDebugInfo,
    required TransformType transformType,
    List<int> highlightPointIds = const [],
    double breathingValue = 1.0,
    bool enableBreathingAnimation = true,
    List<int> currentParameterPointIndexes = const [],
    double? facialCenterLineX,
    bool? facialCenterLineCalculated,
    ParameterValueManager? parameterValueManager,
    ui.Image? deformedImage,
    List<FeaturePoint>? deformedFeaturePoints,
    bool hasAccumulatedState = false,
    Function(ui.Image? deformedImage, List<FeaturePoint>? deformedFeaturePoints)? onDeformationResultCallback,

    DeformationStateValue? cachedResult,
    bool skipSaveCache = false,
    bool? isParameterIncreasing // 参数值是否在增加（点击加号按钮）
  }) : 
       _areaType = areaType,
       _parameterName = parameterName,
       _parameterValue = parameterValue,
       _intensity = intensity,
       _isVisible = isVisible,
       _showDeformationArea = showDeformationArea,
       _featurePointManager = featurePointManager,
       _transformationService = transformationService,
       _image = image,
       _sideImage = sideImage,
       _imageSize = image != null ? Size(image.width.toDouble(), image.height.toDouble()) : Size.zero,
       _highlightPointIds = highlightPointIds,
       _breathingValue = breathingValue,
       _enableBreathingAnimation = enableBreathingAnimation,
       _currentParameterPointIndexes = currentParameterPointIndexes,
       _onDeformationResultCallback = onDeformationResultCallback,

       _isParameterIncreasing = isParameterIncreasing,
       _showCoordinateSystem = showCoordinateSystem,
       _showFeaturePoints = showFeaturePoints,
       _showDebugInfo = showDebugInfo,
       _transformType = transformType {
    // 设置原始图像尺寸到图像尺寸管理器
    _initializeImageSizeManager(image);
    
    // 初始化参数值管理器
    if (parameterValueManager != null) {
      _parameterValueManager = parameterValueManager;
      Logger.flow(_logTag, 'constructor', '📈 使用外部提供的参数值管理器，参数数量: ${parameterValueManager.getAllParameters().length}');
    } else {
      // 如果没有提供参数值管理器，使用单例实例
      _parameterValueManager = ParameterValueManager();
      Logger.flow(_logTag, 'constructor', '📈 使用单例参数值管理器');
    }
    
    // 初始化累积变形状态
    _hasAccumulatedState = hasAccumulatedState;
    if (deformedImage != null) {
      _deformedImage = deformedImage;
    }
    if (deformedFeaturePoints != null) {
      _deformedFeaturePoints = deformedFeaturePoints;
    }
    
    if (_hasAccumulatedState) {
      String statusLog = '';
      if (_deformedImage != null) statusLog += '图像✅ ';
      if (_deformedFeaturePoints != null && _deformedFeaturePoints!.isNotEmpty) {
        statusLog += '特征点✅ (${_deformedFeaturePoints!.length}个)';
      }
      Logger.flow(_logTag, 'constructor', '📷 初始化累积变形状态: $statusLog');
    }
    
    // 属性已在初始化列表中设置，不需要再次设置
    Logger.flow(_logTag, 'constructor', '🔧 变形区域绘制器初始化完成');
    
    // 如果传入了面部中心线缓存，则使用传入的值
    if (facialCenterLineX != null && facialCenterLineCalculated != null && facialCenterLineCalculated) {
      _facialCenterLineX = facialCenterLineX;
      _facialCenterLineCalculated = facialCenterLineCalculated;
      Logger.flow(_logTag, 'SimpleDeformationPainter构造函数', '使用传入的面部中心线缓存: $_facialCenterLineX');
    }
    
    // 初始化预先查询的缓存结果
    _cachedResult = cachedResult;
    if (_cachedResult != null) {
      Logger.flow(_logTag, 'SimpleDeformationPainter构造函数', '✅ 使用预先查询的缓存结果，避免重复查询');
      Logger.flow(_logTag, 'SimpleDeformationPainter构造函数', '✅ 缓存结果信息: 图像=${_cachedResult?.deformedImage != null ? '有效' : '无效'}, 特征点=${_cachedResult?.deformedFeaturePoints != null ? _cachedResult?.deformedFeaturePoints?.length : 0}个');
    } else {
      Logger.flow(_logTag, 'SimpleDeformationPainter构造函数', '❌ 未提供预先查询的缓存结果');
    }
    
    // 初始化跳过保存缓存标志
    _skipSaveCache = skipSaveCache;
    if (_skipSaveCache) {
      Logger.flow(_logTag, 'SimpleDeformationPainter构造函数', '✅ 设置跳过保存缓存标志，避免重复保存');
    }
    
    _log('构造函数', '🔧 [创建] 变形区域绘制器');
    _log('设置当前参数特征点索引', '🔧 [设置] 当前参数特征点索引: $currentParameterPointIndexes');
  }
  
  /// 记录日志 - 简化版本，只记录关键信息
  void _log(String method, String message, {String type = 'ℹ️'}) {
    // 只在调试模式或关键操作时记录日志
    if (_showDebugInfo || type == '❌' || type == '⚠️') {
      final timestamp = _getTimeStamp();
      print('$timestamp 📋 变形绘制器 | $method | $type $message');
    }
  }
  
  // 上次绘制时间
  DateTime? _lastPaintTime;

  /// 检查特征点是否需要更新
  bool _needUpdateFeaturePoints() {
    // 如果标记了需要更新，直接返回true
    if (_featurePointsNeedUpdate) {
      return true;
    }
    
    // 如果特征点已经更新过，则不需要再次更新
    if (_hasUpdatedFeaturePoints && _featurePoints != null && _featurePoints!.isNotEmpty) {
      return false;
    }
    
    // 如果区域类型或参数名称为空，则不需要更新
    if (_areaType == null || _parameterName == null || _areaType!.isEmpty || _parameterName!.isEmpty) {
      return false;
    }
    
    // 检查当前特征点状态
    final currentPoints = _featurePointManager.getFeaturePoints();
    if (currentPoints != null && currentPoints.isNotEmpty) {
      
      // 检查当前特征点是否与当前区域类型和参数名称匹配
      final isMatchingArea = _areaType == _featurePointManager.areaType;
      final isMatchingParameter = _parameterName == _featurePointManager.parameterName;
      
      if (!isMatchingArea || !isMatchingParameter) {
        return true;
      }
      
      return false;
    }
    
    return true;
  }
  
  /// 获取特征点
  List<FeaturePoint>? _getFeaturePoints() {
    // 如果有缓存命中的变形特征点，优先使用缓存的特征点
    if (_deformedFeaturePoints != null && _deformedFeaturePoints!.isNotEmpty) {
      return _deformedFeaturePoints;
    }
    
    // 如果缓存有效且不需要更新，直接返回缓存的特征点
    if (!_featurePointsNeedUpdate && _featurePoints != null && _featurePoints!.isNotEmpty) {
      return _featurePoints;
    }
    
    // 检查是否需要更新特征点
    if (_needUpdateFeaturePoints()) {
      _updateFeaturePoints();
    }
    
    // 从特征点管理器获取特征点
    final points = _featurePointManager.getFeaturePoints();
    
    // 特征点为空检查 - 保留这个关键错误检查
    if (points == null || points.isEmpty) {
      Logger.flowError(_logTag, '_getFeaturePoints', '❌ 错误: 特征点数据为空，无法继续变形操作');
      return null;
    }
    
    // 更新缓存
    _featurePoints = points;
    _featurePointsNeedUpdate = false;
    _hasUpdatedFeaturePoints = true;
    
    return points;
  }
  
  /// 标记特征点需要更新
  void invalidateFeaturePoints() {
    _featurePointsNeedUpdate = true;
    _featurePoints = null;
  }
  
  /// 比较两个列表是否相等
  bool _areListsEqual(List<int> list1, List<int> list2) {
    if (list1.length != list2.length) return false;
    
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    
    return true;
  }
  
  /// 更新特征点状态，确保在变形计算前特征点已正确加载
  /// 返回特征点列表，如果无法获取特征点则返回 null
  List<FeaturePoint>? _updateFeaturePoints() {
    // 如果特征点已经更新，直接返回，避免重复检查
    if (_hasUpdatedFeaturePoints) {
      return null;
    }
    
    Logger.flowStart(_logTag, '_updateFeaturePoints');
    
    // 检查当前特征点状态
    final currentPoints = _featurePointManager.getFeaturePoints();
    if (currentPoints != null && currentPoints.isNotEmpty) {
      Logger.flow(_logTag, '_updateFeaturePoints', '特征点管理器已有特征点，数量: ${currentPoints.length}');
      _hasUpdatedFeaturePoints = true;
      Logger.flowEnd(_logTag, '_updateFeaturePoints');
      return null; // 如果已有特征点，直接返回，不需要重复获取
    }
    
    Logger.flow(_logTag, '_updateFeaturePoints', '当前无特征点，将尝试从变形服务获取');
    
    if (_areaType == null || _parameterName == null || _areaType!.isEmpty || _parameterName!.isEmpty) {
      Logger.flowError(_logTag, '_updateFeaturePoints', '区域类型或参数名称为空，无法更新特征点');
      Logger.flowEnd(_logTag, '_updateFeaturePoints');
      return null;
    }
    
    // 确保特征点管理器中的区域类型和参数名称已正确设置
    _featurePointManager.setAreaType(_areaType!);
    _featurePointManager.setParameterName(_parameterName!);
    
    // 检查当前特征点状态
    final currentPointsAfterSetting = _featurePointManager.getFeaturePoints();
    if (currentPointsAfterSetting != null && currentPointsAfterSetting.isNotEmpty) {
      // 获取当前参数的特征点索引
      final parameterPointIndexes = _featurePointManager.getCurrentParameterPointIndexes();
      
      // 统一使用特征点管理器的逻辑，不再硬编码特殊处理
      if (parameterPointIndexes.isNotEmpty) {
        currentParameterPointIndexes = parameterPointIndexes;
      }
      Logger.flow(_logTag, '_updateFeaturePoints', '特征点管理器已有特征点，总数量: ${currentPointsAfterSetting.length}, 当前参数(${_parameterName})特征点数量: ${parameterPointIndexes.length}');
      _hasUpdatedFeaturePoints = true;
    }
    
    Logger.flowEnd(_logTag, '_updateFeaturePoints');
  }
  
  // 上次绘制时的呼吸值，用于避免因呼吸动画触发重绘
  double _lastPaintBreathingValue = 1.0;
  
  @override
  void paint(Canvas canvas, Size size) {
    final now = DateTime.now();
    
    // 重置缓存检查标记，确保每一帧都重新评估缓存状态
    _cacheCheckedThisFrame = false;
    
    // 检查是否只是呼吸值变化导致的重绘
    bool isOnlyBreathingValueChanged = (_lastPaintBreathingValue != _breathingValue) && 
                                     (_lastPaintTime != null) && 
                                     (now.difference(_lastPaintTime!).inMilliseconds < 100);
    
    // 如果是呼吸动画触发的重绘，只更新特征点的显示，不重绘整个图像
    if (isOnlyBreathingValueChanged && _deformedImage != null) {
      // 记录当前绘制时间
      _lastPaintTime = now;
      // 记录当前呼吸值
      _lastPaintBreathingValue = _breathingValue;
      
      // 直接绘制现有图像，不执行变形计算
      if (_deformedImage != null) {
        // 使用原生的drawImageRect方法绘制图像
        final srcRect = Rect.fromLTWH(0, 0, _deformedImage!.width.toDouble(), _deformedImage!.height.toDouble());
        final dstRect = Rect.fromLTWH(0, 0, size.width, size.height);
        canvas.drawImageRect(_deformedImage!, srcRect, dstRect, Paint()..filterQuality = FilterQuality.high);
      }
      
      // 只绘制特征点，不执行变形计算
      // 注意：_drawFeaturePoints方法已移除，特征点绘制由其他方式处理
      if (_showFeaturePoints && _featurePointManager != null) {
        // 特征点绘制逻辑已移至其他实现中
      }
      
      return;
    }
    
    // 记录当前呼吸值
    _lastPaintBreathingValue = _breathingValue;
    
    // 增强的防抖动机制，避免短时间内多次执行变形操作
    if (_lastPaintTime != null) {
      final timeDiff = now.difference(_lastPaintTime!);
      
      // 如果在短时间内被再次调用，且已有变形图像，直接使用已有图像
      if (timeDiff.inMilliseconds < 200) { // 将防抖时间增加到200毫秒
        // 记录防抖动日志
        Logger.flow(_logTag, 'paint', '⏱️ 防抖动生效: 距离上次绘制 ${timeDiff.inMilliseconds}ms');
        
        // 【关键修复】在防抖动情况下也要检查缓存结果，确保显示最新的变形图像
        if (_cachedResult != null && _cachedResult!.deformedImage != null) {
          print('🎯 【防抖动修复】使用缓存结果图像绘制: ${_cachedResult!.deformedImage.hashCode}');
          _drawScaledImage(canvas, size, _cachedResult!.deformedImage!);
          _lastPaintTime = now;
          return;
        }
        
        // 只绘制当前状态，不执行新的变形计算
        if (_deformedImage != null) {
          Logger.flow(_logTag, 'paint', '📷 使用现有图像绘制，跳过变形计算');
          print('🎯 【防抖动修复】使用现有图像绘制: ${_deformedImage!.hashCode}');
          _drawScaledImage(canvas, size, _deformedImage!);
          
          // 不要忘记更新上次绘制时间，确保防抖动持续生效
          _lastPaintTime = now;
          return;
        }
      }
    }
    
    // 记录当前绘制时间
    _lastPaintTime = now;
    
    // 检查可见性
    if (!_isVisible) {
      return null;
    }
    
    // 【关键修复】检查是否已经有缓存结果，如果有则优先使用
    bool cacheApplied = false;
    if (_cachedResult != null && _cachedResult!.deformedImage != null) {
      _deformedImage = _cachedResult!.deformedImage;
      _hasAccumulatedState = true;
      cacheApplied = true;
      
      // 【关键验证日志】确认主图区正在使用正确的变形图像
      print('═══════════════════════════════════════════════════════════');
      print('🎯 【关键验证】主图区绘制器缓存命中');
      print('   ✅ 缓存图像哈希: ${_cachedResult!.deformedImage.hashCode}');
      print('   ✅ 图像尺寸: ${_cachedResult!.deformedImage!.width}x${_cachedResult!.deformedImage!.height}');
      print('   ✅ 参数名称: $_parameterName');
      print('   ✅ 参数值: $_parameterValue');
      print('   ✅ 即将绘制到主图区: 是');
      print('═══════════════════════════════════════════════════════════');
      
      // 使用简洁的日志记录变形图像信息，避免重复输出
      Logger.flow(_logTag, '_applyImageDeformation', '📷 变形图像: 哈希码=${_cachedResult!.deformedImage.hashCode}, 尺寸=${_cachedResult!.deformedImage!.width}x${_cachedResult!.deformedImage!.height}, 参数=${_parameterName}:${_parameterValue}');
      
      if (_cachedResult!.deformedFeaturePoints.isNotEmpty) {
        Logger.flow(_logTag, 'paint', '✅ 使用缓存命中的变形特征点: ${_cachedResult!.deformedFeaturePoints.length}个点');
        _deformedFeaturePoints = _cachedResult!.deformedFeaturePoints;
        
        // 确保特征点管理器也使用缓存的特征点，但只在需要时更新
        _featurePointManager.updateFeaturePoints(_cachedResult!.deformedFeaturePoints);
        Logger.flow(_logTag, 'paint', '✅ 更新特征点管理器使用缓存特征点');
      }
      
      // 在缓存命中时，直接绘制缓存图像，不进行变形计算
      // 在绘制图像前先正确清除画布，避免图像重叠
      Logger.flow(_logTag, 'paint', '🧙‍♂️ 正在清除画布（缓存命中） - 画布尺寸: ${size.width.toInt()}x${size.height.toInt()}');
      canvas.save();
      canvas.clipRect(Rect.fromLTWH(0, 0, size.width, size.height));
      canvas.drawRect(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Paint()..color = Colors.white..blendMode = BlendMode.src
      );
      canvas.restore();
      Logger.flow(_logTag, 'paint', '✅ 画布清除完成（缓存命中）');
      
      // 记录图像尺寸信息，用于调试
      Logger.flow(_logTag, 'paint', '📷 缓存图像尺寸信息: ${_cachedResult!.deformedImage!.width}x${_cachedResult!.deformedImage!.height}, 画布尺寸: ${size.width.toInt()}x${size.height.toInt()}');
      
      // 绘制缓存图像（确保正确缩放）
      final image = _cachedResult!.deformedImage!;
      final srcRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
      
      // 计算保持宽高比的目标矩形
      final imageAspectRatio = image.width / image.height;
      final canvasAspectRatio = size.width / size.height;
      
      Rect dstRect;
      if (imageAspectRatio > canvasAspectRatio) {
        // 图像比画布更宽，以宽度为准
        final scaledHeight = size.width / imageAspectRatio;
        final offsetY = (size.height - scaledHeight) / 2;
        dstRect = Rect.fromLTWH(0, offsetY, size.width, scaledHeight);
      } else {
        // 图像比画布更高，以高度为准
        final scaledWidth = size.height * imageAspectRatio;
        final offsetX = (size.width - scaledWidth) / 2;
        dstRect = Rect.fromLTWH(offsetX, 0, scaledWidth, size.height);
      }
      
      Logger.flow(_logTag, 'paint', '📏 绘制矩形信息 - 画布: ${size.width.toInt()}x${size.height.toInt()}, 图像: ${image.width}x${image.height}');
      Logger.flow(_logTag, 'paint', '📏 宽高比 - 画布: ${canvasAspectRatio.toStringAsFixed(2)}, 图像: ${imageAspectRatio.toStringAsFixed(2)}');
      Logger.flow(_logTag, 'paint', '📏 目标矩形: ${dstRect.left.toInt()},${dstRect.top.toInt()} ${dstRect.width.toInt()}x${dstRect.height.toInt()}');
      
      canvas.drawImageRect(image, srcRect, dstRect, Paint()..filterQuality = FilterQuality.high);
      Logger.flow(_logTag, 'paint', '💾 绘制缓存图像（保持宽高比）');
      
      // 【关键验证日志】确认图像已绘制到主图区画布
      print('═══════════════════════════════════════════════════════════');
      print('🎯 【关键验证】主图区图像绘制完成');
      print('   ✅ 绘制状态: 成功完成');
      print('   ✅ 图像哈希: ${image.hashCode}');
      print('   ✅ 源矩形: ${srcRect.width.toInt()}x${srcRect.height.toInt()}');
      print('   ✅ 目标矩形: ${dstRect.left.toInt()},${dstRect.top.toInt()} ${dstRect.width.toInt()}x${dstRect.height.toInt()}');
      print('   ✅ 画布尺寸: ${size.width.toInt()}x${size.height.toInt()}');
      print('   ✅ 主图区应显示: 鼻尖调整变形效果');
      print('═══════════════════════════════════════════════════════════');
      
      // 通过回调函数将缓存图像和特征点传递给 SimpleDeformationRenderer
      if (_onDeformationResultCallback != null) {
        _onDeformationResultCallback!(_cachedResult!.deformedImage, _cachedResult!.deformedFeaturePoints);
        Logger.flow(_logTag, 'paint', '🔔 通过回调函数将缓存图像和特征点传递给 SimpleDeformationRenderer');
      }
      
      // 【修复】不清除缓存结果，保持状态持续性
      // _cachedResult = null; // 移除这行，避免意外清除缓存
      
      // 在缓存命中时，直接返回，跳过后续的变形计算
      Logger.flow(_logTag, 'paint', '✅ 缓存命中，跳过变形计算');
      Logger.flowEnd(_logTag, 'paint');
      return null;
    }
    // 如果没有缓存结果，但有构造函数传入的变形图像和特征点，则使用它们
    else if (_deformedImage != null && !cacheApplied) {
      Logger.flow(_logTag, 'paint', '✅ 使用构造函数传入的变形图像: 哈希码=${_deformedImage.hashCode}, 尺寸=${_deformedImage!.width}x${_deformedImage!.height}');
      _hasAccumulatedState = true;
      cacheApplied = true;
      
      // 在绘制图像前先正确清除画布，避免图像重叠
      canvas.save();
      canvas.clipRect(Rect.fromLTWH(0, 0, size.width, size.height));
      canvas.drawRect(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Paint()..color = Colors.white..blendMode = BlendMode.src
      );
      canvas.restore();
      
      // 绘制变形图像
      final image = _deformedImage!;
      final srcRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
      
      // 计算保持宽高比的目标矩形
      final imageAspectRatio = image.width / image.height;
      final canvasAspectRatio = size.width / size.height;
      
      Rect dstRect;
      if (imageAspectRatio > canvasAspectRatio) {
        // 图像比画布更宽，以宽度为准
        final scaledHeight = size.width / imageAspectRatio;
        final offsetY = (size.height - scaledHeight) / 2;
        dstRect = Rect.fromLTWH(0, offsetY, size.width, scaledHeight);
      } else {
        // 图像比画布更高，以高度为准
        final scaledWidth = size.height * imageAspectRatio;
        final offsetX = (size.width - scaledWidth) / 2;
        dstRect = Rect.fromLTWH(offsetX, 0, scaledWidth, size.height);
      }
      
      Logger.flow(_logTag, 'paint', '📏 绘制矩形信息 - 画布: ${size.width.toInt()}x${size.height.toInt()}, 图像: ${image.width}x${image.height}');
      Logger.flow(_logTag, 'paint', '📏 宽高比 - 画布: ${canvasAspectRatio.toStringAsFixed(2)}, 图像: ${imageAspectRatio.toStringAsFixed(2)}');
      Logger.flow(_logTag, 'paint', '📏 目标矩形: ${dstRect.left.toInt()},${dstRect.top.toInt()} ${dstRect.width.toInt()}x${dstRect.height.toInt()}');
      
      canvas.drawImageRect(image, srcRect, dstRect, Paint()..filterQuality = FilterQuality.high);
      Logger.flow(_logTag, 'paint', '💾 绘制变形图像（保持宽高比）');
      
      if (_deformedFeaturePoints != null && _deformedFeaturePoints!.isNotEmpty) {
        Logger.flow(_logTag, 'paint', '✅ 使用构造函数传入的变形特征点: ${_deformedFeaturePoints!.length}个点');
        
        // 确保特征点管理器也使用构造函数传入的特征点，但只在需要时更新
        if (_featurePointsNeedUpdate) {
          _featurePointManager.updateFeaturePoints(_deformedFeaturePoints!);
          _featurePointsNeedUpdate = false;
          Logger.flow(_logTag, 'paint', '✅ 更新特征点管理器使用构造函数传入的特征点');
        }
      }
    }
    // 如果既没有缓存结果，也没有构造函数传入的变形图像和特征点，则尝试从共享变形状态中获取
    else if (!cacheApplied) {
      // 检查是否有共享的变形图像
      ui.Image? sharedImage = DeformationCacheManager.getLatestDeformedImage();
      List<FeaturePoint>? sharedFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
      
      if (sharedImage != null) {
        Logger.flow(_logTag, 'paint', '📷 使用共享变形图像: 哈希码=${sharedImage.hashCode}, 尺寸=${sharedImage.width}x${sharedImage.height}');
        _deformedImage = sharedImage;
        _hasAccumulatedState = true;
        cacheApplied = true;
        
        // 在绘制图像前先正确清除画布，避免图像重叠
        canvas.save();
        canvas.clipRect(Rect.fromLTWH(0, 0, size.width, size.height));
        canvas.drawRect(
          Rect.fromLTWH(0, 0, size.width, size.height),
          Paint()..color = Colors.white..blendMode = BlendMode.src
        );
        canvas.restore();
        
        // 绘制共享变形图像
        final image = sharedImage;
        final srcRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
        
        // 计算保持宽高比的目标矩形
        final imageAspectRatio = image.width / image.height;
        final canvasAspectRatio = size.width / size.height;
        
        Rect dstRect;
        if (imageAspectRatio > canvasAspectRatio) {
          // 图像比画布更宽，以宽度为准
          final scaledHeight = size.width / imageAspectRatio;
          final offsetY = (size.height - scaledHeight) / 2;
          dstRect = Rect.fromLTWH(0, offsetY, size.width, scaledHeight);
        } else {
          // 图像比画布更高，以高度为准
          final scaledWidth = size.height * imageAspectRatio;
          final offsetX = (size.width - scaledWidth) / 2;
          dstRect = Rect.fromLTWH(offsetX, 0, scaledWidth, size.height);
        }
        
        Logger.flow(_logTag, 'paint', '📏 绘制矩形信息 - 画布: ${size.width.toInt()}x${size.height.toInt()}, 图像: ${image.width}x${image.height}');
        Logger.flow(_logTag, 'paint', '📏 宽高比 - 画布: ${canvasAspectRatio.toStringAsFixed(2)}, 图像: ${imageAspectRatio.toStringAsFixed(2)}');
        Logger.flow(_logTag, 'paint', '📏 目标矩形: ${dstRect.left.toInt()},${dstRect.top.toInt()} ${dstRect.width.toInt()}x${dstRect.height.toInt()}');
        
        canvas.drawImageRect(image, srcRect, dstRect, Paint()..filterQuality = FilterQuality.high);
        Logger.flow(_logTag, 'paint', '💾 绘制共享变形图像（保持宽高比）');
        
        if (sharedFeaturePoints != null) {
          Logger.flow(_logTag, 'paint', '📷 使用共享变形特征点: ${sharedFeaturePoints.length}个点');
          _deformedFeaturePoints = sharedFeaturePoints;
          
          // 确保特征点管理器也使用共享的特征点，但只在需要时更新
          if (_featurePointsNeedUpdate) {
            _featurePointManager.updateFeaturePoints(sharedFeaturePoints);
            _featurePointsNeedUpdate = false;
            Logger.flow(_logTag, 'paint', '✅ 更新特征点管理器使用共享特征点');
          }
        }
      }
    }
    
    // 已移除参数切换逻辑，始终执行变形计算
    // 重置参数切换标志，确保不影响变形计算
    if (_isParameterSwitching) {
      Logger.flow(_logTag, 'paint', '✅ 重置参数切换标志，确保变形计算正常执行');
      _isParameterSwitching = false;
    }
    
    // 统一的侧面图像处理逻辑
    if (_sideImage != null && _image == null) {
      Logger.flow(_logTag, 'paint', '✅ 绘制侧面图像: 参数=' + (_parameterName ?? '未设置'));
      
      final srcRect = Rect.fromLTWH(0, 0, _sideImage!.width.toDouble(), _sideImage!.height.toDouble());
      final dstRect = Rect.fromLTWH(0, 0, size.width, size.height);
      canvas.drawImageRect(_sideImage!, srcRect, dstRect, Paint());
      
      Logger.flowEnd(_logTag, 'paint');
      return null;
    }
    
    // 获取特征点
    final featurePoints = _getFeaturePoints();
    
    Logger.flow(_logTag, 'paint', '特征点数量: ${featurePoints?.length ?? 0}');
    
    // 如果特征点为空，记录错误并返回
    if (featurePoints == null || featurePoints.isEmpty) {
      Logger.flowError(_logTag, 'paint', '特征点为空，无法绘制变形区域');
      Logger.flowEnd(_logTag, 'paint');
      return null;
    }
    
    // 获取当前参数的特征点
    final parameterPoints = featurePoints.where((point) => 
      _currentParameterPointIndexes.contains(point.index)).toList();
    
    if (parameterPoints.isEmpty) {
      Logger.flowWarning(_logTag, 'paint', '当前参数没有特征点，无法绘制变形区域');
      _drawOriginalImage(canvas, size);
      Logger.flowEnd(_logTag, 'paint');
      return null;
    }
    
    Logger.flow(_logTag, 'paint', '有效特征点数量: ${parameterPoints.length}');
    
    // 输出特征点索引信息
    Logger.flow(_logTag, 'paint', '🔍 参数 $_parameterName - 当前参数特征点索引: $_currentParameterPointIndexes');
    
    // 计算图片尺寸和缩放因子
    final imageWidth = _image!.width.toDouble();
    final imageHeight = _image!.height.toDouble();
    final scaleX = size.width / imageWidth;
    final scaleY = size.height / imageHeight;
    
    // 检查参数值
    Logger.flow(_logTag, 'paint', '参数值: $_parameterValue');
    
    // 绘制当前图像，维持累积状态
    if (_onDeformationResultCallback != null && _deformedImage != null) {
      _onDeformationResultCallback!(_deformedImage, _deformedFeaturePoints);
      Logger.flow(_logTag, 'paint', '📤 通过回调函数维持累积状态');
    }
    
    // 记录特征点状态
    if (_deformedFeaturePoints != null) {
      Logger.flow(_logTag, 'paint', '特征点数据状态，数量: ${_deformedFeaturePoints!.length}');
    }
    
    // 如果有累积变形状态，确保在参数切换时保持累积状态
    Map<String, double> allParams = _parameterValueManager.getAllParameters();
    if (allParams.isNotEmpty && _parameterName != null) {
      // 如果有其他参数的值不为0，说明已经有累积变形
      bool hasOtherParameterValues = false;
      allParams.forEach((key, value) {
        if (value != 0.0 && key != _parameterName) {
          hasOtherParameterValues = true;
        }
      });
      
      if (hasOtherParameterValues) {
        _hasAccumulatedState = true;
        Logger.flow(_logTag, 'paint', '检测到其他参数的值不为0，设置累积状态为true');
      }
      
      // 检查是否所有参数值均为0
      bool allParametersAreZero = true;
      allParams.forEach((key, value) {
        if (value != 0) {
          allParametersAreZero = false;
        }
      });
      
      // 处理所有参数值为0的情况
      if (allParametersAreZero) {
        // 查找zero_state缓存
        final zeroStateCachedResult = _deformationCacheManager.find({'zero_state': 0.0});
        Logger.flowError(_logTag, 'paint', '🔍🔍🔍 查找零状态缓存: ${zeroStateCachedResult != null ? "找到" : "未找到"} 🔍🔍🔍');
        print('🔍🔍🔍 零状态缓存检查结果: ${zeroStateCachedResult != null ? "找到" : "未找到"}! 缓存对象是否为空: ${zeroStateCachedResult == null ? "是" : "否"} 🔍🔍🔍');
        
        if (zeroStateCachedResult != null) {
          // 零状态缓存存在，使用缓存的图像和特征点
          _deformedImage = zeroStateCachedResult.deformedImage;
          _deformedFeaturePoints = zeroStateCachedResult.deformedFeaturePoints;
          
          // 绘制缓存的零状态图像
          if (_deformedImage != null) {
            final srcRect = Rect.fromLTWH(0, 0, _deformedImage!.width.toDouble(), _deformedImage!.height.toDouble());
            final dstRect = Rect.fromLTWH(0, 0, size.width, size.height);
            canvas.drawImageRect(_deformedImage!, srcRect, dstRect, Paint()..filterQuality = FilterQuality.high);
            Logger.flowError(_logTag, 'paint', '✅✅✅ 成功使用零状态缓存绘制图像（尺寸：${_deformedImage!.width}x${_deformedImage!.height}）');
            print('✅✅✅ 成功使用零状态缓存绘制图像!');
            
            // 通过回调函数通知变形结果
            if (_onDeformationResultCallback != null) {
              _onDeformationResultCallback!(_deformedImage, _deformedFeaturePoints);
              Logger.flow(_logTag, 'paint', '📤 通过回调函数传递零状态缓存结果');
            }
            
            // 不设置累积状态，因为所有参数都为0
            _hasAccumulatedState = false;
            
            // 如果有特征点管理器，并且需要显示特征点，则更新特征点
            // 注意：不在这里更新特征点，因为图像变形后需要对全图“重新”进行特征识别
            if (_showFeaturePoints && _featurePointManager != null && _deformedFeaturePoints != null) {
              // 在零状态缓存接口中，只通知特征点管理器将特征点显示出来，而不更新实际数据
              // 完全替换特征点数据的逻辑应在另一个地方处理
              Logger.flow(_logTag, 'paint', '📍 特征点显示逻辑更新 - 零状态缓存场景');
            }
          }
        } else {
          // 零状态缓存不存在，这是一个严重错误
          Logger.flowError(_logTag, 'paint', '❗️❗️❗️ 致命错误：所有参数为0但找不到零状态缓存!');
          print('❌❌❌ 致命错误：所有参数值为0时必须存在zero_state缓存! 缓存初始化失败! ❌❌❌');
          
          // 尝试显示原始图像作为应急措施
          if (_image != null) {
            _drawOriginalImage(canvas, size);
            Logger.flowError(_logTag, 'paint', '🚨 应急措施: 显示原始图像');
            
            // 通知回调函数已重置为原始状态
            if (_onDeformationResultCallback != null) {
              _onDeformationResultCallback!(null, null);
            }
          }
          
          // 抛出异常，中断程序执行
          throw Exception('系统错误：所有参数值为0时必须存在zero_state缓存，请检查缓存初始化逻辑');
        }
      } else {
        // 存在非零参数值，进行正常的变形绘制逻辑
        if (_deformedImage != null) {
          final srcRect = Rect.fromLTWH(0, 0, _deformedImage!.width.toDouble(), _deformedImage!.height.toDouble());
          final dstRect = Rect.fromLTWH(0, 0, size.width, size.height);
          canvas.drawImageRect(_deformedImage!, srcRect, dstRect, Paint()..filterQuality = FilterQuality.high);
          Logger.flow(_logTag, 'paint', '🖼️ 绘制变形图像（已应用缩放：${_deformedImage!.width}x${_deformedImage!.height} -> ${size.width.toInt()}x${size.height.toInt()}）');
          
          // 通过回调函数通知变形结果
          if (_onDeformationResultCallback != null) {
            _onDeformationResultCallback!(_deformedImage, _deformedFeaturePoints);
            Logger.flow(_logTag, 'paint', '📤 通过回调函数将变形结果回传');
          }
          
          // 标记有累积状态
          _hasAccumulatedState = true;
        }
      }
      // 检查是否有其他参数项非0，如果有，则表示需要在累积状态上继续变形
      bool hasOtherNonZeroParams = false;
      for (var entry in allParams.entries) {
        if (entry.key != _parameterName && entry.value != 0) {
          hasOtherNonZeroParams = true;
          break;
        }
      }

      // 更新当前参数值到参数值管理器中
      _parameterValueManager.setValue(_parameterName!, _parameterValue);
      
      // 先尝试查找缓存，无论参数值是否为0
      DeformationStateValue? cacheResult = null;
      if (_deformationCacheManager != null) {
        // 生成缓存键并查找缓存
        cacheResult = _deformationCacheManager.find(_parameterValueManager.getAllParameters());
        if (cacheResult != null && cacheResult.deformedImage != null) {
          Logger.flow(_logTag, 'paint', '✅ 缓存命中，使用缓存结果 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue');
          _cachedResult = cacheResult;
        } else {
          Logger.flow(_logTag, 'paint', '❌ 缓存未命中 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue');
        }
      }

      // 记录当前参数状态和变形需求
      if (_parameterValue == 0 && hasOtherNonZeroParams) {
        Logger.flow(_logTag, 'paint', '📈 参数值为0但存在其他非0参数，继续在累积状态上变形 - 区域: $_areaType, 参数: $_parameterName');
      } else if (_parameterValue != 0) {
        Logger.flow(_logTag, 'paint', '📈 应用变形算法 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue, 强度: $_intensity');
        Logger.flow(_logTag, 'paint', '📈 应用变形算法 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue (${_parameterValue < 0 ? "负值" : "正值"}), 强度: $_intensity');
      } else if (_parameterValue == 0 && !hasOtherNonZeroParams) {
        Logger.flow(_logTag, 'paint', '📈 所有参数值都为0，但缓存未命中，执行变形计算 - 区域: $_areaType, 参数: $_parameterName');
      }
      
      // 检查是否已经有缓存结果，如果有则直接使用，避免重复变形
      if (_cachedResult != null && _cachedResult!.deformedImage != null) {
        Logger.flow(_logTag, 'paint', '✅ 【路径A】使用预先查询的缓存结果，避免重复变形计算');
        _deformedImage = _cachedResult!.deformedImage;
        _deformedFeaturePoints = _cachedResult!.deformedFeaturePoints;
        
        // 使用单一日志记录缓存图像信息，避免重复输出
        Logger.flow(_logTag, 'paint', '📷 缓存命中图像: 哈希码=${_cachedResult!.deformedImage.hashCode}, 尺寸=${_cachedResult!.deformedImage!.width}x${_cachedResult!.deformedImage!.height}, 参数=${_parameterName}:${_parameterValue}');
        
        // 更新特征点管理器
        if (_cachedResult!.deformedFeaturePoints.isNotEmpty) {
          _featurePointManager.updateFeaturePoints(_cachedResult!.deformedFeaturePoints);
          Logger.flow(_logTag, 'paint', '✅ 更新特征点管理器使用缓存特征点: ${_cachedResult!.deformedFeaturePoints.length}个点');
        }
        
        // 绘制缓存的图像（确保正确缩放）
        final srcRect = Rect.fromLTWH(0, 0, _deformedImage!.width.toDouble(), _deformedImage!.height.toDouble());
        final dstRect = Rect.fromLTWH(0, 0, size.width, size.height);
        canvas.drawImageRect(_deformedImage!, srcRect, dstRect, Paint()..filterQuality = FilterQuality.high);
        Logger.flow(_logTag, 'paint', '💾 绘制缓存图像（已应用缩放：${_deformedImage!.width}x${_deformedImage!.height} -> ${size.width.toInt()}x${size.height.toInt()}）');
        
        // 确保在paint方法中也通过回调函数将变形结果回传给SimpleDeformationRenderer
        if (_onDeformationResultCallback != null) {
          _onDeformationResultCallback!(_deformedImage, _deformedFeaturePoints);
          Logger.flow(_logTag, 'paint', '🔔 通过回调函数将缓存结果回传给SimpleDeformationRenderer');
        }
        
        // 在缓存命中时，直接返回，跳过后续的变形计算
        Logger.flow(_logTag, 'paint', '✅ 缓存命中，跳过变形计算');
        Logger.flowEnd(_logTag, 'paint');
        return null;
      } else {
        // 如果没有缓存结果，无条件执行变形计算
        Logger.flow(_logTag, 'paint', '❌ 【路径B】缓存未命中，执行变形计算 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue');
        
        // 【关键修复】改为同步变形调用，确保变形图像能立即显示在主Canvas上
        Logger.flow(_logTag, 'paint', '🚀 调用同步变形方法: _applyImageDeformationSync');
        try {
          final newDeformedPoints = _applyImageDeformationSync(canvas, size);
          if (newDeformedPoints != null && newDeformedPoints.isNotEmpty) {
            _deformedFeaturePoints = newDeformedPoints;
            Logger.flow(_logTag, 'paint', '📈 更新变形后特征点 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue, 特征点数量: ${_deformedFeaturePoints?.length ?? 0}');
            
            // 使用_safelyNotifyDeformationResult方法安全地通知变形结果
            _safelyNotifyDeformationResult(_deformedImage, _deformedFeaturePoints);
          }
        } catch (e) {
          Logger.flowError(_logTag, 'paint', '❌ 同步变形调用失败: $e');
          // 如果同步变形失败，绘制原始图像
          _drawOriginalImage(canvas, size);
        }
      }
    }
    
    // 绘制坐标系
    if (_showCoordinateSystem) {
      _drawCoordinateSystem(canvas, size);
    }
    
    // 绘制变形区域（只在显示变形区域可视化时绘制）
    if (_showDeformationArea && parameterPoints.isNotEmpty) {
      Logger.flow(_logTag, 'paint', '开始绘制变形区域，特征点数量: ${parameterPoints.length}');
      _drawUnifiedDeformationArea(canvas, parameterPoints, scaleX, scaleY);
      Logger.flow(_logTag, 'paint', '绘制变形区域完成');
    } else {
      // 如果不显示变形区域，但需要显示特征点
      Logger.flow(_logTag, 'paint', '变形区域可视化已隐藏');      
    }
    
    // 始终绘制特征点，无论变形区域是否可见
    if (_showFeaturePoints) {
      // 特征点数据只从缓存累积中获取
      List<FeaturePoint> pointsToShow;
      
      // 优先使用缓存中的特征点
      if (_deformationCacheManager != null && _cachedResult != null && _cachedResult!.deformedFeaturePoints.isNotEmpty) {
        // 直接从缓存结果中获取特征点
        pointsToShow = _cachedResult!.deformedFeaturePoints;
        Logger.flow(_logTag, 'paint', '📦 从缓存累积中获取特征点: ${pointsToShow.length} 个');
      }
      // 如果缓存中没有，使用变形后的特征点
      else if (_deformedFeaturePoints != null && _deformedFeaturePoints!.isNotEmpty) {
        pointsToShow = _deformedFeaturePoints!;
        Logger.flow(_logTag, 'paint', '👁️ 使用当前变形后的特征点: ${pointsToShow.length} 个');
      }
      // 最后才使用原始特征点
      else if (featurePoints.isNotEmpty) {
        pointsToShow = featurePoints;
        Logger.flow(_logTag, 'paint', '⚠️ 无缓存，使用原始特征点: ${pointsToShow.length} 个');
      }
      // 如果没有特征点可用，直接返回
      else {
        Logger.flow(_logTag, 'paint', '⚠️ 没有特征点可用，跳过绘制');
        return;
      }
      
      // 计算图像中心点
      double centerX = size.width / 2;
      double centerY = size.height / 2;
      
      // 调用 _drawParameterFeaturePoints 方法绘制特征点
      _drawParameterFeaturePoints(canvas, pointsToShow, scaleX, scaleY, centerX, centerY);
      Logger.flow(_logTag, 'paint', '✅ 特征点绘制完成');
    } else {
      Logger.flow(_logTag, 'paint', '🚫 特征点显示已关闭');
    }
    
    // 绘制调试信息
    if (_showDebugInfo) {
      _drawDebugInfo(canvas, size);
    }
    
    // 绘制参数值指示器
    _drawParameterValueIndicator(canvas);
    
    Logger.flowEnd(_logTag, 'paint');
  }
  
  /// 绘制坐标系
  void _drawCoordinateSystem(Canvas canvas, Size size) {
    // 不再绘制坐标系，包括水平线、垂直线和红点
    // 这里留空，保留方法但不执行任何绘制操作
    return null;
  }
  
  // 已移除未使用的绘制方法：
  // - _drawFeaturePoints
  // - _drawVectorFieldArea
  // - _drawThinPlateSplineArea
  // - _drawGridArea
  // - _drawLocalArea
  // 这些方法已被统一到其他实现中或不再需要
  
  /// 统一绘制变形区域和特征点
  void _drawUnifiedDeformationArea(Canvas canvas, List<FeaturePoint> parameterPoints, double scaleX, double scaleY) {
    // 注释掉侧面图相关日志
    // Logger.flowStart(_logTag, '_drawUnifiedDeformationArea');
    
    // 如果是侧面图像模式，不显示变形区域
    if (_sideImage != null) {
      // 注释掉侧面图相关日志
      // Logger.flow(_logTag, '_drawUnifiedDeformationArea', '⚠️ 侧面图像模式，不显示变形区域');
      // 注释掉侧面图相关日志
      // Logger.flowEnd(_logTag, '_drawUnifiedDeformationArea');
      return;
    }
    
    // 计算中心点
    double sumX = 0;
    double sumY = 0;
    double minX = double.infinity;
    double maxX = -double.infinity;
    
    for (final point in parameterPoints) {
      final scaledX = point.x * scaleX;
      final scaledY = point.y * scaleY;
      
      sumX += scaledX;
      sumY += scaledY;
      minX = math.min(minX, scaledX);
      maxX = math.max(maxX, scaledX);
    }
    
    // 使用所有特征点的平均值作为中心点
    double centerY = sumY / parameterPoints.length;
    
    // 获取或计算面部中心线X坐标
    double centerX;
    
    // 首次计算面部中心线或重新计算
    if (_facialCenterLineX == null) {
      List<FeaturePoint>? allFeaturePoints = _featurePointManager?.getFeaturePoints();
      if (allFeaturePoints != null && allFeaturePoints.isNotEmpty) {
        _facialCenterLineX = _calculateFacialCenterLineX(allFeaturePoints, scaleX);
        Logger.flow(_logTag, '_drawUnifiedDeformationArea', '计算并固定面部中心线X坐标: $_facialCenterLineX');
      } else {
        // 如果没有特征点，使用参数点的中心
        _facialCenterLineX = (minX + maxX) / 2;
        Logger.flow(_logTag, '_drawUnifiedDeformationArea', '无特征点，使用参数点计算面部中心线X坐标: $_facialCenterLineX');
      }
    }
    
    // 确保 _facialCenterLineX 已经被初始化
    if (_facialCenterLineX == null) {
      _facialCenterLineX = _imageSize.width / 2; // 默认使用画布中心作为面部中心线
      Logger.flow(_logTag, '_drawUnifiedDeformationArea', '无法计算面部中心线，使用画布中心: $_facialCenterLineX');
    }
    
    centerX = _facialCenterLineX!;
    
    // 记录面部中心线位置
    Logger.flow(_logTag, '_drawUnifiedDeformationArea', '使用面部中心线X坐标: $centerX');
    
    // 绘制面部中心线
    if (_showDeformationArea) {
      // 面部中心线绘制功能已移除
      // 如需绘制面部中心线，应使用变形策略中的方法
    }
    
    // 计算变形半径
    double radius = 0;
    for (final point in parameterPoints) {
      final scaledX = point.x * scaleX;
      final scaledY = point.y * scaleY;
      
      final distance = math.sqrt(math.pow(scaledX - centerX, 2) + math.pow(scaledY - centerY, 2));
      radius = math.max(radius, distance);
    }
    
    // 增加半径以确保覆盖整个变形区域
    radius *= 1.5;
    
    // 根据参数值的绝对值决定颜色强度
    final parameterIntensity = _parameterValue.abs() / 10.0; // 假设参数值范围是-10到10
    
    // 创建虚线画笔
    final dashPaint = Paint()
      ..color = Colors.white.withOpacity(0.6 * _breathingValue)  // 使用白色半透明
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // 创建椭圆形变形区域路径
    final path = Path();
  
    // 计算椭圆形区域的参数 - 水平方向更宽，垂直方向更窄
    final horizontalRadius = radius * 1.3; // 水平半径更大
    final verticalRadius = radius * 0.7;  // 垂直半径更小
  
    // 使用椭圆形，更好地匹配唇形的自然形状
    path.addOval(Rect.fromCenter(
      center: Offset(centerX, centerY),
      width: horizontalRadius * 2,
      height: verticalRadius * 2,
    ));
    
    // 绘制虚线路径
    final dashLength = 5.0;
    final dashSpace = 5.0;
    final pathMetrics = path.computeMetrics();
    
    for (final metric in pathMetrics) {
      double distance = 0;
      while (distance < metric.length) {
        double start = distance;
        double end = distance + dashLength;
        if (end > metric.length) {
          end = metric.length;
        }
        final extractPath = metric.extractPath(start, end);
        canvas.drawPath(extractPath, dashPaint);
        distance = end + dashSpace;
      }
    }
    
    // 注释掉侧面图相关日志
    // Logger.flowEnd(_logTag, '_drawUnifiedDeformationArea');
  }
  
  /// 绘制特征点
  void _drawParameterFeaturePoints(Canvas canvas, List<FeaturePoint> points, double scaleX, double scaleY, double centerX, double centerY) {
    // 记录方法调用开始
    Logger.flowStart(_logTag, '_drawParameterFeaturePoints');
    
    // 如果特征点显示已关闭或特征点列表为空，直接返回
    if (!_showFeaturePoints || points.isEmpty) {
      Logger.flow(_logTag, '_drawParameterFeaturePoints', '⚠️ 特征点显示已关闭或特征点列表为空');
      Logger.flowEnd(_logTag, '_drawParameterFeaturePoints');
      return;
    }
    
    // 记录传入的特征点数据来源
    Logger.flow(_logTag, '_drawParameterFeaturePoints', '💾 使用变形后特征点数据: ${points.length} 个');
    
    // 🔧 [关键修复] 获取当前参数对应的特征点索引 - 使用与变形逻辑相同的索引获取方式
    List<int> currentParameterPointIndexes = [];
    
    // 【架构清理】直接使用已存在的_currentParameterPointIndexes，避免重复获取
    currentParameterPointIndexes = _currentParameterPointIndexes;
    Logger.flow(_logTag, '_drawParameterFeaturePoints', '🔧 [修复] 使用策略系统已设置的索引: ${currentParameterPointIndexes.length} 个');
    Logger.flow(_logTag, '_drawParameterFeaturePoints', '🔧 [修复] 特征点索引: $currentParameterPointIndexes');
    
    if (currentParameterPointIndexes.isEmpty) {
      Logger.flow(_logTag, '_drawParameterFeaturePoints', '⚠️ 当前参数特征点索引为空，无法绘制');
      Logger.flowEnd(_logTag, '_drawParameterFeaturePoints');
      return;
    }
    
    // 如果特征点索引为空，直接返回
    if (currentParameterPointIndexes.isEmpty) {
      Logger.flow(_logTag, '_drawParameterFeaturePoints', '⚠️ 特征点索引列表为空');
      Logger.flowEnd(_logTag, '_drawParameterFeaturePoints');
      return;
    }
    
    // 获取图像尺寸 - 优先使用变形后图像的尺寸
    double imageWidth, imageHeight;
    if (_deformedImage != null) {
      imageWidth = _deformedImage!.width.toDouble();
      imageHeight = _deformedImage!.height.toDouble();
      Logger.flow(_logTag, '_drawParameterFeaturePoints', '📏 使用变形后图像尺寸: ${imageWidth}x${imageHeight}');
    } else {
      imageWidth = _image?.width.toDouble() ?? 0;
      imageHeight = _image?.height.toDouble() ?? 0;
      Logger.flow(_logTag, '_drawParameterFeaturePoints', '📏 使用原始图像尺寸: ${imageWidth}x${imageHeight}');
    }
    
    // 使用传入的scaleX和scaleY计算缩放比例，确保与绘制图像时使用的计算方式一致
    final scale = math.min(scaleX, scaleY);
    
    // 计算实际绘制区域的位置，与绘制图像时使用的计算方式一致
    // 使用传入的centerX和centerY作为画布中心
    final destRect = Rect.fromLTWH(
      centerX - (imageWidth * scale) / 2,
      centerY - (imageHeight * scale) / 2,
      imageWidth * scale,
      imageHeight * scale
    );
    
    // 使用实际绘制区域的左上角作为偏移
    double offsetX = destRect.left;
    double offsetY = destRect.top;
    
    Logger.flow(_logTag, '_drawParameterFeaturePoints', '📐 缩放比例: $scale, 偏移: ($offsetX, $offsetY), 绘制区域: $destRect');
    
    // 计算透明度从0到半透明(0.5)
    double adjustedOpacity = _breathingValue * 0.5; // 将透明度范围缩小到0-0.5
    
    // 移除原有的画笔初始化代码，只在绘制时创建蓝色画笔
    
    Logger.flow(_logTag, '_drawParameterFeaturePoints', '📍 开始绘制特征点: ${currentParameterPointIndexes.length} 个, 透明度=$_breathingValue');
    
    // 记录当前特征点绘制的详细信息
    if (_parameterName != null && _parameterName!.isNotEmpty) {
      Logger.flow(_logTag, '_drawParameterFeaturePoints', '📍 当前参数: $_parameterName, 变形值: $_parameterValue');
    }
    
    // 绘制当前参数对应的特征点
    for (int index in currentParameterPointIndexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        
        // 使用特征点的坐标，应用正确的缩放和偏移
        // 特征点坐标是基于原始图像尺寸的，需要应用相同的缩放比例
        final pointX = point.x * scale + offsetX;
        final pointY = point.y * scale + offsetY;
        final position = Offset(pointX, pointY);
        
        // 记录详细的坐标计算过程，用于调试
        Logger.flow(_logTag, '_drawParameterFeaturePoints', '📍 参数 $_parameterName 特征点[$index] - 计算过程: 原始=(${point.x}, ${point.y}), 缩放=$scale, 偏移=($offsetX, $offsetY), 绘制=($pointX, $pointY)');
        
        // 使用红色画笔绘制特征点，透明度从0到1
        final redPaint = Paint()
          ..style = PaintingStyle.fill
          ..color = Color.fromRGBO(255, 0, 0, _breathingValue); // 透明度从0到1
        
        // 使用红色画笔直接绘制
        canvas.drawCircle(position, 1.5, redPaint); // 特征点大小为1.5px
        
        // 记录特征点的绘制位置，用于调试
        Logger.flow(_logTag, '_drawParameterFeaturePoints', '📍 参数 $_parameterName 特征点[$index] - 变形后坐标: (${point.x}, ${point.y}), 绘制坐标: ($pointX, $pointY)');
      }
    }
    
    Logger.flow(_logTag, '_drawParameterFeaturePoints', '✅ 特征点绘制完成');
    Logger.flowEnd(_logTag, '_drawParameterFeaturePoints');
  }
  
  /// 绘制调试信息
  void _drawDebugInfo(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      color: Colors.black87,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );
    
    final textPainter = TextPainter(
      text: TextSpan(
        text: '区域: $_areaType\n参数: $_parameterName\n值: ${_parameterValue.toStringAsFixed(2)}\n强度: ${_intensity.toStringAsFixed(2)}',
        style: textStyle,
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout(maxWidth: size.width);
    textPainter.paint(canvas, Offset(10, 10));
  }
  
  /// 绘制参数值指示器
  void _drawParameterValueIndicator(Canvas canvas) {
    // 不再绘制参数值指示器（进度条）
    // 这里留空，保留方法但不执行任何绘制操作
    return null;
  }

  /// 绘制原始图像或累积变形图像
  ui.Image? _drawOriginalImage(Canvas canvas, Size size) {
    Logger.flowStart(_logTag, '_drawOriginalImage');
    Logger.flow(_logTag, '_drawOriginalImage', '📷 [开始] 绘制图像');
    
    // 检查图像是否已加载
    if (_image == null) {
      Logger.flowWarning(_logTag, '_drawOriginalImage', '⚠️ 图像未加载，无法绘制');
      Logger.flowEnd(_logTag, '_drawOriginalImage');
      return null;
    }
    
    // 决定使用哪个图像进行绘制
    ui.Image imageToDraw;
    
    // 输出当前状态信息，帮助调试
    if (_cachedResult != null) {
      Logger.flow(_logTag, '_drawOriginalImage', '📷 缓存结果: ${_cachedResult!.deformedImage != null ? "有效" : "无效"}');
      if (_cachedResult!.deformedImage != null) {
        Logger.flow(_logTag, '_drawOriginalImage', '📷 缓存图像哈希码: ${_cachedResult!.deformedImage.hashCode}');
      }
    } else {
      Logger.flow(_logTag, '_drawOriginalImage', '📷 缓存结果: 无');
    }
    
    Logger.flow(_logTag, '_drawOriginalImage', '📷 变形图像: ${_deformedImage != null ? "有效" : "无效"}');
    if (_deformedImage != null) {
      Logger.flow(_logTag, '_drawOriginalImage', '📷 变形图像哈希码: ${_deformedImage.hashCode}');
    }
    
    // 首先检查是否有缓存命中的结果
    if (_cachedResult != null && _cachedResult!.deformedImage != null) {
      // 有缓存命中的结果，优先使用缓存图像
      imageToDraw = _cachedResult!.deformedImage!;
      
      // 使用日志系统记录缓存图像信息，避免重复输出
      Logger.flow(_logTag, '_drawOriginalImage', '📷 使用缓存图像: 哈希码=${_cachedResult!.deformedImage.hashCode}, 尺寸=${_cachedResult!.deformedImage!.width}x${_cachedResult!.deformedImage!.height}, 参数=${_parameterName}:${_parameterValue}');
      
      // 确保累积状态标志被正确设置
      _hasAccumulatedState = true;
      
      // 更新实例变量，确保其他方法也能使用缓存图像
      _deformedImage = _cachedResult!.deformedImage;
      
      // 如果缓存中有特征点数据，也一并更新
      if (_cachedResult!.deformedFeaturePoints.isNotEmpty) {
        _deformedFeaturePoints = _cachedResult!.deformedFeaturePoints;
        Logger.flow(_logTag, '_drawOriginalImage', '📷 更新变形特征点: ${_deformedFeaturePoints!.length}个点');
      }
      
      // 确保特征点管理器也使用缓存的特征点
      _featurePointManager.updateFeaturePoints(_cachedResult!.deformedFeaturePoints);
      Logger.flow(_logTag, '_drawOriginalImage', '✅ 更新特征点管理器使用缓存特征点');
      
      // 强制设置需要重绘标志
      _needsRepaint = true;
    }
    // 如果没有缓存结果，但有构造函数传入的变形图像和特征点，则使用它们
    else if (_deformedImage != null) {
      // 有构造函数传入的变形图像，使用它
      imageToDraw = _deformedImage!;
      Logger.flow(_logTag, '_drawOriginalImage', '📷 使用构造函数传入的变形图像: 哈希码=${_deformedImage.hashCode}');
      
      // 确保累积状态标志被正确设置
      _hasAccumulatedState = true;
      
      // 如果缓存中有特征点数据，也一并更新
      if (_deformedFeaturePoints != null && _deformedFeaturePoints!.isNotEmpty) {
        Logger.flow(_logTag, '_drawOriginalImage', '📷 使用构造函数传入的变形特征点: ${_deformedFeaturePoints!.length}个点');
        
        // 确保特征点管理器也使用构造函数传入的特征点
        _featurePointManager.updateFeaturePoints(_deformedFeaturePoints!);
        Logger.flow(_logTag, '_drawOriginalImage', '✅ 更新特征点管理器使用构造函数传入的特征点');
      }
    } else {
      // 使用原始图像
      imageToDraw = _image!;
      Logger.flow(_logTag, '_drawOriginalImage', '📷 使用原始图片作为绘制图像 (无累积变形图像)');
    }
    
    // 计算图像绘制区域
    final imageWidth = imageToDraw.width.toDouble();
    final imageHeight = imageToDraw.height.toDouble();
    
    // 计算缩放比例，使图像适应画布
    final scaleX = size.width / imageWidth;
    final scaleY = size.height / imageHeight;
    
    // 使用较小的缩放比例，确保图像完全显示在画布内
    final scale = math.min(scaleX, scaleY);
    
    // 计算绘制区域
    final destRect = Rect.fromLTWH(
      (size.width - imageWidth * scale) / 2,
      (size.height - imageHeight * scale) / 2,
      imageWidth * scale,
      imageHeight * scale
    );
    
    // 绘制图像
    final paint = Paint()
      ..filterQuality = FilterQuality.high;
    
    canvas.drawImageRect(
      imageToDraw,
      Rect.fromLTWH(0, 0, imageWidth, imageHeight),
      destRect,
      paint
    );
    
    // 遵循变形系统原则，所有变形必须在同一个实例下进行，共享同一缓存
    // 完全移除侧面图像绘制以避免小图像重叠问题
    // 需要保证整个变形生命周期内变形状态保持一致
    
    Logger.flowEnd(_logTag, '_drawOriginalImage');
    return imageToDraw;
  }

  /// 绘制指定图像并进行适当缩放以适应画布
  void _drawScaledImage(Canvas canvas, Size size, ui.Image image) {
    // 计算图像绘制区域
    final imageWidth = image.width.toDouble();
    final imageHeight = image.height.toDouble();
    
    // 计算缩放比例，使图像适应画布
    final scaleX = size.width / imageWidth;
    final scaleY = size.height / imageHeight;
    
    // 使用较小的缩放比例，确保图像完全显示在画布内
    final scale = math.min(scaleX, scaleY);
    
    // 计算绘制区域
    final destRect = Rect.fromLTWH(
      (size.width - imageWidth * scale) / 2,
      (size.height - imageHeight * scale) / 2,
      imageWidth * scale,
      imageHeight * scale
    );
    
    // 绘制图像
    final paint = Paint()
      ..filterQuality = FilterQuality.high;
    
    canvas.drawImageRect(
      image,
      Rect.fromLTWH(0, 0, imageWidth, imageHeight),
      destRect,
      paint
    );
  }
  
  // 记录上次缓存保存的时间和缓存键
  DateTime? _lastCacheSaveTime;
  String? _lastCacheKey;
  
  /// 保存变形结果到缓存，确保变形效果正确累积和缓存
  /// 返回是否成功保存到缓存
  bool _saveCacheIfNeeded(Map<String, double> paramValues, ui.Image deformedImage, List<FeaturePoint>? deformedPoints) {
    // 检查变形后图像尺寸是否与原始图像一致
    if (!_imageSizeManager.checkImageSizeConsistency(deformedImage)) {
      Logger.flowWarning(_logTag, '_saveCacheIfNeeded', '⚠️ 变形后图像尺寸与原始图像不一致，无法保存到缓存');
      Logger.flowEnd(_logTag, '_saveCacheIfNeeded');
      return false;
    }
    Logger.flowStart(_logTag, '_saveCacheIfNeeded');
    
    // 确保特征点不为空
    if (deformedPoints == null || deformedPoints.isEmpty) {
      Logger.flowError(_logTag, '_saveCacheIfNeeded', '❌ 特征点为空，无法保存缓存');
      Logger.flowEnd(_logTag, '_saveCacheIfNeeded');
      return false;
    }
    
    // 记录全量参数值
    Logger.flow(_logTag, '_saveCacheIfNeeded', '📊 全量参数: ${paramValues.entries.map((e) => '${e.key}=${e.value}').join(', ')}');
    
    // 使用全量参数值生成缓存键，确保所有非零参数都被包含
    Map<String, double> nonZeroParams = {};
    paramValues.forEach((key, value) {
      if (value != 0.0) {
        nonZeroParams[key] = value;
      }
    });
    
    // 记录非零参数
    Logger.flow(_logTag, '_saveCacheIfNeeded', '📈 非零参数: ${nonZeroParams.entries.map((e) => '${e.key}=${e.value}').join(', ')}');
    
    // 生成缓存键
    String cacheKey = _deformationCacheManager.generateCacheKey(paramValues);
    Logger.flow(_logTag, '_saveCacheIfNeeded', '🔑 生成缓存键: $cacheKey');
    
    // 检查是否在短时间内重复保存相同的缓存键
    final now = DateTime.now();
    if (_lastCacheSaveTime != null && _lastCacheKey == cacheKey) {
      final timeDiff = now.difference(_lastCacheSaveTime!);
      if (timeDiff.inMilliseconds < 500) { // 如果在500毫秒内重复保存相同的缓存键，则跳过
        Logger.flow(_logTag, '_saveCacheIfNeeded', '⏭️ 跳过缓存保存，避免重复保存相同的缓存键: $cacheKey');
        Logger.flowEnd(_logTag, '_saveCacheIfNeeded');
        return false;
      }
    }
    
    // 保存到缓存
    _deformationCacheManager.save(
      paramValues, // 使用全量参数值，确保所有非零参数都被包含
      deformedImage,
      deformedPoints,
      facialCenterLineX: _facialCenterLineX,
      facialCenterLineCalculated: _facialCenterLineCalculated
    );
    
    // 更新上次缓存保存的时间和缓存键
    _lastCacheSaveTime = now;
    _lastCacheKey = cacheKey;
    
    Logger.flow(_logTag, '_saveCacheIfNeeded', '💾 变形结果已保存到缓存，缓存键: $cacheKey');
    Logger.flowEnd(_logTag, '_saveCacheIfNeeded');
    return true;
  }
  
  /// 应用变形算法到图片上（同步版本）
  /// 返回变形后的特征点列表
  /// 【关键修复】为了确保在paint方法中能立即显示变形效果，创建同步版本
  List<FeaturePoint>? _applyImageDeformationSync(Canvas canvas, Size size) {
    Logger.flow(_logTag, '_applyImageDeformationSync', '🚀 同步变形方法被调用: 参数=$_parameterName, 值=$_parameterValue');
    // 简化日志记录，只保留关键信息
    if (_image == null) {
      Logger.flowError(_logTag, '_applyImageDeformationSync', '❌ 错误: 图像为空，无法应用变形');
      return null;
    }
    
    // 标记是否已应用缓存结果
    bool cacheApplied = false;
    
    // 【关键修复】优先使用DeformationCacheManager中的全局累积变形状态作为变形起点
    // 这确保了在参数切换时能正确获取其他参数的累积变形结果
    ui.Image baseImage;
    List<FeaturePoint> baseFeaturePoints = _featurePointManager.getFeaturePoints() ?? [];
    
    // 【关键修复】基于当前参数状态查找正确的基础图像和特征点
    // 获取当前所有非零参数，找到对应的缓存状态作为基础
    Map<String, double> currentParams = ParameterValueManager().getAllParameters();
    
    // 查找当前参数状态的缓存
    DeformationStateValue? currentState = DeformationCacheManager().find(currentParams);
    
    if (currentState != null && currentState.deformedImage != null) {
      baseImage = currentState.deformedImage!;
      baseFeaturePoints = currentState.deformedFeaturePoints;
      Logger.flow(_logTag, '_applyImageDeformationSync', 
          '✅ 使用当前参数状态的缓存图像: 哈希码=${currentState.deformedImage!.hashCode}, 参数=${currentParams.keys.join(",")}');
    } else {
      // 如果当前参数状态没有缓存，使用全局最新状态
      ui.Image? globalAccumulatedImage = DeformationCacheManager.getLatestDeformedImage();
      List<FeaturePoint>? globalAccumulatedFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
      
      if (globalAccumulatedImage != null) {
        baseImage = globalAccumulatedImage;
        Logger.flow(_logTag, '_applyImageDeformationSync', 
            '⚠️ 当前参数状态无缓存，使用全局最新状态: 哈希码=${globalAccumulatedImage.hashCode}');
      } else if (_hasAccumulatedState && _deformedImage != null) {
        baseImage = _deformedImage!;
        Logger.flow(_logTag, '_applyImageDeformationSync', '⚠️ 使用本地累积变形图像');
      } else {
        baseImage = _image!;
        Logger.flow(_logTag, '_applyImageDeformationSync', '⚠️ 使用原始图像');
      }
      
      if (globalAccumulatedFeaturePoints != null && globalAccumulatedFeaturePoints.isNotEmpty) {
        baseFeaturePoints = globalAccumulatedFeaturePoints;
        Logger.flow(_logTag, '_applyImageDeformationSync', 
            '⚠️ 使用全局缓存的累积变形特征点: ${globalAccumulatedFeaturePoints.length}个');
      } else if (_deformedFeaturePoints != null && _deformedFeaturePoints!.isNotEmpty) {
        baseFeaturePoints = _deformedFeaturePoints!;
        Logger.flow(_logTag, '_applyImageDeformationSync', '⚠️ 使用本地累积变形特征点');
      }
    }
    
    // 使用预先查询的缓存结果，避免重复查询
    DeformationStateValue? resultToUse = _cachedResult;
    
    // 清除缓存结果引用，避免重复使用
    _cachedResult = null;
    
    if (resultToUse != null && resultToUse.deformedImage != null) {
      cacheApplied = true;
    } else {
      // 如果没有预先查询的缓存结果，则跳过缓存查询，直接执行变形计算
      // 不再进行缓存查询，避免重复查询
    }
    
    if (resultToUse != null && resultToUse.deformedImage != null && cacheApplied) {
      // 检查缓存中的变形图像是否有效
      _deformedImage = resultToUse.deformedImage;
      
      // 确保变形后的特征点被正确地设置
      if (resultToUse.deformedFeaturePoints.isNotEmpty) {
        _deformedFeaturePoints = List<FeaturePoint>.from(resultToUse.deformedFeaturePoints);
        
        // 只在需要时更新特征点管理器
        if (_featurePointsNeedUpdate) {
          _featurePointManager.updateFeaturePoints(_deformedFeaturePoints!);
          _featurePointsNeedUpdate = false;
        }
      } else {
        // 保留警告日志，因为特征点数据为空是一个重要问题
        Logger.flowWarning(_logTag, '_applyImageDeformationSync', '⚠️ 缓存中的变形后特征点为空');
      }
      
      // 恢复面部中心线信息（如果有）
      if (resultToUse.facialCenterLineX != null) {
        _facialCenterLineX = resultToUse.facialCenterLineX;
        _facialCenterLineCalculated = resultToUse.facialCenterLineCalculated ?? false;
      }
      
      // 设置累积状态标志
      _hasAccumulatedState = true;
      
      // 绘制缓存的变形图像（使用正确的缩放显示）
      _drawScaledImage(canvas, size, _deformedImage!);
      return resultToUse?.deformedFeaturePoints;
    }
    
    // 如果缓存已应用，则跳过变形计算
    if (cacheApplied) {
      return _deformedFeaturePoints;
    }
    
    // 使用 TransformationFactory 检查是否有对应的变形策略
    print('============ 变形检测日志 ============');
    print('执行变形: 参数名称=$_parameterName, 参数值=$_parameterValue');
    
    // 提取短参数名（不带区域前缀）
    String shortParamName = _parameterName ?? '';
    if (_parameterName != null && _parameterName!.contains('.')) {
      shortParamName = _parameterName!.split('.').last;
      print('提取短参数名: $shortParamName (从 $_parameterName)');
    }
    
    final transformationFactory = TransformationFactory();
    final strategy = transformationFactory.getStrategy(shortParamName);
    
    if (strategy == null) {
      // 保留错误日志，因为缺少变形策略是一个重要问题
      Logger.flowError(_logTag, '_applyImageDeformationSync', '❌ 错误: 参数 $_parameterName 未实现变形策略，无法执行变形');
      return null;
    }
    
    // 添加标志，避免重复保存缓存
    bool skipSaveCache = false;
    
    // 【重要修复】移除区域类型干扰，所有参数使用统一的变形处理逻辑
    // 区域类型不应该影响参数变形逻辑，区域仅仅是UI分类
    
    // 统一的策略模式变形处理逻辑
    Logger.flow(_logTag, '_applyImageDeformationSync', '🔍 使用策略模式进行统一变形处理，参数: $_parameterName');
  
    // 根据变形类型应用不同的变形算法
    List<FeaturePoint>? updatedFeaturePoints;
    ui.Image? deformedImage;
    
    // 创建一个记录器，用于捕获画布绘制结果
    final recorder = ui.PictureRecorder();
    final recordCanvas = Canvas(recorder);
    
    // 先绘制基准图像（可能是原始图像或累积变形后的图像）
    // 【关键修复】使用原始图像尺寸，不缩放到Canvas尺寸，确保图像尺寸一致性
    final srcRect = Rect.fromLTWH(0, 0, baseImage.width.toDouble(), baseImage.height.toDouble());
    final dstRect = Rect.fromLTWH(0, 0, baseImage.width.toDouble(), baseImage.height.toDouble());
    recordCanvas.drawImageRect(baseImage, srcRect, dstRect, Paint());
    Logger.flow(_logTag, '_applyImageDeformationSync', '📷 绘制基准图像作为变形起点');
    
    // 更新特征点管理器中的特征点，使用基准特征点
    if (baseFeaturePoints.isNotEmpty) {
      _featurePointManager.updateFeaturePoints(baseFeaturePoints);
      Logger.flow(_logTag, '_applyImageDeformationSync', '👁️ 更新特征点: ${baseFeaturePoints.length} 个');
    }
    
    // 应用变形
    if (_parameterName != null && _parameterName!.isNotEmpty && _transformationService != null) {
      // 【修复】删除错误的参数值为0时跳过变形的逻辑
      // 所有参数值都应该执行变形，包括目标值为0的情况
      
      print('====== 进入变形阶段 ======');
      print('参数名: $_parameterName, 参数值: $_parameterValue, 方向: ${_valueChangeDirection ?? "unknown"}');
      
      // 使用变形服务获取对应参数的变形策略
      final transformationFactory = TransformationFactory();
      
      // 提取短参数名（不带区域前缀）
      String shortParamName = _parameterName!;
      if (_parameterName!.contains('.')) {
        shortParamName = _parameterName!.split('.').last;
        Logger.flow(_logTag, '_applyImageDeformationSync', '💡 提取短参数名: $shortParamName (从 $_parameterName)');
        print('提取短参数名: $shortParamName (从 $_parameterName)');
      }
      
      final strategy = transformationFactory.getStrategy(shortParamName);
      
      // 【关键修复】统一使用_applyLocalDeformation路径，这是重构前成功的实现
      // 使用原始图像尺寸而非Canvas尺寸
      final imageSize = Size(baseImage.width.toDouble(), baseImage.height.toDouble());
      Logger.flow(_logTag, '_applyImageDeformationSync', '🚀 调用成功的_applyLocalDeformation方法');
      updatedFeaturePoints = _applyLocalDeformation(recordCanvas, imageSize);
    } else {
      // 参数名为空或变形服务为空，无法应用变形
      Logger.flowWarning(_logTag, '_applyImageDeformationSync', '⚠️ 警告: 参数名为空或变形服务为空，无法应用变形');
      updatedFeaturePoints = baseFeaturePoints; // 使用基准特征点
    }
    
    // 【关键修复】更新跳过保存缓存标志
    _skipSaveCache = skipSaveCache;
    print('🔍🔍🔍 [缓存标志更新] skipSaveCache=$skipSaveCache, _skipSaveCache=$_skipSaveCache');
    
    // 将记录器中的绘制内容转换为图像
    final picture = recorder.endRecording();
    // 使用同步方式创建图像，避免异步问题
    try {
      deformedImage = picture.toImageSync(_imageSizeManager.getOriginalWidth(), _imageSizeManager.getOriginalHeight());
      // 将变形后的图像保存到 _deformedImage 字段，以便在切换参数时保持累积变形状态
      _deformedImage = deformedImage;
      // 设置累积变形状态标记
      _hasAccumulatedState = true;
      
      // 通过回调函数将变形结果回传给SimpleDeformationRenderer
      if (_onDeformationResultCallback != null) {
        Logger.flow(_logTag, '_applyImageDeformationSync', '🔍 [回调] 通过回调函数通知 $_parameterName 变形完成');
        
        _onDeformationResultCallback!(deformedImage, updatedFeaturePoints);
        Logger.i(_logTag, '💾 【变形结果】图像保存成功: 哈希码=${deformedImage.hashCode}, 尺寸=${deformedImage.width}x${deformedImage.height}');
        Logger.i(_logTag, '👁 【特征点累积】当前特征点总数: ${updatedFeaturePoints?.length ?? 0}个');
        Logger.flow(_logTag, '_applyImageDeformationSync', '📤 通过回调函数将变形结果回传给SimpleDeformationRenderer');
      }
      
      Logger.flow(_logTag, '_applyImageDeformationSync', '✅ 成功创建变形图像并保存到累积状态');
      Logger.i(_logTag, '💾 【累积状态】变形图像已保存到累积状态');
    } catch (e) {
      Logger.flowError(_logTag, '_applyImageDeformationSync', '❌ 创建变形图像失败: $e');
    }
    
    // 【关键修复】将绘制结果复制到原始画布上（使用正确的缩放显示）
    if (deformedImage != null) {
      _drawScaledImage(canvas, size, deformedImage);
      Logger.flow(_logTag, '_applyImageDeformationSync', '🎨 同步变形图像已绘制到主Canvas');
    }
    
    // 如果变形成功，将结果保存到缓存
    print('🔍🔍🔍 [缓存保存条件检查] _areaType=$_areaType, _parameterName=$_parameterName, deformedImage=${deformedImage?.hashCode}');
    if (_areaType != null && _parameterName != null && deformedImage != null) {
      // 如果没有变形后的特征点，则使用基准特征点
      if (updatedFeaturePoints == null) {
        updatedFeaturePoints = baseFeaturePoints;
      }
      // 使用参数值管理器中的参数值，而不是重新从 TransformationService 获取
      Map<String, double> allParamValues = {};
      
      Map<String, double> currentParams = _parameterValueManager.getAllParameters();
      if (currentParams.isNotEmpty) {
        // 使用参数值管理器中的参数值
        allParamValues = currentParams;
      } else {
        // 如果参数值管理器中没有参数，使用当前参数值
        allParamValues[_parameterName!] = _parameterValue;
      }
      
      // 保存变形结果到缓存
      _saveCacheIfNeeded(allParamValues, deformedImage, updatedFeaturePoints);
    }
    
    return updatedFeaturePoints;
  }

  /// 应用变形算法到图片上
  /// 返回变形后的特征点列表
  Future<List<FeaturePoint>?> _applyImageDeformation(Canvas canvas, Size size) async {
    // 简化日志记录，只保留关键信息
    if (_image == null) {
      Logger.flowError(_logTag, '_applyImageDeformation', '❌ 错误: 图像为空，无法应用变形');
      return null;
    }
    
    // 标记是否已应用缓存结果
    bool cacheApplied = false;
    
    // 【关键修复】优先使用DeformationCacheManager中的全局累积变形状态作为变形起点
    // 这确保了在参数切换时能正确获取其他参数的累积变形结果
    ui.Image baseImage;
    List<FeaturePoint> baseFeaturePoints = _featurePointManager.getFeaturePoints() ?? [];
    
    // 【关键修复】基于当前参数状态查找正确的基础图像和特征点
    // 获取当前所有非零参数，找到对应的缓存状态作为基础
    Map<String, double> currentParams = ParameterValueManager().getAllParameters();
    
    // 查找当前参数状态的缓存
    DeformationStateValue? currentState = DeformationCacheManager().find(currentParams);
    
    if (currentState != null && currentState.deformedImage != null) {
      baseImage = currentState.deformedImage!;
      baseFeaturePoints = currentState.deformedFeaturePoints;
      Logger.flow(_logTag, '_applyImageDeformation', 
          '✅ 使用当前参数状态的缓存图像: 哈希码=${currentState.deformedImage!.hashCode}, 参数=${currentParams.keys.join(",")}');
    } else {
      // 如果当前参数状态没有缓存，使用全局最新状态
      ui.Image? globalAccumulatedImage = DeformationCacheManager.getLatestDeformedImage();
      List<FeaturePoint>? globalAccumulatedFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
      
      if (globalAccumulatedImage != null) {
        baseImage = globalAccumulatedImage;
        Logger.flow(_logTag, '_applyImageDeformation', 
            '⚠️ 当前参数状态无缓存，使用全局最新状态: 哈希码=${globalAccumulatedImage.hashCode}');
      } else if (_hasAccumulatedState && _deformedImage != null) {
        baseImage = _deformedImage!;
        Logger.flow(_logTag, '_applyImageDeformation', '⚠️ 使用本地累积变形图像');
      } else {
        baseImage = _image!;
        Logger.flow(_logTag, '_applyImageDeformation', '⚠️ 使用原始图像');
      }
      
      if (globalAccumulatedFeaturePoints != null && globalAccumulatedFeaturePoints.isNotEmpty) {
        baseFeaturePoints = globalAccumulatedFeaturePoints;
        Logger.flow(_logTag, '_applyImageDeformation', 
            '⚠️ 使用全局缓存的累积变形特征点: ${globalAccumulatedFeaturePoints.length}个');
      } else if (_deformedFeaturePoints != null && _deformedFeaturePoints!.isNotEmpty) {
        baseFeaturePoints = _deformedFeaturePoints!;
        Logger.flow(_logTag, '_applyImageDeformation', '⚠️ 使用本地累积变形特征点');
      }
    }
    
    // 使用预先查询的缓存结果，避免重复查询
    DeformationStateValue? resultToUse = _cachedResult;
    
    // 清除缓存结果引用，避免重复使用
    _cachedResult = null;
    
    if (resultToUse != null && resultToUse.deformedImage != null) {
      cacheApplied = true;
    } else {
      // 如果没有预先查询的缓存结果，则跳过缓存查询，直接执行变形计算
      // 不再进行缓存查询，避免重复查询
    }
    
    if (resultToUse != null && resultToUse.deformedImage != null && cacheApplied) {
      // 检查缓存中的变形图像是否有效
      _deformedImage = resultToUse.deformedImage;
      
      // 确保变形后的特征点被正确地设置
      if (resultToUse.deformedFeaturePoints.isNotEmpty) {
        _deformedFeaturePoints = List<FeaturePoint>.from(resultToUse.deformedFeaturePoints);
        
        // 只在需要时更新特征点管理器
        if (_featurePointsNeedUpdate) {
          _featurePointManager.updateFeaturePoints(_deformedFeaturePoints!);
          _featurePointsNeedUpdate = false;
        }
      } else {
        // 保留警告日志，因为特征点数据为空是一个重要问题
        Logger.flowWarning(_logTag, '_applyImageDeformation', '⚠️ 缓存中的变形后特征点为空');
      }
      
      // 恢复面部中心线信息（如果有）
      if (resultToUse.facialCenterLineX != null) {
        _facialCenterLineX = resultToUse.facialCenterLineX;
        _facialCenterLineCalculated = resultToUse.facialCenterLineCalculated ?? false;
      }
      
      // 设置累积状态标志
      _hasAccumulatedState = true;
      
      // 绘制缓存的变形图像（使用正确的缩放显示）
      _drawScaledImage(canvas, size, _deformedImage!);
      Logger.flowEnd(_logTag, '_applyImageDeformation');
      return resultToUse?.deformedFeaturePoints;
    }
    
    // 如果缓存已应用，则跳过变形计算
    if (cacheApplied) {
      Logger.flowEnd(_logTag, '_applyImageDeformation');
      return _deformedFeaturePoints;
    }
    
    // 使用 TransformationFactory 检查是否有对应的变形策略
    print('============ 变形检测日志 ============');
    print('执行变形: 参数名称=$_parameterName, 参数值=$_parameterValue');
    
    // 提取短参数名（不带区域前缀）
    String shortParamName = _parameterName ?? '';
    if (_parameterName != null && _parameterName!.contains('.')) {
      shortParamName = _parameterName!.split('.').last;
      print('提取短参数名: $shortParamName (从 $_parameterName)');
    }
    
    final transformationFactory = TransformationFactory();
    final strategy = transformationFactory.getStrategy(shortParamName);
    
    if (strategy == null) {
      // 保留错误日志，因为缺少变形策略是一个重要问题
      Logger.flowError(_logTag, '_applyImageDeformation', '❌ 错误: 参数 $_parameterName 未实现变形策略，无法执行变形');
      Logger.flowEnd(_logTag, '_applyImageDeformation');
      return null;
    }
    
    // 添加标志，避免重复保存缓存
    bool skipSaveCache = false;
    
    // 【重要修复】移除区域类型干扰，所有参数使用统一的变形处理逻辑
    // 区域类型不应该影响参数变形逻辑，区域仅仅是UI分类
    
    // 统一的策略模式变形处理逻辑
    Logger.flow(_logTag, '_applyImageDeformation', '🔍 使用策略模式进行统一变形处理，参数: $_parameterName');
  
    // 根据变形类型应用不同的变形算法
    List<FeaturePoint>? updatedFeaturePoints;
    ui.Image? deformedImage;
    
    // 创建一个记录器，用于捕获画布绘制结果
    final recorder = ui.PictureRecorder();
    final recordCanvas = Canvas(recorder);
    
    // 先绘制基准图像（可能是原始图像或累积变形后的图像）
    // 【关键修复】使用原始图像尺寸，不缩放到Canvas尺寸，确保图像尺寸一致性
    final srcRect = Rect.fromLTWH(0, 0, baseImage.width.toDouble(), baseImage.height.toDouble());
    final dstRect = Rect.fromLTWH(0, 0, baseImage.width.toDouble(), baseImage.height.toDouble());
    recordCanvas.drawImageRect(baseImage, srcRect, dstRect, Paint());
    Logger.flow(_logTag, '_applyImageDeformation', '📷 绘制基准图像作为变形起点');
    
    // 更新特征点管理器中的特征点，使用基准特征点
    if (baseFeaturePoints.isNotEmpty) {
      _featurePointManager.updateFeaturePoints(baseFeaturePoints);
      Logger.flow(_logTag, '_applyImageDeformation', '👁️ 更新特征点: ${baseFeaturePoints.length} 个');
    }
    
    // 应用变形
    if (_parameterName != null && _parameterName!.isNotEmpty && _transformationService != null) {
      // 【修复】删除错误的参数值为0时跳过变形的逻辑
      // 所有参数值都应该执行变形，包括目标值为0的情况
      
      print('====== 进入变形阶段 ======');
      print('参数名: $_parameterName, 参数值: $_parameterValue, 方向: ${_valueChangeDirection ?? "unknown"}');
      
      // 使用变形服务获取对应参数的变形策略
      final transformationFactory = TransformationFactory();
      
      // 提取短参数名（不带区域前缀）
      String shortParamName = _parameterName!;
      if (_parameterName!.contains('.')) {
        shortParamName = _parameterName!.split('.').last;
        Logger.flow(_logTag, '_applyImageDeformation', '💡 提取短参数名: $shortParamName (从 $_parameterName)');
        print('提取短参数名: $shortParamName (从 $_parameterName)');
      }
      
      final strategy = transformationFactory.getStrategy(shortParamName);
      
      if (strategy != null) {
        // 使用策略应用变形
        Logger.flow(_logTag, '_applyImageDeformation', '🔄 使用变形策略应用变形: $_parameterName (短名称: $shortParamName)');
        Logger.flow(_logTag, '_applyImageDeformation', '🚨 开始执行变形: 策略=${strategy.runtimeType}, 参数值=$_parameterValue, 特征点数量=${baseFeaturePoints.length}');
        // 找到策略后，应用变形
      print('找到变形策略: ${strategy.runtimeType}');
      print('特征点索引: $_currentParameterPointIndexes');
      print('变形方向: ${_isParameterIncreasing ?? (_valueChangeDirection == "increase" ? true : _valueChangeDirection == "decrease" ? false : null)}');
      print('✓✓✓ 找到变形策略，准备执行特征点变形算法，参数值=$_parameterValue');
      
      try {
          // 记录变形开始时间
          final startTime = DateTime.now();
          print('开始执行变形时间: $startTime');
          
          // 使用同步方式执行变形，不再调用异步方法
          print('⚠️⚠️⚠️ 执行同步变形流程');
          
          // 【架构清理】统一的策略模式处理，移除所有参数特定的硬编码逻辑
          print('💿 开始执行策略变形：参数值=$_parameterValue');
          
          // 【关键修复】确保所有变形都允许缓存保存
          skipSaveCache = false;
          
          // 记录变形前的特征点状态
          print('📑 变形前特征点：${baseFeaturePoints.length}个');
          
          // 统一的特征点变形处理
          print('📍 开始策略特征点变形处理...');
          strategy.applyFeaturePointTransformation(
            baseFeaturePoints, 
            _currentParameterPointIndexes,
            _parameterValue, 
            _intensity,
            facialCenterLineX: _facialCenterLineX,
            isIncreasing: _isParameterIncreasing ?? (_valueChangeDirection == 'increase' ? true : _valueChangeDirection == 'decrease' ? false : null)
          );
          print('📍 特征点变形完成，基础特征点已更新');
          
          // 统一的图像变形处理 - 这是关键步骤，确保实际执行图像变形
          print('🖼️ 开始执行图像变形操作，使用变形后的特征点...');
          // 【关键修复】使用原始图像尺寸而非Canvas尺寸
          final imageSize = Size(baseImage.width.toDouble(), baseImage.height.toDouble());
          final deformedImage = strategy.applyImageTransformation(
            recordCanvas,
            imageSize,
            imageSize.width / 2, // 图像中心X坐标
            imageSize.height / 2, // 图像中心Y坐标
            imageSize.width / 3, // 变形半径
            _parameterValue,
            _intensity,
            baseImage,
            false, // 不显示变形区域，根据用户要求
            _facialCenterLineX,
            isIncreasing: _isParameterIncreasing ?? (_valueChangeDirection == 'increase' ? true : _valueChangeDirection == 'decrease' ? false : null),
            currentFeaturePoints: baseFeaturePoints // 【关键修复】传递变形后的特征点
          );
          
          print('🖼️ 图像变形已执行完成，结果：${deformedImage != null ? '成功' : '失败'}');
          
          // 使用变形后的特征点和图像作为结果
          updatedFeaturePoints = baseFeaturePoints;
          
          // 更新DeformationCacheManager中的累积变形状态
          // 如果图像变形成功，使用变形后的图像；否则使用原始图像
          final imageToCache = deformedImage ?? baseImage;
          DeformationCacheManager.setLatestDeformedState(imageToCache, updatedFeaturePoints);
          print('💾 已更新变形缓存：图像=${imageToCache.hashCode}，特征点=${updatedFeaturePoints.length}个');
          print('\u2705 变形过程完成：图像变形 + 特征点变形 + 缓存更新');
          
          Logger.flow(_logTag, '_applyImageDeformation', '🔍 [缓存] 保存 $_parameterName 变形结果到DeformationCacheManager');
          // 如果有回调函数，通知变形结果
          if (_onDeformationResultCallback != null && deformedImage != null) {
            Logger.flow(_logTag, '_applyImageDeformation', '🔍 [回调] 通过回调函数通知 $_parameterName 变形完成');
            
            _onDeformationResultCallback!(deformedImage, updatedFeaturePoints);
            print('📲 已通知变形结果回调');
          }
          
          // 记录变形用时
          final duration = DateTime.now().difference(startTime).inMilliseconds;
          Logger.flow(_logTag, '_applyImageDeformation', '✅ 变形完成, 用时: ${duration}ms');
          
          // 简化日志输出，只显示关键信息
          String paramName = _parameterName ?? 'unknown';
          String areaType = _areaType ?? 'unknown';
          Logger.i(_logTag, '✅ 【变形执行】$areaType.$paramName 变形完成, 用时: ${duration}ms, 特征点数量: ${baseFeaturePoints.length}个');
          
          // 【架构清理】移除参数特定的硬编码逻辑，保持架构统一
          
          // 兼容原有输出
          print('变形完成, 用时: ${duration}ms');
          print('变形结果: 特征点数量=${baseFeaturePoints.length}');
          print('✓✓✓ 变形算法执行完成，处理了${baseFeaturePoints.length}个特征点');
          
          // 记录几个关键特征点的位置变化用于验证变形效果
          List<int> keyPoints = [61, 62, 63, 91, 92, 93]; // 唇部关键点
          for (int index in keyPoints) {
            try {
              var point = baseFeaturePoints.firstWhere((p) => p.index == index, orElse: () => FeaturePoint(index: -1, x: 0, y: 0));
              if (point.index != -1) {
                print('特征点[$index]位置: (${point.x.toStringAsFixed(1)}, ${point.y.toStringAsFixed(1)})');
              }
            } catch (e) {}
          }
          
          updatedFeaturePoints = baseFeaturePoints;
        } catch (e) {
          Logger.flowError(_logTag, '_applyImageDeformation', '❌ 应用变形时出错: $e');
          updatedFeaturePoints = baseFeaturePoints;
        }
      } else {
        // 如果没有找到策略，尝试使用本地变形（如果是本地变形类型）
        if (_transformType == TransformType.local) {
          // 【关键修复】使用原始图像尺寸而非Canvas尺寸
          final imageSize = Size(baseImage.width.toDouble(), baseImage.height.toDouble());
          updatedFeaturePoints = _applyLocalDeformation(recordCanvas, imageSize);
        } else {
          // 没有找到策略且不是本地变形，记录错误并使用基准特征点
          Logger.flowError(_logTag, '_applyImageDeformation', '❌ 错误: 未找到参数 $_parameterName 的变形策略，无法执行变形');
          updatedFeaturePoints = baseFeaturePoints;
        }
      }
    } else {
      // 参数名为空或变形服务为空，无法应用变形
      Logger.flowWarning(_logTag, '_applyImageDeformation', '⚠️ 警告: 参数名为空或变形服务为空，无法应用变形');
      updatedFeaturePoints = baseFeaturePoints; // 使用基准特征点
    }
    
    // 【关键修复】更新跳过保存缓存标志
    _skipSaveCache = skipSaveCache;
    print('🔍🔍🔍 [缓存标志更新] skipSaveCache=$skipSaveCache, _skipSaveCache=$_skipSaveCache');
    
    // 将记录器中的绘制内容转换为图像
    final picture = recorder.endRecording();
    // 使用同步方式创建图像，避免异步问题
    try {
      deformedImage = picture.toImageSync(_imageSizeManager.getOriginalWidth(), _imageSizeManager.getOriginalHeight());
      // 将变形后的图像保存到 _deformedImage 字段，以便在切换参数时保持累积变形状态
      _deformedImage = deformedImage;
      // 设置累积变形状态标记
      _hasAccumulatedState = true;
      
      // 通过回调函数将变形结果回传给SimpleDeformationRenderer
      if (_onDeformationResultCallback != null) {
        Logger.flow(_logTag, '_applyImageDeformation', '🔍 [回调] 通过回调函数通知 $_parameterName 变形完成');
        
        _onDeformationResultCallback!(deformedImage, updatedFeaturePoints);
        Logger.i(_logTag, '💾 【变形结果】图像保存成功: 哈希码=${deformedImage.hashCode}, 尺寸=${deformedImage.width}x${deformedImage.height}');
        Logger.i(_logTag, '👁 【特征点累积】当前特征点总数: ${updatedFeaturePoints?.length ?? 0}个');
        Logger.flow(_logTag, '_applyImageDeformation', '📤 通过回调函数将变形结果回传给SimpleDeformationRenderer');
      }
      
      Logger.flow(_logTag, '_applyImageDeformation', '✅ 成功创建变形图像并保存到累积状态');
      Logger.i(_logTag, '💾 【累积状态】变形图像已保存到累积状态');
    } catch (e) {
      Logger.flowError(_logTag, '_applyImageDeformation', '❌ 创建变形图像失败: $e');
    }
    
    // 将绘制结果复制到原始画布上（使用正确的缩放显示）
    if (deformedImage != null) {
      _drawScaledImage(canvas, size, deformedImage);
    }
    
    // 如果变形成功，将结果保存到缓存
    print('🔍🔍🔍 [缓存保存条件检查] _areaType=$_areaType, _parameterName=$_parameterName, deformedImage=${deformedImage?.hashCode}');
    if (_areaType != null && _parameterName != null && deformedImage != null) {
      // 如果没有变形后的特征点，则使用基准特征点
      if (updatedFeaturePoints == null) {
        updatedFeaturePoints = baseFeaturePoints;
      }
      // 使用参数值管理器中的参数值，而不是重新从 TransformationService 获取
      Map<String, double> allParamValues = {};
      
      Map<String, double> currentParams = _parameterValueManager.getAllParameters();
      if (currentParams.isNotEmpty) {
        // 使用参数值管理器中的参数值
        allParamValues.addAll(currentParams);
        Logger.flow(_logTag, '_applyImageDeformation', '📊 使用参数值管理器中的参数值保存缓存: ${currentParams.length} 个参数值');
        // 调试：记录所有参数
        currentParams.forEach((key, value) {
          Logger.flow(_logTag, '_applyImageDeformation', '🔍 参数值管理器参数: $key = $value');
        });
      } else {
        // 【修复】如果参数值管理器中没有参数值，则从 TransformationService 获取
        if (_transformationService != null) {
          final transformParams = _transformationService.getTransformationParams();
          if (transformParams != null) {
            allParamValues.addAll(transformParams);
            // 同步更新参数值管理器
            for (var entry in transformParams.entries) {
              _parameterValueManager.setValue(entry.key, entry.value);
            }
            Logger.flow(_logTag, '_applyImageDeformation', '📊 从 TransformationService 获取并更新参数值管理器: ${transformParams.length} 个参数值');
            // 调试：记录所有参数
            transformParams.forEach((key, value) {
              Logger.flow(_logTag, '_applyImageDeformation', '🔍 TransformationService参数: $key = $value');
            });
          }
        }
      }
      
      // 确保当前参数值是最新的
      if (_parameterName != null && _parameterName!.isNotEmpty) {
        // 【修复】直接使用参数名称，不包含区域信息
        allParamValues[_parameterName!] = _parameterValue;
        _parameterValueManager.setValue(_parameterName!, _parameterValue);
        Logger.flow(_logTag, '_applyImageDeformation', '📊 更新参数值管理器: $_parameterName = $_parameterValue');
      }
      
      // 【调试】最终参数映射
      Logger.flow(_logTag, '_applyImageDeformation', '🔍 最终参数映射中的所有参数:');
      allParamValues.forEach((key, value) {
        Logger.flow(_logTag, '_applyImageDeformation', '🔍 最终参数: $key = $value');
      });
      
      // 移除区域类型标记代码
      
      
      
      // 使用DeformationCacheManager的generateCacheKey方法生成缓存键
      String cacheKey = _deformationCacheManager.generateCacheKey(allParamValues);
      
      Logger.flow(_logTag, '_applyImageDeformation', '🔑 生成缓存键: $cacheKey');
      
      // 保存到缓存 - 确保特征点非空且没有设置_skipSaveCache标志
      print('🔍🔍🔍 [缓存保存条件检查2] updatedFeaturePoints=${updatedFeaturePoints?.length}, _skipSaveCache=$_skipSaveCache');
      
      // 【唇形调整专用调试】
      if (_parameterName == 'lip_shape') {
        print('🔥🔥🔥 [唇形调整专用调试] 缓存保存条件检查');
        print('   • 参数名称: $_parameterName');
        print('   • 区域类型: $_areaType'); 
        print('   • 特征点数量: ${updatedFeaturePoints?.length ?? "null"}');
        print('   • 跳过保存标志: $_skipSaveCache');
        print('   • 变形图像哈希: ${deformedImage?.hashCode ?? "null"}');
        print('   • 参数值: $_parameterValue');
        print('   • 条件判断: updatedFeaturePoints=${updatedFeaturePoints != null}, !_skipSaveCache=${!_skipSaveCache}');
        print('   • 最终条件结果: ${updatedFeaturePoints != null && !_skipSaveCache}');
      }
      
      if (updatedFeaturePoints != null && !_skipSaveCache) {
        // 记录变形完成时间
        final deformationCompleteTime = DateTime.now();
        print('📸 变形完成时间: ${deformationCompleteTime.toString()}');
        
        // 立即触发缓存通知，不等待缓存保存
        try {
          // 生成缓存键
          final cacheKey = _deformationCacheManager.generateCacheKey(allParamValues);
          Logger.flow(_logTag, '_applyImageDeformation', '🔑 生成缓存键: $cacheKey, 参数=${_parameterName}, 时间=${DateTime.now().toString()}');
          
          // 直接通知缓存更新，使用静态方法
          Logger.flow(_logTag, '_applyImageDeformation', '📞 开始通知缓存更新: 参数=${_parameterName}, 缓存键=$cacheKey, 时间=${DateTime.now().toString()}');
          Logger.flow(_logTag, '_applyImageDeformation', '📷 变形图像信息: 哈希码=${deformedImage.hashCode}, 尺寸=${deformedImage.width}x${deformedImage.height}, 参数=${_parameterName}:${_parameterValue}');
          
          Logger.flow(_logTag, '_applyImageDeformation', '➕ $_parameterName 变形: 缓存键=$cacheKey, 时间=${DateTime.now().toString()}');
          
          DeformationCacheManager.notifyCacheUpdated(deformedImage, cacheKey, allParamValues);
          Logger.flow(_logTag, '_applyImageDeformation', '💾 缓存更新完成: 参数=${_parameterName}, 时间=${DateTime.now().toString()}');
          Logger.flow(_logTag, '_applyImageDeformation', '➕ $_parameterName 变形完成通知缓存更新: 时间=${DateTime.now().toString()}');
          
          // 异步保存到缓存（这不会阻塞UI更新）
          Logger.flow(_logTag, '_applyImageDeformation', '💾 开始异步保存到缓存: 参数=${_parameterName}, 时间=${DateTime.now().toString()}');
          
          Logger.flow(_logTag, '_applyImageDeformation', '🔍 [缓存] 保存 $_parameterName 变形结果到DeformationCacheManager');
          
          _deformationCacheManager.save(
            allParamValues,
            deformedImage,
            updatedFeaturePoints,
            facialCenterLineX: _facialCenterLineX,
            facialCenterLineCalculated: _facialCenterLineCalculated
          ).then((_) {
            // 异步保存完成后的回调
            final saveCompleteTime = DateTime.now();
            final saveDuration = saveCompleteTime.difference(deformationCompleteTime).inMilliseconds;
            print('💾 异步保存完成，时间: ${saveCompleteTime.toString()}, 总耗时: ${saveDuration}ms');
          }).catchError((e) {
            print('❌ 异步保存失败: ${e.toString()}');
          });
          
          // 记录处理完成时间
          final processCompleteTime = DateTime.now();
          final processDuration = processCompleteTime.difference(deformationCompleteTime).inMilliseconds;
          print('⏱ 处理完成时间: ${processCompleteTime.toString()}, 耗时: ${processDuration}ms');
          Logger.flow(_logTag, '_applyImageDeformation', '💾 变形结果处理完成, 耗时: ${processDuration}ms');
        } catch (e) {
          print('❌ 处理变形结果失败: ${e.toString()}');
          Logger.flowError(_logTag, '_applyImageDeformation', '❌ 处理变形结果失败: ${e.toString()}');
        }
      } else if (_skipSaveCache) {
        Logger.flow(_logTag, '_applyImageDeformation', '✅ 跳过保存缓存，避免重复保存');
      } else {
        Logger.flowWarning(_logTag, '_applyImageDeformation', '⚠️ 未能保存到缓存：特征点为空');
      }
    }
    
    // 不再创建变形结果对象，这部分逻辑移至SimpleDeformationRenderer中处理
    
    Logger.flow(_logTag, '_applyImageDeformation', '📊 变形操作完成 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue, 强度: $_intensity');
    Logger.flowEnd(_logTag, '_applyImageDeformation');
    return updatedFeaturePoints; // 返回变形后的特征点，如果未变形则返回基准特征点
  }
  
  /// 应用局部变形
  /// 返回变形后的特征点列表
  List<FeaturePoint>? _applyLocalDeformation(Canvas canvas, Size size) {
    Logger.flowStart(_logTag, '_applyLocalDeformation');
    Logger.flow(_logTag, '_applyLocalDeformation', '📊 开始局部变形 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue, 强度: $_intensity');
    
    if (_image == null) {
      Logger.flowError(_logTag, '_applyLocalDeformation', '图像为空，无法应用变形');
      Logger.flowEnd(_logTag, '_applyLocalDeformation');
      return null;
    }
    
    // 计算缩放比例
    final scaleX = size.width / _image!.width.toDouble();
    final scaleY = size.height / _image!.height.toDouble();
    
    // 获取特征点
    final points = _featurePointManager.getFeaturePoints();
    if (points == null || points.isEmpty) {
      Logger.flowError(_logTag, '_applyLocalDeformation', '特征点为空，无法应用变形');
      Logger.flowEnd(_logTag, '_applyLocalDeformation');
      return null;
    }
    
    // 【架构清理】使用现有的_currentParameterPointIndexes，这些已经由策略系统正确设置
    List<int> parameterPointIndexes = _currentParameterPointIndexes;
    Logger.flow(_logTag, '_applyLocalDeformation', '当前参数 $_areaType.$_parameterName 的特征点索引: $parameterPointIndexes');
    
    // 计算参数特征点的平均Y坐标和X坐标范围
    double sumY = 0;
    double minX = double.infinity;
    double maxX = -double.infinity;
    int validPointCount = 0;
    
    for (final point in points) {
      final scaledX = point.x * scaleX;
      final scaledY = point.y * scaleY;
      
      // 【修正】只计算当前参数相关特征点的Y坐标，如果没有参数特征点则使用所有点
      if (parameterPointIndexes.isEmpty || parameterPointIndexes.contains(point.index)) {
        sumY += scaledY;
        validPointCount++;
      }
      
      // X坐标范围仍使用所有特征点
      minX = math.min(minX, scaledX);
      maxX = math.max(maxX, scaledX);
    }
    
    // 【修正】使用当前参数特征点的平均值作为中心点的Y坐标
    double centerY = validPointCount > 0 ? sumY / validPointCount : points.map((p) => p.y * scaleY).reduce((a, b) => a + b) / points.length;
    Logger.flow(_logTag, '_applyLocalDeformation', '变形中心Y坐标: $centerY (基于 $validPointCount 个参数特征点计算)');
    
    // 获取或计算面部中心线X坐标
    double centerX;
    
    // 首次计算面部中心线或重新计算
    if (!_facialCenterLineCalculated || _facialCenterLineX == null) {
      List<FeaturePoint>? allFeaturePoints = _featurePointManager?.getFeaturePoints();
      if (allFeaturePoints != null && allFeaturePoints.isNotEmpty) {
        _facialCenterLineX = _calculateFacialCenterLineX(allFeaturePoints, size.width / _image!.width.toDouble());
        _facialCenterLineCalculated = true; // 标记面部中心线已计算
        Logger.flow(_logTag, '_applyLocalDeformation', '计算并固定面部中心线X坐标: $_facialCenterLineX');
      } else {
        // 如果没有特征点，使用参数点的中心
        _facialCenterLineX = (minX + maxX) / 2;
        _facialCenterLineCalculated = true; // 标记面部中心线已计算
        Logger.flow(_logTag, '_applyLocalDeformation', '无特征点，使用参数点计算面部中心线X坐标: $_facialCenterLineX');
      }
    }
    
    // 确保 _facialCenterLineX 已经被初始化
    if (_facialCenterLineX == null) {
      _facialCenterLineX = size.width / 2; // 默认使用画布中心作为面部中心线
      Logger.flow(_logTag, '_applyLocalDeformation', '无法计算面部中心线，使用画布中心: $_facialCenterLineX');
    }
    
    centerX = _facialCenterLineX!;
    
    // 记录面部中心线位置
    Logger.flow(_logTag, '_applyLocalDeformation', '使用面部中心线X坐标: $centerX');
    
    // 绘制面部中心线
    if (_showDeformationArea) {
      // u9762u90e8u4e2du5fc3u7ebfu7ed8u5236u529fu80fdu5df2u79fbu9664
      // u5982u9700u7ed8u5236u9762u90e8u4e2du5fc3u7ebfuff0cu5e94u4f7fu7528u53d8u5f62u7b56u7565u4e2du7684u65b9u6cd5
    }
    
    // 计算变形半径
    double radius = 0;
    for (final point in points) {
      final scaledX = point.x * scaleX;
      final scaledY = point.y * scaleY;
      
      final distance = math.sqrt(math.pow(scaledX - centerX, 2) + math.pow(scaledY - centerY, 2));
      radius = math.max(radius, distance);
    }
    
    // 增加半径以确保覆盖整个变形区域
    radius *= 1.5;
    
    // 【架构统一】完全依赖策略模式实现，无任何通用变形逻辑
    Logger.flow(_logTag, '_applyLocalDeformation', '📊 使用参数值: $_parameterValue, 强度: $_intensity');
    
    // 获取变形策略 - 每个参数项都必须实现自己的专门变形策略
    final transformationFactory = TransformationFactory();
    final strategy = transformationFactory.getStrategy(_parameterName);
    
    // 如果没有对应的变形策略，直接绘制原始图像，不执行任何变形
    if (strategy == null) {
      Logger.flow(_logTag, '_applyLocalDeformation', '⚠️ 未找到参数 $_parameterName 的变形策略，跳过变形处理');
      _drawOriginalImage(canvas, size);
      Logger.flowEnd(_logTag, '_applyLocalDeformation');
      return _featurePoints; // 返回原始特征点
    }
    
    Logger.flow(_logTag, '_applyLocalDeformation', '✅ 使用专门变形策略: $_parameterName');
    
    Logger.flow(_logTag, '_applyLocalDeformation', '应用变形策略: $_parameterName, 参数值: $_parameterValue, 强度: $_intensity');
    
    // 创建一个记录器，用于捕获变形结果
    canvas.save();
    
    // 选择变形输入图像 - 始终优先使用累积变形后的图像
    ui.Image imageToUse;
    if (_hasAccumulatedState && _deformedImage != null) {
      imageToUse = _deformedImage!;
      Logger.flow(_logTag, '_applyLocalDeformation', '📷 使用累积变形图片作为变形输入，确保变形效果累积');
    } else {
      imageToUse = _image!;
      Logger.flow(_logTag, '_applyLocalDeformation', '📷 使用原始图片作为变形输入（首次变形）');
    }
    
    // 创建一个记录器，用于捕获画布绘制结果
    final recorder = ui.PictureRecorder();
    final recordCanvas = Canvas(recorder);
    
    // 先绘制基准图像（原始图像或累积变形图像）
    final srcRect = Rect.fromLTWH(0, 0, imageToUse.width.toDouble(), imageToUse.height.toDouble());
    final dstRect = Rect.fromLTWH(0, 0, size.width, size.height);
    recordCanvas.drawImageRect(imageToUse, srcRect, dstRect, Paint());
    Logger.flow(_logTag, '_applyLocalDeformation', '📷 绘制基准图像作为变形起点');
    
    strategy.applyImageTransformation(
      recordCanvas, 
      size, 
      centerX, 
      centerY, 
      radius, 
      _parameterValue,
      _intensity,
      imageToUse, // 使用累积变形图像或原始图像作为变形输入
      _showDeformationArea,
      _facialCenterLineX,
      isIncreasing: _isParameterIncreasing ?? (_valueChangeDirection == 'increase' ? true : _valueChangeDirection == 'decrease' ? false : null),
      currentFeaturePoints: points  // 传递当前特征点
    );
    
    // 将记录器中的绘制内容转换为图像
    final picture = recorder.endRecording();
    // 使用同步方式创建图像，避免异步问题
    try {
      // 使用原始图像尺寸创建变形图像，确保尺寸一致性
      ui.Image deformedImage = picture.toImageSync(_imageSizeManager.getOriginalWidth(), _imageSizeManager.getOriginalHeight());
      Logger.flow(_logTag, '_applyLocalDeformation', '📏 使用画布尺寸创建图像: ${size.width.toInt()}x${size.height.toInt()}');
      // 将变形后的图像保存到 _deformedImage 字段，以便在切换参数时保持累积变形状态
      _deformedImage = deformedImage;
      // 设置累积变形状态标记
      _hasAccumulatedState = true;
      Logger.flow(_logTag, '_applyLocalDeformation', '✅ 成功创建变形图像并保存到累积状态');
      
      // 保存变形结果到缓存，使用参数值管理器中的参数值作为缓存键
      
      // 【唇形调整专用调试2】
      if (_parameterName == 'lip_shape') {
        print('🔥🔥🔥 [唇形调整专用调试2] _applyLocalDeformation缓存保存检查');
        print('   • 参数名称: $_parameterName');
        print('   • 跳过保存标志: $_skipSaveCache');
        print('   • 变形图像哈希: ${deformedImage.hashCode}');
        print('   • 特征点数量: ${_deformedFeaturePoints?.length ?? "null"}');
        print('   • 条件结果: ${!_skipSaveCache}');
      }
      
      if (!_skipSaveCache) {
        Map<String, double> allParams = _parameterValueManager.getAllParameters();
        _saveCacheIfNeeded(allParams, deformedImage, _deformedFeaturePoints);
        Logger.flow(_logTag, '_applyLocalDeformation', '💾 变形结果已保存到缓存，确保参数切换时能正确恢复');
      } else {
        Logger.flow(_logTag, '_applyLocalDeformation', '⏭️ 跳过保存缓存');
      }
      
      // 关键：通过回调函数将变形图像传递给 SimpleDeformationRenderer
      if (_onDeformationResultCallback != null) {
        _onDeformationResultCallback!(_deformedImage, _deformedFeaturePoints);
        Logger.flow(_logTag, '_applyLocalDeformation', '📤 通过回调函数将变形图像传递给 SimpleDeformationRenderer');
      } else {
        Logger.flowWarning(_logTag, '_applyLocalDeformation', '⚠️ 回调函数为空，无法将变形图像传递给 SimpleDeformationRenderer');
      }
      
      // 将绘制结果复制到原始画布上（使用正确的缩放显示）
      _drawScaledImage(canvas, size, deformedImage);
    } catch (e) {
      Logger.flowError(_logTag, '_applyLocalDeformation', '❌ 创建变形图像失败: $e');
      // 如果创建变形图像失败，则直接在原始画布上应用变形
      strategy.applyImageTransformation(
        canvas, 
        size, 
        centerX, 
        centerY, 
        radius, 
        _parameterValue,
        _intensity,
        imageToUse,
        _showDeformationArea,
        _facialCenterLineX,
        isIncreasing: _isParameterIncreasing ?? (_valueChangeDirection == 'increase' ? true : _valueChangeDirection == 'decrease' ? false : null),
        currentFeaturePoints: points  // 传递当前特征点
      );
    }
    
    canvas.restore();
    
    Logger.flow(_logTag, '_applyLocalDeformation', '✅ 应用变形策略完成');
    
    // 统一的策略模式特征点变形处理
    List<FeaturePoint>? updatedFeaturePoints;
    
    if (strategy != null) {
      // 复制特征点列表以避免修改原始数据
      updatedFeaturePoints = List<FeaturePoint>.from(points);
      
      // 确定变形方向
      bool? isIncreasing = _isParameterIncreasing ?? (_valueChangeDirection == 'increase' ? true : _valueChangeDirection == 'decrease' ? false : null);
      
      Logger.flow(_logTag, '_applyLocalDeformation', '🔄 策略模式变形 | 参数: $_parameterName | 参数值: $_parameterValue | 变形方向: ${isIncreasing == true ? "增大" : (isIncreasing == false ? "减小" : "无")} | 强度: $_intensity');
      
      // 先应用图像变形，获取变形后的图像
      ui.Image? deformedImage = strategy.applyImageTransformation(
        canvas, 
        size, 
        centerX, 
        centerY, 
        radius, 
        _parameterValue,
        _intensity,
        imageToUse,
        _showDeformationArea,
        _facialCenterLineX,
        isIncreasing: isIncreasing,
        currentFeaturePoints: updatedFeaturePoints  // 传递当前特征点
      );
      
      // 保存变形后的图像到累积状态
      if (deformedImage != null) {
        _deformedImage = deformedImage;
        Logger.flow(_logTag, '_applyLocalDeformation', '✅ 策略模式图像变形成功 | 图像哈希: ${deformedImage.hashCode} | 尺寸: ${deformedImage.width}x${deformedImage.height}');
      }
      
      // 然后应用特征点变形
      strategy.applyFeaturePointTransformation(
        updatedFeaturePoints!,
        parameterPointIndexes,
        _parameterValue,
        _intensity,
        facialCenterLineX: centerX,
        isIncreasing: isIncreasing
      );
      
      Logger.flow(_logTag, '_applyLocalDeformation', '✅ 策略模式变形成功应用 | 参数: $_parameterName | 特征点数量: ${updatedFeaturePoints.length}');
    } else {
      Logger.flowError(_logTag, '_applyLocalDeformation', '❌ 错误: 参数 $_parameterName 未实现变形策略，无法执行变形');
      updatedFeaturePoints = null;
    }
    
    // 如果成功获取到变形后的特征点，则更新特征点管理器和累积变形状态
    if (updatedFeaturePoints != null && updatedFeaturePoints!.isNotEmpty) {
      _deformedFeaturePoints = updatedFeaturePoints;
      _featurePointManager.updateFeaturePoints(updatedFeaturePoints!);
      Logger.flow(_logTag, '_applyLocalDeformation', '✅ 更新变形后特征点到累积状态 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue, 特征点数量: ${updatedFeaturePoints!.length}');
      
      // 再次通过回调函数将最终的变形结果（包括特征点）传递给 SimpleDeformationRenderer
      if (_onDeformationResultCallback != null && _deformedImage != null) {
        _onDeformationResultCallback!(_deformedImage!, updatedFeaturePoints!);
        Logger.flow(_logTag, '_applyLocalDeformation', '📤 通过回调函数将最终变形结果（含特征点）传递给 SimpleDeformationRenderer');
      }
    }
    
    // 【关键修复】如果策略模式生成了变形图像，立即绘制到主Canvas
    if (_deformedImage != null) {
      Logger.flow(_logTag, '_applyLocalDeformation', '🎨 策略模式变形图像绘制到主Canvas | 哈希: ${_deformedImage!.hashCode}');
      _drawScaledImage(canvas, size, _deformedImage!);
    } else {
      Logger.flowWarning(_logTag, '_applyLocalDeformation', '⚠️ 策略模式没有生成变形图像，主Canvas将显示原始图像');
    }
    
    Logger.flow(_logTag, '_applyLocalDeformation', '📊 局部变形完成 - 区域: $_areaType, 参数: $_parameterName, 值: $_parameterValue, 强度: $_intensity');
    Logger.flowEnd(_logTag, '_applyLocalDeformation');
    return updatedFeaturePoints;
  }
  
  /// 生成缓存键
  String _generateCacheKey(Map<String, double> parameterValues) {
    return DeformationCacheManager().generateCacheKey(parameterValues);
  }
  
  /// 计算面部中心线X坐标
  double _calculateFacialCenterLineX(List<FeaturePoint> featurePoints, double scaleX) {
    Logger.flowStart(_logTag, '_calculateFacialCenterLineX');
    
    // 如果面部中心线已经固定，直接返回缓存的值
    if (_facialCenterLineCalculated && _facialCenterLineX != null) {
      Logger.flow(_logTag, '_calculateFacialCenterLineX', '使用已固定的面部中心线: $_facialCenterLineX');
      Logger.flowEnd(_logTag, '_calculateFacialCenterLineX');
      return _facialCenterLineX!;
    }
    
    // 严格要求：确保所有必要条件都满足
    if (_image == null) {
      Logger.flowError(_logTag, '_calculateFacialCenterLineX', '❌ 严重错误: 图像为空，无法计算面部中心线');
      throw Exception('图像为空，无法计算面部中心线');
    }
    
    if (featurePoints.isEmpty) {
      Logger.flowError(_logTag, '_calculateFacialCenterLineX', '❌ 严重错误: 特征点列表为空，无法计算面部中心线');
      throw Exception('特征点列表为空，无法计算面部中心线');
    }
    
    // 1. 查找眼睛和鼻孔对称特征点（必须找到这些关键点才能计算中心线）
    FeaturePoint? leftEye;
    FeaturePoint? rightEye;
    FeaturePoint? leftNostril;
    FeaturePoint? rightNostril;
    
    // 查找关键特征点
    for (final point in featurePoints) {
      String id = point.id.toLowerCase();
      
      // 眼睛特征点 - 使用更精确的匹配条件
      if ((id.contains('left') || id.contains('左')) && 
          (id.contains('eye') || id.contains('眼')) && 
          (id.contains('center') || id.contains('中心'))) {
        leftEye = point;
      } else if ((id.contains('right') || id.contains('右')) && 
                (id.contains('eye') || id.contains('眼')) && 
                (id.contains('center') || id.contains('中心'))) {
        rightEye = point;
      }
      // 鼻孔特征点 - 使用更精确的匹配条件
      else if ((id.contains('left') || id.contains('左')) && 
              (id.contains('nostril') || id.contains('鼻孔'))) {
        leftNostril = point;
      } else if ((id.contains('right') || id.contains('右')) && 
                (id.contains('nostril') || id.contains('鼻孔'))) {
        rightNostril = point;
      }
    }
    
    // 如果没有找到特征点对，尝试通过索引查找
    if (leftEye == null || rightEye == null) {
      // 尝试通过索引找到眼睛特征点
      for (final point in featurePoints) {
        if (point.index == 33 || point.index == 133 || point.index == 159) { // 左眼中心的常见索引
          leftEye = point;
        } else if (point.index == 362 || point.index == 263 || point.index == 386) { // 右眼中心的常见索引
          rightEye = point;
        }
      }
    }
    
    // 如果没有找到鼻孔特征点，尝试通过索引查找
    if (leftNostril == null || rightNostril == null) {
      for (final point in featurePoints) {
        if (point.index == 220 || point.index == 242) { // 左鼻孔的常见索引
          leftNostril = point;
        } else if (point.index == 442 || point.index == 464) { // 右鼻孔的常见索引
          rightNostril = point;
        }
      }
    }
    
    // 严格验证：确保必须找到眼睛对称点
    if (leftEye == null || rightEye == null) {
      Logger.flowError(_logTag, '_calculateFacialCenterLineX', '❌ 严重错误: 未找到眼睛对称特征点，无法计算面部中心线');
      throw Exception('未找到眼睛对称特征点，无法计算面部中心线');
    }
    
    // 2. 计算眼睛中心线（主要参考点）
    double eyesCenterX = (leftEye.x + rightEye.x) / 2 * scaleX;
    Logger.flow(_logTag, '_calculateFacialCenterLineX', '眼睛中心线: $eyesCenterX (左眼: ${leftEye.x}, 右眼: ${rightEye.x})');
    
    // 3. 如果有鼻孔特征点，与眼睛中心线加权平均
    double facialCenterX = eyesCenterX;
    
    if (leftNostril != null && rightNostril != null) {
      double noseCenterX = (leftNostril.x + rightNostril.x) / 2 * scaleX;
      Logger.flow(_logTag, '_calculateFacialCenterLineX', '鼻孔中心线: $noseCenterX (左鼻孔: ${leftNostril.x}, 右鼻孔: ${rightNostril.x})');
      
      // 加权平均：眼睛权重0.7，鼻孔权重0.3
      facialCenterX = eyesCenterX * 0.7 + noseCenterX * 0.3;
      Logger.flow(_logTag, '_calculateFacialCenterLineX', '加权平均中心线: $facialCenterX');
    }
    
    // 4. 保存计算结果并标记为已固定
    _facialCenterLineX = facialCenterX;
    _facialCenterLineCalculated = true;
    
    Logger.flow(_logTag, '_calculateFacialCenterLineX', '✅ 面部中心线已固定: $_facialCenterLineX，将在整个变形生命周期内使用');
    Logger.flowEnd(_logTag, '_calculateFacialCenterLineX');
    
    return facialCenterX;
  }
  

  
  /// 安全地通知变形结果，确保回调在当前帧渲染完成后执行
  /// 这样可以避免在渲染过程中触发状态更新，防止"Build scheduled during frame"异常
  void _safelyNotifyDeformationResult(ui.Image? deformedImage, List<FeaturePoint>? deformedFeaturePoints) {
    if (_onDeformationResultCallback != null) {
      // 使用Future.microtask确保回调在当前帧渲染完成后执行
      Future.microtask(() {
        _onDeformationResultCallback!(deformedImage, deformedFeaturePoints);
        Logger.flow(_logTag, '_safelyNotifyDeformationResult', '📤 在当前帧渲染完成后通过回调函数将变形结果回传给SimpleDeformationRenderer');
      });
    }
  }

  @override
  bool shouldRepaint(covariant SimpleDeformationPainter oldDelegate) {
    Logger.flowStart(_logTag, 'shouldRepaint');
    bool shouldRepaint = _needsRepaint || 
                        oldDelegate._parameterValue != _parameterValue ||
                        oldDelegate._parameterName != _parameterName ||
                        oldDelegate._areaType != _areaType ||
                        oldDelegate._showFeaturePoints != _showFeaturePoints ||
                        oldDelegate._showDeformationArea != _showDeformationArea ||
                        oldDelegate._showCoordinateSystem != _showCoordinateSystem ||
                        oldDelegate._breathingValue != _breathingValue ||
                        oldDelegate._deformedImage != _deformedImage ||
                        oldDelegate._image != _image ||
                        oldDelegate._sideImage != _sideImage;
    
    if (shouldRepaint) {
      Logger.flow(_logTag, 'shouldRepaint', '需要重绘，原因：参数变化或标记为需要重绘');
    }
    
    _needsRepaint = false; // 重置重绘标志
    Logger.flowEnd(_logTag, 'shouldRepaint');
    return shouldRepaint;
  }
  

}


