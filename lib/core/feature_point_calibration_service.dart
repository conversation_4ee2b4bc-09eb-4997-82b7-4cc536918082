import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:beautifun/core/models/feature_point.dart';
import 'package:beautifun/core/simple_deformation_renderer.dart';
import 'package:beautifun/beautify_feature/services/transformation_service.dart';
import 'package:beautifun/utils/logger.dart';

/// 特征点校准服务
/// 
/// 负责在科技扫描完成后校准特征点位置
class FeaturePointCalibrationService {
  static const String _logTag = 'FeaturePointCalibrationService';
  
  // 单例实例
  static final FeaturePointCalibrationService _instance = FeaturePointCalibrationService._internal();
  
  // 获取单例实例
  static FeaturePointCalibrationService get instance => _instance;
  
  // 私有构造函数
  FeaturePointCalibrationService._internal() {
    Logger.flowStart(_logTag, 'constructor');
    Logger.flow(_logTag, 'constructor', '✅ 特征点校准服务初始化完成');
    Logger.flowEnd(_logTag, 'constructor');
  }
  
  /// 在科技扫描完成后校准特征点
  /// 
  /// [deformedImage] 变形后的图像
  /// [renderer] 变形渲染器
  /// 
  /// 返回校准是否成功
  Future<bool> calibrateFeaturePointsOnTechScan(ui.Image deformedImage, SimpleDeformationRenderer renderer) async {
    Logger.flowStart(_logTag, 'calibrateFeaturePointsOnTechScan');
    Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '▶️ 开始科技扫描特征点校准');
    
    try {
      // 1. 获取当前特征点状态
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '1️⃣ 获取当前特征点状态');
      final currentFeaturePoints = renderer.getFeaturePoints();
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '   当前特征点数量: ${currentFeaturePoints.length}个');
      
      // 2. 识别变形图像特征点
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '2️⃣ 识别变形图像特征点');
      final latestImage = deformedImage;
      
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '   图像尺寸: ${latestImage.width}x${latestImage.height}');
      
      // 输出图像详细信息到控制台
      print('======== 科技扫描校准图片信息 ========');
      print('📷 校准图像详细信息:');
      print('  - 哈希码: ${latestImage.hashCode}');
      print('  - 宽度: ${latestImage.width}');
      print('  - 高度: ${latestImage.height}');
      print('  - 内存地址: ${latestImage.toString()}');
      print('  - 估计内存占用: ${(latestImage.width * latestImage.height * 4) / 1024} KB');
      print('  - 记录时间: ${DateTime.now().toIso8601String()}');
      print('  - 期望尺寸: 1024x1478');
      print('  - 尺寸匹配: ${latestImage.width == 1024 && latestImage.height == 1478 ? "是" : "否"}');
      print('============================');
      
      // 获取原始图像用于比较
      final originalImage = renderer.getOriginalImage();
      if (originalImage == null) {
        Logger.flowError(_logTag, 'calibrateFeaturePointsOnTechScan', '❌ 无法获取原始图像');
        Logger.flowEnd(_logTag, 'calibrateFeaturePointsOnTechScan');
        return false;
      }
      
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '   原始图像尺寸: ${originalImage.width}x${originalImage.height}');
      
      // 检查图像尺寸是否一致
      final sizesMatch = latestImage.width == originalImage.width && latestImage.height == originalImage.height;
      
      // 计算图像尺寸比例，但我们不会使用它进行缩放，因为我们已经确保图像尺寸一致
      final widthRatio = latestImage.width / originalImage.width;
      final heightRatio = latestImage.height / originalImage.height;
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '   图像尺寸一致: ${sizesMatch ? "是" : "否"}');
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '   图像尺寸比例: 宽度=${widthRatio.toStringAsFixed(3)}, 高度=${heightRatio.toStringAsFixed(3)} (不进行缩放)');
      
      // 对变形后的图像进行特征点识别
      final recognizedPoints = await _recognizeFeaturePoints(latestImage);
      
      if (recognizedPoints == null || recognizedPoints.isEmpty) {
        Logger.flowError(_logTag, 'calibrateFeaturePointsOnTechScan', '❌ 特征点识别失败或结果为空');
        Logger.flowEnd(_logTag, 'calibrateFeaturePointsOnTechScan');
        return false;
      }
      
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '✓ 识别成功: ${recognizedPoints.length}个特征点');
      
      // 3. 特征点对比分析
      final pointCountDiff = recognizedPoints.length - currentFeaturePoints.length;
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '3️⃣ 特征点对比分析');
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '   特征点变化: ${currentFeaturePoints.length}个 → ${recognizedPoints.length}个 (${pointCountDiff > 0 ? "+$pointCountDiff" : pointCountDiff})');
      
      // 3.5 特征点坐标校准
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '3.5️⃣ 特征点坐标校准');
      
      // 使用已识别的特征点，不进行额外校准
      final calibratedPoints = recognizedPoints;
      
      // 输出前5个校准后的特征点坐标
      if (calibratedPoints.length >= 5) {
        Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '   前5个特征点坐标示例:');
        for (int i = 0; i < 5; i++) {
          final point = calibratedPoints[i];
          Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '     点[${point.index}]: (${point.x.toStringAsFixed(1)}, ${point.y.toStringAsFixed(1)})');
        }
      }
      
      // 4. 更新渲染器特征点数据
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '4️⃣ 更新渲染器特征点数据');
      renderer.updateFeaturePoints(calibratedPoints);
      
      // 5. 刷新UI显示
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '5️⃣ 刷新UI显示');
      
      Logger.flow(_logTag, 'calibrateFeaturePointsOnTechScan', '✅ 特征点校准完成');
      Logger.flowEnd(_logTag, 'calibrateFeaturePointsOnTechScan');
      return true;
    } catch (e) {
      Logger.flowError(_logTag, 'calibrateFeaturePointsOnTechScan', '❌ 特征点校准过程中发生异常: $e');
      Logger.flowEnd(_logTag, 'calibrateFeaturePointsOnTechScan');
      return false;
    }
  }
  
  /// 对图像进行特征点识别
  /// 
  /// [image] 需要识别特征点的图像
  /// 返回识别到的特征点列表
  Future<List<FeaturePoint>?> _recognizeFeaturePoints(ui.Image image) async {
    Logger.flowStart(_logTag, '_recognizeFeaturePoints');
    Logger.flow(_logTag, '_recognizeFeaturePoints', '🔍 开始对图像进行特征点识别');
    
    try {
      // 获取TransformationService实例
      final transformationService = TransformationService.instance;
      
      // 使用TransformationService进行特征点识别
      Logger.flow(_logTag, '_recognizeFeaturePoints', '📊 调用TransformationService识别特征点');
      final recognizedPoints = await transformationService.recognizeFeaturePoints(image);
      
      if (recognizedPoints == null || recognizedPoints.isEmpty) {
        Logger.flowError(_logTag, '_recognizeFeaturePoints', '❌ 特征点识别失败或结果为空');
        Logger.flowEnd(_logTag, '_recognizeFeaturePoints');
        return null;
      }
      
      Logger.flow(_logTag, '_recognizeFeaturePoints', '✅ 特征点识别成功，识别到 ${recognizedPoints.length} 个特征点');
      
      // 输出前3个识别到的特征点的坐标
      if (recognizedPoints.length >= 3) {
        Logger.flow(_logTag, '_recognizeFeaturePoints', '📍 前3个识别到的特征点坐标:');
        for (int i = 0; i < 3; i++) {
          final point = recognizedPoints[i];
          Logger.flow(_logTag, '_recognizeFeaturePoints', '   点[${point.index}]: (${point.x.toStringAsFixed(1)}, ${point.y.toStringAsFixed(1)})');
        }
      }
      
      Logger.flowEnd(_logTag, '_recognizeFeaturePoints');
      return recognizedPoints;
    } catch (e) {
      Logger.flowError(_logTag, '_recognizeFeaturePoints', '❌ 特征点识别过程中发生异常: $e');
      Logger.flowEnd(_logTag, '_recognizeFeaturePoints');
      return null;
    }
  }
}