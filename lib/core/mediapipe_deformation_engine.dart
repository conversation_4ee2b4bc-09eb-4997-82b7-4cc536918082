import 'dart:io';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:collection/collection.dart';
import 'dart:math' as math;
import 'package:vector_math/vector_math_64.dart';
import 'package:beautifun/utils/logger.dart';
import 'platform_adapters/ml_kit_adapter.dart';

/// 变形结果
class DeformationResult {
  final bool success;
  final String outputPath;
  final String errorMessage;
  
  DeformationResult({
    required this.success,
    this.outputPath = '',
    this.errorMessage = '',
  });
}

/// 变形图像结果
class DeformationImageResult {
  final ui.Image image;
  final List<Offset> calibratedPoints;
  
  DeformationImageResult({
    required this.image,
    required this.calibratedPoints,
  });
}

/// MediaPipe面部变形引擎
/// 
/// 使用MediaPipe提取面部特征点并应用变形效果
class MediapipeDeformationEngine {
  final String _logTag = 'MediapipeDeformationEngine';

  /// Face Detector实例
  final FaceDetector _faceDetector = MLKitAdapter.createFaceDetector(
    enableContours: true,
    enableLandmarks: true,
    enableClassification: true,
    enableTracking: false,
    performanceMode: FaceDetectorMode.accurate,
  );
  
  /// 特征点映射表 - 将MediaPipe的468点映射到我们需要的特征点
  /// 这里需要根据实际需要进行映射
  final Map<int, int> _landmarkMapping = {
    // 眼睛区域
    // 左眼
    33: 0,   // 左眼内角
    133: 1,  // 左眼上缘
    159: 2,  // 左眼外角
    145: 3,  // 左眼下缘
    // 右眼
    362: 4,  // 右眼内角
    386: 5,  // 右眼上缘
    263: 6,  // 右眼外角
    374: 7,  // 右眼下缘
    
    // 鼻子区域
    1: 8,    // 鼻尖
    4: 9,    // 鼻梁
    94: 10,  // 左鼻翼
    324: 11, // 右鼻翼
    
    // 嘴巴区域
    61: 12,  // 嘴左角
    0: 13,   // 嘴上中点
    291: 14, // 嘴右角
    17: 15,  // 嘴下中点
    
    // 脸部轮廓
    10: 16,  // 左脸颊
    152: 17, // 左下颌
    234: 18, // 下巴
    454: 19, // 右下颌
    227: 20, // 右脸颊
  };
  
  /// 应用变形
  /// 
  /// [imagePath] 原始图像路径
  /// [deformations] 变形数据，键为特征点索引，值为偏移量
  /// 
  /// 返回变形结果
  Future<DeformationResult> applyDeformation({
    required String imagePath,
    required Map<int, Offset> deformations,
  }) async {
    Logger.flowStart(_logTag, 'applyDeformation');
    Logger.flow(_logTag, 'applyDeformation', '📊 开始应用MediaPipe变形 | 图像: ${path.basename(imagePath)} | 变形点数: ${deformations.length}');
    
    try {
      // 检查输入图像是否存在
      final File imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        Logger.flowError(_logTag, 'applyDeformation', '❌ 输入图像不存在: $imagePath');
        Logger.flowEnd(_logTag, 'applyDeformation');
        return DeformationResult(
          success: false,
          errorMessage: '输入图像不存在: $imagePath',
        );
      }
      
      // 使用适配器检测人脸
      final List<Face> faces = await MLKitAdapter.processImage(_faceDetector, imagePath);
      if (faces.isEmpty) {
        Logger.flowError(_logTag, 'applyDeformation', '❌ 未检测到人脸');
        Logger.flowEnd(_logTag, 'applyDeformation');
        return DeformationResult(
          success: false,
          errorMessage: '未检测到人脸',
        );
      }
      
      // 获取第一个检测到的人脸
      final Face face = faces.first;
      
      // 创建特征点列表
      List<Offset> landmarks = [];
      
      if (Platform.isMacOS) {
        // 在 macOS 上直接使用所有面部轮廓点
        Logger.flow(_logTag, 'applyDeformation', '📊 使用模拟面部轮廓点进行变形');
        
        // 添加主要特征点
        face.landmarks.forEach((type, landmark) {
          if (landmark != null) {
            landmarks.add(Offset(landmark.position.x.toDouble(), landmark.position.y.toDouble()));
          }
        });
        
        // 添加面部轮廓点
        face.contours.forEach((type, contour) {
          if (contour != null && contour.points.isNotEmpty) {
            for (final point in contour.points) {
              landmarks.add(Offset(point.x.toDouble(), point.y.toDouble()));
            }
          }
        });
      } else {
        // 在其他平台上使用原来的方法
        final List<FaceLandmark?> faceLandmarks = [
          face.landmarks[FaceLandmarkType.leftEye],
          face.landmarks[FaceLandmarkType.rightEye],
          face.landmarks[FaceLandmarkType.leftCheek],
          face.landmarks[FaceLandmarkType.rightCheek],
          face.landmarks[FaceLandmarkType.noseBase],
          face.landmarks[FaceLandmarkType.bottomMouth],
          face.landmarks[FaceLandmarkType.leftMouth],
          face.landmarks[FaceLandmarkType.rightMouth],
        ];
        
        // 过滤掉空值
        final List<FaceLandmark> validLandmarks = faceLandmarks
            .whereNotNull()
            .toList();
        
        if (validLandmarks.isEmpty) {
          Logger.flowError(_logTag, 'applyDeformation', '❌ 未提取到有效的面部特征点');
          Logger.flowEnd(_logTag, 'applyDeformation');
          return DeformationResult(
            success: false,
            errorMessage: '未提取到有效的面部特征点',
          );
        }
        
        // 将特征点转换为我们需要的格式
        landmarks = validLandmarks
            .map((landmark) => Offset(landmark.position.x.toDouble(), landmark.position.y.toDouble()))
            .toList();
      }
      
      if (landmarks.isEmpty) {
        Logger.flowError(_logTag, 'applyDeformation', '❌ 未提取到有效的面部特征点');
        Logger.flowEnd(_logTag, 'applyDeformation');
        return DeformationResult(
          success: false,
          errorMessage: '未提取到有效的面部特征点',
        );
      }
      
      // 读取原始图像
      final img.Image? originalImage = img.decodeImage(await imageFile.readAsBytes());
      if (originalImage == null) {
        Logger.flowError(_logTag, 'applyDeformation', '❌ 无法解码图像');
        Logger.flowEnd(_logTag, 'applyDeformation');
        return DeformationResult(
          success: false,
          errorMessage: '无法解码图像',
        );
      }
      
      // 记录原始图像尺寸
      Logger.flow(_logTag, 'applyDeformation', '📏 原始图像尺寸: ${originalImage.width}x${originalImage.height}');
      
      // 增强变形效果 - 将变形向量放大
      final Map<int, Offset> enhancedDeformations = {};
      deformations.forEach((index, offset) {
        // 将变形向量放大 3 倍，使效果更明显
        enhancedDeformations[index] = Offset(offset.dx * 3.0, offset.dy * 3.0);
      });
      
      Logger.flow(_logTag, 'applyDeformation', '📈 应用增强的变形效果，放大系数: 3.0');
      
      // 应用变形，确保使用原始图像尺寸
      final img.Image? deformedImage = await _applyImageDeformation(
        originalImage,
        landmarks,
        enhancedDeformations,
        originalImage.width,
        originalImage.height,
      );
      
      if (deformedImage == null) {
        Logger.flowError(_logTag, 'applyDeformation', '❌ 图像变形失败');
        Logger.flowEnd(_logTag, 'applyDeformation');
        return DeformationResult(
          success: false,
          errorMessage: '图像变形失败',
        );
      }
      
      // 验证变形后图像尺寸
      Logger.flow(_logTag, 'applyDeformation', '📏 变形后图像尺寸: ${deformedImage.width}x${deformedImage.height}');
      if (deformedImage.width != originalImage.width || deformedImage.height != originalImage.height) {
        Logger.flowWarning(_logTag, 'applyDeformation', '⚠️ 变形后图像尺寸与原始图像不一致，将调整为原始尺寸');
        final img.Image resizedImage = img.copyResize(
          deformedImage,
          width: originalImage.width,
          height: originalImage.height,
          interpolation: img.Interpolation.linear
        );
        
        // 保存结果
        final String outputPath = await _saveImage(resizedImage, imagePath);
        Logger.flow(_logTag, 'applyDeformation', '📁 变形完成，结果保存到: $outputPath');
        
        Logger.flowEnd(_logTag, 'applyDeformation');
        return DeformationResult(
          success: true,
          outputPath: outputPath,
        );
      }
      
      // 保存结果
      final String outputPath = await _saveImage(deformedImage, imagePath);
      Logger.flow(_logTag, 'applyDeformation', '📁 变形完成，结果保存到: $outputPath');
      
      Logger.flowEnd(_logTag, 'applyDeformation');
      return DeformationResult(
        success: true,
        outputPath: outputPath,
      );
    } catch (e) {
      Logger.flowError(_logTag, 'applyDeformation', '❌ 应用变形时出错: $e');
      Logger.flowEnd(_logTag, 'applyDeformation');
      return DeformationResult(
        success: false,
        errorMessage: '应用变形时出错: $e',
      );
    } finally {
      // 在 macOS 上不调用 close 方法
      try {
        if (!Platform.isMacOS) {
          await _faceDetector.close();
        }
      } catch (e) {
        Logger.flowError(_logTag, 'applyDeformation', '❌ 关闭面部检测器时出错: $e');
      }
    }
  }
  
  /// 应用图像变形
  /// 
  /// 使用Delaunay三角剖分和仿射变换实现图像变形
  Future<img.Image?> _applyImageDeformation(
    img.Image originalImage,
    List<Offset> landmarks,
    Map<int, Offset> deformations,
    int targetWidth,
    int targetHeight,
  ) async {
    Logger.flowStart(_logTag, '_applyImageDeformation');
    Logger.flow(_logTag, '_applyImageDeformation', '📊 开始应用图像变形');
    Logger.flow(_logTag, '_applyImageDeformation', '📏 原始图像尺寸: ${originalImage.width}x${originalImage.height}');
    Logger.flow(_logTag, '_applyImageDeformation', '📏 目标图像尺寸: ${targetWidth}x${targetHeight}');
    
    try {
      // 确保目标尺寸与原始图像尺寸相同
      if (targetWidth != originalImage.width || targetHeight != originalImage.height) {
        Logger.flowError(_logTag, '_applyImageDeformation', '⚠️ 目标尺寸与原始图像不符，使用原始图像尺寸');
        targetWidth = originalImage.width;
        targetHeight = originalImage.height;
      }
      
      // 创建结果图像副本 - 确保尺寸与原始图像完全相同
      final img.Image resultImage = img.copyResize(
        originalImage,
        width: originalImage.width,
        height: originalImage.height,
        interpolation: img.Interpolation.linear
      );
      
      Logger.flow(_logTag, '_applyImageDeformation', '📏 结果图像尺寸: ${resultImage.width}x${resultImage.height}');
      
      // 创建源点和目标点列表
      final List<Offset> srcPoints = [];
      final List<Offset> dstPoints = [];
      
      // 添加所有特征点
      for (int i = 0; i < landmarks.length; i++) {
        final Offset srcPoint = landmarks[i];
        
        // 获取变形偏移量，如果没有则使用零偏移
        final Offset deformation = deformations[i] ?? Offset.zero;
        
        // 计算目标点
        final Offset dstPoint = srcPoint + deformation;
        
        // 添加到列表
        srcPoints.add(srcPoint);
        dstPoints.add(dstPoint);
      }
      
      // 创建Delaunay三角剖分
      final List<List<int>> triangles = _createDelaunayTriangulation(srcPoints);
      Logger.flow(_logTag, '_applyImageDeformation', '📐 创建了 ${triangles.length} 个三角形');
      
      // 对每个三角形应用变形
      for (final triangle in triangles) {
        // 获取三角形的三个顶点
        final Offset srcPt1 = srcPoints[triangle[0]];
        final Offset srcPt2 = srcPoints[triangle[1]];
        final Offset srcPt3 = srcPoints[triangle[2]];
        
        final Offset dstPt1 = dstPoints[triangle[0]];
        final Offset dstPt2 = dstPoints[triangle[1]];
        final Offset dstPt3 = dstPoints[triangle[2]];
        
        // 变形三角形
        _warpTriangle(
          originalImage,
          resultImage,
          srcPt1,
          srcPt2,
          srcPt3,
          dstPt1,
          dstPt2,
          dstPt3,
        );
      }
      
      // 确保结果图像尺寸与原始图像尺寸一致
      if (resultImage.width != originalImage.width || resultImage.height != originalImage.height) {
        Logger.flowError(_logTag, '_applyImageDeformation', '⚠️ 结果图像尺寸与原始图像不一致，进行调整');
        final img.Image resizedImage = img.copyResize(
          resultImage,
          width: originalImage.width,
          height: originalImage.height,
          interpolation: img.Interpolation.linear
        );
        Logger.flow(_logTag, '_applyImageDeformation', '📏 调整后图像尺寸: ${resizedImage.width}x${resizedImage.height}');
        Logger.flowEnd(_logTag, '_applyImageDeformation');
        return resizedImage;
      }
      
      Logger.flow(_logTag, '_applyImageDeformation', '✅ 图像变形完成，尺寸保持不变');
      Logger.flowEnd(_logTag, '_applyImageDeformation');
      return resultImage;
    } catch (e) {
      Logger.flowError(_logTag, '_applyImageDeformation', '❌ 应用图像变形时出错: $e');
      Logger.flowEnd(_logTag, '_applyImageDeformation');
      return null;
    }
  }
  
  /// 创建Delaunay三角剖分
  List<List<int>> _createDelaunayTriangulation(List<Offset> points) {
    // 改进的实现，使用预定义的面部三角形
    // 这些三角形是基于面部解剖学的知识预定义的
    final List<List<int>> triangles = [];
    
    Logger.flow(_logTag, '_createDelaunayTriangulation', '📐 创建面部三角剖分，点数: ${points.length}');
    
    // 如果点数少于 20，使用简化的三角剖分
    if (points.length < 20) {
      // 简化的三角剖分，确保至少有一些三角形
      for (int i = 0; i < points.length - 2; i++) {
        triangles.add([i, i + 1, i + 2]);
      }
      
      // 添加一些交叉三角形
      for (int i = 0; i < points.length - 3; i += 2) {
        triangles.add([i, i + 2, i + 3]);
      }
      
      return triangles;
    }
    
    // 如果有足够的点，使用更复杂的面部三角剖分
    
    // 面部区域划分（假设前 20 个点是主要特征点）
    int eyeRegionStart = 0;
    int noseRegionStart = points.length > 30 ? 20 : 8;
    int mouthRegionStart = points.length > 40 ? 30 : 12;
    int contourRegionStart = points.length > 50 ? 40 : 16;
    
    // 面部区域三角剖分
    
    // 1. 眼睛区域
    for (int i = eyeRegionStart; i < noseRegionStart - 2; i++) {
      triangles.add([i, i + 1, i + 2]);
      if (i < noseRegionStart - 3) {
        triangles.add([i, i + 2, i + 3]);
      }
    }
    
    // 2. 鼻子区域
    for (int i = noseRegionStart; i < mouthRegionStart - 2; i++) {
      triangles.add([i, i + 1, i + 2]);
    }
    
    // 3. 嘴巴区域
    for (int i = mouthRegionStart; i < contourRegionStart - 2; i++) {
      triangles.add([i, i + 1, i + 2]);
    }
    
    // 4. 面部轮廓
    for (int i = contourRegionStart; i < points.length - 2; i++) {
      triangles.add([i, i + 1, i + 2]);
      // 添加一些连接到面部中心的三角形
      if (i % 3 == 0 && i < points.length - 10) {
        // 选择鼻尖或嘴巴中心作为连接点
        int centerPoint = noseRegionStart;  // 鼻尖
        triangles.add([i, i + 2, centerPoint]);
      }
    }
    
    // 5. 连接不同区域
    // 连接眼睛和鼻子
    triangles.add([eyeRegionStart + 1, noseRegionStart, noseRegionStart + 1]);
    triangles.add([eyeRegionStart + 4, noseRegionStart, noseRegionStart + 2]);
    
    // 连接鼻子和嘴巴
    triangles.add([noseRegionStart, noseRegionStart + 1, mouthRegionStart]);
    triangles.add([noseRegionStart, noseRegionStart + 2, mouthRegionStart + 2]);
    
    // 连接嘴巴和面部轮廓
    triangles.add([mouthRegionStart, mouthRegionStart + 3, contourRegionStart]);
    triangles.add([mouthRegionStart + 2, mouthRegionStart + 3, contourRegionStart + 5]);
    
    // 6. 添加图像边界三角形
    // 如果有足够的点，添加边界点与面部特征点的连接
    if (points.length > 60) {
      // 添加一些连接到边界的三角形
      int boundaryStart = 60;
      for (int i = boundaryStart; i < points.length - 1; i++) {
        // 连接到面部中心特征点
        triangles.add([i, i + 1, noseRegionStart]);
        
        // 连接到面部轮廓点
        if (i % 2 == 0 && contourRegionStart + (i - boundaryStart) / 2 < points.length) {
          triangles.add([i, i + 1, contourRegionStart + (i - boundaryStart) ~/ 2]);
        }
      }
    }
    
    Logger.flow(_logTag, '_createDelaunayTriangulation', '📐 创建了 ${triangles.length} 个三角形');
    return triangles;
  }
  
  /// 变形单个三角形
  void _warpTriangle(
    img.Image source,
    img.Image target,
    Offset srcPt1,
    Offset srcPt2,
    Offset srcPt3,
    Offset dstPt1,
    Offset dstPt2,
    Offset dstPt3,
  ) {
    // 计算目标三角形的包围盒
    final double minX = math.min(math.min(dstPt1.dx, dstPt2.dx), dstPt3.dx);
    final double minY = math.min(math.min(dstPt1.dy, dstPt2.dy), dstPt3.dy);
    final double maxX = math.max(math.max(dstPt1.dx, dstPt2.dx), dstPt3.dx);
    final double maxY = math.max(math.max(dstPt1.dy, dstPt2.dy), dstPt3.dy);
    
    // 确保边界在图像范围内
    final int startX = math.max(0, minX.floor());
    final int startY = math.max(0, minY.floor());
    final int endX = math.min(target.width - 1, maxX.ceil());
    final int endY = math.min(target.height - 1, maxY.ceil());
    
    // 计算仿射变换矩阵
    final Matrix4 mat = _computeAffineTransform(
      dstPt1, dstPt2, dstPt3,
      srcPt1, srcPt2, srcPt3,
    );
    
    // 对包围盒内的每个像素进行处理
    for (int y = startY; y <= endY; y++) {
      for (int x = startX; x <= endX; x++) {
        final Offset point = Offset(x.toDouble(), y.toDouble());
        
        // 检查点是否在三角形内
        if (_isPointInTriangle(point, dstPt1, dstPt2, dstPt3)) {
          // 计算源图像中的对应点
          final Offset srcPoint = _transformPoint(point, mat);
          
          // 如果源点在图像范围内，进行颜色插值
          if (srcPoint.dx >= 0 && srcPoint.dx < source.width - 1 &&
              srcPoint.dy >= 0 && srcPoint.dy < source.height - 1) {
            // 双线性插值获取颜色
            final pixel = _bilinearInterpolation(source, srcPoint);
            target.setPixelRgba(x, y, pixel.r, pixel.g, pixel.b, pixel.a);
          }
        }
      }
    }
  }
  
  /// 计算仿射变换矩阵
  Matrix4 _computeAffineTransform(
    Offset dst1, Offset dst2, Offset dst3,
    Offset src1, Offset src2, Offset src3,
  ) {
    // 创建方程组
    final Matrix4 A = Matrix4.zero();
    final Matrix4 B = Matrix4.zero();
    
    // 设置方程组系数
    A.setRow(0, Vector4(src1.dx, src1.dy, 1, 0));
    A.setRow(1, Vector4(src2.dx, src2.dy, 1, 0));
    A.setRow(2, Vector4(src3.dx, src3.dy, 1, 0));
    
    B.setRow(0, Vector4(dst1.dx, dst1.dy, 0, 0));
    B.setRow(1, Vector4(dst2.dx, dst2.dy, 0, 0));
    B.setRow(2, Vector4(dst3.dx, dst3.dy, 0, 0));
    
    // 求解方程组得到变换矩阵
    // 简化实现，实际应使用完整的矩阵求解
    final Matrix4 transform = Matrix4.identity();
    transform.setRow(0, Vector4(
      (dst1.dx - dst3.dx) / (src1.dx - src3.dx),
      0,
      0,
      (dst3.dx * src1.dx - dst1.dx * src3.dx) / (src1.dx - src3.dx),
    ));
    transform.setRow(1, Vector4(
      0,
      (dst2.dy - dst3.dy) / (src2.dy - src3.dy),
      0,
      (dst3.dy * src2.dy - dst2.dy * src3.dy) / (src2.dy - src3.dy),
    ));
    
    return transform;
  }
  
  /// 变换点坐标
  Offset _transformPoint(Offset point, Matrix4 transform) {
    final result = Vector3(point.dx, point.dy, 1);
    transform.transform3(result);
    return Offset(result.x, result.y);
  }
  
  /// 判断点是否在三角形内
  bool _isPointInTriangle(Offset p, Offset a, Offset b, Offset c) {
    final double area = 0.5 * ((-b.dy * c.dx + a.dy * (-b.dx + c.dx) + 
                              a.dx * (b.dy - c.dy) + b.dx * c.dy));
    final double s = 1 / (2 * area) * (a.dy * c.dx - a.dx * c.dy + 
                                     (c.dy - a.dy) * p.dx + (a.dx - c.dx) * p.dy);
    final double t = 1 / (2 * area) * (a.dx * b.dy - a.dy * b.dx + 
                                     (a.dy - b.dy) * p.dx + (b.dx - a.dx) * p.dy);
    
    return s >= 0 && t >= 0 && (s + t) <= 1;
  }
  
  /// 双线性插值获取颜色
  img.ColorRgba8 _bilinearInterpolation(img.Image image, Offset point) {
    final int x1 = point.dx.floor();
    final int y1 = point.dy.floor();
    final int x2 = math.min(x1 + 1, image.width - 1);
    final int y2 = math.min(y1 + 1, image.height - 1);
    
    final double wx = point.dx - x1;
    final double wy = point.dy - y1;
    
    final c1 = image.getPixel(x1, y1);
    final c2 = image.getPixel(x2, y1);
    final c3 = image.getPixel(x1, y2);
    final c4 = image.getPixel(x2, y2);
    
    // 分别对RGB通道进行插值
    final int r = _interpolateChannel(
      c1.r.toInt(), c2.r.toInt(), c3.r.toInt(), c4.r.toInt(),
      wx, wy,
    );
    final int g = _interpolateChannel(
      c1.g.toInt(), c2.g.toInt(), c3.g.toInt(), c4.g.toInt(),
      wx, wy,
    );
    final int b = _interpolateChannel(
      c1.b.toInt(), c2.b.toInt(), c3.b.toInt(), c4.b.toInt(),
      wx, wy,
    );
    final int a = _interpolateChannel(
      c1.a.toInt(), c2.a.toInt(), c3.a.toInt(), c4.a.toInt(),
      wx, wy,
    );
    
    return img.ColorRgba8(r, g, b, a);
  }
  
  /// 插值单个颜色通道
  int _interpolateChannel(int c1, int c2, int c3, int c4, double wx, double wy) {
    final double topInterp = c1 * (1 - wx) + c2 * wx;
    final double bottomInterp = c3 * (1 - wx) + c4 * wx;
    return (topInterp * (1 - wy) + bottomInterp * wy).round();
  }
  
  /// 保存图像
  Future<String> _saveImage(img.Image image, String originalPath) async {
    Logger.flowStart(_logTag, '_saveImage');
    Logger.flow(_logTag, '_saveImage', '📊 开始保存图像 | 尺寸: ${image.width}x${image.height}');
    
    try {
      // 获取临时目录
      final Directory tempDir = await getTemporaryDirectory();
      
      // 创建输出文件名
      final String fileName = path.basename(originalPath);
      final String outputPath = path.join(tempDir.path, 'deformed_$fileName');
      
      // 编码为PNG并保存
      final File outputFile = File(outputPath);
      await outputFile.writeAsBytes(img.encodePng(image));
      
      Logger.flow(_logTag, '_saveImage', '✅ 图像保存成功: $outputPath');
      Logger.flowEnd(_logTag, '_saveImage');
      return outputPath;
    } catch (e) {
      Logger.flowError(_logTag, '_saveImage', '❌ 保存图像时出错: $e');
      Logger.flowEnd(_logTag, '_saveImage');
      throw Exception('保存图像时出错: $e');
    }
  }
  
  /// 释放资源
  void dispose() {
    try {
      if (!Platform.isMacOS) {
        _faceDetector.close();
      }
    } catch (e) {
      Logger.flowError(_logTag, 'dispose', '❌ 关闭面部检测器时出错: $e');
    }
  }
  
  /// 应用变形（使用ui.Image和特征点）
  /// 
  /// [originalImage] 原始图像
  /// [landmarks] 特征点列表
  /// [deformations] 变形数据，键为特征点索引，值为偏移量
  /// [targetWidth] 目标宽度（默认与原始图像相同）
  /// [targetHeight] 目标高度（默认与原始图像相同）
  /// 
  /// 返回变形结果，包含变形后的图像和校准后的特征点
  Future<DeformationImageResult?> applyDeformationWithImage({
    required ui.Image originalImage,
    required List<Offset> landmarks,
    required Map<int, Offset> deformations,
    int? targetWidth,
    int? targetHeight,
  }) async {
    Logger.flowStart(_logTag, 'applyDeformationWithImage');
    Logger.flow(_logTag, 'applyDeformationWithImage', '📊 开始应用变形 | 原始图像大小: ${originalImage.width}x${originalImage.height} | 特征点数: ${landmarks.length} | 变形点数: ${deformations.length}');
    
    // 输出原始图像详细信息到控制台
    print('======== 变形前原始图片信息 ========');
    print('📷 原始图像详细信息:');
    print('  - 哈希码: ${originalImage.hashCode}');
    print('  - 宽度: ${originalImage.width}');
    print('  - 高度: ${originalImage.height}');
    print('  - 内存地址: ${originalImage.toString()}');
    print('  - 估计内存占用: ${(originalImage.width * originalImage.height * 4) / 1024} KB');
    print('  - 记录时间: ${DateTime.now().toIso8601String()}');
    print('============================');
    
    try {
      // 使用原始图像尺寸作为目标尺寸
      final int finalTargetWidth = originalImage.width;
      final int finalTargetHeight = originalImage.height;
      
      Logger.flow(_logTag, 'applyDeformationWithImage', '📏 目标尺寸设置为原始图像尺寸: ${finalTargetWidth}x${finalTargetHeight}');
      
      // 直接使用原始图像，不进行尺寸调整
      // 将ui.Image转换为img.Image
      final img.Image? imgImage = await _convertUiImageToImgImage(originalImage);
      if (imgImage == null) {
        Logger.flowError(_logTag, 'applyDeformationWithImage', '❌ 无法转换图像格式');
        Logger.flowEnd(_logTag, 'applyDeformationWithImage');
        return null;
      }
        
        Logger.flow(_logTag, 'applyDeformationWithImage', '📏 转换后图像大小: ${imgImage.width}x${imgImage.height}');
        
        // 应用图像变形，使用原始图像
        final img.Image? deformedImage = await _applyImageDeformation(
          imgImage,
          landmarks,
          deformations,
          finalTargetWidth,
          finalTargetHeight,
        );
        
        if (deformedImage == null) {
          Logger.flowError(_logTag, 'applyDeformationWithImage', '❌ 图像变形失败');
          Logger.flowEnd(_logTag, 'applyDeformationWithImage');
          return null;
        }
        
        Logger.flow(_logTag, 'applyDeformationWithImage', '📏 变形后图像大小: ${deformedImage.width}x${deformedImage.height}');
        
        // 确保变形后的图像尺寸与原始图像尺寸相同
        img.Image finalDeformedImage = deformedImage;
        if (deformedImage.width != finalTargetWidth || deformedImage.height != finalTargetHeight) {
          Logger.flow(_logTag, 'applyDeformationWithImage', '⚠️ 变形后图像尺寸不符合原始尺寸，进行调整: ${deformedImage.width}x${deformedImage.height} -> ${finalTargetWidth}x${finalTargetHeight}');
          finalDeformedImage = img.copyResize(
            deformedImage,
            width: finalTargetWidth,
            height: finalTargetHeight,
            interpolation: img.Interpolation.linear
          );
          Logger.flow(_logTag, 'applyDeformationWithImage', '📏 调整后图像大小: ${finalDeformedImage.width}x${finalDeformedImage.height}');
        }
        
        // 将img.Image转换回ui.Image
        final ui.Image? uiDeformedImage = await _convertImgImageToUiImage(finalDeformedImage);
        if (uiDeformedImage == null) {
          Logger.flowError(_logTag, 'applyDeformationWithImage', '❌ 无法转换回UI图像格式');
          Logger.flowEnd(_logTag, 'applyDeformationWithImage');
          return null;
        }
        
        Logger.flow(_logTag, 'applyDeformationWithImage', '📏 最终UI图像大小: ${uiDeformedImage.width}x${uiDeformedImage.height}');
        
        // 输出变形后图像详细信息到控制台
        print('======== 变形后图片信息 ========');
        print('📷 变形后图像详细信息:');
        print('  - 哈希码: ${uiDeformedImage.hashCode}');
        print('  - 宽度: ${uiDeformedImage.width}');
        print('  - 高度: ${uiDeformedImage.height}');
        print('  - 内存地址: ${uiDeformedImage.toString()}');
        print('  - 估计内存占用: ${(uiDeformedImage.width * uiDeformedImage.height * 4) / 1024} KB');
        print('  - 记录时间: ${DateTime.now().toIso8601String()}');
        print('  - 期望尺寸: ${finalTargetWidth}x${finalTargetHeight}');
        print('  - 尺寸匹配: ${uiDeformedImage.width == finalTargetWidth && uiDeformedImage.height == finalTargetHeight ? "是" : "否"}');
        print('============================');
        
        // 校准特征点
        final List<Offset> calibratedPoints = _calibrateFeaturePoints(
          originalImage: imgImage,
          deformedImage: finalDeformedImage,
          landmarks: landmarks,
          deformations: deformations,
        );
        
        Logger.flow(_logTag, 'applyDeformationWithImage', '✅ 变形应用成功 | 输出图像大小: ${uiDeformedImage.width}x${uiDeformedImage.height} | 校准特征点数: ${calibratedPoints.length}');
        Logger.flowEnd(_logTag, 'applyDeformationWithImage');
        
        return DeformationImageResult(
          image: uiDeformedImage,
          calibratedPoints: calibratedPoints,
        );
    } catch (e) {
      Logger.flowError(_logTag, 'applyDeformationWithImage', '❌ 应用变形时出错: $e');
      Logger.flowEnd(_logTag, 'applyDeformationWithImage');
      return null;
    }
  }
  
  /// 强制调整图像尺寸
  Future<ui.Image?> _forceResizeImage(ui.Image image, int targetWidth, int targetHeight) async {
    Logger.flowStart(_logTag, '_forceResizeImage');
    Logger.flow(_logTag, '_forceResizeImage', '📏 原始尺寸: ${image.width}x${image.height}, 目标尺寸: ${targetWidth}x${targetHeight}');
    
    try {
      // 将ui.Image转换为字节数据
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        Logger.flowError(_logTag, '_forceResizeImage', '❌ 无法获取图像字节数据');
        Logger.flowEnd(_logTag, '_forceResizeImage');
        return null;
      }
      
      // 转换为Uint8List
      final Uint8List uint8List = byteData.buffer.asUint8List();
      
      // 解码为img.Image
      final img.Image? imgImage = img.decodeImage(uint8List);
      if (imgImage == null) {
        Logger.flowError(_logTag, '_forceResizeImage', '❌ 无法解码图像数据');
        Logger.flowEnd(_logTag, '_forceResizeImage');
        return null;
      }
      
      // 调整尺寸
      final img.Image resizedImage = img.copyResize(
        imgImage,
        width: targetWidth,
        height: targetHeight,
        interpolation: img.Interpolation.linear
      );
      
      // 编码为PNG
      final Uint8List resizedBytes = Uint8List.fromList(img.encodePng(resizedImage));
      
      // 解码为ui.Image，明确指定目标尺寸
      final ui.Codec codec = await ui.instantiateImageCodec(
        resizedBytes,
        targetWidth: targetWidth,
        targetHeight: targetHeight
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image resizedUiImage = frameInfo.image;
      
      Logger.flow(_logTag, '_forceResizeImage', '✅ 图像尺寸调整成功: ${resizedUiImage.width}x${resizedUiImage.height}');
      Logger.flowEnd(_logTag, '_forceResizeImage');
      
      return resizedUiImage;
    } catch (e) {
      Logger.flowError(_logTag, '_forceResizeImage', '❌ 调整图像尺寸时出错: $e');
      Logger.flowEnd(_logTag, '_forceResizeImage');
      return null;
    }
  }
  
  /// 将ui.Image转换为img.Image
  Future<img.Image?> _convertUiImageToImgImage(ui.Image uiImage) async {
    Logger.flowStart(_logTag, '_convertUiImageToImgImage');
    Logger.flow(_logTag, '_convertUiImageToImgImage', '📊 开始转换UI图像 | 原始尺寸: ${uiImage.width}x${uiImage.height}');
    
    try {
      // 获取图像字节数据
      final ByteData? byteData = await uiImage.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) {
        Logger.flowError(_logTag, '_convertUiImageToImgImage', '❌ 无法获取图像字节数据');
        Logger.flowEnd(_logTag, '_convertUiImageToImgImage');
        return null;
      }
      
      // 转换为Uint8List
      final Uint8List uint8List = byteData.buffer.asUint8List();
      
      // 解码为img.Image
      final img.Image? imgImage = img.decodeImage(uint8List);
      if (imgImage == null) {
        Logger.flowError(_logTag, '_convertUiImageToImgImage', '❌ 无法解码图像数据');
        Logger.flowEnd(_logTag, '_convertUiImageToImgImage');
        return null;
      }
      
      // 检查图像尺寸是否符合预期
      if (imgImage.width != 1024 || imgImage.height != 1478) {
        Logger.flowWarning(_logTag, '_convertUiImageToImgImage', '⚠️ 输出图像尺寸不符合预期: ${imgImage.width}x${imgImage.height}，调整为1024x1478');
        
        // 调整图像尺寸
        final img.Image resizedImage = img.copyResize(
          imgImage,
          width: 1024,
          height: 1478,
          interpolation: img.Interpolation.linear
        );
        
        Logger.flow(_logTag, '_convertUiImageToImgImage', '📏 图像尺寸已调整: ${resizedImage.width}x${resizedImage.height}');
        Logger.flow(_logTag, '_convertUiImageToImgImage', '✅ 转换成功');
        Logger.flowEnd(_logTag, '_convertUiImageToImgImage');
        return resizedImage;
      }

      Logger.flow(_logTag, '_convertUiImageToImgImage', '✅ 转换成功 | 输出图像大小: ${imgImage.width}x${imgImage.height}');
      Logger.flowEnd(_logTag, '_convertUiImageToImgImage');
      return imgImage;
    } catch (e) {
      Logger.flowError(_logTag, '_convertUiImageToImgImage', '❌ 转换图像时出错: $e');
      Logger.flowEnd(_logTag, '_convertUiImageToImgImage');
      return null;
    }
  }
  
  /// 将img.Image转换为ui.Image
  Future<ui.Image?> _convertImgImageToUiImage(img.Image imgImage) async {
    Logger.flowStart(_logTag, '_convertImgImageToUiImage');
    Logger.flow(_logTag, '_convertImgImageToUiImage', '📊 开始转换图像 | 输入图像大小: ${imgImage.width}x${imgImage.height}');
    
    try {
      // 检查图像尺寸是否符合预期
      if (imgImage.width != 1024 || imgImage.height != 1478) {
        Logger.flowWarning(_logTag, '_convertImgImageToUiImage', '⚠️ 输入图像尺寸不符合预期: ${imgImage.width}x${imgImage.height}，调整为1024x1478');
        
        // 调整图像尺寸
        imgImage = img.copyResize(
          imgImage,
          width: 1024,
          height: 1478,
          interpolation: img.Interpolation.linear
        );
        
        Logger.flow(_logTag, '_convertImgImageToUiImage', '📏 图像尺寸已调整: ${imgImage.width}x${imgImage.height}');
      }
      
      // 编码为PNG格式
      final Uint8List uint8List = Uint8List.fromList(img.encodePng(imgImage));

      // 解码为ui.Image，明确指定目标尺寸
      final ui.Codec codec = await ui.instantiateImageCodec(
        uint8List,
        targetWidth: 1024,
        targetHeight: 1478
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image uiImage = frameInfo.image;

      Logger.flow(_logTag, '_convertImgImageToUiImage', '✅ 转换成功 | 输出图像大小: ${uiImage.width}x${uiImage.height}');
      Logger.flowEnd(_logTag, '_convertImgImageToUiImage');
      return uiImage;
    } catch (e) {
      Logger.flowError(_logTag, '_convertImgImageToUiImage', '❌ 转换图像时出错: $e');
      Logger.flowEnd(_logTag, '_convertImgImageToUiImage');
      return null;
    }
  }
  
  /// 校准特征点
  /// 
  /// 根据变形数据校准特征点位置
  /// 
  /// [originalImage] 原始图像
  /// [deformedImage] 变形后的图像
  /// [landmarks] 原始特征点列表
  /// [deformations] 变形数据，键为特征点索引，值为偏移量
  List<Offset> _calibrateFeaturePoints({
    required img.Image originalImage,
    required img.Image deformedImage,
    required List<Offset> landmarks,
    required Map<int, Offset> deformations,
  }) {
    Logger.flowStart(_logTag, '_calibrateFeaturePoints');
    Logger.flow(_logTag, '_calibrateFeaturePoints', '📊 开始校准特征点 | 特征点数: ${landmarks.length} | 变形点数: ${deformations.length}');
    
    // 输出图像尺寸信息
    Logger.flow(_logTag, '_calibrateFeaturePoints', '📏 原始图像尺寸: ${originalImage.width}x${originalImage.height}');
    Logger.flow(_logTag, '_calibrateFeaturePoints', '📏 变形图像尺寸: ${deformedImage.width}x${deformedImage.height}');
    
    // 检查图像尺寸是否符合预期
    final bool sizesMatch = originalImage.width == 1024 && originalImage.height == 1478 && 
                            deformedImage.width == 1024 && deformedImage.height == 1478;
    
    Logger.flow(_logTag, '_calibrateFeaturePoints', '📏 图像尺寸符合预期: ${sizesMatch ? "是" : "否"}');
    
    // 创建校准后的特征点列表
    final List<Offset> calibratedPoints = List.from(landmarks);
    
    // 输出前3个原始特征点的坐标
    if (landmarks.length >= 3) {
      Logger.flow(_logTag, '_calibrateFeaturePoints', '📍 前3个原始特征点坐标:');
      for (int i = 0; i < 3; i++) {
        Logger.flow(_logTag, '_calibrateFeaturePoints', '   点[$i]: (${landmarks[i].dx.toStringAsFixed(1)}, ${landmarks[i].dy.toStringAsFixed(1)})');
      }
    }
    
    // 应用变形偏移量
    for (int i = 0; i < calibratedPoints.length; i++) {
      if (deformations.containsKey(i)) {
        final Offset originalPoint = calibratedPoints[i];
        final Offset deformation = deformations[i]!;
        calibratedPoints[i] = originalPoint + deformation;
        
        // 记录重要特征点的变形情况
        if (i < 10 || i % 50 == 0) {
          Logger.flow(_logTag, '_calibrateFeaturePoints', '   点[$i]变形: (${originalPoint.dx.toStringAsFixed(1)}, ${originalPoint.dy.toStringAsFixed(1)}) -> (${calibratedPoints[i].dx.toStringAsFixed(1)}, ${calibratedPoints[i].dy.toStringAsFixed(1)})');
        }
      }
    }
    
    // 确保特征点在图像范围内
    int adjustedPointCount = 0;
    for (int i = 0; i < calibratedPoints.length; i++) {
      final Offset point = calibratedPoints[i];
      bool needsAdjustment = false;
      
      // 限制x坐标在图像宽度范围内
      double x = point.dx;
      if (x < 0) {
        x = 0;
        needsAdjustment = true;
      }
      if (x >= deformedImage.width) {
        x = deformedImage.width - 1;
        needsAdjustment = true;
      }
      
      // 限制y坐标在图像高度范围内
      double y = point.dy;
      if (y < 0) {
        y = 0;
        needsAdjustment = true;
      }
      if (y >= deformedImage.height) {
        y = deformedImage.height - 1;
        needsAdjustment = true;
      }
      
      // 更新校准后的特征点
      if (needsAdjustment) {
        final Offset originalPoint = calibratedPoints[i];
        calibratedPoints[i] = Offset(x, y);
        adjustedPointCount++;
        
        // 记录调整后的特征点
        if (i < 10 || i % 50 == 0) {
          Logger.flow(_logTag, '_calibrateFeaturePoints', '   点[$i]调整: (${originalPoint.dx.toStringAsFixed(1)}, ${originalPoint.dy.toStringAsFixed(1)}) -> (${x.toStringAsFixed(1)}, ${y.toStringAsFixed(1)})');
        }
      }
    }
    
    // 输出前3个校准后特征点的坐标
    if (calibratedPoints.length >= 3) {
      Logger.flow(_logTag, '_calibrateFeaturePoints', '📍 前3个校准后特征点坐标:');
      for (int i = 0; i < 3; i++) {
        Logger.flow(_logTag, '_calibrateFeaturePoints', '   点[$i]: (${calibratedPoints[i].dx.toStringAsFixed(1)}, ${calibratedPoints[i].dy.toStringAsFixed(1)})');
      }
    }
    
    Logger.flow(_logTag, '_calibrateFeaturePoints', '✅ 特征点校准完成 | 校准后特征点数: ${calibratedPoints.length} | 调整边界的点数: $adjustedPointCount');
    Logger.flowEnd(_logTag, '_calibrateFeaturePoints');
    return calibratedPoints;
  }
}
