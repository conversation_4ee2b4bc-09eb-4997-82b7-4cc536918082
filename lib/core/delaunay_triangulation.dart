import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart';

/// Delaunay三角剖分工具
class DelaunayTriangulation {
  /// 计算点到线段的距离
  static double _pointToLineDistance(Offset p, Offset a, Offset b) {
    final double normalLength = math.sqrt(math.pow(b.dx - a.dx, 2) + math.pow(b.dy - a.dy, 2));
    return ((p.dx - a.dx) * (b.dy - a.dy) - (p.dy - a.dy) * (b.dx - a.dx)).abs() / normalLength;
  }
  
  /// 计算三角形的外接圆
  static Circle _computeCircumcircle(Offset a, Offset b, Offset c) {
    final double d = 2 * (a.dx * (b.dy - c.dy) + b.dx * (c.dy - a.dy) + c.dx * (a.dy - b.dy));
    
    if (d.abs() < 1e-10) {
      // 三点共线，无法形成三角形
      return Circle(Offset.zero, 0);
    }
    
    final double aSq = a.dx * a.dx + a.dy * a.dy;
    final double bSq = b.dx * b.dx + b.dy * b.dy;
    final double cSq = c.dx * c.dx + c.dy * c.dy;
    
    final double x = (aSq * (b.dy - c.dy) + bSq * (c.dy - a.dy) + cSq * (a.dy - b.dy)) / d;
    final double y = (aSq * (c.dx - b.dx) + bSq * (a.dx - c.dx) + cSq * (b.dx - a.dx)) / d;
    
    final Offset center = Offset(x, y);
    final double radius = (center - a).distance;
    
    return Circle(center, radius);
  }
  
  /// 判断点是否在三角形内
  static bool _isPointInTriangle(Offset p, Offset a, Offset b, Offset c) {
    final double area = 0.5 * ((-b.dy * c.dx + a.dy * (-b.dx + c.dx) + 
                              a.dx * (b.dy - c.dy) + b.dx * c.dy));
    final double s = 1 / (2 * area) * (a.dy * c.dx - a.dx * c.dy + 
                                     (c.dy - a.dy) * p.dx + (a.dx - c.dx) * p.dy);
    final double t = 1 / (2 * area) * (a.dx * b.dy - a.dy * b.dx + 
                                     (a.dy - b.dy) * p.dx + (b.dx - a.dx) * p.dy);
    
    return s >= 0 && t >= 0 && (s + t) <= 1;
  }
  
  /// 判断点是否在圆内
  static bool _isPointInCircle(Offset p, Circle circle) {
    return (p - circle.center).distance <= circle.radius;
  }
  
  /// 生成Delaunay三角剖分
  static List<List<int>> triangulate(List<Offset> points) {
    if (points.length < 3) {
      return [];
    }
    
    // 找到包含所有点的超级三角形
    final Offset superTriA = Offset(-1000, -1000);
    final Offset superTriB = Offset(2000, -1000);
    final Offset superTriC = Offset(500, 2000);
    
    // 初始三角形列表，从超级三角形开始
    final List<Triangle> triangles = [
      Triangle(-1, -2, -3, superTriA, superTriB, superTriC),
    ];
    
    // 逐个添加点
    for (int i = 0; i < points.length; i++) {
      final Offset point = points[i];
      final List<Edge> edges = [];
      
      // 遍历现有三角形
      final List<Triangle> trianglesToRemove = [];
      
      for (final Triangle triangle in triangles) {
        // 计算外接圆
        final Circle circumcircle = _computeCircumcircle(
          triangle.a, triangle.b, triangle.c);
        
        // 如果点在外接圆内，标记三角形待删除，并保存边
        if (_isPointInCircle(point, circumcircle)) {
          trianglesToRemove.add(triangle);
          
          // 保存三角形的边
          edges.add(Edge(triangle.aIndex, triangle.bIndex, triangle.a, triangle.b));
          edges.add(Edge(triangle.bIndex, triangle.cIndex, triangle.b, triangle.c));
          edges.add(Edge(triangle.cIndex, triangle.aIndex, triangle.c, triangle.a));
        }
      }
      
      // 从三角形列表中移除被标记的三角形
      triangles.removeWhere((triangle) => trianglesToRemove.contains(triangle));
      
      // 找出不重复的边，这些边将形成新的三角形
      final List<Edge> uniqueEdges = [];
      for (final Edge edge in edges) {
        bool isDuplicate = false;
        for (int j = 0; j < edges.length; j++) {
          if (edge != edges[j] && edge.isEqual(edges[j])) {
            isDuplicate = true;
            break;
          }
        }
        
        if (!isDuplicate) {
          uniqueEdges.add(edge);
        }
      }
      
      // 使用不重复的边和新点创建新三角形
      for (final Edge edge in uniqueEdges) {
        triangles.add(Triangle(
          edge.aIndex, edge.bIndex, i,
          edge.a, edge.b, point,
        ));
      }
    }
    
    // 移除包含超级三角形顶点的三角形
    triangles.removeWhere((triangle) => 
      triangle.aIndex < 0 || triangle.bIndex < 0 || triangle.cIndex < 0);
    
    // 转换为索引列表
    return triangles.map((triangle) => 
      [triangle.aIndex, triangle.bIndex, triangle.cIndex]).toList();
  }
}

/// 圆类
class Circle {
  final Offset center;
  final double radius;
  
  Circle(this.center, this.radius);
}

/// 边类
class Edge {
  final int aIndex;
  final int bIndex;
  final Offset a;
  final Offset b;
  
  Edge(this.aIndex, this.bIndex, this.a, this.b);
  
  bool isEqual(Edge other) {
    return (aIndex == other.aIndex && bIndex == other.bIndex) ||
           (aIndex == other.bIndex && bIndex == other.aIndex);
  }
}

/// 三角形类
class Triangle {
  final int aIndex;
  final int bIndex;
  final int cIndex;
  final Offset a;
  final Offset b;
  final Offset c;
  
  Triangle(this.aIndex, this.bIndex, this.cIndex, this.a, this.b, this.c);
}
