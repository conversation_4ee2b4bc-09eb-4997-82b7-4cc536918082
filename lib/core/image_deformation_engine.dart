import 'dart:io';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;

// 导入Logger类
import '../utils/logger.dart';

/// 变形结果
class DeformationResult {
  final bool success;
  final String outputPath;
  final String errorMessage;
  final String transformedImagePath;
  
  DeformationResult({
    required this.success,
    this.outputPath = '',
    this.errorMessage = '',
    String? transformedImagePath,
  }) : transformedImagePath = transformedImagePath ?? outputPath;
}

/// Python脚本执行结果
class PythonResult {
  final bool success;
  final String outputPath;
  final String errorMessage;
  
  PythonResult({
    required this.success,
    this.outputPath = '',
    this.errorMessage = '',
  });
}

/// 图像变形引擎
/// 
/// 负责将特征点变形应用到图像上
class ImageDeformationEngine {
  /// 应用变形
  /// 
  /// [imagePath] 原始图像路径
  /// [deformations] 变形数据，键为特征点索引，值为偏移量
  /// [landmarks] 特征点数据
  /// 
  /// 返回变形结果
  Future<DeformationResult> applyDeformation({
    required String imagePath,
    required Map<int, Offset> deformations,
    required List<Map<String, dynamic>> landmarks,
  }) async {
    print('开始应用变形 | 图像: ${path.basename(imagePath)} | 变形点数: ${deformations.length} | 特征点数: ${landmarks.length}');
    print('完整图像路径: $imagePath');
    print('变形数据详情: ${deformations.entries.take(5).map((e) => '点${e.key}: (${e.value.dx.toStringAsFixed(2)}, ${e.value.dy.toStringAsFixed(2)})').join(', ')}...');
    
    try {
      // 检查输入图像是否存在
      final inputFile = File(imagePath);
      if (!await inputFile.exists()) {
        print('错误: 输入图像不存在: $imagePath');
        return DeformationResult(
          success: false,
          errorMessage: '输入图像不存在: $imagePath',
        );
      } else {
        print('✅ 输入图像存在，大小: ${await inputFile.length()} 字节');
      }
      
      // 检查变形数据是否有效
      if (deformations.isEmpty) {
        print('警告: 无变形数据，将返回原始图像');
      }
      
      // 使用临时目录保存变形后的图像和脚本，避免权限问题
      final tempDir = Directory.systemTemp.createTempSync('beautifun_transform_');
      print('创建临时输出目录: ${tempDir.path}');
      
      // 确保临时目录有写入和执行权限
      try {
        await Process.run('chmod', ['777', tempDir.path]);
        print('设置临时目录权限: ${tempDir.path}');
      } catch (e) {
        print('设置临时目录权限失败: $e');
      }
      
      // 创建临时脚本目录
      final scriptDir = Directory(path.join(tempDir.path, 'core'));
      if (!scriptDir.existsSync()) {
        scriptDir.createSync(recursive: true);
      }
      
      // 设置脚本目录权限
      try {
        await Process.run('chmod', ['777', scriptDir.path]);
        print('设置脚本目录权限: ${scriptDir.path}');
      } catch (e) {
        print('设置脚本目录权限失败: $e');
      }
      
      // 尝试多个可能的源脚本路径，按优先级排序
      final List<String> possibleSourceScriptPaths = [
        // 应用程序包内的资源路径（最高优先级）
        path.join(path.dirname(Platform.resolvedExecutable), '../Resources/flutter_assets/core/image_deformation.py'),
        path.join(path.dirname(Platform.resolvedExecutable), 'Resources/flutter_assets/core/image_deformation.py'),
        // 开发环境中的路径
        path.join(Directory.current.path, 'assets/core/image_deformation.py'),
        path.join(Directory.current.path, 'core/image_deformation.py'),
        // 用户指定的路径
        '/Users/<USER>/beautifun/core/image_deformation.py',
        // 沙盒容器内的路径
        '/Users/<USER>/Library/Containers/com.example.beautifun/Data/core/image_deformation.py',
        // 相对路径
        'core/image_deformation.py',
        'assets/core/image_deformation.py',
      ];
      
      // 打印所有可能的脚本路径用于调试
      print('可能的脚本路径:');
      for (final scriptPath in possibleSourceScriptPaths) {
        final exists = File(scriptPath).existsSync();
        print('  - $scriptPath (${exists ? "存在" : "不存在"})');
        if (exists) {
          print('    文件大小: ${File(scriptPath).lengthSync()} 字节');
          print('    最后修改时间: ${File(scriptPath).lastModifiedSync()}');
        }
      }
      
      String? sourceScriptPath;
      for (final scriptPath in possibleSourceScriptPaths) {
        if (File(scriptPath).existsSync()) {
          sourceScriptPath = scriptPath;
          print('找到源Python脚本: $sourceScriptPath');
          break;
        }
      }
      
      // 复制脚本到临时目录
      String? tempScriptPath;
      if (sourceScriptPath != null) {
        final targetScriptPath = path.join(scriptDir.path, 'image_deformation.py');
        try {
          // 读取源脚本内容
          final scriptContent = await File(sourceScriptPath).readAsString();
          
          // 写入到临时目录
          await File(targetScriptPath).writeAsString(scriptContent);
          print('已将脚本内容写入到临时目录: $targetScriptPath');
          
          // 确保脚本有执行权限
          await Process.run('chmod', ['+x', targetScriptPath]);
          print('已设置脚本执行权限');
          
          tempScriptPath = targetScriptPath;
        } catch (e) {
          print('复制脚本到临时目录失败: $e');
          // 尝试直接使用源脚本路径
          print('尝试直接使用源脚本路径: $sourceScriptPath');
          tempScriptPath = sourceScriptPath;
        }
      } else {
        print('错误: 找不到Python脚本文件');
        return DeformationResult(
          success: false,
          errorMessage: '找不到Python脚本文件',
        );
      }
      
      // 生成输出文件名
      final outputFileName = '${path.basenameWithoutExtension(imagePath)}_transformed${path.extension(imagePath)}';
      
      // 生成临时输出路径
      final tempOutputPath = path.join(tempDir.path, outputFileName);
      
      print('输出路径: $tempOutputPath');
      
      // 准备变形数据
      final deformationData = _prepareDeformationData(deformations, landmarks);
      
      // 调用Python脚本执行变形
      final result = await _runPythonDeformation(
        inputPath: imagePath,
        outputPath: tempOutputPath,
        deformationData: deformationData,
      );
      
      if (result.success) {
        print('变形应用成功 | 输出路径: ${result.outputPath}');
        
        return DeformationResult(
          success: true,
          outputPath: result.outputPath,
          transformedImagePath: result.outputPath,
        );
      } else {
        print('变形应用失败: ${result.errorMessage}');
        return DeformationResult(
          success: false,
          errorMessage: result.errorMessage,
        );
      }
    } catch (e) {
      print('变形应用异常: $e');
      return DeformationResult(
        success: false,
        errorMessage: e.toString(),
      );
    }
  }
  
  /// 准备变形数据
  Map<String, dynamic> _prepareDeformationData(Map<int, Offset> deformations, List<Map<String, dynamic>> landmarks) {
    print('准备变形数据 | 变形点数: ${deformations.length} | 特征点数: ${landmarks.length}');
    Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
      '🔍 [开始] 准备变形数据 | 变形点数: ${deformations.length} | 特征点数: ${landmarks.length}');
    
    // 创建变形数据结构
    Map<String, dynamic> deformationData = {
      'deformation_vectors': <String, dynamic>{},
      'landmarks': <dynamic>[],
    };

    // 记录最大变形量，用于调试
    double maxMagnitude = 0.0;
    int maxMagnitudePointId = -1;
    int validDeformationCount = 0;
    
    // 分析变形向量的方向统计
    int positiveXCount = 0; // 水平向右的变形数
    int negativeXCount = 0; // 水平向左的变形数
    int positiveYCount = 0; // 垂直向下的变形数
    int negativeYCount = 0; // 垂直向上的变形数
    
    // 判断是否有变形向量
    if (deformations.isEmpty) {
      Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
        '⚠️ [警告] 没有变形向量，返回空的变形数据');
      return deformationData;
    }

    // 判断是否有鼻翼宽度变形向量
    bool hasNostrilWidthDeformation = false;
    // 检查是否有纯水平方向的变形向量（可能是鼻翼宽度变形）
    for (var entry in deformations.entries) {
      if (entry.value.dy == 0 && entry.value.dx != 0) {
        hasNostrilWidthDeformation = true;
        break;
      }
    }

    // 处理每个变形点
    deformations.forEach((pointId, offset) {
      // 计算变形向量的大小
      double magnitude = sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
      
      // 如果是鼻翼宽度变形，则不过滤小变形向量
      bool isNostrilWidthPoint = offset.dy == 0 && offset.dx != 0;
      
      // 对于鼻翼宽度变形点，记录特殊日志
      if (isNostrilWidthPoint) {
        Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
          '🔍 [鼻翼宽度点] 检测到鼻翼宽度变形点 | ID: $pointId | ' +
          '偏移: (${offset.dx.toStringAsFixed(6)}, ${offset.dy.toStringAsFixed(6)}) | ' +
          '大小: ${magnitude.toStringAsFixed(6)}');
      }
      
      // 降低变形向量过滤阈值，确保小的变形也能被应用
      // 对于鼻翼宽度变形，完全不过滤
      if (magnitude < 0.0001 && !isNostrilWidthPoint && !hasNostrilWidthDeformation) {
        Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
          '⚠️ [跳过] 过小变形点 | ID: $pointId | 偏移: (${offset.dx.toStringAsFixed(6)}, ${offset.dy.toStringAsFixed(6)})');
        return;
      }
      
      // 如果变形量太小但是鼻翼宽度变形点，增强变形量
      if (magnitude < 0.1 && isNostrilWidthPoint) {
        // 保持方向不变，但增强变形量
        double enhancedMagnitude = 0.5;
        double enhancedDx = offset.dx < 0 ? -enhancedMagnitude : enhancedMagnitude;
        
        Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
          '💡 [增强] 增强鼻翼宽度变形点 | ID: $pointId | ' +
          '原始偏移: (${offset.dx.toStringAsFixed(6)}, ${offset.dy.toStringAsFixed(6)}) | ' +
          '增强后: ($enhancedDx, 0.0)');
        
        // 更新偏移量
        offset = Offset(enhancedDx, 0.0);
        magnitude = enhancedMagnitude;
      }
      
      // 统计变形向量方向
      if (offset.dx > 0) positiveXCount++;
      if (offset.dx < 0) negativeXCount++;
      if (offset.dy > 0) positiveYCount++;
      if (offset.dy < 0) negativeYCount++;
      
      validDeformationCount++;
      
      // 更新最大变形量
      if (magnitude > maxMagnitude) {
        maxMagnitude = magnitude;
        maxMagnitudePointId = pointId;
      }

      // 根据变形强度统一标准，放大变形向量以使变形效果更加明显
      // 根据记忆中的要求，将变形系数从5.0增加到10.0
      double amplificationFactor = 10.0;
      
      // 如果是鼻翼宽度变形，进一步增大放大系数
      if (isNostrilWidthPoint || hasNostrilWidthDeformation) {
        amplificationFactor = 20.0; // 对鼻翼宽度变形使用更大的放大系数
        Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
          '💡 [增强] 鼻翼宽度变形点 | ID: $pointId | 原始偏移: $offset | 放大系数: $amplificationFactor');
        
        // 确保鼻翼宽度变形的效果足够明显
        double minEffectiveMagnitude = 1.0; // 最小有效变形量
        double currentMagnitude = offset.dx.abs();
        
        if (currentMagnitude * amplificationFactor < minEffectiveMagnitude) {
          // 计算需要的放大系数
          double requiredFactor = minEffectiveMagnitude / currentMagnitude;
          amplificationFactor = requiredFactor > amplificationFactor ? requiredFactor : amplificationFactor;
          
          Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
            '💡 [强化] 进一步增强鼻翼宽度变形 | ID: $pointId | ' +
            '原始大小: $currentMagnitude | 调整后放大系数: $amplificationFactor | ' +
            '预期最终大小: ${currentMagnitude * amplificationFactor}');
        }
      }
      
      double dx = offset.dx * amplificationFactor;
      double dy = offset.dy * amplificationFactor;

      print('变形点 $pointId: 原始偏移 (${offset.dx.toStringAsFixed(2)}, ${offset.dy.toStringAsFixed(2)}) | ' +
            '放大后 (${dx.toStringAsFixed(2)}, ${dy.toStringAsFixed(2)}) | 变形量: ${magnitude.toStringAsFixed(2)}');

      // 添加变形向量到数据结构
      deformationData['deformation_vectors'][pointId.toString()] = {
        'dx': dx,
        'dy': dy,
      };
    });

    // 添加所有特征点到landmarks数组
    for (var i = 0; i < landmarks.length; i++) {
      var landmark = landmarks[i];
      if (landmark.containsKey('x') && landmark.containsKey('y')) {
        deformationData['landmarks'].add({
          'id': landmark['id'] ?? i,
          'x': landmark['x'],
          'y': landmark['y'],
        });
      }
    }

    // 记录调试信息
    print('有效变形点数: $validDeformationCount/${deformations.length}');
    if (maxMagnitude > 0) {
      print('最大变形量: ${maxMagnitude.toStringAsFixed(2)} (点 $maxMagnitudePointId)');
    }
    
    // 如果没有有效变形点但有鼻翼宽度变形，添加默认变形向量
    if (validDeformationCount == 0 && hasNostrilWidthDeformation) {
      Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
        '⚠️ [警告] 没有有效变形点但检测到鼻翼宽度变形意图，添加默认变形向量');
      
      // 添加左右鼻翼内侧点的默认变形向量
      int leftNostrilId = 115;  // 左鼻翼内侧点
      int rightNostrilId = 344; // 右鼻翼内侧点
      
      // 假设是增宽鼻翼（向外变形）
      deformationData['deformation_vectors'][leftNostrilId.toString()] = {
        'dx': -10.0,  // 左侧向左移动
        'dy': 0.0,
      };
      
      deformationData['deformation_vectors'][rightNostrilId.toString()] = {
        'dx': 10.0,   // 右侧向右移动
        'dy': 0.0,
      };
      
      Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
        '💡 [修复] 添加默认鼻翼宽度变形向量 | 左点: $leftNostrilId, 右点: $rightNostrilId');
      
      validDeformationCount += 2;
    }
    
    // 计算变形方向统计百分比
    double positiveXPercent = validDeformationCount > 0 ? (positiveXCount / validDeformationCount * 100) : 0;
    double negativeXPercent = validDeformationCount > 0 ? (negativeXCount / validDeformationCount * 100) : 0;
    double positiveYPercent = validDeformationCount > 0 ? (positiveYCount / validDeformationCount * 100) : 0;
    double negativeYPercent = validDeformationCount > 0 ? (negativeYCount / validDeformationCount * 100) : 0;
    
    // 输出变形向量方向统计
    Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
      '📊 [方向统计] 变形向量方向分析 | ' +
      '向右: $positiveXCount (${positiveXPercent.toStringAsFixed(1)}%) | ' +
      '向左: $negativeXCount (${negativeXPercent.toStringAsFixed(1)}%) | ' +
      '向下: $positiveYCount (${positiveYPercent.toStringAsFixed(1)}%) | ' +
      '向上: $negativeYCount (${negativeYPercent.toStringAsFixed(1)}%)');
    
    // 分析变形的对称性
    String symmetryAnalysis = '';
    if (validDeformationCount > 0) {
      if (positiveXCount > 0 && negativeXCount > 0) {
        double xRatio = positiveXCount / negativeXCount.toDouble();
        if (xRatio > 0.8 && xRatio < 1.2) {
          symmetryAnalysis = '水平方向对称性良好';
        } else if (xRatio >= 1.2) {
          symmetryAnalysis = '右侧变形显著';
        } else {
          symmetryAnalysis = '左侧变形显著';
        }
      } else if (positiveXCount > 0) {
        symmetryAnalysis = '仅有向右变形';
      } else if (negativeXCount > 0) {
        symmetryAnalysis = '仅有向左变形';
      }
    }
    
    if (symmetryAnalysis.isNotEmpty) {
      Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
        '📊 [对称性分析] $symmetryAnalysis');
    }
    
    // 打印变形数据样本
    if (deformationData['deformation_vectors'].isNotEmpty) {
      print('变形数据样本:');
      int count = 0;
      deformationData['deformation_vectors'].forEach((key, value) {
        if (count < 5) {
          print('  点 $key: dx=${value['dx']}, dy=${value['dy']}');
          count++;
        }
      });
      if (deformationData['deformation_vectors'].length > 5) {
        print('  ... 共 ${deformationData['deformation_vectors'].length} 个点');
      }
      
      // 输出详细的变形数据样本
      Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
        '📊 [变形数据样本] 共 ${deformationData['deformation_vectors'].length} 个点');
      count = 0;
      deformationData['deformation_vectors'].forEach((key, value) {
        if (count < 5) {
          Logger.log('ImageDeformationEngine', '_prepareDeformationData', 
            '  点 $key: dx=${value['dx'].toStringAsFixed(2)}, dy=${value['dy'].toStringAsFixed(2)}');
          count++;
        }
      });
    }

    return deformationData;
  }
  
  /// 获取Python脚本路径
  Future<String> _getPythonScriptPath() async {
    // 尝试多个可能的源脚本路径，按优先级排序
    final List<String> possibleSourceScriptPaths = [
      // 应用程序包内的资源路径（最高优先级）
      path.join(path.dirname(Platform.resolvedExecutable), '../Resources/flutter_assets/core/image_deformation.py'),
      path.join(path.dirname(Platform.resolvedExecutable), 'Resources/flutter_assets/core/image_deformation.py'),
      // 开发环境中的路径
      path.join(Directory.current.path, 'assets/core/image_deformation.py'),
      path.join(Directory.current.path, 'core/image_deformation.py'),
      // 用户指定的路径
      '/Users/<USER>/beautifun/core/image_deformation.py',
      // 沙盒容器内的路径
      '/Users/<USER>/Library/Containers/com.example.beautifun/Data/core/image_deformation.py',
      // 相对路径
      'core/image_deformation.py',
      'assets/core/image_deformation.py',
    ];
    
    // 打印所有可能的脚本路径用于调试
    print('可能的脚本路径:');
    for (final scriptPath in possibleSourceScriptPaths) {
      final exists = File(scriptPath).existsSync();
      print('  - $scriptPath (${exists ? "存在" : "不存在"})');
      if (exists) {
        print('    文件大小: ${File(scriptPath).lengthSync()} 字节');
        print('    最后修改时间: ${File(scriptPath).lastModifiedSync()}');
      }
    }
    
    String? sourceScriptPath;
    for (final scriptPath in possibleSourceScriptPaths) {
      if (File(scriptPath).existsSync()) {
        sourceScriptPath = scriptPath;
        print('找到源Python脚本: $sourceScriptPath');
        break;
      }
    }
    
    if (sourceScriptPath == null) {
      print('错误: 找不到Python脚本文件');
      throw Exception('找不到Python脚本文件');
    }
    
    return sourceScriptPath;
  }
  
  /// 运行Python变形脚本
  Future<PythonResult> _runPythonDeformation({
    required String inputPath,
    required String outputPath,
    required Map<String, dynamic> deformationData,
  }) async {
    try {
      // 确保Python脚本存在
      final scriptPath = await _getPythonScriptPath();
      print('Python脚本路径: $scriptPath');
      
      // 确保输入文件存在
      final inputFile = File(inputPath);
      if (!inputFile.existsSync()) {
        print('错误: 输入文件不存在: $inputPath');
        return PythonResult(
          success: false,
          errorMessage: '输入文件不存在: $inputPath',
        );
      }
      
      // 确保输出目录存在
      final outputDir = Directory(path.dirname(outputPath));
      if (!outputDir.existsSync()) {
        outputDir.createSync(recursive: true);
      }
      
      // 将变形数据转换为JSON字符串
      final jsonData = jsonEncode(deformationData);
      print('变形数据大小: ${jsonData.length} 字节');
      
      // 为了避免命令行参数过长导致的问题，将数据写入临时文件
      final tempDir = Directory.systemTemp.createTempSync('beautifun_deform_');
      final dataFilePath = path.join(tempDir.path, 'deformation_data.json');
      
      try {
        // 将变形数据写入临时文件
        File(dataFilePath).writeAsStringSync(jsonData);
        print('已将变形数据写入临时文件: $dataFilePath');
        
        // 检测可用的Python解释器
        final pythonExecutables = ['python3', 'python'];
        String? pythonExe;
        
        for (final exe in pythonExecutables) {
          try {
            final result = await Process.run(exe, ['--version']);
            if (result.exitCode == 0) {
              pythonExe = exe;
              print('找到可用的Python解释器: $exe (${result.stdout.toString().trim()})');
              break;
            }
          } catch (e) {
            print('检测Python解释器 $exe 时出错: $e');
          }
        }
        
        if (pythonExe == null) {
          print('错误: 找不到可用的Python解释器');
          return PythonResult(
            success: false,
            errorMessage: '找不到可用的Python解释器',
          );
        }
        
        // 构建命令行参数 - 使用绝对路径
        final absoluteInputPath = inputFile.absolute.path;
        print('使用绝对输入路径: $absoluteInputPath');
        
        final args = [
          scriptPath,
          '--input', absoluteInputPath,
          '--output', outputPath,
          '--data-file', dataFilePath,
        ];
        
        print('执行命令: $pythonExe ${args.join(' ')}');
        
        // 设置环境变量，确保在沙盒环境中能正常运行
        final env = <String, String>{
          'PYTHONIOENCODING': 'utf-8',
          'LC_ALL': 'en_US.UTF-8',
          'PYTHONUNBUFFERED': '1',
          'PATH': Platform.environment['PATH'] ?? '',
        };
        
        // 执行Python脚本
        final process = await Process.run(
          pythonExe,
          args,
          stdoutEncoding: utf8,
          stderrEncoding: utf8,
          environment: env,
          workingDirectory: path.dirname(scriptPath), // 设置工作目录为脚本所在目录
        );
        
        // 处理结果
        final result = _handlePythonProcessResult(process, outputPath);
        
        // 清理临时文件
        try {
          if (tempDir.existsSync()) {
            tempDir.deleteSync(recursive: true);
            print('已清理临时目录: ${tempDir.path}');
          }
        } catch (e) {
          print('警告: 清理临时目录时出错: $e');
        }
        
        return result;
      } catch (e) {
        print('写入变形数据到临时文件时出错: $e');
        
        // 如果临时文件方法失败，尝试使用备用方法
        print('尝试使用备用方法...');
        
        // 尝试使用Python的内置功能直接处理JSON数据
        final tempJsonPath = path.join(tempDir.path, 'data.json');
        try {
          File(tempJsonPath).writeAsStringSync(jsonData);
          
          final args = [
            '-c',
            'import sys, json, os; '
            'sys.path.append(os.path.dirname("$scriptPath")); '
            'import image_deformation; '
            'data = json.load(open("$tempJsonPath")); '
            'image_deformation.apply_deformation_to_file("$inputPath", "$outputPath", data)'
          ];
          
          print('执行备用Python命令: python ${args.join(' ')}');
          
          // 执行Python脚本
          final process = await Process.run(
            'python',
            args,
            stdoutEncoding: utf8,
            stderrEncoding: utf8,
          );
          
          // 处理结果
          return _handlePythonProcessResult(process, outputPath);
        } catch (e2) {
          print('备用方法也失败: $e2');
          return PythonResult(
            success: false,
            errorMessage: '执行Python脚本失败: $e\n备用方法也失败: $e2',
          );
        } finally {
          // 确保临时目录被清理
          try {
            if (tempDir.existsSync()) {
              tempDir.deleteSync(recursive: true);
            }
          } catch (e) {
            print('警告: 清理临时目录时出错: $e');
          }
        }
      }
    } catch (e) {
      print('执行Python脚本时出错: $e');
      return PythonResult(
        success: false,
        errorMessage: '执行Python脚本时出错: $e',
      );
    }
  }
  
  /// 处理Python进程的返回结果
  PythonResult _handlePythonProcessResult(ProcessResult process, String outputPath) {
    // 打印完整的标准输出和标准错误
    print('Python脚本标准输出:');
    print(process.stdout);
    print('Python脚本标准错误:');
    print(process.stderr);
    
    if (process.exitCode != 0) {
      print('Python脚本执行失败: ${process.stderr}');
      print('退出码: ${process.exitCode}');
      print('标准输出: ${process.stdout}');
      return PythonResult(
        success: false,
        errorMessage: process.stderr.toString(),
      );
    }
    
    // 检查输出文件是否存在
    final outputFile = File(outputPath);
    if (!outputFile.existsSync()) {
      print('错误: 输出文件不存在: $outputPath');
      return PythonResult(
        success: false,
        errorMessage: '输出文件不存在: $outputPath',
      );
    }
    
    // 检查输出文件大小
    final fileSize = outputFile.lengthSync();
    print('输出文件大小: $fileSize 字节');
    
    if (fileSize <= 0) {
      print('错误: 输出文件大小为0: $outputPath');
      return PythonResult(
        success: false,
        errorMessage: '输出文件大小为0: $outputPath',
      );
    }
    
    // 解析输出
    print('Python脚本执行成功');
    return PythonResult(
      success: true,
      outputPath: outputPath,
    );
  }
}
