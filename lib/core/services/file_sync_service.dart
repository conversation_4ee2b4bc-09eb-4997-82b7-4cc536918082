import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../../utils/logger.dart';

/// 文件同步服务
/// 
/// 负责确保项目文件在更新后实时同步到运行环境
class FileSyncService {
  // 单例实例
  static final FileSyncService _instance = FileSyncService._internal();
  
  // 工厂构造函数
  factory FileSyncService() {
    return _instance;
  }
  
  // 私有构造函数
  FileSyncService._internal();
  
  /// 输出日志
  /// 
  /// [message] 日志信息
  void log(String message) {
    Logger.log('FileSyncService', 'sync', message);
  }
  
  // 运行环境目录
  String? _runtimeDirectory;
  
  /// 初始化同步服务
  /// 
  /// 设置运行环境目录路径
  Future<void> initialize() async {
    try {
      // 获取应用文档目录作为运行环境目录
      final appDir = await getApplicationDocumentsDirectory();
      _runtimeDirectory = path.join(appDir.path, 'beautifun_runtime');
      
      // 确保运行环境目录存在
      final directory = Directory(_runtimeDirectory!);
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }
      
      log('文件同步服务初始化完成，运行环境目录: $_runtimeDirectory');
    } catch (e) {
      log('文件同步服务初始化失败: $e');
    }
  }
  
  /// 同步配置文件到运行环境
  /// 
  /// 确保配置文件在更新后实时同步到运行环境
  Future<void> syncConfigFiles() async {
    if (_runtimeDirectory == null) {
      await initialize();
    }
    
    try {
      // 同步Dart配置文件
      await _syncFile(
        source: path.join(Directory.current.path, 'lib', 'core', 'config', 'deformation_config.dart'),
        destination: path.join(_runtimeDirectory!, 'config', 'deformation_config.dart')
      );
      
      // 同步Python配置文件
      await _syncFile(
        source: path.join(Directory.current.path, 'core', 'deformation_config.py'),
        destination: path.join(_runtimeDirectory!, 'core', 'deformation_config.py')
      );
      
      log('配置文件同步完成');
    } catch (e) {
      log('配置文件同步失败: $e');
    }
  }
  
  /// 同步Python脚本到运行环境
  /// 
  /// 确保Python脚本在更新后实时同步到运行环境
  Future<void> syncPythonScripts() async {
    if (_runtimeDirectory == null) {
      await initialize();
    }
    
    try {
      // 获取core目录
      final coreDir = Directory(path.join(Directory.current.path, 'core'));
      
      // 确保目标目录存在
      final destCoreDir = Directory(path.join(_runtimeDirectory!, 'core'));
      if (!destCoreDir.existsSync()) {
        destCoreDir.createSync(recursive: true);
      }
      
      // 同步所有Python文件
      await for (final entity in coreDir.list(recursive: false)) {
        if (entity is File && entity.path.endsWith('.py')) {
          final fileName = path.basename(entity.path);
          await _syncFile(
            source: entity.path,
            destination: path.join(_runtimeDirectory!, 'core', fileName)
          );
        }
      }
      
      log('Python脚本同步完成');
    } catch (e) {
      log('Python脚本同步失败: $e');
    }
  }
  
  /// 同步所有必要文件到运行环境
  /// 
  /// 确保所有必要文件在更新后实时同步到运行环境
  Future<void> syncAllFiles() async {
    await syncConfigFiles();
    await syncPythonScripts();
    log('所有文件同步完成');
  }
  
  /// 同步单个文件
  /// 
  /// [source] 源文件路径
  /// [destination] 目标文件路径
  Future<void> _syncFile({required String source, required String destination}) async {
    try {
      final sourceFile = File(source);
      if (!sourceFile.existsSync()) {
        log('源文件不存在: $source');
        return;
      }
      
      // 确保目标目录存在
      final destinationDir = Directory(path.dirname(destination));
      if (!destinationDir.existsSync()) {
        destinationDir.createSync(recursive: true);
      }
      
      // 复制文件
      await sourceFile.copy(destination);
      log('文件同步成功: $source -> $destination');
    } catch (e) {
      log('文件同步失败: $e');
    }
  }
}
