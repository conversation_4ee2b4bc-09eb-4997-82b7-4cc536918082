import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as path;
import '../../utils/logger.dart';
import 'python_script_service.dart';

/// 变形服务
/// 
/// 提供各种图像变形功能，集中处理所有图像变形的逻辑
class DeformationService {
  /// 应用向量场变形
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [deformationVectors] 变形向量数据
  /// 
  /// 返回变形后的图像路径
  static Future<String> applyVectorFieldDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    required Map<String, Map<String, dynamic>> deformationVectors,
  }) async {
    // 生成处理ID，用于日志跟踪
    final processId = DateTime.now().millisecondsSinceEpoch.toString();
    final startTime = DateTime.now();
    
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '🔄 [开始] [处理ID: $processId] 应用向量场变形');
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '📃 [参数] [处理ID: $processId] 输入图像: $imagePath');
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '📃 [参数] [处理ID: $processId] 特征点数量: ${featurePoints.length}');
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '📃 [参数] [处理ID: $processId] 变形向量数量: ${deformationVectors.length}');
    
    // 检查输入文件是否存在
    final inputFile = File(imagePath);
    if (!await inputFile.exists()) {
      Logger.log('DeformationService', 'applyVectorFieldDeformation', '❌ [错误] [处理ID: $processId] 输入文件不存在: $imagePath');
      throw Exception('输入文件不存在: $imagePath');
    }
    
    // 检查输入文件大小和修改时间
    final fileSize = await inputFile.length();
    final lastModified = await inputFile.lastModified();
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '📁 [文件] [处理ID: $processId] 输入文件信息 | 大小: ${fileSize}字节 | 修改时间: ${lastModified.toString()}');
    
    // 创建缓存目录路径
    final appDir = Directory.current.path;
    final cacheDir = path.join(appDir, 'cache');
    
    // 确保缓存目录存在
    final cacheDirObj = Directory(cacheDir);
    if (!await cacheDirObj.exists()) {
      await cacheDirObj.create(recursive: true);
      Logger.log('DeformationService', 'applyVectorFieldDeformation', '📁 [目录] [处理ID: $processId] 创建缓存目录: $cacheDir');
    }
    
    // 生成输出文件路径
    final inputFileName = path.basenameWithoutExtension(imagePath);
    final inputExt = path.extension(imagePath);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final outputFileName = '${inputFileName}_deformed_${timestamp}${inputExt}';
    final outputPath = path.join(cacheDir, outputFileName);
    
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '📃 [路径] [处理ID: $processId] 输出文件路径: $outputPath');
    
    // 记录部分特征点样本，确认使用的是最新特征点缓存
    if (featurePoints.isNotEmpty) {
      final samplePoints = featurePoints.take(3).toList();
      Logger.log('DeformationService', 'applyVectorFieldDeformation', '📈 [缓存] [处理ID: $processId] 特征点样本:');
      for (final point in samplePoints) {
        if (point.containsKey('id') && point.containsKey('x') && point.containsKey('y')) {
          final pointId = point['id'];
          final x = point['x'] is int ? (point['x'] as int).toDouble() : point['x'] as double;
          final y = point['y'] is int ? (point['y'] as int).toDouble() : point['y'] as double;
          Logger.log('DeformationService', 'applyVectorFieldDeformation', '  • 点 $pointId: 位置 (${x.toStringAsFixed(2)}, ${y.toStringAsFixed(2)})');
        }
      }
    }
    
    // 记录部分变形向量样本
    if (deformationVectors.isNotEmpty) {
      final sampleVectors = deformationVectors.entries.take(3).toList();
      Logger.log('DeformationService', 'applyVectorFieldDeformation', '📈 [数据] [处理ID: $processId] 变形向量样本:');
      for (final entry in sampleVectors) {
        final pointId = entry.key;
        final vector = entry.value;
        if (vector.containsKey('dx') && vector.containsKey('dy')) {
          final dx = vector['dx'] is int ? (vector['dx'] as int).toDouble() : vector['dx'] as double;
          final dy = vector['dy'] is int ? (vector['dy'] as int).toDouble() : vector['dy'] as double;
          Logger.log('DeformationService', 'applyVectorFieldDeformation', '  • 点 $pointId: 偏移量 (${dx.toStringAsFixed(2)}, ${dy.toStringAsFixed(2)})');
        }
      }
    }
    
    // 确保每个特征点都有一个id键
    final processedFeaturePoints = featurePoints.asMap().entries.map((entry) {
      final index = entry.key;
      final point = Map<String, dynamic>.from(entry.value);
      
      // 如果特征点没有id键，则添加一个
      if (!point.containsKey('id')) {
        point['id'] = index.toString();
        Logger.log('DeformationService', 'applyVectorFieldDeformation', '⚠️ [警告] [处理ID: $processId] 特征点 $index 缺少ID，已添加');
      }
      
      return point;
    }).toList();
    
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '📃 [数据] [处理ID: $processId] 处理特征点数据，确保每个点都有id | 点数: ${processedFeaturePoints.length}');
    
    // 构建符合Python脚本期望格式的数据结构
    final Map<String, dynamic> combinedData = {
      'landmarks': processedFeaturePoints,
      'deformation_vectors': deformationVectors,
      'metadata': {
        'process_id': processId,
        'timestamp': startTime.millisecondsSinceEpoch,
        'version': '1.0.2', // 版本号，用于跟踪变形算法版本
      }
    };
    
    // 将数据转换为JSON
    final combinedDataJson = jsonEncode(combinedData);
    
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '📃 [数据] [处理ID: $processId] 已构建合并数据，大小: ${combinedDataJson.length} 字节');
    
    // 创建Python脚本
    final scriptPath = await PythonScriptService.createDeformationScript();
    Logger.log('DeformationService', 'applyVectorFieldDeformation', '📃 [脚本] [处理ID: $processId] Python脚本路径: $scriptPath');
    
    // 执行Python脚本
    try {
      // 记录脚本开始执行时间
      final scriptStartTime = DateTime.now();
      Logger.log('DeformationService', 'applyVectorFieldDeformation', '🕒 [时间] [处理ID: $processId] 脚本开始执行时间: ${scriptStartTime.toString()}');
      
      // 打印完整的命令行（不包含数据）
      Logger.log('DeformationService', 'applyVectorFieldDeformation', '🐍 [命令] [处理ID: $processId] 执行: python3 $scriptPath --input $imagePath --output $outputPath --data [...]');
      
      // 执行Python脚本
      final result = await Process.run('python3', [
        scriptPath,
        '--input', imagePath,
        '--output', outputPath,
        '--data', combinedDataJson,
      ]);
      
      // 打印Python脚本的所有输出，包括标准输出和错误输出
      final stdoutLines = result.stdout.toString().split('\n');
      for (final line in stdoutLines) {
        if (line.trim().isNotEmpty) {
          Logger.log('DeformationService', 'applyVectorFieldDeformation', '🐍 [Python] [stdout] $line');
        }
      }
      
      final stderrLines = result.stderr.toString().split('\n');
      for (final line in stderrLines) {
        if (line.trim().isNotEmpty) {
          Logger.log('DeformationService', 'applyVectorFieldDeformation', '⚠️ [Python] [stderr] $line');
        }
      }
      
      // 检查退出代码
      if (result.exitCode != 0) {
        final errorMessage = result.stderr.toString().trim().isNotEmpty 
            ? result.stderr.toString() 
            : '未知错误，退出代码: ${result.exitCode}';
        Logger.log('DeformationService', 'applyVectorFieldDeformation', '❌ [错误] Python脚本执行失败: $errorMessage');
        throw Exception('Python脚本执行失败: $errorMessage');
      }
      
      // 检查输出文件是否存在
      final outputFile = File(outputPath);
      if (!await outputFile.exists()) {
        Logger.log('DeformationService', 'applyVectorFieldDeformation', '❌ [错误] 输出文件不存在: $outputPath');
        throw Exception('输出文件不存在: $outputPath');
      }
      
      Logger.log('DeformationService', 'applyVectorFieldDeformation', '✅ [完成] 向量场变形应用成功');
      return outputPath;
    } catch (e) {
      Logger.log('DeformationService', 'applyVectorFieldDeformation', '❌ [异常] 应用向量场变形异常: $e');
      throw Exception('应用向量场变形异常: $e');
    }
  }
  
  /// 将变形数据列表转换为映射格式
  /// 
  /// [deformationData] 变形数据列表
  /// 
  /// 返回映射格式的变形数据
  static Map<String, Map<String, dynamic>> convertDeformationDataToMap(
    List<Map<String, dynamic>> deformationData
  ) {
    final Map<String, Map<String, dynamic>> result = {};
    
    for (final deform in deformationData) {
      if (deform.containsKey('id') && deform.containsKey('dx') && deform.containsKey('dy')) {
        final pointId = deform['id'].toString();
        result[pointId] = {
          'dx': deform['dx'],
          'dy': deform['dy'],
        };
      }
    }
    
    return result;
  }
  
  /// 应用面部轮廓变形
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [contourTighten] 轮廓收紧参数
  /// [chinAdjust] 下巴调整参数
  /// [cheekboneAdjust] 颧骨调整参数
  /// [faceShape] 脸型优化参数
  /// 
  /// 返回变形后的图像路径
  static Future<String> applyFaceContourDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double contourTighten = 0.0,
    double chinAdjust = 0.0,
    double cheekboneAdjust = 0.0,
    double faceShape = 0.0,
  }) async {
    Logger.log('DeformationService', 'applyFaceContourDeformation', '🔄 [开始] 应用面部轮廓变形');
    
    // 构建变形向量
    final deformationVectors = <String, Map<String, dynamic>>{};
    
    // 实现面部轮廓变形的逻辑
    // 这里需要根据具体的面部特征点和变形需求来实现
    // 以下是示例实现
    
    // 轮廓收紧 - 使用特征点3, 5, 7, 9进行变形
    if (contourTighten != 0.0) {
      // 负值表示收紧，正值表示放松
      final factor = -contourTighten * 0.02; // 缩放系数
      
      // 左侧脸颊
      deformationVectors['3'] = {'dx': factor, 'dy': 0.0};
      // 右侧脸颊
      deformationVectors['7'] = {'dx': -factor, 'dy': 0.0};
    }
    
    // 下巴调整 - 使用特征点8进行变形
    if (chinAdjust != 0.0) {
      // 负值表示缩短，正值表示延长
      final factor = chinAdjust * 0.02; // 缩放系数
      
      // 下巴点
      deformationVectors['8'] = {'dx': 0.0, 'dy': factor};
    }
    
    // 颧骨调整 - 使用特征点2, 10进行变形
    if (cheekboneAdjust != 0.0) {
      // 负值表示减小，正值表示增大
      final factor = cheekboneAdjust * 0.02; // 缩放系数
      
      // 左侧颧骨
      deformationVectors['2'] = {'dx': factor, 'dy': 0.0};
      // 右侧颧骨
      deformationVectors['10'] = {'dx': -factor, 'dy': 0.0};
    }
    
    // 脸型优化 - 使用多个特征点进行综合变形
    if (faceShape != 0.0) {
      // 负值表示瘦脸，正值表示丰满
      final factor = -faceShape * 0.015; // 缩放系数
      
      // 左侧脸颊轮廓点
      deformationVectors['1'] = {'dx': factor, 'dy': 0.0};
      deformationVectors['2'] = {'dx': factor * 1.2, 'dy': 0.0};
      deformationVectors['3'] = {'dx': factor * 1.5, 'dy': 0.0};
      
      // 右侧脸颊轮廓点
      deformationVectors['9'] = {'dx': -factor * 1.2, 'dy': 0.0};
      deformationVectors['10'] = {'dx': -factor * 1.5, 'dy': 0.0};
      deformationVectors['11'] = {'dx': -factor, 'dy': 0.0};
    }
    
    // 应用向量场变形
    return applyVectorFieldDeformation(
      imagePath: imagePath,
      featurePoints: featurePoints,
      deformationVectors: deformationVectors,
    );
  }
  
  /// 应用鼻部塑形变形
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [bridgeHeight] 鼻梁高度参数
  /// [tipAdjust] 鼻尖调整参数
  /// [nostrilWidth] 鼻翼宽度参数
  /// [baseHeight] 鼻基抬高参数
  /// 
  /// 返回变形后的图像路径
  static Future<String> applyNoseDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double bridgeHeight = 0.0,
    double tipAdjust = 0.0,
    double nostrilWidth = 0.0,
    double baseHeight = 0.0,
  }) async {
    Logger.log('DeformationService', 'applyNoseDeformation', '🔄 [开始] 应用鼻部塑形变形');
    
    // 构建变形向量
    final deformationVectors = <String, Map<String, dynamic>>{};
    
    // 实现鼻部塑形变形的逻辑
    // 这里需要根据具体的鼻部特征点和变形需求来实现
    // 以下是示例实现
    
    // 鼻梁高度 - 使用特征点27, 28, 29进行变形
    if (bridgeHeight != 0.0) {
      // 负值表示降低，正值表示提高
      final factor = bridgeHeight * 0.02; // 缩放系数
      
      // 鼻梁上部
      deformationVectors['27'] = {'dx': 0.0, 'dy': -factor * 0.7};
      // 鼻梁中部
      deformationVectors['28'] = {'dx': 0.0, 'dy': -factor};
      // 鼻梁下部
      deformationVectors['29'] = {'dx': 0.0, 'dy': -factor * 0.7};
    }
    
    // 鼻尖调整 - 使用特征点30进行变形
    if (tipAdjust != 0.0) {
      // 负值表示降低，正值表示提升
      final factor = tipAdjust * 0.02; // 缩放系数
      
      // 鼻尖
      deformationVectors['30'] = {'dx': 0.0, 'dy': -factor};
    }
    
    // 鼻翼宽度 - 使用特征点31, 35进行变形
    if (nostrilWidth != 0.0) {
      // 负值表示缩小，正值表示增宽
      final factor = nostrilWidth * 0.02; // 缩放系数
      
      // 左鼻翼
      deformationVectors['31'] = {'dx': -factor, 'dy': 0.0};
      // 右鼻翼
      deformationVectors['35'] = {'dx': factor, 'dy': 0.0};
    }
    
    // 鼻基抬高 - 使用特征点32, 33, 34进行变形
    if (baseHeight != 0.0) {
      // 负值表示降低，正值表示抬高
      final factor = baseHeight * 0.02; // 缩放系数
      
      // 鼻基左侧
      deformationVectors['32'] = {'dx': 0.0, 'dy': -factor};
      // 鼻基中部
      deformationVectors['33'] = {'dx': 0.0, 'dy': -factor};
      // 鼻基右侧
      deformationVectors['34'] = {'dx': 0.0, 'dy': -factor};
    }
    
    // 应用向量场变形
    return applyVectorFieldDeformation(
      imagePath: imagePath,
      featurePoints: featurePoints,
      deformationVectors: deformationVectors,
    );
  }
  
  /// 应用眼部美化变形
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [doubleFold] 双眼皮参数
  /// [canthalTilt] 开眼角参数
  /// [eyeBagRemoval] 去眼袋参数
  /// [outerCornerLift] 提眼尾参数
  /// 
  /// 返回变形后的图像路径
  static Future<String> applyEyeDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double doubleFold = 0.0,
    double canthalTilt = 0.0,
    double eyeBagRemoval = 0.0,
    double outerCornerLift = 0.0,
  }) async {
    Logger.log('DeformationService', 'applyEyeDeformation', '🔄 [开始] 应用眼部美化变形');
    
    // 构建变形向量
    final deformationVectors = <String, Map<String, dynamic>>{};
    
    // 实现眼部美化变形的逻辑
    // 这里需要根据具体的眼部特征点和变形需求来实现
    // 以下是示例实现
    
    // 双眼皮 - 使用特征点37, 38, 43, 44进行变形
    if (doubleFold != 0.0) {
      // 负值表示减小，正值表示增大
      final factor = doubleFold * 0.015; // 缩放系数
      
      // 左眼上眼睑
      deformationVectors['37'] = {'dx': 0.0, 'dy': -factor};
      deformationVectors['38'] = {'dx': 0.0, 'dy': -factor};
      
      // 右眼上眼睑
      deformationVectors['43'] = {'dx': 0.0, 'dy': -factor};
      deformationVectors['44'] = {'dx': 0.0, 'dy': -factor};
    }
    
    // 开眼角 - 使用特征点36, 39, 42, 45进行变形
    if (canthalTilt != 0.0) {
      // 负值表示降低，正值表示提升
      final factor = canthalTilt * 0.015; // 缩放系数
      
      // 左眼内眼角
      deformationVectors['36'] = {'dx': -factor, 'dy': -factor};
      // 左眼外眼角
      deformationVectors['39'] = {'dx': factor, 'dy': -factor};
      
      // 右眼内眼角
      deformationVectors['42'] = {'dx': -factor, 'dy': -factor};
      // 右眼外眼角
      deformationVectors['45'] = {'dx': factor, 'dy': -factor};
    }
    
    // 去眼袋 - 使用特征点41, 47进行变形
    if (eyeBagRemoval != 0.0) {
      // 负值表示减小，正值表示增大
      final factor = -eyeBagRemoval * 0.02; // 缩放系数，注意这里取反
      
      // 左眼下方
      deformationVectors['41'] = {'dx': 0.0, 'dy': factor};
      // 右眼下方
      deformationVectors['47'] = {'dx': 0.0, 'dy': factor};
    }
    
    // 提眼尾 - 使用特征点39, 45进行变形
    if (outerCornerLift != 0.0) {
      // 负值表示降低，正值表示提升
      final factor = outerCornerLift * 0.02; // 缩放系数
      
      // 左眼外眼角
      deformationVectors['39'] = {'dx': 0.0, 'dy': -factor};
      // 右眼外眼角
      deformationVectors['45'] = {'dx': 0.0, 'dy': -factor};
    }
    
    // 应用向量场变形
    return applyVectorFieldDeformation(
      imagePath: imagePath,
      featurePoints: featurePoints,
      deformationVectors: deformationVectors,
    );
  }
  
  /// 应用唇部造型变形
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [lipShape] 唇形调整参数
  /// [lipThickness] 嘴唇厚度参数
  /// [mouthCorner] 嘴角上扬参数
  /// [lipColor] 唇色优化参数
  /// 
  /// 返回变形后的图像路径
  static Future<String> applyLipDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double lipShape = 0.0,
    double lipThickness = 0.0,
    double mouthCorner = 0.0,
    double lipColor = 0.0,
  }) async {
    Logger.log('DeformationService', 'applyLipDeformation', '🔄 [开始] 应用唇部造型变形');
    
    // 构建变形向量
    final deformationVectors = <String, Map<String, dynamic>>{};
    
    // 实现唇部造型变形的逻辑
    // 这里需要根据具体的唇部特征点和变形需求来实现
    // 以下是示例实现
    
    // 唇形调整 - 使用特征点48-60进行变形
    if (lipShape != 0.0) {
      // 负值表示收紧，正值表示放松
      final factor = lipShape * 0.015; // 缩放系数
      
      // 上唇轮廓
      deformationVectors['48'] = {'dx': -factor, 'dy': 0.0};
      deformationVectors['49'] = {'dx': -factor * 0.5, 'dy': -factor};
      deformationVectors['50'] = {'dx': 0.0, 'dy': -factor};
      deformationVectors['51'] = {'dx': factor * 0.5, 'dy': -factor};
      deformationVectors['52'] = {'dx': factor, 'dy': 0.0};
      
      // 下唇轮廓
      deformationVectors['54'] = {'dx': factor, 'dy': 0.0};
      deformationVectors['55'] = {'dx': factor * 0.5, 'dy': factor};
      deformationVectors['56'] = {'dx': 0.0, 'dy': factor};
      deformationVectors['57'] = {'dx': -factor * 0.5, 'dy': factor};
      deformationVectors['58'] = {'dx': -factor, 'dy': 0.0};
    }
    
    // 嘴唇厚度 - 使用特征点50, 52, 56, 58进行变形
    if (lipThickness != 0.0) {
      // 负值表示减小，正值表示增大
      final factor = lipThickness * 0.02; // 缩放系数
      
      // 上唇
      deformationVectors['50'] = {'dx': 0.0, 'dy': -factor};
      // 下唇
      deformationVectors['56'] = {'dx': 0.0, 'dy': factor};
    }
    
    // 嘴角上扬 - 使用特征点48, 54进行变形
    if (mouthCorner != 0.0) {
      // 负值表示下垂，正值表示上扬
      final factor = mouthCorner * 0.02; // 缩放系数
      
      // 左嘴角
      deformationVectors['48'] = {'dx': 0.0, 'dy': -factor};
      // 右嘴角
      deformationVectors['54'] = {'dx': 0.0, 'dy': -factor};
    }
    
    // 唇色优化 - 这需要颜色处理，在向量场变形中不实现
    if (lipColor != 0.0) {
      // 暂时不实现颜色处理逻辑
      // 这里只是占位，实际实现需要颜色处理功能
      Logger.log('DeformationService', 'applyLipDeformation', '⚠️ [警告] 唇色优化功能尚未实现');
    }
    
    // 应用向量场变形
    return applyVectorFieldDeformation(
      imagePath: imagePath,
      featurePoints: featurePoints,
      deformationVectors: deformationVectors,
    );
  }
  
  /// 应用抗衰冻龄变形
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [nasolabialFolds] 法令纹参数
  /// [wrinkleRemoval] 去皱纹参数
  /// [foreheadFullness] 额头饱满参数
  /// [facialFirmness] 面容紧致参数
  /// 
  /// 返回变形后的图像路径
  static Future<String> applyAntiAgingDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double nasolabialFolds = 0.0,
    double wrinkleRemoval = 0.0,
    double foreheadFullness = 0.0,
    double facialFirmness = 0.0,
  }) async {
    Logger.log('DeformationService', 'applyAntiAgingDeformation', '🔄 [开始] 应用抗衰冻龄变形');
    
    // 构建变形向量
    final deformationVectors = <String, Map<String, dynamic>>{};
    
    // 实现抗衰冻龄变形的逻辑
    // 这里需要根据具体的面部特征点和变形需求来实现
    // 以下是示例实现
    
    // 法令纹 - 使用特征点31, 35, 48, 54进行变形
    if (nasolabialFolds != 0.0) {
      // 负值表示减轻，正值表示加重
      final factor = -nasolabialFolds * 0.02; // 缩放系数，注意这里取反
      
      // 左侧法令纹区域
      deformationVectors['31'] = {'dx': 0.0, 'dy': factor};
      deformationVectors['48'] = {'dx': 0.0, 'dy': factor};
      
      // 右侧法令纹区域
      deformationVectors['35'] = {'dx': 0.0, 'dy': factor};
      deformationVectors['54'] = {'dx': 0.0, 'dy': factor};
    }
    
    // 去皱纹 - 使用特征点17, 21, 22, 26进行变形
    if (wrinkleRemoval != 0.0) {
      // 负值表示减轻，正值表示加重
      final factor = -wrinkleRemoval * 0.015; // 缩放系数，注意这里取反
      
      // 左侧眼角皱纹
      deformationVectors['17'] = {'dx': 0.0, 'dy': factor};
      deformationVectors['21'] = {'dx': 0.0, 'dy': factor};
      
      // 右侧眼角皱纹
      deformationVectors['22'] = {'dx': 0.0, 'dy': factor};
      deformationVectors['26'] = {'dx': 0.0, 'dy': factor};
    }
    
    // 额头饱满 - 使用特征点17, 18, 19, 20, 21, 22, 23, 24, 25, 26进行变形
    if (foreheadFullness != 0.0) {
      // 负值表示减小，正值表示增大
      final factor = foreheadFullness * 0.015; // 缩放系数
      
      // 额头区域
      for (int i = 17; i <= 26; i++) {
        deformationVectors[i.toString()] = {'dx': 0.0, 'dy': -factor};
      }
    }
    
    // 面容紧致 - 使用多个特征点进行综合变形
    if (facialFirmness != 0.0) {
      // 负值表示松弛，正值表示紧致
      final factor = facialFirmness * 0.02; // 缩放系数
      
      // 脸颊区域
      for (int i = 1; i <= 16; i++) {
        // 计算向内的方向
        double dx = 0.0;
        if (i <= 8) {
          // 左侧脸颊，向右移动
          dx = factor;
        } else {
          // 右侧脸颊，向左移动
          dx = -factor;
        }
        
        // 向上提升
        double dy = -factor * 0.5;
        
        deformationVectors[i.toString()] = {'dx': dx, 'dy': dy};
      }
    }
    
    // 应用向量场变形
    return applyVectorFieldDeformation(
      imagePath: imagePath,
      featurePoints: featurePoints,
      deformationVectors: deformationVectors,
    );
  }
}
