import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../../utils/logger.dart';

/// Python脚本服务
/// 
/// 负责生成和管理Python脚本，确保脚本在沙盒环境中正确创建和更新
class PythonScriptService {
  /// 创建变形算法的Python脚本
  /// 
  /// 在应用沙盒目录中创建或更新Python脚本
  /// 返回脚本文件路径
  static Future<String> createDeformationScript() async {
    // 获取应用沙盒目录
    final appDocDir = await getApplicationDocumentsDirectory();
    final sandboxCorePath = path.join(appDocDir.path, 'core');
    
    // 确保沙盒中的core目录存在
    final sandboxCoreDir = Directory(sandboxCorePath);
    if (!await sandboxCoreDir.exists()) {
      await sandboxCoreDir.create(recursive: true);
      Logger.log('PythonScriptService', 'createDeformationScript', '📁 [创建] 创建沙盒core目录: $sandboxCorePath');
    }
    
    // 检查Python环境
    try {
      final pythonVersionResult = await Process.run('python3', ['--version']);
      if (pythonVersionResult.exitCode == 0) {
        Logger.log('PythonScriptService', 'createDeformationScript', '✅ [环境] Python版本: ${pythonVersionResult.stdout.toString().trim()}');
      } else {
        Logger.log('PythonScriptService', 'createDeformationScript', '⚠️ [警告] 无法获取Python版本: ${pythonVersionResult.stderr}');
      }
    } catch (e) {
      Logger.log('PythonScriptService', 'createDeformationScript', '⚠️ [警告] 检查Python环境时出错: $e');
    }
    
    // 沙盒中的Python脚本路径
    final sandboxScriptPath = path.join(sandboxCorePath, 'image_deformation.py');
    
    // 每次都重新创建脚本，确保使用最新版本
    final sandboxScriptFile = File(sandboxScriptPath);
    // 删除旧脚本（如果存在）
    if (await sandboxScriptFile.exists()) {
      try {
        await sandboxScriptFile.delete();
        Logger.log('PythonScriptService', 'createDeformationScript', '🗑️ [删除] 删除旧版本Python脚本');
      } catch (e) {
        Logger.log('PythonScriptService', 'createDeformationScript', '⚠️ [警告] 无法删除旧版本Python脚本: $e');
      }
    }
    
    // 创建新脚本
    try {
      // 直接在沙盒中创建Python脚本，而不是从外部复制
      // 这样可以避免沙盒环境下的权限问题
      await sandboxScriptFile.writeAsString('''
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import numpy as np
import argparse
import json
import os
import sys
import time

def log(message):
    """打印日志信息"""
    print(f"[Python] {message}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='图像变形处理工具')
    parser.add_argument('--input', required=True, help='输入图像路径')
    parser.add_argument('--output', required=True, help='输出图像路径')
    parser.add_argument('--data', required=True, help='变形数据JSON字符串')
    return parser.parse_args()

def apply_deformation(image, landmarks, deformation_vectors):
    """应用变形到图像 - 增强版
    
    此版本大幅增强变形效果，确保变形可见
    
    Args:
        image: 输入图像
        landmarks: 特征点数据
        deformation_vectors: 变形数据，包含特征点ID和位移向量
    
    Returns:
        变形后的图像
    """
    height, width = image.shape[:2]
    log(f"图像尺寸: {width}x{height}")
    
    # 创建网格
    grid_x, grid_y = np.meshgrid(np.arange(width), np.arange(height))
    
    # 创建变形映射
    map_x = grid_x.astype(np.float32)
    map_y = grid_y.astype(np.float32)
    
    # 记录原始图像用于调试
    debug_image = image.copy()
    
    # 变形强度统一标准参数 - 适度版
    scale_factor = 3.0  # 比例值转换为像素单位的系数调整为3.0，使变形效果适度
    influence_radius = width * 0.05  # 影响半径设置为图像宽度的5%
    point_power = 2.0  # 点的影响力统一为2.0
    weight_factor = 2.0  # 权重因子调整为2.0，使变形效果适度
    falloff_factor = 0.5  # 减弱因子调整为0.5，使变形效果更自然
    
    log(f"变形参数: 比例因子={scale_factor}, 影响半径={influence_radius:.1f}px, 点影响力={point_power}, 权重因子={weight_factor}")
    
    # 应用变形
    for point_id, vector in deformation_vectors.items():
        # 获取位移向量
        dx = vector['dx']
        dy = vector['dy']
        
        # 确保变形向量有足够大的影响
        # 如果变形向量太小，放大它
        magnitude = np.sqrt(dx**2 + dy**2)
        if magnitude < 0.01:  # 降低忽略阈值，确保小变形也能被应用
            continue
        
        # 获取特征点坐标
        point_found = False
        point_x, point_y = 0, 0
        
        for landmark in landmarks:
            if str(landmark['id']) == point_id:
                point_x = landmark['x']
                point_y = landmark['y']
                point_found = True
                break
        
        if not point_found:
            log(f"⚠️ 警告: 未找到ID为{point_id}的特征点")
            continue
        
        # 在调试图像上绘制特征点和变形向量
        cv2.circle(debug_image, (int(point_x), int(point_y)), 5, (0, 255, 0), -1)
        cv2.line(
            debug_image, 
            (int(point_x), int(point_y)), 
            (int(point_x + dx * 50), int(point_y + dy * 50)), 
            (0, 0, 255), 
            2
        )
        
        # 计算每个像素点受到的影响
        for y in range(height):
            for x in range(width):
                # 计算像素点到特征点的距离
                distance = np.sqrt((x - point_x)**2 + (y - point_y)**2)
                
                # 如果距离在影响半径内，应用变形
                if distance < influence_radius:
                    # 计算影响因子（距离越近，影响越大）
                    influence = (1 - (distance / influence_radius)**point_power) * falloff_factor
                    
                    # 应用变形（注意这里是加法，确保变形方向正确）
                    map_x[y, x] += influence * dx * weight_factor
                    map_y[y, x] += influence * dy * weight_factor
    
    # 应用变形映射
    # 使用更高质量的插值方法
    deformed_image = cv2.remap(image, map_x, map_y, cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
    
    return deformed_image

def main():
    """主函数"""
    try:
        start_time = time.time()
        
        # 解析命令行参数
        args = parse_arguments()
        
        # 输出脚本版本信息
        log(f"图像变形脚本版本: 1.0.1")
        log(f"Python版本: {sys.version}")
        log(f"OpenCV版本: {cv2.__version__}")
        log(f"NumPy版本: {np.__version__}")
        
        # 检查输入文件是否存在
        if not os.path.exists(args.input):
            log(f"❌ 错误: 输入文件不存在: {args.input}")
            sys.exit(1)
        
        # 读取输入图像
        log(f"读取输入图像: {args.input}")
        image = cv2.imread(args.input)
        if image is None:
            log(f"❌ 错误: 无法读取输入图像: {args.input}")
            sys.exit(1)
        
        # 检查图像是否有效
        if image.size == 0 or len(image.shape) < 2:
            log(f"❌ 错误: 输入图像无效，尺寸: {image.shape}")
            sys.exit(1)
        
        # 解析变形数据
        try:
            data = json.loads(args.data)
            landmarks = data.get('landmarks', [])
            deformation_vectors = data.get('deformation_vectors', {})
            
            log(f"特征点数量: {len(landmarks)}")
            log(f"变形向量数量: {len(deformation_vectors)}")
            
            # 检查特征点和变形向量数量
            if len(landmarks) == 0:
                log("❌ 错误: 特征点数据为空")
                sys.exit(1)
                
            if len(deformation_vectors) == 0:
                log("❌ 错误: 变形向量数据为空")
                sys.exit(1)
        except json.JSONDecodeError as e:
            log(f"❌ 错误: JSON解析失败: {str(e)}")
            sys.exit(1)
        except Exception as e:
            log(f"❌ 错误: 解析变形数据失败: {str(e)}")
            sys.exit(1)
        
        # 应用变形
        log("开始应用变形...")
        deformed_image = apply_deformation(image, landmarks, deformation_vectors)
        
        # 检查变形后的图像是否有效
        if deformed_image is None or deformed_image.size == 0:
            log("❌ 错误: 变形后的图像无效")
            sys.exit(1)
        
        # 确保输出目录存在
        output_dir = os.path.dirname(args.output)
        if output_dir and not os.path.exists(output_dir):
            log(f"创建输出目录: {output_dir}")
            os.makedirs(output_dir, exist_ok=True)
        
        # 保存结果
        log(f"保存变形结果到: {args.output}")
        success = cv2.imwrite(args.output, deformed_image)
        
        # 检查保存是否成功
        if not success:
            log(f"❗ 警告: cv2.imwrite 返回失败")
        
        # 再次检查文件是否存在
        if not os.path.exists(args.output):
            log(f"❌ 错误: 输出文件未创建成功: {args.output}")
            sys.exit(1)
        
        # 计算处理时间
        processing_time = (time.time() - start_time) * 1000
        log(f"✅ 变形完成! 处理时间: {processing_time:.2f}ms")
    except Exception as e:
        log(f"❌ 未捕获的错误: {str(e)}")
        import traceback
        log(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
''');
      
      // 设置脚本权限
      await Process.run('chmod', ['+x', sandboxScriptPath]);
      
      Logger.log('PythonScriptService', 'createDeformationScript', '✅ [创建] 成功创建Python脚本: $sandboxScriptPath');
      return sandboxScriptPath;
    } catch (e) {
      Logger.log('PythonScriptService', 'createDeformationScript', '❌ [错误] 创建Python脚本失败: $e');
      throw Exception('创建Python脚本失败: $e');
    }
  }
}
