import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

/// 日志级别枚举
enum LogLevel {
  /// 调试级别
  debug,
  
  /// 信息级别
  info,
  
  /// 警告级别
  warning,
  
  /// 错误级别
  error,
}

/// 日志服务类
/// 
/// 提供全应用范围的日志记录功能，支持不同级别的日志，
/// 记录函数进入/退出、执行时间和详细参数，仅支持终端输出
class LoggerService {
  /// 日期格式化器
  final DateFormat _dateFormatter = DateFormat('yyyy-MM-dd HH:mm:ss.SSS');
  
  /// 私有构造函数
  LoggerService();
  
  /// 初始化日志服务
  void initialize() {
    logInfo('LoggerService', '日志服务初始化成功，仅输出到终端');
  }
  
  /// 记录函数进入
  /// 
  /// [module] 模块名称
  /// [functionName] 函数名称
  /// [params] 函数参数
  void logFunctionEntry(String module, String functionName, [Map<String, dynamic>? params]) {
    final String message = '进入函数 $functionName ${params != null ? '参数: $params' : ''}';
    _log(LogLevel.debug, module, message);
  }
  
  /// 记录函数退出
  /// 
  /// [module] 模块名称
  /// [functionName] 函数名称
  /// [result] 函数返回结果
  /// [executionTime] 函数执行时间
  void logFunctionExit(String module, String functionName, [dynamic result, Duration? executionTime]) {
    final String timeInfo = executionTime != null ? '执行时间: ${executionTime.inMilliseconds}ms' : '';
    final String resultInfo = result != null ? '返回结果: $result' : '';
    final String message = '退出函数 $functionName $timeInfo $resultInfo'.trim();
    _log(LogLevel.debug, module, message);
  }
  
  /// 记录调试信息
  /// 
  /// [module] 模块名称
  /// [message] 日志消息
  void logDebug(String module, String message) {
    _log(LogLevel.debug, module, message);
  }
  
  /// 记录一般信息
  /// 
  /// [module] 模块名称
  /// [message] 日志消息
  void logInfo(String module, String message) {
    _log(LogLevel.info, module, message);
  }
  
  /// 记录警告信息
  /// 
  /// [module] 模块名称
  /// [message] 日志消息
  void logWarning(String module, String message) {
    _log(LogLevel.warning, module, message);
  }
  
  /// 记录错误信息
  /// 
  /// [module] 模块名称
  /// [message] 日志消息
  /// [exception] 异常对象
  void logError(String module, String message, [Exception? exception]) {
    final String exceptionInfo = exception != null ? ' 异常: $exception' : '';
    _log(LogLevel.error, module, '$message$exceptionInfo');
  }
  
  /// 内部日志记录方法
  /// 
  /// [level] 日志级别
  /// [module] 模块名称
  /// [message] 日志消息
  void _log(LogLevel level, String module, String message) {
    final String timestamp = _dateFormatter.format(DateTime.now());
    final String levelStr = level.toString().split('.').last.toUpperCase();
    final String logMessage = '[$timestamp] [$levelStr] [$module] | $message';
    
    // 控制台输出
    switch (level) {
      case LogLevel.debug:
        debugPrint(logMessage);
        break;
      case LogLevel.info:
        debugPrint('\x1B[32m$logMessage\x1B[0m'); // 绿色
        break;
      case LogLevel.warning:
        debugPrint('\x1B[33m$logMessage\x1B[0m'); // 黄色
        break;
      case LogLevel.error:
        debugPrint('\x1B[31m$logMessage\x1B[0m'); // 红色
        break;
    }
  }
}
