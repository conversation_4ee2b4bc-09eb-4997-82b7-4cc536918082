import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import '../../utils/logger.dart';
import '../models/feature_point.dart';


/// 图像处理服务
/// 
/// 负责与Python脚本交互，处理图像分析和变换
class ImageProcessingService {
  static final ImageProcessingService _instance = ImageProcessingService._internal();
  
  /// 日志记录器
  final Logger _logger = Logger();
  
  /// Python解释器路径
  String _pythonPath = 'python3';
  
  /// 核心目录路径
  String _corePath = '';
  
  /// 图像宽度
  double _imageWidth = 0.0;
  
  /// 图像高度
  double _imageHeight = 0.0;
  
  /// 特征点缓存
  Map<String, dynamic> _landmarksCache = {};
  
  /// 工作目录
  String _workingDirectory = '';
  
  /// 获取图像宽度
  double getImageWidth() => _imageWidth;
  
  /// 获取图像高度
  double getImageHeight() => _imageHeight;
  
  /// 构造函数
  factory ImageProcessingService() {
    return _instance;
  }
  
  /// 内部构造函数
  ImageProcessingService._internal();
  
  /// 解码图像文件
  /// 
  /// 使用Flutter原生的图像解码功能
  /// 返回图像尺寸信息
  Future<Map<String, double>?> decodeImageFile(File imageFile) async {
    Logger.log('ImageProcessingService', 'decodeImageFile', '开始解码图像: ${imageFile.path}', LogLevel.info);
    
    try {
      // 验证文件存在
      if (!await imageFile.exists()) {
        Logger.log('ImageProcessingService', 'decodeImageFile', '图像文件不存在', LogLevel.error);
        return null;
      }
      
      // 读取图像文件数据
      final bytes = await imageFile.readAsBytes();
      
      // 解码图像
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      final image = frame.image;
      
      // 获取尺寸
      _imageWidth = image.width.toDouble();
      _imageHeight = image.height.toDouble();
      
      Logger.log('ImageProcessingService', 'decodeImageFile', '图像解码成功: ${_imageWidth.toInt()} x ${_imageHeight.toInt()}', LogLevel.info);
      
      // 释放资源
      image.dispose();
      
      return {
        'width': _imageWidth,
        'height': _imageHeight,
      };
    } catch (e) {
      Logger.log('ImageProcessingService', 'decodeImageFile', '解码图像失败: $e', LogLevel.error);
      return null;
    }
  }
  
  /// 获取图像尺寸
  Future<Map<String, double>?> getImageSize(File imageFile) async {
    try {
      // 调用face_mesh_processor.py获取图像尺寸
      final result = await _runPythonModule('face_mesh_processor.py', {
        'image_path': imageFile.path,
        'options': {
          'get_image_size': true,
        },
      });
      
      if (result == null || !result.containsKey('image_size')) {
        Logger.log('ImageProcessingService', 'decodeImageSize', '无法获取图像尺寸', LogLevel.error);
        return {
          'width': 0.0,
          'height': 0.0,
        };
      }
      
      final size = result['image_size'];
      _imageWidth = size['width'].toDouble();
      _imageHeight = size['height'].toDouble();
      
      return {
        'width': _imageWidth,
        'height': _imageHeight,
      };
    } catch (e) {
      Logger.log('ImageProcessingService', 'decodeImageSize', '解码图像尺寸失败: $e', LogLevel.error);
      return {
        'width': 0.0,
        'height': 0.0,
      };
    }
  }
  
  /// 初始化服务
  Future<void> initializeService() async {
    Logger.log('ImageProcessingService', 'initializeService', '开始初始化服务', LogLevel.info);
    
    // 获取当前工作目录
    _workingDirectory = Directory.current.path;
    Logger.log('ImageProcessingService', 'initializeService', '当前工作目录: $_workingDirectory', LogLevel.info);
    
    // 尝试多个可能的核心目录路径
    final List<String> possibleCorePaths = [
      path.join(_workingDirectory, 'core'),  // 当前工作目录下的core
      '/Users/<USER>/beautifun/core',        // 项目根目录下的core
      path.join(_workingDirectory, '../../../core'),  // 向上三级目录下的core
      path.join(_workingDirectory, '../../core'),     // 向上两级目录下的core
      path.join(_workingDirectory, '../core'),        // 向上一级目录下的core
    ];
    
    bool foundCorePath = false;
    
    // 验证Python环境
    try {
      Logger.log('ImageProcessingService', 'initializeService', '验证Python环境', LogLevel.info);
      final result = await Process.run(_pythonPath, ['--version']);
      
      if (result.exitCode != 0) {
        Logger.log('ImageProcessingService', 'initializeService', 'Python路径验证失败: ${result.stderr}', LogLevel.error);
        throw Exception('Python路径验证失败: ${result.stderr}');
      }
      
      Logger.log('ImageProcessingService', 'initializeService', 'Python版本: ${result.stdout}', LogLevel.info);
    } catch (e) {
      Logger.log('ImageProcessingService', 'initializeService', 'Python路径验证异常: $e', LogLevel.error);
      rethrow;
    }
    
    // 尝试每个可能的核心目录路径
    for (final corePath in possibleCorePaths) {
      final faceMeshProcessorPath = path.join(corePath, 'face_mesh_processor.py');
      final landmarkVisualizerPath = path.join(corePath, 'landmark_visualizer.py');
      final parameterMappingPath = path.join(corePath, 'parameter_mapping.py');
      
      // 检查所有必需的Python文件是否存在
      if (File(faceMeshProcessorPath).existsSync() && 
          File(landmarkVisualizerPath).existsSync() && 
          File(parameterMappingPath).existsSync()) {
        _corePath = corePath;
        foundCorePath = true;
        Logger.log('ImageProcessingService', 'initializeService', '核心目录路径: $_corePath', LogLevel.info);
        break;
      }
    }
    
    if (!foundCorePath) {
      Logger.log('ImageProcessingService', 'initializeService', '无法找到有效的核心目录路径', LogLevel.error);
      throw Exception('无法找到有效的核心目录路径，请确保所有必需的Python模块文件存在');
    }
    
    // 验证核心Python文件
    final faceMeshProcessorPath = path.join(_corePath, 'face_mesh_processor.py');
    final landmarkVisualizerPath = path.join(_corePath, 'landmark_visualizer.py');
    final parameterMappingPath = path.join(_corePath, 'parameter_mapping.py');
    
    // 再次确认所有文件存在
    if (!File(faceMeshProcessorPath).existsSync()) {
      Logger.log('ImageProcessingService', 'initializeService', 'face_mesh_processor.py 不存在', LogLevel.error);
      throw Exception('核心Python模块 face_mesh_processor.py 不存在');
    }
    
    if (!File(landmarkVisualizerPath).existsSync()) {
      Logger.log('ImageProcessingService', 'initializeService', 'landmark_visualizer.py 不存在', LogLevel.error);
      throw Exception('核心Python模块 landmark_visualizer.py 不存在');
    }
    
    if (!File(parameterMappingPath).existsSync()) {
      Logger.log('ImageProcessingService', 'initializeService', 'parameter_mapping.py 不存在', LogLevel.error);
      throw Exception('核心Python模块 parameter_mapping.py 不存在');
    }
    
    Logger.log('ImageProcessingService', 'initializeService', '图像处理服务初始化成功', LogLevel.info);
  }
  
  /// 处理图像
/// 
/// [imagePath] 图像路径
/// [options] 处理选项
/// 
/// 返回处理结果
Future<List<FeaturePoint>> processImage(String imagePath, {Map<String, dynamic>? options}) async {
  Logger.i('图像处理服务', '开始处理图像');
  Logger.d('图像处理服务', '  • 路径: $imagePath');
  Logger.d('图像处理服务', '  • 选项: $options');
  
  final stopwatch = Stopwatch()..start();
  
  try {
    // 如果是测试模式，直接返回测试数据
    if (options?.containsKey('test_mode') == true && options?['test_mode'] == true) {
      Logger.i('图像处理服务', '使用测试模式');
      final points = _generateTestPoints();
      Logger.i('图像处理服务', '测试数据生成完成');
      Logger.d('图像处理服务', '  • 特征点数量: ${points.length}');
      return points;
    }
    
    // 验证文件存在
    final imageFile = File(imagePath);
    if (!await imageFile.exists()) {
      Logger.e('图像处理服务', '图像文件不存在');
      return [];
    }
    
    // 获取图像尺寸
    final size = await getImageSize(imageFile);
    if (size == null) {
      Logger.e('图像处理服务', '无法获取图像尺寸');
      return [];
    }
    
    // 调用Python脚本处理图像
    final result = await _runPythonModule('face_mesh_processor.py', {
      'image_path': imagePath,
      'options': options ?? {
        'min_detection_confidence': 0.7,
        'min_tracking_confidence': 0.7,
        'max_num_faces': 1,
      },
    });
    
    if (result == null || !result.containsKey('landmarks')) {
      Logger.e('图像处理服务', '未能获取特征点数据');
      return [];
    }
    
    // 解析特征点数据
    final landmarksData = result['landmarks'] as List<dynamic>;
    final List<FeaturePoint> points = [];
    
    // 转换为FeaturePoint对象
    for (var landmark in landmarksData) {
      if (landmark is Map) {
        try {
          final index = landmark.containsKey('index') ? 
              int.parse(landmark['index'].toString()) : points.length;
          
          points.add(FeaturePoint(
            index: index,
            x: (landmark['x'] as num).toDouble(),
            y: (landmark['y'] as num).toDouble(),
            z: landmark['z'] != null ? (landmark['z'] as num).toDouble() : 0.0,
            visibility: landmark['visibility'] != null ? (landmark['visibility'] as num).toDouble() : 1.0,
            confidence: landmark['confidence'] != null ? (landmark['confidence'] as num).toDouble() : 1.0,
          ));
        } catch (e) {
          Logger.w('图像处理服务', '解析特征点数据异常: $e');
        }
      } else if (landmark is List) {
        try {
          points.add(FeaturePoint(
            index: points.length,
            x: landmark[0].toDouble(),
            y: landmark[1].toDouble(),
            z: landmark.length > 2 ? landmark[2].toDouble() : 0.0,
            visibility: landmark.length > 3 ? landmark[3].toDouble() : 1.0,
            confidence: landmark.length > 4 ? landmark[4].toDouble() : 1.0,
          ));
        } catch (e) {
          Logger.w('图像处理服务', '解析特征点数据异常: $e');
        }
      }
    }
    
    Logger.i('图像处理服务', '图像处理完成');
    Logger.d('图像处理服务', '  • 特征点数量: ${points.length}');
    
    return points;
  } catch (e) {
    Logger.e('图像处理服务', '处理图像时发生错误:');
    Logger.e('图像处理服务', '  • 错误信息: $e');
    return [];
  }
}

  /// 生成测试特征点数据
  List<FeaturePoint> _generateTestPoints() {
    Logger.i('图像处理服务', '生成测试特征点');
    
    final List<FeaturePoint> points = [];
    
    // 1. 面部轮廓区域
    final faceContourPoints = [
      // 下巴
      152, 175, 371,
      // 左侧下颜线
      162, 21, 54, 103, 67, 109,
      // 右侧下颜线
      389, 251, 284, 333, 297, 338,
      // 左侧颜骨
      123, 147, 187, 207, 216,
      // 右侧颜骨
      352, 376, 411, 427, 436,
      // 左侧太阳穴
      139, 71, 68, 104,
      // 右侧太阳穴
      368, 301, 298, 332
    ];
    
    // 2. 眼部区域
    final leftEyePoints = List.generate(8, (i) => 36 + i);   // [36-43]
    final rightEyePoints = List.generate(8, (i) => 44 + i);  // [44-51]
    
    // 3. 嘴部区域
    final mouthPoints = [48, 49, 53, 54, 61, 65];  // 嘴角调整
    final lipsPoints = [50, 51, 52, 55, 56, 57, 58, 59, 60, 62, 63, 64];  // 唇形调整
    
    // 4. 鼻部区域
    final nosePoints = [
      6, 197, 195, 5,  // 鼻梁点
      4, 1,            // 鼻尖点
      2,               // 鼻底点
      8, 9,            // 鼻翼点
      3, 7             // 鼻翼边缘点
    ];
    
    // 5. 抗衬老区域
    final antiAgingPoints = [
      // 法令纹区域
      92, 322,   // 左右法令纹上点
      129, 358,  // 左右法令纹中点
      166, 395,  // 左右法令纹下点
      167, 393,  // 法令纹主要点对（误差3.12像素）
      169, 394,  // 法令纹主要点对（误差5.35像素）
      
      // 皱纹区域
      113, 342,  // 左右皱纹中心
      151,       // 中心线点，无对称点
      
      // 额头区域
      71, 301,   // 中心点
      68, 298,   // 上部点
      104, 333,  // 中颊点对1
      103, 332,  // 中颊点对2
      139, 368,  // 太阳穴
      69, 299    // 边缘
    ];
    
    // 生成所有特征点
    final allPoints = [
      ...faceContourPoints,
      ...leftEyePoints,
      ...rightEyePoints,
      ...mouthPoints,
      ...lipsPoints,
      ...nosePoints,
      ...antiAgingPoints,
    ];
    
    // 设置特征点坐标
    for (var index in allPoints) {
      points.add(FeaturePoint(
        index: index,
        x: 100 + (index % 10) * 50,  // 横向分布
        y: 100 + (index ~/ 10) * 50, // 纵向分布
        z: 0,
        visibility: 1.0,  // 所有点都可见
        confidence: 1.0,  // 所有点的置信度都为1.0
      ));
    }
    
    Logger.i('图像处理服务', '测试特征点生成完成');
    Logger.d('图像处理服务', '  • 特征点数量: ${points.length}');
    
    return points;
  }

  /// 可视化特征点
  /// 
  /// [imagePath] 输入图像路径
  /// [outputPath] 输出图像路径
  /// [landmarks] 特征点数据
  /// 
  /// 返回处理结果
  Future<String> visualizeLandmarks(String imagePath, String outputPath, List<FeaturePoint> landmarks) async {
    Logger.log('ImageProcessingService', 'visualizeLandmarks', '开始可视化 | 特征点: ${landmarks.length}个', LogLevel.info);
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // 检查输入文件是否存在
      if (!File(imagePath).existsSync()) {
        Logger.log('ImageProcessingService', 'visualizeLandmarks', '输入图像不存在: $imagePath', LogLevel.error);
        throw Exception('特征点可视化失败: 输入图像不存在');
      }
      
      // 检查特征点列表是否为空
      if (landmarks.isEmpty) {
        Logger.log('ImageProcessingService', 'visualizeLandmarks', '特征点列表为空', LogLevel.warning);
        // 生成测试特征点
        landmarks = _generateTestPoints();
        Logger.log('ImageProcessingService', 'visualizeLandmarks', '使用测试特征点继续处理', LogLevel.info);
      }
      
      // 转换特征点数据为JSON格式
      final List<Map<String, dynamic>> landmarksJson = landmarks.map((point) => {
        'index': point.index,
        'x': point.x,
        'y': point.y,
        'z': point.z,
        'visibility': point.visibility,
        'confidence': point.confidence,
      }).toList();
      
      Logger.log('ImageProcessingService', 'visualizeLandmarks', '将调用landmark_visualizer.py脚本绘制${landmarksJson.length}个特征点', LogLevel.debug);
      
      // 调用特征点可视化
      // 检查参数格式
      final params = {
        'input': imagePath,
        'output': outputPath,
        'landmarks': landmarksJson,
      };
      
      // 输出完整的参数以便于调试
      Logger.log('ImageProcessingService', 'visualizeLandmarks', '参数详情: ${jsonEncode(params)}', LogLevel.debug);
      
      final result = await _runPythonModule(
        'landmark_visualizer.py',
        params,
      );
      
      // 检查结果是否为null
      if (result == null) {
        Logger.log('ImageProcessingService', 'visualizeLandmarks', '特征点可视化返回null结果', LogLevel.error);
        throw Exception('特征点可视化失败: 返回null结果');
      }
      
      Logger.log('ImageProcessingService', 'visualizeLandmarks', '特征点可视化结果状态: ${result['status']}', LogLevel.debug);
      Logger.log('ImageProcessingService', 'visualizeLandmarks', '特征点可视化完整结果: ${jsonEncode(result)}', LogLevel.debug);
      
      if (result['status'] != 'success') {
        Logger.log('ImageProcessingService', 'visualizeLandmarks', '特征点可视化失败: ${result['message']}', LogLevel.error);
        throw Exception('特征点可视化失败: ${result['message']}');
      }
      
      // 检查输出文件是否存在
      final outputFile = File(outputPath);
      if (!outputFile.existsSync()) {
        Logger.log('ImageProcessingService', 'visualizeLandmarks', '输出文件不存在: $outputPath', LogLevel.error);
        throw Exception('特征点可视化失败: 输出文件不存在');
      }
      
      // 检查输出文件大小
      final fileSize = outputFile.lengthSync();
      Logger.log('ImageProcessingService', 'visualizeLandmarks', '输出文件大小: ${fileSize} 字节', LogLevel.debug);
      
      if (fileSize <= 0) {
        Logger.log('ImageProcessingService', 'visualizeLandmarks', '输出文件为空: $outputPath', LogLevel.error);
        throw Exception('特征点可视化失败: 输出文件为空');
      }
      
      stopwatch.stop();
      Logger.log('ImageProcessingService', 'visualizeLandmarks', '特征点可视化完成，耗时: ${stopwatch.elapsedMilliseconds}ms', LogLevel.info);
      Logger.log('ImageProcessingService', 'visualizeLandmarks', '输出文件路径: $outputPath', LogLevel.info);
      
      return outputPath;
    } catch (e) {
      stopwatch.stop();
      Logger.log('ImageProcessingService', 'visualizeLandmarks', '特征点可视化异常: $e', LogLevel.error);
      rethrow;
    }
  }
  
  /// 获取区域配置
  /// 
  /// [areaName] 区域名称
  /// 
  /// 返回区域配置
  Future<Map<String, dynamic>> getAreaConfig(String areaName) async {
    Logger.log('ImageProcessingService', 'getAreaConfig', '获取区域配置: $areaName', LogLevel.info);
    
    try {
      // 调用参数映射模块
      final result = await _runPythonModule(
        'parameter_mapping.py',
        {
          'action': 'get_area_config',
          'area_name': areaName,
        },
      );
      
      // 检查结果是否为null
      if (result == null) {
        Logger.log('ImageProcessingService', 'getAreaConfig', '获取区域配置返回null结果', LogLevel.error);
        throw Exception('获取区域配置失败: 返回null结果');
      }
      
      Logger.log('ImageProcessingService', 'getAreaConfig', '获取区域配置结果状态: ${result['status']}', LogLevel.debug);
      
      if (result['status'] != 'success') {
        Logger.log('ImageProcessingService', 'getAreaConfig', '获取区域配置失败: ${result['message']}', LogLevel.error);
        throw Exception('获取区域配置失败: ${result['message']}');
      }
      
      // 解析区域配置
      final areaConfig = result['area_config'];
      
      Logger.log('ImageProcessingService', 'getAreaConfig', '获取区域配置成功', LogLevel.info);
      
      return areaConfig;
    } catch (e) {
      Logger.log('ImageProcessingService', 'getAreaConfig', '获取区域配置异常: $e', LogLevel.error);
      rethrow;
    }
  }
  
  /// 获取参数配置
  /// 
  /// [areaName] 区域名称
  /// [paramName] 参数名称
  /// 
  /// 返回参数配置
  Future<Map<String, dynamic>> getParameterConfig(String areaName, String paramName) async {
    Logger.log('ImageProcessingService', 'getParameterConfig', '获取参数配置: $areaName.$paramName', LogLevel.info);
    
    try {
      // 调用参数映射模块
      final result = await _runPythonModule(
        'parameter_mapping.py',
        {
          'action': 'get_parameter_config',
          'area_name': areaName,
          'param_name': paramName,
        },
      );
      
      // 检查结果是否为null
      if (result == null) {
        Logger.log('ImageProcessingService', 'getParameterConfig', '获取参数配置返回null结果', LogLevel.error);
        throw Exception('获取参数配置失败: 返回null结果');
      }
      
      Logger.log('ImageProcessingService', 'getParameterConfig', '获取参数配置结果状态: ${result['status']}', LogLevel.debug);
      
      if (result['status'] != 'success') {
        Logger.log('ImageProcessingService', 'getParameterConfig', '获取参数配置失败: ${result['message']}', LogLevel.error);
        throw Exception('获取参数配置失败: ${result['message']}');
      }
      
      // 解析参数配置
      final paramConfig = result['parameter_config'];
      
      Logger.log('ImageProcessingService', 'getParameterConfig', '获取参数配置成功: ${paramConfig['display_name']}', LogLevel.info);
      
      return paramConfig;
    } catch (e) {
      Logger.log('ImageProcessingService', 'getParameterConfig', '获取参数配置异常: $e', LogLevel.error);
      rethrow;
    }
  }
  
  /// 运行Python模块
  /// 
  /// [moduleName] 模块名称
  /// [args] 参数
  /// 
  /// 返回运行结果
  Future<Map<String, dynamic>?> _runPythonModule(String moduleName, Map<String, dynamic> args) async {
    Logger.log('ImageProcessingService', '_runPythonModule', '运行Python模块: $moduleName', LogLevel.debug);
    Logger.log('ImageProcessingService', '_runPythonModule', '参数: ${jsonEncode(args)}', LogLevel.debug);
    
    // 创建一个默认的错误响应
    final Map<String, dynamic> errorResponse = {
      'status': 'error',
      'message': '未知错误',
      'landmarks': <dynamic>[],  // 确保始终有一个空的landmarks列表
      'image_size': {'width': 0.0, 'height': 0.0},  // 确保始终有图像尺寸信息
    };
    
    try {
      final modulePath = path.join(_corePath, moduleName);
      
      if (!File(modulePath).existsSync()) {
        final errorMsg = 'Python模块不存在: $modulePath';
        Logger.log('ImageProcessingService', '_runPythonModule', errorMsg, LogLevel.error);
        errorResponse['message'] = errorMsg;
        return errorResponse;
      }
      
      // 将参数转换为JSON字符串
      final argsJson = jsonEncode(args);
      
      // 构建命令行参数
      // 构建命令行参数
      List<String> cmdArgs;
      
      // 根据不同的脚本使用不同的参数格式
      if (moduleName == 'landmark_visualizer.py') {
        // 对于 landmark_visualizer.py，直接传递必要的参数
        final inputPath = args['input'] as String;
        final outputPath = args['output'] as String;
        final landmarks = args['landmarks'] as List;
        
        // 将特征点转换为适合的格式
        final landmarksJson = jsonEncode(landmarks);
        
        // 检查输入路径是否存在
        final inputFile = File(inputPath);
        if (!inputFile.existsSync()) {
          Logger.log('ImageProcessingService', '_runPythonModule', '输入文件不存在: $inputPath', LogLevel.error);
          throw Exception('输入文件不存在: $inputPath');
        }
        
        // 创建输出目录（如果不存在）
        final outputDir = Directory(path.dirname(outputPath));
        if (!outputDir.existsSync()) {
          outputDir.createSync(recursive: true);
        }
        
        cmdArgs = [
          modulePath,
          '--input', inputPath,
          '--output', outputPath,
          '--landmarks', landmarksJson
        ];
        
        Logger.log('ImageProcessingService', '_runPythonModule', '使用直接参数方式调用landmark_visualizer.py', LogLevel.debug);
        Logger.log('ImageProcessingService', '_runPythonModule', '输入路径: $inputPath', LogLevel.debug);
        Logger.log('ImageProcessingService', '_runPythonModule', '输出路径: $outputPath', LogLevel.debug);
        Logger.log('ImageProcessingService', '_runPythonModule', '特征点数量: ${landmarks.length}', LogLevel.debug);
      } else {
        // 所有脚本都使用JSON参数格式
        // 将JSON字符串进行转义，确保在命令行中正确传递
        cmdArgs = [
          modulePath,
          '--params',
          argsJson,
        ];
      }
      
      Logger.log('ImageProcessingService', '_runPythonModule', '执行Python命令: $_pythonPath ${cmdArgs.join(' ')}', LogLevel.debug);
      
      // 执行Python脚本
      Logger.log('ImageProcessingService', '_runPythonModule', '开始执行Python脚本: $moduleName', LogLevel.info);
      final result = await Process.run(_pythonPath, cmdArgs);
      
      Logger.log('ImageProcessingService', '_runPythonModule', '执行结果 - 退出码: ${result.exitCode}', LogLevel.debug);
      
      // 输出完整的stdout和stderr以便于调试
      final stdoutContent = result.stdout as String;
      final stderrContent = result.stderr as String;
      
      if (stdoutContent.isNotEmpty) {
        Logger.log('ImageProcessingService', '_runPythonModule', '执行结果 - 标出:\n$stdoutContent', LogLevel.debug);
      } else {
        Logger.log('ImageProcessingService', '_runPythonModule', '执行结果 - 标出: <空>', LogLevel.debug);
      }
      
      if (stderrContent.isNotEmpty) {
        Logger.log('ImageProcessingService', '_runPythonModule', '执行结果 - 错误:\n$stderrContent', LogLevel.debug);
      } else {
        Logger.log('ImageProcessingService', '_runPythonModule', '执行结果 - 错误: <空>', LogLevel.debug);
      }
      
      if (result.exitCode != 0) {
        final errorMsg = '执行Python模块失败: ${result.stderr}';
        Logger.log('ImageProcessingService', '_runPythonModule', errorMsg, LogLevel.error);
        errorResponse['message'] = errorMsg;
        return errorResponse;
      }
      
      // 检查输出是否为空
      final resultOutput = result.stdout as String;
      if (resultOutput.trim().isEmpty) {
        final errorMsg = 'Python模块返回空结果';
        Logger.log('ImageProcessingService', '_runPythonModule', errorMsg, LogLevel.error);
        errorResponse['message'] = errorMsg;
        return errorResponse;
      }
      
      // 解析JSON结果
      try {
        final dynamic jsonResult = jsonDecode(resultOutput);
        
        // 确保返回结果是一个有效的Map
        if (jsonResult == null) {
          final errorMsg = 'Python模块返回空结果';
          Logger.log('ImageProcessingService', '_runPythonModule', errorMsg, LogLevel.error);
          errorResponse['message'] = errorMsg;
          return errorResponse;
        }
        
        // 处理非Map类型的结果
        if (jsonResult is! Map) {
          final errorMsg = 'Python模块返回结果不是有效的JSON对象: $jsonResult';
          Logger.log('ImageProcessingService', '_runPythonModule', errorMsg, LogLevel.error);
          errorResponse['message'] = errorMsg;
          errorResponse['data'] = jsonResult.toString();
          return errorResponse;
        }
        
        // 将动态Map转换为Map<String, dynamic>
        final Map<String, dynamic> result = {};
        (jsonResult as Map).forEach((key, value) {
          if (key is String) {
            result[key] = value;
          } else {
            result[key.toString()] = value;
          }
        });
        
        // 确保结果包含所有必要的字段
        if (!result.containsKey('status')) {
          result['status'] = 'success';
        }
        
        if (!result.containsKey('landmarks')) {
          result['landmarks'] = <dynamic>[];
        }
        
        if (!result.containsKey('image_size')) {
          result['image_size'] = {'width': 0.0, 'height': 0.0};
        }
        
        Logger.log('ImageProcessingService', '_runPythonModule', '执行Python模块成功', LogLevel.debug);
        return result;
      } catch (e) {
        final errorMsg = '解析Python模块结果失败: $e';
        Logger.log('ImageProcessingService', '_runPythonModule', errorMsg, LogLevel.error);
        errorResponse['message'] = errorMsg;
        errorResponse['stdout'] = stdout;
        return errorResponse;
      }
    } catch (e) {
      final errorMsg = '执行Python模块异常: $e';
      Logger.log('ImageProcessingService', '_runPythonModule', errorMsg, LogLevel.error);
      errorResponse['message'] = errorMsg;
      return errorResponse;
    }
  }
  
  /// 更新特征点缓存
  /// 
  /// [result] 特征点检测结果
  void _updateLandmarksCache(Map<String, dynamic>? result) {
    Logger.log('ImageProcessingService', '_updateLandmarksCache', '更新特征点缓存', LogLevel.debug);
    
    // 确保缓存不为null
    if (result == null) {
      Logger.log('ImageProcessingService', '_updateLandmarksCache', '结果为null，不更新缓存', LogLevel.warning);
      return;
    }
    
    _landmarksCache = result;
    
    // 更新图像尺寸
    if (result.containsKey('image_size') && result['image_size'] != null) {
      try {
        final imageSize = result['image_size'];
        
        // 检查image_size是否为Map类型
        if (imageSize is Map) {
          if (imageSize.containsKey('width') && imageSize.containsKey('height') && 
              imageSize['width'] != null && imageSize['height'] != null) {
            _imageWidth = (imageSize['width'] as num).toDouble();
            _imageHeight = (imageSize['height'] as num).toDouble();
            Logger.log('ImageProcessingService', '_updateLandmarksCache', '更新图像尺寸: ${_imageWidth}x${_imageHeight}', LogLevel.debug);
          } else {
            Logger.log('ImageProcessingService', '_updateLandmarksCache', 'image_size缺少width或height字段', LogLevel.warning);
          }
        } else if (imageSize is List && imageSize.length >= 2 && 
                  imageSize[0] != null && imageSize[1] != null) {
          // 如果image_size是数组格式 [width, height]
          _imageWidth = (imageSize[0] as num).toDouble();
          _imageHeight = (imageSize[1] as num).toDouble();
          Logger.log('ImageProcessingService', '_updateLandmarksCache', '更新图像尺寸: ${_imageWidth}x${_imageHeight}', LogLevel.debug);
        } else {
          Logger.log('ImageProcessingService', '_updateLandmarksCache', 'image_size格式不支持: $imageSize', LogLevel.warning);
        }
      } catch (e) {
        Logger.log('ImageProcessingService', '_updateLandmarksCache', '解析image_size异常: $e', LogLevel.warning);
      }
    } else {
      Logger.log('ImageProcessingService', '_updateLandmarksCache', '结果中不包含image_size字段或为null', LogLevel.debug);
    }
  }
  
  /// 应用对称性规则
  /// 
  /// 根据区域类型应用对称性规则，生成新的特征点
  /// 
  /// [points] 原始特征点列表
  /// [areaType] 区域类型
  /// 
  /// 返回调整后的特征点列表
  Future<List<Map<String, dynamic>>> applySymmetry(List<Map<String, dynamic>> points, String areaType) async {
    Logger.log('ImageProcessingService', 'applySymmetry', '开始应用对称性规则: $areaType', LogLevel.info);
    
    try {
      // 如果图像宽度未设置，返回原始点
      if (_imageWidth <= 0) {
        Logger.log('ImageProcessingService', 'applySymmetry', '图像尺寸未设置，无法应用对称性规则', LogLevel.warning);
        return points;
      }
      
      // 如果点为空，返回空列表
      if (points.isEmpty) {
        Logger.log('ImageProcessingService', 'applySymmetry', '原始点列表为空，无法应用对称性规则', LogLevel.warning);
        return points;
      }
      
      // 计算图像中心线
      final centerX = _imageWidth / 2;
      
      // 创建新的点列表，包含原始点
      final List<Map<String, dynamic>> adjustedPoints = List.from(points);
      
      // 根据区域类型应用不同的对称性规则
      switch (areaType) {
        case 'left_eyebrow':
        case 'right_eyebrow':
        case 'left_eye':
        case 'right_eye':
        case 'eyes':
          // 对于眼眉和眼睛，应用水平对称
          _applyHorizontalSymmetry(points, adjustedPoints, centerX);
          break;
        case 'lips':
        case 'mouth':
          // 对于嘴唇和嘴巴，应用水平对称
          _applyHorizontalSymmetry(points, adjustedPoints, centerX);
          break;
        case 'nose':
          // 对于鼻子，应用水平对称
          _applyHorizontalSymmetry(points, adjustedPoints, centerX);
          break;
        case 'face_contour':
          // 对于面部轮廓，应用水平对称
          _applyHorizontalSymmetry(points, adjustedPoints, centerX);
          break;
        case 'left_cheek':
        case 'right_cheek':
          // 对于脸颈，应用水平对称
          _applyHorizontalSymmetry(points, adjustedPoints, centerX);
          break;
        default:
          // 其他区域不应用对称性规则
          Logger.log('ImageProcessingService', 'applySymmetry', '区域 $areaType 不需要应用对称性规则', LogLevel.info);
          break;
      }
      
      Logger.log('ImageProcessingService', 'applySymmetry', '应用对称性规则完成，生成 ${adjustedPoints.length} 个点', LogLevel.info);
      return adjustedPoints;
    } catch (e) {
      Logger.log('ImageProcessingService', 'applySymmetry', '应用对称性规则异常: $e', LogLevel.error);
      // 发生异常时返回原始点
      return points;
    }
  }
  
  /// 应用水平对称
  /// 
  /// 将原始点列表中的点水平对称，生成新的点并添加到调整后的点列表中
  /// 
  /// [originalPoints] 原始点列表
  /// [adjustedPoints] 调整后的点列表
  /// [centerX] 图像中心线的X坐标
  void _applyHorizontalSymmetry(List<Map<String, dynamic>> originalPoints, List<Map<String, dynamic>> adjustedPoints, double centerX) {
    // 记录已存在的点索引，避免重复
    final existingIndices = adjustedPoints.map((p) => p['index'] as int).toSet();
    
    // 对每个原始点生成对称点
    for (var point in originalPoints) {
      // 计算对称点的坐标
      final symmetricX = 2 * centerX - (point['x'] as double);
      
      // 创建新的对称点，使用原始点的索引 + 1000 以区分
      final symmetricIndex = (point['index'] as int) + 1000;
      
      // 如果对称点不存在，添加到调整后的点列表中
      if (!existingIndices.contains(symmetricIndex)) {
        adjustedPoints.add({
          'index': symmetricIndex,
          'x': symmetricX,
          'y': point['y'],
          'z': point['z'],
          'visibility': point['visibility'],
          'confidence': point['confidence']
        });
      }
    }
  }
}
