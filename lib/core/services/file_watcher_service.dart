import 'dart:io';
import 'dart:async';
import 'package:path/path.dart' as path;
import '../../utils/logger.dart';
import 'file_sync_service.dart';

/// 文件监听服务
/// 
/// 监听关键文件的变更，当文件发生变化时自动同步到运行环境
class FileWatcherService {
  // 单例实例
  static final FileWatcherService _instance = FileWatcherService._internal();
  
  // 工厂构造函数
  factory FileWatcherService() {
    return _instance;
  }
  
  // 私有构造函数
  FileWatcherService._internal();
  
  // 文件监听器列表
  final List<StreamSubscription<FileSystemEvent>> _watchers = [];
  
  // 监听状态
  bool _isWatching = false;
  
  /// 启动文件监听
  /// 
  /// 开始监听关键文件的变更
  Future<void> startWatching() async {
    if (_isWatching) {
      log('文件监听服务已经在运行');
      return;
    }
    
    try {
      // 获取项目根目录
      final projectRoot = Directory.current.path;
      
      // 监听Dart配置文件
      _watchFile(
        path.join(projectRoot, 'lib', 'core', 'config', 'deformation_config.dart'),
        onModified: (file) async {
          log('检测到Dart配置文件变更: ${file.path}');
          await FileSyncService().syncConfigFiles();
        }
      );
      
      // 监听Python配置文件
      _watchFile(
        path.join(projectRoot, 'core', 'deformation_config.py'),
        onModified: (file) async {
          log('检测到Python配置文件变更: ${file.path}');
          await FileSyncService().syncConfigFiles();
        }
      );
      
      // 监听core目录下所有Python文件
      _watchDirectory(
        path.join(projectRoot, 'core'),
        extension: '.py',
        onModified: (file) async {
          log('检测到Python文件变更: ${file.path}');
          await FileSyncService().syncPythonScripts();
        }
      );
      
      _isWatching = true;
      log('文件监听服务启动成功，正在监听关键文件变更');
    } catch (e) {
      log('启动文件监听服务失败: $e');
    }
  }
  
  /// 停止文件监听
  /// 
  /// 停止所有文件监听器
  void stopWatching() {
    if (!_isWatching) {
      log('文件监听服务未在运行');
      return;
    }
    
    try {
      for (var watcher in _watchers) {
        watcher.cancel();
      }
      _watchers.clear();
      _isWatching = false;
      log('文件监听服务已停止');
    } catch (e) {
      log('停止文件监听服务失败: $e');
    }
  }
  
  /// 监听单个文件
  /// 
  /// [filePath] 文件路径
  /// [onModified] 文件修改时的回调函数
  void _watchFile(String filePath, {required Function(File) onModified}) {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        log('文件不存在，无法监听: $filePath');
        return;
      }
      
      final watcher = file.watch().listen((event) {
        if (event.type == FileSystemEvent.modify) {
          onModified(file);
        }
      });
      
      _watchers.add(watcher);
      log('开始监听文件: $filePath');
    } catch (e) {
      log('监听文件失败: $filePath, 错误: $e');
    }
  }
  
  /// 监听目录中的所有特定扩展名文件
  /// 
  /// [directoryPath] 目录路径
  /// [extension] 文件扩展名，例如 '.py'
  /// [onModified] 文件修改时的回调函数
  void _watchDirectory(String directoryPath, {required String extension, required Function(File) onModified}) {
    try {
      final directory = Directory(directoryPath);
      if (!directory.existsSync()) {
        log('目录不存在，无法监听: $directoryPath');
        return;
      }
      
      // 监听目录中的所有文件变更
      final watcher = directory.watch(recursive: true).listen((event) {
        if (event.type == FileSystemEvent.modify) {
          final file = File(event.path);
          
          // 只处理特定扩展名的文件
          if (file.path.endsWith(extension)) {
            onModified(file);
          }
        }
      });
      
      _watchers.add(watcher);
      log('开始监听目录中的$extension文件: $directoryPath');
    } catch (e) {
      log('监听目录失败: $directoryPath, 错误: $e');
    }
  }
  
  /// 输出日志
  /// 
  /// [message] 日志信息
  void log(String message) {
    Logger.log('FileWatcherService', 'watch', message);
  }
}
