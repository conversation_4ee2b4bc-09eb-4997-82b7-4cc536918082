import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/logger.dart';

/// 变形工具类
/// 提供变形相关的常量和工具函数
class DeformationUtils {
  static const String logTag = 'DeformationUtils';
  
  // 区域颜色映射
  static final Map<String, Color> areaColors = {
    'face_contour': Colors.purple.withOpacity(0.5),
    'nose': Colors.red.withOpacity(0.5),
    'eyes': Colors.blue.withOpacity(0.5),
    'lips': Colors.pink.withOpacity(0.5),
    'anti_aging': Colors.orange.withOpacity(0.5),
  };
  
  // 预定义的区域位置映射
  static final Map<String, Map<String, Rect>> predefinedAreas = {
    'face_contour': {
      'contour_tighten': Rect.fromLTRB(0.15, 0.15, 0.85, 0.85),    // 整个面部
      'chin_adjust': Rect.fromLTRB(0.35, 0.75, 0.65, 0.95),       // 下巴区域
      'cheekbone_adjust': Rect.fromLTRB(0.15, 0.35, 0.85, 0.6),   // 颧骨区域
      'face_shape': Rect.fromLTRB(0.15, 0.15, 0.85, 0.85),         // 整个面部
    },
    'nose': {
      'bridge_height': Rect.fromLTRB(0.45, 0.35, 0.55, 0.5),    // 鼻梁区域
      'tip_adjust': Rect.fromLTRB(0.45, 0.45, 0.55, 0.55),       // 鼻尖区域
      'nostril_width': Rect.fromLTRB(0.4, 0.45, 0.6, 0.55),  // 鼻翼区域
      'base_height': Rect.fromLTRB(0.42, 0.5, 0.58, 0.6),        // 鼻基区域
    },
    'eyes': {
      'double_fold': Rect.fromLTRB(0.25, 0.28, 0.75, 0.38),       // 双眼区域
      'canthal_tilt': Rect.fromLTRB(0.25, 0.28, 0.75, 0.38),      // 眼角区域
      'eye_bag_removal': Rect.fromLTRB(0.25, 0.32, 0.75, 0.42),   // 眼袋区域
      'outer_corner_lift': Rect.fromLTRB(0.25, 0.28, 0.75, 0.38), // 眼尾区域
    },
    'lips': {
      'lip_shape': Rect.fromLTRB(0.35, 0.65, 0.65, 0.75),         // 嘴唇区域
      'mouth_corner': Rect.fromLTRB(0.35, 0.65, 0.65, 0.75),      // 嘴角区域
    },
    'anti_aging': {
      'nasolabial_folds': Rect.fromLTRB(0.35, 0.5, 0.65, 0.65),   // 法令纹区域
      'wrinkle_removal': Rect.fromLTRB(0.25, 0.2, 0.75, 0.8),  // 皱纹区域
      'forehead_fullness': Rect.fromLTRB(0.25, 0.1, 0.75, 0.25),// 额头区域
      'facial_firmness': Rect.fromLTRB(0.2, 0.2, 0.8, 0.8),     // 面部紧致
    },
  };
  
  // 默认图像尺寸 (用于在实际尺寸未设置时提供合理的默认值)
  static const Size defaultImageSize = Size(400, 600);
  
  /// 检查坐标是否有效
  static bool isValidCoordinate(double x, double y) {
    return !x.isNaN && !y.isNaN && x.isFinite && y.isFinite;
  }
  
  /// 获取区域颜色
  static Color getAreaColor(String areaType) {
    return areaColors[areaType] ?? Colors.transparent;
  }
  
  /// 记录日志
  static void log(String tag, String method, String message) {
    Logger.i(tag, '$method: $message');
  }
  
  /// 记录流程开始日志
  static void flowStart(String tag, String flowName) {
    Logger.flowStart(tag, flowName);
  }
  
  /// 记录流程步骤日志
  static void flow(String tag, String flowName, String step) {
    Logger.flow(tag, flowName, step);
  }
  
  /// 记录流程结束日志
  static void flowEnd(String tag, String flowName) {
    Logger.flowEnd(tag, flowName);
  }
  
  /// 记录流程错误日志
  static void flowError(String tag, String flowName, String error) {
    Logger.flowError(tag, flowName, error);
  }

  /// 使用双线性插值获取图像中指定坐标的颜色
  /// 
  /// 参数:
  /// - image: 源图像
  /// - x: 浮点数x坐标
  /// - y: 浮点数y坐标
  /// 
  /// 返回:
  /// - 插值后的颜色
  static Color getColorBilinear(ui.Image image, double x, double y) {
    // 确保坐标在图像范围内
    x = x.clamp(0, image.width - 1.0);
    y = y.clamp(0, image.height - 1.0);
    
    // 计算四个最近的整数坐标点
    final int x0 = x.floor();
    final int y0 = y.floor();
    final int x1 = math.min(x0 + 1, image.width - 1);
    final int y1 = math.min(y0 + 1, image.height - 1);
    
    // 计算插值权重
    final double wx = x - x0;
    final double wy = y - y0;
    final double wx1 = 1 - wx;
    final double wy1 = 1 - wy;
    
    // 获取四个角点的颜色
    final Color c00 = getColorAtPixel(image, x0, y0);
    final Color c10 = getColorAtPixel(image, x1, y0);
    final Color c01 = getColorAtPixel(image, x0, y1);
    final Color c11 = getColorAtPixel(image, x1, y1);
    
    // 双线性插值计算最终颜色
    final int r = (wx1 * wy1 * c00.red + 
                  wx * wy1 * c10.red + 
                  wx1 * wy * c01.red + 
                  wx * wy * c11.red).round();
    
    final int g = (wx1 * wy1 * c00.green + 
                  wx * wy1 * c10.green + 
                  wx1 * wy * c01.green + 
                  wx * wy * c11.green).round();
    
    final int b = (wx1 * wy1 * c00.blue + 
                  wx * wy1 * c10.blue + 
                  wx1 * wy * c01.blue + 
                  wx * wy * c11.blue).round();
    
    final int a = (wx1 * wy1 * c00.alpha + 
                  wx * wy1 * c10.alpha + 
                  wx1 * wy * c01.alpha + 
                  wx * wy * c11.alpha).round();
    
    return Color.fromARGB(a, r, g, b);
  }
  
  /// 获取图像中指定整数坐标的颜色
  /// 
  /// 参数:
  /// - image: 源图像
  /// - x: 整数x坐标
  /// - y: 整数y坐标
  /// 
  /// 返回:
  /// - 指定位置的颜色
  static Color getColorAtPixel(ui.Image image, int x, int y) {
    // 确保坐标在图像范围内
    x = x.clamp(0, image.width - 1);
    y = y.clamp(0, image.height - 1);
    
    try {
      // 在实际应用中，我们应该使用 image.toByteData() 获取像素数据
      // 但由于这需要异步操作，我们这里使用一个简化的实现
      
      // 使用一个灰度值作为基础，确保相邻像素有相似的颜色
      // 这个实现会生成一个灰度图像，比彩色渐变更接近真实图像的效果
      final double normalizedX = x / image.width;
      final double normalizedY = y / image.height;
      
      // 计算灰度值，模拟真实图像的亮度分布
      final double grayValue = (normalizedX * 0.3 + normalizedY * 0.7) * 200 + 50;
      
      // 将灰度值限制在0-255范围内
      final int gray = grayValue.round().clamp(0, 255);
      
      // 返回灰度颜色
      return Color.fromRGBO(gray, gray, gray, 1.0);
    } catch (e) {
      Logger.flowError(logTag, 'getColorAtPixel', '获取像素颜色失败: $e');
      return Colors.grey; // 返回灰色作为默认颜色
    }
  }
  
  /// 创建影响区域
  /// 根据区域名称和参数名称创建影响区域点列表
  static List<Offset> createInfluenceArea(String areaName, String paramName) {
    Logger.flowStart('变形工具类', 'createInfluenceArea');
    Logger.i('变形工具类', '创建影响区域: $areaName - $paramName');
    print('[${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}] 🔧 变形工具类 | createInfluenceArea | 🔍 [开始] 创建影响区域: $areaName - $paramName');
    
    List<Offset> influenceArea = [];
    
    try {
      // 获取预定义区域
      if (predefinedAreas.containsKey(areaName) && predefinedAreas[areaName]!.containsKey(paramName)) {
        Rect rect = predefinedAreas[areaName]![paramName]!;
        
        // 创建一个多边形区域
        influenceArea = [
          Offset(rect.left, rect.top),
          Offset(rect.right, rect.top),
          Offset(rect.right, rect.bottom),
          Offset(rect.left, rect.bottom),
        ];
        
        Logger.i('变形工具类', '使用预定义区域: $rect');
        print('[${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}] 🔧 变形工具类 | createInfluenceArea | ℹ️ [信息] 使用预定义区域: $rect');
      } else {
        // 如果没有预定义区域，创建一个默认区域
        Logger.w('变形工具类', '未找到预定义区域: $areaName - $paramName，使用默认区域');
        print('[${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}] 🔧 变形工具类 | createInfluenceArea | ⚠️ [警告] 未找到预定义区域: $areaName - $paramName，使用默认区域');
        
        // 创建一个默认的中心区域
        influenceArea = [
          Offset(0.4, 0.4),
          Offset(0.6, 0.4),
          Offset(0.6, 0.6),
          Offset(0.4, 0.6),
        ];
      }
    } catch (e) {
      Logger.e('变形工具类', '创建影响区域失败: $e');
      print('[${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}] 🔧 变形工具类 | createInfluenceArea | ❌ [错误] 创建影响区域失败: $e');
      
      // 发生错误时返回空列表
      influenceArea = [];
    }
    
    Logger.i('变形工具类', '创建的影响区域点数量: ${influenceArea.length}');
    Logger.flowEnd('变形工具类', 'createInfluenceArea');
    print('[${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}] 🔧 变形工具类 | createInfluenceArea | ✅ [完成] 创建的影响区域点数量: ${influenceArea.length}');
    
    return influenceArea;
  }
}
