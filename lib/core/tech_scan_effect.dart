import 'dart:math';
import 'dart:ui';
import 'package:flutter/material.dart';
import '../utils/logger.dart';

/// 科技扫描效果
/// 
/// 实现从上到下的科技感扫描线效果
class TechScanEffect {
  static const String _logTag = 'TechScanEffect';
  
  /// 扫描线颜色
  final Color scanLineColor;
  
  /// 扫描线宽度
  final double scanLineWidth;
  
  /// 扫描线透明度
  final double scanLineOpacity;
  
  /// 扫描线粒子数量
  final int particleCount;
  
  /// 扫描线粒子大小范围
  final double minParticleSize;
  final double maxParticleSize;
  
  /// 扫描线粒子速度范围
  final double minParticleSpeed;
  final double maxParticleSpeed;
  
  /// 随机数生成器
  final Random _random = Random();
  
  /// 粒子列表
  final List<_ScanParticle> _particles = [];
  
  /// 是否已初始化粒子
  bool _particlesInitialized = false;
  
  /// 构造函数
  TechScanEffect({
    this.scanLineColor = Colors.white,
    this.scanLineWidth = 2.0,
    this.scanLineOpacity = 0.7,
    this.particleCount = 50,
    this.minParticleSize = 1.0,
    this.maxParticleSize = 3.0,
    this.minParticleSpeed = 1.0,
    this.maxParticleSpeed = 3.0,
  }) {
    Logger.flow(_logTag, 'constructor', '✅ 科技扫描效果初始化完成');
  }
  
  /// 初始化粒子
  void _initializeParticles(Size size) {
    _particles.clear();
    
    for (int i = 0; i < particleCount; i++) {
      _particles.add(_ScanParticle(
        position: Offset(
          _random.nextDouble() * size.width,
          _random.nextDouble() * size.height,
        ),
        size: minParticleSize + _random.nextDouble() * (maxParticleSize - minParticleSize),
        speed: minParticleSpeed + _random.nextDouble() * (maxParticleSpeed - minParticleSpeed),
        opacity: 0.3 + _random.nextDouble() * 0.7,
      ));
    }
    
    _particlesInitialized = true;
    
    Logger.flow(_logTag, '_initializeParticles', '✅ 粒子初始化完成: ${_particles.length}个');
  }
  
  /// 绘制扫描效果
  void draw(Canvas canvas, Size size, double progress) {
    if (!_particlesInitialized) {
      _initializeParticles(size);
    }
    
    // 计算扫描线位置
    final scanLineY = size.height * progress;
    
    // 绘制扫描线
    final scanLinePaint = Paint()
      ..color = scanLineColor.withOpacity(scanLineOpacity)
      ..strokeWidth = scanLineWidth
      ..style = PaintingStyle.stroke;
    
    // 绘制主扫描线
    canvas.drawLine(
      Offset(0, scanLineY),
      Offset(size.width, scanLineY),
      scanLinePaint,
    );
    
    // 绘制扫描线光晕
    final glowPaint = Paint()
      ..color = scanLineColor.withOpacity(scanLineOpacity * 0.5)
      ..strokeWidth = scanLineWidth * 3
      ..style = PaintingStyle.stroke
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5.0);
    
    canvas.drawLine(
      Offset(0, scanLineY),
      Offset(size.width, scanLineY),
      glowPaint,
    );
    
    // 更新和绘制粒子
    for (final particle in _particles) {
      // 只绘制靠近扫描线的粒子
      if ((particle.position.dy - scanLineY).abs() < size.height * 0.1) {
        // 更新粒子位置
        particle.position = Offset(
          particle.position.dx,
          particle.position.dy + (scanLineY - particle.position.dy) * 0.1,
        );
        
        // 绘制粒子
        final particlePaint = Paint()
          ..color = scanLineColor.withOpacity(particle.opacity)
          ..style = PaintingStyle.fill;
        
        canvas.drawCircle(
          particle.position,
          particle.size,
          particlePaint,
        );
      }
    }
    
    // 绘制扫描区域
    final scanAreaPaint = Paint()
      ..color = scanLineColor.withOpacity(0.05)
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(
      Rect.fromPoints(
        Offset(0, 0),
        Offset(size.width, scanLineY),
      ),
      scanAreaPaint,
    );
  }
}

/// 扫描粒子
class _ScanParticle {
  /// 位置
  Offset position;
  
  /// 大小
  final double size;
  
  /// 速度
  final double speed;
  
  /// 透明度
  final double opacity;
  
  _ScanParticle({
    required this.position,
    required this.size,
    required this.speed,
    required this.opacity,
  });
}
