/// 图像处理服务结果
/// 
/// 用于封装图像处理操作的结果，包括成功状态、输出路径、错误信息和处理时间
class ImageProcessingResult {
  /// 处理是否成功
  final bool success;
  
  /// 输出文件路径
  final String outputPath;
  
  /// 错误信息（如果有）
  final String? error;
  
  /// 处理耗时（毫秒）
  final int processingTimeMs;

  /// 构造函数
  ImageProcessingResult({
    required this.success,
    required this.outputPath,
    this.error,
    required this.processingTimeMs,
  });
  
  /// 创建成功结果
  static ImageProcessingResult createSuccess({
    required String outputPath,
    required int processingTimeMs,
  }) {
    return ImageProcessingResult(
      success: true,
      outputPath: outputPath,
      processingTimeMs: processingTimeMs,
    );
  }
  
  /// 创建失败结果
  static ImageProcessingResult failure({
    required String error,
    required int processingTimeMs,
  }) {
    return ImageProcessingResult(
      success: false,
      outputPath: '',
      error: error,
      processingTimeMs: processingTimeMs,
    );
  }
}
