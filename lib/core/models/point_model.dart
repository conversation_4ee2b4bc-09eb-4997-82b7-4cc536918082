import 'dart:ui';

/// 点位类型枚举
enum PointType {
  /// 主要特征点
  primary,
  
  /// 次要特征点
  secondary,
}

/// 点位模型
/// 
/// 定义点位的基本属性和行为
class PointModel {
  /// 点位ID
  final String id;
  
  /// X坐标（0.0-1.0）
  final double x;
  
  /// Y坐标（0.0-1.0）
  final double y;
  
  /// Z坐标（深度，可选）
  final double z;
  
  /// 点位置信度（0.0-1.0）
  final double confidence;
  
  /// 点位类型
  final PointType type;

  /// 点位描述
  final String? description;
  
  /// 构造函数
  PointModel({
    required this.id,
    required this.x,
    required this.y,
    this.z = 0.0,
    this.confidence = 1.0,
    this.type = PointType.secondary,
    this.description,
  });
  
  /// 创建点位的副本，可选择性地修改部分属性
  PointModel copyWith({
    String? id,
    double? x,
    double? y,
    double? z,
    double? confidence,
    PointType? type,
    String? description,
  }) {
    return PointModel(
      id: id ?? this.id,
      x: x ?? this.x,
      y: y ?? this.y,
      z: z ?? this.z,
      confidence: confidence ?? this.confidence,
      type: type ?? this.type,
      description: description ?? this.description,
    );
  }
  
  /// 计算与另一个点的距离
  double distanceTo(PointModel other) {
    return Offset(x - other.x, y - other.y).distance;
  }
  
  /// 计算与另一个点的3D距离
  double distance3DTo(PointModel other) {
    final dx = x - other.x;
    final dy = y - other.y;
    final dz = z - other.z;
    return Offset(Offset(dx, dy).distance, dz).distance;
  }
  
  /// 将相对坐标（0.0-1.0）转换为绝对坐标
  Offset toOffset(Size size) {
    return Offset(x * size.width, y * size.height);
  }
  
  /// 从绝对坐标创建点位
  static PointModel fromOffset(
    Offset offset,
    Size size, {
    required String id,
    double z = 0.0,
    double confidence = 1.0,
    PointType type = PointType.secondary,
  }) {
    return PointModel(
      id: id,
      x: offset.dx / size.width,
      y: offset.dy / size.height,
      z: z,
      confidence: confidence,
      type: type,
    );
  }
  
  /// 将模型转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'x': x,
      'y': y,
      'z': z,
      'confidence': confidence,
      'type': type.toString(),
      'description': description,
    };
  }
  
  /// 从Map创建模型
  static PointModel fromMap(Map<String, dynamic> map) {
    return PointModel(
      id: map['id'],
      x: map['x'],
      y: map['y'],
      z: map['z'] ?? 0.0,
      confidence: map['confidence'] ?? 1.0,
      description: map['description'] as String?,
      type: map['type'] == 'PointType.primary' ? PointType.primary : PointType.secondary,
    );
  }
  
  @override
  String toString() {
    return 'PointModel{id: $id, x: $x, y: $y, z: $z, confidence: $confidence, type: $type}';
  }
}
