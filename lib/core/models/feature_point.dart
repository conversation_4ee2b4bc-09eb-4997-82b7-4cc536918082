import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 特征点模型
class FeaturePoint {
  final String id;       // 特征点唯一标识
  final int index;      // 特征点索引
  final String? name;   // 特征点名称
  final double x;       // x坐标
  final double y;       // y坐标
  final double z;       // z坐标（深度）
  final double visibility;  // 可见性（0-1）
  final double confidence; // 置信度（0-1）
  final bool isPrimary;    // 是否为主要特征点
  final bool isSecondary;  // 是否为次要特征点
  final double opacity;    // 透明度
  final double size;       // 大小
  final Color color;       // 颜色

  const FeaturePoint({
    String? id,
    required this.index,
    this.name,
    required this.x,
    required this.y,
    this.z = 0.0,
    this.visibility = 1.0,
    this.confidence = 1.0,
    this.isPrimary = false,
    this.isSecondary = false,
    this.opacity = 1.0,
    this.size = 4.0,
    this.color = Colors.blue,
  }) : id = id ?? 'fp_$index';

  /// 从JSON创建特征点
  factory FeaturePoint.fromJson(Map<String, dynamic> json) {
    return FeaturePoint(
      index: json['index'] as int,
      name: json['name'] as String?,
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      z: json['z'] != null ? (json['z'] as num).toDouble() : 0.0,
      visibility: json['visibility'] != null ? (json['visibility'] as num).toDouble() : 1.0,
      confidence: json['confidence'] != null ? (json['confidence'] as num).toDouble() : 1.0,
      isPrimary: json['isPrimary'] as bool? ?? false,
      isSecondary: json['isSecondary'] as bool? ?? false,
      opacity: json['opacity'] != null ? (json['opacity'] as num).toDouble() : 1.0,
      size: json['size'] != null ? (json['size'] as num).toDouble() : 4.0,
      color: json['color'] != null ? Color(json['color'] as int) : Colors.blue,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'name': name,
      'x': x,
      'y': y,
      'z': z,
      'visibility': visibility,
      'confidence': confidence,
      'isPrimary': isPrimary,
      'isSecondary': isSecondary,
      'opacity': opacity,
      'size': size,
      'color': color.value,
    };
  }

  /// 创建特征点的副本
  FeaturePoint copyWith({
    int? index,
    String? name,
    double? x,
    double? y,
    double? z,
    double? visibility,
    double? confidence,
    bool? isPrimary,
    bool? isSecondary,
    double? opacity,
    double? size,
    Color? color,
  }) {
    return FeaturePoint(
      index: index ?? this.index,
      name: name ?? this.name,
      x: x ?? this.x,
      y: y ?? this.y,
      z: z ?? this.z,
      visibility: visibility ?? this.visibility,
      confidence: confidence ?? this.confidence,
      isPrimary: isPrimary ?? this.isPrimary,
      isSecondary: isSecondary ?? this.isSecondary,
      opacity: opacity ?? this.opacity,
      size: size ?? this.size,
      color: color ?? this.color,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FeaturePoint &&
        other.index == index &&
        other.name == name &&
        other.x == x &&
        other.y == y &&
        other.z == z &&
        other.visibility == visibility &&
        other.confidence == confidence &&
        other.isPrimary == isPrimary &&
        other.isSecondary == isSecondary &&
        other.opacity == opacity &&
        other.size == size &&
        other.color == color;
  }

  @override
  int get hashCode {
    return Object.hash(
      index,
      name,
      x,
      y,
      z,
      visibility,
      confidence,
      isPrimary,
      isSecondary,
      opacity,
      size,
      color,
    );
  }

  @override
  String toString() {
    return 'FeaturePoint(index: $index, name: $name, x: $x, y: $y, z: $z, visibility: $visibility, confidence: $confidence, isPrimary: $isPrimary, isSecondary: $isSecondary, opacity: $opacity, size: $size, color: $color)';
  }

  /// 获取位置
  Offset get position => Offset(x, y);
}
