import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../utils/logger.dart';
import '../models/image_state.dart';
import 'models/feature_point.dart';
import '../config/service_config.dart';
import '../services/face_detection_service.dart';

/// 侧面图特征点处理器
/// 负责侧面图片的特征点识别和处理
class SideProfileProcessor {
  static const String _logTag = '侧面图处理器';
  
  // 特征点检测服务（使用正面特征点检测服务）
  final _detectionService = FaceDetectionService();
  
  // 单例模式
  static final SideProfileProcessor _instance = SideProfileProcessor._internal();
  factory SideProfileProcessor() => _instance;
  SideProfileProcessor._internal();
  
  // 原始特征点数据
  List<FeaturePoint>? _originalSideFeaturePoints;
  
  // 变形后的特征点数据
  List<FeaturePoint>? _sideFeaturePoints;
  
  // 处理状态
  bool _isProcessing = false;
  String? _sideImagePath;
  
  // 获取特征点数据
  List<FeaturePoint>? get featurePoints => _sideFeaturePoints;
  List<FeaturePoint>? get originalFeaturePoints => _originalSideFeaturePoints;
  
  /// 处理侧面图片并识别特征点
  Future<List<FeaturePoint>?> processSideImage(String imagePath) async {
    // 注释掉侧面图相关日志
    // Logger.flowStart(_logTag, 'processSideImage');
    // Logger.flow(_logTag, 'processSideImage', '开始处理侧面图片: $imagePath');
    
    if (_isProcessing) {
      // 注释掉侧面图相关日志
      // Logger.flowWarning(_logTag, 'processSideImage', '已有处理任务正在进行中，请等待完成');
      // Logger.flowEnd(_logTag, 'processSideImage');
      return null;
    }
    
    _isProcessing = true;
    _sideImagePath = imagePath;
    
    try {
      // 检查图像是否存在
      final file = File(imagePath);
      if (!await file.exists()) {
        // 注释掉侧面图相关日志
        // Logger.flowError(_logTag, 'processSideImage', '图像文件不存在: $imagePath');
        _isProcessing = false;
        // Logger.flowEnd(_logTag, 'processSideImage');
        throw Exception('图像文件不存在: $imagePath');
      }
      
      // 使用正面特征点检测服务处理侧面图片
      // 注释掉侧面图相关日志
      // Logger.flow(_logTag, 'processSideImage', '使用正面特征点检测服务处理侧面图片');
      
      try {
        // 初始化服务
        await _detectionService.initialize();
        
        // 获取Python和脚本路径
        final pythonPath = await ServiceConfig.pythonPath;
        final scriptPath = await ServiceConfig.faceMeshScript;
        
        // 注释掉侧面图相关日志
        // Logger.flow(_logTag, 'processSideImage', '🐍 Python路径: $pythonPath');
        // Logger.flow(_logTag, 'processSideImage', '📜 脚本路径: $scriptPath');
        
        // 准备JSON参数字符串
        final paramsMap = {
          'image_path': imagePath,
          'is_side_profile': true,
          'options': {
            'min_detection_confidence': 0.5,
            'min_tracking_confidence': 0.5,
            'max_num_faces': 1
          }
        };
        final paramsJson = json.encode(paramsMap);
        
        // 注释掉侧面图相关日志
        // Logger.flow(_logTag, 'processSideImage', '📊 参数: $paramsJson');
        
        // 使用--params参数传递JSON字符串
        final result = await Process.run(
          pythonPath,
          [
            scriptPath, 
            '--params', 
            paramsJson
          ],
          stdoutEncoding: const SystemEncoding(),
          stderrEncoding: const SystemEncoding(),
        );
        
        if (result.exitCode != 0) {
          // 注释掉侧面图相关日志
          // Logger.flowError(_logTag, 'processSideImage', '❌ 脚本执行失败: ${result.stderr}');
          throw Exception('特征点检测失败: ${result.stderr}');
        }
        
        final output = result.stdout.toString().trim();
        // 注释掉侧面图相关日志
        // Logger.flow(_logTag, 'processSideImage', '✅ 脚本执行成功');
        
        try {
          // 尝试解析输出
          Map<String, dynamic> jsonResult;
          
          // 处理可能的Python字典格式输出
          if (output.startsWith('{') && output.contains(':')) {
            // 尝试直接解析JSON
            try {
              jsonResult = json.decode(output);
            } catch (e) {
              // 如果失败，尝试将Python字典转换为JSON
              final jsonString = output
                  .replaceAll("'", '"')
                  .replaceAll('True', 'true')
                  .replaceAll('False', 'false')
                  .replaceAll('None', 'null');
              
              jsonResult = json.decode(jsonString);
            }
          } else {
            // 注释掉侧面图相关日志
            // Logger.flowError(_logTag, 'processSideImage', '❌ 输出格式不是有效的JSON或Python字典');
            throw Exception('输出格式不正确');
          }
          
          if (jsonResult['status'] == 'error') {
            // 注释掉侧面图相关日志
            // Logger.flowError(_logTag, 'processSideImage', '❌ 处理错误: ${jsonResult['message']}');
            throw Exception('处理错误: ${jsonResult['message']}');
          }
          
          if (jsonResult['status'] == 'no_face') {
            // 注释掉侧面图相关日志
            // Logger.flowWarning(_logTag, 'processSideImage', '⚠️ 未检测到侧面人脸');
            throw Exception('未检测到侧面人脸');
          }
          
          // 解析特征点数据
          return _parseSideFeaturePoints(jsonResult);
        } catch (e) {
          // 注释掉侧面图相关日志
          // Logger.flowError(_logTag, 'processSideImage', '❌ JSON解析失败: $e');
          // Logger.flowError(_logTag, 'processSideImage', '原始输出: $output');
          throw Exception('特征点数据解析失败');
        }
      } catch (e) {
        // 注释掉侧面图相关日志
        // Logger.flowError(_logTag, 'processSideImage', '使用服务检测特征点失败: $e');
        // Logger.flow(_logTag, 'processSideImage', '尝试使用备用方法');
        
        // 备用方法：生成模拟数据
        return await _generateMockFeaturePoints(imagePath);
      }
    } catch (e) {
      // 注释掉侧面图相关日志
      // Logger.flowError(_logTag, 'processSideImage', '处理失败: $e');
      _isProcessing = false;
      // Logger.flowEnd(_logTag, 'processSideImage');
      return null;
    } finally {
      _isProcessing = false;
      Logger.flowEnd(_logTag, 'processSideImage');
    }
  }

  List<FeaturePoint> _parseSideFeaturePoints(dynamic result) {
    // 注释掉侧面图相关日志
    // Logger.flowStart(_logTag, '_parseSideFeaturePoints');
    // Logger.flow(_logTag, '_parseSideFeaturePoints', '解析特征点数据');
    
    final List<FeaturePoint> featurePoints = [];
    
    if (result is Map && result.containsKey('landmarks') && result['landmarks'] is List) {
      final landmarks = result['landmarks'] as List;
      // 注释掉侧面图相关日志
      // Logger.flow(_logTag, '_parseSideFeaturePoints', '解析特征点数据，共 ${landmarks.length} 个点');
      
      for (final point in landmarks) {
        if (point is Map) {
          featurePoints.add(FeaturePoint(
            index: point['index'] as int,
            x: (point['x'] as num).toDouble(),
            y: (point['y'] as num).toDouble(),
            z: point['z'] != null ? (point['z'] as num).toDouble() : 0.0,
            visibility: point['visibility'] != null ? (point['visibility'] as num).toDouble() : 1.0,
            confidence: point['confidence'] != null ? (point['confidence'] as num).toDouble() : 1.0,
            isPrimary: point['primary'] as bool? ?? false,
            isSecondary: point['secondary'] as bool? ?? false,
          ));
        }
      }
      
      // 保存特征点数据
      _originalSideFeaturePoints = List.from(featurePoints);
      _sideFeaturePoints = List.from(featurePoints);
      
      // 注释掉侧面图相关日志
      // Logger.flow(_logTag, '_parseSideFeaturePoints', '成功解析 ${featurePoints.length} 个特征点');
    } else {
      // 注释掉侧面图相关日志
      // Logger.flowWarning(_logTag, '_parseSideFeaturePoints', '⚠️ 返回结果格式不正确');
    }
    
    // 注释掉侧面图相关日志
    // Logger.flowEnd(_logTag, '_parseSideFeaturePoints');
    return featurePoints;
  }

  /// 生成模拟特征点数据
  Future<List<FeaturePoint>> _generateMockFeaturePoints(String imagePath) async {
    // 注释掉侧面图相关日志
    // Logger.flowStart(_logTag, '_generateMockFeaturePoints');
    // Logger.flow(_logTag, '_generateMockFeaturePoints', '生成模拟侧面特征点数据');
    
    try {
      // 加载图像获取尺寸
      final file = File(imagePath);
      if (!await file.exists()) {
        // 注释掉侧面图相关日志
        // Logger.flowError(_logTag, '_generateMockFeaturePoints', '图像文件不存在: $imagePath');
        // Logger.flowEnd(_logTag, '_generateMockFeaturePoints');
        return [];
      }
      
      // 创建模拟特征点
      final List<FeaturePoint> featurePoints = [];
      
      // 生成50个模拟特征点，模拟侧面轮廓
      for (int i = 0; i < 50; i++) {
        final x = 0.2 + (i % 10) * 0.06;
        final y = 0.2 + (i ~/ 10) * 0.12;
        
        final isPrimary = i % 5 == 0;
        final isSecondary = i % 7 == 0;
        
        featurePoints.add(FeaturePoint(
          index: i,
          x: x,
          y: y,
          z: 0.0,
          visibility: 1.0,
          confidence: 0.95,
          isPrimary: isPrimary,
          isSecondary: isSecondary,
        ));
      }
      
      // 保存特征点数据
      _originalSideFeaturePoints = List.from(featurePoints);
      _sideFeaturePoints = List.from(featurePoints);
      
      // 注释掉侧面图相关日志
      // Logger.flow(_logTag, '_generateMockFeaturePoints', '✅ 生成了 ${featurePoints.length} 个模拟特征点');
      // Logger.flowEnd(_logTag, '_generateMockFeaturePoints');
      return featurePoints;
    } catch (e) {
      // 注释掉侧面图相关日志
      // Logger.flowError(_logTag, '_generateMockFeaturePoints', '生成模拟特征点失败: $e');
      // Logger.flowEnd(_logTag, '_generateMockFeaturePoints');
      return [];
    }
  }

  /// 应用鼻梁高度变形
  void applyBridgeHeightTransform(double value) {
    // 注释掉侧面图相关日志
    // Logger.flowStart(_logTag, 'applyBridgeHeightTransform');
    // Logger.flow(_logTag, 'applyBridgeHeightTransform', '开始应用鼻梁高度变形 | 参数值: $value');
    
    if (_sideFeaturePoints == null || _originalSideFeaturePoints == null) {
      // 注释掉侧面图相关日志
      // Logger.flowWarning(_logTag, 'applyBridgeHeightTransform', '特征点数据为空，无法应用变形');
      // Logger.flowEnd(_logTag, 'applyBridgeHeightTransform');
      return;
    }
    
    // 获取鼻梁区域的特征点 - 扩大特征点选择范围
    final bridgePoints = _sideFeaturePoints!.where((point) => 
      // 鼻梁上部
      point.index >= 168 && point.index <= 174 || 
      // 鼻梁中部
      point.index >= 197 && point.index <= 200 ||
      // 鼻梁下部和鼻尖连接处
      point.index >= 4 && point.index <= 6 ||
      // 鼻梁与额头连接处
      point.index >= 8 && point.index <= 10
    ).toList();
    
    if (bridgePoints.isEmpty) {
      // 注释掉侧面图相关日志
      // Logger.flowWarning(_logTag, 'applyBridgeHeightTransform', '未找到鼻梁区域的特征点');
      // Logger.flowEnd(_logTag, 'applyBridgeHeightTransform');
      return;
    }
    
    // 变形强度系数 - 根据医美效果调整
    final intensityFactor = 0.08;  // 增加变形强度
    
    // 应用变形
    for (var point in bridgePoints) {
      // 获取原始点
      final originalPoint = _originalSideFeaturePoints!.firstWhere(
        (p) => p.index == point.index,
        orElse: () => point,
      );
      
      // 根据特征点位置调整变形强度
      double xFactor = 0.0;
      double zFactor = 0.0;
      
      // 鼻梁上部 - 主要向前突出
      if (point.index >= 168 && point.index <= 174) {
        xFactor = 0.7;  // 水平方向变形较小
        zFactor = 1.0;  // 深度方向变形较大
      } 
      // 鼻梁中部 - 均衡变形
      else if (point.index >= 197 && point.index <= 200) {
        xFactor = 0.8;
        zFactor = 0.9;
      }
      // 鼻梁下部和鼻尖连接处 - 主要向外突出
      else if (point.index >= 4 && point.index <= 6) {
        xFactor = 1.0;
        zFactor = 0.8;
      }
      // 鼻梁与额头连接处 - 轻微变形
      else {
        xFactor = 0.5;
        zFactor = 0.6;
      }
      
      // 计算新的坐标
      // x坐标变化 - 向外突出
      final newX = originalPoint.x + value * intensityFactor * xFactor;
      
      // z坐标变化 - 深度方向（如果特征点有z坐标）
      double newZ = originalPoint.z;
      if (value > 0) {  // 只有在正向变形（鼻梁挺高）时才调整z坐标
        newZ = originalPoint.z - value * intensityFactor * zFactor;  // 负值表示向前突出
      }
      
      // 更新特征点
      final updatedPoint = point.copyWith(x: newX, z: newZ);
      
      // 替换原有点
      final index = _sideFeaturePoints!.indexWhere((p) => p.index == point.index);
      if (index != -1) {
        _sideFeaturePoints![index] = updatedPoint;
      }
    }
    
    // 注释掉侧面图相关日志
    // Logger.flow(_logTag, 'applyBridgeHeightTransform', '✅ 完成鼻梁高度变形 | 处理点数: ${bridgePoints.length}');
    // Logger.flowEnd(_logTag, 'applyBridgeHeightTransform');
  }
  
  /// 应用鼻尖调整变形
  void applyTipAdjustTransform(double value) {
    // 注释掉侧面图相关日志
    // Logger.flowStart(_logTag, 'applyTipAdjustTransform');
    // Logger.flow(_logTag, 'applyTipAdjustTransform', '开始应用鼻尖调整变形 | 参数值: $value');
    
    if (_sideFeaturePoints == null || _originalSideFeaturePoints == null) {
      // 注释掉侧面图相关日志
      // Logger.flowWarning(_logTag, 'applyTipAdjustTransform', '特征点数据为空，无法应用变形');
      // Logger.flowEnd(_logTag, 'applyTipAdjustTransform');
      return;
    }
    
    // 获取鼻尖区域的特征点 - 扩大特征点选择范围
    final tipPoints = _sideFeaturePoints!.where((point) => 
      // 鼻尖核心区域
      point.index >= 4 && point.index <= 8 || 
      // 鼻尖下部
      point.index >= 195 && point.index <= 200 ||
      // 鼻尖与鼻翼连接处
      point.index >= 94 && point.index <= 98
    ).toList();
    
    if (tipPoints.isEmpty) {
      // 注释掉侧面图相关日志
      // Logger.flowWarning(_logTag, 'applyTipAdjustTransform', '未找到鼻尖区域的特征点');
      // Logger.flowEnd(_logTag, 'applyTipAdjustTransform');
      return;
    }
    
    // 变形强度系数 - 根据医美效果调整
    final intensityFactor = 0.1;  // 增加变形强度
    
    // 应用变形
    for (var point in tipPoints) {
      // 获取原始点
      final originalPoint = _originalSideFeaturePoints!.firstWhere(
        (p) => p.index == point.index,
        orElse: () => point,
      );
      
      // 根据特征点位置调整变形强度
      double yFactor = 0.0;  // 垂直方向
      double xFactor = 0.0;  // 水平方向
      double zFactor = 0.0;  // 深度方向
      
      // 鼻尖核心区域 - 主要向上和向前变形
      if (point.index >= 4 && point.index <= 8) {
        yFactor = 1.0;  // 垂直方向变形强
        xFactor = 0.3;  // 水平方向变形弱
        zFactor = 0.8;  // 深度方向变形中等
      } 
      // 鼻尖下部 - 主要向上变形
      else if (point.index >= 195 && point.index <= 200) {
        yFactor = 0.9;
        xFactor = 0.2;
        zFactor = 0.6;
      }
      // 鼻尖与鼻翼连接处 - 轻微变形
      else {
        yFactor = 0.5;
        xFactor = 0.1;
        zFactor = 0.4;
      }
      
      // 计算新的坐标
      // y坐标变化 - 向上或向下移动
      final newY = originalPoint.y - value * intensityFactor * yFactor;
      
      // x坐标变化 - 轻微水平调整
      final newX = originalPoint.x + value * intensityFactor * xFactor * 0.5;
      
      // z坐标变化 - 深度方向（如果特征点有z坐标）
      double newZ = originalPoint.z;
      if (value > 0) {  // 只有在正向变形（鼻尖上翘）时才调整z坐标
        newZ = originalPoint.z - value * intensityFactor * zFactor;  // 负值表示向前突出
      }
      
      // 更新特征点
      final updatedPoint = point.copyWith(x: newX, y: newY, z: newZ);
      
      // 替换原有点
      final index = _sideFeaturePoints!.indexWhere((p) => p.index == point.index);
      if (index != -1) {
        _sideFeaturePoints![index] = updatedPoint;
      }
    }
    
    // 注释掉侧面图相关日志
    // Logger.flow(_logTag, 'applyTipAdjustTransform', '✅ 完成鼻尖调整变形 | 处理点数: ${tipPoints.length}');
    // Logger.flowEnd(_logTag, 'applyTipAdjustTransform');
  }
  
  /// 重置所有变形
  void resetTransforms() {
    if (_originalSideFeaturePoints == null) {
      // 注释掉侧面图相关日志
      // Logger.w(_logTag, '原始特征点数据为空，无法重置变形');
      return;
    }
    
    // 从原始特征点复制一份
    _sideFeaturePoints = List.from(_originalSideFeaturePoints!);
    // 注释掉侧面图相关日志
    // Logger.i(_logTag, '重置所有变形');
  }
  
  /// 生成模拟特征点数据
  /// 注意：此方法仅用于测试，实际应用中应使用真实特征点数据
  Future<List<FeaturePoint>> generateMockFeaturePoints() async {
    // 注释掉侧面图相关日志
    // Logger.i(_logTag, '生成模拟特征点数据');
    
    // 创建随机数生成器
    final random = Random();
    
    // 生成模拟特征点
    final List<FeaturePoint> mockPoints = [];
    
    // 生成面部轮廓点
    for (var i = 0; i < 15; i++) {
      mockPoints.add(FeaturePoint(
        index: i,
        x: 0.3 + random.nextDouble() * 0.1,
        y: 0.2 + i * 0.05,
        z: 0.0,
        visibility: 1.0,
        confidence: 0.9,
        isPrimary: i < 5,
        isSecondary: i >= 5 && i < 10,
      ));
    }
    
    // 生成鼻部点
    for (var i = 0; i < 10; i++) {
      mockPoints.add(FeaturePoint(
        index: i + 15,
        x: 0.4 + random.nextDouble() * 0.1,
        y: 0.4 + i * 0.02,
        z: 0.0,
        visibility: 1.0,
        confidence: 0.95,
        isPrimary: i < 3,
        isSecondary: i >= 3 && i < 7,
      ));
    }
    
    // 生成眼睛点
    for (var i = 0; i < 8; i++) {
      mockPoints.add(FeaturePoint(
        index: i + 25,
        x: 0.35 + random.nextDouble() * 0.1,
        y: 0.3 + i * 0.01,
        z: 0.0,
        visibility: 1.0,
        confidence: 0.9,
        isPrimary: i < 2,
        isSecondary: i >= 2 && i < 5,
      ));
    }
    
    // 生成嘴部点
    for (var i = 0; i < 6; i++) {
      mockPoints.add(FeaturePoint(
        index: i + 33,
        x: 0.38 + random.nextDouble() * 0.1,
        y: 0.6 + i * 0.02,
        z: 0.0,
        visibility: 1.0,
        confidence: 0.85,
        isPrimary: i < 2,
        isSecondary: i >= 2 && i < 4,
      ));
    }
    
    // 生成下巴点
    for (var i = 0; i < 3; i++) {
      mockPoints.add(FeaturePoint(
        index: i + 39,
        x: 0.35 + random.nextDouble() * 0.1,
        y: 0.75 + i * 0.05,
        z: 0.0,
        visibility: 1.0,
        confidence: 0.8,
        isPrimary: i < 1,
        isSecondary: i >= 1 && i < 2,
      ));
    }
    
    // 注释掉侧面图相关日志
    // Logger.i(_logTag, '生成了 ${mockPoints.length} 个模拟特征点');
    return mockPoints;
  }
}
