import 'package:flutter/material.dart';

/// MediaPipe面部特征点映射器
/// 
/// 用于将MediaPipe的468个特征点映射到我们的面部变形系统使用的特征点
class FaceLandmarkMapper {
  /// MediaPipe面部特征点到我们系统特征点的映射
  static const Map<String, List<int>> regionToMediapipeIndices = {
    // 眼部区域
    'eyes': [
      // 左眼
      33, 133, 157, 158, 159, 160, 161, 173, 246,
      // 右眼
      263, 362, 384, 385, 386, 387, 388, 398, 466,
      // 眼袋区域
      110, 117, 118, 119, 120, 121, 128, 338, 345, 346, 347, 348, 349, 357,
    ],
    
    // 鼻部区域
    'nose': [
      1, 2, 3, 4, 5, 6, 19, 20, 94, 97, 98, 99, 129, 195, 196, 197, 218, 219, 220, 235, 236, 237, 324, 358, 429, 438, 439, 440,
    ],
    
    // 嘴部区域
    'lips': [
      0, 11, 12, 13, 14, 15, 16, 17, 37, 38, 39, 40, 41, 42, 61, 62, 72, 73, 74, 76, 77, 78, 80, 81, 82, 84, 85, 86, 87, 88, 89, 90, 91, 95, 146, 178, 179, 180, 181, 182, 183, 184, 185, 191, 267, 268, 269, 270, 271, 272, 291, 292, 302, 303, 304, 306, 307, 308, 310, 311, 312, 314, 315, 316, 317, 318, 319, 320, 321, 324, 375, 402, 403, 404, 405, 406, 407, 408, 409, 415,
    ],
    
    // 面部轮廓
    'face_contour': [
      10, 21, 54, 58, 67, 71, 93, 103, 109, 127, 132, 136, 148, 149, 150, 151, 152, 162, 169, 170, 171, 175, 177, 187, 188, 193, 201, 205, 206, 207, 210, 211, 212, 216, 234, 251, 280, 284, 288, 297, 301, 318, 323, 330, 332, 333, 338, 341, 345, 352, 356, 376, 377, 378, 379, 389, 397, 400, 419, 420, 423, 425, 428, 430, 450, 454, 461,
    ],
    
    // 抗衰老区域
    'anti_aging': [
      24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 45, 55, 65, 75, 85, 95, 105, 115, 125, 135, 145, 155, 165, 175, 185, 195, 205, 215, 225, 235, 245, 255, 265, 275, 285, 295, 305, 315, 325, 335, 345, 355, 365, 375, 385, 395, 405, 415, 425, 435, 445, 455, 465,
    ],
  };
  
  /// 将我们系统的特征点索引映射到MediaPipe特征点索引
  static const Map<int, int> systemToMediapipeIndex = {
    // 眼睛
    0: 33,   // 左眼内角
    1: 133,  // 左眼上缘
    2: 159,  // 左眼外角
    3: 145,  // 左眼下缘
    4: 362,  // 右眼内角
    5: 386,  // 右眼上缘
    6: 263,  // 右眼外角
    7: 374,  // 右眼下缘
    
    // 鼻子
    8: 1,    // 鼻尖
    9: 4,    // 鼻梁
    10: 94,  // 左鼻翼
    11: 324, // 右鼻翼
    
    // 嘴巴
    12: 61,  // 嘴左角
    13: 0,   // 嘴上中点
    14: 291, // 嘴右角
    15: 17,  // 嘴下中点
    
    // 脸部轮廓
    16: 10,  // 左脸颊
    17: 152, // 左下颌
    18: 234, // 下巴
    19: 454, // 右下颌
    20: 227, // 右脸颊
  };
  
  /// 将MediaPipe特征点索引映射到我们系统的特征点索引
  static const Map<int, int> mediapipeToSystemIndex = {
    33: 0,   // 左眼内角
    133: 1,  // 左眼上缘
    159: 2,  // 左眼外角
    145: 3,  // 左眼下缘
    362: 4,  // 右眼内角
    386: 5,  // 右眼上缘
    263: 6,  // 右眼外角
    374: 7,  // 右眼下缘
    
    1: 8,    // 鼻尖
    4: 9,    // 鼻梁
    94: 10,  // 左鼻翼
    324: 11, // 右鼻翼
    
    61: 12,  // 嘴左角
    0: 13,   // 嘴上中点
    291: 14, // 嘴右角
    17: 15,  // 嘴下中点
    
    10: 16,  // 左脸颊
    152: 17, // 左下颌
    234: 18, // 下巴
    454: 19, // 右下颌
    227: 20, // 右脸颊
  };
  
  /// 获取特定区域的MediaPipe特征点索引
  static List<int> getMediapipeIndicesForRegion(String region) {
    return regionToMediapipeIndices[region] ?? [];
  }
  
  /// 将MediaPipe特征点转换为我们系统的特征点
  static Map<int, Offset> convertMediapipeToSystemLandmarks(Map<int, Offset> mediapipeLandmarks) {
    final Map<int, Offset> systemLandmarks = {};
    
    mediapipeLandmarks.forEach((mediapipeIndex, offset) {
      final int? systemIndex = mediapipeToSystemIndex[mediapipeIndex];
      if (systemIndex != null) {
        systemLandmarks[systemIndex] = offset;
      }
    });
    
    return systemLandmarks;
  }
  
  /// 将我们系统的特征点转换为MediaPipe特征点
  static Map<int, Offset> convertSystemToMediapipeLandmarks(Map<int, Offset> systemLandmarks) {
    final Map<int, Offset> mediapipeLandmarks = {};
    
    systemLandmarks.forEach((systemIndex, offset) {
      final int? mediapipeIndex = systemToMediapipeIndex[systemIndex];
      if (mediapipeIndex != null) {
        mediapipeLandmarks[mediapipeIndex] = offset;
      }
    });
    
    return mediapipeLandmarks;
  }
}
