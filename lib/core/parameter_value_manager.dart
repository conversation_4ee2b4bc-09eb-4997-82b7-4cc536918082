import '../utils/logger.dart';

/// 参数值管理器 - 单例模式
/// 用于管理全局参数键值组合，确保整个应用程序中只有一个实例
class ParameterValueManager {
  static const String _logTag = 'ParameterValueManager';
  
  // 单例实例
  static final ParameterValueManager _instance = ParameterValueManager._internal();
  
  // 工厂构造函数，返回单例实例
  factory ParameterValueManager() {
    return _instance;
  }
  
  // 私有构造函数，防止外部创建实例
  ParameterValueManager._internal() {
    Logger.flow(_logTag, 'constructor', '🔧 创建参数值管理器单例');
  }
  
  // 全局参数键值组合
  final Map<String, double> _parameters = {};
  
  /// 获取参数值
  /// @param paramName 参数名称，例如："tip_adjust"
  /// @return 参数值，如果不存在则返回0.0
  double getValue(String paramName) {
    if (_parameters.containsKey(paramName)) {
      Logger.flow(_logTag, 'getValue', '📊 获取参数值: $paramName = ${_parameters[paramName]}');
      return _parameters[paramName]!;
    } else {
      Logger.flow(_logTag, 'getValue', '📊 参数不存在，返回默认值0.0: $paramName');
      return 0.0;
    }
  }
  
  /// 设置参数值
  /// @param paramName 参数名称，例如："tip_adjust"
  /// @param value 参数值
  void setValue(String paramName, double value) {
    // 只有当参数值非零时才保存到参数键值组合中
    if (value != 0.0) {
      _parameters[paramName] = value;
      Logger.flow(_logTag, 'setValue', '📊 设置参数值: $paramName = $value');
    } else {
      // 如果参数值为零，从参数键值组合中移除
      if (_parameters.containsKey(paramName)) {
        _parameters.remove(paramName);
        Logger.flow(_logTag, 'setValue', '📊 移除零值参数: $paramName');
      }
    }
  }
  
  /// 检查参数是否存在
  /// @param paramName 参数名称，例如："tip_adjust"
  /// @return 参数是否存在
  bool containsParameter(String paramName) {
    return _parameters.containsKey(paramName);
  }
  
  /// 获取所有参数键值组合
  /// @return 所有参数键值组合的副本
  Map<String, double> getAllParameters() {
    return Map<String, double>.from(_parameters);
  }
  
  /// 获取所有参数键值组合的数量
  /// @return 参数键值组合的数量
  int getParameterCount() {
    return _parameters.length;
  }
  
  /// 移除指定参数值
  void removeValue(String key) {
    if (_parameters.containsKey(key)) {
      _parameters.remove(key);
      Logger.flow(_logTag, 'removeValue', '🗑 移除参数: $key');
    }
  }

  /// 清空所有参数值
  void clearAllParameters() {
    _parameters.clear();
    Logger.flow(_logTag, 'clearAllParameters', '🧹 清空所有参数');
  }
  
  /// 打印所有非零参数值（用于调试）
  void logAllParameters() {
    Logger.flow(_logTag, 'logAllParameters', '📊 当前所有非零参数值:');
    if (_parameters.isEmpty) {
      Logger.flow(_logTag, 'logAllParameters', '  - 无参数');
    } else {
      _parameters.forEach((key, val) {
        if (val != 0.0) {
          Logger.flow(_logTag, 'logAllParameters', '  - $key = $val');
        }
      });
    }
  }
}
