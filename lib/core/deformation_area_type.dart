/// 变形区域类型枚举
/// 
/// 定义了不同的变形技术对应的区域类型
enum DeformationAreaType {
  /// 无变形区域
  /// 
  /// 表示没有选择任何变形技术
  none,

  /// 向量场变形
  /// 
  /// 显示以控制点为中心的径向衰减影响区域
  /// 绿色区域表示影响强度，随距离线性衰减
  /// 特点：局部影响，主要影响控制点附近区域
  vectorField,
  
  /// 薄板样条插值变形
  /// 
  /// 显示全局影响区域，红色强度表示影响程度
  /// 特点：理论上影响整个图像，但强度随距离迅速衰减
  /// 可视化：全局红色渐变，中心区域影响最强
  tps,
  
  /// 网格变形
  /// 
  /// 显示实际网格结构和受影响的网格区域
  /// 蓝色线条表示网格线，蓝色半透明区域表示影响范围
  /// 特点：影响范围限于网格区域，边界清晰
  mesh,
  
  /// 局部变形
  /// 
  /// 显示以控制点为中心的平方衰减影响区域
  /// 黄色区域表示影响强度，使用平方衰减函数
  /// 特点：影响范围中等，边缘平滑过渡
  local,
  
  /// 三角剖分变形
  /// 
  /// 显示德劳内三角剖分网格和受影响的三角形
  /// 紫色线条表示三角形边界，半透明紫色填充表示影响区域
  /// 特点：影响范围严格限于包含控制点的三角形
  triangulation,
}
