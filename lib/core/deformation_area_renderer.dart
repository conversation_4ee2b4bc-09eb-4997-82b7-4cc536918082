import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'models/feature_point.dart';  // 使用 core 目录下的 FeaturePoint 类
import '../utils/logger.dart';
import 'deformation_area_type.dart';

/// 变形区域渲染器
/// 
/// 负责管理和渲染不同类型的变形区域，根据变形技术的特点显示不同的视觉效果
class DeformationAreaRenderer extends ChangeNotifier {
  static const String _logTag = 'DeformationAreaRenderer';
  
  // 变形区域的类型
  DeformationAreaType _areaType = DeformationAreaType.none;
  
  // 变形区域的颜色和透明度配置
  final Map<DeformationAreaType, Color> _areaColors = {
    DeformationAreaType.vectorField: Colors.green.withOpacity(0.3),
    DeformationAreaType.tps: Colors.red.withOpacity(0.3),
    DeformationAreaType.mesh: Colors.blue.withOpacity(0.3),
    DeformationAreaType.local: Colors.yellow.withOpacity(0.3),
    DeformationAreaType.triangulation: Colors.purple.withOpacity(0.3),
  };
  
  // 受影响的特征点
  List<FeaturePoint>? _affectedPoints = [];
  
  // 变形区域的边界
  Rect? _areaBounds = Rect.zero;
  
  // 变形强度（用于渐变效果）
  double _intensity = 1.0;
  
  // 参数值（用于方向指示）
  double _paramValue = 0.0;
  
  // 可见性控制
  bool _isVisible = false;
  
  // Getters
  DeformationAreaType get areaType => _areaType;
  List<FeaturePoint>? get affectedPoints => _affectedPoints;
  Rect? get areaBounds => _areaBounds;
  double get intensity => _intensity;
  double get paramValue => _paramValue;
  bool get isVisible => _isVisible;
  Color get areaColor => _areaColors[_areaType] ?? Colors.transparent;
  
  /// 更新变形区域配置
  void updateDeformationArea({
    required DeformationAreaType areaType,
    required List<FeaturePoint>? affectedPoints,
    required Rect? areaBounds,
    required double intensity,
    required double paramValue,
  }) {
    Logger.i(_logTag, 'updateDeformationArea - 更新变形区域配置');
    print(' [DeformationAreaRenderer.updateDeformationArea] 入口');
    print(' [DeformationAreaRenderer] 更新变形区域配置');
    print(' [DeformationAreaRenderer] 区域类型: $areaType');
    print(' [DeformationAreaRenderer] 受影响点数量: ${affectedPoints?.length ?? 0}');
    print(' [DeformationAreaRenderer] 区域边界: $areaBounds');
    print(' [DeformationAreaRenderer] 强度: $intensity');
    print(' [DeformationAreaRenderer] 参数值: $paramValue');
    print(' [DeformationAreaRenderer] 当前可见性: $_isVisible');
    
    _areaType = areaType;
    _affectedPoints = affectedPoints;
    _areaBounds = areaBounds;
    _intensity = intensity;
    _paramValue = paramValue;
    
    // 确保变形区域可见
    bool visibilityChanged = !_isVisible;
    _isVisible = true;
    print(' [DeformationAreaRenderer] 设置可见性为true ${visibilityChanged ? "(已改变)" : "(未改变)"}');
    
    // 通知监听器
    print(' [DeformationAreaRenderer] 准备调用 notifyListeners()');
    notifyListeners();
    print(' [DeformationAreaRenderer] notifyListeners() 调用完成');
    
    Logger.i(_logTag, 'updateDeformationArea - 变形区域配置更新完成');
    print(' [DeformationAreaRenderer.updateDeformationArea] 出口 - 变形区域配置更新完成');
  }
  
  /// 更新变形区域强度
  void updateIntensity(double intensity) {
    if (_intensity != intensity) {
      _intensity = intensity;
      
      // 确保可见性为true
      if (!_isVisible) {
        _isVisible = true;
        Logger.d(_logTag, 'updateIntensity - 强度更新时设置可见性为true');
      }
      
      // 通知监听器更新UI
      notifyListeners();
    }
  }
  
  /// 显示变形区域
  void showDeformationArea() {
    print(' [DeformationAreaRenderer.showDeformationArea] 入口');
    Logger.i(_logTag, 'showDeformationArea - 显示变形区域');
    print(' [DeformationAreaRenderer] 显示变形区域');
    print(' [DeformationAreaRenderer] 当前可见性状态: $_isVisible');
    
    if (!_isVisible) {
      _isVisible = true;
      print(' [DeformationAreaRenderer] 变形区域已设置为可见');
      print(' [DeformationAreaRenderer] 区域类型: $areaType');
      print(' [DeformationAreaRenderer] 受影响点数量: ${affectedPoints?.length ?? 0}');
      print(' [DeformationAreaRenderer] 区域边界: $areaBounds');
      
      print(' [DeformationAreaRenderer] 准备调用 notifyListeners()');
      notifyListeners();
      print(' [DeformationAreaRenderer] notifyListeners() 调用完成');
    } else {
      print(' [DeformationAreaRenderer] 变形区域已经是可见状态');
    }
    
    print(' [DeformationAreaRenderer.showDeformationArea] 出口');
  }
  
  /// 隐藏变形区域
  void hideDeformationArea() {
    Logger.i(_logTag, 'hideDeformationArea - 隐藏变形区域');
    print(' [DeformationAreaRenderer] 隐藏变形区域');
    
    if (_isVisible) {
      _isVisible = false;
      print(' [DeformationAreaRenderer] 可见性状态从true变为false');
      notifyListeners();
    } else {
      print(' [DeformationAreaRenderer] 变形区域已经是隐藏状态');
    }
  }
  
  /// 获取当前变形区域的可见性
  bool get visible => _isVisible;
  
  /// 设置当前变形区域的可见性
  set visible(bool visible) {
    _isVisible = visible;
    notifyListeners();
  }
}

/// 变形区域绘制器
/// 
/// 负责在画布上绘制不同类型的变形区域视觉效果
class DeformationAreaPainter extends CustomPainter {
  static const String _logTag = 'DeformationAreaPainter';
  final DeformationAreaRenderer renderer;
  
  DeformationAreaPainter(this.renderer) : super(repaint: renderer);
  
  @override
  void paint(Canvas canvas, Size size) {
    print(' [DeformationAreaPainter] 开始绘制变形区域');
    print(' [DeformationAreaPainter] 区域类型: ${renderer.areaType}');
    print(' [DeformationAreaPainter] 可见性: ${renderer.isVisible}');
    print(' [DeformationAreaPainter] 受影响点数量: ${renderer.affectedPoints?.length ?? 0}');
    print(' [DeformationAreaPainter] 区域边界: ${renderer.areaBounds}');
    print(' [DeformationAreaPainter] 画布大小: ${size.width} x ${size.height}');
    
    // 如果变形区域不可见，则跳过绘制
    if (!renderer.isVisible) {
      print(' [DeformationAreaPainter] 变形区域不可见，跳过绘制');
      return;
    }
    
    // 绘制画布边界，以便确认坐标系统
    final canvasBorderPaint = Paint()
      ..color = Colors.blue.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), canvasBorderPaint);
    print(' [DeformationAreaPainter] 绘制了画布边界: ${Rect.fromLTWH(0, 0, size.width, size.height)}');
    
    // 只在调试模式下绘制调试信息
    bool showDebugInfo = true; // 设置为 true 可以显示调试信息
    if (showDebugInfo) {
      _drawDebugInfo(canvas, size);
    }
    
    // 如果没有受影响的点或区域边界，则绘制备用区域
    if (renderer.affectedPoints == null || renderer.affectedPoints!.isEmpty || 
        renderer.areaBounds == null || renderer.areaBounds == Rect.zero) {
      print(' [DeformationAreaPainter] 没有受影响的点或区域边界，使用备用区域');
      _drawFallbackArea(canvas, size);
      return;
    }
    
    // 根据变形区域类型选择不同的绘制方法
    switch (renderer.areaType) {
      case DeformationAreaType.vectorField:
        print(' [DeformationAreaPainter] 开始绘制向量场变形区域');
        _drawVectorFieldArea(canvas, size);
        print(' [DeformationAreaPainter] 向量场变形区域绘制完成');
        break;
      case DeformationAreaType.tps:
        print(' [DeformationAreaPainter] 开始绘制薄板样条插值变形区域');
        _drawTPSArea(canvas, size);
        print(' [DeformationAreaPainter] 薄板样条插值变形区域绘制完成');
        break;
      case DeformationAreaType.mesh:
        print(' [DeformationAreaPainter] 开始绘制网格变形区域');
        _drawMeshArea(canvas, size);
        print(' [DeformationAreaPainter] 网格变形区域绘制完成');
        break;
      case DeformationAreaType.local:
        print(' [DeformationAreaPainter] 开始绘制局部变形区域');
        _drawLocalArea(canvas, size);
        print(' [DeformationAreaPainter] 局部变形区域绘制完成');
        break;
      case DeformationAreaType.triangulation:
        print(' [DeformationAreaPainter] 开始绘制三角剖分变形区域');
        _drawTriangulationArea(canvas, size);
        print(' [DeformationAreaPainter] 三角剖分变形区域绘制完成');
        break;
      default:
        print(' [DeformationAreaPainter] 未知的变形区域类型: ${renderer.areaType}');
        _drawFallbackArea(canvas, size);
    }
  }
  
  /// 绘制调试信息
  void _drawDebugInfo(Canvas canvas, Size size) {
    print(' [DeformationAreaPainter] 绘制调试信息');
    
    // 绘制边框
    final borderPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.stroke
      ..strokeWidth = 5.0;
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      borderPaint
    );
    
    // 绘制对角线
    final linePaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    canvas.drawLine(
      Offset.zero,
      Offset(size.width, size.height),
      linePaint
    );
    
    canvas.drawLine(
      Offset(size.width, 0),
      Offset(0, size.height),
      linePaint
    );
    
    // 绘制中心点
    final centerPaint = Paint()
      ..color = Colors.yellow
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      10.0,
      centerPaint
    );
    
    // 添加文本信息
    final textStyle = TextStyle(
      color: Colors.black,
      fontSize: 16.0,
      fontWeight: FontWeight.bold,
      backgroundColor: Colors.white.withOpacity(0.8),
    );
    
    _drawText(
      canvas,
      '调试模式: ${renderer.areaType}',
      Offset(20, 30),
      textStyle
    );
    
    _drawText(
      canvas,
      '可见性: ${renderer.isVisible}',
      Offset(20, 60),
      textStyle
    );
    
    _drawText(
      canvas,
      '受影响点: ${renderer.affectedPoints?.length ?? 0}个',
      Offset(20, 90),
      textStyle
    );
    
    print(' [DeformationAreaPainter] 调试信息绘制完成');
  }
  
  /// 绘制文本
  void _drawText(Canvas canvas, String text, Offset position, TextStyle style) {
    final textSpan = TextSpan(
      text: text,
      style: style,
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, position);
  }
  
  /// 绘制备用区域（当没有受影响的点或区域边界时）
  void _drawFallbackArea(Canvas canvas, Size size) {
    print(' [DeformationAreaPainter] 绘制备用区域');
    
    // 根据变形区域类型选择颜色
    Color areaColor;
    String areaTypeName;
    
    switch (renderer.areaType) {
      case DeformationAreaType.vectorField:
        areaColor = Colors.green.withOpacity(0.3);
        areaTypeName = '向量场变形';
        break;
      case DeformationAreaType.tps:
        areaColor = Colors.red.withOpacity(0.3);
        areaTypeName = '薄板样条插值变形';
        break;
      case DeformationAreaType.mesh:
        areaColor = Colors.blue.withOpacity(0.3);
        areaTypeName = '网格变形';
        break;
      case DeformationAreaType.local:
        areaColor = Colors.yellow.withOpacity(0.3);
        areaTypeName = '局部变形';
        break;
      case DeformationAreaType.triangulation:
        areaColor = Colors.purple.withOpacity(0.3);
        areaTypeName = '三角剖分变形';
        break;
      default:
        areaColor = Colors.orange.withOpacity(0.3);
        areaTypeName = '未知变形类型';
        break;
    }
    
    // 绘制中心区域
    final centerRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: size.width * 0.6,
      height: size.height * 0.6,
    );
    
    // 绘制半透明背景
    final bgPaint = Paint()
      ..color = areaColor
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(centerRect, bgPaint);
    
    // 绘制边框
    final borderPaint = Paint()
      ..color = areaColor.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;
    
    canvas.drawRect(centerRect, borderPaint);
    
    // 绘制中心文本
    final textStyle = TextStyle(
      color: Colors.black,
      fontSize: 20.0,
      fontWeight: FontWeight.bold,
      backgroundColor: Colors.white.withOpacity(0.8),
    );
    
    _drawText(
      canvas,
      areaTypeName,
      Offset(
        (size.width - 200) / 2,
        (size.height - 30) / 2
      ),
      textStyle
    );
    
    // 绘制说明文本
    final descriptionStyle = TextStyle(
      color: Colors.black,
      fontSize: 16.0,
      backgroundColor: Colors.white.withOpacity(0.8),
    );
    
    _drawText(
      canvas,
      '(无特征点时的备用显示)',
      Offset(
        (size.width - 200) / 2,
        (size.height + 30) / 2
      ),
      descriptionStyle
    );
    
    print(' [DeformationAreaPainter] 备用区域绘制完成');
  }
  
  /// 绘制向量场变形区域（径向衰减）
  void _drawVectorFieldArea(Canvas canvas, Size size) {
    Logger.d(_logTag, '_drawVectorFieldArea - 绘制向量场变形区域');
    
    // 为每个受影响的点绘制径向渐变
    for (var point in renderer.affectedPoints ?? []) {
      final center = Offset(point.x, point.y);
      final radius = 50.0 * renderer.intensity;
      
      final gradient = RadialGradient(
        colors: [
          Colors.green.withOpacity(0.5),
          Colors.green.withOpacity(0.0),
        ],
      );
      
      final rect = Rect.fromCircle(center: center, radius: radius);
      final shader = gradient.createShader(rect);
      
      canvas.drawCircle(
        center,
        radius,
        Paint()..shader = shader,
      );
      
      // 绘制方向指示箭头
      if (renderer.paramValue != 0) {
        _drawDirectionArrow(canvas, point, renderer.paramValue);
      }
    }
  }
  
  /// 绘制薄板样条插值变形区域（全局影响，红色渐变）
  void _drawTPSArea(Canvas canvas, Size size) {
    Logger.d(_logTag, '_drawTPSArea - 绘制薄板样条插值变形区域');
    print(' [DeformationAreaPainter._drawTPSArea] 开始绘制 TPS 变形区域');
    
    // 即使没有受影响的点，也显示默认区域
    if ((renderer.affectedPoints ?? []).isEmpty) {
      print(' [DeformationAreaPainter._drawTPSArea] 没有受影响的点，使用默认中心点');
      
      // 使用画布中心作为默认中心点
      final center = Offset(size.width / 2, size.height / 2);
      print(' [DeformationAreaPainter._drawTPSArea] 默认中心点: $center');
      
      // 使用较大的半径，确保变形区域可见
      final maxDimension = math.max(size.width, size.height);
      final radius = maxDimension * 0.7 * renderer.intensity;
      print(' [DeformationAreaPainter._drawTPSArea] 默认半径: $radius');
      
      // 使用更明显的颜色
      final gradient = RadialGradient(
        colors: [
          Colors.red.withOpacity(0.9),  // 更高的不透明度
          Colors.red.withOpacity(0.3),  // 更高的不透明度
        ],
      );
      
      final rect = Rect.fromCircle(center: center, radius: radius);
      print(' [DeformationAreaPainter._drawTPSArea] 默认渐变区域: $rect');
      final shader = gradient.createShader(rect);
      
      // 绘制渐变圆形
      canvas.drawCircle(
        center,
        radius,
        Paint()..shader = shader,
      );
      print(' [DeformationAreaPainter._drawTPSArea] 绘制了默认渐变圆形');
      
      // 绘制中心点标记
      canvas.drawCircle(
        center,
        15.0,
        Paint()
          ..color = Colors.white.withOpacity(0.8)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0,
      );
      
      canvas.drawCircle(
        center,
        12.0,
        Paint()..color = Colors.red.withOpacity(1.0),
      );
      
      return;
    }
    
    // 计算中心点
    double centerX = 0;
    double centerY = 0;
    for (var point in renderer.affectedPoints ?? []) {
      centerX += point.x;
      centerY += point.y;
    }
    centerX /= (renderer.affectedPoints ?? []).length;
    centerY /= (renderer.affectedPoints ?? []).length;
    
    final center = Offset(centerX, centerY);
    print(' [DeformationAreaPainter._drawTPSArea] 中心点: $center');
    
    // 增加半径大小，使变形区域更加明显
    final maxDimension = math.max(size.width, size.height);
    final radius = maxDimension * 0.7 * renderer.intensity;  // 增加半径比例
    print(' [DeformationAreaPainter._drawTPSArea] 半径: $radius');
    
    // 增加颜色的不透明度，使变形区域更加明显
    final gradient = RadialGradient(
      colors: [
        Colors.red.withOpacity(0.8),  // 增加不透明度
        Colors.red.withOpacity(0.2),  // 增加不透明度
      ],
    );
    
    final rect = Rect.fromCircle(center: center, radius: radius);
    print(' [DeformationAreaPainter._drawTPSArea] 渐变区域: $rect');
    final shader = gradient.createShader(rect);
    
    // 绘制渐变圆形
    canvas.drawCircle(
      center,
      radius,
      Paint()..shader = shader,
    );
    print(' [DeformationAreaPainter._drawTPSArea] 绘制了渐变圆形');
    
    // 绘制控制点，增加大小和不透明度
    for (var point in renderer.affectedPoints ?? []) {
      final pointPosition = Offset(point.x, point.y);
      print(' [DeformationAreaPainter._drawTPSArea] 绘制控制点: $pointPosition');
      
      // 绘制控制点外圈
      canvas.drawCircle(
        pointPosition,
        12.0,  // 增加控制点外圈大小
        Paint()
          ..color = Colors.white.withOpacity(0.6)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.0,
      );
      
      // 绘制控制点内圈
      canvas.drawCircle(
        pointPosition,
        10.0,  // 增加控制点内圈大小
        Paint()..color = Colors.red.withOpacity(0.9),  // 增加不透明度
      );
      
      // 绘制方向指示箭头
      if (renderer.paramValue != 0) {
        _drawDirectionArrow(canvas, point, renderer.paramValue);
      }
    }
    
    print(' [DeformationAreaPainter._drawTPSArea] TPS 变形区域绘制完成');
  }
  
  /// 绘制网格变形区域（网格结构）
  void _drawMeshArea(Canvas canvas, Size size) {
    Logger.d(_logTag, '_drawMeshArea - 绘制网格变形区域');
    
    if ((renderer.affectedPoints ?? []).isEmpty) return;
    
    final bounds = renderer.areaBounds;
    final paint = Paint()
      ..color = Colors.blue.withOpacity(0.2)
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(bounds ?? Rect.zero, paint);
    
    // 绘制网格线
    paint
      ..color = Colors.blue.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    final cellSize = 20.0;
    for (double x = (bounds ?? Rect.zero).left; x <= (bounds ?? Rect.zero).right; x += cellSize) {
      canvas.drawLine(
        Offset(x, (bounds ?? Rect.zero).top),
        Offset(x, (bounds ?? Rect.zero).bottom),
        paint,
      );
    }
    
    for (double y = (bounds ?? Rect.zero).top; y <= (bounds ?? Rect.zero).bottom; y += cellSize) {
      canvas.drawLine(
        Offset((bounds ?? Rect.zero).left, y),
        Offset((bounds ?? Rect.zero).right, y),
        paint,
      );
    }
    
    // 绘制控制点
    for (var point in renderer.affectedPoints ?? []) {
      canvas.drawCircle(
        Offset(point.x, point.y),
        5.0,
        Paint()..color = Colors.blue,
      );
      
      // 绘制方向指示箭头
      if (renderer.paramValue != 0) {
        _drawDirectionArrow(canvas, point, renderer.paramValue);
      }
    }
  }
  
  /// 绘制局部变形区域（平方衰减）
  void _drawLocalArea(Canvas canvas, Size size) {
    Logger.d(_logTag, '_drawLocalArea - 绘制局部变形区域');
    
    // 为每个受影响的点绘制平方衰减渐变
    for (var point in renderer.affectedPoints ?? []) {
      final center = Offset(point.x, point.y);
      final radius = 70.0 * renderer.intensity;
      
      final gradient = RadialGradient(
        colors: [
          Colors.yellow.withOpacity(0.5),
          Colors.yellow.withOpacity(0.3),
          Colors.yellow.withOpacity(0.0),
        ],
        stops: const [0.0, 0.5, 1.0],
      );
      
      final rect = Rect.fromCircle(center: center, radius: radius);
      final shader = gradient.createShader(rect);
      
      canvas.drawCircle(
        center,
        radius,
        Paint()..shader = shader,
      );
      
      // 绘制方向指示箭头
      if (renderer.paramValue != 0) {
        _drawDirectionArrow(canvas, point, renderer.paramValue);
      }
    }
  }
  
  /// 绘制三角剖分变形区域（德劳内三角剖分）
  void _drawTriangulationArea(Canvas canvas, Size size) {
    Logger.d(_logTag, '_drawTriangulationArea - 绘制三角剖分变形区域');
    
    if ((renderer.affectedPoints ?? []).length < 3) return;
    
    // 简化的三角剖分可视化
    final points = renderer.affectedPoints ?? [];
    final paint = Paint()
      ..color = Colors.purple.withOpacity(0.2)
      ..style = PaintingStyle.fill;
    
    // 创建简单的三角形（实际应用中应使用德劳内三角剖分算法）
    for (int i = 0; i < points.length - 2; i++) {
      final path = Path();
      path.moveTo(points[i].x, points[i].y);
      path.lineTo(points[i + 1].x, points[i + 1].y);
      path.lineTo(points[i + 2].x, points[i + 2].y);
      path.close();
      
      canvas.drawPath(path, paint);
    }
    
    // 绘制三角形边缘
    paint
      ..color = Colors.purple.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    for (int i = 0; i < points.length - 2; i++) {
      final path = Path();
      path.moveTo(points[i].x, points[i].y);
      path.lineTo(points[i + 1].x, points[i + 1].y);
      path.lineTo(points[i + 2].x, points[i + 2].y);
      path.close();
      
      canvas.drawPath(path, paint);
    }
    
    // 绘制控制点
    for (var point in points) {
      canvas.drawCircle(
        Offset(point.x, point.y),
        5.0,
        Paint()..color = Colors.purple,
      );
      
      // 绘制方向指示箭头
      if (renderer.paramValue != 0) {
        _drawDirectionArrow(canvas, point, renderer.paramValue);
      }
    }
  }
  
  /// 绘制方向指示箭头
  void _drawDirectionArrow(Canvas canvas, FeaturePoint point, double paramValue) {
    final arrowLength = 15.0 * renderer.intensity;
    final arrowWidth = 5.0 * renderer.intensity;
    
    // 确定箭头方向
    final direction = paramValue > 0 ? 1.0 : -1.0;
    
    // 判断点是在左侧还是右侧
    final isLeftSide = point.x < (renderer.areaBounds?.center.dx ?? 0);
    
    // 箭头方向：左侧点向左/右，右侧点向右/左
    final arrowDirection = isLeftSide ? -direction : direction;
    
    final start = Offset(point.x, point.y);
    final end = Offset(point.x + arrowLength * arrowDirection, point.y);
    
    // 箭头颜色基于变形区域类型
    Color arrowColor;
    switch (renderer.areaType) {
      case DeformationAreaType.vectorField:
        arrowColor = Colors.green;
        break;
      case DeformationAreaType.tps:
        arrowColor = Colors.red;
        break;
      case DeformationAreaType.mesh:
        arrowColor = Colors.blue;
        break;
      case DeformationAreaType.local:
        arrowColor = Colors.yellow.shade800;
        break;
      case DeformationAreaType.triangulation:
        arrowColor = Colors.purple;
        break;
      default:
        arrowColor = Colors.black;
    }
    
    // 绘制箭头主体
    final paint = Paint()
      ..color = arrowColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    canvas.drawLine(start, end, paint);
    
    // 绘制箭头头部
    final path = Path();
    path.moveTo(end.dx, end.dy);
    path.lineTo(
      end.dx - arrowDirection * arrowWidth,
      end.dy - arrowWidth,
    );
    path.lineTo(
      end.dx - arrowDirection * arrowWidth,
      end.dy + arrowWidth,
    );
    path.close();
    
    paint.style = PaintingStyle.fill;
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
