import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:ui' as ui;
import 'feature_points_data.dart';
import '../utils/logger.dart';

/// 变形区域可视化应用
class DeformationVisualizerApp extends StatefulWidget {
  const DeformationVisualizerApp({Key? key}) : super(key: key);

  @override
  _DeformationVisualizerAppState createState() => _DeformationVisualizerAppState();
}

class _DeformationVisualizerAppState extends State<DeformationVisualizerApp> {
  static const String _logTag = 'DeformationVisualizerApp';
  
  // 当前选中的区域
  String _selectedArea = 'face_contour';
  
  // 当前选中的参数
  String _selectedParameter = 'contour_tighten';
  
  // 参数值
  double _paramValue = 0.0;
  
  // 图像路径
  final String _imagePath = 'assets/test_face.jpg';
  
  // 图像
  ui.Image? _image;
  
  // 是否正在处理
  bool _isProcessing = false;
  
  // 区域参数映射
  final Map<String, List<String>> _areaParameters = {};
  
  @override
  void initState() {
    super.initState();
    _initAreaParameters();
    _loadImage();
  }
  
  // 初始化区域参数映射
  void _initAreaParameters() {
    // 从feature_points_data.dart中获取区域和参数
    final areaNames = getAllAreaNames();
    
    for (final area in areaNames) {
      final parameters = getAreaParameterNames(area);
      _areaParameters[area] = parameters;
    }
    
    // 如果没有数据，使用默认值
    if (_areaParameters.isEmpty) {
      _areaParameters.addAll({
        'face_contour': ['contour_tighten', 'chin_adjust', 'cheekbone_adjust', 'face_shape'],
        'nose': ['bridge_height', 'tip_adjust', 'nostril_width', 'base_height'],
        'eyes': ['double_fold', 'canthal_tilt', 'eye_bag_removal', 'outer_corner_lift'],
        'lips': ['lip_shape', 'mouth_corner'],
        'anti_aging': ['nasolabial_folds', 'wrinkle_removal', 'forehead_fullness', 'facial_firmness'],
      });
    }
  }
  
  // 加载图像
  Future<void> _loadImage() async {
    try {
      // 加载资源图片
      final ByteData data = await rootBundle.load(_imagePath);
      final Uint8List bytes = data.buffer.asUint8List();
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      
      setState(() {
        _image = frameInfo.image;
      });
    } catch (e) {
      Logger.log(_logTag, '_loadImage', '加载图像时出错: $e');
      if (mounted) {
        _showError('加载图像时出错: $e');
      }
    }
  }
  
  // 生成可视化
  void _generateVisualization() {
    // 在这里我们只是更新参数，实际的可视化会在CustomPaint中完成
    setState(() {
      // 更新状态以触发重绘
    });
  }
  
  // 显示错误信息
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    // 计算屏幕尺寸比例，模拟iPhone高端手机比例
    final screenSize = MediaQuery.of(context).size;
    final phoneRatio = 19.5 / 9.0; // iPhone 12/13/14 Pro的比例
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('面部美化变形区域可视化'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: Colors.black,
      body: Column(
        children: [
          // 图片区域（主要部分）
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              color: Colors.black,
              child: _image == null
                  ? const Center(child: CircularProgressIndicator(color: Colors.white))
                  : Center(
                      child: AspectRatio(
                        aspectRatio: phoneRatio,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: SizedBox(
                            width: _image!.width.toDouble(),
                            height: _image!.height.toDouble(),
                            child: CustomPaint(
                              painter: DeformationVisualizerPainter(
                                image: _image!,
                                area: _selectedArea,
                                parameter: _selectedParameter,
                                value: _paramValue,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
            ),
          ),
          
          // 控制面板（底部）
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 状态信息
                Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Text(
                    '${_getAreaDisplayName(_selectedArea)} - ${_getParameterDisplayName(_selectedArea, _selectedParameter)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                
                // 区域和参数选择（水平排列）
                Row(
                  children: [
                    // 区域选择
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '区域',
                            style: TextStyle(color: Colors.white70),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              color: Colors.grey[800],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _selectedArea,
                                isExpanded: true,
                                dropdownColor: Colors.grey[800],
                                icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                                style: const TextStyle(color: Colors.white),
                                onChanged: (String? newValue) {
                                  if (newValue != null) {
                                    setState(() {
                                      _selectedArea = newValue;
                                      // 切换区域时，选择该区域的第一个参数
                                      _selectedParameter = _areaParameters[newValue]?.first ?? '';
                                      _generateVisualization();
                                    });
                                  }
                                },
                                items: _areaParameters.keys.map<DropdownMenuItem<String>>((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: Text(_getAreaDisplayName(value)),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // 参数选择
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '参数',
                            style: TextStyle(color: Colors.white70),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              color: Colors.grey[800],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: _selectedParameter,
                                isExpanded: true,
                                dropdownColor: Colors.grey[800],
                                icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
                                style: const TextStyle(color: Colors.white),
                                onChanged: (String? newValue) {
                                  if (newValue != null) {
                                    setState(() {
                                      _selectedParameter = newValue;
                                      _generateVisualization();
                                    });
                                  }
                                },
                                items: (_areaParameters[_selectedArea] ?? []).map<DropdownMenuItem<String>>((String value) {
                                  return DropdownMenuItem<String>(
                                    value: value,
                                    child: Text(_getParameterDisplayName(_selectedArea, value)),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // 参数值滑块
                Row(
                  children: [
                    const Text(
                      '强度',
                      style: TextStyle(color: Colors.white70),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: SliderTheme(
                        data: SliderThemeData(
                          activeTrackColor: Colors.blue,
                          inactiveTrackColor: Colors.grey[600],
                          thumbColor: Colors.white,
                          overlayColor: Colors.blue.withOpacity(0.3),
                          valueIndicatorColor: Colors.blue,
                          valueIndicatorTextStyle: const TextStyle(color: Colors.white),
                        ),
                        child: Slider(
                          value: _paramValue,
                          min: -1.0,
                          max: 1.0,
                          divisions: 20,
                          label: _paramValue.toStringAsFixed(2),
                          onChanged: (double value) {
                            setState(() {
                              _paramValue = value;
                              _generateVisualization();
                            });
                          },
                        ),
                      ),
                    ),
                    Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: Text(
                        _paramValue.toStringAsFixed(2),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  // 获取区域显示名称
  String _getAreaDisplayName(String area) {
    final areaConfig = getAreaConfig(area);
    return areaConfig?.displayName ?? area;
  }
  
  // 获取参数显示名称
  String _getParameterDisplayName(String area, String parameter) {
    final paramConfig = getParameterConfig(area, parameter);
    return paramConfig?.displayName ?? parameter;
  }
}

/// 变形可视化绘制器
class DeformationVisualizerPainter extends CustomPainter {
  final ui.Image image;
  final String area;
  final String parameter;
  final double value;
  
  DeformationVisualizerPainter({
    required this.image,
    required this.area,
    required this.parameter,
    required this.value,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // 绘制原始图像
    paintImage(
      canvas: canvas,
      rect: Rect.fromLTWH(0, 0, size.width, size.height),
      image: image,
      fit: BoxFit.contain,
    );
    
    // 根据区域和参数绘制变形区域
    final Paint areaPaint = Paint()
      ..color = _getAreaColor(area)
      ..style = PaintingStyle.fill;
    
    // 这里简单示例，实际应该根据面部特征点和变形类型绘制
    // 面部轮廓
    if (area == 'face_contour') {
      if (parameter == 'contour_tighten') {
        // 绘制轮廓收紧区域
        canvas.drawOval(
          Rect.fromCenter(
            center: Offset(size.width / 2, size.height / 2),
            width: size.width * 0.8,
            height: size.height * 0.9,
          ),
          areaPaint,
        );
      } else if (parameter == 'chin_adjust') {
        // 绘制下巴调整区域
        canvas.drawOval(
          Rect.fromCenter(
            center: Offset(size.width / 2, size.height * 0.7),
            width: size.width * 0.3,
            height: size.height * 0.2,
          ),
          areaPaint,
        );
      }
      // 其他参数...
    }
    // 鼻部塑形
    else if (area == 'nose') {
      if (parameter == 'nostril_width') {
        // 绘制鼻翼宽度区域
        canvas.drawOval(
          Rect.fromCenter(
            center: Offset(size.width / 2, size.height * 0.4),
            width: size.width * 0.2,
            height: size.height * 0.1,
          ),
          areaPaint,
        );
      } else if (parameter == 'bridge_height') {
        // 绘制鼻梁高度区域
        canvas.drawRect(
          Rect.fromCenter(
            center: Offset(size.width / 2, size.height * 0.35),
            width: size.width * 0.05,
            height: size.height * 0.15,
          ),
          areaPaint,
        );
      }
      // 其他参数...
    }
    // 其他区域...
    
    // 绘制参数值指示
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: '${parameter}: ${value.toStringAsFixed(1)}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              offset: Offset(1, 1),
              blurRadius: 3,
              color: Colors.black,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(10, 10),
    );
  }
  
  @override
  bool shouldRepaint(DeformationVisualizerPainter oldDelegate) {
    return oldDelegate.image != image ||
           oldDelegate.area != area ||
           oldDelegate.parameter != parameter ||
           oldDelegate.value != value;
  }
  
  // 获取区域颜色
  Color _getAreaColor(String area) {
    final Map<String, Color> areaColors = {
      'face_contour': Color.fromRGBO(128, 0, 128, 0.3),  // 紫色半透明
      'nose': Color.fromRGBO(0, 0, 255, 0.3),           // 红色半透明
      'eyes': Color.fromRGBO(255, 0, 0, 0.3),           // 蓝色半透明
      'lips': Color.fromRGBO(255, 0, 255, 0.3),         // 粉色半透明
      'anti_aging': Color.fromRGBO(255, 165, 0, 0.3)    // 橙色半透明
    };
    
    return areaColors[area] ?? Colors.grey.withOpacity(0.3);
  }
}

/// 主函数
void main() {
  runApp(const MaterialApp(
    title: '面部美化变形区域可视化',
    debugShowCheckedModeBanner: false,
    home: DeformationVisualizerApp(),
  ));
}
