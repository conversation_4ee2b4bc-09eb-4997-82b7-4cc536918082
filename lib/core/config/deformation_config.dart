/// 变形参数配置
/// 
/// 集中管理所有面部区域的变形参数，确保前端和后端使用一致的参数值
class DeformationConfig {
  // 单例实例
  static final DeformationConfig _instance = DeformationConfig._internal();
  
  // 工厂构造函数
  factory DeformationConfig() {
    return _instance;
  }
  
  // 私有构造函数
  DeformationConfig._internal();
  
  /// 获取特定参数的影响半径（相对于图像宽度的比例）
  /// 
  /// [area] 面部区域，如'nose'、'eyes'等
  /// [param] 具体参数名称，如'nostril_width'等
  double getInfluenceRadius(String area, String param) {
    // 标准化区域和参数名称，以防大小写不一致
    final normalizedArea = area.toLowerCase();
    final normalizedParam = param.toLowerCase();
    
    // 鼻子区域参数
    if (normalizedArea == 'nose') {
      // 鼻翼宽度参数使用5%的影响半径
      if (normalizedParam == 'nostril_width') {
        return 0.05; // 5%的影响半径，确保变形效果严格限制在鼻翼区域
      }
      // 鼻尖调整参数
      else if (normalizedParam == 'tip_size') {
        return 0.15; // 15%的影响半径
      }
      // 鼻梁高度参数
      else if (normalizedParam == 'bridge_height') {
        return 0.2; // 20%的影响半径
      }
      // 其他鼻子参数
      else {
        return 0.15; // 默认15%的影响半径
      }
    }
    // 眼睛区域参数
    else if (normalizedArea == 'eyes') {
      // 双眼皮参数
      if (normalizedParam == 'double_eyelid') {
        return 0.1; // 10%的影响半径
      }
      // 其他眼睛参数
      else {
        return 0.15; // 默认15%的影响半径
      }
    }
    // 面部轮廓区域参数
    else if (normalizedArea == 'face') {
      // 脸型参数通常需要更大的影响半径
      return 0.3; // 30%的影响半径
    }
    // 默认影响半径
    return 0.25; // 默认25%的影响半径
  }
  
  /// 获取特定参数的变形强度系数
  /// 
  /// [area] 面部区域，如'nose'、'eyes'等
  /// [param] 具体参数名称，如'nostril_width'等
  double getStrengthFactor(String area, String param) {
    // 标准化区域和参数名称，以防大小写不一致
    final normalizedArea = area.toLowerCase();
    final normalizedParam = param.toLowerCase();
    
    // 鼻子区域参数
    if (normalizedArea == 'nose') {
      // 鼻翼宽度参数使用更大的强度系数
      if (normalizedParam == 'nostril_width') {
        return 2.0; // 鼻翼宽度使用2.0的强度系数
      }
      // 其他鼻子参数
      else {
        return 1.5; // 默认1.5的强度系数
      }
    }
    // 眼睛区域参数
    else if (normalizedArea == 'eyes') {
      return 1.0; // 眼睛区域使用1.0的强度系数
    }
    // 面部轮廓区域参数
    else if (normalizedArea == 'face') {
      return 3.0; // 面部轮廓使用3.0的强度系数
    }
    // 默认强度系数
    return 1.5; // 默认1.5的强度系数
  }
  
  /// 获取特定参数的权重因子
  /// 
  /// [area] 面部区域，如'nose'、'eyes'等
  /// [param] 具体参数名称，如'nostril_width'等
  double getWeightFactor(String area, String param) {
    // 标准化区域和参数名称，以防大小写不一致
    final normalizedArea = area.toLowerCase();
    final normalizedParam = param.toLowerCase();
    
    // 鼻子区域参数
    if (normalizedArea == 'nose') {
      // 鼻翼宽度参数
      if (normalizedParam == 'nostril_width') {
        return 2.0; // 鼻翼宽度使用2.0的权重因子
      }
      // 其他鼻子参数
      else {
        return 1.5; // 默认1.5的权重因子
      }
    }
    // 默认权重因子
    return 2.0; // 默认2.0的权重因子
  }
  
  /// 获取特定参数的衰减因子
  /// 
  /// [area] 面部区域，如'nose'、'eyes'等
  /// [param] 具体参数名称，如'nostril_width'等
  double getFalloffFactor(String area, String param) {
    // 标准化区域和参数名称，以防大小写不一致
    final normalizedArea = area.toLowerCase();
    final normalizedParam = param.toLowerCase();
    
    // 所有参数使用统一的衰减因子
    return 0.5; // 统一使用0.5的衰减因子
  }
  
  /// 获取特定参数的点影响力
  /// 
  /// [area] 面部区域，如'nose'、'eyes'等
  /// [param] 具体参数名称，如'nostril_width'等
  double getPointPower(String area, String param) {
    // 标准化区域和参数名称，以防大小写不一致
    final normalizedArea = area.toLowerCase();
    final normalizedParam = param.toLowerCase();
    
    // 所有参数使用统一的点影响力
    return 2.0; // 统一使用2.0的点影响力
  }
  
  /// 获取特定参数的比例因子
  /// 
  /// [area] 面部区域，如'nose'、'eyes'等
  /// [param] 具体参数名称，如'nostril_width'等
  double getScaleFactor(String area, String param) {
    // 标准化区域和参数名称，以防大小写不一致
    final normalizedArea = area.toLowerCase();
    final normalizedParam = param.toLowerCase();
    
    // 所有参数使用统一的比例因子
    return 3.0; // 统一使用3.0的比例因子
  }
}
