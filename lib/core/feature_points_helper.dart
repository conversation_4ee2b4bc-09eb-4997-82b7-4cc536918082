import 'dart:math' as math;
import 'dart:math' show Point, sqrt, pow;
import 'feature_points_data.dart';
import '../utils/logger.dart';
import 'feature_points_data.dart' as fpd;
import 'package:flutter/material.dart';
import '../beautify_feature/services/transformation_service.dart';

/// 面部特征区域类型
enum FeatureAreaType {
  none,         // 无区域
  face_contour, // 面部轮廓
  nose,         // 鼻部塑形
  eyes,         // 眼部美化
  lips,         // 唇部造型
  anti_aging,   // 抗衰冻龄
}

/// 特征点系统定义
class FeaturePointSystem {
  final List<int> primaryPoints;
  final List<int> secondaryPoints;
  final List<int> auxiliaryPoints;
  
  const FeaturePointSystem({
    required this.primaryPoints,
    required this.secondaryPoints,
    this.auxiliaryPoints = const [],
  });
  
  /// 获取所有特征点索引
  List<int> getAllPoints() {
    return [...primaryPoints, ...secondaryPoints, ...auxiliaryPoints];
  }
}

/// 面部轮廓复合系统定义
final Map<String, FeaturePointSystem> faceContourComplexSystems = {
  'contour_tighten': FeaturePointSystem(
    primaryPoints: [67, 297, 109, 338],
    secondaryPoints: [152, 175, 216, 436],
    auxiliaryPoints: [145, 146, 147, 374, 375, 376, 123, 352, 148, 149, 150, 151, 377, 378, 379],
  ),
  'face_shape': FeaturePointSystem(
    primaryPoints: [152, 67, 297],
    secondaryPoints: [109, 338],
    auxiliaryPoints: [216, 436],
  ),
  'v_chin': FeaturePointSystem(
    primaryPoints: [67, 297, 148, 377, 152],  // 核心下巴特征点：下颌角和下巴中心
    secondaryPoints: [145, 374, 146, 375, 147, 376], // 下颌线辅助点
    auxiliaryPoints: [149, 378, 150, 379], // 下巴辅助点
  ),
};

/// 中心线类
class _CenterLine {
  final Point<double> top;
  final Point<double> bottom;

  _CenterLine(this.top, this.bottom);

  /// 计算点到中心线的距离
  double distanceToPoint(Point<double> point) {
    // 使用点到线段的距离公式
    final x1 = top.x;
    final y1 = top.y;
    final x2 = bottom.x;
    final y2 = bottom.y;
    final x0 = point.x;
    final y0 = point.y;

    // 计算线段长度的平方
    final lineLength2 = (x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1);
    if (lineLength2 == 0) return math.sqrt((x0 - x1) * (x0 - x1) + (y0 - y1) * (y0 - y1));

    // 计算投影点的参数t
    final t = ((x0 - x1) * (x2 - x1) + (y0 - y1) * (y2 - y1)) / lineLength2;

    // 如果t超出[0,1]范围，则计算到端点的距离
    if (t < 0) return math.sqrt((x0 - x1) * (x0 - x1) + (y0 - y1) * (y0 - y1));
    if (t > 1) return math.sqrt((x0 - x2) * (x0 - x2) + (y0 - y2) * (y0 - y2));

    // 计算投影点坐标
    final projX = x1 + t * (x2 - x1);
    final projY = y1 + t * (y2 - y1);

    // 计算点到投影点的距离
    return math.sqrt((x0 - projX) * (x0 - projX) + (y0 - projY) * (y0 - projY));
  }

  /// 计算点在中心线的哪一侧
  /// 返回-1表示在左侧，1表示在右侧，0表示在线上
  int whichSide(Point<double> point) {
    final crossProduct = (bottom.x - top.x) * (point.y - top.y) - (bottom.y - top.y) * (point.x - top.x);
    if (crossProduct > 0) return -1; // 左侧
    if (crossProduct < 0) return 1;  // 右侧
    return 0; // 在线上
  }
}

class FeaturePointsHelper {
  static FeaturePointsHelper? _instance;
  static const String _logTag = 'FeaturePointsHelper';
  
  // 缓存
  final Map<String, List<int>> _featurePointsCache = {};
  final Map<String, List<int>> _featurePointIndexCache = {};
  
  /// 获取区域的特征点ID
  /// 
  /// 返回指定区域和参数名称的特征点ID列表
  List<int> getFeaturePointIds(String areaName, String paramName) {
    Logger.flowStart('FeaturePointsHelper', '获取特征点ID');
    Logger.flow('FeaturePointsHelper', '获取特征点ID', '🔍 [开始] 区域: $areaName, 参数: $paramName');
    
    List<int> result = [];
    
    try {
      // 获取参数特征点配置
      final pointsConfig = getParameterPointsConfig(areaName, paramName);
      
      // 合并所有类型的特征点
      if (pointsConfig.containsKey('primary')) {
        result.addAll(pointsConfig['primary']!);
      }
      
      if (pointsConfig.containsKey('secondary')) {
        result.addAll(pointsConfig['secondary']!);
      }
      
      if (pointsConfig.containsKey('auxiliary')) {
        result.addAll(pointsConfig['auxiliary']!);
      }
      
      // 去重
      result = result.toSet().toList();
      
      Logger.flow('FeaturePointsHelper', '获取特征点ID', '✅ [完成] 获取到 ${result.length} 个特征点ID');
    } catch (e) {
      Logger.flowError('FeaturePointsHelper', '获取特征点ID', '❌ [错误] 获取特征点ID失败: $e');
    }
    
    Logger.flowEnd('FeaturePointsHelper', '获取特征点ID');
    return result;
  }
  
  /// 获取特定区域类型和参数的特征点索引
  ///
  /// 返回一个包含该区域和参数相关的所有特征点索引的列表
  List<int> getFeaturePointIndexes(FeatureAreaType areaType, String paramName) {
    Logger.flowStart('FeaturePointsHelper', '获取特征点索引');
    Logger.flow('FeaturePointsHelper', '获取特征点索引', '🔍 [开始] 区域类型: $areaType, 参数: $paramName');
    
    // 获取区域名称
    String areaName = _getAreaName(areaType);
    if (areaName.isEmpty) {
      Logger.flowError('FeaturePointsHelper', '获取特征点索引', '❌ [错误] 无效的区域类型: $areaType');
      Logger.flowEnd('FeaturePointsHelper', '获取特征点索引');
      return [];
    }
    
    // 特殊处理鼻翼宽度参数
    if (areaName == 'nose' && paramName == 'nostril_width') {
      Logger.flow('FeaturePointsHelper', '获取特征点索引', '⚠️ [特殊处理] 鼻翼宽度参数');
      
      // 使用feature_points_data.dart中的鼻翼宽度特征点
      final Set<int> nostrilPoints = {};
      
      // 获取鼻翼宽度参数配置
      final noseConfig = beautyAreaConfigs['nose'];
      if (noseConfig != null) {
        final nostrilConfig = noseConfig.parameters['nostril_width'];
        if (nostrilConfig != null) {
          Logger.flow('FeaturePointsHelper', '获取特征点索引', '✅ [添加] 鼻翼宽度主特征点: ${nostrilConfig.primaryPoints.length}个');
          nostrilPoints.addAll(nostrilConfig.primaryPoints);
          
          Logger.flow('FeaturePointsHelper', '获取特征点索引', '✅ [添加] 鼻翼宽度次特征点: ${nostrilConfig.secondaryPoints.length}个');
          nostrilPoints.addAll(nostrilConfig.secondaryPoints);
          
          Logger.flow('FeaturePointsHelper', '获取特征点索引', '✅ [添加] 鼻翼宽度辅助特征点: ${nostrilConfig.auxiliaryPoints.length}个');
          nostrilPoints.addAll(nostrilConfig.auxiliaryPoints);
        }
      }
      
      final result = nostrilPoints.toList();
      Logger.flow('FeaturePointsHelper', '获取特征点索引', '✅ [完成] 鼻翼宽度参数特征点总数: ${result.length}');
      Logger.flow('FeaturePointsHelper', '获取特征点索引', '📋 特征点索引: $result');
      Logger.flowEnd('FeaturePointsHelper', '获取特征点索引');
      return result;
    }
    
    // 使用已有的getParameterPoints方法获取特征点
    List<int> result = getParameterPoints(areaName, paramName);
    
    Logger.flow('FeaturePointsHelper', '获取特征点索引', '✅ [完成] 获取到 ${result.length} 个特征点索引');
    Logger.flow('FeaturePointsHelper', '获取特征点索引', '📋 特征点索引: $result');
    Logger.flowEnd('FeaturePointsHelper', '获取特征点索引');
    return result;
  }

  /// 清除参数特征点缓存
  /// 
  /// 清除所有缓存的特征点索引，强制下次重新获取
  void clearParameterPointsCache() {
    Logger.flowStart('特征点辅助工具', 'clearParameterPointsCache');
    
    final cacheCount = _featurePointIndexCache.length;
    _featurePointIndexCache.clear();
    _loggedParams.clear();
    
    Logger.flow('特征点辅助工具', 'clearParameterPointsCache', '已清除 $cacheCount 个缓存项');
    Logger.flowEnd('特征点辅助工具', 'clearParameterPointsCache');
  }

  /// 获取指定参数的特征点
  /// 
  /// 返回一个包含该参数特征点 ID 的列表
  List<int> getParameterPoints(String areaName, String paramName) {
    Logger.flowStart('特征点辅助工具', '获取参数特征点');
    Logger.flow('特征点辅助工具', '获取参数特征点', '区域名称: $areaName, 参数名称: $paramName');
    
    // 生成缓存键
    final cacheKey = '$areaName:$paramName';
    
    // 检查缓存中是否已有结果
    if (_featurePointIndexCache.containsKey(cacheKey)) {
      final cachedResult = _featurePointIndexCache[cacheKey]!;
      Logger.flow('特征点辅助工具', '获取参数特征点', '从缓存中获取结果，共 ${cachedResult.length} 个特征点');
      Logger.flowEnd('特征点辅助工具', '获取参数特征点');
      return cachedResult;
    }
    
    // 🔧 [关键修复] 特殊处理鼻尖调整参数 - 使用正确的MediaPipe特征点索引
    if (areaName == 'nose' && paramName == 'tip_adjust') {
      Logger.flow('特征点辅助工具', '获取参数特征点', '⚠️ [特殊处理] 鼻尖调整参数 - 使用正确的MediaPipe索引');
      Logger.flow('特征点辅助工具', '获取参数特征点', '🔧 [强制清除] 清除可能的错误缓存');
      
      // 强制清除该参数的缓存，确保不使用错误的缓存结果
      _featurePointIndexCache.remove(cacheKey);
      
      // 🔧 [修复] 使用正确的MediaPipe鼻尖相关特征点索引
      // 这些索引与鼻翼宽度变形中使用的索引保持一致，确保都是正确的MediaPipe索引
      final List<int> correctTipIndices = [
        94,   // 鼻尖中心点
        19,   // 鼻基底中心点  
        114,  // 左鼻尖外侧点
        343,  // 右鼻尖外侧点
        129,  // 左鼻翼点
        358,  // 右鼻翼点
        219,  // 左鼻翼外缘点
        439,  // 右鼻翼外缘点
      ];
      
      Logger.flow('特征点辅助工具', '获取参数特征点', '✅ [修复] 使用正确的MediaPipe鼻尖特征点索引: ${correctTipIndices.length}个');
      Logger.flow('特征点辅助工具', '获取参数特征点', '📋 正确索引: $correctTipIndices');
      
      final result = correctTipIndices;
      
      // 将结果存入缓存
      _featurePointIndexCache[cacheKey] = result;
      
      Logger.flow('特征点辅助工具', '获取参数特征点', '✅ [完成] 鼻尖调整参数特征点总数: ${result.length}');
      Logger.flow('特征点辅助工具', '获取参数特征点', '📋 特征点索引: $result');
      Logger.flowEnd('特征点辅助工具', '获取参数特征点');
      return result;
    }
    
    // 🔧 [关键修复] 特殊处理V型下巴参数 - 使用完整的面部轮廓特征点集合
    if (areaName == 'face_contour' && paramName == 'v_chin') {
      Logger.flow('特征点辅助工具', '获取参数特征点', '⚠️ [特殊处理] V型下巴参数 - 使用完整面部轮廓特征点实现自然过渡');
      
      // 强制清除该参数的缓存，确保使用新的完整特征点集合
      _featurePointIndexCache.remove(cacheKey);
      
      // 🔧 [重新设计] 基于医美面部轮廓塑形原理的完整特征点集合
      // V型下巴需要从太阳穴到下巴的完整轮廓线，实现自然过渡
      final List<int> completeVChinIndices = [
        // 1. 太阳穴和颧骨区域 - 上部轮廓起点
        139, 368,   // 太阳穴主点 (左右对称)
        71, 301,    // 太阳穴辅助点 (左右对称)
        216, 436,   // 颧骨主点 (左右对称)
        123, 352,   // 颧骨辅助点1 (左右对称)
        50, 280,    // 颧骨辅助点2 (左右对称)
        207, 427,   // 颧骨辅助点3 (左右对称)
        187, 411,   // 颧骨辅助点4 (左右对称)
        
        // 2. 面颊中部区域 - 中部轮廓过渡
        67, 297,    // 颌角 (左右对称)
        109, 338,   // 面颊中部 (左右对称)
        104, 333,   // 中颊点对 (左右对称)
        58, 288,    // 面颊支撑点 (左右对称)
        54, 284,    // 下颌线中间点2 (左右对称)
        
        // 3. 下颌线区域 - 关键轮廓线
        145, 374,   // 下颌线辅助点1 (左右对称)
        146, 375,   // 下颌线辅助点2 (左右对称)
        147, 376,   // 下颌线辅助点3 (左右对称)
        132, 361,   // 法令纹下部 (左右对称)
        169, 394,   // 面部下部轮廓点 (左右对称)
        
        // 4. 下巴区域 - 主要变形区域
        148, 377,   // 下巴辅助点1 (左右对称)
        149, 378,   // 下巴辅助点2 (左右对称)
        150, 379,   // 下巴辅助点3 (左右对称)
        176, // 左下颌轮廓（无对称点，单独处理）
        175, 18, 152, // 下巴中心和辅助点
        
        // 5. 轮廓起点 - 确保完整闭合
        162, 389,   // 下巴轮廓起点 (左右对称)
      ];
      
      Logger.flow('特征点辅助工具', '获取参数特征点', '✅ [重新设计] 使用完整V型下巴特征点索引: ${completeVChinIndices.length}个');
      Logger.flow('特征点辅助工具', '获取参数特征点', '📋 完整索引: $completeVChinIndices');
      Logger.flow('特征点辅助工具', '获取参数特征点', '🎯 覆盖区域: 太阳穴→颧骨→面颊→下颌→下巴，实现完整自然过渡');
      
      final result = completeVChinIndices;
      
      // 将结果存入缓存
      _featurePointIndexCache[cacheKey] = result;
      
      Logger.flow('特征点辅助工具', '获取参数特征点', '✅ [完成] V型下巴参数特征点总数: ${result.length}');
      Logger.flow('特征点辅助工具', '获取参数特征点', '📋 特征点索引: $result');
      Logger.flowEnd('特征点辅助工具', '获取参数特征点');
      return result;
    }
    
    // 检查是否已记录过详细日志
    final isFirstLog = !_loggedParams.contains(cacheKey);
    if (isFirstLog) {
      _loggedParams.add(cacheKey);
      Logger.flow('特征点辅助工具', '获取参数特征点', '区域: $areaName, 参数: $paramName');
    }
    
    // 获取参数配置
    final areaConfig = fpd.beautyAreaConfigs[areaName];
    if (areaConfig == null) {
      if (isFirstLog) {
        Logger.flowError('特征点辅助工具', '获取参数特征点', '未找到区域配置: $areaName');
        Logger.flowEnd('特征点辅助工具', '获取参数特征点');
      }
      return [];
    }
    
    final paramConfig = areaConfig.parameters[paramName];
    if (paramConfig == null) {
      if (isFirstLog) {
        Logger.flowError('特征点辅助工具', '获取参数特征点', '未找到参数配置: $areaName.$paramName');
        Logger.flowEnd('特征点辅助工具', '获取参数特征点');
      }
      return [];
    }
    
    // 创建一个集合用于存储所有点（自动去重）
    final Set<int> allPoints = {};
    
    // 添加主导点、协同点和支撑点
    if (isFirstLog) {
      Logger.flow('特征点辅助工具', '获取参数特征点', '添加主导点: ${paramConfig.primaryPoints.length}个');
    }
    allPoints.addAll(paramConfig.primaryPoints);
    
    if (isFirstLog) {
      Logger.flow('特征点辅助工具', '获取参数特征点', '添加协同点: ${paramConfig.secondaryPoints.length}个');
    }
    allPoints.addAll(paramConfig.secondaryPoints);
    
    if (isFirstLog) {
      Logger.flow('特征点辅助工具', '获取参数特征点', '添加支撑点: ${paramConfig.auxiliaryPoints.length}个');
    }
    allPoints.addAll(paramConfig.auxiliaryPoints);
    
    // 所有区域都使用feature_points_data.dart中的统一配置
    // 不再需要特殊处理，因为所有特征点已经在上面添加过了
    
    if (areaName == 'nose') {
      // 鼻部参数名称映射
      String mappedParamName = paramName;
      if (paramName == 'bridge_height' || paramName == 'bridgeHeight') {
        mappedParamName = 'nasal_midline';
        Logger.flow('特征点辅助工具', '获取参数特征点', '鼻部参数映射: $paramName -> $mappedParamName');
      } else if (paramName == 'bridge_width' || paramName == 'bridgeWidth') {
        mappedParamName = 'nasal_midline';
        Logger.flow('特征点辅助工具', '获取参数特征点', '鼻部参数映射: $paramName -> $mappedParamName');
      } else if (paramName == 'tip_adjust' || paramName == 'tipAdjust' || paramName == 'tip_height') {
        mappedParamName = 'tip_complex';
        Logger.flow('特征点辅助工具', '获取参数特征点', '鼻部参数映射: $paramName -> $mappedParamName');
      } else if (paramName == 'base_height' || paramName == 'baseHeight') {
        mappedParamName = 'base_complex';
        Logger.flow('特征点辅助工具', '获取参数特征点', '鼻部参数映射: $paramName -> $mappedParamName');
      }
      
      // 特殊处理鼻翼宽度参数
      if (paramName == 'nostril_width') {
        if (isFirstLog) {
          Logger.flow('特征点辅助工具', '获取参数特征点', '特殊处理鼻翼宽度参数');
          Logger.flow('特征点辅助工具', '获取参数特征点', '使用预定义的鼻翼特征点');
        }
        
        // 使用预定义的鼻翼特征点集合
        // 这些点在nose_transform.dart的nostril_width参数配置中定义
        final nostrilPoints = [
          115, 344,  // 左右鼻翼内侧点（主导点）
          220, 440,  // 左右鼻翼软骨点（协同点）
          79, 309,   // 左右鼻孔外缘点（支撑点）
          129, 358, 219, 439 // 额外的鼻翼点
        ];
        
        if (isFirstLog) {
          Logger.flow('特征点辅助工具', '获取参数特征点', '使用预定义的鼻翼特征点: ${nostrilPoints.length} 个点');
          for (final index in nostrilPoints) {
            Logger.flow('特征点辅助工具', '获取参数特征点', '预定义特征点索引: $index');
          }
        }
        
        // 将预定义特征点添加到结果中
        allPoints.addAll(nostrilPoints);
        
        if (isFirstLog) {
          Logger.flow('特征点辅助工具', '获取参数特征点', '鼻翼宽度参数特征点总数: ${allPoints.length}');
        }
      } else {
        // 其他鼻部参数使用正常复合系统
        // 添加详细日志，显示可用的鼻部参数键
        final noseConfig = beautyAreaConfigs['nose'];
        Logger.flow('特征点辅助工具', '获取参数特征点', '可用的鼻部参数: ${noseConfig?.parameters.keys.toList() ?? []}');
        Logger.flow('特征点辅助工具', '获取参数特征点', '当前查找的参数: $mappedParamName');
        
        // 使用feature_points_data.dart中的配置
        final paramConfig = noseConfig?.parameters[mappedParamName];
        
        if (paramConfig != null) {
          if (isFirstLog) {
            Logger.flow('特征点辅助工具', '获取参数特征点', '处理鼻部参数: $mappedParamName');
            Logger.flow('特征点辅助工具', '获取参数特征点', '添加主特征点: ${paramConfig.primaryPoints.length}个');
          }
          allPoints.addAll(paramConfig.primaryPoints);
          
          if (isFirstLog) {
            Logger.flow('特征点辅助工具', '获取参数特征点', '添加次特征点: ${paramConfig.secondaryPoints.length}个');
          }
          allPoints.addAll(paramConfig.secondaryPoints);
          
          if (isFirstLog) {
            Logger.flow('特征点辅助工具', '获取参数特征点', '添加辅助特征点: ${paramConfig.auxiliaryPoints.length}个');
          }
          allPoints.addAll(paramConfig.auxiliaryPoints);
        } else {
          Logger.flowWarning('特征点辅助工具', '获取参数特征点', '未找到鼻部参数配置: $mappedParamName');
          Logger.flow('特征点辅助工具', '获取参数特征点', '可用的鼻部参数: ${noseConfig?.parameters.keys.toList() ?? []}');
        }
      }
    }
    
    // 将结果转换为列表
    final result = allPoints.toList();
    
    // 将结果存入缓存
    _featurePointIndexCache[cacheKey] = result;
    
    if (isFirstLog) {
      Logger.flow('特征点辅助工具', '获取参数特征点', '参数特征点获取完成');
      Logger.flow('特征点辅助工具', '获取参数特征点', '区域: $areaName, 参数: $paramName');
      Logger.flow('特征点辅助工具', '获取参数特征点', '特征点总数量: ${result.length}');
      Logger.flow('特征点辅助工具', '获取参数特征点', '结果已存入缓存，缓存键: $cacheKey');
    }
    
    Logger.flowEnd('特征点辅助工具', '获取参数特征点');
    return result;
  }
  
  /// 获取参数配置中的特征点
  /// 
  /// 返回一个包含主导点、次要点和辅助点的映射
  Map<String, List<int>> getParameterPointsConfig(String areaName, String paramName) {
    final areaConfig = fpd.beautyAreaConfigs[areaName];
    if (areaConfig == null) {
      return {};
    }
    
    final paramConfig = areaConfig.parameters[paramName];
    if (paramConfig == null) {
      return {};
    }
    
    return {
      'primary': paramConfig.primaryPoints,
      'secondary': paramConfig.secondaryPoints,
      'auxiliary': paramConfig.auxiliaryPoints,
    };
  }

  /// 初始化特征点辅助工具
  Future<void> initialize() async {
    Logger.flowStart(_logTag, 'initialize');
    
    // 清除缓存
    _featurePointsCache.clear();
    _featurePointIndexCache.clear();
    
    Logger.flow(_logTag, 'initialize', '✅ 初始化完成');
    Logger.flowEnd(_logTag, 'initialize');
  }
  
  factory FeaturePointsHelper() {
    _instance ??= FeaturePointsHelper._internal();
    return _instance!;
  }
  
  FeaturePointsHelper._internal();
  
  // 不再使用缓存，只保留坐标信息
  List<Point<double>>? _coordinates;

  /// 面部中心线特征点
  static const List<int> centerLinePoints = [
    8,   // 额头中心
    168, // 鼻梁顶部
    6,   // 鼻尖
    195, // 上唇中心
    0,   // 下唇中心
    152, // 下巴尖
  ];

  // 使用已定义的 _logTag
  
  // 用于记录已经记录过详细日志的参数
  static final Set<String> _loggedParams = {};

  /// 设置特征点坐标
  void setCoordinates(List<Point<double>> coordinates) {
    // 清除所有缓存
    invalidateCache();
    // 设置新坐标
    _coordinates = coordinates;
    Logger.i('特征点辅助工具', '更新特征点坐标: ${coordinates.length} 个点');
  }

  /// 重置坐标信息
  void invalidateCache() {
    _coordinates = null;
    Logger.i('特征点辅助工具', '坐标信息已重置');
  }
  
  /// 强制刷新特征点配置
  /// 
  /// 由于不再使用缓存，此方法仅作为兼容保留
  void forceRefreshConfigCache() {
    Logger.i('特征点辅助工具', '不再使用缓存，每次都从配置中实时获取特征点');
  }
  
  /// 清除参数特征点缓存
  /// 清除参数缓存
  /// 
  /// 由于不再使用缓存，此方法仅作为兼容保留
  void clearParameterCache({String? areaName, String? paramName}) {
    if (areaName != null && paramName != null) {
      Logger.i('特征点辅助工具', '不再使用缓存，每次都从配置中实时获取特征点: $areaName.$paramName');
    } else if (areaName != null) {
      Logger.i('特征点辅助工具', '不再使用缓存，每次都从配置中实时获取特征点: $areaName');
    } else {
      Logger.i('特征点辅助工具', '不再使用缓存，每次都从配置中实时获取特征点');
    }
  }

  /// 获取区域名称
  String _getAreaName(FeatureAreaType type) {
    switch (type) {
      case FeatureAreaType.face_contour:
        return 'face_contour';
      case FeatureAreaType.nose:
        return 'nose';
      case FeatureAreaType.eyes:
        return 'eyes';
      case FeatureAreaType.lips:
        return 'lips';
      case FeatureAreaType.anti_aging:
        return 'anti_aging';
      case FeatureAreaType.none:
      default:
        Logger.w('特征点辅助工具', '未知的区域类型: $type');
        return '';
    }
  }

  /// 对称性校准
  /// 
  /// 根据区域配置中定义的对称点对信息，确保变形效果在面部两侧保持对称
  /// 如果点集中包含左侧点但缺少右侧点，会添加右侧点
  /// 如果点集中包含右侧点但缺少左侧点，会添加左侧点
  /// 
  /// [points] 原始特征点列表
  /// [type] 面部特征区域类型
  /// 
  /// 返回一个Map，键为特征点索引，值为变形向量
  Map<String, List<int>> symmetryCalibration(List<int> points, FeatureAreaType type) {
    // 暂时屏蔽特征点校准功能
    Logger.i('特征点校准', '对称性校准功能已暂时屏蔽');
    
    // 直接返回原始点，不进行校准
    return {
      'original': points,
      'calibrated': points,
      'added': <int>[],
      'removed': <int>[],
    };
  }

  /// 清除特征点缓存
  /// 
  /// 由于不再使用缓存，此方法仅作为兼容保留
  void clearCache({FeatureAreaType? areaType}) {
    if (areaType != null) {
      String areaName = _getAreaName(areaType);
      if (areaName.isNotEmpty) {
        Logger.i('特征点辅助工具', '不再使用缓存，每次都从配置中实时获取区域 $areaName 的特征点');
      }
    } else {
      Logger.i('特征点辅助工具', '不再使用缓存，每次都从配置中实时获取特征点');
    }
  }

  /// 获取区域显示名称
  String getAreaDisplayName(FeatureAreaType type) {
    String areaName = _getAreaName(type);
    final areaConfig = fpd.beautyAreaConfigs[areaName];
    return areaConfig?.displayName ?? '';
  }
  
  /// 输出区域的对称点对信息
  /// 
  /// 在终端调试日志中详细输出中心点和每一对对称点[a,b]
  void logSymmetryPairsForArea(String areaName, String paramName) {
    try {
      // 获取区域配置
      final areaConfig = fpd.beautyAreaConfigs[areaName];
      if (areaConfig == null) {
        Logger.e('对称点对', '未找到区域配置: $areaName');
        return;
      }
      
      // 获取区域的中心点
      final centerPoint = areaConfig.region.centerPoint;
      Logger.i('对称点对', '✨ 区域: $areaName, 参数: $paramName');
      Logger.i('对称点对', '⭐ 中心点: $centerPoint');
      
      // 获取对称点对
      List<Map<String, int>> symmetricPairs = [];
      
      // 根据区域类型获取对应的对称点对
      // 所有区域都使用feature_points_data.dart中的配置
      symmetricPairs = areaConfig.region.symmetricPairs;
      
      if (symmetricPairs.isEmpty) {
        Logger.w('对称点对', '区域 $areaName 没有定义对称点对');
        return;
      }
      
      // 获取实际特征点数量
      final transformationService = TransformationService.instance;
      final points = transformationService.getFeaturePoints();
      final maxIndex = (points == null || points.isEmpty) ? 0 : points.length - 1;
      
      // 过滤无效的对称点对
      List<Map<String, int>> validPairs = [];
      for (final pair in symmetricPairs) {
        final left = pair['left'] as int;
        final right = pair['right'] as int;
        
        // 只保留有效的特征点索引
        if (left >= 0 && left <= maxIndex && right >= 0 && right <= maxIndex) {
          validPairs.add(pair);
        }
      }
      
      // 创建格式化的对称点对字符串
      String pairsOutput = '';
      for (final pair in validPairs) {
        final left = pair['left'];
        final right = pair['right'];
        pairsOutput += '[$left,$right]';
      }
      
      // 输出完整的对称点对列表
      Logger.i('对称点对', '⭐ 有效对称点对: $pairsOutput');
      
      // 同时保留详细输出，便于调试
      for (int i = 0; i < validPairs.length; i++) {
        final pair = validPairs[i];
        final left = pair['left'];
        final right = pair['right'];
        Logger.i('对称点对', '  • 对 ${i+1}: [$left,$right]');
      }
      
      // 获取参数配置
      final paramConfig = areaConfig.parameters[paramName];
      if (paramConfig != null) {
        // 输出参数的主要点
        Logger.i('对称点对', '⭐ 参数 $paramName 的主要点:');
        for (final point in paramConfig.primaryPoints) {
          Logger.i('对称点对', '  • 主要点: $point');
        }
        
        // 输出参数的次要点
        if (paramConfig.secondaryPoints.isNotEmpty) {
          Logger.i('对称点对', '⭐ 参数 $paramName 的次要点:');
          for (final point in paramConfig.secondaryPoints) {
            Logger.i('对称点对', '  • 次要点: $point');
          }
        }
      }
    } catch (e) {
      Logger.e('对称点对', '输出对称点对信息时出错: $e');
    }
  }

  /// 计算特定区域特定参数的变形
  /// 
  /// 根据区域、参数名和参数值计算特征点的变形向量
  /// 
  /// [area] 区域名称
  /// [paramName] 参数名称
  /// [value] 参数当前值
  /// [oldValue] 参数旧值
  /// [landmarks] 特征点坐标列表
  /// 
  /// 返回一个Map，键为特征点索引，值为变形向量
  Map<int, Offset> calculateDeformation({
    required String area,
    required String paramName,
    required double value,
    required double oldValue,
    required List<Map<String, dynamic>> landmarks,
  }) {
    Logger.i('特征点辅助工具', '计算变形 | 区域: $area | 参数: $paramName | 值: $value | 旧值: $oldValue');
    
    // 将Map类型的landmarks转换为Point类型
    List<Point<double>> points = [];
    for (var landmark in landmarks) {
      if (landmark.containsKey('x') && landmark.containsKey('y')) {
        points.add(Point(landmark['x'] as double, landmark['y'] as double));
      }
    }
    
    // 设置坐标信息，用于后续计算
    setCoordinates(points);
    
    // 调用feature_points_data.dart中的calculateDeformation方法
    // 使用正确的参数名称和类型
    return fpd.calculateDeformation(area, paramName, value);
  }
  
  /// 获取对称点对
  /// 
  /// 根据特征点列表获取对称的点对
  /// 
  /// [points] 特征点列表
  /// 
  /// 返回对称点对列表，每个元素是一个包含两个点索引的列表
  List<List<int>> getSymmetricPointPairs(List<int> points) {
    Logger.i('特征点辅助工具', '获取对称点对 | 点数量: ${points.length}');
    
    // 获取所有可能的对称点对
    List<List<int>> result = [];
    
    // 从所有区域配置中查找对称点对
    for (final areaName in ['face_contour', 'eyes', 'nose', 'lips', 'anti_aging']) {
      final areaConfig = beautyAreaConfigs[areaName];
      final symmetryPairs = areaConfig?.region.symmetricPairs ?? [];
      
      for (var pair in symmetryPairs) {
        final left = pair['left'] as int;
        final right = pair['right'] as int;
        
        // 如果两个点都在输入列表中，添加到结果中
        if (points.contains(left) && points.contains(right)) {
          result.add([left, right]);
        }
      }
    }
    
    Logger.i('特征点辅助工具', '找到对称点对: ${result.length}对');
    return result;
  }

  /// 获取指定区域的特征点列表
  /// 
  /// 通过汇总该区域所有参数的特征点，进行去重后返回
  List<int> getAreaPoints(FeatureAreaType type) {
    Logger.flowStart('特征点辅助工具', '获取区域特征点');
    Logger.flow('特征点辅助工具', '获取区域特征点', '区域类型: $type');
    
    String areaName = _getAreaName(type);
    if (areaName.isEmpty) {
      Logger.flowError('特征点辅助工具', '获取区域特征点', '区域名称为空，无法获取特征点');
      Logger.flowEnd('特征点辅助工具', '获取区域特征点');
      return [];
    }
    
    // 获取区域配置
    final areaConfig = beautyAreaConfigs[areaName];
    if (areaConfig == null) {
      Logger.flowError('特征点辅助工具', '获取区域特征点', '未找到区域配置: $areaName');
      Logger.flowEnd('特征点辅助工具', '获取区域特征点');
      return [];
    }
    
    // 创建一个集合用于存储所有点（自动去重）
    final Set<int> allPoints = {};
    
    // 遍历该区域的所有参数
    areaConfig.parameters.forEach((paramName, _) {
      // 获取该参数的特征点（直接从配置中获取，不使用缓存）
      final paramPoints = getParameterPoints(areaName, paramName);
      allPoints.addAll(paramPoints);
      Logger.flow('特征点辅助工具', '获取区域特征点', '参数 $paramName 特征点数量: ${paramPoints.length}');
    });
    
    // 获取原始点
    var originalPoints = allPoints.toList();
    
    // 执行对称性校准
    var calibrated = symmetryCalibration(originalPoints, type);
    
    // 添加详细日志
    Logger.flow('特征点辅助工具', '获取区域特征点', '区域 $areaName 特征点获取完成');
    Logger.flow('特征点辅助工具', '获取区域特征点', '原始点数量: ${allPoints.length}');
    Logger.flow('特征点辅助工具', '获取区域特征点', '校准后点数量: ${calibrated['calibrated']?.length ?? 0}');
    Logger.flow('特征点辅助工具', '获取区域特征点', '新增对称点数量: ${calibrated['added']?.length ?? 0}');
    
    Logger.flowEnd('特征点辅助工具', '获取区域特征点');
    return calibrated['calibrated'] ?? [];
  }

  /// 获取指定区域的特征点ID列表
  /// 
  /// 通过汇总该区域所有参数的特征点ID，进行去重后返回
  List<int> getAreaPointIds(String areaName) {
    Logger.flowStart('特征点辅助工具', '获取区域特征点ID');
    Logger.flow('特征点辅助工具', '获取区域特征点ID', '区域名称: $areaName');
    
    // 获取区域配置
    final areaConfig = beautyAreaConfigs[areaName];
    if (areaConfig == null) {
      Logger.flowError('特征点辅助工具', '获取区域特征点ID', '未找到区域配置: $areaName');
      Logger.flowEnd('特征点辅助工具', '获取区域特征点ID');
      return [];
    }
    
    // 创建一个集合用于存储所有点ID（自动去重）
    final Set<int> allPointIds = {};
    
    // 遍历该区域的所有参数
    areaConfig.parameters.forEach((paramName, _) {
      // 获取该参数的特征点ID
      final paramPointIds = getFeaturePointIds(areaName, paramName);
      allPointIds.addAll(paramPointIds);
      Logger.flow('特征点辅助工具', '获取区域特征点ID', '参数 $paramName 特征点ID数量: ${paramPointIds.length}');
    });
    
    // 添加详细日志
    Logger.flow('特征点辅助工具', '获取区域特征点ID', '区域 $areaName 特征点ID获取完成');
    Logger.flow('特征点辅助工具', '获取区域特征点ID', '总特征点ID数量: ${allPointIds.length}');
    
    Logger.flowEnd('特征点辅助工具', '获取区域特征点ID');
    return allPointIds.toList();
  }
}
