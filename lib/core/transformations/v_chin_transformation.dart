import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/feature_point.dart';
import '../../utils/logger.dart';
import 'transformation_strategy.dart';
import '../deformation_cache_manager.dart';
import '../transform_type.dart';
import 'dart:typed_data';

/// V型下巴变形策略实现
/// 【修复版本】统一对称性逻辑，确保完美的左右对称变形
/// 采用动态强度算法和断层修复技术，实现自然的V型下巴效果
class VChinTransformation extends TransformationStrategy {
  static const String _logTag = 'VChinTransformation';

  @override
  String get logTag => _logTag;

  @override
  String get parameterName => 'v_chin';

  // 固定步长值，用于所有变形计算
  static const double fixedStepSize = 0.2;

  @override
  void applyFeaturePointTransformation(List<FeaturePoint> featurePoints,
      List<int> pointIndexes, double value, double intensity,
      {double? facialCenterLineX, bool? isIncreasing}) {
    Logger.flowStart(_logTag, 'applyFeaturePointTransformation');

    Logger.flow(_logTag, 'applyFeaturePointTransformation', 
        '开始应用V型下巴变形 - 统一对称性逻辑实现');

    // 根据用户点击的是加号还是减号按钮来确定变形方向
    if (isIncreasing == null) {
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
      return;
    }

    // 根据点击加号或减号确定变形方向
    double direction = isIncreasing ? 1.0 : -1.0;
    // 【修正】使用固定步长计算变形因子，确保每次变形幅度一致
    final deformationFactor = direction * fixedStepSize;

    // 记录变形方向和预期效果
    if (isIncreasing!) {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📈 点击加号: 方向系数: $direction, 预期效果: 下颌角内收，形成V型下巴');
    } else {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📉 点击减号: 方向系数: $direction, 预期效果: 下颌角外扩，恢复原状');
    }

    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '变形因子: $deformationFactor (方向: ${isIncreasing! ? "加号" : "减号"}, 固定步长: $fixedStepSize)');

    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '📋 传入的特征点索引: $pointIndexes (共${pointIndexes.length}个)');

    // 【关键修复】优先使用全局缓存的面部中心线，确保绝对对称性
    double facialCenterX;
    
    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '✅ 使用全局缓存的面部中心线: $facialCenterX (永久缓存)');
    } else if (facialCenterLineX != null) {
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyFeaturePointTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      facialCenterX = calculateFacialCenterLineX(featurePoints);
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，重新计算可能导致不对称: $facialCenterX');
    }

    // 【连续性优化】应用变形到扩展的特征点集合，实现从下巴到面颊的连续变形
    for (var index in pointIndexes) {
      final pointIndex = featurePoints.indexWhere((p) => p.index == index);
      if (pointIndex != -1) {
        final oldPoint = featurePoints[pointIndex];

        // 计算点到中心线的距离
        final dx = oldPoint.x - facialCenterX;
        final distanceFromCenter = math.sqrt(dx * dx + math.pow(oldPoint.y - 300, 2)); // 假设脸部中心Y约为300

        // 【连续性计算】基于特征点位置计算变形强度
        double pointStrength = _calculateFeaturePointStrength(index, distanceFromCenter);

        // 【修复】使用统一的对称性计算函数，确保完美对称
        double offsetX = _calculateSymmetricOffset(
            dx, 
            1.0, // 特征点使用归一化强度1.0
            deformationFactor.abs() * pointStrength * 9.2, // 增加15%变形强度 (8.0 * 1.15 = 9.2)
            isIncreasing!
        );
        
        Logger.flow(_logTag, 'applyFeaturePointTransformation',
            '特征点 $index (${dx > 0 ? "右侧" : "左侧"}): 强度=$pointStrength, 偏移量=$offsetX');

        // 计算新的X坐标
        final newX = oldPoint.x + offsetX;

        // 替换原有特征点
        featurePoints[pointIndex] = updateFeaturePoint(oldPoint, newX: newX);

        // 记录变形后的坐标
        Logger.flow(_logTag, 'applyFeaturePointTransformation',
            '特征点 ID=${oldPoint.index} 变形后坐标: ($newX, ${oldPoint.y})，原坐标: (${oldPoint.x}, ${oldPoint.y})，偏移量: $offsetX');
      }
    }

    Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
  }

  /// 计算图像缩放矩形，确保图像按比例缩放并适应画布
  Rect _calculateScaledImageRect(ui.Image image, Size canvasSize) {
    final double imageAspectRatio = image.width / image.height;
    final double canvasAspectRatio = canvasSize.width / canvasSize.height;

    Rect dstRect;
    if (imageAspectRatio > canvasAspectRatio) {
      // 图像比画布更宽，以宽度为基准缩放
      final double scaledHeight = canvasSize.width / imageAspectRatio;
      final double topOffset = (canvasSize.height - scaledHeight) / 2;
      dstRect = Rect.fromLTWH(0, topOffset, canvasSize.width, scaledHeight);
    } else {
      // 图像比画布更高，以高度为基准缩放
      final double scaledWidth = canvasSize.height * imageAspectRatio;
      final double leftOffset = (canvasSize.width - scaledWidth) / 2;
      dstRect = Rect.fromLTWH(leftOffset, 0, scaledWidth, canvasSize.height);
    }

    return dstRect;
  }

  @override
  ui.Image? applyImageTransformation(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double radius,
      double value,
      double intensity,
      ui.Image? image,
      bool showDeformationArea,
      double? facialCenterLineX,
      {bool? isIncreasing, List<FeaturePoint>? currentFeaturePoints}) {
    Logger.flowStart(_logTag, 'applyImageTransformation');
    
    // 【关键调试】记录输入图像信息
    Logger.flow(_logTag, 'applyImageTransformation', '📥 输入图像信息: 哈希码=${image?.hashCode}, 尺寸=${image?.width}x${image?.height}');
    
    // 【修复累积变形】使用传入的累积图像进行变形
    Logger.flow(_logTag, 'applyImageTransformation', '📷 使用传入的累积图像进行变形: 哈希码=${image?.hashCode}');

    if (image == null) {
      Logger.flow(_logTag, 'applyImageTransformation', '❌ [错误] 图像为空，无法应用变形');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 即使参数值为0，也要执行变形操作，确保变形连续性
    if (value == 0.0) {
      Logger.i(_logTag, '✅ V型下巴变形 | 参数值为0，但仍然执行变形');
    }

    // 【关键修复】直接使用原始图像尺寸，不需要缩放计算
    final Rect dstRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final srcRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());

    Logger.flow(_logTag, 'applyImageTransformation', '开始执行V型下巴变形操作');

    // 【关键修复】优先使用全局缓存的面部中心线，确保绝对对称性
    double facialCenterX;
    
    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用全局缓存的面部中心线: $facialCenterX (永久缓存)');
    } else if (facialCenterLineX != null) {
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      facialCenterX = centerX;
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，使用画布中心可能导致不对称: $facialCenterX');
    }

    // 判断变形方向 - 根据用户点击的加号或减号来确定
    double direction = 0.0;

    if (isIncreasing != null) {
      direction = isIncreasing ? 1.0 : -1.0;
      Logger.flow(_logTag, 'applyImageTransformation',
          '🔍 变形方向根据用户点击确定: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 预期效果: ${isIncreasing ? "下颌角内收，形成V型下巴" : "下颌角外扩，恢复原状"}');
    } else {
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 【修正】使用固定步长计算变形因子，确保每次变形幅度一致
    double deformationFactor = direction * fixedStepSize;

    // 绘制面部中心线
    if (showDeformationArea) {
      Paint linePaint = Paint()
        ..color = Colors.green
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;
      canvas.drawLine(Offset(facialCenterX, 0),
          Offset(facialCenterX, size.height), linePaint);
    }

    // 【V型下巴专用】精确限制变形半径，只影响下巴区域
    double effectiveRadius = radius * 0.8; // 缩小至80%，只覆盖下巴和下颌区域

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形半径已调整: $effectiveRadius (原半径: ${radius})');

    // 【V型下巴专用】适度增强变形系数，聚焦下巴收紧效果
    final double enhancedFactor = deformationFactor * 230.0; // 增加15%变形强度 (200.0 * 1.15 = 230.0)

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形参数 - 中心点: ($facialCenterX, $centerY), 半径: $effectiveRadius, 变形系数: $deformationFactor, 增强系数: $enhancedFactor');

    // 绘制原始图像作为背景
    final paint = Paint()..filterQuality = FilterQuality.high;
    canvas.drawImageRect(image, srcRect, dstRect, paint);

    // 【关键修复】直接基于原始图像尺寸创建离屏画布
    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);

    // 【V型下巴专用】使用适中的网格密度，平衡性能和效果
    final int gridSizeX = 150; // 适中网格密度
    final int gridSizeY = 150; // 适中网格密度

    // 使用原始图像尺寸计算网格
    final double cellWidth = image.width.toDouble() / gridSizeX;
    final double cellHeight = image.height.toDouble() / gridSizeY;

    // 在原始图像坐标系下进行网格变形
    final double originalFacialCenterX = facialCenterX * image.width / size.width;
    final double originalCenterY = centerY * image.height / size.height;
    final double originalEffectiveRadius = effectiveRadius * image.width / size.width;

    // 【重要修正】先获取下巴关键特征点来准确定位变形区域
    Logger.flowStart(_logTag, '特征点定位变形区域');
    
    // 查找关键特征点坐标 - 使用所有下巴特征点来定位变形区域
    // 下巴关键点索引
    const int chinCenterIndex = 152; // 下巴中心点
    const int leftJawlineIndex = 148; // 左下颌线点
    const int rightJawlineIndex = 377; // 右下颌线点
    const int mouthBottomIndex = 17; // 嘴巴底线
    
    double chinCenterY = 0;
    double jawlineY = 0;
    double mouthY = 0;
    int pointCount = 0;
    
    // 从缓存中获取当前特征点
    List<FeaturePoint>? cachedFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
    List<FeaturePoint> pointsToUse = cachedFeaturePoints ?? [];
    
    // 遍历特征点查找下巴坐标
    for (var point in pointsToUse) {
      if (point.index == chinCenterIndex) {
        chinCenterY = point.y;
        pointCount++;
        Logger.flow(_logTag, '特征点定位', '找到下巴中心点: y=${chinCenterY.toStringAsFixed(2)}');
      } else if (point.index == leftJawlineIndex || point.index == rightJawlineIndex) {
        jawlineY += point.y;
        pointCount++;
        Logger.flow(_logTag, '特征点定位', '找到下颌线点 ${point.index}: y=${point.y.toStringAsFixed(2)}');
      } else if (point.index == mouthBottomIndex) {
        mouthY = point.y;
        pointCount++;
        Logger.flow(_logTag, '特征点定位', '找到嘴巴底部点: y=${mouthY.toStringAsFixed(2)}');
      }
    }
    
    // 计算平均下颌线高度
    if (pointCount > 0) {
      jawlineY = jawlineY / (pointCount - 1); // 减去下巴中心点
      Logger.flow(_logTag, '特征点定位', '下颌线平均高度: ${jawlineY.toStringAsFixed(2)}');
    }
    
    // 根据实际特征点定位设置变形区域 - 实现从颧骨到下巴的完整渐变
    // 1. 全面部区域 - 扩大到包含颧骨区域
    double faceCenterY = mouthY > 0 ? (mouthY + chinCenterY) / 2 : chinCenterY - originalEffectiveRadius * 0.3;
    final double cheekboneY = faceCenterY - originalEffectiveRadius * 0.6; // 颧骨区域
    final double fullFaceTopY = cheekboneY - originalEffectiveRadius * 0.4; // 从颧骨上方开始
    final double fullFaceBottomY = chinCenterY + originalEffectiveRadius * 0.2; // 限制在下巴下方一点
    
    // 2. 【V型下巴专用】精准定义嘴唇核心保护区域 - 严格保护嘴唇不受变形影响
    final double mouthCoreTopY = mouthY - originalEffectiveRadius * 0.08;    // 适度扩大保护区域高度
    final double mouthCoreBottomY = mouthY + originalEffectiveRadius * 0.06; // 适度扩大保护区域高度
    final double mouthCoreLeftX = originalFacialCenterX - originalEffectiveRadius * 0.12;  // 适度扩大保护区域宽度
    final double mouthCoreRightX = originalFacialCenterX + originalEffectiveRadius * 0.12; // 适度扩大保护区域宽度
    
    // 3. 下巴和下颌核心区域 - 主要变形区域
    final double chinTopY = mouthCoreBottomY; // 【修复】从嘴唇核心区域下边缘开始
    final double chinBottomY = chinCenterY + originalEffectiveRadius * 0.15; // 仅包含下巴，不包含脖子
    
    // 4. 新增：颧骨到嘴唇边缘的过渡区域定义
    final double cheekTransitionTopY = cheekboneY;
    final double cheekTransitionBottomY = mouthCoreTopY; // 【修复】到嘴唇核心区域上边缘结束
    
    Logger.flowEnd(_logTag, '特征点定位变形区域');
    
    Logger.flow(_logTag, 'applyImageTransformation', '【V型下巴专用】保护区域设置完成：嘴唇保护区域(${mouthCoreTopY.toStringAsFixed(1)} - ${mouthCoreBottomY.toStringAsFixed(1)}), 下巴变形区域(${chinTopY.toStringAsFixed(1)} - ${chinBottomY.toStringAsFixed(1)})');

    // 输出网格变形信息
    Logger.flow(_logTag, 'applyImageTransformation', '📊 网格变形设置');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 网格尺寸: ${gridSizeX}x${gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 原始图像坐标系: 中心线X=${originalFacialCenterX.toStringAsFixed(1)}, 中心Y=${originalCenterY.toStringAsFixed(1)}, 半径=${originalEffectiveRadius.toStringAsFixed(1)}');

    // 【对称性统计】记录左右两侧变形网格数量
    int leftSideGridCount = 0;
    int rightSideGridCount = 0;

    // 遍历网格
    for (int y = 0; y < gridSizeY; y++) {
      for (int x = 0; x < gridSizeX; x++) {
        // 在原始图像坐标系中计算网格单元
        final double left = x * cellWidth;
        final double top = y * cellHeight;
        final double right = (x + 1) * cellWidth;
        final double bottom = (y + 1) * cellHeight;

        // 计算网格单元中心点
        final double centerGridX = (left + right) / 2;
        final double centerGridY = (top + bottom) / 2;

        // 计算中心点到面部中心线的距离
        final double dx = centerGridX - originalFacialCenterX;
        final double distanceFromCenterLine = dx.abs();
        final double dy = centerGridY - originalCenterY;
        final distance = math.sqrt(dx * dx + dy * dy);

        // 计算变形量
        double offsetX = 0.0;

        // 【V型下巴专用】精确的保护区域判断
        bool isInNeckArea = centerGridY > chinBottomY + originalEffectiveRadius * 0.05;
        bool isInMouthProtectionArea = _isInMouthProtectionArea(centerGridX, centerGridY, originalFacialCenterX, mouthY, originalEffectiveRadius);
        bool isWithinFaceBoundary = _isWithinFaceBoundary(centerGridX, centerGridY, originalFacialCenterX, originalCenterY, originalEffectiveRadius);
        
        // 【关键改进】使用连续的变形强度计算，消除区域划分导致的断裂
        double continuousStrength = _calculateContinuousDeformationStrength(
            distance, 
            distanceFromCenterLine, 
            originalEffectiveRadius, 
            originalCenterY, 
            centerGridY
        );
        
        // 【V型下巴专用】基于精确保护区域判断是否应用变形
        bool shouldApplyDeformation = continuousStrength > 0.01 && // 强度阈值
                                      !isInNeckArea && // 保护脖子不受影响
                                      !isInMouthProtectionArea && // 【强化保护】嘴唇完全锁定不变形
                                      isWithinFaceBoundary; // 【严格边界】必须在面部轮廓线内
        
        if (shouldApplyDeformation) {
          // 【对称性统计】记录左右两侧变形网格数量
          if (dx > 0) {
            rightSideGridCount++;
          } else if (dx < 0) {
            leftSideGridCount++;
          }
          
          // 【V型下巴专用】基于连续强度的精确变形计算
          final double normalizedDistance = distanceFromCenterLine / originalEffectiveRadius;
          
          // 【边界渐变】在接近边界时减少变形强度，确保平滑过渡
          double boundaryFactor = _calculateBoundaryFactor(centerGridX, centerGridY, originalFacialCenterX, originalCenterY, originalEffectiveRadius);
          
          final double baseMagnitude = continuousStrength * boundaryFactor * deformationFactor.abs() * 138.0; // 增加15%变形强度 (120.0 * 1.15 = 138.0)
          
          // 【对称性计算】使用统一的对称性函数
          offsetX = _calculateSymmetricOffset(dx, normalizedDistance, baseMagnitude, isIncreasing!);

          // 在原始图像坐标系中计算变形后的位置
          final double deformedSrcX = centerGridX - offsetX;
          final double deformedSrcY = centerGridY; // Y坐标保持不变

          // 确保源坐标在图像范围内
          if (deformedSrcX >= 0 &&
              deformedSrcX < image.width &&
              deformedSrcY >= 0 &&
              deformedSrcY < image.height) {
            
            final srcUnitRect = Rect.fromLTWH(
                deformedSrcX - cellWidth / 2,
                deformedSrcY - cellHeight / 2, 
                cellWidth, 
                cellHeight);

            final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

            offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
          }
        } else {
          // 不在变形半径内，直接复制原始图像的对应部分
          final srcUnitRect = Rect.fromLTWH(
              centerGridX - cellWidth / 2,
              centerGridY - cellHeight / 2, 
              cellWidth, 
              cellHeight);

          final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

          offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
        }
      }
    }
    
    // 【对称性检查】输出左右两侧变形网格统计
    Logger.flow(_logTag, 'applyImageTransformation', 
        '📊 对称性统计: 左侧变形网格=${leftSideGridCount}个, 右侧变形网格=${rightSideGridCount}个, 差异=${(leftSideGridCount - rightSideGridCount).abs()}个');

    // 创建变形图像
    Logger.flow(_logTag, 'applyImageTransformation', '🎯 开始创建变形图像');
    final ui.Image finalImage = recorder.endRecording().toImageSync(image.width, image.height);
    
    Logger.flow(_logTag, 'applyImageTransformation', '📤 创建的变形图像信息:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 实际输出尺寸: ${finalImage.width}x${finalImage.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 哈希码: ${finalImage.hashCode}');
    
    // 将变形图像绘制到主画布上
    canvas.drawImageRect(
        finalImage,
        Rect.fromLTWH(0, 0, finalImage.width.toDouble(), finalImage.height.toDouble()),
        dstRect,
        paint);

    // 绘制变形区域边界
    if (showDeformationArea) {
      final deformationAreaRect = Rect.fromCenter(
          center: Offset(centerX, centerY),
          width: effectiveRadius * 2,
          height: effectiveRadius * 2);

      final borderPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawOval(deformationAreaRect, borderPaint);
    }

    // 验证图像尺寸一致性
    if (finalImage.width != image.width || finalImage.height != image.height) {
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 严重错误: 图像尺寸不一致! 输入=${image.width}x${image.height}, 输出=${finalImage.width}x${finalImage.height}');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return image;
    }

    // 保存变形状态到缓存
    List<FeaturePoint>? featurePointsToSave;
    
    if (currentFeaturePoints != null && currentFeaturePoints.isNotEmpty) {
      featurePointsToSave = currentFeaturePoints;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用传入的变形后特征点: ${currentFeaturePoints.length}个');
    } else {
      featurePointsToSave = DeformationCacheManager.getLatestDeformedFeaturePoints();
      Logger.flowWarning(_logTag, 'applyImageTransformation', 
          '⚠️ 警告: 没有传入变形后特征点，使用缓存数据');
    }

    // 设置最新的变形状态
    DeformationCacheManager.setLatestDeformedState(finalImage, featurePointsToSave);
    Logger.flow(_logTag, 'applyImageTransformation',
        '✅ 变形图像已保存到缓存 | 哈希码: ${finalImage.hashCode} | 尺寸: ${finalImage.width}x${finalImage.height}');

    Logger.flowEnd(_logTag, 'applyImageTransformation');
    return finalImage;
  }

  @override
  bool handleSpecialCases(
      List<FeaturePoint> featurePoints, double value, bool isIncreasing) {
    return false;
  }

  @override
  TransformType getRequiredTransformType() {
    return TransformType.local;
  }

  @override
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints,
      [double scaleX = 1.0]) {
    Logger.flowStart(_logTag, 'calculateFacialCenterLineX');

    // 【医美专业】使用下巴区域的对称特征点计算面部中心线
    // 优先使用下颌线的对称点确保V型下巴变形的准确性
    FeaturePoint? leftJawline;   // 左下颌线点 - 索引148
    FeaturePoint? rightJawline;  // 右下颌线点 - 索引377
    FeaturePoint? chinCenter;    // 下巴中心点 - 索引152

    // 遍历特征点，找出关键的对称特征点
    for (var point in featurePoints) {
      if (point.index == 148) {
        leftJawline = point;
      } else if (point.index == 377) {
        rightJawline = point;
      } else if (point.index == 152) {
        chinCenter = point;
      }
    }

    // 优先使用下颌线对称点计算中心线
    double facialCenterX;
    if (leftJawline != null && rightJawline != null) {
      facialCenterX = (leftJawline.x + rightJawline.x) / 2 * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '🎯 使用下颌线对称点计算中心线: $facialCenterX (左下颌线${leftJawline.index}: ${leftJawline.x}, 右下颌线${rightJawline.index}: ${rightJawline.x})');
    } else if (chinCenter != null) {
      // 备选方案：使用下巴中心点
      facialCenterX = chinCenter.x * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '🎯 使用下巴中心点计算中心线: $facialCenterX (下巴中心${chinCenter.index}: ${chinCenter.x})');
    } else {
      // 最后备选：使用所有特征点的平均值
      facialCenterX = 0.0;
      for (var point in featurePoints) {
        facialCenterX += point.x;
      }
      facialCenterX = (facialCenterX / featurePoints.length) * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用所有特征点的平均X坐标作为中心线: $facialCenterX');
    }

    Logger.flow(_logTag, 'calculateFacialCenterLineX', '【V型下巴】面部中心线已固定: $facialCenterX');
    Logger.flowEnd(_logTag, 'calculateFacialCenterLineX');

    return facialCenterX;
  }

  @override
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint,
      {double? newX, double? newY}) {
    Logger.flow(_logTag, 'updateFeaturePoint',
        '更新特征点 ID=${oldPoint.index} 坐标: (${newX ?? oldPoint.x}, ${newY ?? oldPoint.y})');
    return FeaturePoint(
        id: oldPoint.id,
        index: oldPoint.index,
        x: newX ?? oldPoint.x,
        y: newY ?? oldPoint.y,
        z: oldPoint.z);
  }

  @override
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY,
      {Color color = Colors.red}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // 绘制垂直中心线
    canvas.drawLine(
        Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);

    // 绘制水平中心线
    canvas.drawLine(Offset(0, centerY), Offset(size.width, centerY), paint);
  }

  /// 【V型下巴专用】精确的面部轮廓线边界检测 - 确保轮廓线之外完全不变形
  bool _isWithinFaceBoundary(double x, double y, double centerX, double centerY, double radius) {
    // 获取特征点进行精确边界检测
    List<FeaturePoint>? cachedFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
    
    if (cachedFeaturePoints != null && cachedFeaturePoints.isNotEmpty) {
      return _isWithinPreciseFacialContour(x, y, centerX, centerY, radius, cachedFeaturePoints);
    }
    
    // 备选方案：使用默认边界检测
    return _isWithinDefaultFaceBoundary(x, y, centerX, centerY, radius);
  }
  
  /// 【精细化轮廓检测】基于实际特征点的精确面部轮廓线边界检测
  bool _isWithinPreciseFacialContour(double x, double y, double centerX, double centerY, double radius, List<FeaturePoint> featurePoints) {
    // 【关键限制1】只允许下半脸区域变形
    if (y <= centerY) {
      return false; // 上半脸完全不允许变形
    }
    
    // 面部轮廓线关键特征点索引
    List<int> faceContourIndices = [
      // 下颌轮廓线
      172, 136, 150, 149, 176, 148, 152, 377, 400, 378, 379, 365, 397, 288, 361, 323,
      // 面部边界点
      10, 151, 9, 8, 168, 6, 234, 93, 132, 58, 172, 136, 150, 149, 176
    ];
    
    // 提取轮廓线特征点
    List<FeaturePoint> contourPoints = [];
    for (var point in featurePoints) {
      if (faceContourIndices.contains(point.index)) {
        contourPoints.add(point);
      }
    }
    
    if (contourPoints.isEmpty) {
      return _isWithinDefaultFaceBoundary(x, y, centerX, centerY, radius);
    }
    
    // 计算轮廓线边界
    double minX = contourPoints.map((p) => p.x).reduce(math.min);
    double maxX = contourPoints.map((p) => p.x).reduce(math.max);
    double minY = contourPoints.map((p) => p.y).reduce(math.min);
    double maxY = contourPoints.map((p) => p.y).reduce(math.max);
    
    // 基于实际轮廓线的边界检测，添加安全边距
    double safetyMargin = radius * 0.03; // 3%的安全边距，更紧密的保护
    
    // 严格的轮廓线边界检测
    bool isWithinContour = x >= (minX + safetyMargin) && x <= (maxX - safetyMargin) &&
                          y >= (minY + safetyMargin) && y <= (maxY - safetyMargin);
    
    // 额外的中心线保护
    final double distanceFromCenterLine = (x - centerX).abs();
    bool tooCloseToCenter = distanceFromCenterLine < radius * 0.12; // 12%的中心保护范围
    
    bool isValid = isWithinContour && !tooCloseToCenter;
    
    if (!isValid) {
      Logger.flow(_logTag, '_isWithinPreciseFacialContour', 
          '点(${x.toStringAsFixed(1)}, ${y.toStringAsFixed(1)})超出精确面部轮廓线边界或过于接近中心线');
    }
    
    return isValid;
  }
  
  /// 【备选方案】默认面部轮廓线边界检测
  bool _isWithinDefaultFaceBoundary(double x, double y, double centerX, double centerY, double radius) {
    // 计算到面部中心线的距离
    final double distanceFromCenterLine = (x - centerX).abs();
    
    // 【关键限制1】只允许下半脸区域变形
    if (y <= centerY) {
      return false; // 上半脸完全不允许变形
    }
    
    // 【关键限制2】基于实际面部轮廓的精确边界检测
    final double relativeY = (y - centerY) / radius;
    
    // 根据垂直位置动态调整允许的水平范围，模拟真实的面部轮廓
    double allowedHorizontalRange;
    
    if (relativeY <= 0.3) {
      // 靠近嘴部区域：较宽的轮廓
      allowedHorizontalRange = radius * 0.75; // 收紧边界
    } else if (relativeY <= 0.6) {
      // 下颌区域：中等宽度的轮廓
      allowedHorizontalRange = radius * 0.65; // 收紧边界
    } else if (relativeY <= 0.9) {
      // 下巴区域：较窄的轮廓
      allowedHorizontalRange = radius * 0.55; // 收紧边界
    } else {
      // 下巴尖端：最窄的轮廓
      allowedHorizontalRange = radius * 0.45; // 收紧边界
    }
    
    // 【严格边界检测】确保在面部轮廓线内部
    if (distanceFromCenterLine > allowedHorizontalRange) {
      return false; // 超出面部轮廓线，不允许变形
    }
    
    // 【额外安全边界】排除太靠近中心线的区域，避免影响面部结构
    if (distanceFromCenterLine < radius * 0.18) { // 增加中心保护范围
      return false; // 太靠近中心线，不允许变形
    }
    
    return true; // 在允许的变形范围内
  }

  /// 【V型下巴专用】精确的嘴唇保护区域检测 - 确保嘴唇完全锁定不变形
  bool _isInMouthProtectionArea(double x, double y, double centerX, double mouthY, double radius) {
    // 获取当前特征点用于精确定位嘴唇
    List<FeaturePoint>? cachedFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
    
    if (cachedFeaturePoints != null && cachedFeaturePoints.isNotEmpty) {
      return _isInPreciseLipEllipseArea(x, y, centerX, cachedFeaturePoints, radius);
    }
    
    // 备选方案：使用默认椭圆保护区域
    return _isInDefaultLipEllipseArea(x, y, centerX, mouthY, radius);
  }
  
  /// 【精细化嘴唇保护】基于实际特征点的精确椭圆形嘴唇保护区域
  bool _isInPreciseLipEllipseArea(double x, double y, double centerX, List<FeaturePoint> featurePoints, double radius) {
    // 关键嘴唇特征点索引 - 使用MediaPipe的标准索引
    Map<String, List<int>> lipLandmarks = {
      'upper_lip': [0, 11, 12, 13, 14, 15, 16, 17, 18, 200], // 上唇轮廓
      'lower_lip': [17, 18, 200, 269, 270, 267, 271, 272], // 下唇轮廓
      'lip_corners': [61, 84, 17, 314, 405, 320, 307, 291], // 嘴角
      'lip_center': [13, 14] // 嘴唇中心点
    };
    
    // 提取嘴唇特征点
    List<FeaturePoint> allLipPoints = [];
    for (var landmarkList in lipLandmarks.values) {
      for (var index in landmarkList) {
        var point = featurePoints.where((p) => p.index == index).firstOrNull;
        if (point != null) {
          allLipPoints.add(point);
        }
      }
    }
    
    if (allLipPoints.isEmpty) {
      return _isInDefaultLipEllipseArea(x, y, centerX, y, radius);
    }
    
    // 计算嘴唇的精确边界
    double minX = allLipPoints.map((p) => p.x).reduce(math.min);
    double maxX = allLipPoints.map((p) => p.x).reduce(math.max);
    double minY = allLipPoints.map((p) => p.y).reduce(math.min);
    double maxY = allLipPoints.map((p) => p.y).reduce(math.max);
    
    // 计算椭圆中心
    double ellipseCenterX = (minX + maxX) / 2;
    double ellipseCenterY = (minY + maxY) / 2;
    
    // 计算椭圆半径，精确贴合嘴唇形状（仅覆盖嘴唇，上下变窄）
    double lipWidth = maxX - minX;
    double lipHeight = maxY - minY;
    double ellipseA = (lipWidth * 0.55) + (radius * 0.022); // 水平半径：嘴唇宽度 + 10% + 最小保护边距
    double ellipseB = (lipHeight * 0.4) + (radius * 0.015); // 垂直半径：上下变窄，仅覆盖嘴唇厚度 + 最小保护边距
    
    // 椭圆保护区域检测
    double normalizedX = (x - ellipseCenterX) / ellipseA;
    double normalizedY = (y - ellipseCenterY) / ellipseB;
    double ellipseDistance = math.sqrt(normalizedX * normalizedX + normalizedY * normalizedY);
    
    bool isProtected = ellipseDistance <= 1.0;
    
    if (isProtected) {
      Logger.flow(_logTag, '_isInPreciseLipEllipseArea', 
          '点(${x.toStringAsFixed(1)}, ${y.toStringAsFixed(1)})在精确嘴唇椭圆保护区域内 - 中心:(${ellipseCenterX.toStringAsFixed(1)}, ${ellipseCenterY.toStringAsFixed(1)}), 半径:${ellipseA.toStringAsFixed(1)}x${ellipseB.toStringAsFixed(1)}');
    }
    
    return isProtected;
  }
  
  /// 【备选方案】默认椭圆形嘴唇保护区域
  bool _isInDefaultLipEllipseArea(double x, double y, double centerX, double mouthY, double radius) {
    // 计算到面部中心线的距离
    final double distanceFromCenterLine = (x - centerX).abs();
    
    // 【精准嘴唇椭圆保护区域】仅覆盖嘴唇形状，上下变窄
    final double ellipseA = radius * 0.11; // 水平半径：嘴唇宽度 + 10%
    final double ellipseB = radius * 0.045; // 垂直半径：上下变窄，仅覆盖嘴唇厚度
    
    // 椭圆保护区域检测
    final double normalizedX = distanceFromCenterLine / ellipseA;
    final double normalizedY = (y - mouthY) / ellipseB;
    final double ellipseDistance = math.sqrt(normalizedX * normalizedX + normalizedY * normalizedY);
    
    bool isProtected = ellipseDistance <= 1.0;
    
    if (isProtected) {
      Logger.flow(_logTag, '_isInDefaultLipEllipseArea', 
          '点(${x.toStringAsFixed(1)}, ${y.toStringAsFixed(1)})在默认椭圆嘴唇保护区域内');
    }
    
    return isProtected;
  }

  /// 【梯度过渡优化】计算丝滑的过渡效果 - 使用三阶贝塞尔曲线
  double _calculateSmoothTransition(double distanceRatio) {
    // 使用三阶贝塞尔曲线实现更自然的过渡
    final double t = 1.0 - distanceRatio;
    final double t2 = t * t;
    final double t3 = t2 * t;
    
    // 贝塞尔控制点，创建平滑的衰减曲线
    final double p0 = 1.0;  // 起始点
    final double p1 = 0.85; // 控制点1
    final double p2 = 0.3;  // 控制点2
    final double p3 = 0.0;  // 终点
    
    // 三阶贝塞尔曲线公式
    return p0 * t3 + 3 * p1 * t2 * (1 - t) + 3 * p2 * t * (1 - t) * (1 - t) + p3 * (1 - t) * (1 - t) * (1 - t);
  }

  /// 【连续性优化】基于距离的连续变形强度计算函数
  double _calculateContinuousDeformationStrength(double distanceFromCenter, double distanceFromCenterLine, double effectiveRadius, double centerY, double pointY) {
    // 基于到变形中心的距离计算基础强度
    final double distanceRatio = distanceFromCenter / effectiveRadius;
    if (distanceRatio >= 1.0) return 0.0;
    
    // 使用平滑的衰减函数
    double baseStrength = _calculateSmoothTransition(distanceRatio);
    
    // 基于垂直位置的连续强度调整
    final double verticalInfluence = _calculateVerticalInfluence(centerY, pointY, effectiveRadius);
    
    // 基于水平位置的连续强度调整
    final double horizontalInfluence = _calculateHorizontalInfluence(distanceFromCenterLine, effectiveRadius);
    
    return baseStrength * verticalInfluence * horizontalInfluence;
  }

  /// 【V型下巴专用】基于垂直位置的影响计算 - 只影响下半脸
  double _calculateVerticalInfluence(double centerY, double pointY, double effectiveRadius) {
    // 【关键限制】上半脸完全不参与变形
    if (pointY <= centerY) {
      return 0.0; // 上半脸强度为0
    }
    
    // 计算相对于面部中心的下方位置
    final double relativeY = (pointY - centerY) / effectiveRadius;
    
    // 下巴核心区域（相对位置 0.3 - 0.8）：最大强度
    if (relativeY >= 0.3 && relativeY <= 0.8) {
      return 1.0;
    }
    
    // 下颌过渡区域（相对位置 0.1 - 0.3）：逐渐增强
    if (relativeY >= 0.1 && relativeY < 0.3) {
      double t = (relativeY - 0.1) / 0.2; // 归一化到 [0, 1]
      return 0.4 + 0.6 * t; // 从 0.4 过渡到 1.0
    }
    
    // 下巴下方区域（相对位置 0.8 - 1.0）：逐渐减弱
    if (relativeY > 0.8 && relativeY <= 1.0) {
      double t = (1.0 - relativeY) / 0.2; // 归一化到 [0, 1]
      return 0.6 + 0.4 * t; // 从 0.6 过渡到 1.0
    }
    
    return 0.0; // 超出范围不变形
  }

  /// 【连续性优化】基于水平位置的影响计算
  double _calculateHorizontalInfluence(double distanceFromCenterLine, double effectiveRadius) {
    final double horizontalRatio = distanceFromCenterLine / effectiveRadius;
    
    // 中心区域（0 - 0.3）：全强度
    if (horizontalRatio <= 0.3) {
      return 1.0;
    }
    
    // 过渡区域（0.3 - 0.7）：平滑衰减
    if (horizontalRatio <= 0.7) {
      double t = (horizontalRatio - 0.3) / 0.4;
      return 1.0 - 0.2 * t; // 从 1.0 衰减到 0.8
    }
    
    // 边缘区域（0.7 - 1.0）：快速衰减
    if (horizontalRatio <= 1.0) {
      double t = (horizontalRatio - 0.7) / 0.3;
      return 0.8 - 0.7 * t; // 从 0.8 衰减到 0.1
    }
    
    return 0.0;
  }

  /// 【V型下巴专用】基于特征点类型计算变形强度 - 只针对下半脸特征点
  double _calculateFeaturePointStrength(int pointIndex, double distanceFromCenter) {
    // 核心下巴特征点：最大强度
    if ([67, 297, 148, 377, 152].contains(pointIndex)) {
      return 1.0;
    }
    
    // 下颌线辅助点：中等强度
    if ([145, 374, 146, 375, 147, 376].contains(pointIndex)) {
      return 0.8;
    }
    
    // 下巴辅助点：较小强度
    if ([149, 378, 150, 379].contains(pointIndex)) {
      return 0.6;
    }
    
    // 其他特征点不参与V型下巴变形
    return 0.0;
  }

  /// 【V型下巴专用】计算边界因子 - 在接近边界时减少变形强度
  double _calculateBoundaryFactor(double x, double y, double centerX, double centerY, double radius) {
    final double distanceFromCenterLine = (x - centerX).abs();
    final double relativeY = (y - centerY) / radius;
    
    // 根据垂直位置获取允许的水平范围
    double allowedHorizontalRange;
    if (relativeY <= 0.3) {
      allowedHorizontalRange = radius * 0.85;
    } else if (relativeY <= 0.6) {
      allowedHorizontalRange = radius * 0.75;
    } else if (relativeY <= 0.9) {
      allowedHorizontalRange = radius * 0.65;
    } else {
      allowedHorizontalRange = radius * 0.5;
    }
    
    // 计算距离边界的比例
    final double distanceToBoundary = allowedHorizontalRange - distanceFromCenterLine;
    final double boundaryThreshold = allowedHorizontalRange * 0.2; // 边界渐变区域为20%
    
    // 在边界渐变区域内逐渐减少强度
    if (distanceToBoundary <= boundaryThreshold) {
      return (distanceToBoundary / boundaryThreshold) * 0.5 + 0.5; // 从0.5到1.0的渐变
    }
    
    return 1.0; // 远离边界时保持全强度
  }

  /// 【修复】对称性优化 - 基于面部中心线确保完美对称变形
  /// 
  /// 统一变形方向定义：
  /// - isIncreasing = true：V型下巴效果（下颌角向中心线收缩）
  /// - isIncreasing = false：下颌角外扩（恢复原状或增宽）
  double _calculateSymmetricOffset(double dx, double normalizedDistance, double magnitude, bool isIncreasing) {
    // 确保左右两侧变形量完全对称，并增加10%水平变形强度
    final double symmetricMagnitude = normalizedDistance * magnitude * 1.1; // 增加10%水平变形强度
    
    // 【修复】统一对称性逻辑：确保左右两侧完全对称
    if (isIncreasing) {
      // V型收缩：左右两侧都向中心线收缩
      return dx > 0 ? -symmetricMagnitude : symmetricMagnitude;
    } else {
      // 下颌扩张：左右两侧都向外扩张
      return dx > 0 ? symmetricMagnitude : -symmetricMagnitude;
    }
  }

  @override
  void resetState() {
    Logger.flow(_logTag, 'resetState', '重置V型下巴变形状态');
  }
}