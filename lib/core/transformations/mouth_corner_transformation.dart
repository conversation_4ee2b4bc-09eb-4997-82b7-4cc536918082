import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/feature_point.dart';
import '../../utils/logger.dart';
import 'transformation_strategy.dart';
import '../deformation_cache_manager.dart';
import '../transform_type.dart';
import 'dart:typed_data';

/// 嘴角上扬变形策略实现
/// 【完全复制V型下巴成功逻辑】基于经过验证的V型下巴成功实现
class MouthCornerTransformation extends TransformationStrategy {
  static const String _logTag = 'MouthCornerTransformation';

  // 【强制原则2】统一特征点分类 - 基于MediaPipe 468点模型
  // 核心嘴角特征点 - 左右嘴角点及其直接附近点（变形强度100%）
  static const List<int> _corePoints = [61, 291, 76, 306, 77, 307]; 
  
  // 微笑肌区域特征点 - 微笑线上的关键点（变形强度80%）
  static const List<int> _smileMusclePoints = [90, 320, 91, 321, 92, 322, 40, 270, 39, 269];
  
  // 过渡特征点 - 微笑肌连接面颜的点（变形强度50%）
  static const List<int> _transitionPoints = [78, 308, 80, 310, 88, 318, 89, 319, 207, 427, 206, 426];
  
  // 辅助特征点 - 面颜下部及唆边支撑点（变形强度30%）
  static const List<int> _auxiliaryPoints = [84, 314, 17, 405, 18, 200, 202, 421, 422, 104, 334];
  
  // 保护点 - 不应该受到影响的区域（唇中部，鼻子等）（变形强度0%）
  static const List<int> _protectionPoints = [0, 13, 14, 15, 17, 37, 267, 87, 317, 164, 391, 92, 322, 93, 323];

  @override
  String get logTag => _logTag;

  @override
  String get parameterName => 'mouth_corner';

  // 固定步长值，用于所有变形计算
  static const double fixedStepSize = 0.2;

  @override
  void applyFeaturePointTransformation(List<FeaturePoint> featurePoints,
      List<int> pointIndexes, double value, double intensity,
      {double? facialCenterLineX, bool? isIncreasing}) {
    
    // 【DEBUG】强制输出调试信息，不受日志过滤器影响
    print('╔═══════════════════════════════════════════════════════════════╗');
    print('║  🎯 【MouthCornerTransformation】applyFeaturePointTransformation  ║');
    print('║  ✅ 方法被成功调用                                                  ║');
    print('║  📊 参数值: $value                                              ║');
    print('║  🔄 变形方向: ${isIncreasing != null ? (isIncreasing ? "增大(加号)" : "减小(减号)") : "未指定"}  ║');
    print('║  📍 特征点数量: ${featurePoints.length}                           ║');
    print('║  📋 特征点索引: ${pointIndexes.length}个                          ║');
    print('║  🎯 面部中心线X: $facialCenterLineX                              ║');
    print('╚═══════════════════════════════════════════════════════════════╝');
    
    Logger.flowStart(_logTag, 'applyFeaturePointTransformation');

    // 【强制原则2】日志验证 - 特征点分类统计
    Logger.flow(_logTag, 'applyFeaturePointTransformation', 
        '📊 [特征点分类] 核心: ${_corePoints.length}, 过渡: ${_transitionPoints.length}, 辅助: ${_auxiliaryPoints.length}');

    Logger.flow(_logTag, 'applyFeaturePointTransformation', 
        '开始应用嘴角上扬变形 - V4.0框架实现');

    // 根据用户点击的是加号还是减号按钮来确定变形方向
    if (isIncreasing == null) {
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
      return;
    }

    // 根据点击加号或减号确定变形方向
    double direction = isIncreasing ? 1.0 : -1.0;
    // 【修正】使用固定步长计算变形因子，确保每次变形幅度一致
    final deformationFactor = direction * fixedStepSize;

    // 记录变形方向和预期效果
    if (isIncreasing!) {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📈 点击加号: 方向系数: $direction, 预期效果: 嘴角上扬');
    } else {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📉 点击减号: 方向系数: $direction, 预期效果: 嘴角下压');
    }

    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '变形因子: $deformationFactor (方向: ${isIncreasing! ? "加号" : "减号"}, 固定步长: $fixedStepSize)');

    // 【强制原则4】面部中心线全局共享
    double facialCenterX;
    
    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '🎯 [中心线] 使用全局中心线: ${facialCenterX.toStringAsFixed(2)} (永久缓存)');
    } else if (facialCenterLineX != null) {
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyFeaturePointTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      facialCenterX = calculateFacialCenterLineX(featurePoints);
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，重新计算可能导致不对称: $facialCenterX');
    }

    // 计算并缓存嘴角Y坐标
    double totalY = 0;
    int corePointCount = 0;
    for (var index in _corePoints) {
      final pointIndex = featurePoints.indexWhere((p) => p.index == index);
      if (pointIndex != -1) {
        totalY += featurePoints[pointIndex].y;
        corePointCount++;
      }
    }
    if (corePointCount > 0) {
      final mouthCenterY = totalY / corePointCount;
      DeformationCacheManager.setMouthCenterY(mouthCenterY);
      Logger.flow(_logTag, 'applyFeaturePointTransformation', '缓存嘴角中心Y坐标: $mouthCenterY');
    }

    // 应用变形到所有特征点
    for (var index in pointIndexes) {
      final pointIndex = featurePoints.indexWhere((p) => p.index == index);
      if (pointIndex != -1) {
        final oldPoint = featurePoints[pointIndex];

        // 计算点到中心线的距离
        final dx = oldPoint.x - facialCenterX;
        final distanceFromCenter = math.sqrt(dx * dx + math.pow(oldPoint.y - 300, 2)); // 假设脸部中心Y约为300

        // 【强制原则2】基于特征点位置计算变形强度
        double pointStrength = _calculateFeaturePointStrength(index, distanceFromCenter);

        // 【强制原则5】使用统一的对称性计算函数，确保完美对称
        // 【医美级精准控制】嘴角上扬的精细向量计算，模拟自然微笑肌肉收缩
        final deformVector = _calculateMouthCornerVector(
            dx,
            oldPoint.y,
            oldPoint.x,
            oldPoint.index,
            1.0, // 特征点使用归一化强度1.0
            deformationFactor.abs() * pointStrength * 8.0, // 【关键优化】大幅提高变形强度，从2.5提高到8.0
            isIncreasing!
        );
        
        double offsetY = deformVector.dy;
        double offsetX = deformVector.dx;

        // 计算变形向量大小（用于统计）
        double vectorMagnitude = math.sqrt(offsetX * offsetX + offsetY * offsetY);

        // 特征点变形统计（简化日志）

        // 计算新的坐标
        final newX = oldPoint.x + offsetX;
        final newY = oldPoint.y + offsetY;

        // 【强制原则4】日志记录
        featurePoints[pointIndex] = updateFeaturePoint(oldPoint, newX: newX, newY: newY);

        // 特征点变形完成
      }
    }

    Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
  }

  @override
  ui.Image? applyImageTransformation(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double radius,
      double value,
      double intensity,
      ui.Image? image,
      bool showDeformationArea,
      double? facialCenterLineX,
      {bool? isIncreasing, List<FeaturePoint>? currentFeaturePoints}) {
    
    // 【完全复制V型下巴成功逻辑】使用经过验证的成功实现
    Logger.flowStart(_logTag, 'applyImageTransformation');
    
    print('🎯 【嘴角上扬变形】完全复制V型下巴成功算法');
    Logger.flow(_logTag, 'applyImageTransformation', '📥 输入图像信息: 哈希码=${image?.hashCode}, 尺寸=${image?.width}x${image?.height}');

    if (image == null) {
      Logger.flowError(_logTag, 'applyImageTransformation', '❌ [错误] 图像为空，无法应用变形');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    final Rect dstRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final paint = Paint()..filterQuality = FilterQuality.high;
    canvas.drawImageRect(image, Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()), dstRect, paint);

    // 【关键修复】优先使用全局缓存的面部中心线，确保绝对对称性 - 完全复制V型下巴逻辑
    double facialCenterX;

    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '🎯 [中心线] 使用全局中心线: ${facialCenterX.toStringAsFixed(2)} (永久缓存)');
    } else if (facialCenterLineX != null) {
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      facialCenterX = centerX;
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，使用画布中心可能导致不对称: $facialCenterX');
    }

    // 判断变形方向 - 根据用户点击的加号或减号来确定 - 完全复制V型下巴逻辑
    double direction = 0.0;

    if (isIncreasing != null) {
      direction = isIncreasing ? 1.0 : -1.0;
      Logger.flow(_logTag, 'applyImageTransformation',
          '🔍 变形方向根据用户点击确定: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 预期效果: ${isIncreasing ? "嘴角上扬，更加亲和" : "嘴角还原，回到自然状态"}');
    } else {
      Logger.flowError(_logTag, 'applyImageTransformation', '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null; // 必须提供变形方向
    }

    // 【修正】使用固定步长计算变形因子，确保每次变形幅度一致
    double deformationFactor = direction * fixedStepSize;
    
    // 【微笑肌群】使用适中半径，覆盖从嘴角到脸颊的微笑肌区域
    double effectiveRadius = radius * 0.12; // 扩大半径以覆盖完整的微笑肌群
    Logger.flow(_logTag, 'applyImageTransformation', '嘴角专用半径: $effectiveRadius (原半径: ${radius})');    
    
    // 【关键修复】不使用单一中心点，而是分别处理左右嘴角
    Logger.flow(_logTag, 'applyImageTransformation', '嘴角分离变形模式: 左右嘴角独立处理');

    Logger.flow(_logTag, 'applyImageTransformation',
        '嘴角变形参数 - 面部中心线: $facialCenterX, 半径: $effectiveRadius, 变形系数: $deformationFactor');

    // 【关键修复】直接基于原始图像尺寸创建离屏画布
    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);

    // 【嘴角专用】使用高密度网格，确保精细变形效果
    final int gridSizeX = 200; // 嘴角变形使用高密度网格
    final int gridSizeY = 200; // 嘴角变形使用高密度网格

    // 使用原始图像尺寸计算网格 - 完全复制V型下巴逻辑
    final double cellWidth = image.width.toDouble() / gridSizeX;
    final double cellHeight = image.height.toDouble() / gridSizeY;

    // 【关键修复】使用实际特征点坐标精确定位嘴角
    final double originalFacialCenterX = facialCenterX * image.width / size.width;
    final double originalEffectiveRadius = effectiveRadius * image.width / size.width;
    
    // 【精准定位】从特征点或缓存获取真实嘴角坐标
    double leftMouthCornerX, rightMouthCornerX, mouthCornerY;
    
    if (currentFeaturePoints != null && currentFeaturePoints.isNotEmpty) {
      // 使用实际特征点坐标
      FeaturePoint? leftCorner = currentFeaturePoints.firstWhere((p) => p.index == 61, orElse: () => FeaturePoint(index: -1, x: 0, y: 0));
      FeaturePoint? rightCorner = currentFeaturePoints.firstWhere((p) => p.index == 291, orElse: () => FeaturePoint(index: -1, x: 0, y: 0));
      
      if (leftCorner.index != -1 && rightCorner.index != -1) {
        leftMouthCornerX = leftCorner.x * image.width / size.width;
        rightMouthCornerX = rightCorner.x * image.width / size.width;
        mouthCornerY = ((leftCorner.y + rightCorner.y) / 2) * image.height / size.height;
        
        Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用实际特征点: 左嘴角(${leftMouthCornerX.toInt()},${mouthCornerY.toInt()}), 右嘴角(${rightMouthCornerX.toInt()},${mouthCornerY.toInt()})');
      } else {
        // 后备方案：使用估算值
        leftMouthCornerX = originalFacialCenterX - 140 * (image.width / size.width);
        rightMouthCornerX = originalFacialCenterX + 130 * (image.width / size.width);
        mouthCornerY = centerY * image.height / size.height;
        Logger.flow(_logTag, 'applyImageTransformation', '⚠️ 特征点不可用，使用估算嘴角位置');
      }
    } else {
      // 后备方案：使用估算值
      leftMouthCornerX = originalFacialCenterX - 140 * (image.width / size.width);
      rightMouthCornerX = originalFacialCenterX + 130 * (image.width / size.width);
      mouthCornerY = centerY * image.height / size.height;
      Logger.flow(_logTag, 'applyImageTransformation', '⚠️ 无特征点数据，使用估算嘴角位置');
    }

    // 输出网格变形信息
    Logger.flow(_logTag, 'applyImageTransformation', '📊 网格变形设置');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 网格尺寸: ${gridSizeX}x${gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 原始图像坐标系: 中心线X=${originalFacialCenterX.toStringAsFixed(1)}, 嘴角Y=${mouthCornerY.toStringAsFixed(1)}, 半径=${originalEffectiveRadius.toStringAsFixed(1)}');

    // 【对称性统计】记录左右两侧变形网格数量 - 完全复制V型下巴逻辑
    int leftSideGridCount = 0;
    int rightSideGridCount = 0;

    // 遍历网格 - 完全复制V型下巴的网格遍历逻辑
    for (int y = 0; y < gridSizeY; y++) {
      for (int x = 0; x < gridSizeX; x++) {
        // 在原始图像坐标系中计算网格单元 - 完全复制V型下巴逻辑
        final double left = x * cellWidth;
        final double top = y * cellHeight;
        final double right = (x + 1) * cellWidth;
        final double bottom = (y + 1) * cellHeight;

        // 计算网格单元中心点
        final double centerGridX = (left + right) / 2;
        final double centerGridY = (top + bottom) / 2;

        // 【关键修复】分别计算到左右嘴角的距离
        final double distanceToLeftCorner = math.sqrt(
          math.pow(centerGridX - leftMouthCornerX, 2) + 
          math.pow(centerGridY - mouthCornerY, 2)
        );
        final double distanceToRightCorner = math.sqrt(
          math.pow(centerGridX - rightMouthCornerX, 2) + 
          math.pow(centerGridY - mouthCornerY, 2)
        );

        // 计算变形量
        double offsetY = 0.0; // 【关键】嘴角变形主要是Y方向
        double offsetX = 0.0; // 【关键】声明水平偏移量变量

        // 【微笑肌群判断】分别判断左右微笑肌区域
        bool isInLeftSmileZone = distanceToLeftCorner <= originalEffectiveRadius;
        bool isInRightSmileZone = distanceToRightCorner <= originalEffectiveRadius;
        
        // 【嘴唇中间保护】严格保护嘴唇中央区域，避免影响嘴唇形状
        double distanceToMouthCenter = (centerGridX - originalFacialCenterX).abs();
        bool isTooNearMouthCenter = distanceToMouthCenter < originalEffectiveRadius * 0.6; // 缩小保护范围，允许更自然过渡
        
        // 【解剖学微笑肌群】基于真实面部解剖结构的自然变形
        bool shouldApplyDeformation = (isInLeftSmileZone || isInRightSmileZone) && !isTooNearMouthCenter;
        
        if (shouldApplyDeformation) {
          // 【对称性统计】记录左右两侧变形网格数量
          if (isInRightSmileZone) {
            rightSideGridCount++;
          } else if (isInLeftSmileZone) {
            leftSideGridCount++;
          }
          
          // 确定当前处理的是左侧还是右侧嘴角
          final bool isLeftSide = isInLeftSmileZone;
          final double cornerX = isLeftSide ? leftMouthCornerX : rightMouthCornerX;
          final double cornerY = mouthCornerY;
          
          // 【解剖学基础】计算相对于嘴角的位置向量
          final double relativeX = centerGridX - cornerX;
          final double relativeY = centerGridY - cornerY;
          final double actualDistance = math.sqrt(relativeX * relativeX + relativeY * relativeY);
          
          if (actualDistance > 0 && actualDistance <= originalEffectiveRadius) {
            // 【关键】计算从嘴角到颧骨的解剖学角度
            // 颧大肌角度：约45度向上外方（最主要的微笑肌肉）
            // 颧小肌角度：约60度向上外方
            // 笑肌角度：约0度水平向外
            
            final double normalizedDistance = actualDistance / originalEffectiveRadius;
            final double baseMagnitude = (1.0 - normalizedDistance) * deformationFactor.abs() * 100.0;
            
            if (deformationFactor > 0) { // 嘴角上扬
              
              // 【简洁自然】统一的向上外方运动，模拟真实微笑
              // 计算距离衰减：距离嘴角越近效果越强
              final double distanceFactor = math.max(0.1, 1.0 - normalizedDistance * normalizedDistance);
              
              // 【核心原理】真实微笑时嘴角向上外方移动，角度约35度
              final double smileAngle = 35 * math.pi / 180; // 35度角，自然微笑的理想角度
              
              // 计算基础运动向量
              final double baseIntensity = baseMagnitude * distanceFactor;
              
              // 向上运动分量（主要）
              offsetY = -baseIntensity * math.sin(smileAngle);
              
              // 向外运动分量（次要）
              final double horizontalDirection = isLeftSide ? -1.0 : 1.0;
              offsetX = horizontalDirection * baseIntensity * math.cos(smileAngle) * 0.6;
              
              // 【重要】根据相对位置微调方向，确保自然过渡
              // 离嘴角更远的区域，水平扩展更明显
              if (actualDistance > originalEffectiveRadius * 0.5) {
                offsetX *= 1.2; // 外围区域水平扩展增强
                offsetY *= 0.8; // 外围区域向上提升减弱
              }
              
              // 【垂直位置调整】确保只影响嘴角水平线附近区域
              if (relativeY < -originalEffectiveRadius * 0.3) {
                // 嘴角上方区域减弱效果
                offsetY *= 0.4;
                offsetX *= 0.4;
              } else if (relativeY > originalEffectiveRadius * 0.6) {
                // 嘴角下方过远区域减弱效果
                offsetY *= 0.6;
                offsetX *= 0.6;
              }
              
            } else { // 嘴角还原（减号方向）
              // 【关键修复】减号应该是还原效果，不是下压效果
              // 使用与加号相反但较轻微的运动，实现自然还原
              final double distanceFactor = math.max(0.1, 1.0 - normalizedDistance * normalizedDistance);
              
              // 【统一逻辑】使用与特征点变形相同的35度角度，但方向相反且强度减半
              final double smileAngle = 35 * math.pi / 180; // 保持35度角
              final double baseIntensity = baseMagnitude * distanceFactor * 0.5; // 还原强度减半
              
              // 还原运动：轻微向下内方移动（与上扬相反但更轻微）
              offsetY = baseIntensity * math.sin(smileAngle); // 轻微向下移动（正Y）
              
              // 向内收敛（与外扩相反）
              final double horizontalDirection = isLeftSide ? 1.0 : -1.0; // 左侧向右，右侧向左（向内收敛）
              offsetX = horizontalDirection * baseIntensity * math.cos(smileAngle) * 0.3; // 轻微内收
              
              // 【重要】根据相对位置微调方向，确保自然还原
              if (actualDistance > originalEffectiveRadius * 0.5) {
                offsetX *= 0.8; // 外围区域内收减弱
                offsetY *= 0.6; // 外围区域还原减弱
              }
              
              // 【垂直位置调整】确保只影响嘴角水平线附近区域
              if (relativeY < -originalEffectiveRadius * 0.3) {
                // 嘴角上方区域减弱效果
                offsetY *= 0.4;
                offsetX *= 0.4;
              } else if (relativeY > originalEffectiveRadius * 0.6) {
                // 嘴角下方过远区域减弱效果
                offsetY *= 0.6;
                offsetX *= 0.6;
              }
            }
          }
          
          // 计算变形强度（用于统计，不输出详细日志）
          double offsetMagnitude = math.sqrt(offsetX * offsetX + offsetY * offsetY);

          // 在原始图像坐标系中计算变形后的位置
          final double deformedSrcX = centerGridX; // X坐标保持不变
          final double deformedSrcY = centerGridY - offsetY; // 应用Y方向变形

          // 确保源坐标在图像范围内
          if (deformedSrcX >= 0 &&
              deformedSrcX < image.width &&
              deformedSrcY >= 0 &&
              deformedSrcY < image.height) {
            
            final srcUnitRect = Rect.fromLTWH(
                deformedSrcX - cellWidth / 2,
                deformedSrcY - cellHeight / 2, 
                cellWidth, 
                cellHeight);

            final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

            offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
          }
        } else {
          // 不在变形半径内，直接复制原始图像的对应部分
          final srcUnitRect = Rect.fromLTWH(
              centerGridX - cellWidth / 2,
              centerGridY - cellHeight / 2, 
              cellWidth, 
              cellHeight);

          final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

          offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
        }
      }
    }
    
    // 【对称性检查】输出左右两侧变形网格统计
    Logger.flow(_logTag, 'applyImageTransformation', 
        '📊 对称性统计: 左侧变形网格=${leftSideGridCount}个, 右侧变形网格=${rightSideGridCount}个, 差异=${(leftSideGridCount - rightSideGridCount).abs()}个');

    // 创建变形图像 - 完全复制V型下巴逻辑
    Logger.flow(_logTag, 'applyImageTransformation', '🎯 开始创建变形图像');
    final ui.Image finalImage = recorder.endRecording().toImageSync(image.width, image.height);
    
    Logger.flow(_logTag, 'applyImageTransformation', '📤 创建的变形图像信息:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 实际输出尺寸: ${finalImage.width}x${finalImage.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 哈希码: ${finalImage.hashCode}');
    
    canvas.drawImageRect(
        finalImage,
        Rect.fromLTWH(0, 0, finalImage.width.toDouble(), finalImage.height.toDouble()),
        dstRect,
        paint);

    if (showDeformationArea) {
      final deformationAreaRect = Rect.fromCenter(
          center: Offset(centerX, centerY),
          width: effectiveRadius * 2,
          height: effectiveRadius * 2);

      final borderPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawOval(deformationAreaRect, borderPaint);
    }

    Logger.flow(_logTag, 'applyImageTransformation', '🔍 图像尺寸验证:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 输入图像: ${image.width}x${image.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 输出图像: ${finalImage.width}x${finalImage.height}');
    
    if (finalImage.width != image.width || finalImage.height != image.height) {
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 严重错误: 图像尺寸不一致! 输入=${image.width}x${image.height}, 输出=${finalImage.width}x${finalImage.height}');
      Logger.flowError(_logTag, 'applyImageTransformation', '❌ 返回原始图像以避免尺寸不一致问题');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return image;
    }

    List<FeaturePoint>? featurePointsToSave;
    if (currentFeaturePoints != null && currentFeaturePoints.isNotEmpty) {
      featurePointsToSave = currentFeaturePoints;
      Logger.flow(_logTag, 'applyImageTransformation', '✅ 使用传入的变形后特征点: ${currentFeaturePoints.length}个');
    } else {
      featurePointsToSave = DeformationCacheManager.getLatestDeformedFeaturePoints();
      Logger.flowWarning(_logTag, 'applyImageTransformation', '⚠️ 警告: 没有传入变形后特征点，使用缓存数据，可能影响累积变形');
    }

    // 【强制原则1】保存变形状态到缓存
    DeformationCacheManager.setLatestDeformedState(finalImage, featurePointsToSave);
    Logger.flow(_logTag, 'applyImageTransformation',
        '✅ [状态保存] 变形图像已保存到缓存 | 哈希码: ${finalImage.hashCode} | 尺寸: ${finalImage.width}x${finalImage.height}');

    Logger.flowEnd(_logTag, 'applyImageTransformation');
    return finalImage;
  }

  // 其他所有方法与原文件保持一致...
  @override
  void resetState() {
    Logger.flow(_logTag, 'resetState', '重置嘴角上扬变形状态');
  }

  /// 基于特征点类型计算变形强度 - 增强强度更有效果
  double _calculateFeaturePointStrength(int pointIndex, double distance) {
    // 核心嘴角点：100%强度
    if (_corePoints.contains(pointIndex)) {
      return 1.0;
    }
    
    // 微笑肌点：80%强度
    if (_smileMusclePoints.contains(pointIndex)) {
      return 0.8;
    }
    
    // 过渡点：50%强度
    if (_transitionPoints.contains(pointIndex)) {
      return 0.5;
    }
    
    // 辅助点：30%强度
    if (_auxiliaryPoints.contains(pointIndex)) {
      return 0.3;
    }
    
    // 保护点：0%强度
    if (_protectionPoints.contains(pointIndex)) {
      return 0.0;
    }
    
    // 默认为10%强度
    return 0.1;
  }

  /// 嘴角变形向量计算 - 修复减号方向逻辑，确保一致性
  Offset _calculateMouthCornerVector(double dx, double pointY, double pointX, int pointIndex,
      double normalizedIntensity, double magnitude, bool isIncreasing) {
    double offsetX = 0.0;
    double offsetY = 0.0;

    // 【核心优化】大幅增强变形系数，确保效果可见
    // 核心嘴角点需要更强的变形效果
    double strengthMultiplier = 1.0;
    if (_corePoints.contains(pointIndex)) {
      strengthMultiplier = 3.0; // 核心点强化3倍
    } else if (_smileMusclePoints.contains(pointIndex)) {
      strengthMultiplier = 2.5; // 微笑肌点强化2.5倍
    }

    // 【关键修复】统一使用35度自然微笑角度和对称的反向逻辑
    final double smileAngle = 35 * math.pi / 180; // 35度角，自然微笑的理想角度
    final double baseIntensity = magnitude * strengthMultiplier;
    
    if (isIncreasing) {
      // 加号：嘴角上扬（自然微笑方向）
      offsetY = -baseIntensity * math.sin(smileAngle); // 向上移动（负Y）
      final double horizontalDirection = dx > 0 ? 1.0 : -1.0; // 右侧向右，左侧向左
      offsetX = horizontalDirection * baseIntensity * math.cos(smileAngle) * 0.6; // 适度外扩
      
      // 加号变形计算完成
    } else {
      // 减号：嘴角还原（反向运动，但不是下压，而是向内向下的自然还原）
      offsetY = baseIntensity * math.sin(smileAngle) * 0.5; // 轻微向下移动，强度减半（自然还原）
      final double horizontalDirection = dx > 0 ? -1.0 : 1.0; // 右侧向左，左侧向右（向内收敛）
      offsetX = horizontalDirection * baseIntensity * math.cos(smileAngle) * 0.3; // 轻微内收
      
      // 减号变形计算完成
    }

    // 变形向量计算完成
    
    return Offset(offsetX, offsetY);
  }

  @override
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints, [double scaleX = 1.0]) {
    double totalX = 0;
    int count = 0;
    for (var point in featurePoints) {
      totalX += point.x;
      count++;
    }
    return count > 0 ? (totalX / count) * scaleX : 0;
  }

  /// 获取嘴角上扬变形相关的特征点索引
  /// 返回所有需要变形的嘴角相关特征点
  List<int> getParameterFeaturePointIndexes() {
    // 返回嘴角上扬变形相关的所有特征点索引
    List<int> allIndexes = [];
    allIndexes.addAll(_corePoints);      // 核心嘴角点
    allIndexes.addAll(_smileMusclePoints); // 微笑肌点
    allIndexes.addAll(_transitionPoints); // 过渡点
    allIndexes.addAll(_auxiliaryPoints);  // 辅助点
    
    // 移除重复索引并排序
    allIndexes = allIndexes.toSet().toList()..sort();
    
    Logger.flow(_logTag, 'getParameterFeaturePointIndexes', 
      '嘴角上扬特征点索引: ${allIndexes.length}个 - $allIndexes');
    
    return allIndexes;
  }

  @override
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint, {double? newX, double? newY}) {
    return FeaturePoint(
      index: oldPoint.index,
      x: newX ?? oldPoint.x,
      y: newY ?? oldPoint.y,
    );
  }

  @override
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY, {Color color = Colors.red}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0;
    canvas.drawLine(Offset(0, centerY), Offset(size.width, centerY), paint);
  }
}
