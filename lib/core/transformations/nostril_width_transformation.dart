import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/feature_point.dart';
import '../../utils/logger.dart';
import 'transformation_strategy.dart';
import '../deformation_cache_manager.dart';
import '../transform_type.dart';
import 'dart:typed_data';

/// 鼻翼宽度变形策略实现
class NostrilWidthTransformation extends TransformationStrategy {
  static const String _logTag = 'NostrilWidthTransformation';

  @override
  String get logTag => _logTag;

  @override
  String get parameterName => 'nostril_width';

  // 固定步长值，用于所有变形计算
  static const double fixedStepSize = 0.2;

  @override
  void applyFeaturePointTransformation(List<FeaturePoint> featurePoints,
      List<int> pointIndexes, double value, double intensity,
      {double? facialCenterLineX, bool? isIncreasing}) {
    Logger.flowStart(_logTag, 'applyFeaturePointTransformation');

    // 【修复】只在实际需要变形时执行操作，不强制变形
    Logger.flow(_logTag, 'applyFeaturePointTransformation', '开始应用鼻孔宽度变形');

    // 根据用户点击的是加号还是减号按钮来确定变形方向
    // isIncreasing为true表示点击加号，为false表示点击减号
    double direction = 0.0;

    if (isIncreasing != null) {
      direction = isIncreasing ? 1.0 : -1.0;
    } else {
      // 如果没有提供isIncreasing参数，则报错并中止变形
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
      return;
    }

    // 记录变形方向和预期效果
    if (isIncreasing!) {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📈 点击加号: 方向系数: $direction, 预期效果: 鼻子变宽');
    } else {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📉 点击减号: 方向系数: $direction, 预期效果: 鼻子变窄');
    }

    // 【修正】使用固定步长计算变形因子，确保每次变形幅度一致
    final deformationFactor = direction * fixedStepSize;
    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '变形因子: $deformationFactor (方向: ${isIncreasing! ? "加号" : "减号"}, 固定步长: $fixedStepSize)');

    // 【关键修复】优先使用全局缓存的面部中心线，确保绝对对称性
    double facialCenterX;
    
    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '✅ 使用全局缓存的面部中心线: $facialCenterX (永久缓存)');
    } else if (facialCenterLineX != null) {
      // 次优先：使用传入的中心线
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyFeaturePointTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      // 最后备选：重新计算（不推荐）
      facialCenterX = calculateFacialCenterLineX(featurePoints);
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，重新计算可能导致不对称: $facialCenterX');
    }

    // 有效半径，与图像变形保持一致
    final effectiveRadius = 150.0; // 增大半径，确保所有鼻翼特征点都在变形范围内

    // 应用变形到所有特征点
    for (var index in pointIndexes) {
      final pointIndex = featurePoints.indexWhere((p) => p.index == index);
      if (pointIndex != -1) {
        final oldPoint = featurePoints[pointIndex];

        // 计算点到中心线的距离
        final dx = oldPoint.x - facialCenterX;

        // 对所有鼻翼特征点应用变形，不再限制半径
        // 计算变形系数，距离中心越近，变形越明显
        final distanceRatio = dx.abs() / effectiveRadius;
        final smoothFactor = (1.0 - math.min(distanceRatio, 1.0)) *
            math.cos(math.min(distanceRatio, 1.0) * math.pi / 2);

        // 【修正】使用固定步长计算变形因子，确保每次变形幅度一致
        final deformationFactor = direction * fixedStepSize;

        // 计算水平方向的位移量
        double offsetX = 0.0;

        // 简化偏移量计算，只与变形因子和特征点位置有关，与参数值无关
        if (dx > 0) {
          // 在中心线右侧
          // 右侧点：正方向向右移动，负方向向左移动
          offsetX = deformationFactor * 5.0; // 使用固定倍数增强效果
          Logger.flow(_logTag, 'applyFeaturePointTransformation',
              '右侧点偏移量计算: 方向=${isIncreasing! ? "加号" : "减号"}, 方向系数=$direction, 变形因子=$deformationFactor, 偏移量=$offsetX');
        } else if (dx < 0) {
          // 在中心线左侧
          // 左侧点：正方向向左移动，负方向向右移动
          offsetX = deformationFactor * -5.0; // 左侧需要反向，使用固定倍数增强效果
          Logger.flow(_logTag, 'applyFeaturePointTransformation',
              '左侧点偏移量计算: 方向=${isIncreasing! ? "加号" : "减号"}, 方向系数=$direction, 变形因子=$deformationFactor, 偏移量=$offsetX');
        }

        // 计算新的X坐标
        final newX = oldPoint.x + offsetX;

        // 替换原有特征点
        featurePoints[pointIndex] = updateFeaturePoint(oldPoint, newX: newX);

        // 记录变形后的坐标
        Logger.flow(_logTag, 'applyFeaturePointTransformation',
            '特征点 ID=${oldPoint.index} 变形后坐标: ($newX, ${oldPoint.y})，原坐标: (${oldPoint.x}, ${oldPoint.y})，偏移量: $offsetX');
      }
    }

    Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
  }

  /// 计算图像缩放矩形，确保图像按比例缩放并适应画布
  Rect _calculateScaledImageRect(ui.Image image, Size canvasSize) {
    final double imageAspectRatio = image.width / image.height;
    final double canvasAspectRatio = canvasSize.width / canvasSize.height;

    Rect dstRect;
    if (imageAspectRatio > canvasAspectRatio) {
      // 图像比画布更宽，以宽度为基准缩放
      final double scaledHeight = canvasSize.width / imageAspectRatio;
      final double topOffset = (canvasSize.height - scaledHeight) / 2;
      dstRect = Rect.fromLTWH(0, topOffset, canvasSize.width, scaledHeight);
    } else {
      // 图像比画布更高，以高度为基准缩放
      final double scaledWidth = canvasSize.height * imageAspectRatio;
      final double leftOffset = (canvasSize.width - scaledWidth) / 2;
      dstRect = Rect.fromLTWH(leftOffset, 0, scaledWidth, canvasSize.height);
    }

    return dstRect;
  }

  @override
  ui.Image? applyImageTransformation(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double radius,
      double value,
      double intensity,
      ui.Image? image,
      bool showDeformationArea,
      double? facialCenterLineX,
      {bool? isIncreasing, List<FeaturePoint>? currentFeaturePoints}) {
    Logger.flowStart(_logTag, 'applyImageTransformation');
    
    // 【关键调试】记录输入图像信息
    Logger.flow(_logTag, 'applyImageTransformation', '📥 输入图像信息: 哈希码=${image?.hashCode}, 尺寸=${image?.width}x${image?.height}');
    
    // 【修复累积变形】不再使用getLatestDeformedImage，而是直接使用传入的image
    // 因为传入的image已经是SimpleDeformationRenderer中正确获取的累积图像
    Logger.flow(_logTag, 'applyImageTransformation', '📷 使用传入的累积图像进行变形: 哈希码=${image?.hashCode}');

    // 添加图像变形详细流程日志

    if (image == null) {
      Logger.flow(_logTag, 'applyImageTransformation', '❌ [错误] 图像为空，无法应用变形');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }
    // 即使参数值为0，也要执行变形，因为我们使用固定步长而非参数值
    if (value == 0.0) {
      Logger.i(_logTag, '✅ 鼻翼宽度变形 | 参数值为0，但仍然执行变形');
    }

    // 【关键修复】由于Canvas现在使用原始图像坐标系，直接使用原始尺寸，不需要缩放计算
    final Rect dstRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final srcRect =
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());

    // 即使参数值为0，也要执行变形操作，确保变形连续性
    // 【修复】参数值为0时不应该执行变形，只有实际的用户操作才触发变形
    if (value == 0.0 && (isIncreasing == null)) {
      Logger.flow(_logTag, 'applyImageTransformation', '⚠️ 参数值为0且无用户操作，跳过变形');
      return image; // 直接返回原始图像，不执行变形
    }
    
    Logger.flow(_logTag, 'applyImageTransformation', '开始执行鼻翼宽度变形操作');

    // 【关键修复】优先使用全局缓存的面部中心线，确保绝对对称性
    double facialCenterX;
    
    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用全局缓存的面部中心线: $facialCenterX (永久缓存)');
    } else if (facialCenterLineX != null) {
      // 次优先：使用传入的中心线
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      // 最后备选：使用画布中心（不推荐）
      facialCenterX = centerX;
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，使用画布中心可能导致不对称: $facialCenterX');
    }

    // 判断变形方向 - 唯一根据用户点击的是加号还是减号来确定
    // 如果点击加号，则isIncreasing为true；如果点击减号，则为false
    double direction = 0.0;

    // 检查是否提供了isIncreasing参数
    if (isIncreasing != null) {
      // 方向系数 - 唯一根据用户点击的按钮来确定
      direction = isIncreasing ? 1.0 : -1.0; // 点击加号为1.0，点击减号为-1.0
      Logger.flow(_logTag, 'applyImageTransformation',
          '🔍 变形方向根据用户点击确定: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 预期效果: ${isIncreasing ? "鼻孔变宽" : "鼻孔变窄"}');
    } else {
      // 如果没有提供用户点击信息，则报错并中止变形
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 【修正】使用固定步长计算变形因子，确保每次变形幅度一致
    double deformationFactor = direction * fixedStepSize;

    // 绘制面部中心线
    if (showDeformationArea) {
      // 绘制面部中心线
      Paint linePaint = Paint()
        ..color = Colors.green
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;
      canvas.drawLine(Offset(facialCenterX, 0),
          Offset(facialCenterX, size.height), linePaint);
    }

    // 计算有效半径（考虑参数值的影响）
    // 使用更小的半径确保变形更加局部化
    double effectiveRadius = radius * 0.3; // 鼻翼宽度变形使用适合的半径

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形半径已调整: $effectiveRadius (原半径: ${radius})');

    // 调整变形系数，使效果更加明显
    final double enhancedFactor = deformationFactor * 10.0; // 【调试】大幅增加变形强度，以便观察效果

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形参数 - 中心点: ($facialCenterX, $centerY), 半径: $effectiveRadius, 变形系数: $deformationFactor, 增强系数: $enhancedFactor');

    // 绘制原始图像作为背景，使用按比例缩放的矩形
    // 使用高质量绘制
    final paint = Paint()..filterQuality = FilterQuality.high;

    canvas.drawImageRect(image, srcRect, dstRect, paint);

    // 【关键修复】直接基于原始图像尺寸创建离屏画布，避免任何缩放问题
    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);

    // 【关键修复】定义更高的网格密度，确保高质量的变形效果
    final int gridSizeX = 200; // 增加网格密度
    final int gridSizeY = 200; // 增加网格密度

    // 【关键修复】直接使用原始图像尺寸计算网格，而不使用dstRect
    final double cellWidth = image.width.toDouble() / gridSizeX;
    final double cellHeight = image.height.toDouble() / gridSizeY;

    // 【关键修复】直接在原始图像坐标系下进行网格变形，避免坐标转换问题
    // 计算原始图像中的面部中心线和变形中心点坐标
    final double originalFacialCenterX = facialCenterX * image.width / size.width;
    final double originalCenterY = centerY * image.height / size.height;
    final double originalEffectiveRadius = effectiveRadius * image.width / size.width;

    // 【关键修复】在原始图像坐标系下计算眼睛和嘴唇区域范围
    final double originalEyeAreaBottomY = originalCenterY - originalEffectiveRadius * 0.5;
    final double originalLipAreaTopY = originalCenterY + originalEffectiveRadius * 1.2;

    // 输出网格变形信息
    Logger.flow(_logTag, 'applyImageTransformation', '📅️ 网格变形设置');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 网格尺寸: ${gridSizeX}x${gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 总网格单元数: ${gridSizeX * gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 变形区域内网格单元数量估计: ${(math.pi * originalEffectiveRadius * originalEffectiveRadius / (image.width * image.height) * gridSizeX * gridSizeY).toInt()}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 原始图像坐标系: 中心线X=${originalFacialCenterX.toStringAsFixed(1)}, 中心Y=${originalCenterY.toStringAsFixed(1)}, 半径=${originalEffectiveRadius.toStringAsFixed(1)}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 眼睛区域底部Y=${originalEyeAreaBottomY.toStringAsFixed(1)}, 嘴唇区域顶部Y=${originalLipAreaTopY.toStringAsFixed(1)}');

    // 遍历网格
    for (int y = 0; y < gridSizeY; y++) {
      for (int x = 0; x < gridSizeX; x++) {
        // 【关键修复】直接在原始图像坐标系中计算网格单元
        final double left = x * cellWidth;
        final double top = y * cellHeight;
        final double right = (x + 1) * cellWidth;
        final double bottom = (y + 1) * cellHeight;

        // 计算网格单元中心点（原始图像坐标系）
        final double centerGridX = (left + right) / 2;
        final double centerGridY = (top + bottom) / 2;

        // 计算中心点到面部中心线的距离（原始图像坐标系）
        final double dx = centerGridX - originalFacialCenterX;
        final double distanceFromCenterLine = dx.abs();
        final double dy = centerGridY - originalCenterY;
        final distance = math.sqrt(dx * dx + dy * dy);

        // 计算变形量
        double offsetX = 0.0;

        // 【关键修复】使用统一的原始图像坐标系进行判断
        // 如果变形半径内且不在眼睛区域，应用变形
        if (distance < originalEffectiveRadius && centerGridY > originalEyeAreaBottomY) {
          // 计算变形系数，距离中心越近，变形越明显
          final double distanceRatio = distance / originalEffectiveRadius;
          // 使用线性与余弦混合的衰减函数，使过渡更加平滑
          final smoothFactor =
              (1.0 - distanceRatio) * math.cos(distanceRatio * math.pi / 2);

          // 计算水平方向的位移量，仅依赖于方向参数（isIncreasing）
          if (dx > 0) {
            // 在中心线右侧
            if (isIncreasing) {
              // 点击加号：鼻子变宽，右侧点向右移动（远离中心线）
              offsetX = dx * smoothFactor * deformationFactor.abs() * 0.5;
            } else {
              // 点击减号：鼻子变窄，右侧点向左移动（向中心线移动）
              offsetX = -dx * smoothFactor * deformationFactor.abs() * 0.5;
            }
          } else if (dx < 0) {
            // 在中心线左侧
            if (isIncreasing) {
              // 点击加号：鼻子变宽，左侧点向左移动（远离中心线）
              offsetX = dx * smoothFactor * deformationFactor.abs() * 0.5;
            } else {
              // 点击减号：鼻子变窄，左侧点向右移动（向中心线移动）
              offsetX = -dx * smoothFactor * deformationFactor.abs() * 0.5;
            }
          }

          // 【关键修复】直接在原始图像坐标系中计算变形后的位置
          final double deformedSrcX = centerGridX - offsetX;
          final double deformedSrcY = centerGridY;

          // 确保源坐标在图像范围内
          if (deformedSrcX >= 0 &&
              deformedSrcX < image.width &&
              deformedSrcY >= 0 &&
              deformedSrcY < image.height) {
            
            // 【关键修复】直接使用原始图像坐标系的矩形
            final srcUnitRect = Rect.fromLTWH(
                deformedSrcX - cellWidth / 2,
                deformedSrcY - cellHeight / 2, 
                cellWidth, 
                cellHeight);

            final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

            offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
          }
        } else {
          // 【关键修复】不在变形半径内，直接复制原始图像的对应部分
          final srcUnitRect = Rect.fromLTWH(
              centerGridX - cellWidth / 2,
              centerGridY - cellHeight / 2, 
              cellWidth, 
              cellHeight);

          final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

          offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
        }
      }
    }

    // 【关键调试】记录变形处理前的图像状态
    Logger.flow(_logTag, 'applyImageTransformation', '🎯 开始创建变形图像:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 处理图像尺寸: ${image.width}x${image.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 目标输出尺寸: ${image.width}x${image.height}');
    
    // 【关键修复】直接从离屏画布创建与原始图像完全相同尺寸的变形图像
    final ui.Image finalImage = recorder.endRecording().toImageSync(image.width, image.height);
    
    // 【关键调试】立即验证创建的图像尺寸
    Logger.flow(_logTag, 'applyImageTransformation', '📤 创建的变形图像信息:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 实际输出尺寸: ${finalImage.width}x${finalImage.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 哈希码: ${finalImage.hashCode}');
    
    // 将变形图像绘制到主画布上（仅用于显示）
    canvas.drawImageRect(
        finalImage,
        Rect.fromLTWH(0, 0, finalImage.width.toDouble(), finalImage.height.toDouble()),
        dstRect,
        paint);

    // 如果需要显示变形区域，绘制变形区域边界
    if (showDeformationArea) {
      final deformationAreaRect = Rect.fromCenter(
          center: Offset(centerX, centerY),
          width: effectiveRadius * 2,
          height: effectiveRadius * 2);

      final borderPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawOval(deformationAreaRect, borderPaint);
    }

    // 【关键修复】验证图像尺寸一致性
    Logger.flow(_logTag, 'applyImageTransformation', '🔍 图像尺寸验证:');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 输入图像: ${image.width}x${image.height}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 输出图像: ${finalImage.width}x${finalImage.height}');
    
    if (finalImage.width != image.width || finalImage.height != image.height) {
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 严重错误: 图像尺寸不一致! 输入=${image.width}x${image.height}, 输出=${finalImage.width}x${finalImage.height}');
      Logger.flowError(_logTag, 'applyImageTransformation', '❌ 返回原始图像以避免尺寸不一致问题');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return image;
    }

    // 【关键修复】使用变形后的特征点数据，确保累积变形状态正确传递
    List<FeaturePoint>? featurePointsToSave;
    
    // 优先使用传入的当前特征点（这些应该是经过applyFeaturePointTransformation处理的）
    if (currentFeaturePoints != null && currentFeaturePoints.isNotEmpty) {
      featurePointsToSave = currentFeaturePoints;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用传入的变形后特征点: ${currentFeaturePoints.length}个');
    } else {
      // 备选方案：使用缓存中的最新特征点
      featurePointsToSave = DeformationCacheManager.getLatestDeformedFeaturePoints();
      Logger.flowWarning(_logTag, 'applyImageTransformation', 
          '⚠️ 警告: 没有传入变形后特征点，使用缓存数据，可能影响累积变形');
    }

    // 设置最新的变形状态，包括图像和特征点数据
    DeformationCacheManager.setLatestDeformedState(finalImage, featurePointsToSave);
    Logger.flow(_logTag, 'applyImageTransformation',
        '✅ 变形图像已保存到缓存 | 哈希码: ${finalImage.hashCode} | 尺寸: ${finalImage.width}x${finalImage.height}');

    Logger.flowEnd(_logTag, 'applyImageTransformation');
    return finalImage;
  }

  // handleSpecialCases方法已在基类中实现，始终返回false

  @override
  bool handleSpecialCases(
      List<FeaturePoint> featurePoints, double value, bool isIncreasing) {
    return false;
  }

  @override
  TransformType getRequiredTransformType() {
    return TransformType.local;
  }

  @override
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints,
      [double scaleX = 1.0]) {
    Logger.flowStart(_logTag, 'calculateFacialCenterLineX');

    // 找出左右眼睛的特征点
    FeaturePoint? leftEye;
    FeaturePoint? rightEye;

    // 找出左右鼻孔的特征点
    FeaturePoint? leftNostril;
    FeaturePoint? rightNostril;

    // 遍历特征点，找出眼睛和鼻孔的特征点
    for (var point in featurePoints) {
      // 根据特征点的ID或位置判断其类型
      // 这里简化处理，假设已知特定ID对应特定类型的特征点
      if (point.id == 'left_eye' || point.index == 10) {
        leftEye = point;
      } else if (point.id == 'right_eye' || point.index == 11) {
        rightEye = point;
      } else if (point.id == 'left_nostril' || point.index == 20) {
        leftNostril = point;
      } else if (point.id == 'right_nostril' || point.index == 21) {
        rightNostril = point;
      }
    }

    // 计算眼睛中心线
    double eyeCenterX = 0.0;
    if (leftEye != null && rightEye != null) {
      eyeCenterX = (leftEye.x + rightEye.x) / 2 * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '眼睛中心线: $eyeCenterX (左眼: ${leftEye.x}, 右眼: ${rightEye.x})');
    }

    // 计算鼻孔中心线
    double nostrilCenterX = 0.0;
    if (leftNostril != null && rightNostril != null) {
      nostrilCenterX = (leftNostril.x + rightNostril.x) / 2 * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '鼻孔中心线: $nostrilCenterX (左鼻孔: ${leftNostril.x}, 右鼻孔: ${rightNostril.x})');
    }

    // 如果找到了眼睛和鼻孔的特征点，计算加权平均的中心线
    double facialCenterX;
    if (eyeCenterX > 0 && nostrilCenterX > 0) {
      // 给眼睛中心线更高的权重
      facialCenterX = (eyeCenterX * 0.7 + nostrilCenterX * 0.3);
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '基于加权平均计算的中心线: $facialCenterX');
    } else if (eyeCenterX > 0) {
      facialCenterX = eyeCenterX;
      Logger.flow(
          _logTag, 'calculateFacialCenterLineX', '仅使用眼睛中心线: $facialCenterX');
    } else if (nostrilCenterX > 0) {
      facialCenterX = nostrilCenterX;
      Logger.flow(
          _logTag, 'calculateFacialCenterLineX', '仅使用鼻孔中心线: $facialCenterX');
    } else {
      // 如果没有找到有效的特征点，使用图像中心作为面部中心线
      facialCenterX = 0.0;
      for (var point in featurePoints) {
        facialCenterX += point.x;
      }
      facialCenterX = (facialCenterX / featurePoints.length) * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用所有特征点的平均X坐标作为中心线: $facialCenterX');
    }

    Logger.flow(
        _logTag, 'calculateFacialCenterLineX', '面部中心线已固定: $facialCenterX');
    Logger.flowEnd(_logTag, 'calculateFacialCenterLineX');

    return facialCenterX;
  }

  @override
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint,
      {double? newX, double? newY}) {
    Logger.flow(_logTag, 'updateFeaturePoint',
        '更新特征点 ID=${oldPoint.index} 坐标: (${newX ?? oldPoint.x}, ${newY ?? oldPoint.y})');
    return FeaturePoint(
        id: oldPoint.id,
        index: oldPoint.index,
        x: newX ?? oldPoint.x,
        y: newY ?? oldPoint.y,
        z: oldPoint.z);
  }

  @override
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY,
      {Color color = Colors.red}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // 绘制垂直中心线
    canvas.drawLine(
        Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);

    // 绘制水平中心线
    canvas.drawLine(Offset(0, centerY), Offset(size.width, centerY), paint);
  }

  @override
  void resetState() {
    // 简化重置状态逻辑
    Logger.flow(_logTag, 'resetState', '重置鼻孔宽度变形状态');
  }
}
