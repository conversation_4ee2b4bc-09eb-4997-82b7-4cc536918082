import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/feature_point.dart';
import '../../utils/logger.dart';
import 'transformation_strategy.dart';
import '../deformation_cache_manager.dart';
import '../transform_type.dart';
import 'dart:typed_data';

/// 轮廓收紧变形策略实现
class ContourTightenTransformation extends TransformationStrategy {
  static const String _logTag = 'ContourTightenTransformation';

  @override
  String get logTag => _logTag;

  @override
  String get parameterName => 'contour_tighten';

  // 固定步长值，用于所有变形计算
  static const double fixedStepSize = 0.2;

  @override
  void applyFeaturePointTransformation(List<FeaturePoint> featurePoints,
      List<int> pointIndexes, double value, double intensity,
      {double? facialCenterLineX, bool? isIncreasing}) {
    Logger.flowStart(_logTag, 'applyFeaturePointTransformation');

    Logger.flow(_logTag, 'applyFeaturePointTransformation', '开始应用轮廓收紧变形');

    // 根据用户点击的是加号还是减号按钮来确定变形方向
    double direction = 0.0;

    if (isIncreasing != null) {
      direction = isIncreasing ? 1.0 : -1.0;
    } else {
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
      return;
    }

    // 记录变形方向和预期效果
    if (isIncreasing!) {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📈 点击加号: 方向系数: $direction, 预期效果: 面部轮廓收紧，下颌线收窄');
    } else {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📉 点击减号: 方向系数: $direction, 预期效果: 面部轮廓放松，下颌线放宽');
    }

    // 【架构统一】使用纯策略模式的标准变形因子计算
    final deformationFactor = direction * fixedStepSize;
    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '变形因子: $deformationFactor (方向: ${isIncreasing! ? "加号" : "减号"}, 方向系数: $direction, 固定步长: $fixedStepSize)');

    // 【关键修复】优先使用全局缓存的面部中心线
    double facialCenterX;
    
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '✅ 使用全局缓存的面部中心线: $facialCenterX');
    } else if (facialCenterLineX != null) {
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyFeaturePointTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX');
    } else {
      facialCenterX = calculateFacialCenterLineX(featurePoints);
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 重新计算面部中心线可能导致不对称: $facialCenterX');
    }

    // 【医美专业】轮廓收紧的关键特征点选择
    // 基于面部轮廓解剖学和医美轮廓收紧手术原理
    List<int> contourPoints = [
      67, 297,    // 下颌角 - 主要收紧点
      109, 338,   // 面颊中部 - 配合收紧
      162, 389,   // 下巴轮廓起点 - 轮廓塑形
      71, 301,    // 太阳穴辅助点 - 上部收紧
      68, 298,    // 太阳穴辅助点 - 上部支撑
      104, 333,   // 中颊点对 - 中部收紧
      169, 394,   // 面部下部轮廓点 - 下部收紧
    ];
    
    // 【医美专业】精确的强度因子映射 - 基于轮廓收紧解剖结构
    final Map<int, double> intensityFactors = {
      67: 1.0,    // 左下颌角 - 最大收紧强度
      297: 1.0,   // 右下颌角 - 最大收紧强度
      109: 0.8,   // 左面颊中部 - 较大收紧
      338: 0.8,   // 右面颊中部 - 较大收紧
      162: 0.6,   // 左下巴轮廓起点 - 中等收紧
      389: 0.6,   // 右下巴轮廓起点 - 中等收紧
      71: 0.7,    // 左太阳穴辅助点 - 上部收紧
      301: 0.7,   // 右太阳穴辅助点 - 上部收紧
      68: 0.5,    // 左太阳穴辅助点 - 轻度支撑
      298: 0.5,   // 右太阳穴辅助点 - 轻度支撑
      104: 0.9,   // 左中颊点 - 重要收紧点
      333: 0.9,   // 右中颊点 - 重要收紧点
      169: 0.7,   // 左面部下部轮廓点 - 下部收紧
      394: 0.7,   // 右面部下部轮廓点 - 下部收紧
    };

    // 【医美专业】应用轮廓收紧变形
    for (int pointIndex in contourPoints) {
      if (pointIndex < featurePoints.length) {
        final point = featurePoints[pointIndex];
        final intensityFactor = intensityFactors[pointIndex] ?? 0.5;
        
        // 计算点与面部中心线的距离
        final distanceFromCenter = point.x - facialCenterX;
        
        // 【医美专业】轮廓收紧的复合变形逻辑
        // 1. 水平收紧：向面部中心线方向收紧
        // 2. 垂直提升：轻微上提效果
        
        // 水平收紧变形
        double horizontalDeformation = -distanceFromCenter.abs() * deformationFactor * intensityFactor * 0.3;
        
        // 垂直提升变形（轻微）
        double verticalDeformation = -deformationFactor * intensityFactor * 0.15;
        
        // 根据点的位置调整变形策略
        if (pointIndex == 67 || pointIndex == 297) {
          // 下颌角：主要水平收紧
          horizontalDeformation *= 1.2;
          verticalDeformation *= 0.8;
        } else if (pointIndex == 71 || pointIndex == 301 || pointIndex == 68 || pointIndex == 298) {
          // 太阳穴区域：主要垂直提升
          horizontalDeformation *= 0.6;
          verticalDeformation *= 1.5;
        } else if (pointIndex == 104 || pointIndex == 333) {
          // 中颊点：均衡收紧
          horizontalDeformation *= 1.0;
          verticalDeformation *= 1.0;
        }
        
        // 应用变形
        double newX = point.x + (distanceFromCenter > 0 ? horizontalDeformation : -horizontalDeformation);
        double newY = point.y + verticalDeformation;
        
        // 更新特征点位置
        featurePoints[pointIndex] = updateFeaturePoint(point, newX: newX, newY: newY);
        
        Logger.flow(_logTag, 'applyFeaturePointTransformation',
            '特征点 $pointIndex: 原始位置(${point.x.toStringAsFixed(2)}, ${point.y.toStringAsFixed(2)}) → '
            '新位置(${newX.toStringAsFixed(2)}, ${newY.toStringAsFixed(2)}) '
            '变形量(${horizontalDeformation.toStringAsFixed(3)}, ${verticalDeformation.toStringAsFixed(3)}) '
            '强度系数: ${intensityFactor.toStringAsFixed(2)}');
      }
    }

    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '✅ 轮廓收紧变形应用完成，共处理 ${contourPoints.length} 个特征点');
    Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
  }

  @override
  ui.Image? applyImageTransformation(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double radius,
      double value,
      double intensity,
      ui.Image? image,
      bool showDeformationArea,
      double? facialCenterLineX,
      {bool? isIncreasing, List<FeaturePoint>? currentFeaturePoints}) {
    Logger.flowStart(_logTag, 'applyImageTransformation');
    
    // 【关键调试】记录输入图像信息
    Logger.flow(_logTag, 'applyImageTransformation', '📥 输入图像信息: 哈希码=${image?.hashCode}, 尺寸=${image?.width}x${image?.height}');
    
    // 【修复累积变形】不再使用getLatestDeformedImage，而是直接使用传入的image
    // 因为传入的image已经是SimpleDeformationRenderer中正确获取的累积图像
    Logger.flow(_logTag, 'applyImageTransformation', '📷 使用传入的累积图像进行变形: 哈希码=${image?.hashCode}');

    if (image == null) {
      Logger.flow(_logTag, 'applyImageTransformation', '❌ [错误] 图像为空，无法应用变形');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 即使参数值为0，也要执行变形，因为我们使用固定步长而非参数值
    if (value == 0.0) {
      Logger.i(_logTag, '✅ 轮廓收紧变形 | 参数值为0，但仍然执行变形');
    }

    // 【关键修复】由于Canvas现在使用原始图像坐标系，直接使用原始尺寸，不需要缩放计算
    final Rect dstRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final srcRect =
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());

    // 即使参数值为0，也要执行变形操作，确保变形连续性
    Logger.flow(_logTag, 'applyImageTransformation', '无论参数值如何，都执行变形操作，保持变形连续性');

    // 【关键修复】优先使用全局缓存的面部中心线，确保绝对对称性
    double facialCenterX;
    
    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用全局缓存的面部中心线: $facialCenterX (永久缓存)');
    } else if (facialCenterLineX != null) {
      // 次优先：使用传入的中心线
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      // 最后备选：使用画布中心（不推荐）
      facialCenterX = centerX;
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，使用画布中心可能导致不对称: $facialCenterX');
    }

    // 判断变形方向 - 唯一根据用户点击的是加号还是减号来确定
    // 如果点击加号，则isIncreasing为true；如果点击减号，则为false
    double direction = 0.0;

    // 检查是否提供了isIncreasing参数
    if (isIncreasing != null) {
      // 方向系数 - 唯一根据用户点击的按钮来确定
      direction = isIncreasing ? 1.0 : -1.0; // 点击加号为1.0，点击减号为-1.0
      Logger.flow(_logTag, 'applyImageTransformation',
          '🔍 变形方向根据用户点击确定: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 预期效果: ${isIncreasing ? "面部轮廓收紧，下颌线收窄" : "面部轮廓放松，下颌线放宽"}');
    } else {
      // 如果没有提供用户点击信息，则报错并中止变形
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 【修正】使用固定步长计算变形因子，确保变形的一致性
    // 所有策略类都使用统一的固定步长0.2，不依赖参数值
    double deformationFactor = direction * fixedStepSize;

    // 绘制面部中心线
    if (showDeformationArea) {
      // 绘制面部中心线
      Paint linePaint = Paint()
        ..color = Colors.green
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;
      canvas.drawLine(Offset(facialCenterX, 0),
          Offset(facialCenterX, size.height), linePaint);
    }

    // 计算有效半径（根据参数调整）
    // 【轮廓收紧最佳实践】使用中等范围半径，确保精确控制轮廓收紧范围
    double effectiveRadius = radius * 0.35; // 轮廓收紧变形使用适中的半径

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形半径已调整: $effectiveRadius (原半径: ${radius})');

    // 调整变形系数，使效果更加明显
    final double enhancedFactor = deformationFactor * 2.0; // 增加强度

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形参数 - 中心点: ($facialCenterX, $centerY), 半径: $effectiveRadius, 变形系数: $deformationFactor, 增强系数: $enhancedFactor');

    // 绘制原始图像作为背景，使用按比例缩放的矩形
    // 使用高质量绘制
    final paint = Paint()..filterQuality = FilterQuality.high;

    canvas.drawImageRect(image, srcRect, dstRect, paint);

    // 【关键修复】直接基于原始图像尺寸创建离屏画布，避免任何缩放问题
    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);

    // 【关键修复】定义更高的网格密度，确保高质量的变形效果
    final int gridSizeX = 200; // 增加网格密度
    final int gridSizeY = 200; // 增加网格密度

    // 【关键修复】直接使用原始图像尺寸计算网格，而不使用dstRect
    final double cellWidth = image.width.toDouble() / gridSizeX;
    final double cellHeight = image.height.toDouble() / gridSizeY;

    // 【关键修复】直接在原始图像坐标系下进行网格变形，避免坐标转换问题
    // 计算原始图像中的面部中心线和变形中心点坐标
    final double originalFacialCenterX = facialCenterX * image.width / size.width;
    final double originalCenterY = centerY * image.height / size.height;
    final double originalEffectiveRadius = effectiveRadius * image.width / size.width;

    // 【关键修复】在原始图像坐标系下计算排除区域范围（根据参数调整）
    // 【轮廓收紧专业】设置适当的保护区域，确保不影响眼部区域
    final double originalEyeAreaBottomY = originalCenterY - originalEffectiveRadius * 1.8;
    final double originalLipAreaTopY = originalCenterY + originalEffectiveRadius * 1.2;

    // 输出网格变形信息
    Logger.flow(_logTag, 'applyImageTransformation', '📅️ 网格变形设置');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 网格尺寸: ${gridSizeX}x${gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 总网格单元数: ${gridSizeX * gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 变形区域内网格单元数量估计: ${(math.pi * originalEffectiveRadius * originalEffectiveRadius / (image.width * image.height) * gridSizeX * gridSizeY).toInt()}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 原始图像坐标系: 中心线X=${originalFacialCenterX.toStringAsFixed(1)}, 中心Y=${originalCenterY.toStringAsFixed(1)}, 半径=${originalEffectiveRadius.toStringAsFixed(1)}');

    // 【对称性优化】对网格变形进行对称性优化处理
    // 记录左右两侧的变形网格数量，确保完全对称
    int leftSideGridCount = 0;
    int rightSideGridCount = 0;
    
    // 遍历网格
    for (int y = 0; y < gridSizeY; y++) {
      for (int x = 0; x < gridSizeX; x++) {
        // 【关键修复】直接在原始图像坐标系中计算网格单元
        final double left = x * cellWidth;
        final double top = y * cellHeight;
        final double right = (x + 1) * cellWidth;
        final double bottom = (y + 1) * cellHeight;

        // 计算网格单元中心点（原始图像坐标系）
        final double centerGridX = (left + right) / 2;
        final double centerGridY = (top + bottom) / 2;

        // 计算中心点到面部中心线的距离（原始图像坐标系）
        final double dx = centerGridX - originalFacialCenterX;
        final double distanceFromCenterLine = dx.abs();
        final double dy = centerGridY - originalCenterY;
        final distance = math.sqrt(dx * dx + dy * dy);

        // 计算变形量
        double offsetX = 0.0;
        double offsetY = 0.0;

        // 【关键修复】使用统一的原始图像坐标系进行判断
        // 【轮廓收紧专业】限制变形范围，确保仅影响下颌轮廓线
        bool shouldApplyDeformation = distance < originalEffectiveRadius && 
                                     centerGridY > originalEyeAreaBottomY &&
                                     centerGridY < originalLipAreaTopY;
        
        if (shouldApplyDeformation) {
          // 【对称性统计】记录左右两侧变形网格数量
          if (dx > 0) {
            rightSideGridCount++;
          } else if (dx < 0) {
            leftSideGridCount++;
          }
          
          // 计算变形系数，距离中心越近，变形越明显
          final double distanceRatio = distance / originalEffectiveRadius;
          // 使用线性与余弦混合的衰减函数，使过渡更加平滑
          final smoothFactor =
              (1.0 - distanceRatio) * math.cos(distanceRatio * math.pi / 2);

          // 【轮廓收紧专业】复合方向变形
          // 1. 水平收紧：向面部中心线方向收紧
          // 2. 垂直提升：轻微上提效果
          
          // 水平收紧变形 - 向中心线靠拢
          final double normalizedDistance = distanceFromCenterLine / originalEffectiveRadius;
          final double symmetricalMagnitude = normalizedDistance * smoothFactor * deformationFactor.abs() * 1.5;
          
          if (dx > 0) {
            // 在中心线右侧
            if (isIncreasing) {
              // 点击加号：轮廓收紧，右侧点向左移动（向中心线靠拢）
              offsetX = -symmetricalMagnitude;
            } else {
              // 点击减号：轮廓放松，右侧点向右移动（远离中心线）
              offsetX = symmetricalMagnitude;
            }
          } else if (dx < 0) {
            // 在中心线左侧
            if (isIncreasing) {
              // 点击加号：轮廓收紧，左侧点向右移动（向中心线靠拢）
              offsetX = symmetricalMagnitude;
            } else {
              // 点击减号：轮廓放松，左侧点向左移动（远离中心线）
              offsetX = -symmetricalMagnitude;
            }
          }

          // 垂直提升变形（轻微）
          offsetY = deformationFactor > 0 ?
              -dy * smoothFactor * deformationFactor.abs() * 0.8 :
              dy * smoothFactor * deformationFactor.abs() * 0.8;

          // 【关键修复】直接在原始图像坐标系中计算变形后的位置
          final double deformedSrcX = centerGridX - offsetX;
          final double deformedSrcY = centerGridY - offsetY;

          // 确保源坐标在图像范围内
          if (deformedSrcX >= 0 &&
              deformedSrcX < image.width &&
              deformedSrcY >= 0 &&
              deformedSrcY < image.height) {
            
            // 【关键修复】直接使用原始图像坐标系的矩形
            final srcUnitRect = Rect.fromLTWH(
                deformedSrcX - cellWidth / 2,
                deformedSrcY - cellHeight / 2, 
                cellWidth, 
                cellHeight);

            final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

            offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
          }
        } else {
          // 【关键修复】不在变形半径内，直接复制原始图像的对应部分
          final srcUnitRect = Rect.fromLTWH(
              centerGridX - cellWidth / 2,
              centerGridY - cellHeight / 2, 
              cellWidth, 
              cellHeight);

          final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

          offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
        }
      }
    }
    
    // 【对称性检查】输出左右两侧变形网格统计
    Logger.flow(_logTag, 'applyImageTransformation', 
        '📊 对称性统计: 左侧变形网格=${leftSideGridCount}个, 右侧变形网格=${rightSideGridCount}个, 差异=${(leftSideGridCount - rightSideGridCount).abs()}个');
    if ((leftSideGridCount - rightSideGridCount).abs() > 2) {
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 警告: 左右变形网格数量差异较大，可能导致不对称！');
    }

    // 【关键调试】记录变形处理前的图像状态
    Logger.flow(_logTag, 'applyImageTransformation', '🎯 开始创建变形图像:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 处理图像尺寸: ${image.width}x${image.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 目标输出尺寸: ${image.width}x${image.height}');
    
    // 【关键修复】直接从离屏画布创建与原始图像完全相同尺寸的变形图像
    final ui.Image finalImage = recorder.endRecording().toImageSync(image.width, image.height);
    
    // 【关键调试】立即验证创建的图像尺寸
    Logger.flow(_logTag, 'applyImageTransformation', '📤 创建的变形图像信息:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 实际输出尺寸: ${finalImage.width}x${finalImage.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 哈希码: ${finalImage.hashCode}');
    
    // 将变形图像绘制到主画布上（仅用于显示）
    canvas.drawImageRect(
        finalImage,
        Rect.fromLTWH(0, 0, finalImage.width.toDouble(), finalImage.height.toDouble()),
        dstRect,
        paint);

    // 如果需要显示变形区域，绘制变形区域边界
    if (showDeformationArea) {
      final deformationAreaRect = Rect.fromCenter(
          center: Offset(centerX, centerY),
          width: effectiveRadius * 2,
          height: effectiveRadius * 2);

      final borderPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawOval(deformationAreaRect, borderPaint);
    }

    // 【关键修复】验证图像尺寸一致性
    Logger.flow(_logTag, 'applyImageTransformation', '🔍 图像尺寸验证:');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 输入图像: ${image.width}x${image.height}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 输出图像: ${finalImage.width}x${finalImage.height}');
    
    if (finalImage.width != image.width || finalImage.height != image.height) {
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 严重错误: 图像尺寸不一致! 输入=${image.width}x${image.height}, 输出=${finalImage.width}x${finalImage.height}');
      Logger.flowError(_logTag, 'applyImageTransformation', '❌ 返回原始图像以避免尺寸不一致问题');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return image;
    }

    // 【关键修复】使用变形后的特征点数据，确保累积变形状态正确传递
    List<FeaturePoint>? featurePointsToSave;
    
    // 优先使用传入的当前特征点（这些应该是经过applyFeaturePointTransformation处理的）
    if (currentFeaturePoints != null && currentFeaturePoints.isNotEmpty) {
      featurePointsToSave = currentFeaturePoints;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用传入的变形后特征点: ${currentFeaturePoints.length}个');
    } else {
      // 备选方案：使用缓存中的最新特征点
      featurePointsToSave = DeformationCacheManager.getLatestDeformedFeaturePoints();
      Logger.flowWarning(_logTag, 'applyImageTransformation', 
          '⚠️ 警告: 没有传入变形后特征点，使用缓存数据，可能影响累积变形');
    }

    // 设置最新的变形状态，包括图像和特征点数据
    DeformationCacheManager.setLatestDeformedState(finalImage, featurePointsToSave);
    Logger.flow(_logTag, 'applyImageTransformation',
        '✅ 变形图像已保存到缓存 | 哈希码: ${finalImage.hashCode} | 尺寸: ${finalImage.width}x${finalImage.height}');

    Logger.flowEnd(_logTag, 'applyImageTransformation');
    return finalImage;
  }

  @override
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints,
      [double scaleX = 1.0]) {
    Logger.flowStart(_logTag, 'calculateFacialCenterLineX');

    // 使用鼻翼点计算面部中心线
    FeaturePoint? leftNostril;   // 左鼻翼点 - 索引129
    FeaturePoint? rightNostril;  // 右鼻翼点 - 索引358

    for (var point in featurePoints) {
      if (point.index == 129) {
        leftNostril = point;
      } else if (point.index == 358) {
        rightNostril = point;
      }
    }

    double facialCenterX;
    if (leftNostril != null && rightNostril != null) {
      facialCenterX = (leftNostril.x + rightNostril.x) / 2 * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用鼻翼点计算中心线: $facialCenterX');
    } else {
      // 备选方案：使用所有特征点的平均值
      facialCenterX = 0.0;
      for (var point in featurePoints) {
        facialCenterX += point.x;
      }
      facialCenterX = (facialCenterX / featurePoints.length) * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用所有特征点的平均X坐标作为中心线: $facialCenterX');
    }

    Logger.flowEnd(_logTag, 'calculateFacialCenterLineX');
    return facialCenterX;
  }

  @override
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint,
      {double? newX, double? newY}) {
    Logger.flow(_logTag, 'updateFeaturePoint',
        '更新特征点 ID=${oldPoint.index} 坐标: (${newX ?? oldPoint.x}, ${newY ?? oldPoint.y})');
    return FeaturePoint(
        id: oldPoint.id,
        index: oldPoint.index,
        x: newX ?? oldPoint.x,
        y: newY ?? oldPoint.y,
        z: oldPoint.z);
  }

  @override
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY,
      {Color color = Colors.red}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // 绘制垂直中心线
    canvas.drawLine(
        Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);

    // 绘制水平中心线
    canvas.drawLine(Offset(0, centerY), Offset(size.width, centerY), paint);
  }

  @override
  void resetState() {
    Logger.flow(_logTag, 'resetState', '重置轮廓收紧变形状态');
  }
}