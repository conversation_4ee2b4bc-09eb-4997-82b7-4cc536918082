import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/feature_point.dart';
import '../../utils/logger.dart';
import '../transform_type.dart';

/// 变形策略抽象类，定义所有变形器必须实现的方法
/// 改为抽象类而非接口，以便提供默认实现
abstract class TransformationStrategy {
  // 日志标签
  final String _logTag = 'TransformationStrategy';
  /// 日志标签
  String get logTag;
  
  /// 参数名称
  String get parameterName;
  
  /// 应用特征点变形
  /// [featurePoints] 特征点列表
  /// [pointIndexes] 特征点索引列表
  /// [value] 参数值，通常范围为-1.0到1.0
  /// [intensity] 变形强度系数
  void applyFeaturePointTransformation(
    List<FeaturePoint> featurePoints, 
    List<int> pointIndexes, 
    double value,
    double intensity,
    {double? facialCenterLineX, bool? isIncreasing}
  );
  
  /// 应用变形
  /// 封装特征点变形方法，便于在SimpleDeformationRenderer中使用
  /// [featurePoints] 特征点列表
  /// [pointIndexes] 特征点索引列表
  /// [value] 参数值，通常范围为-1.0到1.0
  /// [intensity] 变形强度系数
  /// 
  /// 默认实现，只调用特征点变形方法
  /// 注意：对于需要图像变形的策略，需要在SimpleDeformationRenderer中单独调用applyImageTransformation方法
  void applyTransformation(
    List<FeaturePoint> featurePoints, 
    List<int> pointIndexes, 
    double value,
    double intensity,
    {double? facialCenterLineX, bool? isIncreasing}
  ) {
    // 记录日志
    Logger.flow(logTag, 'applyTransformation', '开始应用变形，参数值: $value, 变形方向: ${isIncreasing != null ? (isIncreasing ? "增大" : "减小") : "未指定"}');
    
    // 【关键成功验证日志】变形策略调用层验证
    if (runtimeType.toString().contains('TipAdjustTransformation')) {
      print('═══════════════════════════════════════════════════════════');
      print('🎯 【关键验证】变形策略调用层成功');
      print('   ✅ 策略类型: $runtimeType');
      print('   ✅ 调用状态: 成功触发');
      print('   ✅ 参数传递: 正确传递');
      print('   ✅ 变形方向: ${isIncreasing != null ? (isIncreasing ? "增大" : "减小") : "未指定"}');
      print('══════════════════════════════════════════════════════════');
    }

    // 强制输出调试信息，不受过滤器影响
    print('🔍 TransformationStrategy.applyTransformation 调用开始');
    print('   • 策略类型: ${runtimeType}');
    print('   • 参数值: $value');
    print('   • 特征点数量: ${featurePoints.length}');
    print('   • 索引数量: ${pointIndexes.length}');
    print('   • 变形方向: ${isIncreasing != null ? (isIncreasing ? "增大" : "减小") : "未指定"}');
    print('   • 即将调用: applyFeaturePointTransformation');
    
    // 嘴角上扬特别调试
    if (runtimeType.toString().contains('MouthCornerTransformation')) {
      print('═══════════════════════════════════════════════════════════');
      print('🎯 【关键验证】TransformationStrategy嘴角上扬变形调用');
      print('   ✅ 策略类型: $runtimeType');
      print('   ✅ 调用状态: 成功触发');
      print('   ✅ 参数传递: 正确传递');
      print('   ✅ 变形方向: ${isIncreasing != null ? (isIncreasing ? "增大" : "减小") : "未指定"}');
      print('══════════════════════════════════════════════════════════');
    }
    
    // 调用特征点变形方法
    applyFeaturePointTransformation(
      featurePoints,
      pointIndexes,
      value,
      intensity,
      facialCenterLineX: facialCenterLineX,
      isIncreasing: isIncreasing
    );
    
    print('🔍 TransformationStrategy.applyTransformation 调用完成');
    
    // 【关键成功验证日志】变形策略执行完成验证
    if (runtimeType.toString().contains('TipAdjustTransformation')) {
      print('═══════════════════════════════════════════════════════════');
      print('🎯 【关键验证】鼻尖调整变形策略执行完成');
      print('   ✅ 策略执行状态: 成功完成');
      print('   ✅ 特征点变形: 已完成');
      print('   ✅ 变形传递: 成功传递给具体实现');
      print('   ✅ 调用链完整性: 已验证');
      print('══════════════════════════════════════════════════════════');
    }
  }
  
  /// 应用图像变形
  /// [canvas] 画布
  /// [size] 画布大小
  /// [centerX] 变形中心X坐标
  /// [centerY] 变形中心Y坐标
  /// [radius] 变形半径
  /// [value] 参数值，通常范围为-1.0到1.0
  /// [intensity] 变形强度系数
  /// [image] 原始图像
  /// [showDeformationArea] 是否显示变形区域
  /// [facialCenterLineX] 面部中心线X坐标
  ui.Image? applyImageTransformation(
    Canvas canvas, 
    Size size, 
    double centerX, 
    double centerY, 
    double radius, 
    double value,
    double intensity,
    ui.Image? image,
    bool showDeformationArea,
    double? facialCenterLineX,
    {bool? isIncreasing, List<FeaturePoint>? currentFeaturePoints}
  );
  
  /// 应用完整变形，包括图像和特征点变形
  /// 这个方法将在SimpleDeformationPainter中被调用，实现完整的变形过程
  /// 包括图像变形、特征点变形、缓存保存等
  /// 返回变形后的特征点列表和图像
  /// 默认实现为空，子类可以重写这个方法来提供完整的变形实现
  Future<Map<String, dynamic>?> applyCompleteTransformation(
    Canvas canvas,
    Size size,
    ui.Image baseImage,
    List<FeaturePoint> featurePoints,
    List<int> pointIndexes,
    double value,
    double intensity,
    double? facialCenterLineX,
    bool facialCenterLineCalculated,
    String valueChangeDirection,
    Function(ui.Image, List<FeaturePoint>)? onDeformationResultCallback,
    Function(Map<String, double>, ui.Image, List<FeaturePoint>, {double? facialCenterLineX, bool? facialCenterLineCalculated})? saveToCache,
    Map<String, double> allParameterValues
  ) async {
    Logger.log(_logTag, 'applyCompleteTransformation', '未实现的方法');
    return Future.value(null);
  }
  
  /// 处理特殊情况
  /// [oldValue] 旧参数值
  /// [newValue] 新参数值
  /// 返回是否需要强制重新计算变形
  /// 注意：此方法已被禁用，始终返回false
  bool handleSpecialCases(List<FeaturePoint> featurePoints, double value, bool isIncreasing) {
    return false;
  }
  
  /// 获取当前变形策略所需的变形类型
  /// 子类可以重写这个方法来指定他们需要的变形技术
  /// 默认使用mesh变形
  TransformType getRequiredTransformType() {
    return TransformType.mesh;
  }
  
  /// 计算面部中心线X坐标
  /// [featurePoints] 特征点列表
  /// [scaleX] X方向缩放因子
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints, [double scaleX = 1.0]);
  
  /// 更新特征点
  /// [oldPoint] 原特征点
  /// [newX] 新的X坐标
  /// [newY] 新的Y坐标
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint, {double? newX, double? newY});
  
  /// 绘制面部中心线
  /// [canvas] 画布
  /// [size] 画布大小
  /// [centerY] 中心Y坐标
  /// [color] 线条颜色
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY, {Color color = Colors.red});
}
