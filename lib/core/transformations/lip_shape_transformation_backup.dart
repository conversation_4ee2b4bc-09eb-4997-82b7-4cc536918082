import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/feature_point.dart';
import '../../utils/logger.dart';
import 'transformation_strategy.dart';
import '../deformation_cache_manager.dart';
import '../transform_type.dart';
import 'dart:typed_data';

/// 唇形调整变形策略实现
class LipShapeTransformation extends TransformationStrategy {
  static const String _logTag = 'LipShapeTransformation';

  @override
  String get logTag => _logTag;

  @override
  String get parameterName => 'lip_shape';

  // 固定步长值，用于所有变形计算
  static const double fixedStepSize = 0.2;
  

  @override
  void applyFeaturePointTransformation(List<FeaturePoint> featurePoints,
      List<int> pointIndexes, double value, double intensity,
      {double? facialCenterLineX, bool? isIncreasing}) {
    Logger.flowStart(_logTag, 'applyFeaturePointTransformation');

    // 无论参数值如何，都执行变形操作，确保变形连续性
    Logger.flow(_logTag, 'applyFeaturePointTransformation', '开始应用唇形调整变形');

    // 根据用户点击的是加号还是减号按钮来确定变形方向
    // isIncreasing为true表示点击加号，为false表示点击减号
    double direction = 0.0;

    if (isIncreasing != null) {
      direction = isIncreasing ? 1.0 : -1.0;
    } else {
      // 如果没有提供isIncreasing参数，则报错并中止变形
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
      return;
    }

    // 记录变形方向和预期效果
    if (isIncreasing!) {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📈 点击加号: 方向系数: $direction, 预期效果: 唇形丰满');
    } else {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📉 点击减号: 方向系数: $direction, 预期效果: 唇形变薄');
    }

    // 【标准】使用固定的步长值，不再使用强度参数
    // 这是变形系统的标准做法：固定步长确保每次变形幅度一致
    final double fixedStepSize = 0.2; // 固定步长
    // 【标准】简化变形因子计算，仅使用方向和固定步长
    // 计算公式：变形因子 = 方向系数 × 固定步长
    final deformationFactor = direction * fixedStepSize;
    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '变形因子: $deformationFactor (方向: ${isIncreasing! ? "加号" : "减号"}, 方向系数: $direction, 固定步长: $fixedStepSize)');

    // 【关键修复】优先使用全局缓存的面部中心线，确保绝对对称性
    double facialCenterX;
    
    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '✅ 使用全局缓存的面部中心线: $facialCenterX (永久缓存)');
    } else if (facialCenterLineX != null) {
      // 次优先：使用传入的中心线
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyFeaturePointTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      // 最后备选：重新计算（不推荐）
      facialCenterX = calculateFacialCenterLineX(featurePoints);
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，重新计算可能导致不对称: $facialCenterX');
    }

    // 有效半径，与图像变形保持一致
    final effectiveRadius = 120.0; // 唇部变形半径

    // 应用变形到所有特征点
    for (var index in pointIndexes) {
      final pointIndex = featurePoints.indexWhere((p) => p.index == index);
      if (pointIndex != -1) {
        final oldPoint = featurePoints[pointIndex];

        // 计算点到中心线的距离
        final dx = oldPoint.x - facialCenterX;
        final dy = oldPoint.y - (featurePoints.isNotEmpty ? 
            featurePoints.map((p) => p.y).reduce((a, b) => a + b) / featurePoints.length : 0);

        // 【关键】根据具体参数实现相应的变形逻辑
        // 唇形调整主要是Y方向变形（上下方向），辅以少量X方向变形

        // 判断是上唇还是下唇区域
        final avgY = featurePoints.map((p) => p.y).reduce((a, b) => a + b) / featurePoints.length;
        final isUpperLip = oldPoint.y < avgY;
        
        // Y方向变形（主要变形方向）- 大幅增强特征点变形效果
        double offsetY = 0.0;
        if (isIncreasing!) {
          // 点击加号：唇形丰满 - 上唇向上，下唇向下
          offsetY = isUpperLip ? -deformationFactor * 25.0 : deformationFactor * 25.0;
        } else {
          // 点击减号：唇形变薄 - 上唇向下，下唇向上
          offsetY = isUpperLip ? deformationFactor * 20.0 : -deformationFactor * 20.0;
        }

        // X方向辅助变形（次要变形方向）- 确保完全对称，增强效果
        double offsetX = 0.0;
        final double distanceFromCenter = dx.abs();
        if (dx > 0) {
          // 在中心线右侧
          if (isIncreasing!) {
            // 点击加号：唇形丰满，右侧点轻微向右移动
            offsetX = deformationFactor.abs() * 8.0;
          } else {
            // 点击减号：唇形变薄，右侧点轻微向左移动
            offsetX = -deformationFactor.abs() * 8.0;
          }
        } else if (dx < 0) {
          // 在中心线左侧
          if (isIncreasing!) {
            // 点击加号：唇形丰满，左侧点轻微向左移动
            offsetX = -deformationFactor.abs() * 8.0;
          } else {
            // 点击减号：唇形变薄，左侧点轻微向右移动
            offsetX = deformationFactor.abs() * 8.0;
          }
        }

        // 计算新的坐标
        final newX = oldPoint.x + offsetX;
        final newY = oldPoint.y + offsetY;

        // 替换原有特征点
        featurePoints[pointIndex] = updateFeaturePoint(oldPoint, newX: newX, newY: newY);

        // 记录变形后的坐标
        Logger.flow(_logTag, 'applyFeaturePointTransformation',
            '特征点 ID=${oldPoint.index} 变形后坐标: ($newX, $newY)，原坐标: (${oldPoint.x}, ${oldPoint.y})，偏移量: ($offsetX, $offsetY)');
      }
    }

    Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
  }
  
  /// 计算图像缩放矩形，确保图像按比例缩放并适应画布
  Rect _calculateScaledImageRect(ui.Image image, Size canvasSize) {
    final double imageAspectRatio = image.width / image.height;
    final double canvasAspectRatio = canvasSize.width / canvasSize.height;

    Rect dstRect;
    if (imageAspectRatio > canvasAspectRatio) {
      // 图像比画布更宽，以宽度为基准缩放
      final double scaledHeight = canvasSize.width / imageAspectRatio;
      final double topOffset = (canvasSize.height - scaledHeight) / 2;
      dstRect = Rect.fromLTWH(0, topOffset, canvasSize.width, scaledHeight);
    } else {
      // 图像比画布更高，以高度为基准缩放
      final double scaledWidth = canvasSize.height * imageAspectRatio;
      final double leftOffset = (canvasSize.width - scaledWidth) / 2;
      dstRect = Rect.fromLTWH(leftOffset, 0, scaledWidth, canvasSize.height);
    }

    return dstRect;
  }
  
  @override
  ui.Image? applyImageTransformation(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double radius,
      double value,
      double intensity,
      ui.Image? image,
      bool showDeformationArea,
      double? facialCenterLineX,
      {bool? isIncreasing}) {
    Logger.flowStart(_logTag, 'applyImageTransformation');
    
    // 【关键调试】记录输入图像信息
    Logger.flow(_logTag, 'applyImageTransformation', '📥 输入图像信息: 哈希码=${image?.hashCode}, 尺寸=${image?.width}x${image?.height}');
    
    // 重要：使用DeformationCacheManager获取最新的累积变形图像
    // 这确保了在参数项切换时保持累积变形状态
    ui.Image? accumulatedImage =
        DeformationCacheManager.getLatestDeformedImage();
    if (accumulatedImage != null && image != accumulatedImage) {
      Logger.flow(_logTag, 'applyImageTransformation', '🔄 检测到累积变形图像，准备切换');
      Logger.flow(_logTag, 'applyImageTransformation',
          '  • 当前输入图像: 哈希码=${image?.hashCode}, 尺寸=${image?.width}x${image?.height}');
      Logger.flow(_logTag, 'applyImageTransformation',
          '  • 累积变形图像: 哈希码=${accumulatedImage.hashCode}, 尺寸=${accumulatedImage.width}x${accumulatedImage.height}');
      
      // 【关键修复】检查尺寸一致性，如果累积图像尺寸不一致，则不使用
      if (image != null && (accumulatedImage.width != image.width || accumulatedImage.height != image.height)) {
        Logger.flowError(_logTag, 'applyImageTransformation', 
            '❌ 累积图像尺寸不一致，强制使用原始输入图像: 输入=${image.width}x${image.height}, 累积=${accumulatedImage.width}x${accumulatedImage.height}');
      } else {
        Logger.flow(_logTag, 'applyImageTransformation', '✅ 切换到累积变形图像');
        image = accumulatedImage;
      }
    } else {
      Logger.flow(_logTag, 'applyImageTransformation', '📷 使用原始输入图像进行变形');
    }

    if (image == null) {
      Logger.flow(_logTag, 'applyImageTransformation', '❌ [错误] 图像为空，无法应用变形');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 即使参数值为0，也要执行变形，因为我们使用固定步长而非参数值
    if (value == 0.0) {
      Logger.i(_logTag, '✅ 唇形调整变形 | 参数值为0，但仍然执行变形');
    }

    // 【关键修复】由于Canvas现在使用原始图像坐标系，直接使用原始尺寸，不需要缩放计算
    final Rect dstRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final srcRect =
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());

    // 即使参数值为0，也要执行变形操作，确保变形连续性
    Logger.flow(_logTag, 'applyImageTransformation', '无论参数值如何，都执行变形操作，保持变形连续性');

    // 【关键修复】优先使用全局缓存的面部中心线，确保绝对对称性
    double facialCenterX;
    
    // 最优先：使用全局缓存的面部中心线
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用全局缓存的面部中心线: $facialCenterX (永久缓存)');
    } else if (facialCenterLineX != null) {
      // 次优先：使用传入的中心线
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX (全局缓存不可用)');
    } else {
      // 最后备选：使用画布中心（不推荐）
      facialCenterX = centerX;
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 全局缓存和传入参数都不可用，使用画布中心可能导致不对称: $facialCenterX');
    }

    // 判断变形方向 - 唯一根据用户点击的是加号还是减号来确定
    // 如果点击加号，则isIncreasing为true；如果点击减号，则为false
    double direction = 0.0;

    // 检查是否提供了isIncreasing参数
    if (isIncreasing != null) {
      // 方向系数 - 唯一根据用户点击的按钮来确定
      direction = isIncreasing ? 1.0 : -1.0; // 点击加号为1.0，点击减号为-1.0
      Logger.flow(_logTag, 'applyImageTransformation',
          '🔍 变形方向根据用户点击确定: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 预期效果: ${isIncreasing ? "唇形丰满" : "唇形变薄"}');
    } else {
      // 如果没有提供用户点击信息，则报错并中止变形
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 【修正】使用固定的步长值计算变形因子，确保每次变化都是一个步长
    // 与applyFeaturePointTransformation方法保持一致，仅使用方向和固定步长
    final double stepSize = 0.2; // 固定步长
    // 【修正】计算变形因子 - 仅使用方向系数和固定步长，不使用intensity参数
    // 这确保了与特征点变形方法的计算一致性
    double deformationFactor = direction * stepSize;
    // 绘制面部中心线
    if (showDeformationArea) {
      // 绘制面部中心线
      Paint linePaint = Paint()
        ..color = Colors.green
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;
      canvas.drawLine(Offset(facialCenterX, 0),
          Offset(facialCenterX, size.height), linePaint);
    }

    // 计算有效半径（根据参数调整）- 使用更精确的半径限制唇部区域
    double effectiveRadius = radius * 0.4; // 唇形调整使用更小的半径，精确控制变形区域

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形半径已调整: $effectiveRadius (原半径: ${radius})');

    // 适度增强变形系数，确保效果自然
    final double enhancedFactor = deformationFactor * 5.0; // 适度增强唇形调整系数

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形参数 - 中心点: ($facialCenterX, $centerY), 半径: $effectiveRadius, 变形系数: $deformationFactor, 增强系数: $enhancedFactor');

    // 绘制原始图像作为背景，使用按比例缩放的矩形
    // 使用高质量绘制
    final paint = Paint()..filterQuality = FilterQuality.high;

    canvas.drawImageRect(image, srcRect, dstRect, paint);

    // 【关键修复】直接基于原始图像尺寸创建离屏画布，避免任何缩放问题
    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);
    
    // 【关键修复】为离屏画布填充白色背景，避免透明区域导致显示异常
    offscreenCanvas.drawRect(
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      Paint()..color = Colors.white..blendMode = BlendMode.src
    );

    // 【关键修复】定义更高的网格密度，确保高质量的变形效果
    final int gridSizeX = 200; // 增加网格密度
    final int gridSizeY = 200; // 增加网格密度

    // 【关键修复】直接使用原始图像尺寸计算网格，而不使用dstRect
    final double cellWidth = image.width.toDouble() / gridSizeX;
    final double cellHeight = image.height.toDouble() / gridSizeY;

    // 【关键修复】直接在原始图像坐标系下进行网格变形，避免坐标转换问题
    // 计算原始图像中的面部中心线和变形中心点坐标
    final double originalFacialCenterX = facialCenterX * image.width / size.width;
    
    // 【关键修复】对于唇形调整，变形中心应该是唇部的实际位置，不是画布中心
    // 使用唇部特征点计算唇部中心Y坐标
    List<FeaturePoint>? currentFeaturePoints = DeformationCacheManager.getLatestDeformedFeaturePoints();
    double lipCenterY = centerY; // 默认值
    
    if (currentFeaturePoints != null && currentFeaturePoints.isNotEmpty) {
      // 使用更多唇部关键特征点计算唇部中心
      List<FeaturePoint> upperLipPoints = [];
      List<FeaturePoint> lowerLipPoints = [];
      
      for (var point in currentFeaturePoints) {
        // 上唇特征点
        if (point.index == 13 || point.index == 0 || point.index == 267 || point.index == 37) {
          upperLipPoints.add(point);
        }
        // 下唇特征点
        if (point.index == 14 || point.index == 17 || point.index == 291 || point.index == 84) {
          lowerLipPoints.add(point);
        }
      }
      
      double upperLipY = 0.0;
      double lowerLipY = 0.0;
      
      if (upperLipPoints.isNotEmpty) {
        upperLipY = upperLipPoints.map((p) => p.y).reduce((a, b) => a + b) / upperLipPoints.length;
      }
      
      if (lowerLipPoints.isNotEmpty) {
        lowerLipY = lowerLipPoints.map((p) => p.y).reduce((a, b) => a + b) / lowerLipPoints.length;
      }
      
      if (upperLipPoints.isNotEmpty && lowerLipPoints.isNotEmpty) {
        lipCenterY = (upperLipY + lowerLipY) / 2;
        Logger.flow(_logTag, 'applyImageTransformation', 
            '✅ 使用唇部特征点计算的唇部中心Y坐标: $lipCenterY (上唇Y=$upperLipY, 下唇Y=$lowerLipY)');
      } else if (upperLipPoints.isNotEmpty) {
        lipCenterY = upperLipY;
        Logger.flow(_logTag, 'applyImageTransformation', 
            '⚠️ 仅使用上唇特征点计算的唇部中心Y坐标: $lipCenterY');
      } else if (lowerLipPoints.isNotEmpty) {
        lipCenterY = lowerLipY;
        Logger.flow(_logTag, 'applyImageTransformation', 
            '⚠️ 仅使用下唇特征点计算的唇部中心Y坐标: $lipCenterY');
      }
    }
    
    final double originalCenterY = lipCenterY * image.height / size.height;
    final double originalEffectiveRadius = effectiveRadius * image.width / size.width;

    // 输出网格变形信息
    Logger.flow(_logTag, 'applyImageTransformation', '📅️ 网格变形设置');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 网格尺寸: ${gridSizeX}x${gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 总网格单元数: ${gridSizeX * gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 变形区域内网格单元数量估计: ${(math.pi * originalEffectiveRadius * originalEffectiveRadius / (image.width * image.height) * gridSizeX * gridSizeY).toInt()}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 原始图像坐标系: 中心线X=${originalFacialCenterX.toStringAsFixed(1)}, 中心Y=${originalCenterY.toStringAsFixed(1)}, 半径=${originalEffectiveRadius.toStringAsFixed(1)}');

    // 【对称性优化】对网格变形进行对称性优化处理
    // 记录左右两侧的变形网格数量，确保完全对称
    int leftSideGridCount = 0;
    int rightSideGridCount = 0;
    
    // 遍历网格
    for (int y = 0; y < gridSizeY; y++) {
      for (int x = 0; x < gridSizeX; x++) {
        // 【关键修复】直接在原始图像坐标系中计算网格单元
        final double left = x * cellWidth;
        final double top = y * cellHeight;
        final double right = (x + 1) * cellWidth;
        final double bottom = (y + 1) * cellHeight;

        // 计算网格单元中心点（原始图像坐标系）
        final double centerGridX = (left + right) / 2;
        final double centerGridY = (top + bottom) / 2;

        // 计算中心点到面部中心线的距离（原始图像坐标系）
        final double dx = centerGridX - originalFacialCenterX;
        final double distanceFromCenterLine = dx.abs();
        final double dy = centerGridY - originalCenterY;
        final distance = math.sqrt(dx * dx + dy * dy);

        // 计算变形量
        double offsetX = 0.0;
        double offsetY = 0.0;

        // 【关键修复】使用更精确的条件判断唇部区域
        // 根据参数调整变形范围判断条件 - 精准锁定唇部区域
        // 水平方向限制更严格，垂直方向适当放宽
        bool inLipArea = distance < originalEffectiveRadius * 0.9 && 
                        distanceFromCenterLine < originalEffectiveRadius * 0.5 &&
                        centerGridY > originalCenterY - originalEffectiveRadius * 0.7 && 
                        centerGridY < originalCenterY + originalEffectiveRadius * 0.7;
                        
        if (inLipArea) {
          // 【对称性统计】记录左右两侧变形网格数量
          if (dx > 0) {
            rightSideGridCount++;
          } else if (dx < 0) {
            leftSideGridCount++;
          }
          
          // 计算变形系数，距离中心越近，变形越明显
          final double distanceRatio = distance / originalEffectiveRadius;
          // 使用二次衰减函数，确保变形平滑过渡
          final smoothFactor = math.pow(1.0 - distanceRatio, 2) * math.cos(distanceRatio * math.pi / 2);

          // 【关键】根据唇形调整特点实现相应的变形逻辑
          // 唇形调整主要是Y方向变形（上下方向），X方向辅助变形

          // 判断是上唇还是下唇区域
          final bool isUpperLip = centerGridY < originalCenterY;
          
          // Y方向变形（主要变形方向）- 适度增强变形效果
          if (isIncreasing) {
            // 点击加号：唇形丰满 - 上唇向上，下唇向下
            offsetY = isUpperLip ? 
                -dy * smoothFactor * deformationFactor.abs() * 6.0 : // 上唇向上，适度增强
                dy * smoothFactor * deformationFactor.abs() * 6.0;   // 下唇向下，适度增强
          } else {
            // 点击减号：唇形变薄 - 上唇向下，下唇向上
            offsetY = isUpperLip ? 
                dy * smoothFactor * deformationFactor.abs() * 5.0 :  // 上唇向下，适度增强
                -dy * smoothFactor * deformationFactor.abs() * 5.0;  // 下唇向上，适度增强
          }

          // 【修正】X方向辅助变形（次要变形方向）- 确保完全对称，适度效果
          // 【强化对称性】使用标准化的距离和统一的幅度计算
          final double normalizedDistance = distanceFromCenterLine / originalEffectiveRadius;
          final double symmetricalMagnitude = normalizedDistance * smoothFactor * deformationFactor.abs() * 2.5; // 适度X方向变形
          
          if (dx > 0) {
            // 在中心线右侧
            if (isIncreasing) {
              // 点击加号：唇形丰满，右侧点轻微向右移动
              offsetX = symmetricalMagnitude;
            } else {
              // 点击减号：唇形变薄，右侧点轻微向左移动
              offsetX = -symmetricalMagnitude;
            }
          } else if (dx < 0) {
            // 在中心线左侧
            if (isIncreasing) {
              // 点击加号：唇形丰满，左侧点轻微向左移动
              offsetX = -symmetricalMagnitude;
            } else {
              // 点击减号：唇形变薄，左侧点轻微向右移动
              offsetX = symmetricalMagnitude;
            }
          }

          // 【关键修复】直接在原始图像坐标系中计算变形后的位置
          final double deformedSrcX = centerGridX - offsetX;
          final double deformedSrcY = centerGridY - offsetY;

          // 确保源坐标在图像范围内
          if (deformedSrcX >= 0 &&
              deformedSrcX < image.width &&
              deformedSrcY >= 0 &&
              deformedSrcY < image.height) {
            
            // 【关键修复】直接使用原始图像坐标系的矩形
            final srcUnitRect = Rect.fromLTWH(
                deformedSrcX - cellWidth / 2,
                deformedSrcY - cellHeight / 2, 
                cellWidth, 
                cellHeight);

            final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

            offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
          }
        } else {
          // 【关键修复】不在变形半径内，直接复制原始图像的对应部分
          final srcUnitRect = Rect.fromLTWH(
              centerGridX - cellWidth / 2,
              centerGridY - cellHeight / 2, 
              cellWidth, 
              cellHeight);

          final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);

          offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
        }
      }
    }
    
    // 【对称性检查】输出左右两侧变形网格统计
    Logger.flow(_logTag, 'applyImageTransformation', 
        '📊 对称性统计: 左侧变形网格=${leftSideGridCount}个, 右侧变形网格=${rightSideGridCount}个, 差异=${(leftSideGridCount - rightSideGridCount).abs()}个');
    if ((leftSideGridCount - rightSideGridCount).abs() > 2) {
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 警告: 左右变形网格数量差异较大，可能导致不对称！');
    }
    // 【关键调试】记录变形处理前的图像状态
    Logger.flow(_logTag, 'applyImageTransformation', '🎯 开始创建变形图像:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 处理图像尺寸: ${image.width}x${image.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 目标输出尺寸: ${image.width}x${image.height}');
    
    // 【关键修复】直接从离屏画布创建与原始图像完全相同尺寸的变形图像
    final ui.Image finalImage = recorder.endRecording().toImageSync(image.width, image.height);
    
    // 【关键调试】立即验证创建的图像尺寸
    Logger.flow(_logTag, 'applyImageTransformation', '📤 创建的变形图像信息:');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 实际输出尺寸: ${finalImage.width}x${finalImage.height}');
    Logger.flow(_logTag, 'applyImageTransformation', '  • 哈希码: ${finalImage.hashCode}');
    
    // 将变形图像绘制到主画布上（仅用于显示）
    canvas.drawImageRect(
        finalImage,
        Rect.fromLTWH(0, 0, finalImage.width.toDouble(), finalImage.height.toDouble()),
        dstRect,
        paint);

    // 如果需要显示变形区域，绘制变形区域边界
    if (showDeformationArea) {
      final deformationAreaRect = Rect.fromCenter(
          center: Offset(centerX, centerY),
          width: effectiveRadius * 2,
          height: effectiveRadius * 2);

      final borderPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawOval(deformationAreaRect, borderPaint);
    }

    // 【关键修复】验证图像尺寸一致性
    Logger.flow(_logTag, 'applyImageTransformation', '🔍 图像尺寸验证:');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 输入图像: ${image.width}x${image.height}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 输出图像: ${finalImage.width}x${finalImage.height}');
    
    if (finalImage.width != image.width || finalImage.height != image.height) {
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 严重错误: 图像尺寸不一致! 输入=${image.width}x${image.height}, 输出=${finalImage.width}x${finalImage.height}');
      Logger.flowError(_logTag, 'applyImageTransformation', '❌ 返回原始图像以避免尺寸不一致问题');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return image;
    }

    // 获取当前特征点数据
    List<FeaturePoint>? latestFeaturePoints =
        DeformationCacheManager.getLatestDeformedFeaturePoints();

    // 设置最新的变形状态，包括图像和特征点数据
    DeformationCacheManager.setLatestDeformedState(finalImage, latestFeaturePoints);
    Logger.flow(_logTag, 'applyImageTransformation',
        '✅ 变形图像已保存到缓存 | 哈希码: ${finalImage.hashCode} | 尺寸: ${finalImage.width}x${finalImage.height}');

    Logger.flowEnd(_logTag, 'applyImageTransformation');
    return finalImage;
  }

  @override
  bool handleSpecialCases(
      List<FeaturePoint> featurePoints, double value, bool isIncreasing) {
    return false;
  }

  @override
  TransformType getRequiredTransformType() {
    return TransformType.local;
  }

  @override
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints,
      [double scaleX = 1.0]) {
    Logger.flowStart(_logTag, 'calculateFacialCenterLineX');

    // 【修正】使用正确的MediaPipe特征点索引计算面部中心线
    // 使用与唇形调整相关的特征点来确保一致性
    FeaturePoint? leftLipCorner;   // 左嘴角 - 索引146
    FeaturePoint? rightLipCorner;  // 右嘴角 - 索引375
    FeaturePoint? upperLipCenter;  // 上唇中心点 - 索弖13 (人中)
    FeaturePoint? lowerLipCenter;  // 下唇中心点 - 索弖14

    // 遍历特征点，找出关键的对称特征点
    for (var point in featurePoints) {
      if (point.index == 146) {
        leftLipCorner = point;
      } else if (point.index == 375) {
        rightLipCorner = point;
      } else if (point.index == 13) {
        upperLipCenter = point;
      } else if (point.index == 14) {
        lowerLipCenter = point;
      }
    }

    // 优先使用嘴角点计算中心线（最对称的特征点对）
    double facialCenterX;
    if (leftLipCorner != null && rightLipCorner != null) {
      facialCenterX = (leftLipCorner.x + rightLipCorner.x) / 2 * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用嘴角点计算中心线: $facialCenterX (左嘴角${leftLipCorner.index}: ${leftLipCorner.x}, 右嘴角${rightLipCorner.index}: ${rightLipCorner.x})');
    } else if (upperLipCenter != null && lowerLipCenter != null) {
      // 备选方案：使用上下唇中心点的中点
      facialCenterX = (upperLipCenter.x + lowerLipCenter.x) / 2 * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用上下唇中心点计算中心线: $facialCenterX (上唇${upperLipCenter.index}: ${upperLipCenter.x}, 下唇${lowerLipCenter.index}: ${lowerLipCenter.x})');
    } else {
      // 最后备选：使用所有特征点的平均值
      facialCenterX = 0.0;
      for (var point in featurePoints) {
        facialCenterX += point.x;
      }
      facialCenterX = (facialCenterX / featurePoints.length) * scaleX;
      Logger.flow(_logTag, 'calculateFacialCenterLineX',
          '使用所有特征点的平均X坐标作为中心线: $facialCenterX');
    }

    Logger.flow(
        _logTag, 'calculateFacialCenterLineX', '【修正】面部中心线已固定: $facialCenterX');
    Logger.flowEnd(_logTag, 'calculateFacialCenterLineX');

    return facialCenterX;
  }
  
  @override
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint,
      {double? newX, double? newY}) {
    Logger.flow(_logTag, 'updateFeaturePoint',
        '更新特征点 ID=${oldPoint.index} 坐标: (${newX ?? oldPoint.x}, ${newY ?? oldPoint.y})');
    return FeaturePoint(
        id: oldPoint.id,
        index: oldPoint.index,
        x: newX ?? oldPoint.x,
        y: newY ?? oldPoint.y,
        z: oldPoint.z);
  }

  @override
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY,
      {Color color = Colors.red}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // 绘制垂直中心线
    canvas.drawLine(
        Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);

    // 绘制水平中心线
    canvas.drawLine(Offset(0, centerY), Offset(size.width, centerY), paint);
  }

  @override
  void resetState() {
    // 简化重置状态逻辑
    Logger.flow(_logTag, 'resetState', '重置唇形调整变形状态');
  }
}