import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/feature_point.dart';
import '../../utils/logger.dart';
import 'transformation_strategy.dart';
import '../deformation_cache_manager.dart';
import '../transform_type.dart';
import 'dart:typed_data';

/// 面容紧致变形策略实现
class FacialFirmnessTransformation extends TransformationStrategy {
  static const String _logTag = 'FacialFirmnessTransformation';

  @override
  String get logTag => _logTag;

  @override
  String get parameterName => 'facial_firmness';

  // 固定步长值，用于所有变形计算
  static const double fixedStepSize = 0.2;

  @override
  void applyFeaturePointTransformation(List<FeaturePoint> featurePoints,
      List<int> pointIndexes, double value, double intensity,
      {double? facialCenterLineX, bool? isIncreasing}) {
    Logger.flowStart(_logTag, 'applyFeaturePointTransformation');

    Logger.flow(_logTag, 'applyFeaturePointTransformation', '开始应用面容紧致变形');

    // 根据用户点击的是加号还是减号按钮来确定变形方向
    double direction = 0.0;

    if (isIncreasing != null) {
      direction = isIncreasing ? 1.0 : -1.0;
    } else {
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
      return;
    }

    // 记录变形方向和预期效果
    if (isIncreasing) {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📈 点击加号: 方向系数: $direction, 预期效果: 面容紧致，更加年轻');
    } else {
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '📉 点击减号: 方向系数: $direction, 预期效果: 面容还原，回到自然状态');
    }

    // 【架构统一】使用纯策略模式的标准变形因子计算
    final deformationFactor = direction * fixedStepSize;
    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '变形因子: $deformationFactor (方向: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 固定步长: $fixedStepSize)');

    // 【关键修复】优先使用全局缓存的面部中心线
    double facialCenterX;
    
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyFeaturePointTransformation',
          '✅ 使用全局缓存的面部中心线: $facialCenterX');
    } else if (facialCenterLineX != null) {
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyFeaturePointTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX');
    } else {
      facialCenterX = calculateFacialCenterLineX(featurePoints);
      Logger.flowError(_logTag, 'applyFeaturePointTransformation',
          '❌ 错误: 重新计算面部中心线可能导致不对称: $facialCenterX');
    }

    // 【医美专业】面容紧致的关键特征点选择
    List<int> facialFirmnessPoints = [
      // 面部轮廓紧致
      172, 136, 150, 149, 176, 148, 152, 377, 400, 378, 379, 365, 397, 288, 361, 323,
      // 面颈线条紧致
      172, 136, 150, 149, 176, 148, 152, 377, 400, 378, 379, 365, 397, 288, 361, 323,
      // 面部提升区域
      234, 227, 137, 177, 215, 138, 135, 169, 170, 140, 171, 175, 396, 369, 395, 394,
    ];
    
    // 【医美专业】精确的强度因子映射
    final Map<int, double> intensityFactors = {
      172: 1.0, 136: 0.9, 150: 0.8, 149: 0.9, 176: 0.8, 148: 0.9, 152: 1.0,
      377: 1.0, 400: 0.9, 378: 0.8, 379: 0.9, 365: 0.8, 397: 0.9, 288: 0.8, 361: 0.9, 323: 1.0,
      234: 0.8, 227: 0.9, 137: 0.7, 177: 0.8, 215: 0.9, 138: 0.7, 135: 0.8, 169: 0.7, 170: 0.8,
      140: 0.9, 171: 0.8, 175: 0.9, 396: 0.8, 369: 0.9, 395: 0.7, 394: 0.8,
    };

    // 【医美专业】应用面容紧致变形
    for (int pointIndex in facialFirmnessPoints) {
      if (pointIndex < featurePoints.length) {
        final point = featurePoints[pointIndex];
        final intensityFactor = intensityFactors[pointIndex] ?? 0.5;
        
        // 计算点与面部中心线的距离
        final distanceFromCenter = point.x - facialCenterX;
        
        double horizontalDeformation = 0.0;
        double verticalDeformation = 0.0;
        
        // 根据点的位置应用不同的变形策略
        if (pointIndex >= 172 && pointIndex <= 152) {
          // 下面部轮廓：向上提拉
          verticalDeformation = -deformationFactor * intensityFactor * 0.5;
          horizontalDeformation = -distanceFromCenter.abs() * deformationFactor * intensityFactor * 0.2;
        } else if (pointIndex >= 377 && pointIndex <= 323) {
          // 上面部轮廓：向上提拉
          verticalDeformation = -deformationFactor * intensityFactor * 0.4;
          horizontalDeformation = -distanceFromCenter.abs() * deformationFactor * intensityFactor * 0.15;
        } else {
          // 面部中部区域：整体提升
          verticalDeformation = -deformationFactor * intensityFactor * 0.3;
          horizontalDeformation = -distanceFromCenter.abs() * deformationFactor * intensityFactor * 0.1;
        }
        
        // 应用变形
        double newX = point.x + (distanceFromCenter > 0 ? horizontalDeformation : -horizontalDeformation);
        double newY = point.y + verticalDeformation;
        
        // 更新特征点位置
        featurePoints[pointIndex] = updateFeaturePoint(point, newX: newX, newY: newY);
        
        Logger.flow(_logTag, 'applyFeaturePointTransformation',
            '特征点 $pointIndex: 原始位置(${point.x.toStringAsFixed(2)}, ${point.y.toStringAsFixed(2)}) → '
            '新位置(${newX.toStringAsFixed(2)}, ${newY.toStringAsFixed(2)}) '
            '变形量(${horizontalDeformation.toStringAsFixed(3)}, ${verticalDeformation.toStringAsFixed(3)}) '
            '强度系数: ${intensityFactor.toStringAsFixed(2)}');
      }
    }

    Logger.flow(_logTag, 'applyFeaturePointTransformation',
        '✅ 面容紧致变形应用完成，共处理 ${facialFirmnessPoints.length} 个特征点');
    Logger.flowEnd(_logTag, 'applyFeaturePointTransformation');
  }

  @override
  ui.Image? applyImageTransformation(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double radius,
      double value,
      double intensity,
      ui.Image? image,
      bool showDeformationArea,
      double? facialCenterLineX,
      {bool? isIncreasing, List<FeaturePoint>? currentFeaturePoints}) {
    Logger.flowStart(_logTag, 'applyImageTransformation');
    
    // 【关键调试】记录输入图像信息
    Logger.flow(_logTag, 'applyImageTransformation', '📥 输入图像信息: 哈希码=${image?.hashCode}, 尺寸=${image?.width}x${image?.height}');
    
    if (image == null) {
      Logger.flow(_logTag, 'applyImageTransformation', '❌ [错误] 图像为空，无法应用变形');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 【关键修复】优先使用全局缓存的面部中心线
    double facialCenterX;
    
    double? globalCenterX = DeformationCacheManager.getGlobalFacialCenterLineX();
    if (globalCenterX != null) {
      facialCenterX = globalCenterX;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用全局缓存的面部中心线: $facialCenterX');
    } else if (facialCenterLineX != null) {
      facialCenterX = facialCenterLineX;
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 使用传入的面部中心线: $facialCenterX');
    } else {
      facialCenterX = centerX;
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 使用画布中心可能导致不对称: $facialCenterX');
    }

    // 判断变形方向
    double direction = 0.0;
    if (isIncreasing != null) {
      direction = isIncreasing ? 1.0 : -1.0;
      Logger.flow(_logTag, 'applyImageTransformation',
          '🔍 变形方向: ${isIncreasing ? "加号" : "减号"}, 方向系数: $direction, 预期效果: ${isIncreasing ? "面容紧致，更加年轻" : "面容还原"}');
    } else {
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 错误: 必须提供isIncreasing参数');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return null;
    }

    // 使用固定步长计算变形因子
    double deformationFactor = direction * fixedStepSize;

    // 绘制面部中心线
    if (showDeformationArea) {
      Paint linePaint = Paint()
        ..color = Colors.green
        ..strokeWidth = 2.0
        ..style = PaintingStyle.stroke;
      canvas.drawLine(Offset(facialCenterX, 0),
          Offset(facialCenterX, size.height), linePaint);
    }

    // 计算有效半径 - 面容紧致使用较大半径，涵盖整个面部轮廓
    double effectiveRadius = radius * 0.5; // 较大的控制范围，面部整体提升

    Logger.flow(_logTag, 'applyImageTransformation',
        '变形参数 - 中心点: ($facialCenterX, $centerY), 半径: $effectiveRadius, 变形系数: $deformationFactor');

    // 绘制原始图像作为背景
    final paint = Paint()..filterQuality = FilterQuality.high;
    final Rect dstRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final srcRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    canvas.drawImageRect(image, srcRect, dstRect, paint);

    // 创建离屏画布
    final recorder = ui.PictureRecorder();
    final offscreenCanvas = Canvas(recorder);

    // 定义网格密度
    final int gridSizeX = 200;
    final int gridSizeY = 200;

    // 计算网格尺寸
    final double cellWidth = image.width.toDouble() / gridSizeX;
    final double cellHeight = image.height.toDouble() / gridSizeY;

    // 计算原始图像坐标系下的参数
    final double originalFacialCenterX = facialCenterX * image.width / size.width;
    final double originalCenterY = centerY * image.height / size.height;
    final double originalEffectiveRadius = effectiveRadius * image.width / size.width;

    // 定义面容紧致区域边界
    final double originalForeheadAreaTopY = originalCenterY - originalEffectiveRadius * 1.2;
    final double originalChinAreaBottomY = originalCenterY + originalEffectiveRadius * 1.5;

    Logger.flow(_logTag, 'applyImageTransformation', '📅️ 网格变形设置');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 网格尺寸: ${gridSizeX}x${gridSizeY}');
    Logger.flow(_logTag, 'applyImageTransformation',
        '  • 原始图像坐标系: 中心线X=${originalFacialCenterX.toStringAsFixed(1)}, 中心Y=${originalCenterY.toStringAsFixed(1)}, 半径=${originalEffectiveRadius.toStringAsFixed(1)}');

    // 对称性统计
    int leftSideGridCount = 0;
    int rightSideGridCount = 0;
    
    // 遍历网格
    for (int y = 0; y < gridSizeY; y++) {
      for (int x = 0; x < gridSizeX; x++) {
        final double left = x * cellWidth;
        final double top = y * cellHeight;
        final double right = (x + 1) * cellWidth;
        final double bottom = (y + 1) * cellHeight;

        // 计算网格单元中心点
        final double centerGridX = (left + right) / 2;
        final double centerGridY = (top + bottom) / 2;

        // 计算到面部中心线的距离
        final double dx = centerGridX - originalFacialCenterX;
        final double distanceFromCenterLine = dx.abs();
        final double dy = centerGridY - originalCenterY;
        final distance = math.sqrt(dx * dx + dy * dy);

        // 计算变形量
        double offsetX = 0.0;
        double offsetY = 0.0;

        // 判断是否在面容紧致区域内
        bool shouldApplyDeformation = distance < originalEffectiveRadius && 
            centerGridY > originalForeheadAreaTopY && 
            centerGridY < originalChinAreaBottomY;
        
        if (shouldApplyDeformation) {
          // 对称性统计
          if (dx > 0) {
            rightSideGridCount++;
          } else if (dx < 0) {
            leftSideGridCount++;
          }
          
          // 计算变形系数
          final double distanceRatio = distance / originalEffectiveRadius;
          final smoothFactor = (1.0 - distanceRatio) * math.cos(distanceRatio * math.pi / 2);

          // 根据位置应用不同的面容紧致策略
          if (centerGridY >= originalCenterY + originalEffectiveRadius * 0.5) {
            // 下面部轮廓：向上提拉
            offsetY = deformationFactor > 0 ?
                -dy * smoothFactor * deformationFactor.abs() * 1.2 :
                dy * smoothFactor * deformationFactor.abs() * 1.2;
            offsetX = -distanceFromCenterLine * smoothFactor * deformationFactor.abs() * 0.6;
          } else if (centerGridY <= originalCenterY - originalEffectiveRadius * 0.3) {
            // 上面部轮廓：向上提拉
            offsetY = deformationFactor > 0 ?
                -dy * smoothFactor * deformationFactor.abs() * 1.0 :
                dy * smoothFactor * deformationFactor.abs() * 1.0;
            offsetX = -distanceFromCenterLine * smoothFactor * deformationFactor.abs() * 0.4;
          } else {
            // 面部中部区域：整体提升
            offsetY = deformationFactor > 0 ?
                -dy * smoothFactor * deformationFactor.abs() * 0.8 :
                dy * smoothFactor * deformationFactor.abs() * 0.8;
            offsetX = -distanceFromCenterLine * smoothFactor * deformationFactor.abs() * 0.3;
          }

          // 计算变形后的位置
          final double deformedSrcX = centerGridX - offsetX;
          final double deformedSrcY = centerGridY - offsetY;

          // 确保源坐标在图像范围内
          if (deformedSrcX >= 0 && deformedSrcX < image.width &&
              deformedSrcY >= 0 && deformedSrcY < image.height) {
            
            final srcUnitRect = Rect.fromLTWH(
                deformedSrcX - cellWidth / 2,
                deformedSrcY - cellHeight / 2, 
                cellWidth, 
                cellHeight);

            final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);
            offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
          }
        } else {
          // 不在变形区域内，直接复制
          final srcUnitRect = Rect.fromLTWH(
              centerGridX - cellWidth / 2,
              centerGridY - cellHeight / 2, 
              cellWidth, 
              cellHeight);

          final dstUnitRect = Rect.fromLTWH(left, top, cellWidth, cellHeight);
          offscreenCanvas.drawImageRect(image, srcUnitRect, dstUnitRect, paint);
        }
      }
    }
    
    // 对称性检查
    Logger.flow(_logTag, 'applyImageTransformation', 
        '📊 对称性统计: 左侧变形网格=${leftSideGridCount}个, 右侧变形网格=${rightSideGridCount}个, 差异=${(leftSideGridCount - rightSideGridCount).abs()}个');
    if ((leftSideGridCount - rightSideGridCount).abs() > 2) {
      Logger.flowWarning(_logTag, 'applyImageTransformation',
          '⚠️ 警告: 左右变形网格数量差异较大，可能导致不对称！');
    }

    // 创建变形图像
    final ui.Image finalImage = recorder.endRecording().toImageSync(image.width, image.height);
    
    // 将变形图像绘制到主画布上
    canvas.drawImageRect(
        finalImage,
        Rect.fromLTWH(0, 0, finalImage.width.toDouble(), finalImage.height.toDouble()),
        dstRect,
        paint);

    // 显示变形区域
    if (showDeformationArea) {
      final deformationAreaRect = Rect.fromCenter(
          center: Offset(centerX, centerY),
          width: effectiveRadius * 2,
          height: effectiveRadius * 2);

      final borderPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawOval(deformationAreaRect, borderPaint);
    }

    // 验证图像尺寸一致性
    if (finalImage.width != image.width || finalImage.height != image.height) {
      Logger.flowError(_logTag, 'applyImageTransformation',
          '❌ 严重错误: 图像尺寸不一致!');
      Logger.flowEnd(_logTag, 'applyImageTransformation');
      return image;
    }

    // 保存变形状态
    List<FeaturePoint>? featurePointsToSave;
    if (currentFeaturePoints != null && currentFeaturePoints.isNotEmpty) {
      featurePointsToSave = currentFeaturePoints;
      Logger.flow(_logTag, 'applyImageTransformation', 
          '✅ 使用传入的变形后特征点: ${currentFeaturePoints.length}个');
    } else {
      featurePointsToSave = DeformationCacheManager.getLatestDeformedFeaturePoints();
      Logger.flowWarning(_logTag, 'applyImageTransformation', 
          '⚠️ 警告: 没有传入变形后特征点，使用缓存数据');
    }

    DeformationCacheManager.setLatestDeformedState(finalImage, featurePointsToSave);
    Logger.flow(_logTag, 'applyImageTransformation',
        '✅ 变形图像已保存到缓存 | 哈希码: ${finalImage.hashCode} | 尺寸: ${finalImage.width}x${finalImage.height}');

    Logger.flowEnd(_logTag, 'applyImageTransformation');
    return finalImage;
  }

  @override
  double calculateFacialCenterLineX(List<FeaturePoint> featurePoints, [double scaleX = 1.0]) {
    Logger.flowStart(_logTag, 'calculateFacialCenterLineX');

    FeaturePoint? leftNostril;   // 左鼻翼点 - 索引129
    FeaturePoint? rightNostril;  // 右鼻翼点 - 索引358

    for (var point in featurePoints) {
      if (point.index == 129) {
        leftNostril = point;
      } else if (point.index == 358) {
        rightNostril = point;
      }
    }

    double facialCenterX;
    if (leftNostril != null && rightNostril != null) {
      facialCenterX = (leftNostril.x + rightNostril.x) / 2 * scaleX;
    } else {
      facialCenterX = 0.0;
      for (var point in featurePoints) {
        facialCenterX += point.x;
      }
      facialCenterX = (facialCenterX / featurePoints.length) * scaleX;
    }

    Logger.flowEnd(_logTag, 'calculateFacialCenterLineX');
    return facialCenterX;
  }

  @override
  FeaturePoint updateFeaturePoint(FeaturePoint oldPoint, {double? newX, double? newY}) {
    return FeaturePoint(
        id: oldPoint.id,
        index: oldPoint.index,
        x: newX ?? oldPoint.x,
        y: newY ?? oldPoint.y,
        z: oldPoint.z);
  }

  @override
  void drawFacialCenterLine(Canvas canvas, Size size, double centerY, {Color color = Colors.red}) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(Offset(size.width / 2, 0), Offset(size.width / 2, size.height), paint);
    canvas.drawLine(Offset(0, centerY), Offset(size.width, centerY), paint);
  }

  @override
  void resetState() {
    Logger.flow(_logTag, 'resetState', '重置面容紧致变形状态');
  }
}