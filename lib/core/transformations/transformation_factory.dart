import '../../utils/logger.dart';
import 'transformation_strategy.dart';
import 'nostril_width_transformation.dart';
import 'tip_adjust_transformation.dart';
import 'base_height_transformation.dart';
import 'lip_shape_transformation.dart';
import 'v_chin_transformation.dart';
// 面部轮廓变形策略
import 'contour_tighten_transformation.dart';
import 'nose_length_transformation.dart';
import 'face_shape_transformation.dart';
// 眼部美化变形策略
import 'double_fold_transformation.dart';
import 'canthal_tilt_transformation.dart';
import 'eye_bag_removal_transformation.dart';
import 'outer_corner_lift_transformation.dart';
// 唇部造型变形策略
import 'mouth_corner_transformation.dart';
// 抗衰冻龄变形策略
import 'nasolabial_folds_transformation.dart';
import 'wrinkle_removal_transformation.dart';
import 'forehead_fullness_transformation.dart';
import 'facial_firmness_transformation.dart';

/// 变形策略工厂类，负责创建和管理各种变形策略
class TransformationFactory {
  static const String _logTag = 'TransformationFactory';
  
  /// 单例实例
  static final TransformationFactory _instance = TransformationFactory._internal();
  
  /// 获取单例实例
  factory TransformationFactory() => _instance;
  
  /// 内部构造函数
  TransformationFactory._internal();
  
  /// 变形策略映射表 - 包含所有已实现的变形策略
  final Map<String, TransformationStrategy> _strategies = {
    // 鼻部变形策略
    'nose_length': NoseLengthTransformation(),
    'nostril_width': NostrilWidthTransformation(),
    'tip_adjust': TipAdjustTransformation(),
    'base_height': BaseHeightTransformation(),
    
    // 面部轮廓变形策略
    'contour_tighten': ContourTightenTransformation(),
    'v_chin': VChinTransformation(),
    'face_shape': FaceShapeTransformation(),
    
    // 眼部美化变形策略
    'double_fold': DoubleFoldTransformation(),
    'canthal_tilt': CanthalTiltTransformation(),
    'eye_bag_removal': EyeBagRemovalTransformation(),
    'outer_corner_lift': OuterCornerLiftTransformation(),
    
    // 唇部造型变形策略
    'lip_shape': LipShapeTransformation(),
    'mouth_corner': MouthCornerTransformation(),
    
    // 抗衰冻龄变形策略
    'nasolabial_folds': NasolabialFoldsTransformation(),
    'wrinkle_removal': WrinkleRemovalTransformation(),
    'forehead_fullness': ForeheadFullnessTransformation(),
    'facial_firmness': FacialFirmnessTransformation(),
  };
  
  /// 获取指定参数名称的变形策略
  /// [parameterName] 参数名称
  /// 返回对应的变形策略，如果不存在则返回null
  TransformationStrategy? getStrategy(String? parameterName) {
    if (parameterName == null) {
      print('⚠️ 参数名称为空，无法获取变形策略');
      Logger.flow(_logTag, 'getStrategy', '⚠️ 参数名称为空，无法获取变形策略');
      return null;
    }
    
    // 强制打印日志，确保可见
    print('============ 变形策略查找 ============');
    print('当前查询的参数名: "$parameterName"'); 
    print('所有可用的变形策略: ${_strategies.keys.join(', ')}');
    
    // 打印所有可用的变形策略，便于调试
    Logger.flow(_logTag, 'getStrategy', '🔍 当前查询的参数名: "$parameterName"');
    Logger.flow(_logTag, 'getStrategy', '📊 所有可用的变形策略: ${_strategies.keys.join(', ')}');
    
    final strategy = _strategies[parameterName];
    if (strategy == null) {
      print('⚠️ 未找到参数 "$parameterName" 的变形策略');
      Logger.flow(_logTag, 'getStrategy', '⚠️ 未找到参数 "$parameterName" 的变形策略');
    } else {
      print('✅ 找到参数 "$parameterName" 的变形策略: ${strategy.runtimeType}');
      Logger.flow(_logTag, 'getStrategy', '✅ 找到参数 "$parameterName" 的变形策略: ${strategy.runtimeType}');
    }
    return strategy;
  }
  
  /// 检查是否存在指定参数名称的变形策略
  /// [parameterName] 参数名称
  /// 返回是否存在对应的变形策略
  bool hasStrategy(String? parameterName) {
    if (parameterName == null) {
      Logger.flow(_logTag, 'hasStrategy', '⚠️ 参数名称为空，无法检查变形策略');
      return false;
    }
    
    final exists = _strategies.containsKey(parameterName);
    Logger.flow(_logTag, 'hasStrategy', '参数 $parameterName ${exists ? "存在" : "不存在"}变形策略');
    return exists;
  }
  
  /// 注册新的变形策略
  /// [strategy] 变形策略实例
  void registerStrategy(TransformationStrategy strategy) {
    final parameterName = strategy.parameterName;
    _strategies[parameterName] = strategy;
    Logger.flow(_logTag, 'registerStrategy', '✅ 已注册参数 $parameterName 的变形策略: ${strategy.runtimeType}');
  }
}
