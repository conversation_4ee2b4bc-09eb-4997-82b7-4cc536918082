import 'dart:math';
import 'package:flutter/material.dart';
import '../utils/logger.dart';

/// 变形方向类型
enum DeformationDirectionType {
  /// 向内变形 (例如：缩小、收紧)
  inward,
  
  /// 向外变形 (例如：扩大、放松)
  outward,
  
  /// 向上变形 (例如：提升、抬高)
  upward,
  
  /// 向下变形 (例如：下垂、降低)
  downward,
  
  /// 混合变形 (需要特殊处理)
  mixed,
}

/// 变形方向处理器
/// 
/// 负责处理所有区域的变形方向逻辑，确保变形方向的一致性
class DeformationDirectionHandler {
  // 日志标签
  static const String _logTag = 'DeformationDirectionHandler';
  
  // 单例实例
  static final DeformationDirectionHandler _instance = DeformationDirectionHandler._internal();
  
  // 工厂构造函数
  factory DeformationDirectionHandler() => _instance;
  
  // 参数历史记录，用于跟踪参数变化
  final Map<String, double> _parameterHistory = {};
  
  // 当前活动区域和参数
  String? _currentArea;
  String? _currentParameter;
  
  // 内部构造函数
  DeformationDirectionHandler._internal() {
    Logger.log(_logTag, '_internal', '🔄 [初始化] 变形方向处理器初始化');
  }
  
  /// 设置当前参数名称
  /// 当切换参数时，需要调用此方法以正确处理历史记录
  /// [area] 区域类型
  /// [parameter] 参数名称
  /// [initialValue] 参数初始值
  void setCurrentParameter(String? area, String? parameter, {double initialValue = 0.0}) {
    Logger.log(_logTag, 'setCurrentParameter', '🔄 开始设置当前参数');
    
    // 检查是否真的发生了参数切换
    bool isParameterChanged = _currentParameter != parameter;
    bool isAreaChanged = _currentArea != area;
    
    if (isParameterChanged || isAreaChanged) {
      Logger.log(_logTag, 'setCurrentParameter', 
        '🔄 参数切换: ${_currentArea}.${_currentParameter} -> ${area}.${parameter}');
      
      // 更新当前区域和参数
      _currentArea = area;
      _currentParameter = parameter;
      
      // 如果参数有效，则在历史记录中设置初始值
      if (parameter != null && parameter.isNotEmpty) {
        // 将当前参数值作为历史记录的初始值
        _parameterHistory[parameter] = initialValue;
        Logger.log(_logTag, 'setCurrentParameter', 
          '📊 设置参数历史记录初始值: $parameter = $initialValue');
      }
    } else {
      Logger.log(_logTag, 'setCurrentParameter', 
        '📊 参数未变化: ${area}.${parameter}');
    }
    
    Logger.log(_logTag, 'setCurrentParameter', '✅ 完成设置当前参数');
  }
  
  /// 检查参数值变化
  /// 
  /// 简化版本：只记录参数值的变化方向，不再进行复杂的一致性检查
  /// [paramKey] 参数名称
  /// [currentValue] 当前参数值
  /// [previousValue] 上一次的参数值
  /// [deformationType] 变形类型（扩大或收缩）
  void checkParameterSwitching(String paramKey, double currentValue, double previousValue, String deformationType) {
    Logger.flow(_logTag, 'checkParameterSwitching', '开始检查参数值变化: $paramKey');
    
    // 判断变化方向
    String direction = "";
    if (currentValue > 0) {
      direction = "增大"; // 点击加号
    } else if (currentValue < 0) {
      direction = "减小"; // 点击减号
    } else {
      direction = "复位"; // 参数值为0
    }
    
    // 记录参数值变化
    Logger.log(_logTag, 'checkParameterSwitching', 
      '📊 参数值变化: $paramKey, 当前值=$currentValue, 变化方向=$direction');
    
    // 更新参数历史记录
    _parameterHistory[paramKey] = currentValue;
    
    Logger.flowEnd(_logTag, 'checkParameterSwitching');
  }
  
  /// 计算水平方向的变形向量
  /// 
  /// [paramValue] 参数值 - 正值表示点击加号（扩大），负值表示点击减号（收缩）
  /// [isLeftSide] 是否在左侧
  /// [directionType] 变形方向类型
  Offset calculateHorizontalDeformation(
    double paramValue, 
    bool isLeftSide, 
    DeformationDirectionType directionType
  ) {
    // 固定步长，确保每次变形幅度一致
    const double fixedStepSize = 0.2;
    
    // 方向系数：正值表示点击加号，负值表示点击减号
    double direction = paramValue > 0 ? 1.0 : -1.0;
    
    // 计算变形因子
    double xFactor = 0.0;
    
    // 根据变形方向类型和左右侧确定具体的变形方向
    switch (directionType) {
      case DeformationDirectionType.inward:
        // 向内变形：左侧点向右，右侧点向左
        if (direction > 0) { // 点击加号
          xFactor = isLeftSide ? fixedStepSize : -fixedStepSize;
        } else { // 点击减号
          xFactor = isLeftSide ? -fixedStepSize : fixedStepSize;
        }
        break;
        
      case DeformationDirectionType.outward:
        // 向外变形：左侧点向左，右侧点向右
        if (direction > 0) { // 点击加号
          xFactor = isLeftSide ? -fixedStepSize : fixedStepSize;
        } else { // 点击减号
          xFactor = isLeftSide ? fixedStepSize : -fixedStepSize;
        }
        break;
        
      default:
        // 其他方向类型不适用于水平变形
        xFactor = 0.0;
    }
    
    // 记录变形方向和幅度
    Logger.log(_logTag, 'calculateHorizontalDeformation', 
      '变形计算: 方向=${direction > 0 ? "增大" : "减小"}, 左侧=$isLeftSide, 类型=$directionType, 变形因子=$xFactor');
    
    return Offset(xFactor, 0.0);
  }
  
  /// 计算垂直方向的变形向量
  /// 
  /// [paramValue] 参数值 - 正值表示点击加号（增大/提升），负值表示点击减号（减小/降低）
  /// [directionType] 变形方向类型
  Offset calculateVerticalDeformation(
    double paramValue, 
    DeformationDirectionType directionType
  ) {
    // 固定步长，确保每次变形幅度一致
    const double fixedStepSize = 0.2;
    
    // 方向系数：正值表示点击加号，负值表示点击减号
    double direction = paramValue > 0 ? 1.0 : -1.0;
    
    // 计算变形因子
    double yFactor = 0.0;
    
    // 根据变形方向类型确定具体的变形方向
    switch (directionType) {
      case DeformationDirectionType.upward:
        // 向上变形：点击加号时向上移动，点击减号时向下移动
        if (direction > 0) { // 点击加号
          yFactor = -fixedStepSize; // 向上移动（y值减小）
        } else { // 点击减号
          yFactor = fixedStepSize; // 向下移动（y值增大）
        }
        break;
        
      case DeformationDirectionType.downward:
        // 向下变形：点击加号时向下移动，点击减号时向上移动
        if (direction > 0) { // 点击加号
          yFactor = fixedStepSize; // 向下移动（y值增大）
        } else { // 点击减号
          yFactor = -fixedStepSize; // 向上移动（y值减小）
        }
        break;
        
      default:
        // 其他方向类型不适用于垂直变形
        yFactor = 0.0;
    }
    
    // 记录变形方向和幅度
    Logger.log(_logTag, 'calculateVerticalDeformation', 
      '变形计算: 方向=${direction > 0 ? "增大/提升" : "减小/降低"}, 类型=$directionType, 变形因子=$yFactor');
    
    return Offset(0.0, yFactor);
  }
  
  /// 计算鼻翼宽度的变形向量
  /// 
  /// [paramValue] 参数值 - 正值表示点击加号（扩大），负值表示点击减号（收缩）
  /// [isLeftSide] 是否在左侧
  Offset calculateNostrilWidthDeformation(
    double paramValue, 
    bool isLeftSide
  ) {
    // 固定步长，确保每次变形幅度一致
    const double fixedStepSize = 0.2;
    
    // 记录调试信息
    Logger.flow(_logTag, 'calculateNostrilWidthDeformation', 
      '开始计算鼻翼宽度变形 | 参数值=$paramValue | 左侧=$isLeftSide');
    
    // 特殊情况处理：当参数值为0时，不进行变形
    if (paramValue == 0.0) {
      Logger.flow(_logTag, 'calculateNostrilWidthDeformation', '参数值为0，不进行变形');
      return Offset.zero;
    }
    
    // 方向系数：正值表示点击加号，负值表示点击减号
    double direction = paramValue > 0 ? 1.0 : -1.0;
    String directionName = direction > 0 ? "增大(鼻子变宽)" : "减小(鼻子变窄)";
    
    // 初始化变量
    double xFactor = 0.0;
    
    // 根据方向和左右侧确定变形因子
    if (direction > 0) { // 点击加号，鼻子变宽
      // 左侧点向左移动，右侧点向右移动（远离中心）
      xFactor = isLeftSide ? -fixedStepSize : fixedStepSize;
    } else { // 点击减号，鼻子变窄
      // 左侧点向右移动，右侧点向左移动（向中心）
      xFactor = isLeftSide ? fixedStepSize : -fixedStepSize;
    }
    
    // 记录变形向量计算结果
    Logger.flow(_logTag, 'calculateNostrilWidthDeformation', 
      '变形计算: 方向=$directionName, 左侧=$isLeftSide, 变形因子=$xFactor');
    
    // 更新参数历史记录（仅用于状态记录）
    String paramKey = _currentParameter ?? 'nostril_width';
    _parameterHistory[paramKey] = paramValue;
    
    Logger.flowEnd(_logTag, 'calculateNostrilWidthDeformation');
    return Offset(xFactor, 0.0);
  }
  
  /// 计算混合方向的变形向量
  /// 
  /// [paramValue] 参数值 - 正值表示点击加号（扩大），负值表示点击减号（收缩）
  /// [isLeftSide] 是否在左侧
  /// [xDirectionType] 水平方向变形类型
  /// [yDirectionType] 垂直方向变形类型
  Offset calculateMixedDeformation(
    double paramValue, 
    bool isLeftSide,
    {DeformationDirectionType xDirectionType = DeformationDirectionType.outward,
     DeformationDirectionType yDirectionType = DeformationDirectionType.upward}
  ) {
    // 记录调试信息
    Logger.flow(_logTag, 'calculateMixedDeformation', 
      '开始计算混合变形 | 参数值=$paramValue | 左侧=$isLeftSide');
    
    // 计算水平方向变形
    final horizontalDeformation = calculateHorizontalDeformation(
      paramValue, isLeftSide, xDirectionType
    );
    
    // 计算垂直方向变形
    final verticalDeformation = calculateVerticalDeformation(
      paramValue, yDirectionType
    );
    
    // 合并变形向量
    final result = Offset(horizontalDeformation.dx, verticalDeformation.dy);
    
    Logger.flow(_logTag, 'calculateMixedDeformation', 
      '混合变形结果: $result');
    
    return result;
  }
  
  /// 计算通用变形向量
  /// 
  /// [area] 区域
  /// [parameter] 参数
  /// [paramValue] 参数值
  /// [isLeftSide] 是否在左侧
  Offset calculateDeformation(
    String area, 
    String parameter, 
    double paramValue, 
    bool isLeftSide
  ) {
    // 根据区域和参数确定变形方向类型
    switch (area) {
      case 'nose':
        return _calculateNoseDeformation(parameter, paramValue, isLeftSide);
      case 'face_contour':
        return _calculateFaceContourDeformation(parameter, paramValue, isLeftSide);
      case 'eyes':
        return _calculateEyesDeformation(parameter, paramValue, isLeftSide);
      case 'lips':
        return _calculateLipsDeformation(parameter, paramValue, isLeftSide);
      case 'anti_aging':
        return _calculateAntiAgingDeformation(parameter, paramValue, isLeftSide);
      default:
        return Offset.zero;
    }
  }
  
  /// 计算鼻子区域变形向量
  Offset _calculateNoseDeformation(String parameter, double paramValue, bool isLeftSide) {
    switch (parameter) {
      case 'nostril_width':
        // 鼻翼宽度：水平方向偏移
        return calculateNostrilWidthDeformation(paramValue, isLeftSide);
      case 'bridge_height':
        // 鼻梁高度：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.upward);
      case 'tip_adjust':
        // 鼻尖调整：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.upward);
      case 'base_height':
        // 鼻基抬高：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.upward);
      default:
        return Offset.zero;
    }
  }
  
  /// 计算面部轮廓区域变形向量
  Offset _calculateFaceContourDeformation(String parameter, double paramValue, bool isLeftSide) {
    switch (parameter) {
      case 'contour_tighten':
        // 轮廓收紧：水平方向偏移
        return calculateHorizontalDeformation(paramValue, isLeftSide, DeformationDirectionType.inward);
      case 'v_chin':
        // V型下巴：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.downward);
      case 'cheekbone_adjust':
        // 颧骨调整：水平方向偏移
        return calculateHorizontalDeformation(paramValue, isLeftSide, DeformationDirectionType.outward);
      case 'face_shape':
        // 脸型优化：混合方向偏移
        return calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.outward,
          yDirectionType: DeformationDirectionType.upward
        );
      default:
        return Offset.zero;
    }
  }
  
  /// 计算眼睛区域变形向量
  Offset _calculateEyesDeformation(String parameter, double paramValue, bool isLeftSide) {
    switch (parameter) {
      case 'double_fold':
        // 双眼皮：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.upward);
      case 'canthal_tilt':
        // 开眼角：混合方向偏移
        return calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.outward,
          yDirectionType: DeformationDirectionType.upward
        );
      case 'eye_bag_removal':
        // 去眼袋：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.upward);
      case 'outer_corner_lift':
        // 提眼尾：混合方向偏移
        return calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.outward,
          yDirectionType: DeformationDirectionType.upward
        );
      default:
        return Offset.zero;
    }
  }
  
  /// 计算嘴唇区域变形向量
  Offset _calculateLipsDeformation(String parameter, double paramValue, bool isLeftSide) {
    switch (parameter) {
      case 'lip_shape':
        // 唇形调整：混合方向偏移
        return calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.outward,
          yDirectionType: DeformationDirectionType.downward
        );
      case 'mouth_corner':
        // 嘴角上扬：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.upward);
      default:
        return Offset.zero;
    }
  }
  
  /// 计算抗衰老区域变形向量
  Offset _calculateAntiAgingDeformation(String parameter, double paramValue, bool isLeftSide) {
    switch (parameter) {
      case 'nasolabial_folds':
        // 法令纹：混合方向偏移
        return calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.inward,
          yDirectionType: DeformationDirectionType.upward
        );
      case 'wrinkle_removal':
        // 去皱纹：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.upward);
      case 'forehead_fullness':
        // 额头饱满：垂直方向偏移
        return calculateVerticalDeformation(paramValue, DeformationDirectionType.upward);
      case 'facial_firmness':
        // 面容紧致：混合方向偏移
        return calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.inward,
          yDirectionType: DeformationDirectionType.upward
        );
      default:
        return Offset.zero;
    }
  }
  
}
