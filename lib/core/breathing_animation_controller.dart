import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import '../utils/logger.dart';

/// 呼吸动画控制器
/// 专门负责处理特征点的呼吸隐现效果，与变形渲染逻辑分离
class BreathingAnimationController extends ChangeNotifier {
  static const String _logTag = '呼吸动画控制器';
  
  // 呼吸动画定时器
  Timer? _breathingTimer;
  
  // 呼吸动画周期，单位毫秒
  final int _breathingCycleDuration = 2000;
  
  // 上次更新呼吸值的时间
  DateTime _lastBreathingUpdateTime = DateTime.now();
  
  // 当前呼吸值，范围0.0-1.0
  double _breathingValue = 1.0;
  
  // 是否启用呼吸动画
  bool _enableBreathingAnimation = true;
  
  // 是否已经初始化
  bool _isInitialized = false;
  
  /// 构造函数
  BreathingAnimationController() {
    Logger.flow(_logTag, '构造函数', '🔧 [创建] 呼吸动画控制器');
  }
  
  /// 初始化控制器
  void initialize() {
    if (_isInitialized) return;
    
    Logger.flowStart(_logTag, 'initialize');
    _isInitialized = true;
    startBreathingAnimation();
    Logger.flowEnd(_logTag, 'initialize');
  }
  
  // 是否已经输出了启动日志，避免重复输出
  bool _hasLoggedStart = false;
  
  /// 启动呼吸动画
  void startBreathingAnimation() {
    // 只在第一次启动时输出日志
    if (!_hasLoggedStart) {
      Logger.flowStart(_logTag, 'startBreathingAnimation');
      _hasLoggedStart = true;
    }
    
    // 如果已经有定时器，先停止
    stopBreathingAnimation();
    
    // 创建新的定时器，降低刷新频率为500ms，显著减少重绘频率
    // 增加时间间隔，减少更新频率
    _breathingTimer = Timer.periodic(Duration(milliseconds: 500), (timer) {
      _updateBreathingValue();
    });
    
    // 只在第一次启动时输出日志
    if (_hasLoggedStart) {
      Logger.flow(_logTag, 'startBreathingAnimation', '✅ 呼吸动画已启动');
      Logger.flowEnd(_logTag, 'startBreathingAnimation');
      _hasLoggedStart = false; // 重置标志，下次启动时可以再次输出日志
    }
  }
  
  /// 停止呼吸动画
  void stopBreathingAnimation() {
    if (_breathingTimer != null) {
      _breathingTimer!.cancel();
      _breathingTimer = null;
      Logger.flow(_logTag, 'stopBreathingAnimation', '⏹️ 呼吸动画已停止');
    }
  }
  
  // 用于控制日志输出频率
  static DateTime? _lastLogTime;
  static const Duration _minimumLogInterval = Duration(seconds: 5);
  
  // 上次呼吸值，用于比较变化
  double _lastBreathingValueNotified = 1.0;
  
  /// 更新呼吸动画值 - 完全避免触发重绘
  void _updateBreathingValue() {
    if (!_enableBreathingAnimation) {
      if (_breathingValue != 1.0) {
        _breathingValue = 1.0;
        _lastBreathingValueNotified = 1.0; // 更新上次通知的值
      }
      return;
    }
    
    final now = DateTime.now();
    
    // 使用正弦函数生成呼吸效果
    // 将时间映射到0-2π的范围内
    final phase = (now.millisecondsSinceEpoch % _breathingCycleDuration) / _breathingCycleDuration * 2 * pi;
    
    // 生成0.0-1.0范围内的呼吸值
    final newBreathingValue = (1 + sin(phase)) / 2;
    
    // 确保值在有效范围内
    final double clampedValue = 0.2 + 0.8 * newBreathingValue;
    
    // 更新呼吸值
    _breathingValue = clampedValue;
    
    // 增加变化阈值，减少通知频率
    // 只有当呼吸值变化超过0.1时才触发通知
    if ((_breathingValue - _lastBreathingValueNotified).abs() > 0.1) {
      _lastBreathingValueNotified = _breathingValue; // 更新上次通知的值
      
      // 控制日志输出频率，避免日志过多
      bool shouldLog = false;
      if (_lastLogTime == null || now.difference(_lastLogTime!) > _minimumLogInterval) {
        _lastLogTime = now;
        shouldLog = true;
      }
      
      if (shouldLog) {
        Logger.flow(_logTag, '_updateBreathingValue', '🌬️ 呼吸值更新: $_breathingValue');
      }
      
      // 仅在值变化显著时才触发通知，避免频繁重绘
      notifyListeners();
    }
  }
  
  /// 获取当前呼吸动画值
  /// 返回当前呼吸动画值，范围为 0.0-1.0
  double getBreathingValue() {
    return _breathingValue;
  }
  
  /// 设置是否启用呼吸动画
  void setEnableBreathingAnimation(bool enable) {
    if (_enableBreathingAnimation == enable) return;
    
    _enableBreathingAnimation = enable;
    
    if (enable) {
      startBreathingAnimation();
    } else {
      stopBreathingAnimation();
      _breathingValue = 1.0;
      notifyListeners();
    }
    
    Logger.flow(_logTag, 'setEnableBreathingAnimation', '🔄 呼吸动画${enable ? "启用" : "禁用"}');
  }
  
  /// 获取呼吸动画启用状态
  bool isBreathingAnimationEnabled() {
    return _enableBreathingAnimation;
  }
  
  /// 释放资源
  @override
  void dispose() {
    stopBreathingAnimation();
    super.dispose();
    Logger.flow(_logTag, 'dispose', '🗑️ 呼吸动画控制器已释放');
  }
}
