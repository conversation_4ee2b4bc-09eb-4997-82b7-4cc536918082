import 'dart:math';
import 'package:flutter/material.dart';
import '../utils/logger.dart';

/// 呼吸点效果数据
class BreathingEffectData {
  /// 透明度因子
  final double opacityFactor;
  
  /// 大小因子
  final double sizeFactor;
  
  const BreathingEffectData({
    required this.opacityFactor,
    required this.sizeFactor,
  });
}

/// 呼吸点效果
/// 
/// 实现特征点的呼吸效果，使特征点大小和透明度有规律地变化
class BreathingPointEffect {
  static const String _logTag = 'BreathingPointEffect';
  
  /// 呼吸周期（毫秒）
  final int breathingCycleDuration;
  
  /// 透明度变化范围
  final double minOpacityFactor;
  final double maxOpacityFactor;
  
  /// 大小变化范围
  final double minSizeFactor;
  final double maxSizeFactor;
  
  /// 随机偏移量（使不同点的呼吸周期有所不同）
  final double randomOffsetRange;
  
  /// 随机数生成器
  final Random _random = Random();
  
  /// 点偏移量映射表
  final Map<int, double> _pointOffsets = {};
  
  /// 上次计算时间
  DateTime _lastCalculationTime = DateTime.now();
  
  /// 当前周期进度（0.0-1.0）
  double _currentProgress = 0.0;
  
  /// 构造函数
  BreathingPointEffect({
    this.breathingCycleDuration = 2000,
    this.minOpacityFactor = 0.6,
    this.maxOpacityFactor = 1.0,
    this.minSizeFactor = 0.8,
    this.maxSizeFactor = 1.2,
    this.randomOffsetRange = 0.3,
  }) {
    Logger.flow(_logTag, 'constructor', '✅ 呼吸点效果初始化完成');
  }
  
  /// 获取点的偏移量
  double _getPointOffset(int pointIndex) {
    if (!_pointOffsets.containsKey(pointIndex)) {
      _pointOffsets[pointIndex] = _random.nextDouble() * randomOffsetRange;
    }
    
    return _pointOffsets[pointIndex]!;
  }
  
  /// 计算当前呼吸效果
  BreathingEffectData calculate(int pointIndex) {
    // 更新当前进度
    final now = DateTime.now();
    final elapsed = now.difference(_lastCalculationTime).inMilliseconds;
    _lastCalculationTime = now;
    
    _currentProgress += elapsed / breathingCycleDuration;
    _currentProgress %= 1.0;
    
    // 获取点的偏移量
    final offset = _getPointOffset(pointIndex);
    
    // 计算点的呼吸进度（加入偏移量使不同点的呼吸周期有所不同）
    final pointProgress = (_currentProgress + offset) % 1.0;
    
    // 使用正弦函数计算呼吸效果
    final sinValue = sin(pointProgress * 2 * pi);
    final normalizedSinValue = (sinValue + 1) / 2; // 将-1到1的范围映射到0到1
    
    // 计算透明度因子
    final opacityFactor = minOpacityFactor + normalizedSinValue * (maxOpacityFactor - minOpacityFactor);
    
    // 计算大小因子
    final sizeFactor = minSizeFactor + normalizedSinValue * (maxSizeFactor - minSizeFactor);
    
    return BreathingEffectData(
      opacityFactor: opacityFactor,
      sizeFactor: sizeFactor,
    );
  }
}
