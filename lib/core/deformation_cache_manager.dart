import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import '../utils/logger.dart';
import 'models/feature_point.dart';
import 'package:image/image.dart' as img;

/// 变形状态键，用于缓存查找
class DeformationStateKey {
  final Map<String, double> parameterValues;
  
  DeformationStateKey(this.parameterValues);
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DeformationStateKey) return false;
    
    if (parameterValues.length != other.parameterValues.length) return false;
    
    // 比较所有参数值，使用一位小数精度比较
    // 这与generateCacheKey方法使用的精度一致
    for (final entry in parameterValues.entries) {
      final otherValue = other.parameterValues[entry.key];
      if (otherValue == null) return false;
      
      // 将两个值都转换为一位小数的字符串表示进行比较
      // 这确保了与缓存键生成逻辑的一致性
      final thisValueStr = entry.value.toStringAsFixed(1);
      final otherValueStr = otherValue.toStringAsFixed(1);
      if (thisValueStr != otherValueStr) return false;
    }
    
    return true;
  }
  
  @override
  int get hashCode {
    // 按参数名称排序后生成哈希码，确保顺序无关性
    final sortedEntries = parameterValues.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    // 使用与==运算符相同的精度级别
    // 对浮点值进行舍入，保留1位小数，与generateCacheKey方法一致
    return Object.hashAll(
      sortedEntries.map((e) => 
        // 对浮点值进行舍入，确保哈希值与==运算符的容差一致
        Object.hash(e.key, e.value.toStringAsFixed(1))
      )
    );
  }
  
  /// 生成用于日志和调试的字符串表示
  /// 与generateCacheKey方法生成相同格式的缓存键
  String toDebugString() {
    // 按参数名称字母顺序排序
    final sortedEntries = parameterValues.entries.toList()
      // 过滤掉以下划线开头的区域信息参数
      .where((e) => !e.key.startsWith('_'))
      .toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    // 生成缓存键字符串，将浮点值转换为整数值
    final cacheKey = sortedEntries
      .map((e) => '${e.key}:${e.value.round()}')
      .join('|');
    
    return cacheKey.isEmpty ? '空参数' : cacheKey;
  }
}

/// 变形状态值，包含变形后的图像和特征点数据
class DeformationStateValue {
  final ui.Image? deformedImage;
  final List<FeaturePoint> deformedFeaturePoints;
  final double? facialCenterLineX;
  final bool facialCenterLineCalculated;
  DateTime lastAccessTime;
  int accessCount;
  
  DeformationStateValue({
    this.deformedImage,
    required this.deformedFeaturePoints,
    this.facialCenterLineX,
    this.facialCenterLineCalculated = false,
  }) : 
    lastAccessTime = DateTime.now(),
    accessCount = 1;
  
  /// 更新访问统计
  void updateAccessStats() {
    lastAccessTime = DateTime.now();
    accessCount++;
  }
}

/// 变形缓存管理器
/// 
/// 负责管理变形结果的缓存，使用全量参数值作为缓存键
/// 实现LRU（最近最少使用）缓存淘汰策略
/// 只有在图片重新导入时才清空缓存
class DeformationCacheManager {
  static const String _logTag = 'DeformationCacheManager';
  
  // 单例模式
  static final DeformationCacheManager _instance = DeformationCacheManager._internal();
  
  // 最新的变形图像和特征点，用于所有变形绘制器共享
  static ui.Image? _latestDeformedImage;
  static List<FeaturePoint>? _latestDeformedFeaturePoints;
  
  // 记录当前是否正在进行变形
  static bool _isDeforming = false;
  
  // 【关键新增】全局面部中心线缓存 - 图像导入后立即计算并永久缓存
  static double? _globalFacialCenterLineX;
  static bool _globalFacialCenterLineCalculated = false;
  static ui.Image? _facialCenterLineSourceImage; // 记录计算中心线时的源图像，用于检测图像变化

  // 新增：缓存嘴部中心Y坐标
  static double? _mouthCenterY;

  // 新增：设置嘴部中心Y坐标
  static void setMouthCenterY(double y) {
    _mouthCenterY = y;
    Logger.logInfo(_logTag, 'setMouthCenterY', '缓存嘴部中心Y坐标: $y');
  }

  // 新增：获取嘴部中心Y坐标
  static double? getMouthCenterY() {
    Logger.logInfo(_logTag, 'getMouthCenterY', '获取缓存的嘴部中心Y坐标: $_mouthCenterY');
    return _mouthCenterY;
  }
  
  // 获取最新的变形图像
  static ui.Image? getLatestDeformedImage() {
    return _latestDeformedImage;
  }
  
  // 获取最新的变形特征点
  static List<FeaturePoint>? getLatestDeformedFeaturePoints() {
    return _latestDeformedFeaturePoints != null ? 
      List<FeaturePoint>.from(_latestDeformedFeaturePoints!) : null;
  }
  
  // 【关键新增】获取全局面部中心线X坐标
  /// 返回全局缓存的面部中心线X坐标
  /// 该中心线在图像导入后立即计算并永久缓存，不允许重新计算
  static double? getGlobalFacialCenterLineX() {
    if (_globalFacialCenterLineCalculated && _globalFacialCenterLineX != null) {
      Logger.flow(_logTag, 'getGlobalFacialCenterLineX', 
          '🎯 返回全局缓存的面部中心线: $_globalFacialCenterLineX');
      return _globalFacialCenterLineX;
    }
    Logger.flowWarning(_logTag, 'getGlobalFacialCenterLineX', 
        '⚠️ 警告: 全局面部中心线尚未计算，返回null');
    return null;
  }
  
  // 【关键新增】检查全局面部中心线是否已计算
  /// 返回 true 表示全局面部中心线已经计算并缓存
  static bool isGlobalFacialCenterLineCalculated() {
    return _globalFacialCenterLineCalculated && _globalFacialCenterLineX != null;
  }
  
  /// 检查是否正在进行变形
  /// 返回 true 表示正在变形中，不允许新的变形操作
  static bool isDeforming() {
    return _isDeforming;
  }
  
  // 【关键新增】设置全局面部中心线X坐标
  /// 在图像导入并成功特征识别后立即调用，计算并永久缓存面部中心线
  /// [originalFeaturePoints] 原始特征点（未变形）
  /// [sourceImage] 源图像，用于检测图像变化
  /// [scaleX] X方向缩放系数，默认为1.0
  static void setGlobalFacialCenterLineX(List<FeaturePoint> originalFeaturePoints, ui.Image sourceImage, [double scaleX = 1.0]) {
    Logger.flowStart(_logTag, 'setGlobalFacialCenterLineX');
    
    // 检查是否已经计算过且图像没有变化
    if (_globalFacialCenterLineCalculated && 
        _globalFacialCenterLineX != null && 
        _facialCenterLineSourceImage != null &&
        _facialCenterLineSourceImage!.hashCode == sourceImage.hashCode) {
      Logger.flow(_logTag, 'setGlobalFacialCenterLineX',
          '🎯 全局面部中心线已经计算并缓存，直接返回: $_globalFacialCenterLineX');
      Logger.flowEnd(_logTag, 'setGlobalFacialCenterLineX');
      return;
    }
    
    // 强制重新计算（仅在图像变化时）
    Logger.flow(_logTag, 'setGlobalFacialCenterLineX',
        '🔄 开始计算全局面部中心线: 特征点数=${originalFeaturePoints.length}, 图像尺寸=${sourceImage.width}x${sourceImage.height}');
    
    // 使用与鼻尖调整相同的计算逻辑，确保一致性
    FeaturePoint? leftNostril;   // 左鼻翼点 - 索引129
    FeaturePoint? rightNostril;  // 右鼻翼点 - 索引358
    FeaturePoint? noseTip;       // 鼻尖中心点 - 索引94
    FeaturePoint? noseBase;      // 鼻基底中心点 - 索引19

    // 遍历特征点，找出关键的对称特征点
    for (var point in originalFeaturePoints) {
      if (point.index == 129) {
        leftNostril = point;
      } else if (point.index == 358) {
        rightNostril = point;
      } else if (point.index == 94) {
        noseTip = point;
      } else if (point.index == 19) {
        noseBase = point;
      }
    }

    // 优先使用鼻翼点计算中心线（最对称的特征点对）
    double calculatedCenterX;
    if (leftNostril != null && rightNostril != null) {
      calculatedCenterX = (leftNostril.x + rightNostril.x) / 2 * scaleX;
      Logger.flow(_logTag, 'setGlobalFacialCenterLineX',
          '🎯 使用鼻翼点计算中心线: $calculatedCenterX (左鼻翼${leftNostril.index}: ${leftNostril.x}, 右鼻翼${rightNostril.index}: ${rightNostril.x})');
    } else if (noseTip != null && noseBase != null) {
      // 备选方案：使用鼻尖和鼻基底的中点
      calculatedCenterX = (noseTip.x + noseBase.x) / 2 * scaleX;
      Logger.flow(_logTag, 'setGlobalFacialCenterLineX',
          '🎯 使用鼻尖和鼻基底计算中心线: $calculatedCenterX (鼻尖${noseTip.index}: ${noseTip.x}, 鼻基底${noseBase.index}: ${noseBase.x})');
    } else {
      // 最后备选：使用所有特征点的平均值
      calculatedCenterX = 0.0;
      for (var point in originalFeaturePoints) {
        calculatedCenterX += point.x;
      }
      calculatedCenterX = (calculatedCenterX / originalFeaturePoints.length) * scaleX;
      Logger.flowWarning(_logTag, 'setGlobalFacialCenterLineX',
          '⚠️ 使用所有特征点的平均X坐标作为中心线: $calculatedCenterX');
    }
    
    // 设置全局缓存
    _globalFacialCenterLineX = calculatedCenterX;
    _globalFacialCenterLineCalculated = true;
    _facialCenterLineSourceImage = sourceImage;
    
    Logger.flow(_logTag, 'setGlobalFacialCenterLineX',
        '✅ 全局面部中心线计算完成并永久缓存: $_globalFacialCenterLineX');
    Logger.flowEnd(_logTag, 'setGlobalFacialCenterLineX');
  }
  
  // 【关键新增】强制清除全局面部中心线缓存
  /// 仅在新图像导入时调用，清除旧的中心线缓存
  static void clearGlobalFacialCenterLine() {
    Logger.flowStart(_logTag, 'clearGlobalFacialCenterLine');
    
    if (_globalFacialCenterLineCalculated) {
      Logger.flow(_logTag, 'clearGlobalFacialCenterLine',
          '🗑️ 清除旧的全局面部中心线缓存: $_globalFacialCenterLineX');
    }
    
    _globalFacialCenterLineX = null;
    _globalFacialCenterLineCalculated = false;
    _facialCenterLineSourceImage = null;
    
    Logger.flow(_logTag, 'clearGlobalFacialCenterLine',
        '✅ 全局面部中心线缓存已清除，等待新图像的中心线计算');
    Logger.flowEnd(_logTag, 'clearGlobalFacialCenterLine');
  }
  
  // 设置最新的变形状态
  /// 包括变形图像和变形特征点
  /// [isButtonClick] 是否是加减号点击事件
  static void setLatestDeformedState(ui.Image? image, List<FeaturePoint>? featurePoints, {bool isButtonClick = false}) {
    Logger.flowStart(_logTag, 'setLatestDeformedState');
    
    // 如果是按钮点击事件，设置变形状态为正在进行中
    if (isButtonClick) {
      // 记录当前正在进行变形
      _isDeforming = true;
      Logger.flow(_logTag, 'setLatestDeformedState', '🔔 [加减号点击] 开始变形处理');
    } else {
      // 如果不是按钮点击事件，说明是变形结果的回调或参数切换
      // 自动重置变形状态，允许下一次变形
      if (_isDeforming) {
        _isDeforming = false;
        Logger.flow(_logTag, 'setLatestDeformedState', '✅ [自动重置] 变形已完成，允许下一次变形');
      }
    }
    
    if (image != null) {
      Logger.flow(_logTag, 'setLatestDeformedState', '📷 设置最新变形图像: 哈希码=${image.hashCode}, 宽度=${image.width}, 高度=${image.height}');
      
      _latestDeformedImage = image;
      
      // 使用Logger系统记录变形图像信息，减少重复输出
      Logger.flow(_logTag, 'setLatestDeformedState', '📷 变形图像详细信息: 哈希码=${image.hashCode}, 尺寸=${image.width}x${image.height}, 内存=${(image.width * image.height * 4) / 1024}KB, 时间=${DateTime.now().toIso8601String()}');
      
      // 输出图片详细信息到日志
      logDeformedImageInfo(image, '最新变形图像');
    } else {
      Logger.flow(_logTag, 'setLatestDeformedState', '⚠️ 最新变形图像为空');
      _latestDeformedImage = null;
    }
    
    if (featurePoints != null) {
      Logger.flow(_logTag, 'setLatestDeformedState', '👁️ 设置最新变形特征点: ${featurePoints.length}个点');
      _latestDeformedFeaturePoints = List<FeaturePoint>.from(featurePoints);
    } else {
      Logger.flow(_logTag, 'setLatestDeformedState', '⚠️ 最新变形特征点为空');
      _latestDeformedFeaturePoints = null;
    }
    
    Logger.flow(_logTag, 'setLatestDeformedState', '✅ 最新变形状态已更新');
    Logger.flowEnd(_logTag, 'setLatestDeformedState');
    
    // 通知监听器
    _notifyLatestDeformedStateChanged();
  }
  

  
  /// 通知最新变形状态已更改
  static void _notifyLatestDeformedStateChanged() {
    // 通知所有监听器
    for (final listener in _latestDeformedStateListeners) {
      listener();
    }
  }
  
  // 最新变形状态监听器列表
  static final List<VoidCallback> _latestDeformedStateListeners = [];
  
  /// 添加最新变形状态监听器
  static void addLatestDeformedStateListener(VoidCallback listener) {
    if (!_latestDeformedStateListeners.contains(listener)) {
      _latestDeformedStateListeners.add(listener);
    }
  }
  
  /// 移除最新变形状态监听器
  static void removeLatestDeformedStateListener(VoidCallback listener) {
    _latestDeformedStateListeners.remove(listener);
  }
  
  /// 通知变形完成，强制更新UI
  static void notifyDeformationCompleted() {
    Logger.flowStart(_logTag, 'notifyDeformationCompleted');
    Logger.flow(_logTag, 'notifyDeformationCompleted', '强制通知变形完成，确保UI更新');
    
    // 通知所有监听器
    _notifyLatestDeformedStateChanged();
    
    // 输出当前图像信息
    if (_latestDeformedImage != null) {
      Logger.flow(_logTag, 'notifyDeformationCompleted', '当前变形图像哈希码: ${_latestDeformedImage!.hashCode}');
      Logger.flow(_logTag, 'notifyDeformationCompleted', '当前变形图像尺寸: ${_latestDeformedImage!.width}x${_latestDeformedImage!.height}');
    } else {
      Logger.flowWarning(_logTag, 'notifyDeformationCompleted', '当前变形图像为空');
    }
    
    // 标记变形已完成，允许下一次变形
    _isDeforming = false;
    Logger.flow(_logTag, 'notifyDeformationCompleted', '变形已完成，允许下一次变形');
    
    Logger.flowEnd(_logTag, 'notifyDeformationCompleted');
  }
  
  factory DeformationCacheManager() {
    return _instance;
  }
  
  DeformationCacheManager._internal() {
    Logger.flowStart(_logTag, '初始化变形缓存管理器');
    Logger.flow(_logTag, '初始化变形缓存管理器', '✅ 变形缓存管理器初始化成功，最大缓存容量: $_maxCacheSize');
    Logger.flowEnd(_logTag, '初始化变形缓存管理器');
  }
  
  // 缓存容量限制
  final int _maxCacheSize = 50;
  
  // 变形状态缓存，使用全量参数值作为键
  final Map<String, DeformationStateValue> _cache = {};
  
  // 最近一次保存的缓存键，用于避免重复保存
  String? _lastSavedCacheKey;
  
  // 缓存统计
  int _cacheHits = 0;
  int _cacheMisses = 0;
  int _cacheEvictions = 0;
  
  /// 获取当前缓存大小
  int getCacheSize() {
    return _cache.length;
  }
  
  /// 生成缓存键
  /// 公开方法，供其他类调用
  /// 包含所有非零值的参数项和值，按字母顺序排序
  /// 使用一位小数精度来确保相邻的参数值能够生成不同的缓存键
  String generateCacheKey(Map<String, double> parameterValues) {
    Logger.flowStart(_logTag, 'generateCacheKey');
    
    // 记录输入参数
    Logger.flow(_logTag, 'generateCacheKey', '🔍 [输入] 参数值: $parameterValues');
    
    // 按参数名称字母顺序排序，包含所有参数
    final sortedEntries = parameterValues.entries.toList()
      // 过滤掉以下划线开头的区域信息参数
      .where((e) => !e.key.startsWith('_'))
      .toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    // 检查是否所有参数值都为0
    bool allZero = sortedEntries.every((e) => e.value == 0.0);
    
    // 如果所有参数值都为0，返回特殊的零状态缓存键
    if (allZero) {
      Logger.flow(_logTag, 'generateCacheKey', '⚠️ 所有参数值均为0，生成零状态缓存键');
      Logger.flowEnd(_logTag, 'generateCacheKey');
      return 'zero_state';
    }
    
    // 重要改进：使用所有非零参数生成缓存键，确保多个参数项的变形效果能正确累积
    final nonZeroEntries = sortedEntries.where((e) => e.value != 0.0).toList();
    Logger.flow(_logTag, 'generateCacheKey', '🔍 [处理] 非零参数数量: ${nonZeroEntries.length}');
    
    // 记录所有非零参数
    for (var entry in nonZeroEntries) {
      Logger.flow(_logTag, 'generateCacheKey', '🔍 [非零参数] ${entry.key}: ${entry.value}');
    }
    
    // 生成缓存键字符串，统一使用一位小数精度
    // 特别注意负值的处理，确保负号被正确保留
    String cacheKey = nonZeroEntries
      .map((e) {
        // 使用已经标准化的值(一位小数)，保证缓存键一致性
        String valueStr = e.value.toStringAsFixed(1);
        // 确保负零被处理为正零
        if (valueStr == '-0.0') valueStr = '0.0';
        // 记录每个参数的格式化结果
        Logger.flow(_logTag, 'generateCacheKey', '🔍 [参数格式化] ${e.key}: 原值=${e.value}, 格式化后=$valueStr');
        return '${e.key}:$valueStr';
      })
      .join('|');
    
    // 如果缓存键为空（没有非零参数），返回零状态
    if (cacheKey.isEmpty) {
      Logger.flow(_logTag, 'generateCacheKey', '⚠️ 没有非零参数，生成零状态缓存键');
      Logger.flowEnd(_logTag, 'generateCacheKey');
      return 'zero_state';
    }
    
    // 记录最终生成的缓存键
    Logger.flow(_logTag, 'generateCacheKey', '✅ [结果] 生成缓存键: $cacheKey');
    Logger.flow(_logTag, 'generateCacheKey', '✅ [结果] 非零参数数量: ${nonZeroEntries.length}');
    
    // 输出更详细的日志，便于调试
    Logger.i(_logTag, '⟹ 生成缓存键: $cacheKey [非零参数: ${nonZeroEntries.length}个]');
    
    Logger.flowEnd(_logTag, 'generateCacheKey');
    return cacheKey;
  }
  
  /// 统一参数值处理，确保精度一致
  Map<String, double> normalizeParameterValues(Map<String, double> params) {
    Map<String, double> normalized = {};
    params.forEach((key, value) {
      if (!key.startsWith('_')) {
        // 严格统一保留1位小数，确保缓存键生成一致
        String valueStr = value.toStringAsFixed(1);
        // 确保负零被处理为正零
        if (valueStr == '-0.0') valueStr = '0.0';
        normalized[key] = double.parse(valueStr);
        
        // 记录标准化过程
        if (value != normalized[key]) {
          Logger.flow(_logTag, 'normalizeParameterValues', '🔄 参数${key}: ${value} → ${normalized[key]}');
        }
      } else {
        normalized[key] = value;
      }
    });
    return normalized;
  }
  
  /// 查找缓存
  DeformationStateValue? find(Map<String, double> parameterValues) {
    Logger.flowStart(_logTag, '查找缓存');
    
    // 添加缓存管理流程日志
    print('🔄 [缓存管理1] DeformationCacheManager.find 查找缓存');
    
    // 规范化参数值，确保与保存时的处理一致
    parameterValues = normalizeParameterValues(parameterValues);
    
    // 生成缓存键
    final cacheKey = generateCacheKey(parameterValues);
    Logger.flow(_logTag, '查找缓存', '🔍 1. 生成缓存键: $cacheKey');
    
    // 记录参数值，用于调试
    Logger.flow(_logTag, '查找缓存', '🔍 2. 参数值:');
    parameterValues.forEach((key, value) {
      if (value != 0.0) {
        Logger.flow(_logTag, '查找缓存', '  - $key = $value');
      }
    });
    
    // 首先尝试精确匹配
    final result = _cache[cacheKey];
    
    if (result != null) {
      print('🔄 [缓存管理2] 缓存命中: 是');
      print('🔄 [缓存管理3] 使用缓存结果');
      
      // 缓存命中，需要检查缓存的完整性
      if (result.deformedImage == null) {
        // 缓存的图像为空，无法使用
        Logger.flowError(_logTag, '查找缓存', '❌ 缓存命中但图像为空，无法使用缓存');
        _cacheMisses++; // 计为缓存未命中
        Logger.flowEnd(_logTag, '查找缓存');
        return null;
      }
      
      if (result.deformedFeaturePoints.isEmpty) {
        // 缓存的特征点数据为空，无法使用
        Logger.flowError(_logTag, '查找缓存', '❌ 缓存命中但特征点数据为空，无法使用缓存');
        _cacheMisses++; // 计为缓存未命中
        Logger.flowEnd(_logTag, '查找缓存');
        return null;
      }
      
      // 缓存命中，且数据完整
      _cacheHits++;
      result.updateAccessStats();
      
      // 记录缓存命中信息
      Logger.flow(_logTag, '查找缓存', '🟢 3. 全量缓存键命中: Y [缓存键: $cacheKey]');
      Logger.flow(_logTag, '查找缓存', '✅ 3.1 缓存命中，直接使用缓存的结果');
      
      // 使用 Logger.i 输出缓存命中信息，确保在任何模式下都能看到
      Logger.i(_logTag, '⟹ 查找缓存: 🟡 3. 全量缓存键命中: Y [缓存键: $cacheKey]');
      
      // 输出缓存命中的图像信息
      Logger.flow(_logTag, '查找缓存', '📷 缓存命中图像信息:');
      Logger.flow(_logTag, '查找缓存', '  - 哈希码: ${result.deformedImage.hashCode}');
      Logger.flow(_logTag, '查找缓存', '  - 宽度: ${result.deformedImage!.width}');
      Logger.flow(_logTag, '查找缓存', '  - 高度: ${result.deformedImage!.height}');
      
      // 使用 Logger.i 输出缓存命中的图像信息，确保在 keyOnly 模式下也能看到
      Logger.i(_logTag, '缓存命中图像');
      
      // 输出图片详细信息
      logDeformedImageInfo(result.deformedImage, '缓存命中图像');
      
      // 输出缓存命中的特征点信息
      Logger.flow(_logTag, '查找缓存', '👁️ 缓存命中特征点信息: ${result.deformedFeaturePoints.length}个点');
      
      // 输出缓存命中的参数值信息
      print('======== 缓存命中图像详细信息 ========');
      print('📷 缓存命中图像详细信息:');
      print('  - 哈希码: ${result.deformedImage.hashCode}');
      print('  - 宽度: ${result.deformedImage!.width}');
      print('  - 高度: ${result.deformedImage!.height}');
      print('  - 内存地址: ${result.deformedImage.toString()}');
      
      // 输出缓存命中的参数值
      // 检查是否所有参数值都为0
      bool allZero = parameterValues.entries
        .where((e) => !e.key.startsWith('_'))
        .every((e) => e.value == 0.0);
      
      if (allZero) {
        print('  - 参数值: zero_state');
      } else {
        final paramValues = parameterValues.entries
            .where((entry) => entry.value != 0.0)
            .map((entry) => '${entry.key}=${entry.value}')
            .join(', ');
        print('  - 参数值: $paramValues');
      }
      print('===============================');
      
      // 记录缓存统计信息
      Logger.flow(_logTag, '查找缓存', '📊 缓存统计: 命中率=${_hitRate.toStringAsFixed(2)}, 总命中次数=$_cacheHits');
      
      // 重要：更新最新的共享变形状态，确保其他组件能够访问到最新的变形结果
      // 这确保了在参数项切换时，能够正确使用之前的变形结果作为基础
      setLatestDeformedState(result.deformedImage, result.deformedFeaturePoints);
      Logger.flow(_logTag, '查找缓存', '📤 更新共享变形状态: 图像哈希码=${result.deformedImage.hashCode}, 特征点数量=${result.deformedFeaturePoints.length}');
      
      Logger.flowEnd(_logTag, '查找缓存');
      return result;
    } else {
      // 缓存未命中
      _cacheMisses++;
      
      Logger.flow(_logTag, '查找缓存', '🟡 3. 全量缓存键命中: N [缓存键: $cacheKey]');
      Logger.flow(_logTag, '查找缓存', '❌ 3.2 缓存未命中，需要获取累积数据并执行变形');
      
      // 使用 Logger.i 输出缓存未命中信息，确保在任何模式下都能看到
      Logger.i(_logTag, '⟹ 查找缓存: 🟡 3. 全量缓存键命中: N [缓存键: $cacheKey]');
      Logger.i(_logTag, '⟹ 查找缓存: ❌ 3.2 缓存未命中，需要获取累积数据并执行变形');
      
      // 记录缓存统计信息
      Logger.flow(_logTag, '查找缓存', '📊 缓存统计: 命中率=${_hitRate.toStringAsFixed(2)}, 总未命中次数=$_cacheMisses');
      
      Logger.flowEnd(_logTag, '查找缓存');
      return null;
    }
  }
  
  /// 保存到缓存
  /// 如果当前正在进行变形，则不允许新的保存操作
  Future<void> save(Map<String, double> parameterValues, ui.Image deformedImage, List<FeaturePoint>? deformedFeaturePoints, {double? facialCenterLineX, bool facialCenterLineCalculated = false}) async {
    // 使用统一的参数值规范化方法，确保与find方法使用相同的处理方式
    parameterValues = normalizeParameterValues(parameterValues);
    // 检查是否正在进行变形
    if (_isDeforming && !parameterValues.containsKey('_isButtonClick')) {
      Logger.flowWarning(_logTag, '保存缓存', '❗ 当前正在进行变形，不允许新的保存操作');
      return;
    }
    // 如果特征点为空，则不保存缓存
    if (deformedFeaturePoints == null || deformedFeaturePoints.isEmpty) {
      Logger.flowWarning(_logTag, '保存缓存', '⚠️ 特征点为空，无法保存缓存');
      return;
    }
    Logger.flowStart(_logTag, '保存缓存');
    Logger.flow(_logTag, '保存缓存', '🔍 [开始] 保存缓存');
    
    // 记录当前缓存状态
    Logger.flow(_logTag, '保存缓存', '保存前缓存状态: 缓存项数=${_cache.length}, 最大缓存容量=$_maxCacheSize');
    // 3.2 缓存未命中情况下的后续处理
    Logger.flow(_logTag, '保存缓存', '✅ 3.2 获取累积数据: Y');
    Logger.flow(_logTag, '保存缓存', '✅ 3.2 变形成功: Y');
    Logger.flow(_logTag, '保存缓存', '✅ 3.2 变形后保存缓存: Y');
    
    // 记录变形图像的尺寸
    Logger.flow(_logTag, '保存缓存', '📏 原始变形图像尺寸: ${deformedImage.width}x${deformedImage.height}');
    Logger.flow(_logTag, '保存缓存', '📷 图像哈希码: ${deformedImage.hashCode}');
    
    // 检查图像尺寸一致性
    if (_latestDeformedImage != null && _latestDeformedImage!.hashCode != deformedImage.hashCode) {
      // 比较尺寸
      if (_latestDeformedImage!.width != deformedImage.width || _latestDeformedImage!.height != deformedImage.height) {
        Logger.flowError(_logTag, '保存缓存', '❌ 图像尺寸不一致 | 拒绝保存不匹配的图像');
        Logger.flowError(_logTag, '保存缓存', '  • 原始图像: ${_latestDeformedImage!.width}x${_latestDeformedImage!.height}');
        Logger.flowError(_logTag, '保存缓存', '  • 当前图像: ${deformedImage.width}x${deformedImage.height}');
        
        // 拒绝保存尺寸不匹配的图像，使用原始图像代替
        Logger.flow(_logTag, '保存缓存', '⚠️ 使用原始图像代替尺寸不匹配的图像');
        return; // 直接返回，不保存到缓存
      } else {
        Logger.flow(_logTag, '保存缓存', '✅ 图像尺寸一致性检查通过');
        Logger.flow(_logTag, '保存缓存', '  • 图像尺寸: ${deformedImage.width}x${deformedImage.height}');
      }
    }
    
    // 使用原始图像尺寸，不进行调整
    ui.Image imageToCache = deformedImage;
    
    // 生成缓存键
    final key = generateCacheKey(parameterValues);
    Logger.flow(_logTag, '保存缓存', '❗️ 3.2.1 生成缓存键: $key');
    
    // 输出全量参数值，便于调试
    Logger.flow(_logTag, '保存缓存', '❗️ 3.2.1 全量参数值: ${parameterValues.entries.map((e) => '${e.key}=${e.value}').join(', ')}');
    
    // 检查是否已经有相同的缓存键
    if (_cache.containsKey(key)) {
      // 检查现有缓存项的图像哈希码是否与新图像相同
      final existingValue = _cache[key];
      if (existingValue != null && existingValue.deformedImage?.hashCode == deformedImage.hashCode) {
        // 如果图像哈希码相同，表示是相同的图像，跳过保存
        Logger.flow(_logTag, '保存缓存', '⚠️ 已存在相同缓存键和相同图像的缓存项，跳过保存');
        Logger.flow(_logTag, '保存缓存', '✅ [完成] 保存缓存 (跳过)');
        Logger.flowEnd(_logTag, '保存缓存');
        return;
      }
      
      Logger.flow(_logTag, '保存缓存', '⚠️ 已存在相同缓存键: $key，但图像不同，将覆盖现有缓存');
      print('⚠️ 已存在相同缓存键: $key，但图像不同，将覆盖现有缓存');
    }
    
    // 检查是否已存在该缓存
    final isExistingKey = _cache.containsKey(key);
    if (isExistingKey) {
      Logger.flow(_logTag, '保存缓存', '✅ 3.2.2 覆盖现有缓存项 [缓存键: $key]');
    } else {
      Logger.flow(_logTag, '保存缓存', '✅ 3.2.2 创建新缓存项 [缓存键: $key]');
    }
    
    // 检查是否需要淘汰缓存项
    if (_cache.length >= _maxCacheSize && !isExistingKey) {
      Logger.flow(_logTag, '保存缓存', '缓存已满，需要淘汰缓存项');
      _evictCache();
      Logger.flow(_logTag, '保存缓存', '淘汰后缓存状态: 缓存项数=${_cache.length}');
    }
    
    // 创建变形状态值
    Logger.flow(_logTag, '保存缓存', '创建变形状态值: 特征点数=${deformedFeaturePoints.length}, 面部中心线=${facialCenterLineX != null ? facialCenterLineX.toStringAsFixed(2) : '无'}');
    final value = DeformationStateValue(
      deformedImage: imageToCache, // 使用调整后的图像
      deformedFeaturePoints: List<FeaturePoint>.from(deformedFeaturePoints),
      facialCenterLineX: facialCenterLineX,
      facialCenterLineCalculated: facialCenterLineCalculated,
    );
    
    // 保存到缓存
    _cache[key] = value;
    _lastSavedCacheKey = key;
    
    // 立即通知缓存更新监听器，不等待其他异步操作
    // 添加记录时间戳，便于调试
    print('⏱ 开始通知缓存更新时间: ${DateTime.now().toString()}');
    
    // 🔴 关键调试：记录即将通知的图像尺寸信息
    print('======== 🔍 关键调试：缓存保存详情 ========');
    print('💾 即将保存到缓存的图像详情:');
    print('  - 缓存键: $key');
    print('  - 图像哈希码: ${imageToCache.hashCode}');
    print('  - 图像尺寸: ${imageToCache.width}x${imageToCache.height}');
    print('  - 图像类型: ${imageToCache.runtimeType}');
    print('  - 内存地址: ${imageToCache.toString()}');
    print('  - 估算内存占用: ${(imageToCache.width * imageToCache.height * 4 / 1024).toStringAsFixed(2)} KB');
    print('============================');
    
    notifyCacheUpdated(imageToCache, key, parameterValues);
    print('⏱ 结束通知缓存更新时间: ${DateTime.now().toString()}');
    
    // 更新最新的共享变形状态
    setLatestDeformedState(imageToCache, deformedFeaturePoints);
    Logger.flow(_logTag, '保存缓存', '📷 更新共享变形状态: 图像哈希码=${imageToCache.hashCode}, 尺寸=${imageToCache.width}x${imageToCache.height}');
    
    // 更新缓存统计
    Logger.flow(_logTag, '保存缓存', '✅ 3.2.3 保存缓存成功: [缓存键: $key]');
    Logger.flow(_logTag, '保存缓存', '缓存统计: 缓存项数=${_cache.length}, 命中率=${_hitRate.toStringAsFixed(2)}');
    
    // 输出缓存的特征点信息
    final debugKey = DeformationStateKey(parameterValues).toDebugString();
    Logger.flow(_logTag, '保存缓存', '缓存详情: $debugKey');
    Logger.flow(_logTag, '保存缓存', '✅ [完成] 保存缓存');
    Logger.flowEnd(_logTag, '保存缓存');
  }
  
  /// 获取缓存命中率
  double get _hitRate {
    final total = _cacheHits + _cacheMisses;
    return total > 0 ? _cacheHits / total : 0.0;
  }
  
  /// 缓存淘汰策略（LRU - 最近最少使用）
  void _evictCache() {
    if (_cache.isEmpty) return;
    
    // 找到最久未访问的缓存项
    var oldestKey = _cache.keys.first;
    var oldestTime = _cache[oldestKey]!.lastAccessTime;
    
    for (final entry in _cache.entries) {
      if (entry.value.lastAccessTime.isBefore(oldestTime)) {
        oldestKey = entry.key;
        oldestTime = entry.value.lastAccessTime;
      }
    }
    
    // 淘汰该缓存项
    _cache.remove(oldestKey);
    _cacheEvictions++;
    
    Logger.flow(_logTag, '_evictCache', '✅ 淘汰缓存项: $oldestKey, 总淘汰次数: $_cacheEvictions');
  }
  
  /// 清空缓存（只在图片重新导入时调用）
  void clearCache() {
    Logger.flowStart(_logTag, '清空缓存');
    Logger.flow(_logTag, '清空缓存', '⚠️ 缓存清除操作已禁用，仅在图像重新导入时才允许清除缓存');
    Logger.flow(_logTag, '清空缓存', '当前缓存状态: 缓存项数=${_cache.length}');
    
    // 不再清除缓存
    // _cache.clear();
    
    Logger.flow(_logTag, '清空缓存', '✅ 缓存保留不变');
    Logger.flowEnd(_logTag, '清空缓存');
  }
  
  /// 清除所有类型的缓存，包括共享变形状态
  void clearAllCaches() {
    Logger.flowStart(_logTag, '清除所有缓存');
    Logger.flow(_logTag, '清除所有缓存', '⚠️ 缓存清除操作已禁用，仅在图像重新导入时才允许清除缓存');
    
    // 不再清除常规缓存
    // _cache.clear();
    Logger.flow(_logTag, '清除所有缓存', '✅ 常规缓存保留不变');
    
    // 不再清除最新的变形图像和特征点
    // _latestDeformedImage = null;
    // _latestDeformedFeaturePoints = null;
    Logger.flow(_logTag, '清除所有缓存', '✅ 最新变形状态保留不变');
    
    // 不再重置缓存统计
    // _cacheHits = 0;
    // _cacheMisses = 0;
    // _cacheEvictions = 0;
    Logger.flow(_logTag, '清除所有缓存', '✅ 缓存统计保留不变');
    
    // 重置上次保存的缓存键
    _lastSavedCacheKey = null;
    Logger.flow(_logTag, '清除所有缓存', '✅ 上次保存的缓存键已重置');
    
    // 通知监听器
    _notifyLatestDeformedStateChanged();
    Logger.flow(_logTag, '清除所有缓存', '✅ 已通知监听器状态变化');
    
    Logger.flow(_logTag, '清除所有缓存', '✅ 所有缓存已完全清除');
    Logger.flowEnd(_logTag, '清除所有缓存');
  }
  
  /// 根据缓存键获取变形结果
  DeformationStateValue? getDeformationResult(String cacheKey) {
    Logger.flowStart(_logTag, 'getDeformationResult');
    Logger.flow(_logTag, 'getDeformationResult', '根据缓存键获取变形结果: $cacheKey');
    
    // 直接从缓存中获取结果
    final result = _cache[cacheKey];
    
    if (result != null) {
      // 缓存命中
      _cacheHits++;
      result.updateAccessStats();
      
      Logger.flow(_logTag, 'getDeformationResult', '✅ 缓存命中: $cacheKey');
      Logger.flow(_logTag, 'getDeformationResult', '缓存统计: 命中率=${_hitRate.toStringAsFixed(2)}, 总命中次数=$_cacheHits');
      Logger.flowEnd(_logTag, 'getDeformationResult');
      return result;
    } else {
      // 缓存未命中
      _cacheMisses++;
      
      Logger.flow(_logTag, 'getDeformationResult', '❌ 缓存未命中: $cacheKey');
      Logger.flow(_logTag, 'getDeformationResult', '缓存统计: 命中率=${_hitRate.toStringAsFixed(2)}, 总未命中次数=$_cacheMisses');
      Logger.flowEnd(_logTag, 'getDeformationResult');
      return null;
    }
  }
  
  /// 保存变形结果到缓存
  void saveDeformationResult(String cacheKey, ui.Image? deformedImage, List<FeaturePoint> deformedFeaturePoints, {double? facialCenterLineX}) {
    Logger.flowStart(_logTag, 'saveDeformationResult');
    Logger.flow(_logTag, 'saveDeformationResult', '保存变形结果到缓存: $cacheKey');
    
    // 记录当前缓存状态
    Logger.flow(_logTag, 'saveDeformationResult', '保存前缓存状态: 缓存项数=${_cache.length}, 最大缓存容量=$_maxCacheSize');
    
    // 检查是否已存在该缓存
    final isExistingKey = _cache.containsKey(cacheKey);
    if (isExistingKey) {
      Logger.flow(_logTag, 'saveDeformationResult', '覆盖现有缓存项: $cacheKey');
    }
    
    // 检查是否需要淘汰缓存项
    if (_cache.length >= _maxCacheSize && !isExistingKey) {
      Logger.flow(_logTag, 'saveDeformationResult', '缓存已满，需要淘汰缓存项');
      _evictCache();
      Logger.flow(_logTag, 'saveDeformationResult', '淘汰后缓存状态: 缓存项数=${_cache.length}');
    }
    
    // 创建变形状态值
    Logger.flow(_logTag, 'saveDeformationResult', '创建变形状态值: 特征点数=${deformedFeaturePoints.length}, 面部中心线=${facialCenterLineX != null ? facialCenterLineX.toStringAsFixed(2) : '无'}');
    final value = DeformationStateValue(
      deformedImage: deformedImage,
      deformedFeaturePoints: List<FeaturePoint>.from(deformedFeaturePoints),
      facialCenterLineX: facialCenterLineX,
      facialCenterLineCalculated: facialCenterLineX != null,
    );
    
    // 保存到缓存
    _cache[cacheKey] = value;
    
    Logger.flow(_logTag, 'saveDeformationResult', '✅ 变形结果已保存到缓存: $cacheKey');
    Logger.flow(_logTag, 'saveDeformationResult', '保存后缓存状态: 缓存项数=${_cache.length}');
    Logger.flowEnd(_logTag, 'saveDeformationResult');
  }
  
  /// 获取缓存统计信息
  Map<String, dynamic> getStats() {
    return {
      'cacheSize': _cache.length,
      'maxCacheSize': _maxCacheSize,
      'hitCount': _cacheHits,
      'missCount': _cacheMisses,
      'hitRate': _hitRate,
      'evictionCount': _cacheEvictions,
    };
  }
  
  /// 缓存更新监听器列表
  static final List<Function(ui.Image, String, Map<String, double>)> _cacheUpdateListeners = [];
  
  /// 添加缓存更新监听器
  static void addCacheUpdateListener(Function(ui.Image, String, Map<String, double>) listener) {
    Logger.flowStart(_logTag, 'addCacheUpdateListener');
    _cacheUpdateListeners.add(listener);
    Logger.flow(_logTag, 'addCacheUpdateListener', '添加缓存更新监听器，当前监听器数量: ${_cacheUpdateListeners.length}');
    Logger.flowEnd(_logTag, 'addCacheUpdateListener');
  }
  
  /// 移除缓存更新监听器
  static void removeCacheUpdateListener(Function(ui.Image, String, Map<String, double>) listener) {
    Logger.flowStart(_logTag, 'removeCacheUpdateListener');
    _cacheUpdateListeners.remove(listener);
    Logger.flow(_logTag, 'removeCacheUpdateListener', '移除缓存更新监听器，当前监听器数量: ${_cacheUpdateListeners.length}');
    Logger.flowEnd(_logTag, 'removeCacheUpdateListener');
  }
  
  /// 通知所有监听器缓存已更新
  /// 公开方法，允许外部直接调用，以便更新UI
  static void notifyCacheUpdated(ui.Image image, String cacheKey, Map<String, double> parameterValues) {
    Logger.flowStart(_logTag, '_notifyCacheUpdated');
    
    // 记录通知开始时间
    final notifyStartTime = DateTime.now();
    
    // 使用print直接输出，确保在任何模式下都能看到
    print('======== 通知缓存更新 ========');
    print('📷 通知缓存更新:');
    print('  - 缓存键: $cacheKey');
    print('  - 图像哈希码: ${image.hashCode}');
    print('  - 图像尺寸: ${image.width}x${image.height}');
    print('  - 监听器数量: ${_cacheUpdateListeners.length}');
    // 检查是否所有参数值都为0
    bool allZero = parameterValues.entries
      .where((e) => !e.key.startsWith('_'))
      .every((e) => e.value == 0.0);
    
    if (allZero) {
      print('  - 参数值: zero_state');
    } else {
      print('  - 参数值: ${parameterValues.entries.where((e) => e.value != 0.0).map((e) => '${e.key}=${e.value}').join(', ')}');
    }
    print('  - 通知开始时间: ${notifyStartTime.toString()}');
    print('============================');
    
    if (_cacheUpdateListeners.isEmpty) {
      print('⚠️ 警告: 没有缓存更新监听器，无法通知缓存更新');
      Logger.flowWarning(_logTag, '_notifyCacheUpdated', '⚠️ 没有缓存更新监听器，无法通知缓存更新');
      return; // 如果没有监听器，直接返回，避免不必要的处理
    }
    
    // 使用SchedulerBinding.instance.addPostFrameCallback确保UI更新在当前帧渲染完成后进行
    // 这样可以避免"Build scheduled during frame"异常
    
    // 先记录日志，不触发UI更新
    Logger.flow(_logTag, '_notifyCacheUpdated', '收到缓存更新，将在帧渲染完成后通知监听器: $cacheKey');
    
    // 在帧渲染完成后安全地通知监听器
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // 使用循环的方式调用监听器
      for (var listener in _cacheUpdateListeners) {
        try {
          // 在帧渲染完成后调用监听器
          listener(image, cacheKey, parameterValues);
          final callTime = DateTime.now().difference(notifyStartTime).inMilliseconds;
          print('✅ 在帧渲染完成后成功通知监听器: ${listener.hashCode}, 耗时: ${callTime}ms');
        } catch (e) {
          print('❌ 通知监听器失败: ${e.toString()}');
          Logger.flowError(_logTag, '_notifyCacheUpdated', '❌ 通知监听器失败: ${e.toString()}');
        }
      }
      
      // 计算总耗时
      final totalTime = DateTime.now().difference(notifyStartTime).inMilliseconds;
      print('⏱ 通知缓存更新总耗时: ${totalTime}ms');
      print('⏱ 结束通知缓存更新时间: ${DateTime.now()}');
      
      Logger.flow(_logTag, '_notifyCacheUpdated', '在帧渲染完成后通知${_cacheUpdateListeners.length}个监听器缓存已更新, 耗时: ${totalTime}ms');
    });
    
    Logger.flowEnd(_logTag, '_notifyCacheUpdated');
  }
  
  // 是否在调试模式下输出详细日志
  static bool _isVerboseLogging = false;
  
  /// 输出变形图片的信息到日志
  static void logDeformedImageInfo(ui.Image? image, String description) {
    if (image == null) {
      Logger.i(_logTag, '⚠️ $description: 图像为空');
      return;
    }
    
    // 只输出简洁的图像信息，避免日志过多
    Logger.i(_logTag, '📷 $description: 哈希码=${image.hashCode}, 尺寸=${image.width}x${image.height}');
    
    // 只在调试模式下输出详细日志
    if (_isVerboseLogging) {
      // 计算图片的内存占用
      final memorySizeKB = (image.width * image.height * 4) / 1024; // 假设每像素4字节
      Logger.i(_logTag, '  - 内存地址: ${image.toString()}');
      Logger.i(_logTag, '  - 估计内存占用: ${memorySizeKB.toStringAsFixed(2)} KB');
      Logger.i(_logTag, '  - 记录时间: ${DateTime.now().toIso8601String()}');
    }
  }
}
