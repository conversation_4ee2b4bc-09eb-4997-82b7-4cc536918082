import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/logger.dart';
import 'models/feature_point.dart';  // 使用 core 目录下的 FeaturePoint 类
import 'deformation_utils.dart';

/// 鼻子区域可视化工具
/// 负责绘制鼻子相关变形区域的可视化效果
class DeformationAreaVisualizerNose {
  static const String _logTag = 'DeformationAreaVisualizerNose';
  
  /// 绘制鼻梁区域
  static void drawNoseBridgeArea(Canvas canvas, String parameter, List<FeaturePoint> points, double value) {
    Logger.flowStart(_logTag, '绘制鼻梁区域');
    
    if (points.isEmpty) {
      Logger.flowError(_logTag, '绘制鼻梁区域', '没有特征点，无法绘制鼻梁区域');
      Logger.flowEnd(_logTag, '绘制鼻梁区域');
      return;
    }
    
    // 查找鼻梁相关的特征点
    FeaturePoint? noseBridgeTop;
    FeaturePoint? noseBridgeBottom;
    
    for (var point in points) {
      if (point.name?.contains('nose_bridge_top') ?? false) {
        noseBridgeTop = point;
      } else if (point.name?.contains('nose_bridge_bottom') ?? false) {
        noseBridgeBottom = point;
      }
    }
    
    if (noseBridgeTop == null || noseBridgeBottom == null) {
      Logger.flowError(_logTag, '绘制鼻梁区域', '未找到鼻梁特征点');
      Logger.flowEnd(_logTag, '绘制鼻梁区域');
      return;
    }
    
    Logger.flow(_logTag, '绘制鼻梁区域', '找到鼻梁特征点: top(${noseBridgeTop.x}, ${noseBridgeTop.y}), bottom(${noseBridgeBottom.x}, ${noseBridgeBottom.y})');
    
    // 创建画笔
    final paint = Paint()
      ..color = Colors.red.withOpacity(0.5)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;
    
    // 计算鼻梁区域
    final centerX = (noseBridgeTop.x + noseBridgeBottom.x) / 2;
    final width = (noseBridgeTop.x - noseBridgeBottom.x).abs() * 2;
    final height = (noseBridgeTop.y - noseBridgeBottom.y).abs();
    
    Logger.flow(_logTag, '绘制鼻梁区域', '计算区域: centerX=$centerX, width=$width, height=$height');
    
    // 绘制鼻梁区域
    final rect = Rect.fromCenter(
      center: Offset(centerX, (noseBridgeTop.y + noseBridgeBottom.y) / 2),
      width: width,
      height: height,
    );
    
    canvas.drawRect(rect, paint);
    
    // 绘制方向箭头
    final arrowPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    // 根据参数值确定箭头方向
    if (value > 0) {
      Logger.flow(_logTag, '绘制鼻梁区域', '绘制向上箭头 - 增加高度');
      // 向上箭头 - 增加高度
      canvas.drawLine(
        Offset(centerX, noseBridgeTop.y + height / 4),
        Offset(centerX, noseBridgeTop.y - height / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(centerX, noseBridgeTop.y - height / 4),
        Offset(centerX - width / 8, noseBridgeTop.y),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(centerX, noseBridgeTop.y - height / 4),
        Offset(centerX + width / 8, noseBridgeTop.y),
        arrowPaint,
      );
    } else if (value < 0) {
      Logger.flow(_logTag, '绘制鼻梁区域', '绘制向下箭头 - 降低高度');
      // 向下箭头 - 降低高度
      canvas.drawLine(
        Offset(centerX, noseBridgeTop.y - height / 4),
        Offset(centerX, noseBridgeTop.y + height / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(centerX, noseBridgeTop.y + height / 4),
        Offset(centerX - width / 8, noseBridgeTop.y),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(centerX, noseBridgeTop.y + height / 4),
        Offset(centerX + width / 8, noseBridgeTop.y),
        arrowPaint,
      );
    }
    
    // 绘制参数名称
    final textPainter = TextPainter(
      text: TextSpan(
        text: '鼻梁高度',
        style: TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, Offset(
      centerX - textPainter.width / 2,
      noseBridgeTop.y - textPainter.height - 5,
    ));
    
    Logger.flowEnd(_logTag, '绘制鼻梁区域');
  }
  
  /// 绘制鼻尖区域
  static void drawNoseTipArea(Canvas canvas, String parameter, List<FeaturePoint> points, double value) {
    Logger.flowStart(_logTag, '绘制鼻尖区域');
    
    if (points.isEmpty) {
      Logger.flowError(_logTag, '绘制鼻尖区域', '没有特征点，无法绘制鼻尖区域');
      Logger.flowEnd(_logTag, '绘制鼻尖区域');
      return;
    }
    
    // 查找鼻尖相关的特征点
    FeaturePoint? noseTip;
    
    for (var point in points) {
      if (point.name?.contains('nose_tip') ?? false) {
        noseTip = point;
        break;
      }
    }
    
    if (noseTip == null) {
      Logger.flowError(_logTag, '绘制鼻尖区域', '未找到鼻尖特征点');
      Logger.flowEnd(_logTag, '绘制鼻尖区域');
      return;
    }
    
    Logger.flow(_logTag, '绘制鼻尖区域', '找到鼻尖特征点: (${noseTip.x}, ${noseTip.y})');
    
    // 创建画笔
    final paint = Paint()
      ..color = Colors.red.withOpacity(0.5)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;
    
    // 绘制鼻尖区域
    final radius = 20.0;
    canvas.drawCircle(Offset(noseTip.x, noseTip.y), radius, paint);
    
    // 绘制方向箭头
    final arrowPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    // 根据参数值确定箭头方向
    if (value > 0) {
      Logger.flow(_logTag, '绘制鼻尖区域', '绘制向上箭头 - 提升鼻尖');
      // 向上箭头 - 提升鼻尖
      canvas.drawLine(
        Offset(noseTip.x, noseTip.y + radius / 2),
        Offset(noseTip.x, noseTip.y - radius / 2),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(noseTip.x, noseTip.y - radius / 2),
        Offset(noseTip.x - radius / 4, noseTip.y - radius / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(noseTip.x, noseTip.y - radius / 2),
        Offset(noseTip.x + radius / 4, noseTip.y - radius / 4),
        arrowPaint,
      );
    } else if (value < 0) {
      Logger.flow(_logTag, '绘制鼻尖区域', '绘制向下箭头 - 降低鼻尖');
      // 向下箭头 - 降低鼻尖
      canvas.drawLine(
        Offset(noseTip.x, noseTip.y - radius / 2),
        Offset(noseTip.x, noseTip.y + radius / 2),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(noseTip.x, noseTip.y + radius / 2),
        Offset(noseTip.x - radius / 4, noseTip.y + radius / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(noseTip.x, noseTip.y + radius / 2),
        Offset(noseTip.x + radius / 4, noseTip.y + radius / 4),
        arrowPaint,
      );
    }
    
    // 绘制参数名称
    final textPainter = TextPainter(
      text: TextSpan(
        text: '鼻尖调整',
        style: TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, Offset(
      noseTip.x - textPainter.width / 2,
      noseTip.y - radius - textPainter.height - 5,
    ));
    
    Logger.flowEnd(_logTag, '绘制鼻尖区域');
  }
  
  /// 绘制鼻翼区域
  static void drawNoseNostrilArea(Canvas canvas, String parameter, List<FeaturePoint> points, double value) {
    Logger.flowStart(_logTag, '绘制鼻翼区域');
    
    if (points.isEmpty) {
      Logger.flowError(_logTag, '绘制鼻翼区域', '没有特征点，无法绘制鼻翼区域');
      Logger.flowEnd(_logTag, '绘制鼻翼区域');
      return;
    }
    
    // 查找鼻翼相关的特征点
    FeaturePoint? leftNostril;
    FeaturePoint? rightNostril;
    
    for (var point in points) {
      if (point.name?.contains('left_nostril') ?? false) {
        leftNostril = point;
      } else if (point.name?.contains('right_nostril') ?? false) {
        rightNostril = point;
      }
    }
    
    if (leftNostril == null || rightNostril == null) {
      Logger.flowError(_logTag, '绘制鼻翼区域', '未找到鼻翼特征点');
      Logger.flowEnd(_logTag, '绘制鼻翼区域');
      return;
    }
    
    Logger.flow(_logTag, '绘制鼻翼区域', '找到鼻翼特征点: 左(${leftNostril.x}, ${leftNostril.y}), 右(${rightNostril.x}, ${rightNostril.y})');
    
    // 创建画笔
    final paint = Paint()
      ..color = Colors.red.withOpacity(0.5)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;
    
    // 计算鼻翼区域
    final centerX = (leftNostril.x + rightNostril.x) / 2;
    final width = (rightNostril.x - leftNostril.x).abs() * 1.2;
    final height = width / 2;
    final centerY = (leftNostril.y + rightNostril.y) / 2;
    
    Logger.flow(_logTag, '绘制鼻翼区域', '计算区域: centerX=$centerX, centerY=$centerY, width=$width, height=$height');
    
    // 绘制左鼻翼区域
    final leftRadius = width / 4;
    canvas.drawCircle(Offset(leftNostril.x, leftNostril.y), leftRadius, paint);
    
    // 绘制右鼻翼区域
    final rightRadius = width / 4;
    canvas.drawCircle(Offset(rightNostril.x, rightNostril.y), rightRadius, paint);
    
    // 绘制方向箭头
    final arrowPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    // 根据参数值确定箭头方向
    if (value > 0) {
      Logger.flow(_logTag, '绘制鼻翼区域', '绘制向外箭头 - 增宽鼻翼');
      // 向外箭头 - 增宽鼻翼
      // 左侧箭头
      canvas.drawLine(
        Offset(leftNostril.x + leftRadius / 2, leftNostril.y),
        Offset(leftNostril.x - leftRadius / 2, leftNostril.y),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(leftNostril.x - leftRadius / 2, leftNostril.y),
        Offset(leftNostril.x - leftRadius / 4, leftNostril.y - leftRadius / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(leftNostril.x - leftRadius / 2, leftNostril.y),
        Offset(leftNostril.x - leftRadius / 4, leftNostril.y + leftRadius / 4),
        arrowPaint,
      );
      
      // 右侧箭头
      canvas.drawLine(
        Offset(rightNostril.x - rightRadius / 2, rightNostril.y),
        Offset(rightNostril.x + rightRadius / 2, rightNostril.y),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(rightNostril.x + rightRadius / 2, rightNostril.y),
        Offset(rightNostril.x + rightRadius / 4, rightNostril.y - rightRadius / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(rightNostril.x + rightRadius / 2, rightNostril.y),
        Offset(rightNostril.x + rightRadius / 4, rightNostril.y + rightRadius / 4),
        arrowPaint,
      );
    } else if (value < 0) {
      Logger.flow(_logTag, '绘制鼻翼区域', '绘制向内箭头 - 缩小鼻翼');
      // 向内箭头 - 缩小鼻翼
      // 左侧箭头
      canvas.drawLine(
        Offset(leftNostril.x - leftRadius / 2, leftNostril.y),
        Offset(leftNostril.x + leftRadius / 2, leftNostril.y),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(leftNostril.x + leftRadius / 2, leftNostril.y),
        Offset(leftNostril.x + leftRadius / 4, leftNostril.y - leftRadius / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(leftNostril.x + leftRadius / 2, leftNostril.y),
        Offset(leftNostril.x + leftRadius / 4, leftNostril.y + leftRadius / 4),
        arrowPaint,
      );
      
      // 右侧箭头
      canvas.drawLine(
        Offset(rightNostril.x + rightRadius / 2, rightNostril.y),
        Offset(rightNostril.x - rightRadius / 2, rightNostril.y),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(rightNostril.x - rightRadius / 2, rightNostril.y),
        Offset(rightNostril.x - rightRadius / 4, rightNostril.y - rightRadius / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(rightNostril.x - rightRadius / 2, rightNostril.y),
        Offset(rightNostril.x - rightRadius / 4, rightNostril.y + rightRadius / 4),
        arrowPaint,
      );
    }
    
    // 绘制参数名称
    final textPainter = TextPainter(
      text: TextSpan(
        text: '鼻翼宽度',
        style: TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, Offset(
      centerX - textPainter.width / 2,
      centerY - height - textPainter.height - 5,
    ));
    
    Logger.flowEnd(_logTag, '绘制鼻翼区域');
  }
  
  /// 绘制鼻基区域
  static void drawNoseBaseArea(Canvas canvas, String parameter, List<FeaturePoint> points, double value) {
    Logger.flowStart(_logTag, '绘制鼻基区域');
    
    if (points.isEmpty) {
      Logger.flowError(_logTag, '绘制鼻基区域', '没有特征点，无法绘制鼻基区域');
      Logger.flowEnd(_logTag, '绘制鼻基区域');
      return;
    }
    
    // 查找鼻基相关的特征点
    FeaturePoint? noseBase;
    
    for (var point in points) {
      if (point.name?.contains('nose_base') ?? false) {
        noseBase = point;
        break;
      }
    }
    
    if (noseBase == null) {
      Logger.flowError(_logTag, '绘制鼻基区域', '未找到鼻基特征点');
      Logger.flowEnd(_logTag, '绘制鼻基区域');
      return;
    }
    
    Logger.flow(_logTag, '绘制鼻基区域', '找到鼻基特征点: (${noseBase.x}, ${noseBase.y})');
    
    // 创建画笔
    final paint = Paint()
      ..color = Colors.red.withOpacity(0.5)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;
    
    // 绘制鼻基区域
    final width = 40.0;
    final height = 20.0;
    final rect = Rect.fromCenter(
      center: Offset(noseBase.x, noseBase.y),
      width: width,
      height: height,
    );
    
    canvas.drawRect(rect, paint);
    
    // 绘制方向箭头
    final arrowPaint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    // 根据参数值确定箭头方向
    if (value > 0) {
      Logger.flow(_logTag, '绘制鼻基区域', '绘制向上箭头 - 抬高鼻基');
      // 向上箭头 - 抬高鼻基
      canvas.drawLine(
        Offset(noseBase.x, noseBase.y + height / 2),
        Offset(noseBase.x, noseBase.y - height / 2),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(noseBase.x, noseBase.y - height / 2),
        Offset(noseBase.x - width / 8, noseBase.y - height / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(noseBase.x, noseBase.y - height / 2),
        Offset(noseBase.x + width / 8, noseBase.y - height / 4),
        arrowPaint,
      );
    } else if (value < 0) {
      Logger.flow(_logTag, '绘制鼻基区域', '绘制向下箭头 - 降低鼻基');
      // 向下箭头 - 降低鼻基
      canvas.drawLine(
        Offset(noseBase.x, noseBase.y - height / 2),
        Offset(noseBase.x, noseBase.y + height / 2),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(noseBase.x, noseBase.y + height / 2),
        Offset(noseBase.x - width / 8, noseBase.y + height / 4),
        arrowPaint,
      );
      canvas.drawLine(
        Offset(noseBase.x, noseBase.y + height / 2),
        Offset(noseBase.x + width / 8, noseBase.y + height / 4),
        arrowPaint,
      );
    }
    
    // 绘制参数名称
    final textPainter = TextPainter(
      text: TextSpan(
        text: '鼻基抬高',
        style: TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(canvas, Offset(
      noseBase.x - textPainter.width / 2,
      noseBase.y + height / 2 + 5,
    ));
    
    Logger.flowEnd(_logTag, '绘制鼻基区域');
  }
}
