#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

import cv2
import numpy as np
import mediapipe as mp
import json
import os
import sys
import math
from typing import List, Dict, Tuple, Any, Optional

# 确保使用Python 3.11
if not (sys.version_info.major == 3 and sys.version_info.minor == 11):
    print("警告: 此程序需要Python 3.11版本")
    sys.exit(1)

class FaceDeformationVisualizer:
    """面部变形区域可视化器"""
    
    def __init__(self):
        """初始化面部变形区域可视化器"""
        # 初始化MediaPipe人脸网格检测器
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        )
        
        # 区域颜色映射
        self.area_colors = {
            'face_contour': (128, 0, 128),  # 紫色
            'nose': (0, 0, 255),           # 红色
            'eyes': (255, 0, 0),           # 蓝色
            'lips': (255, 0, 255),         # 粉色
            'anti_aging': (255, 165, 0)    # 橙色
        }
        
        # 变形类型颜色映射
        self.transform_colors = {
            'vector_field': (0, 255, 0),    # 绿色
            'tps': (255, 0, 0),            # 红色
            'mesh': (0, 0, 255),           # 蓝色
            'local': (255, 255, 0),        # 黄色
            'triangulation': (128, 0, 128) # 紫色
        }
        
        # 参数变形类型映射
        self.parameter_transform_types = {
            # 面部轮廓
            'contour_tighten': 'mesh',
            'chin_adjust': 'local',
            'cheekbone_adjust': 'tps',
            'face_shape': 'triangulation',
            
            # 鼻部塑形
            'bridge_height': 'tps',
            'tip_adjust': 'local',
            'nostril_width': 'vector_field',
            'base_height': 'mesh',
            
            # 眼部美化
            'double_fold': 'local',
            'canthal_tilt': 'vector_field',
            'eye_bag_removal': 'tps',
            'outer_corner_lift': 'triangulation',
            
            # 唇部造型
            'lip_shape': 'triangulation',
            'lip_thickness': 'vector_field',
            'mouth_corner': 'local',
            'lip_color': 'mesh',
            
            # 抗衰冻龄
            'nasolabial_folds': 'tps',
            'wrinkle_removal': 'local',
            'forehead_fullness': 'mesh',
            'facial_firmness': 'triangulation'
        }
        
        # 参数点索引映射 - 基于feature_points_data.dart中的定义
        self.parameter_points = self._init_parameter_points()
    
    def _init_parameter_points(self):
        """初始化参数点索引映射"""
        return {
            # 面部轮廓
            'face_contour': {
                'contour_tighten': [67, 297, 109, 338, 162, 389, 71, 301, 68, 298, 104, 169, 394],
                'chin_adjust': [152, 175, 148, 149, 150, 151, 377, 378, 379],
                'cheekbone_adjust': [216, 436, 123, 352, 50, 280, 207, 427, 187, 411],
                'face_shape': [67, 297, 109, 338, 145, 374, 146, 375, 147, 376, 162, 389, 21, 251, 54, 284, 104, 333]
            },
            
            # 鼻部塑形
            'nose': {
                'bridge_height': [168, 6, 197, 195, 5, 4],
                'tip_adjust': [1, 2, 3, 4, 5, 6, 19, 94, 197, 168],
                'nostril_width': [129, 358, 209, 429, 48, 278],
                'base_height': [2, 3, 4, 5, 94, 19]
            },
            
            # 眼部美化
            'eyes': {
                'double_fold': [159, 386, 158, 385, 157, 384, 156, 383, 155, 382],
                'canthal_tilt': [133, 362, 130, 359],
                'eye_bag_removal': [145, 374, 144, 373, 143, 372, 142, 371],
                'outer_corner_lift': [130, 359, 145, 374]
            },
            
            # 唇部造型
            'lips': {
                'lip_shape': [61, 291, 76, 306, 62, 292, 78, 308, 95, 325, 88, 318, 184, 408, 183, 407, 42, 272, 41, 271],
                'lip_thickness': [61, 291, 76, 306, 62, 292, 78, 308, 95, 325, 88, 318],
                'mouth_corner': [61, 291, 76, 306],
                'lip_color': [61, 291, 76, 306, 62, 292, 78, 308, 95, 325, 88, 318, 184, 408, 183, 407, 42, 272, 41, 271]
            },
            
            # 抗衰冻龄
            'anti_aging': {
                'nasolabial_folds': [129, 358, 130, 359, 131, 360, 133, 362, 167, 393],
                'wrinkle_removal': [107, 336, 108, 337, 148, 377],
                'forehead_fullness': [21, 251, 71, 301, 162, 389, 68, 298],
                'facial_firmness': [54, 284, 104, 333, 103, 332, 67, 297]
            }
        }
    
    def detect_face_landmarks(self, image_path: str) -> Optional[List[Tuple[float, float]]]:
        """
        检测图像中的面部特征点
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            特征点列表 [(x, y), ...] 或 None（如果未检测到面部）
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图像: {image_path}")
            return None
        
        # 转换为RGB（MediaPipe需要RGB格式）
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        height, width = image.shape[:2]
        
        # 检测面部特征点
        results = self.face_mesh.process(image_rgb)
        
        # 如果未检测到面部，返回None
        if not results.multi_face_landmarks:
            print("未检测到面部")
            return None
        
        # 提取第一个面部的特征点
        face_landmarks = results.multi_face_landmarks[0]
        
        # 转换特征点坐标为像素坐标
        landmarks = []
        for landmark in face_landmarks.landmark:
            x = int(landmark.x * width)
            y = int(landmark.y * height)
            landmarks.append((x, y))
        
        return landmarks
    
    def visualize_all_parameters(self, image_path: str, output_dir: str = None):
        """
        可视化所有参数的变形区域
        
        Args:
            image_path: 图像文件路径
            output_dir: 输出目录（可选）
        """
        # 检测面部特征点
        landmarks = self.detect_face_landmarks(image_path)
        if landmarks is None:
            print("未检测到面部，无法可视化")
            return
        
        # 创建输出目录（如果不存在）
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 遍历所有区域和参数
        for area_name, params in self.parameter_points.items():
            for param_name in params.keys():
                # 可视化参数区域
                output_path = None
                if output_dir:
                    output_path = os.path.join(output_dir, f"{area_name}_{param_name}.jpg")
                
                # 使用正值和负值分别可视化
                for param_value in [0.8, -0.8]:
                    value_suffix = "positive" if param_value > 0 else "negative"
                    if output_dir:
                        output_path = os.path.join(output_dir, f"{area_name}_{param_name}_{value_suffix}.jpg")
                    
                    self.visualize_parameter_area(
                        image_path, area_name, param_name, param_value, output_path
                    )
                    print(f"已生成 {area_name} - {param_name} ({value_suffix}) 的可视化")
    
    def visualize_parameter_area(self, 
                                image_path: str, 
                                area_name: str, 
                                parameter_name: str, 
                                param_value: float = 1.0,
                                output_path: str = None) -> np.ndarray:
        """
        可视化特定参数的变形区域
        
        Args:
            image_path: 图像文件路径
            area_name: 区域名称（face_contour, nose, eyes, lips, anti_aging）
            parameter_name: 参数名称
            param_value: 参数值（-1.0到1.0）
            output_path: 输出图像路径（可选）
            
        Returns:
            可视化后的图像
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图像: {image_path}")
            return None
        
        # 检测面部特征点
        landmarks = self.detect_face_landmarks(image_path)
        if landmarks is None:
            print("未检测到面部，无法可视化")
            return image
        
        # 创建可视化图像（原图的副本）
        vis_image = image.copy()
        
        # 获取参数对应的变形类型
        transform_type = self.parameter_transform_types.get(parameter_name, 'local')
        
        # 获取变形类型对应的颜色
        color = self.transform_colors.get(transform_type)
        
        # 获取与参数相关的特征点索引
        point_indices = self.parameter_points.get(area_name, {}).get(parameter_name, [])
        
        # 提取相关特征点
        parameter_points = [landmarks[idx] for idx in point_indices if idx < len(landmarks)]
        
        # 根据变形类型绘制不同的可视化效果
        if transform_type == 'vector_field':
            self._draw_vector_field_area(vis_image, parameter_points, color, param_value)
        elif transform_type == 'tps':
            self._draw_tps_area(vis_image, parameter_points, color, param_value)
        elif transform_type == 'mesh':
            self._draw_mesh_area(vis_image, parameter_points, color, param_value)
        elif transform_type == 'local':
            self._draw_local_area(vis_image, parameter_points, color, param_value)
        elif transform_type == 'triangulation':
            self._draw_triangulation_area(vis_image, parameter_points, color, param_value)
        
        # 绘制特征点
        for point in parameter_points:
            cv2.circle(vis_image, point, 2, (255, 255, 255), -1)
            cv2.circle(vis_image, point, 3, (0, 0, 0), 1)
        
        # 添加参数信息
        cv2.putText(vis_image, f"{area_name}: {parameter_name}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(vis_image, f"Value: {param_value:.2f}", 
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(vis_image, f"Transform: {transform_type}", 
                   (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 保存图像（如果指定了输出路径）
        if output_path:
            cv2.imwrite(output_path, vis_image)
            print(f"可视化结果已保存到: {output_path}")
        
        return vis_image
    
    def _draw_vector_field_area(self, image: np.ndarray, points: List[Tuple[int, int]], 
                              color: Tuple[int, int, int], param_value: float) -> None:
        """绘制向量场变形区域 - 绿色"""
        if not points:
            return
        
        # 计算区域中心点
        center_x = sum(p[0] for p in points) // len(points)
        center_y = sum(p[1] for p in points) // len(points)
        
        # 创建透明叠加层
        overlay = image.copy()
        
        # 计算影响半径
        max_dist = max(max(abs(p[0] - center_x), abs(p[1] - center_y)) for p in points)
        radius = max(max_dist * 1.5, 50)
        
        # 绘制渐变圆形区域，表示向量场影响范围
        cv2.circle(overlay, (center_x, center_y), int(radius), (*color, 128), -1)
        
        # 绘制向量方向
        arrow_color = color
        for point in points:
            # 计算向量方向 - 根据参数值确定方向
            vector_length = 15.0 * abs(param_value)
            direction = 1 if param_value > 0 else -1
            
            # 计算向量方向（从中心点指向特征点或相反）
            dx = point[0] - center_x
            dy = point[1] - center_y
            if dx == 0 and dy == 0:
                continue
                
            # 归一化方向向量
            length = (dx**2 + dy**2)**0.5
            dx = dx / length * vector_length * direction
            dy = dy / length * vector_length * direction
            
            # 绘制箭头
            cv2.arrowedLine(overlay, point, (int(point[0] + dx), int(point[1] + dy)), 
                          arrow_color, 2, tipLength=0.3)
        
        # 将透明叠加层与原图混合
        alpha = 0.6  # 透明度
        cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0, image)
    
    def _draw_tps_area(self, image: np.ndarray, points: List[Tuple[int, int]], 
                     color: Tuple[int, int, int], param_value: float) -> None:
        """绘制薄板样条插值变形区域 - 红色"""
        if not points:
            return
        
        # 创建透明叠加层
        overlay = image.copy()
        height, width = image.shape[:2]
        
        # 计算区域中心点
        center_x = sum(p[0] for p in points) // len(points)
        center_y = sum(p[1] for p in points) // len(points)
        
        # 计算最大距离
        max_dist = max(max(abs(p[0] - center_x), abs(p[1] - center_y)) for p in points)
        influence_radius = max(max_dist * 2.5, 100)
        
        # 为了性能，只在感兴趣区域内计算热力图
        min_x = max(0, center_x - int(influence_radius))
        max_x = min(width, center_x + int(influence_radius))
        min_y = max(0, center_y - int(influence_radius))
        max_y = min(height, center_y + int(influence_radius))
        
        # 绘制渐变椭圆
        for r in range(1, int(influence_radius) + 1, 5):  # 步长为5，提高性能
            alpha = int(255 * (1 - (r / influence_radius)**2) * 0.7 * abs(param_value))
            if alpha <= 0:
                continue
            cv2.ellipse(overlay, (center_x, center_y), (r, r), 0, 0, 360, (*color, alpha), -1)
        
        # 将透明叠加层与原图混合
        cv2.addWeighted(overlay, 0.7, image, 0.3, 0, image)
        
        # 绘制控制点
        for point in points:
            cv2.circle(image, point, 3, (255, 255, 255), -1)
            cv2.circle(image, point, 4, (0, 0, 0), 1)
    
    def _draw_mesh_area(self, image: np.ndarray, points: List[Tuple[int, int]], 
                      color: Tuple[int, int, int], param_value: float) -> None:
        """绘制网格变形区域 - 蓝色"""
        if not points:
            return
        
        # 计算区域边界
        min_x = min(p[0] for p in points)
        min_y = min(p[1] for p in points)
        max_x = max(p[0] for p in points)
        max_y = max(p[1] for p in points)
        
        # 扩展边界以包含完整的网格
        padding = max(max_x - min_x, max_y - min_y) * 0.2
        min_x = max(0, int(min_x - padding))
        min_y = max(0, int(min_y - padding))
        max_x = min(image.shape[1], int(max_x + padding))
        max_y = min(image.shape[0], int(max_y + padding))
        
        # 创建透明叠加层
        overlay = image.copy()
        
        # 绘制背景区域
        cv2.rectangle(overlay, (min_x, min_y), (max_x, max_y), 
                    (*color, int(128 * 0.3 * abs(param_value))), -1)
        
        # 网格密度 - 根据区域大小调整
        grid_size = max(10, min(max_x - min_x, max_y - min_y) // 6)
        
        # 绘制网格线
        grid_color = (*color, int(128 * 0.7))
        
        # 绘制水平网格线
        for y in range(min_y, max_y + 1, grid_size):
            cv2.line(overlay, (min_x, y), (max_x, y), grid_color, 1)
        
        # 绘制垂直网格线
        for x in range(min_x, max_x + 1, grid_size):
            cv2.line(overlay, (x, min_y), (x, max_y), grid_color, 1)
        
        # 将透明叠加层与原图混合
        alpha = 0.7  # 透明度
        cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0, image)
        
        # 绘制控制点
        for point in points:
            cv2.circle(image, point, 3, (255, 255, 255), -1)
            cv2.circle(image, point, 4, (0, 0, 0), 1)
    
    def _draw_local_area(self, image: np.ndarray, points: List[Tuple[int, int]], 
                       color: Tuple[int, int, int], param_value: float) -> None:
        """绘制局部变形区域 - 黄色"""
        if not points:
            return
        
        # 创建透明叠加层
        overlay = image.copy()
        
        # 计算区域中心点
        center_x = sum(p[0] for p in points) // len(points)
        center_y = sum(p[1] for p in points) // len(points)
        
        # 计算影响半径
        max_dist = max(max(abs(p[0] - center_x), abs(p[1] - center_y)) for p in points)
        radius = max(max_dist * 2, 50)
        
        # 创建径向渐变
        for r in range(int(radius) + 1):
            alpha = int(255 * (1 - (r / radius)**2) * abs(param_value) * 0.7)
            if alpha <= 0:
                continue
                
            cv2.circle(overlay, (center_x, center_y), r, 
                     (*color, alpha), 1)
        
        # 将透明叠加层与原图混合
        cv2.addWeighted(overlay, 0.7, image, 0.3, 0, image)
        
        # 绘制参数值方向指示器
        if param_value != 0:
            direction = 1 if param_value > 0 else -1
            arrow_length = 20 * abs(param_value)
            arrow_color = (255, 255, 255)
            
            # 垂直方向箭头
            cv2.arrowedLine(image, 
                          (center_x, center_y), 
                          (center_x, int(center_y - arrow_length * direction)), 
                          arrow_color, 2, tipLength=0.3)
        
        # 绘制控制点
        for point in points:
            cv2.circle(image, point, 3, (255, 255, 255), -1)
            cv2.circle(image, point, 4, (0, 0, 0), 1)
    
    def _draw_triangulation_area(self, image: np.ndarray, points: List[Tuple[int, int]], 
                               color: Tuple[int, int, int], param_value: float) -> None:
        """绘制三角剖分变形区域 - 紫色"""
        if len(points) < 3:
            return
        
        # 创建透明叠加层
        overlay = image.copy()
        
        # 计算区域中心点
        center_x = sum(p[0] for p in points) // len(points)
        center_y = sum(p[1] for p in points) // len(points)
        
        # 将点转换为numpy数组，以便进行三角剖分
        points_array = np.array(points)
        
        try:
            # 创建Delaunay三角剖分
            from scipy.spatial import Delaunay
            tri = Delaunay(points_array)
            
            # 绘制三角形
            for simplex in tri.simplices:
                pts = np.array([points_array[simplex[0]], points_array[simplex[1]], points_array[simplex[2]]], np.int32)
                pts = pts.reshape((-1, 1, 2))
                
                # 填充三角形
                cv2.fillPoly(overlay, [pts], (*color, int(128 * 0.4 * abs(param_value))))
                
                # 绘制三角形边缘
                cv2.polylines(overlay, [pts], True, (*color, int(128 * 0.8)), 1)
        except Exception as e:
            print(f"三角剖分失败: {e}")
            # 如果三角剖分失败，绘制简单的凸包
            cv2.fillConvexPoly(overlay, points_array.astype(np.int32), 
                             (*color, int(128 * 0.4 * abs(param_value))))
            cv2.polylines(overlay, [points_array.astype(np.int32)], True, 
                        (*color, int(128 * 0.8)), 1)
        
        # 将透明叠加层与原图混合
        alpha = 0.7  # 透明度
        cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0, image)
        
        # 绘制控制点
        for point in points:
            cv2.circle(image, point, 3, (255, 255, 255), -1)
            cv2.circle(image, point, 4, (0, 0, 0), 1)
        
        # 绘制参数值方向指示器
        if param_value != 0:
            direction = 1 if param_value > 0 else -1
            arrow_length = 20 * abs(param_value)
            arrow_color = (255, 255, 255)
            
            # 绘制从中心点出发的多个方向箭头
            for angle in range(0, 360, 45):
                rad = math.radians(angle)
                end_x = int(center_x + arrow_length * direction * math.cos(rad))
                end_y = int(center_y + arrow_length * direction * math.sin(rad))
                cv2.arrowedLine(image, (center_x, center_y), (end_x, end_y), arrow_color, 1, tipLength=0.3)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='面部特征变形区域可视化')
    parser.add_argument('--image', type=str, required=True, help='输入图像路径')
    parser.add_argument('--area', type=str, required=False, 
                        choices=['face_contour', 'nose', 'eyes', 'lips', 'anti_aging'],
                        help='美化区域名称')
    parser.add_argument('--param', type=str, required=False, help='参数名称')
    parser.add_argument('--value', type=float, default=1.0, help='参数值 (-1.0 到 1.0)')
    parser.add_argument('--output', type=str, help='输出图像路径')
    parser.add_argument('--all', action='store_true', help='生成所有参数的可视化')
    parser.add_argument('--output-dir', type=str, help='输出目录（与--all一起使用）')
    
    args = parser.parse_args()
    
    # 创建可视化器
    visualizer = FaceDeformationVisualizer()
    
    if args.all:
        # 可视化所有参数
        visualizer.visualize_all_parameters(args.image, args.output_dir)
    else:
        if not args.area or not args.param:
            print("错误: 当不使用--all选项时，必须指定--area和--param参数")
            parser.print_help()
            return
        
        # 可视化特定参数区域
        result = visualizer.visualize_parameter_area(
            args.image, args.area, args.param, args.value, args.output
        )
        
        if result is not None and not args.output:
            # 显示结果
            cv2.imshow('Visualization', result)
            cv2.waitKey(0)
            cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
