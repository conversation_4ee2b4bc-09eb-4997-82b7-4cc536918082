// 特征点配置文件
// 定义面部特征变形的参数配置和映射关系

import 'package:flutter/foundation.dart';
import 'transform_base.dart';

/// 区域配置
/// 定义一个区域的特征点集合和相关属性
class RegionConfig {
  /// 区域的边界点列表
  /// 这些点定义了区域的外轮廓
  final List<int> boundaryPoints;

  /// 区域的中心点
  /// 可用于定义变形的参考点或旋转中心
  final int? centerPoint;

  /// 对称点对列表
  /// 每个点对由一个 Map 定义，包含 'left' 和 'right' 两个点
  /// 用于处理面部左右对称的变形
  final List<Map<String, int>> symmetricPairs;

  /// 轮廓线列表
  /// 每个轮廓线是一个点列表，定义了一条完整的轮廓
  /// 用于绘制和变形计算
  final List<List<int>> contourLines;

  /// 创建一个区域配置
  /// 
  /// [boundaryPoints] 是必需的，定义区域的边界点
  /// [centerPoint] 是可选的中心点
  /// [symmetricPairs] 是可选的对称点对列表，默认为空列表
  /// [contourLines] 是可选的轮廓线列表，默认为空列表
  const RegionConfig({
    required this.boundaryPoints,
    this.centerPoint,
    this.symmetricPairs = const [],
    this.contourLines = const [],
  });

  @override
  String toString() {
    final parts = <String>[
      '边界点: $boundaryPoints',
      if (centerPoint != null) '中心点: $centerPoint',
      if (symmetricPairs.isNotEmpty) '对称点对: $symmetricPairs',
      if (contourLines.isNotEmpty) '轮廓线: $contourLines',
    ];
    return parts.join(', ');
  }
}

/// 参数配置
class ParameterConfig {
  final String displayName;
  final List<int> primaryPoints;
  final List<int> secondaryPoints;
  final List<int> auxiliaryPoints;
  final Map<int, PointTransformConfig> transformConfigs;
  final IntensityMapping intensityMapping;
  final double minValue;
  final double maxValue;
  final double defaultValue;
  final double weight;
  final RegionConfig? region;

  const ParameterConfig({
    required this.displayName,
    required this.primaryPoints,
    required this.secondaryPoints,
    required this.auxiliaryPoints,
    required this.transformConfigs,
    required this.intensityMapping,
    this.minValue = -1.0,
    this.maxValue = 1.0,
    this.defaultValue = 0.0,
    this.weight = 1.0,
    this.region,
  });

  @override
  String toString() {
    return '''
    显示名称: $displayName
    主要点: $primaryPoints
    次要点: $secondaryPoints
    辅助点: $auxiliaryPoints
    默认值: $defaultValue
    权重: $weight
    ''';
  }
}

/// 区域配置
class AreaConfig {
  /// 区域显示名称
  final String displayName;

  /// 参数配置集
  final Map<String, ParameterConfig> parameters;

  /// 区域特征点配置
  final RegionConfig region;

  /// 创建一个区域配置
  /// 
  /// [displayName] 区域显示名称
  /// [parameters] 参数配置集
  /// [region] 区域特征点配置
  const AreaConfig({
    required this.displayName,
    required this.parameters,
    required this.region,
  });

  @override
  String toString() {
    return '''
    区域名称: $displayName
    参数数量: ${parameters.length}
    区域配置: $region
    ''';
  }
}

/// 中心线点集合
const Set<int> centerLinePoints = {
  10,   // 发际线中心
  151,  // 前额中心
  168,  // 鼻梁顶部
  6,    // 鼻尖
  195,  // 上唇中心
  0,    // 下唇中心
  175,  // 下巴顶部
  152,  // 下巴尖
};

/// 特征点配置管理器
class FeaturePointsManager {
  static final FeaturePointsManager _instance = FeaturePointsManager._internal();
  factory FeaturePointsManager() => _instance;
  FeaturePointsManager._internal();

  /// 获取区域配置
  AreaConfig? getAreaConfig(String areaName) {
    // TODO: 实现区域配置获取逻辑
    return null;
  }

  /// 获取参数配置
  ParameterConfig? getParameterConfig(String areaName, String paramName) {
    // TODO: 实现参数配置获取逻辑
    return null;
  }

  /// 获取特征点统计信息
  Map<String, int> getPointsStatistics(dynamic config) {
    if (config is AreaConfig) {
      var stats = <String, int>{};
      config.parameters.forEach((_, param) {
        stats['primary'] = (stats['primary'] ?? 0) + param.primaryPoints.length;
        stats['secondary'] = (stats['secondary'] ?? 0) + param.secondaryPoints.length;
        stats['auxiliary'] = (stats['auxiliary'] ?? 0) + param.auxiliaryPoints.length;
      });
      return stats;
    } else if (config is ParameterConfig) {
      return {
        'primary': config.primaryPoints.length,
        'secondary': config.secondaryPoints.length,
        'auxiliary': config.auxiliaryPoints.length,
      };
    }
    return {};
  }
}
