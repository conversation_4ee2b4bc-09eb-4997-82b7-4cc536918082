import 'dart:ui';
import 'package:flutter/material.dart';
import '../../utils/logger.dart';
import 'deformation_models.dart';

/// 目标点可视化器类，用于显示特征点的目标位置
class TargetPointVisualizer extends ChangeNotifier {
  /// 日志标签
  static const _logTag = 'TargetPointVisualizer';
  
  /// 当前显示的点
  List<TargetPoint> _currentPoints = [];
  
  /// 获取当前点列表
  List<TargetPoint> get currentPoints => _currentPoints;
  
  /// 检查是否有点
  bool get hasPoints => _currentPoints.isNotEmpty;
  
  /// 呼吸动画值
  double _breathingValue = 0.0;
  
  /// 获取呼吸动画值
  double get breathingValue => _breathingValue;
  
  /// 显示呼吸效果点
  /// 
  /// [positions] 点的位置列表
  /// [duration] 动画持续时间
  Future<void> showBreathingPoints(List<Offset> positions, {required Duration duration}) async {
    Logger.flowStart(_logTag, 'showBreathingPoints');
    
    // 清除现有点
    clearPoints();
    
    if (positions.isEmpty) {
      Logger.flowWarning(_logTag, 'showBreathingPoints', '❌ 位置列表为空，无法显示点');
      Logger.flowEnd(_logTag, 'showBreathingPoints');
      return;
    }
    
    Logger.flow(_logTag, 'showBreathingPoints', '✅ 显示${positions.length}个呼吸效果点，持续${duration.inSeconds}秒');
    
    // 创建新点
    _currentPoints = positions.map((p) => TargetPoint(
      position: p,
      color: Colors.red,
      isBreathing: true,
    )).toList();
    
    // 通知监听器
    notifyListeners();
    
    // 模拟呼吸效果
    final startTime = DateTime.now();
    final endTime = startTime.add(duration);
    
    while (DateTime.now().isBefore(endTime)) {
      // 计算呼吸值（0.0-1.0-0.0循环）
      final elapsed = DateTime.now().difference(startTime).inMilliseconds;
      final cycle = (elapsed % 1000) / 1000.0;
      _breathingValue = cycle < 0.5 ? cycle * 2 : (1.0 - cycle) * 2;
      
      // 通知监听器
      notifyListeners();
      
      // 等待一小段时间
      await Future.delayed(Duration(milliseconds: 16)); // 约60fps
    }
    
    Logger.flowEnd(_logTag, 'showBreathingPoints');
  }
  
  /// 显示半透明点
  /// 
  /// [positions] 点的位置列表
  void showTransparentPoints(List<Offset> positions) {
    Logger.flowStart(_logTag, 'showTransparentPoints');
    
    // 清除现有点
    clearPoints();
    
    if (positions.isEmpty) {
      Logger.flowWarning(_logTag, 'showTransparentPoints', '❌ 位置列表为空，无法显示点');
      Logger.flowEnd(_logTag, 'showTransparentPoints');
      return;
    }
    
    Logger.flow(_logTag, 'showTransparentPoints', '✅ 显示${positions.length}个半透明点');
    
    // 创建新点
    _currentPoints = positions.map((p) => TargetPoint(
      position: p,
      color: Colors.white.withOpacity(0.5),
      isBreathing: false,
    )).toList();
    
    // 通知监听器
    notifyListeners();
    
    Logger.flowEnd(_logTag, 'showTransparentPoints');
  }
  
  /// 清除点
  void clearPoints() {
    if (_currentPoints.isNotEmpty) {
      Logger.flow(_logTag, 'clearPoints', '🧹 清除${_currentPoints.length}个点');
      _currentPoints.clear();
      _breathingValue = 0.0;
      notifyListeners();
    }
  }
  
  /// 绘制点
  /// 
  /// [canvas] 画布
  /// [size] 尺寸
  void paint(Canvas canvas, Size size) {
    if (_currentPoints.isEmpty) {
      return;
    }
    
    // 暂时禁用目标点绘制，使用SimpleDeformationPainter中的特征点绘制
    // for (var point in _currentPoints) {
    //   final paint = Paint()
    //     ..color = point.color
    //     ..style = PaintingStyle.fill;
    //   
    //   // 如果是呼吸效果，调整大小
    //   final radius = point.isBreathing
    //       ? 4.0 + 2.0 * _breathingValue
    //       : 4.0;
    //   
    //   canvas.drawCircle(point.position, radius, paint);
    // }
  }
}
