import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../utils/logger.dart';
import 'target_point_visualizer.dart';

/// 目标点绘制器，用于在画布上绘制目标点
class TargetPointPainter extends CustomPainter {
  /// 日志标签
  static const _logTag = 'TargetPointPainter';
  
  /// 目标点可视化器
  final TargetPointVisualizer visualizer;
  
  /// 图片尺寸
  final Size imageSize;
  
  /// 构造函数
  TargetPointPainter({
    required this.visualizer,
    required this.imageSize,
  }) : super(repaint: visualizer) {
    Logger.flow(_logTag, 'constructor', '✅ 创建目标点绘制器: 图片尺寸=${imageSize.width}x${imageSize.height}');
  }
  
  @override
  void paint(Canvas canvas, Size size) {
    Logger.flowStart(_logTag, 'paint');
    
    if (!visualizer.hasPoints) {
      Logger.flow(_logTag, 'paint', '⚠️ 没有点需要绘制');
      Logger.flowEnd(_logTag, 'paint');
      return;
    }
    
    // 计算缩放比例
    final scaleX = size.width / imageSize.width;
    final scaleY = size.height / imageSize.height;
    final scale = math.min(scaleX, scaleY);
    
    Logger.flow(_logTag, 'paint', '📏 缩放比例: $scale (scaleX=$scaleX, scaleY=$scaleY)');
    
    // 保存画布状态
    canvas.save();
    
    // 缩放画布
    canvas.scale(scale, scale);
    
    // 绘制点
    _drawPoints(canvas);
    
    // 恢复画布状态
    canvas.restore();
    
    Logger.flowEnd(_logTag, 'paint');
  }
  
  /// 绘制点
  void _drawPoints(Canvas canvas) {
    Logger.flowStart(_logTag, '_drawPoints');
    
    final points = visualizer.currentPoints;
    Logger.flow(_logTag, '_drawPoints', '🔍 绘制${points.length}个点');
    
    // 暂时禁用目标点绘制，使用SimpleDeformationPainter中的特征点绘制
    // for (var point in points) {
    //   final paint = Paint()
    //     ..color = point.color
    //     ..style = PaintingStyle.fill;
    //   
    //   // 如果是呼吸效果，调整大小
    //   final radius = point.isBreathing
    //       ? 4.0 + 2.0 * visualizer.breathingValue
    //       : 4.0;
    //   
    //   canvas.drawCircle(point.position, radius, paint);
    // }
    
    Logger.flowEnd(_logTag, '_drawPoints');
  }
  
  @override
  bool shouldRepaint(covariant TargetPointPainter oldDelegate) {
    return oldDelegate.visualizer != visualizer || 
           oldDelegate.imageSize != imageSize;
  }
}
