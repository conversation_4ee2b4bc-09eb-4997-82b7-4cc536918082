import 'dart:ui';
import 'package:flutter/material.dart';
import '../models/feature_point.dart';

/// 目标点模型，用于可视化特征点的目标位置
class TargetPoint {
  /// 点的位置
  final Offset position;
  
  /// 点的颜色
  final Color color;
  
  /// 是否使用呼吸效果
  final bool isBreathing;
  
  /// 构造函数
  TargetPoint({
    required this.position,
    required this.color,
    required this.isBreathing,
  });
}

/// 变形结果模型，存储变形前后的特征点和目标位置
class DeformationResult {
  /// 原始特征点列表
  final List<FeaturePoint> originalPoints;
  
  /// 变形后的特征点列表
  final List<FeaturePoint> deformedPoints;
  
  /// 目标位置列表
  final List<Offset> targetPositions;
  
  /// 构造函数
  DeformationResult({
    required this.originalPoints,
    required this.deformedPoints,
    required this.targetPositions,
  });
}
