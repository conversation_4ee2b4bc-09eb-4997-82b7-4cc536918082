import '../../utils/logger.dart';
import 'deformation_models.dart';

/// 变形缓存类，用于缓存变形参数和结果
class DeformationCache {
  /// 日志标签
  static const _logTag = 'DeformationCache';
  
  /// 缓存变形参数和结果
  final Map<String, DeformationResult> _cache = {};
  
  /// 获取缓存大小
  int get size => _cache.length;
  
  /// 检查缓存是否为空
  bool get isEmpty => _cache.isEmpty;
  
  /// 获取缓存的结果
  /// 
  /// [key] 缓存键
  /// 返回缓存的结果，如果不存在则返回null
  DeformationResult? getResult(String key) {
    final result = _cache[key];
    if (result != null) {
      Logger.flow(_logTag, 'getResult', '✅ 缓存命中: $key');
    } else {
      Logger.flow(_logTag, 'getResult', '❌ 缓存未命中: $key');
    }
    return result;
  }
  
  /// 添加结果到缓存
  /// 
  /// [key] 缓存键
  /// [result] 变形结果
  void addResult(String key, DeformationResult result) {
    // 限制缓存大小，避免内存泄漏
    if (_cache.length > 100) {
      // 移除最早添加的项
      final oldestKey = _cache.keys.first;
      _cache.remove(oldestKey);
      Logger.flow(_logTag, 'addResult', '🧹 缓存已满，移除最早项: $oldestKey');
    }
    
    _cache[key] = result;
    Logger.flow(_logTag, 'addResult', '✅ 添加结果到缓存: $key');
  }
  
  /// 清除缓存
  void clear() {
    _cache.clear();
    Logger.flow(_logTag, 'clear', '🧹 缓存已清除');
  }
  
  /// 生成缓存键
  /// 
  /// [areaType] 区域类型
  /// [paramName] 参数名称
  /// [value] 参数值
  /// 返回生成的缓存键
  String generateKey(String areaType, String paramName, double value) {
    // 对值进行舍入，避免浮点数精度问题
    final roundedValue = (value * 100).round() / 100;
    final key = '$areaType:$paramName:$roundedValue';
    Logger.flow(_logTag, 'generateKey', '🔑 生成缓存键: $key');
    return key;
  }
}
