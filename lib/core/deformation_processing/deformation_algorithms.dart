import 'dart:ui';
import 'dart:math' as math;
import '../../utils/logger.dart';
import '../models/feature_point.dart';

/// 变形算法接口
abstract class DeformationAlgorithm {
  /// 日志标签
  String get logTag;
  
  /// 计算目标位置
  /// 
  /// [points] 特征点列表
  /// [indexes] 特征点索引列表
  /// [value] 参数值
  /// 返回目标位置列表
  List<Offset> calculateTargetPositions(List<FeaturePoint> points, List<int> indexes, double value);
  
  /// 应用变形
  /// 
  /// [points] 特征点列表
  /// [indexes] 特征点索引列表
  /// [targetPositions] 目标位置列表
  /// [value] 参数值
  /// 返回变形后的特征点列表
  List<FeaturePoint> applyDeformation(
    List<FeaturePoint> points, 
    List<int> indexes, 
    List<Offset> targetPositions, 
    double value
  );
}

/// 变形算法工厂类
class DeformationAlgorithmFactory {
  /// 日志标签
  static const _logTag = 'DeformationAlgorithmFactory';
  
  /// 创建变形算法
  /// 
  /// [areaType] 区域类型
  /// [paramName] 参数名称
  /// 返回对应的变形算法
  static DeformationAlgorithm createAlgorithm(String areaType, String paramName) {
    Logger.flowStart(_logTag, 'createAlgorithm');
    Logger.flow(_logTag, 'createAlgorithm', '🔍 创建算法: 区域=$areaType, 参数=$paramName');
    
    DeformationAlgorithm algorithm;
    
    switch (areaType) {
      case 'nose':
        algorithm = NoseDeformationAlgorithm(paramName);
        break;
      case 'eye':
      case 'eyes': // 添加对'eyes'区域类型的支持
        Logger.flow(_logTag, 'createAlgorithm', '✅ 识别到眼睛区域类型: $areaType');
        algorithm = EyeDeformationAlgorithm(paramName);
        break;
      case 'face':
        algorithm = FaceDeformationAlgorithm(paramName);
        break;
      default:
        Logger.flowWarning(_logTag, 'createAlgorithm', '⚠️ 未知区域类型: $areaType, 使用默认算法');
        algorithm = DefaultDeformationAlgorithm();
        break;
    }
    
    Logger.flow(_logTag, 'createAlgorithm', '✅ 创建算法成功: ${algorithm.logTag}');
    Logger.flowEnd(_logTag, 'createAlgorithm');
    return algorithm;
  }
}

/// 默认变形算法
class DefaultDeformationAlgorithm implements DeformationAlgorithm {
  @override
  String get logTag => 'DefaultDeformationAlgorithm';
  
  @override
  List<Offset> calculateTargetPositions(List<FeaturePoint> points, List<int> indexes, double value) {
    Logger.flowStart(logTag, 'calculateTargetPositions');
    
    if (points.isEmpty || indexes.isEmpty) {
      Logger.flowWarning(logTag, 'calculateTargetPositions', '❌ 特征点或索引为空，无法计算目标位置');
      Logger.flowEnd(logTag, 'calculateTargetPositions');
      return [];
    }
    
    // 默认算法简单地沿Y轴移动点
    final List<Offset> targetPositions = [];
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 根据参数值计算Y轴偏移
        final offset = value * 10.0; // 简单线性缩放
        targetPositions.add(Offset(point.x, point.y + offset));
      }
    }
    
    Logger.flow(logTag, 'calculateTargetPositions', '✅ 计算了${targetPositions.length}个目标位置');
    Logger.flowEnd(logTag, 'calculateTargetPositions');
    return targetPositions;
  }
  
  @override
  List<FeaturePoint> applyDeformation(
    List<FeaturePoint> points, 
    List<int> indexes, 
    List<Offset> targetPositions, 
    double value
  ) {
    Logger.flowStart(logTag, 'applyDeformation');
    
    if (points.isEmpty || indexes.isEmpty || targetPositions.isEmpty) {
      Logger.flowWarning(logTag, 'applyDeformation', '❌ 特征点、索引或目标位置为空，无法应用变形');
      Logger.flowEnd(logTag, 'applyDeformation');
      return List.from(points);
    }
    
    // 创建新的特征点列表
    final List<FeaturePoint> deformedPoints = List.from(points);
    
    // 根据目标位置更新特征点
    for (int i = 0; i < indexes.length && i < targetPositions.length; i++) {
      final index = indexes[i];
      if (index >= 0 && index < deformedPoints.length) {
        final targetPosition = targetPositions[i];
        final oldPoint = deformedPoints[index];
        deformedPoints[index] = FeaturePoint(
          id: oldPoint.id,
          index: oldPoint.index,
          name: oldPoint.name,
          x: targetPosition.dx,
          y: targetPosition.dy,
          z: oldPoint.z,
          visibility: oldPoint.visibility,
          confidence: oldPoint.confidence,
          isPrimary: oldPoint.isPrimary,
          opacity: oldPoint.opacity,
          size: oldPoint.size,
          color: oldPoint.color,
        );
      }
    }
    
    Logger.flow(logTag, 'applyDeformation', '✅ 应用变形到${indexes.length}个特征点');
    Logger.flowEnd(logTag, 'applyDeformation');
    return deformedPoints;
  }
}

/// 鼻子变形算法
class NoseDeformationAlgorithm implements DeformationAlgorithm {
  /// 参数名称
  final String paramName;
  
  /// 构造函数
  NoseDeformationAlgorithm(this.paramName);
  
  @override
  String get logTag => 'NoseDeformationAlgorithm';
  
  @override
  List<Offset> calculateTargetPositions(List<FeaturePoint> points, List<int> indexes, double value) {
    Logger.flowStart(logTag, 'calculateTargetPositions');
    Logger.flow(logTag, 'calculateTargetPositions', '🔍 [开始] 参数: $paramName, 值: $value');
    
    // 记录特征点和索引信息
    Logger.flow(logTag, 'calculateTargetPositions', '📊 特征点总数: ${points.length}, 索引总数: ${indexes.length}');
    
    // 输出前几个索引作为样本
    final sampleSize = math.min(5, indexes.length);
    for (int i = 0; i < sampleSize; i++) {
      Logger.flow(logTag, 'calculateTargetPositions', '📊 索引样本[$i]: ${indexes[i]}');
    }
    
    // 检查特征点和索引是否为空
    if (points.isEmpty || indexes.isEmpty) {
      Logger.flowWarning(logTag, 'calculateTargetPositions', '⚠️ 特征点或索引为空，无法计算目标位置');
      Logger.flowEnd(logTag, 'calculateTargetPositions');
      return [];
    }
    
    // 检查索引是否有效
    bool allIndexesValid = true;
    for (final index in indexes) {
      if (index < 0 || index >= points.length) {
        Logger.flowWarning(logTag, 'calculateTargetPositions', '⚠️ 无效的索引: $index, 超出范围[0, ${points.length - 1}]');
        allIndexesValid = false;
      }
    }
    
    if (!allIndexesValid) {
      Logger.flowWarning(logTag, 'calculateTargetPositions', '⚠️ 存在无效索引，可能导致变形效果不正确');
    }
    
    final List<Offset> targetPositions = [];
    
    switch (paramName) {
      case 'bridge_height':
        // 鼻梁高度变形
        _calculateBridgeHeightPositions(points, indexes, value, targetPositions);
        break;
      case 'tip_height':
        // 鼻尖高度变形
        _calculateTipHeightPositions(points, indexes, value, targetPositions);
        break;
      case 'width':
        // 鼻子宽度变形
        _calculateWidthPositions(points, indexes, value, targetPositions);
        break;
      case 'nostril_width':
        // 鼻翼宽度变形
        _calculateNostrilWidthPositions(points, indexes, value, targetPositions);
        break;
      case 'nose_base_height':
        // 鼻基抬高变形 - 现在有专门的变形策略实现，此处仅作为后备
        Logger.flow(logTag, 'calculateTargetPositions', '✅ 鼻基抬高参数现在使用专门的变形策略实现');
        break;
      default:
        Logger.flowWarning(logTag, 'calculateTargetPositions', '⚠️ 未知参数名称: $paramName, 使用默认计算');
        // 默认计算
        for (final index in indexes) {
          if (index >= 0 && index < points.length) {
            final point = points[index];
            // 根据参数值计算Y轴偏移
            final offset = value * 5.0; // 简单线性缩放
            targetPositions.add(Offset(point.x, point.y + offset));
          }
        }
        break;
    }
    
    Logger.flow(logTag, 'calculateTargetPositions', '✅ 计算了${targetPositions.length}个目标位置');
    Logger.flowEnd(logTag, 'calculateTargetPositions');
    return targetPositions;
  }
  
  /// 计算鼻梁高度变形的目标位置
  void _calculateBridgeHeightPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    // 找到鼻梁中心点
    Offset? bridgeCenter;
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        if (bridgeCenter == null) {
          bridgeCenter = Offset(point.x, point.y);
        } else {
          bridgeCenter = Offset(
            (bridgeCenter.dx + point.x) / 2,
            (bridgeCenter.dy + point.y) / 2,
          );
        }
      }
    }
    
    if (bridgeCenter == null) {
      Logger.flowWarning(logTag, '_calculateBridgeHeightPositions', '❌ 无法找到鼻梁中心点');
      return;
    }
    
    // 根据参数值计算Y轴偏移
    final maxOffset = 10.0;
    final offset = value * maxOffset;
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 计算点到中心的距离比例
        final distanceRatio = 1.0 - (point.y - bridgeCenter.dy).abs() / 50.0;
        final pointOffset = offset * distanceRatio.clamp(0.0, 1.0);
        targetPositions.add(Offset(point.x, point.y - pointOffset));
      }
    }
  }
  
  /// 计算鼻尖高度变形的目标位置
  void _calculateTipHeightPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    // 找到鼻尖中心点
    Offset? tipCenter;
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        if (tipCenter == null) {
          tipCenter = Offset(point.x, point.y);
        } else {
          tipCenter = Offset(
            (tipCenter.dx + point.x) / 2,
            (tipCenter.dy + point.y) / 2,
          );
        }
      }
    }
    
    if (tipCenter == null) {
      Logger.flowWarning(logTag, '_calculateTipHeightPositions', '❌ 无法找到鼻尖中心点');
      return;
    }
    
    // 根据参数值计算Y轴偏移
    final maxOffset = 15.0;
    final offset = value * maxOffset;
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 计算点到中心的距离比例
        // 计算点到中心的偏移量
        final pointOffset = Offset(point.x - tipCenter.dx, point.y - tipCenter.dy);
        final distance = pointOffset.distance;
        final distanceRatio = 1.0 - distance / 30.0;
        final offsetValue = offset * distanceRatio.clamp(0.0, 1.0);
        targetPositions.add(Offset(point.x, point.y - offsetValue));
      }
    }
  }
  
  /// 计算鼻子宽度变形的目标位置
  void _calculateWidthPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    // 找到鼻子中心线
    double? centerX;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        if (centerX == null) {
          centerX = point.x;
        } else {
          centerX = (centerX + point.x) / 2;
        }
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
      }
    }
    
    if (centerX == null) {
      Logger.flowWarning(logTag, '_calculateWidthPositions', '❌ 无法找到鼻子中心线');
      return;
    }
    
    // 根据参数值计算X轴偏移
    final maxOffset = 8.0;
    final offset = value * maxOffset;
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 计算点到中心线的距离
        final distanceFromCenter = point.x - centerX; // 正值，表示向中心的距离
        
        // 根据参数值计算X轴偏移
        final newX = centerX + distanceFromCenter * (1.0 + offset * 0.1);
        targetPositions.add(Offset(newX, point.y));
      }
    }
  }
  
  /// 计算鼻翼宽度变形的目标位置
  void _calculateNostrilWidthPositions(
    List<FeaturePoint> points, 
    List<int> featureIds, 
    double value, 
    List<Offset> targetPositions
  ) {
    Logger.flowStart(logTag, '_calculateNostrilWidthPositions');
    Logger.flow(logTag, '_calculateNostrilWidthPositions', '🔍 计算鼻翼宽度变形的目标位置，参数值: $value');
    
    // 首先检查特征点和特征ID是否有效
    if (points.isEmpty || featureIds.isEmpty) {
      Logger.flowError(logTag, '_calculateNostrilWidthPositions', '❌ 特征点列表或特征ID列表为空，无法计算目标位置');
      Logger.flowEnd(logTag, '_calculateNostrilWidthPositions');
      return;
    }
    
    // 输出特征点信息以便调试
    Logger.flow(logTag, '_calculateNostrilWidthPositions', '📊 特征点数量: ${points.length}, 特征ID数量: ${featureIds.length}');
    for (final id in featureIds) {
      Logger.flow(logTag, '_calculateNostrilWidthPositions', '📊 特征ID: $id');
    }
    
    // 收集有效的特征点
    List<FeaturePoint> validPoints = [];
    Map<int, int> idToIndexMap = {}; // 用于映射特征ID到points列表中的索引
    
    // 创建特征ID到索引的映射
    for (int i = 0; i < points.length; i++) {
      idToIndexMap[points[i].index] = i;
    }
    
    // 使用映射快速查找特征点
    for (final id in featureIds) {
      if (idToIndexMap.containsKey(id)) {
        final pointIndex = idToIndexMap[id]!;
        validPoints.add(points[pointIndex]);
        Logger.flow(logTag, '_calculateNostrilWidthPositions', '✅ 找到特征点: ID=$id, 坐标=(${points[pointIndex].x}, ${points[pointIndex].y})');
      } else {
        Logger.flowWarning(logTag, '_calculateNostrilWidthPositions', '⚠️ 无法找到特征ID: $id 对应的特征点');
      }
    }
    
    if (validPoints.isEmpty) {
      Logger.flowError(logTag, '_calculateNostrilWidthPositions', '❌ 没有有效的特征点');
      Logger.flowEnd(logTag, '_calculateNostrilWidthPositions');
      return;
    }
    
    Logger.flow(logTag, '_calculateNostrilWidthPositions', '✅ 成功找到 ${validPoints.length} 个有效特征点');
    
    // 查找鼻子中心点（使用鼻尖点作为参考）
    Offset noseCenter = Offset.zero;
    bool hasNoseCenter = false;
    
    // 尝试查找鼻尖中心点（索引为4）
    for (final point in points) {
      if (point.index == 4) { // 鼻尖
        noseCenter = Offset(point.x, point.y);
        hasNoseCenter = true;
        Logger.flow(logTag, '_calculateNostrilWidthPositions', '📊 找到鼻尖中心点: (${point.x}, ${point.y})');
        break;
      }
    }
    
    // 如果没有找到鼻尖，则尝试使用鼻翼点的中心点
    if (!hasNoseCenter) {
      // 尝试查找鼻翼点（索引为115和344）
      FeaturePoint? leftNostril;
      FeaturePoint? rightNostril;
      
      for (final point in points) {
        if (point.index == 115) { // 左鼻翼
          leftNostril = point;
        } else if (point.index == 344) { // 右鼻翼
          rightNostril = point;
        }
        
        // 如果找到了两个鼻翼点，就可以计算中心点
        if (leftNostril != null && rightNostril != null) {
          noseCenter = Offset(
            (leftNostril.x + rightNostril.x) / 2,
            (leftNostril.y + rightNostril.y) / 2
          );
          hasNoseCenter = true;
          Logger.flow(logTag, '_calculateNostrilWidthPositions', '📊 使用鼻翼点计算的中心点: (${noseCenter.dx}, ${noseCenter.dy})');
          break;
        }
      }
    }
    
    // 如果仍然没有找到中心点，则使用所有有效特征点的平均位置
    if (!hasNoseCenter) {
      double sumX = 0;
      double sumY = 0;
      for (final point in validPoints) {
        sumX += point.x;
        sumY += point.y;
      }
      noseCenter = Offset(sumX / validPoints.length, sumY / validPoints.length);
      Logger.flow(logTag, '_calculateNostrilWidthPositions', '📊 使用所有点计算的中心点: (${noseCenter.dx}, ${noseCenter.dy})');
    }
    
    // 计算鼻翼区域的半径 - 使用鼻翼点之间的距离
    double nostrilRadius = 0;
    FeaturePoint? leftNostril;
    FeaturePoint? rightNostril;
    
    for (final point in points) {
      if (point.index == 115) { // 左鼻翼
        leftNostril = point;
      } else if (point.index == 344) { // 右鼻翼
        rightNostril = point;
      }
      
      if (leftNostril != null && rightNostril != null) {
        // 计算两个鼻翼点之间的距离
        final distance = Offset(rightNostril.x - leftNostril.x, rightNostril.y - leftNostril.y).distance;
        
        // 使用这个距离的1.2倍作为影响半径，确保覆盖整个鼻翼区域但不会过大
        nostrilRadius = distance * 0.6;
        Logger.flow(logTag, '_calculateNostrilWidthPositions', '📊 计算的鼻翼区域半径: $nostrilRadius');
        break;
      }
    }
    
    // 如果无法计算鼻翼半径，则使用一个合理的默认值（约为面部宽度的1/6）
    if (nostrilRadius <= 0) {
      // 尝试计算面部宽度
      double faceWidth = 0;
      FeaturePoint? leftCheek;
      FeaturePoint? rightCheek;
      
      for (final point in points) {
        if (point.index == 127) { // 左脸颊
          leftCheek = point;
        } else if (point.index == 356) { // 右脸颊
          rightCheek = point;
        }
        
        if (leftCheek != null && rightCheek != null) {
          faceWidth = (rightCheek.x - leftCheek.x).abs();
          break;
        }
      }
      
      if (faceWidth > 0) {
        nostrilRadius = faceWidth / 6;
      } else {
        // 如果仍然无法计算，使用一个固定值
        nostrilRadius = 30.0;
      }
      
      Logger.flow(logTag, '_calculateNostrilWidthPositions', '📊 使用默认鼻翼区域半径: $nostrilRadius');
    }
    
    // 将特征点分为左右两组，基于鼻子中心点
    List<FeaturePoint> leftPoints = [];
    List<FeaturePoint> rightPoints = [];
    
    for (final point in validPoints) {
      if (point.x < noseCenter.dx) {
        leftPoints.add(point);
      } else if (point.x > noseCenter.dx) {
        rightPoints.add(point);
      }
    }
    
    Logger.flow(logTag, '_calculateNostrilWidthPositions', '📊 分组结果: 左侧${leftPoints.length}个点, 右侧${rightPoints.length}个点');
    
    // 如果左右两侧都没有点，则无法计算变形
    if (leftPoints.isEmpty && rightPoints.isEmpty) {
      Logger.flowError(logTag, '_calculateNostrilWidthPositions', '❌ 左右两侧都没有有效特征点，无法计算变形');
      Logger.flowEnd(logTag, '_calculateNostrilWidthPositions');
      return;
    }
    
    // 计算变形系数，确保变形效果明显但不过度
    final deformationFactor = 0.3; // 减小变形系数，使变形更加自然
    Logger.flow(logTag, '_calculateNostrilWidthPositions', '🔍 使用变形系数: $deformationFactor, 参数值: $value');
    
    // 计算最大偏移距离，用于调试
    double maxOffset = 0;
    
    // 处理左侧点 - 相对于鼻子中心点移动，并添加基于距离的衰减
    for (final point in leftPoints) {
      // 计算点到鼻子中心的距离
      final distance = Offset(point.x - noseCenter.dx, point.y - noseCenter.dy).distance;
      
      // 基于距离的衰减因子 (0-1)，距离越远衰减越大
      final distanceFactor = math.max(0, 1 - distance / nostrilRadius);
      
      // 如果点在影响半径之外，则不进行变形
      if (distanceFactor <= 0) {
        // 保持原位置不变
        targetPositions.add(Offset(point.x, point.y));
        continue;
      }
      
      // 计算点到鼻子中心的水平距离
      final dx = noseCenter.dx - point.x; // 正值，表示向中心的距离
      
      // 根据参数值和距离衰减计算水平偏移
      double effectiveFactor = deformationFactor * distanceFactor * distanceFactor; // 使用平方衰减
      
      // 对于负值（减小鼻孔宽度），可以使用稍大的系数
      if (value < 0) {
        effectiveFactor *= 1.1;
      }
      
      final offset = value * dx * effectiveFactor;
      maxOffset = math.max(maxOffset, offset.abs());
      
      // 计算新的水平位置
      final newX = point.x - offset;
      
      // 添加目标位置
      targetPositions.add(Offset(newX, point.y));
      Logger.flow(logTag, '_calculateNostrilWidthPositions', '左侧点 ${point.index} 从 ${point.x} 移动到 $newX, 偏移量: ${-offset}, 衰减因子: $distanceFactor');
    }
    
    // 处理右侧点 - 相对于鼻子中心点移动，并添加基于距离的衰减
    for (final point in rightPoints) {
      // 计算点到鼻子中心的距离
      final distance = Offset(point.x - noseCenter.dx, point.y - noseCenter.dy).distance;
      
      // 基于距离的衰减因子 (0-1)，距离越远衰减越大
      final distanceFactor = math.max(0, 1 - distance / nostrilRadius);
      
      // 如果点在影响半径之外，则不进行变形
      if (distanceFactor <= 0) {
        // 保持原位置不变
        targetPositions.add(Offset(point.x, point.y));
        continue;
      }
      
      // 计算点到鼻子中心的水平距离
      final dx = point.x - noseCenter.dx; // 正值，表示向外的距离
      
      // 根据参数值和距离衰减计算水平偏移
      double effectiveFactor = deformationFactor * distanceFactor * distanceFactor; // 使用平方衰减
      
      // 对于负值（减小鼻孔宽度），可以使用稍大的系数
      if (value < 0) {
        effectiveFactor *= 1.1;
      }
      
      final offset = value * dx * effectiveFactor;
      maxOffset = math.max(maxOffset, offset.abs());
      
      // 计算新的水平位置
      final newX = point.x + offset;
      
      // 添加目标位置
      targetPositions.add(Offset(newX, point.y));
      Logger.flow(logTag, '_calculateNostrilWidthPositions', '右侧点 ${point.index} 从 ${point.x} 移动到 $newX, 偏移量: ${offset}, 衰减因子: $distanceFactor');
    }
    
    // 输出最大偏移距离，用于调试
    Logger.flow(logTag, '_calculateNostrilWidthPositions', '🔍 最大偏移距离: $maxOffset');
    
    // 检查目标位置数量是否与有效特征点数量一致
    if (targetPositions.length != leftPoints.length + rightPoints.length) {
      Logger.flowWarning(logTag, '_calculateNostrilWidthPositions', '目标位置数量(${targetPositions.length})与有效特征点数量(${leftPoints.length + rightPoints.length})不一致');
    }
    
    Logger.flow(logTag, '_calculateNostrilWidthPositions', '✅ 计算了${targetPositions.length}个目标位置');
    Logger.flowEnd(logTag, '_calculateNostrilWidthPositions');
  }
  
  @override
  List<FeaturePoint> applyDeformation(
    List<FeaturePoint> points, 
    List<int> indexes, 
    List<Offset> targetPositions, 
    double value
  ) {
    Logger.flowStart(logTag, 'applyDeformation');
    
    if (points.isEmpty || indexes.isEmpty || targetPositions.isEmpty) {
      Logger.flowWarning(logTag, 'applyDeformation', '❌ 特征点、索引或目标位置为空，无法应用变形');
      Logger.flowEnd(logTag, 'applyDeformation');
      return List.from(points);
    }
    
    // 创建新的特征点列表
    final List<FeaturePoint> deformedPoints = List.from(points);
    
    // 根据目标位置更新特征点
    for (int i = 0; i < indexes.length && i < targetPositions.length; i++) {
      final index = indexes[i];
      if (index >= 0 && index < deformedPoints.length) {
        final targetPosition = targetPositions[i];
        final oldPoint = deformedPoints[index];
        deformedPoints[index] = FeaturePoint(
          id: oldPoint.id,
          index: oldPoint.index,
          name: oldPoint.name,
          x: targetPosition.dx,
          y: targetPosition.dy,
          z: oldPoint.z,
          visibility: oldPoint.visibility,
          confidence: oldPoint.confidence,
          isPrimary: oldPoint.isPrimary,
          opacity: oldPoint.opacity,
          size: oldPoint.size,
          color: oldPoint.color,
        );
      }
    }
    
    Logger.flow(logTag, 'applyDeformation', '✅ 应用变形到${indexes.length}个特征点');
    Logger.flowEnd(logTag, 'applyDeformation');
    return deformedPoints;
  }
}

/// 眼睛变形算法
class EyeDeformationAlgorithm implements DeformationAlgorithm {
  /// 参数名称
  final String paramName;
  
  /// 构造函数
  EyeDeformationAlgorithm(this.paramName);
  
  @override
  String get logTag => 'EyeDeformationAlgorithm';
  
  @override
  List<Offset> calculateTargetPositions(List<FeaturePoint> points, List<int> indexes, double value) {
    Logger.flowStart(logTag, 'calculateTargetPositions');
    
    if (points.isEmpty || indexes.isEmpty) {
      Logger.flowWarning(logTag, 'calculateTargetPositions', '❌ 特征点或索引为空，无法计算目标位置');
      Logger.flowEnd(logTag, 'calculateTargetPositions');
      return [];
    }
    
    final List<Offset> targetPositions = [];
    
    // 根据参数名称计算目标位置
    switch (paramName) {
      case 'size':
        // 眼睛大小变形
        _calculateSizePositions(points, indexes, value, targetPositions);
        break;
      case 'height':
        // 眼睛高度变形
        _calculateHeightPositions(points, indexes, value, targetPositions);
        break;
      case 'eye_bag_removal':
        // 眼袋去除变形
        _calculateEyeBagRemovalPositions(points, indexes, value, targetPositions);
        break;
      case 'double_fold':
        // 双眼皮变形
        _calculateDoubleFoldPositions(points, indexes, value, targetPositions);
        break;
      case 'canthal_tilt':
        // 眼角上扬变形
        _calculateCanthalTiltPositions(points, indexes, value, targetPositions);
        break;
      case 'outer_corner_lift':
        // 外眼角提升变形
        _calculateOuterCornerLiftPositions(points, indexes, value, targetPositions);
        break;
      default:
        Logger.flowWarning(logTag, 'calculateTargetPositions', '⚠️ 未知参数名称: $paramName, 使用默认计算');
        // 默认计算
        for (final index in indexes) {
          if (index >= 0 && index < points.length) {
            final point = points[index];
            targetPositions.add(Offset(point.x, point.y));
          }
        }
        break;
    }
    
    Logger.flow(logTag, 'calculateTargetPositions', '✅ 计算了${targetPositions.length}个目标位置');
    Logger.flowEnd(logTag, 'calculateTargetPositions');
    return targetPositions;
  }
  
  /// 计算眼睛大小变形的目标位置
  void _calculateSizePositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    // 找到眼睛中心点
    Offset? eyeCenter;
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        minX = math.min(minX, point.x);
        maxX = math.max(maxX, point.x);
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
      }
    }
    
    eyeCenter = Offset((minX + maxX) / 2, (minY + maxY) / 2);
    
    // 根据参数值计算缩放因子
    final scaleFactor = 1.0 + value * 0.2; // 最大放大/缩小20%
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 计算点相对于中心的偏移
        final offsetX = point.x - eyeCenter.dx;
        final offsetY = point.y - eyeCenter.dy;
        // 应用缩放
        final newX = eyeCenter.dx + offsetX * scaleFactor;
        final newY = eyeCenter.dy + offsetY * scaleFactor;
        targetPositions.add(Offset(newX, newY));
      }
    }
  }
  
  /// 计算眼睛高度变形的目标位置
  void _calculateHeightPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    // 找到眼睛中心点和上下边缘
    Offset? eyeCenter;
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        minX = math.min(minX, point.x);
        maxX = math.max(maxX, point.x);
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
      }
    }
    
    eyeCenter = Offset((minX + maxX) / 2, (minY + maxY) / 2);
    
    // 根据参数值计算Y轴缩放因子
    final scaleFactor = 1.0 + value * 0.3; // 最大放大/缩小30%
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 计算点相对于中心的Y轴偏移
        final offsetY = point.y - eyeCenter.dy;
        // 应用Y轴缩放
        final newY = eyeCenter.dy + offsetY * scaleFactor;
        targetPositions.add(Offset(point.x, newY));
      }
    }
  }
  
  /// 计算眼袋去除变形的目标位置
  void _calculateEyeBagRemovalPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    Logger.flow(logTag, '_calculateEyeBagRemovalPositions', '🔍 计算眼袋去除变形目标位置，参数值: $value');
    
    // 找到眼睛下方区域的中心点
    Offset? eyeBagCenter;
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        minX = math.min(minX, point.x);
        maxX = math.max(maxX, point.x);
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
      }
    }
    
    eyeBagCenter = Offset((minX + maxX) / 2, (minY + maxY) / 2);
    
    // 眼袋去除主要是向上移动下眼睑区域的点
    // 根据参数值计算Y轴偏移量
    final yOffset = value * 5.0; // 最大上移5个像素单位
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 计算点到中心的距离
        final distance = (point.y - eyeBagCenter.dy).abs();
        // 距离越近，移动越明显
        final factor = 1.0 - math.min(1.0, distance / 20.0);
        // 应用Y轴偏移，向上移动（减小Y值）
        final newY = point.y - yOffset * factor;
        
        Logger.flow(logTag, '_calculateEyeBagRemovalPositions', '📊 点 $index: 原始Y=${point.y}, 新Y=$newY, 偏移=${yOffset * factor}');
        
        targetPositions.add(Offset(point.x, newY));
      }
    }
    
    Logger.flow(logTag, '_calculateEyeBagRemovalPositions', '✅ 计算完成，目标位置数量: ${targetPositions.length}');
  }
  
  /// 计算双眼皮变形的目标位置
  void _calculateDoubleFoldPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    Logger.flow(logTag, '_calculateDoubleFoldPositions', '🔍 计算双眼皮变形目标位置，参数值: $value');
    
    // 找到上眼睑区域
    Offset? eyeCenter;
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        minX = math.min(minX, point.x);
        maxX = math.max(maxX, point.x);
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
      }
    }
    
    eyeCenter = Offset((minX + maxX) / 2, (minY + maxY) / 2);
    
    // 双眼皮效果主要是上眼睑区域的下移
    // 根据参数值计算Y轴偏移量
    final yOffset = value * 3.0; // 最大下移3个像素单位
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 只对上眼睑区域的点进行处理（Y坐标小于中心点的点）
        if (point.y < eyeCenter.dy) {
          // 计算点到中心的垂直距离
          final distance = (eyeCenter.dy - point.y);
          // 距离越远，移动越明显
          final factor = math.min(1.0, distance / 15.0);
          // 应用Y轴偏移，向下移动（增加Y值）
          final newY = point.y + yOffset * factor;
          
          Logger.flow(logTag, '_calculateDoubleFoldPositions', '📊 点 $index: 原始Y=${point.y}, 新Y=$newY, 偏移=${yOffset * factor}');
          
          targetPositions.add(Offset(point.x, newY));
        } else {
          // 其他点保持不变
          targetPositions.add(Offset(point.x, point.y));
        }
      }
    }
    
    Logger.flow(logTag, '_calculateDoubleFoldPositions', '✅ 计算完成，目标位置数量: ${targetPositions.length}');
  }
  
  /// 计算眼角上扬变形的目标位置
  void _calculateCanthalTiltPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    Logger.flow(logTag, '_calculateCanthalTiltPositions', '🔍 计算眼角上扬变形目标位置，参数值: $value');
    
    // 找到眼睛区域的中心和边界
    Offset? eyeCenter;
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        minX = math.min(minX, point.x);
        maxX = math.max(maxX, point.x);
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
      }
    }
    
    eyeCenter = Offset((minX + maxX) / 2, (minY + maxY) / 2);
    final eyeWidth = maxX - minX;
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        
        // 计算点到眼睛中心的水平距离比例（-1到1）
        // -1表示最左侧，0表示中心，1表示最右侧
        final horizontalRatio = (point.x - eyeCenter.dx) / (eyeWidth / 2);
        
        // 眼角上扬效果：外侧点向上移动，内侧点保持不变或轻微向下移动
        // 移动量与水平距离成正比
        final yOffset = value * 5.0 * horizontalRatio; // 最大上移/下移5个像素单位
        
        // 应用Y轴偏移，负值表示向上移动
        final newY = point.y - yOffset;
        
        Logger.flow(logTag, '_calculateCanthalTiltPositions', '📊 点 $index: 原始Y=${point.y}, 新Y=$newY, 偏移=${-yOffset}');
        
        targetPositions.add(Offset(point.x, newY));
      }
    }
    
    Logger.flow(logTag, '_calculateCanthalTiltPositions', '✅ 计算完成，目标位置数量: ${targetPositions.length}');
  }
  
  /// 计算外眼角提升变形的目标位置
  void _calculateOuterCornerLiftPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    Logger.flow(logTag, '_calculateOuterCornerLiftPositions', '🔍 计算外眼角提升变形目标位置，参数值: $value');
    
    // 找到眼睛区域的中心和边界
    Offset? eyeCenter;
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    // 找到最外侧的点（左眼最左侧，右眼最右侧）
    int? outerCornerIndex;
    double outerX = 0;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        minX = math.min(minX, point.x);
        maxX = math.max(maxX, point.x);
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
        
        // 假设X坐标最小的是左眼最左侧，最大的是右眼最右侧
        if (point.x == minX || point.x == maxX) {
          outerCornerIndex = index;
          outerX = point.x;
        }
      }
    }
    
    eyeCenter = Offset((minX + maxX) / 2, (minY + maxY) / 2);
    final eyeWidth = maxX - minX;
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        
        // 计算点到眼睛中心的水平距离比例（0到1）
        // 0表示中心，1表示最外侧
        final horizontalDistance = (point.x - eyeCenter.dx).abs();
        final horizontalRatio = horizontalDistance / (eyeWidth / 2);
        
        // 外眼角提升效果：外侧点向上和外侧移动，其他点根据距离逐渐减弱效果
        // 移动量与到外侧的距离成正比
        final yOffset = value * 4.0 * horizontalRatio; // 最大上移4个像素单位
        
        // 外侧点还有轻微的水平移动（向外）
        final xOffset = value * 2.0 * horizontalRatio * (point.x > eyeCenter.dx ? 1 : -1);
        
        // 应用偏移
        final newX = point.x + xOffset;
        final newY = point.y - yOffset; // 负值表示向上移动
        
        Logger.flow(logTag, '_calculateOuterCornerLiftPositions', '📊 点 $index: 原始坐标=(${point.x}, ${point.y}), 新坐标=($newX, $newY)');
        
        targetPositions.add(Offset(newX, newY));
      }
    }
    
    Logger.flow(logTag, '_calculateOuterCornerLiftPositions', '✅ 计算完成，目标位置数量: ${targetPositions.length}');
  }
  
  @override
  List<FeaturePoint> applyDeformation(
    List<FeaturePoint> points, 
    List<int> indexes, 
    List<Offset> targetPositions, 
    double value
  ) {
    Logger.flowStart(logTag, 'applyDeformation');
    
    if (points.isEmpty || indexes.isEmpty || targetPositions.isEmpty) {
      Logger.flowWarning(logTag, 'applyDeformation', '❌ 特征点、索引或目标位置为空，无法应用变形');
      Logger.flowEnd(logTag, 'applyDeformation');
      return List.from(points);
    }
    
    // 创建新的特征点列表
    final List<FeaturePoint> deformedPoints = List.from(points);
    
    // 根据目标位置更新特征点
    for (int i = 0; i < indexes.length && i < targetPositions.length; i++) {
      final index = indexes[i];
      if (index >= 0 && index < deformedPoints.length) {
        final targetPosition = targetPositions[i];
        final oldPoint = deformedPoints[index];
        deformedPoints[index] = FeaturePoint(
          id: oldPoint.id,
          index: oldPoint.index,
          name: oldPoint.name,
          x: targetPosition.dx,
          y: targetPosition.dy,
          z: oldPoint.z,
          visibility: oldPoint.visibility,
          confidence: oldPoint.confidence,
          isPrimary: oldPoint.isPrimary,
          opacity: oldPoint.opacity,
          size: oldPoint.size,
          color: oldPoint.color,
        );
      }
    }
    
    Logger.flow(logTag, 'applyDeformation', '✅ 应用变形到${indexes.length}个特征点');
    Logger.flowEnd(logTag, 'applyDeformation');
    return deformedPoints;
  }
}

/// 脸部变形算法
class FaceDeformationAlgorithm implements DeformationAlgorithm {
  /// 参数名称
  final String paramName;
  
  /// 构造函数
  FaceDeformationAlgorithm(this.paramName);
  
  @override
  String get logTag => 'FaceDeformationAlgorithm';
  
  @override
  List<Offset> calculateTargetPositions(List<FeaturePoint> points, List<int> indexes, double value) {
    Logger.flowStart(logTag, 'calculateTargetPositions');
    
    if (points.isEmpty || indexes.isEmpty) {
      Logger.flowWarning(logTag, 'calculateTargetPositions', '❌ 特征点或索引为空，无法计算目标位置');
      Logger.flowEnd(logTag, 'calculateTargetPositions');
      return [];
    }
    
    final List<Offset> targetPositions = [];
    
    // 根据参数名称计算目标位置
    switch (paramName) {
      case 'width':
        // 脸部宽度变形
        _calculateWidthPositions(points, indexes, value, targetPositions);
        break;
      case 'length':
        // 脸部长度变形
        _calculateLengthPositions(points, indexes, value, targetPositions);
        break;
      default:
        Logger.flowWarning(logTag, 'calculateTargetPositions', '⚠️ 未知参数名称: $paramName, 使用默认计算');
        // 默认计算
        for (final index in indexes) {
          if (index >= 0 && index < points.length) {
            final point = points[index];
            targetPositions.add(Offset(point.x, point.y));
          }
        }
        break;
    }
    
    Logger.flow(logTag, 'calculateTargetPositions', '✅ 计算了${targetPositions.length}个目标位置');
    Logger.flowEnd(logTag, 'calculateTargetPositions');
    return targetPositions;
  }
  
  /// 计算脸部宽度变形的目标位置
  void _calculateWidthPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    // 找到脸部中心线
    double? centerX;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        if (centerX == null) {
          centerX = point.x;
        } else {
          centerX = (centerX + point.x) / 2;
        }
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
      }
    }
    
    if (centerX == null) {
      Logger.flowWarning(logTag, '_calculateWidthPositions', '❌ 无法找到脸部中心线');
      return;
    }
    
    // 根据参数值计算X轴缩放因子
    final scaleFactor = 1.0 + value * 0.15; // 最大放大/缩小15%
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 计算点到中心线的距离
        final distanceFromCenter = point.x - centerX;
        // 计算新的X坐标
        final newX = centerX + distanceFromCenter * scaleFactor;
        targetPositions.add(Offset(newX, point.y));
      }
    }
  }
  
  /// 计算脸部长度变形的目标位置
  void _calculateLengthPositions(
    List<FeaturePoint> points, 
    List<int> indexes, 
    double value, 
    List<Offset> targetPositions
  ) {
    // 找到脸部中心点和上下边缘
    double? centerY;
    double minY = double.infinity;
    double maxY = double.negativeInfinity;
    
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        minY = math.min(minY, point.y);
        maxY = math.max(maxY, point.y);
      }
    }
    
    centerY = (minY + maxY) / 2;
    
    // 根据参数值计算Y轴缩放因子
    final scaleFactor = 1.0 + value * 0.2; // 最大放大/缩小20%
    
    // 计算每个点的目标位置
    for (final index in indexes) {
      if (index >= 0 && index < points.length) {
        final point = points[index];
        // 计算点相对于中心的Y轴偏移
        final offsetY = point.y - centerY;
        // 应用Y轴缩放
        final newY = centerY + offsetY * scaleFactor;
        targetPositions.add(Offset(point.x, newY));
      }
    }
  }
  
  @override
  List<FeaturePoint> applyDeformation(
    List<FeaturePoint> points, 
    List<int> indexes, 
    List<Offset> targetPositions, 
    double value
  ) {
    Logger.flowStart(logTag, 'applyDeformation');
    
    if (points.isEmpty || indexes.isEmpty || targetPositions.isEmpty) {
      Logger.flowWarning(logTag, 'applyDeformation', '❌ 特征点、索引或目标位置为空，无法应用变形');
      Logger.flowEnd(logTag, 'applyDeformation');
      return List.from(points);
    }
    
    // 创建新的特征点列表
    final List<FeaturePoint> deformedPoints = List.from(points);
    
    // 根据目标位置更新特征点
    for (int i = 0; i < indexes.length && i < targetPositions.length; i++) {
      final index = indexes[i];
      if (index >= 0 && index < deformedPoints.length) {
        final targetPosition = targetPositions[i];
        final oldPoint = deformedPoints[index];
        deformedPoints[index] = FeaturePoint(
          id: oldPoint.id,
          index: oldPoint.index,
          name: oldPoint.name,
          x: targetPosition.dx,
          y: targetPosition.dy,
          z: oldPoint.z,
          visibility: oldPoint.visibility,
          confidence: oldPoint.confidence,
          isPrimary: oldPoint.isPrimary,
          opacity: oldPoint.opacity,
          size: oldPoint.size,
          color: oldPoint.color,
        );
      }
    }
    
    Logger.flow(logTag, 'applyDeformation', '✅ 应用变形到${indexes.length}个特征点');
    Logger.flowEnd(logTag, 'applyDeformation');
    return deformedPoints;
  }
}
