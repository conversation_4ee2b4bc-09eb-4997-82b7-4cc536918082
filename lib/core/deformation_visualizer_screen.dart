import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'dart:io';
import 'deformation_visualizer_widget.dart';

/// 面部变形区域可视化屏幕
class DeformationVisualizerScreen extends StatefulWidget {
  const DeformationVisualizerScreen({Key? key}) : super(key: key);

  @override
  _DeformationVisualizerScreenState createState() => _DeformationVisualizerScreenState();
}

class _DeformationVisualizerScreenState extends State<DeformationVisualizerScreen> {
  String? imagePath;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeImagePath();
  }

  /// 初始化测试图像路径
  Future<void> _initializeImagePath() async {
    setState(() {
      isLoading = true;
    });

    try {
      // 获取项目根目录
      final projectRoot = await _getProjectRoot();
      
      // 测试图像路径
      final testImagePath = path.join(projectRoot, 'testdata', 'test_face.jpg');
      
      // 检查文件是否存在
      if (await File(testImagePath).exists()) {
        setState(() {
          imagePath = testImagePath;
          isLoading = false;
        });
      } else {
        // 尝试在assets目录中查找
        final assetImagePath = path.join(projectRoot, 'assets', 'test_face.jpg');
        if (await File(assetImagePath).exists()) {
          // 复制到testdata目录
          final testdataDir = Directory(path.join(projectRoot, 'testdata'));
          if (!await testdataDir.exists()) {
            await testdataDir.create(recursive: true);
          }
          
          await File(assetImagePath).copy(testImagePath);
          
          setState(() {
            imagePath = testImagePath;
            isLoading = false;
          });
        } else {
          setState(() {
            isLoading = false;
          });
        }
      }
    } catch (e) {
      print('初始化图像路径时出错: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  /// 获取项目根目录
  Future<String> _getProjectRoot() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      return path.dirname(path.dirname(appDir.path));
    } catch (e) {
      // 如果无法获取应用文档目录，则使用当前目录
      return Directory.current.path;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('面部变形区域可视化'),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : imagePath != null
              ? DeformationVisualizerWidget(initialImagePath: imagePath!)
              : const Center(
                  child: Text(
                    '找不到测试图像\n请确保 testdata/test_face.jpg 存在',
                    textAlign: TextAlign.center,
                  ),
                ),
    );
  }
}
