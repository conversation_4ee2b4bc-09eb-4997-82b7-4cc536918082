#!/usr/bin/env python3.11
# -*- coding: utf-8 -*-

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import queue

# 确保使用Python 3.11
if not (sys.version_info.major == 3 and sys.version_info.minor == 11):
    print("警告: 此程序需要Python 3.11版本")
    sys.exit(1)

# 导入我们之前创建的可视化器
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from core.face_deformation_visualizer import FaceDeformationVisualizer

class DeformationVisualizerGUI:
    """面部变形区域可视化界面"""
    
    def __init__(self, root):
        """初始化可视化界面"""
        self.root = root
        self.root.title("面部美化变形区域可视化")
        self.root.geometry("1280x800")
        
        # 创建可视化器
        self.visualizer = FaceDeformationVisualizer()
        
        # 当前图像和结果
        self.current_image_path = None
        self.current_image = None
        self.result_image = None
        self.landmarks = None
        
        # 参数配置
        self.area_params = {
            "面部轮廓 (face_contour)": {
                "area_name": "face_contour",
                "params": {
                    "轮廓收紧 (contour_tighten)": "contour_tighten",
                    "下巴调整 (chin_adjust)": "chin_adjust",
                    "颧骨调整 (cheekbone_adjust)": "cheekbone_adjust",
                    "脸型优化 (face_shape)": "face_shape"
                }
            },
            "鼻部塑形 (nose)": {
                "area_name": "nose",
                "params": {
                    "鼻梁高度 (bridge_height)": "bridge_height",
                    "鼻尖调整 (tip_adjust)": "tip_adjust",
                    "鼻翼宽度 (nostril_width)": "nostril_width",
                    "鼻基抬高 (base_height)": "base_height"
                }
            },
            "眼部美化 (eyes)": {
                "area_name": "eyes",
                "params": {
                    "双眼皮 (double_fold)": "double_fold",
                    "开眼角 (canthal_tilt)": "canthal_tilt",
                    "去眼袋 (eye_bag_removal)": "eye_bag_removal",
                    "提眼尾 (outer_corner_lift)": "outer_corner_lift"
                }
            },
            "唇部造型 (lips)": {
                "area_name": "lips",
                "params": {
                    "唇形调整 (lip_shape)": "lip_shape",
                    "嘴唇厚度 (lip_thickness)": "lip_thickness",
                    "嘴角上扬 (mouth_corner)": "mouth_corner",
                    "唇色优化 (lip_color)": "lip_color"
                }
            },
            "抗衰冻龄 (anti_aging)": {
                "area_name": "anti_aging",
                "params": {
                    "法令纹 (nasolabial_folds)": "nasolabial_folds",
                    "去皱纹 (wrinkle_removal)": "wrinkle_removal",
                    "额头饱满 (forehead_fullness)": "forehead_fullness",
                    "面容紧致 (facial_firmness)": "facial_firmness"
                }
            }
        }
        
        # 变形类型颜色映射
        self.transform_colors = {
            'vector_field': "绿色 - 向量场变形",
            'tps': "红色 - 薄板样条插值",
            'mesh': "蓝色 - 网格变形",
            'local': "黄色 - 局部变形",
            'triangulation': "紫色 - 三角剖分变形"
        }
        
        # 创建界面
        self._create_widgets()
        
        # 加载默认图像
        default_image_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                        "testdata", "test_face.jpg")
        if os.path.exists(default_image_path):
            self.load_image(default_image_path)
        
        # 处理队列和线程
        self.queue = queue.Queue()
        self.is_processing = False
        self.root.after(100, self._process_queue)
    
    def _create_widgets(self):
        """创建界面控件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 图像选择按钮
        image_frame = ttk.Frame(control_frame)
        image_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(image_frame, text="选择图像", command=self._select_image).pack(side=tk.LEFT)
        ttk.Button(image_frame, text="使用默认图像", command=self._use_default_image).pack(side=tk.LEFT, padx=(5, 0))
        
        # 区域选择
        ttk.Label(control_frame, text="选择美化区域:").pack(anchor=tk.W)
        self.area_var = tk.StringVar()
        area_combo = ttk.Combobox(control_frame, textvariable=self.area_var, state="readonly")
        area_combo["values"] = list(self.area_params.keys())
        area_combo.current(0)
        area_combo.pack(fill=tk.X, pady=(0, 10))
        area_combo.bind("<<ComboboxSelected>>", self._on_area_selected)
        
        # 参数选择
        ttk.Label(control_frame, text="选择参数:").pack(anchor=tk.W)
        self.param_var = tk.StringVar()
        self.param_combo = ttk.Combobox(control_frame, textvariable=self.param_var, state="readonly")
        self.param_combo.pack(fill=tk.X, pady=(0, 10))
        
        # 参数值滑块
        ttk.Label(control_frame, text="参数值:").pack(anchor=tk.W)
        self.value_var = tk.DoubleVar(value=0.5)
        self.value_slider = ttk.Scale(control_frame, from_=-1.0, to=1.0, orient=tk.HORIZONTAL, 
                                    variable=self.value_var, command=self._on_value_changed)
        self.value_slider.pack(fill=tk.X)
        
        # 参数值标签
        self.value_label = ttk.Label(control_frame, text="0.50")
        self.value_label.pack(anchor=tk.E)
        
        # 变形类型信息
        self.transform_type_frame = ttk.LabelFrame(control_frame, text="变形类型", padding="5")
        self.transform_type_frame.pack(fill=tk.X, pady=10)
        
        self.transform_type_label = ttk.Label(self.transform_type_frame, text="")
        self.transform_type_label.pack(anchor=tk.W)
        
        # 变形类型说明
        info_frame = ttk.LabelFrame(control_frame, text="变形类型说明", padding="5")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        for transform_type, desc in self.transform_colors.items():
            ttk.Label(info_frame, text=desc).pack(anchor=tk.W)
        
        # 可视化按钮
        ttk.Button(control_frame, text="生成可视化", command=self._visualize).pack(fill=tk.X, pady=10)
        
        # 保存按钮
        ttk.Button(control_frame, text="保存结果", command=self._save_result).pack(fill=tk.X)
        
        # 创建右侧图像显示区域
        image_frame = ttk.LabelFrame(main_frame, text="可视化结果", padding="10")
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建图像画布
        self.canvas = tk.Canvas(image_frame, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 初始化参数下拉框
        self._update_param_combo()
    
    def _select_image(self):
        """选择图像"""
        file_path = filedialog.askopenfilename(
            title="选择图像",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp")]
        )
        if file_path:
            self.load_image(file_path)
    
    def _use_default_image(self):
        """使用默认图像"""
        default_image_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                        "testdata", "test_face.jpg")
        if os.path.exists(default_image_path):
            self.load_image(default_image_path)
        else:
            messagebox.showerror("错误", "找不到默认图像: testdata/test_face.jpg")
    
    def load_image(self, image_path):
        """加载图像"""
        self.current_image_path = image_path
        self.status_var.set(f"正在加载图像: {os.path.basename(image_path)}")
        
        # 在后台线程中检测特征点
        threading.Thread(target=self._detect_landmarks, args=(image_path,), daemon=True).start()
    
    def _detect_landmarks(self, image_path):
        """在后台线程中检测特征点"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                self.queue.put(("error", f"无法读取图像: {image_path}"))
                return
            
            # 检测特征点
            landmarks = self.visualizer.detect_face_landmarks(image_path)
            if landmarks is None:
                self.queue.put(("error", "未检测到面部"))
                return
            
            # 调整图像大小以适应画布
            self.current_image = image
            self.landmarks = landmarks
            
            # 显示原始图像
            self._display_image(image)
            
            self.queue.put(("status", f"已加载图像: {os.path.basename(image_path)}"))
        except Exception as e:
            self.queue.put(("error", f"处理图像时出错: {str(e)}"))
    
    def _display_image(self, image):
        """在画布上显示图像"""
        # 调整图像大小以适应画布
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            # 画布尚未完全初始化，使用默认大小
            canvas_width = 800
            canvas_height = 600
        
        # 计算缩放比例
        img_height, img_width = image.shape[:2]
        scale = min(canvas_width / img_width, canvas_height / img_height)
        
        # 缩放图像
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        resized_image = cv2.resize(image, (new_width, new_height))
        
        # 转换为RGB并创建PhotoImage
        image_rgb = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image_rgb)
        photo = ImageTk.PhotoImage(pil_image)
        
        # 清除画布并显示图像
        self.canvas.delete("all")
        self.canvas.config(width=new_width, height=new_height)
        self.canvas.create_image(new_width // 2, new_height // 2, image=photo)
        self.canvas.image = photo  # 保持引用
    
    def _on_area_selected(self, event):
        """处理区域选择事件"""
        self._update_param_combo()
        self._update_transform_type()
    
    def _update_param_combo(self):
        """更新参数下拉框"""
        area_key = self.area_var.get()
        if area_key in self.area_params:
            params = list(self.area_params[area_key]["params"].keys())
            self.param_combo["values"] = params
            if params:
                self.param_combo.current(0)
    
    def _on_param_selected(self, event):
        """处理参数选择事件"""
        self._update_transform_type()
    
    def _update_transform_type(self):
        """更新变形类型信息"""
        area_key = self.area_var.get()
        param_key = self.param_var.get()
        
        if area_key in self.area_params and param_key in self.area_params[area_key]["params"]:
            area_name = self.area_params[area_key]["area_name"]
            param_name = self.area_params[area_key]["params"][param_key]
            
            # 获取变形类型
            transform_type = self.visualizer.parameter_transform_types.get(param_name, "local")
            transform_desc = self.transform_colors.get(transform_type, "未知变形类型")
            
            self.transform_type_label.config(text=transform_desc)
    
    def _on_value_changed(self, event):
        """处理参数值变化事件"""
        value = self.value_var.get()
        self.value_label.config(text=f"{value:.2f}")
    
    def _visualize(self):
        """生成可视化"""
        if self.current_image_path is None or self.landmarks is None:
            messagebox.showwarning("警告", "请先加载图像")
            return
        
        if self.is_processing:
            messagebox.showwarning("警告", "正在处理中，请稍候")
            return
        
        # 获取当前选择的参数
        area_key = self.area_var.get()
        param_key = self.param_var.get()
        
        if area_key not in self.area_params or param_key not in self.area_params[area_key]["params"]:
            messagebox.showwarning("警告", "请选择有效的区域和参数")
            return
        
        area_name = self.area_params[area_key]["area_name"]
        param_name = self.area_params[area_key]["params"][param_key]
        param_value = self.value_var.get()
        
        # 在后台线程中生成可视化
        self.is_processing = True
        self.status_var.set(f"正在生成可视化: {param_key}, 值: {param_value:.2f}")
        
        threading.Thread(
            target=self._generate_visualization,
            args=(area_name, param_name, param_value),
            daemon=True
        ).start()
    
    def _generate_visualization(self, area_name, param_name, param_value):
        """在后台线程中生成可视化"""
        try:
            # 生成可视化
            result = self.visualizer.visualize_parameter_area(
                self.current_image_path, area_name, param_name, param_value
            )
            
            if result is not None:
                self.result_image = result
                # 显示结果
                self._display_image(result)
                self.queue.put(("status", f"已生成可视化: {param_name}, 值: {param_value:.2f}"))
            else:
                self.queue.put(("error", "生成可视化失败"))
        except Exception as e:
            self.queue.put(("error", f"生成可视化时出错: {str(e)}"))
        finally:
            self.is_processing = False
    
    def _save_result(self):
        """保存可视化结果"""
        if self.result_image is None:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
        
        # 获取当前选择的参数
        area_key = self.area_var.get()
        param_key = self.param_var.get()
        
        if area_key not in self.area_params or param_key not in self.area_params[area_key]["params"]:
            messagebox.showwarning("警告", "请选择有效的区域和参数")
            return
        
        area_name = self.area_params[area_key]["area_name"]
        param_name = self.area_params[area_key]["params"][param_key]
        param_value = self.value_var.get()
        
        # 默认文件名
        default_filename = f"{area_name}_{param_name}_{param_value:.2f}.jpg"
        
        # 选择保存位置
        file_path = filedialog.asksaveasfilename(
            title="保存结果",
            defaultextension=".jpg",
            initialfile=default_filename,
            filetypes=[("JPEG图像", "*.jpg"), ("所有文件", "*.*")]
        )
        
        if file_path:
            cv2.imwrite(file_path, self.result_image)
            self.status_var.set(f"已保存结果到: {os.path.basename(file_path)}")
    
    def _process_queue(self):
        """处理消息队列"""
        try:
            while True:
                message_type, message = self.queue.get_nowait()
                
                if message_type == "status":
                    self.status_var.set(message)
                elif message_type == "error":
                    messagebox.showerror("错误", message)
                    self.status_var.set("就绪")
                
                self.queue.task_done()
        except queue.Empty:
            pass
        
        # 继续检查队列
        self.root.after(100, self._process_queue)

def main():
    """主函数"""
    root = tk.Tk()
    app = DeformationVisualizerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
