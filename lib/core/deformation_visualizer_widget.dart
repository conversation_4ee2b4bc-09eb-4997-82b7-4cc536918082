import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:process_run/process_run.dart';

/// 面部变形区域可视化组件
class DeformationVisualizerWidget extends StatefulWidget {
  final String initialImagePath;
  
  const DeformationVisualizerWidget({
    Key? key,
    required this.initialImagePath,
  }) : super(key: key);

  @override
  _DeformationVisualizerWidgetState createState() => _DeformationVisualizerWidgetState();
}

class _DeformationVisualizerWidgetState extends State<DeformationVisualizerWidget> {
  // 当前选择的区域、参数和值
  String selectedArea = 'face_contour';
  String selectedParam = 'contour_tighten';
  double paramValue = 0.5;
  
  // 图像相关
  String? currentImagePath;
  ui.Image? resultImage;
  bool isProcessing = false;
  String statusMessage = '准备就绪';
  
  // 区域和参数映射
  final Map<String, Map<String, String>> areaParams = {
    '面部轮廓': {
      'area_name': 'face_contour',
      'params': {
        '轮廓收紧': 'contour_tighten',
        '下巴调整': 'chin_adjust',
        '颧骨调整': 'cheekbone_adjust',
        '脸型优化': 'face_shape',
      }
    },
    '鼻部塑形': {
      'area_name': 'nose',
      'params': {
        '鼻梁高度': 'bridge_height',
        '鼻尖调整': 'tip_adjust',
        '鼻翼宽度': 'nostril_width',
        '鼻基抬高': 'base_height',
      }
    },
    '眼部美化': {
      'area_name': 'eyes',
      'params': {
        '双眼皮': 'double_fold',
        '开眼角': 'canthal_tilt',
        '去眼袋': 'eye_bag_removal',
        '提眼尾': 'outer_corner_lift',
      }
    },
    '唇部造型': {
      'area_name': 'lips',
      'params': {
        '唇形调整': 'lip_shape',
        '嘴角上扬': 'mouth_corner',
      }
    },
    '抗衰冻龄': {
      'area_name': 'anti_aging',
      'params': {
        '法令纹': 'nasolabial_folds',
        '去皱纹': 'wrinkle_removal',
        '额头饱满': 'forehead_fullness',
        '面容紧致': 'facial_firmness',
      }
    },
  };
  
  // 变形类型映射
  final Map<String, String> transformTypeMap = {
    'contour_tighten': 'mesh',
    'chin_adjust': 'local',
    'cheekbone_adjust': 'tps',
    'face_shape': 'triangulation',
    'bridge_height': 'tps',
    'tip_adjust': 'local',
    'nostril_width': 'vector_field',
    'base_height': 'mesh',
    'double_fold': 'local',
    'canthal_tilt': 'vector_field',
    'eye_bag_removal': 'tps',
    'outer_corner_lift': 'triangulation',
    'lip_shape': 'triangulation',
    'mouth_corner': 'local',
    'nasolabial_folds': 'tps',
    'wrinkle_removal': 'local',
    'forehead_fullness': 'mesh',
    'facial_firmness': 'triangulation',
  };
  
  // 变形类型颜色映射
  final Map<String, Map<String, dynamic>> transformColors = {
    'vector_field': {'name': '向量场变形', 'color': Colors.green, 'description': '显示以控制点为中心的径向衰减影响区域'},
    'tps': {'name': '薄板样条插值', 'color': Colors.red, 'description': '显示全局影响区域，影响强度随距离迅速衰减'},
    'mesh': {'name': '网格变形', 'color': Colors.blue, 'description': '显示网格结构和受影响的网格区域'},
    'local': {'name': '局部变形', 'color': Colors.yellow, 'description': '显示以控制点为中心的平方衰减影响区域'},
    'triangulation': {'name': '三角剖分变形', 'color': Colors.purple, 'description': '显示德劳内三角剖分网格和受影响的三角形'},
  };
  
  // 当前选择的参数对应的变形类型
  String get currentTransformType => transformTypeMap[selectedParam] ?? 'local';
  
  @override
  void initState() {
    super.initState();
    currentImagePath = widget.initialImagePath;
    
    // 初始化选择的区域和参数
    final firstAreaKey = areaParams.keys.first;
    selectedArea = areaParams[firstAreaKey]!['area_name']!;
    final firstParamKey = areaParams[firstAreaKey]!['params']!.keys.first;
    selectedParam = areaParams[firstAreaKey]!['params']![firstParamKey]!;
    
    // 生成初始可视化
    _generateVisualization();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('面部变形区域可视化'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveVisualization,
            tooltip: '保存可视化结果',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 左侧控制面板
                SizedBox(
                  width: 300,
                  child: Card(
                    margin: const EdgeInsets.all(8.0),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: _buildControlPanel(),
                    ),
                  ),
                ),
                
                // 右侧图像显示区域
                Expanded(
                  child: Card(
                    margin: const EdgeInsets.all(8.0),
                    child: _buildImageDisplay(),
                  ),
                ),
              ],
            ),
          ),
          
          // 底部状态栏
          Container(
            height: 30,
            color: Colors.grey[200],
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                if (isProcessing)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                    ),
                  ),
                const SizedBox(width: 8.0),
                Text(statusMessage),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建控制面板
  Widget _buildControlPanel() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 区域选择
        const Text('选择美化区域:', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8.0),
        DropdownButtonFormField<String>(
          value: areaParams.entries.where((e) => e.value['area_name'] == selectedArea).first.key,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          ),
          items: areaParams.keys.map((String key) {
            return DropdownMenuItem<String>(
              value: key,
              child: Text(key),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                selectedArea = areaParams[newValue]!['area_name']!;
                // 更新参数为该区域的第一个参数
                final firstParamKey = areaParams[newValue]!['params']!.keys.first;
                selectedParam = areaParams[newValue]!['params']![firstParamKey]!;
              });
              _generateVisualization();
            }
          },
        ),
        
        const SizedBox(height: 16.0),
        
        // 参数选择
        const Text('选择参数:', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8.0),
        DropdownButtonFormField<String>(
          value: _getDisplayParamKey(),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          ),
          items: _getParamItems(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              final areaKey = areaParams.entries.where((e) => e.value['area_name'] == selectedArea).first.key;
              setState(() {
                selectedParam = areaParams[areaKey]!['params']![newValue]!;
              });
              _generateVisualization();
            }
          },
        ),
        
        const SizedBox(height: 16.0),
        
        // 参数值滑块
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('参数值:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('${paramValue.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        const SizedBox(height: 8.0),
        Slider(
          value: paramValue,
          min: -1.0,
          max: 1.0,
          divisions: 20,
          label: paramValue.toStringAsFixed(2),
          onChanged: (double value) {
            setState(() {
              paramValue = value;
            });
          },
          onChangeEnd: (double value) {
            _generateVisualization();
          },
        ),
        
        const SizedBox(height: 16.0),
        
        // 变形类型信息
        Card(
          color: Colors.grey[100],
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '变形类型: ${transformColors[currentTransformType]!['name']}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4.0),
                Container(
                  height: 20,
                  width: double.infinity,
                  color: transformColors[currentTransformType]!['color'],
                ),
                const SizedBox(height: 8.0),
                Text(transformColors[currentTransformType]!['description']),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16.0),
        
        // 变形类型说明
        const Text('变形类型说明:', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8.0),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: transformColors.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        color: entry.value['color'],
                      ),
                      const SizedBox(width: 8.0),
                      Expanded(
                        child: Text('${entry.value['name']}'),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        
        const SizedBox(height: 16.0),
        
        // 生成可视化按钮
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: isProcessing ? null : _generateVisualization,
            child: const Text('生成可视化'),
          ),
        ),
      ],
    );
  }
  
  /// 构建图像显示区域
  Widget _buildImageDisplay() {
    return Stack(
      children: [
        // 图像显示
        if (resultImage != null)
          Center(
            child: RawImage(
              image: resultImage,
              fit: BoxFit.contain,
            ),
          )
        else
          const Center(
            child: Text('无可视化结果'),
          ),
          
        // 加载指示器
        if (isProcessing)
          const Center(
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }
  
  /// 获取当前区域的参数列表
  List<DropdownMenuItem<String>> _getParamItems() {
    final areaKey = areaParams.entries.where((e) => e.value['area_name'] == selectedArea).first.key;
    return areaParams[areaKey]!['params']!.keys.map((String key) {
      return DropdownMenuItem<String>(
        value: key,
        child: Text(key),
      );
    }).toList();
  }
  
  /// 获取当前参数的显示名称
  String _getDisplayParamKey() {
    final areaKey = areaParams.entries.where((e) => e.value['area_name'] == selectedArea).first.key;
    return areaParams[areaKey]!['params']!.entries.where((e) => e.value == selectedParam).first.key;
  }
  
  /// 生成可视化
  Future<void> _generateVisualization() async {
    if (isProcessing || currentImagePath == null) return;
    
    setState(() {
      isProcessing = true;
      statusMessage = '正在生成可视化...';
    });
    
    try {
      // 生成临时输出路径
      final tempDir = await getTemporaryDirectory();
      final outputPath = path.join(tempDir.path, 'visualization_result.jpg');
      
      // 获取当前区域和参数的英文名称
      final areaName = selectedArea;
      final paramName = selectedParam;
      
      // 调用Python脚本生成可视化
      final result = await _runPythonVisualization(
        imagePath: currentImagePath!,
        areaName: areaName,
        paramName: paramName,
        paramValue: paramValue,
        outputPath: outputPath,
      );
      
      if (result) {
        // 加载生成的图像
        final bytes = await File(outputPath).readAsBytes();
        final codec = await ui.instantiateImageCodec(bytes);
        final frame = await codec.getNextFrame();
        
        setState(() {
          resultImage = frame.image;
          isProcessing = false;
          statusMessage = '可视化生成成功';
        });
      } else {
        setState(() {
          isProcessing = false;
          statusMessage = '可视化生成失败';
        });
      }
    } catch (e) {
      setState(() {
        isProcessing = false;
        statusMessage = '错误: $e';
      });
    }
  }
  
  /// 运行Python可视化脚本
  Future<bool> _runPythonVisualization({
    required String imagePath,
    required String areaName,
    required String paramName,
    required double paramValue,
    required String outputPath,
  }) async {
    try {
      // 获取项目根目录
      final appDir = await getApplicationDocumentsDirectory();
      final projectRoot = path.dirname(path.dirname(appDir.path));
      
      // 构建Python脚本路径
      final scriptPath = path.join(projectRoot, 'lib', 'core', 'face_deformation_visualizer.py');
      
      // 构建命令
      final command = 'python3.11';
      final arguments = [
        scriptPath,
        '--image', imagePath,
        '--area', areaName,
        '--param', paramName,
        '--value', paramValue.toString(),
        '--output', outputPath,
      ];
      
      // 运行命令
      final shell = Shell();
      final result = await shell.run('$command ${arguments.join(' ')}');
      
      // 检查输出文件是否存在
      return File(outputPath).existsSync();
    } catch (e) {
      print('运行Python脚本时出错: $e');
      return false;
    }
  }
  
  /// 保存可视化结果
  Future<void> _saveVisualization() async {
    if (resultImage == null) {
      setState(() {
        statusMessage = '没有可保存的结果';
      });
      return;
    }
    
    try {
      // 获取下载目录
      final downloadsDir = await getDownloadsDirectory();
      if (downloadsDir == null) {
        setState(() {
          statusMessage = '无法获取下载目录';
        });
        return;
      }
      
      // 构建文件名
      final fileName = '${selectedArea}_${selectedParam}_${paramValue.toStringAsFixed(2)}.jpg';
      final filePath = path.join(downloadsDir.path, fileName);
      
      // 复制当前结果图像
      final tempDir = await getTemporaryDirectory();
      final tempFilePath = path.join(tempDir.path, 'visualization_result.jpg');
      if (File(tempFilePath).existsSync()) {
        await File(tempFilePath).copy(filePath);
        
        setState(() {
          statusMessage = '已保存到: $filePath';
        });
      } else {
        setState(() {
          statusMessage = '保存失败: 临时文件不存在';
        });
      }
    } catch (e) {
      setState(() {
        statusMessage = '保存失败: $e';
      });
    }
  }
}
