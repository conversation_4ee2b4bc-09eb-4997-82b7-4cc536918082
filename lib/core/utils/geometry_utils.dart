import 'dart:math' as math;
import 'dart:ui';

import '../models/point_model.dart';
import 'math_utils.dart';

/// 线段类
class Line {
  /// 起点
  final PointModel start;
  
  /// 终点
  final PointModel end;
  
  /// 构造函数
  Line(this.start, this.end);
  
  /// 线段长度
  double get length => MathUtils.calculateDistance(start, end);
  
  /// 线段中点
  PointModel get midpoint => MathUtils.interpolatePoint(start, end, 0.5);
  
  /// 线段方向向量
  Offset get direction => Offset(end.x - start.x, end.y - start.y);
  
  /// 线段单位方向向量
  Offset get unitDirection {
    final Offset dir = direction;
    final double len = dir.distance;
    return len > 0 ? Offset(dir.dx / len, dir.dy / len) : Offset.zero;
  }
  
  /// 线段法向量（逆时针旋转90度）
  Offset get normal {
    final Offset unit = unitDirection;
    return Offset(-unit.dy, unit.dx);
  }
}

/// 多边形类
class Polygon {
  /// 顶点列表
  final List<PointModel> vertices;
  
  /// 构造函数
  Polygon(this.vertices);
  
  /// 多边形边数
  int get edgeCount => vertices.length;
  
  /// 获取多边形的边
  List<Line> get edges {
    final List<Line> result = [];
    for (int i = 0; i < vertices.length; i++) {
      final PointModel start = vertices[i];
      final PointModel end = vertices[(i + 1) % vertices.length];
      result.add(Line(start, end));
    }
    return result;
  }
  
  /// 多边形面积
  double get area {
    if (vertices.length < 3) {
      return 0;
    }
    
    double sum = 0;
    for (int i = 0; i < vertices.length; i++) {
      final PointModel current = vertices[i];
      final PointModel next = vertices[(i + 1) % vertices.length];
      sum += (current.x * next.y - next.x * current.y);
    }
    
    return sum.abs() / 2;
  }
  
  /// 多边形周长
  double get perimeter {
    double sum = 0;
    for (final edge in edges) {
      sum += edge.length;
    }
    return sum;
  }
  
  /// 多边形中心点
  PointModel get centroid => MathUtils.calculateCentroid(vertices);
}

/// 几何工具类
/// 
/// 提供各种几何计算工具函数
class GeometryUtils {
  /// 私有构造函数，防止实例化
  GeometryUtils._();
  
  /// 判断点是否在多边形内部
  /// 
  /// [point] 待判断的点
  /// [polygon] 多边形顶点列表
  /// 
  /// 返回点是否在多边形内部
  static bool isPointInPolygon(PointModel point, List<PointModel> polygon) {
    if (polygon.length < 3) {
      return false;
    }
    
    bool isInside = false;
    int j = polygon.length - 1;
    
    for (int i = 0; i < polygon.length; i++) {
      if ((polygon[i].y > point.y) != (polygon[j].y > point.y) &&
          (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / 
           (polygon[j].y - polygon[i].y) + polygon[i].x)) {
        isInside = !isInside;
      }
      j = i;
    }
    
    return isInside;
  }
  
  /// 创建对称点
  /// 
  /// [point] 原始点
  /// [symmetryLine] 对称轴
  /// 
  /// 返回对称点
  static PointModel createSymmetricPoint(PointModel point, Line symmetryLine) {
    // 计算点到直线的投影点
    final Offset lineDir = symmetryLine.direction;
    final double lineLenSq = lineDir.distanceSquared;
    
    if (lineLenSq == 0) {
      return point;
    }
    
    final double t = ((point.x - symmetryLine.start.x) * lineDir.dx +
                     (point.y - symmetryLine.start.y) * lineDir.dy) / lineLenSq;
    
    final PointModel projection = PointModel(
      id: 'projection',
      x: symmetryLine.start.x + t * lineDir.dx,
      y: symmetryLine.start.y + t * lineDir.dy,
      z: point.z,
      confidence: point.confidence,
      type: point.type,
    );
    
    // 计算对称点
    return PointModel(
      id: 'symmetric_${point.id}',
      x: 2 * projection.x - point.x,
      y: 2 * projection.y - point.y,
      z: point.z,
      confidence: point.confidence,
      type: point.type,
    );
  }
  
  /// 创建对称点列表
  /// 
  /// [points] 原始点列表
  /// [symmetryLine] 对称轴
  /// 
  /// 返回对称点列表
  static List<PointModel> createSymmetricPoints(List<PointModel> points, Line symmetryLine) {
    return points.map((point) => createSymmetricPoint(point, symmetryLine)).toList();
  }
  
  /// 计算两个区域的面积比
  /// 
  /// [area1] 第一个区域的顶点列表
  /// [area2] 第二个区域的顶点列表
  /// 
  /// 返回面积比（area1 / area2）
  static double calculateAreaRatio(List<PointModel> area1, List<PointModel> area2) {
    final double area1Size = Polygon(area1).area;
    final double area2Size = Polygon(area2).area;
    
    if (area2Size == 0) {
      throw ArgumentError('第二个区域的面积不能为0');
    }
    
    return area1Size / area2Size;
  }
  
  /// 计算凸包
  /// 
  /// [points] 点列表
  /// 
  /// 返回凸包顶点列表（Graham扫描法）
  static List<PointModel> computeConvexHull(List<PointModel> points) {
    if (points.length < 3) {
      return List.from(points);
    }
    
    // 找到y坐标最小的点
    PointModel pivot = points[0];
    for (int i = 1; i < points.length; i++) {
      if (points[i].y < pivot.y || (points[i].y == pivot.y && points[i].x < pivot.x)) {
        pivot = points[i];
      }
    }
    
    // 根据极角排序
    final List<PointModel> sortedPoints = List.from(points);
    sortedPoints.remove(pivot);
    sortedPoints.sort((a, b) {
      final double angleA = math.atan2(a.y - pivot.y, a.x - pivot.x);
      final double angleB = math.atan2(b.y - pivot.y, b.x - pivot.x);
      
      if (angleA == angleB) {
        return MathUtils.calculateDistance(pivot, a).compareTo(
            MathUtils.calculateDistance(pivot, b));
      }
      
      return angleA.compareTo(angleB);
    });
    
    // Graham扫描
    final List<PointModel> hull = [pivot];
    
    for (final point in sortedPoints) {
      while (hull.length > 1 && !_isLeftTurn(hull[hull.length - 2], hull[hull.length - 1], point)) {
        hull.removeLast();
      }
      hull.add(point);
    }
    
    return hull;
  }
  
  /// 判断三点是否形成左转
  static bool _isLeftTurn(PointModel a, PointModel b, PointModel c) {
    return (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x) > 0;
  }
  
  /// 计算点集的最小外接矩形
  /// 
  /// [points] 点列表
  /// 
  /// 返回最小外接矩形的四个顶点
  static List<PointModel> computeMinimumBoundingBox(List<PointModel> points) {
    if (points.isEmpty) {
      return [];
    }
    
    double minX = points[0].x;
    double minY = points[0].y;
    double maxX = points[0].x;
    double maxY = points[0].y;
    
    for (int i = 1; i < points.length; i++) {
      if (points[i].x < minX) minX = points[i].x;
      if (points[i].y < minY) minY = points[i].y;
      if (points[i].x > maxX) maxX = points[i].x;
      if (points[i].y > maxY) maxY = points[i].y;
    }
    
    return [
      PointModel(id: 'bbox_tl', x: minX, y: minY),
      PointModel(id: 'bbox_tr', x: maxX, y: minY),
      PointModel(id: 'bbox_br', x: maxX, y: maxY),
      PointModel(id: 'bbox_bl', x: minX, y: maxY),
    ];
  }
  
  /// 计算点到多边形的最短距离
  /// 
  /// [point] 点
  /// [polygon] 多边形顶点列表
  /// 
  /// 返回点到多边形的最短距离
  static double pointToPolygonDistance(PointModel point, List<PointModel> polygon) {
    if (isPointInPolygon(point, polygon)) {
      return 0;
    }
    
    double minDistance = double.infinity;
    final edges = Polygon(polygon).edges;
    
    for (final edge in edges) {
      final double distance = MathUtils.pointToLineDistance(point, edge.start, edge.end);
      if (distance < minDistance) {
        minDistance = distance;
      }
    }
    
    return minDistance;
  }
  
  /// 计算两个多边形的交集
  /// 
  /// [polygon1] 第一个多边形顶点列表
  /// [polygon2] 第二个多边形顶点列表
  /// 
  /// 返回交集多边形顶点列表（简化实现，仅支持凸多边形）
  static List<PointModel> polygonIntersection(List<PointModel> polygon1, List<PointModel> polygon2) {
    // 注意：这是一个简化实现，仅适用于凸多边形
    // 完整的多边形交集算法（如Weiler-Atherton算法）更为复杂
    
    final List<PointModel> result = [];
    
    // 添加第一个多边形内的第二个多边形顶点
    for (final vertex in polygon2) {
      if (isPointInPolygon(vertex, polygon1)) {
        result.add(vertex);
      }
    }
    
    // 添加第二个多边形内的第一个多边形顶点
    for (final vertex in polygon1) {
      if (isPointInPolygon(vertex, polygon2)) {
        result.add(vertex);
      }
    }
    
    // 添加边的交点
    final edges1 = Polygon(polygon1).edges;
    final edges2 = Polygon(polygon2).edges;
    
    for (final edge1 in edges1) {
      for (final edge2 in edges2) {
        final PointModel? intersection = MathUtils.lineIntersection(
            edge1.start, edge1.end, edge2.start, edge2.end);
        
        if (intersection != null) {
          result.add(intersection);
        }
      }
    }
    
    // 如果交点数量不足以形成多边形，返回空列表
    if (result.length < 3) {
      return [];
    }
    
    // 计算交集的凸包
    return computeConvexHull(result);
  }
}
