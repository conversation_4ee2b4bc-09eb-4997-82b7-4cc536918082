import 'dart:math' as math;
import 'dart:ui';

import '../models/point_model.dart';

/// 数学工具类
/// 
/// 提供各种数学计算工具函数
class MathUtils {
  /// 私有构造函数，防止实例化
  MathUtils._();
  
  /// 计算两点之间的距离
  /// 
  /// [p1] 第一个点
  /// [p2] 第二个点
  /// 
  /// 返回两点之间的欧几里得距离
  static double calculateDistance(PointModel p1, PointModel p2) {
    return p1.distanceTo(p2);
  }
  
  /// 计算三点形成的角度
  /// 
  /// [p1] 第一个点
  /// [p2] 中心点（角度顶点）
  /// [p3] 第三个点
  /// 
  /// 返回角度（弧度）
  static double calculateAngle(PointModel p1, PointModel p2, PointModel p3) {
    final double a = calculateDistance(p2, p3);
    final double b = calculateDistance(p1, p3);
    final double c = calculateDistance(p1, p2);
    
    // 使用余弦定理计算角度
    return math.acos((a * a + c * c - b * b) / (2 * a * c));
  }
  
  /// 计算三点形成的角度（度数）
  /// 
  /// [p1] 第一个点
  /// [p2] 中心点（角度顶点）
  /// [p3] 第三个点
  /// 
  /// 返回角度（度数）
  static double calculateAngleDegrees(PointModel p1, PointModel p2, PointModel p3) {
    return calculateAngle(p1, p2, p3) * 180 / math.pi;
  }
  
  /// 在两点之间插值生成新的点
  /// 
  /// [p1] 起始点
  /// [p2] 结束点
  /// [t] 插值参数（0.0-1.0）
  /// 
  /// 返回插值点
  static PointModel interpolatePoint(PointModel p1, PointModel p2, double t) {
    return PointModel(
      id: '${p1.id}_${p2.id}_$t',
      x: p1.x + (p2.x - p1.x) * t,
      y: p1.y + (p2.y - p1.y) * t,
      z: p1.z + (p2.z - p1.z) * t,
      confidence: math.min(p1.confidence, p2.confidence),
      type: PointType.secondary,
    );
  }
  
  /// 在点列表之间插值生成新的点列表
  /// 
  /// [points] 原始点列表
  /// [count] 需要插值生成的点数量
  /// 
  /// 返回包含原始点和插值点的列表
  static List<PointModel> interpolatePoints(List<PointModel> points, int count) {
    if (points.length < 2) {
      return List.from(points);
    }
    
    final List<PointModel> result = [];
    
    // 计算每段需要插入的点数
    final int segmentCount = points.length - 1;
    final int pointsPerSegment = count ~/ segmentCount;
    
    for (int i = 0; i < segmentCount; i++) {
      final PointModel p1 = points[i];
      final PointModel p2 = points[i + 1];
      
      result.add(p1);
      
      for (int j = 1; j <= pointsPerSegment; j++) {
        final double t = j / (pointsPerSegment + 1);
        result.add(interpolatePoint(p1, p2, t));
      }
    }
    
    // 添加最后一个点
    result.add(points.last);
    
    return result;
  }
  
  /// 计算点的加权平均值
  /// 
  /// [points] 点列表
  /// [weights] 权重列表，与点列表长度相同
  /// 
  /// 返回加权平均点
  static PointModel weightedAverage(List<PointModel> points, List<double> weights) {
    if (points.isEmpty || points.length != weights.length) {
      throw ArgumentError('点列表和权重列表长度必须相同且不为空');
    }
    
    double sumX = 0;
    double sumY = 0;
    double sumZ = 0;
    double sumWeights = 0;
    double sumConfidence = 0;
    
    for (int i = 0; i < points.length; i++) {
      final PointModel point = points[i];
      final double weight = weights[i];
      
      sumX += point.x * weight;
      sumY += point.y * weight;
      sumZ += point.z * weight;
      sumConfidence += point.confidence * weight;
      sumWeights += weight;
    }
    
    return PointModel(
      id: 'weighted_avg',
      x: sumX / sumWeights,
      y: sumY / sumWeights,
      z: sumZ / sumWeights,
      confidence: sumConfidence / sumWeights,
      type: PointType.secondary,
    );
  }
  
  /// 计算点列表的中心点
  /// 
  /// [points] 点列表
  /// 
  /// 返回中心点
  static PointModel calculateCentroid(List<PointModel> points) {
    if (points.isEmpty) {
      throw ArgumentError('点列表不能为空');
    }
    
    double sumX = 0;
    double sumY = 0;
    double sumZ = 0;
    double sumConfidence = 0;
    
    for (final point in points) {
      sumX += point.x;
      sumY += point.y;
      sumZ += point.z;
      sumConfidence += point.confidence;
    }
    
    return PointModel(
      id: 'centroid',
      x: sumX / points.length,
      y: sumY / points.length,
      z: sumZ / points.length,
      confidence: sumConfidence / points.length,
      type: PointType.secondary,
    );
  }
  
  /// 计算点到直线的距离
  /// 
  /// [point] 点
  /// [lineStart] 直线起点
  /// [lineEnd] 直线终点
  /// 
  /// 返回点到直线的距离
  static double pointToLineDistance(PointModel point, PointModel lineStart, PointModel lineEnd) {
    final double lineLength = calculateDistance(lineStart, lineEnd);
    if (lineLength == 0) {
      return calculateDistance(point, lineStart);
    }
    
    // 计算点到直线的距离
    final double t = ((point.x - lineStart.x) * (lineEnd.x - lineStart.x) +
                     (point.y - lineStart.y) * (lineEnd.y - lineStart.y)) /
                    (lineLength * lineLength);
    
    if (t < 0) {
      return calculateDistance(point, lineStart);
    }
    if (t > 1) {
      return calculateDistance(point, lineEnd);
    }
    
    final PointModel projection = PointModel(
      id: 'projection',
      x: lineStart.x + t * (lineEnd.x - lineStart.x),
      y: lineStart.y + t * (lineEnd.y - lineStart.y),
      z: lineStart.z + t * (lineEnd.z - lineStart.z),
      confidence: math.min(lineStart.confidence, lineEnd.confidence),
      type: PointType.secondary,
    );
    
    return calculateDistance(point, projection);
  }
  
  /// 计算两条直线的交点
  /// 
  /// [line1Start] 第一条直线起点
  /// [line1End] 第一条直线终点
  /// [line2Start] 第二条直线起点
  /// [line2End] 第二条直线终点
  /// 
  /// 返回交点，如果直线平行则返回null
  static PointModel? lineIntersection(
      PointModel line1Start, PointModel line1End, PointModel line2Start, PointModel line2End) {
    final double a1 = line1End.y - line1Start.y;
    final double b1 = line1Start.x - line1End.x;
    final double c1 = a1 * line1Start.x + b1 * line1Start.y;
    
    final double a2 = line2End.y - line2Start.y;
    final double b2 = line2Start.x - line2End.x;
    final double c2 = a2 * line2Start.x + b2 * line2Start.y;
    
    final double determinant = a1 * b2 - a2 * b1;
    
    if (determinant == 0) {
      // 直线平行
      return null;
    }
    
    final double x = (b2 * c1 - b1 * c2) / determinant;
    final double y = (a1 * c2 - a2 * c1) / determinant;
    
    return PointModel(
      id: 'intersection',
      x: x,
      y: y,
      z: (line1Start.z + line1End.z + line2Start.z + line2End.z) / 4,
      confidence: math.min(
        math.min(line1Start.confidence, line1End.confidence),
        math.min(line2Start.confidence, line2End.confidence),
      ),
      type: PointType.secondary,
    );
  }
}
