import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:io';
import '../utils/logger.dart';
import 'models/feature_point.dart';  // 使用 core 目录下的 FeaturePoint 类
import '../beautify_feature/services/transformation_service.dart';
import 'deformation_utils.dart';
import 'feature_points_helper.dart'; // Import FeaturePointsHelper
import 'deformation_cache_manager.dart'; // 导入变形缓存管理器
import 'transformations/transformation_factory.dart'; // 导入变形工厂

/// 特征点管理器
/// 负责特征点的获取、更新和处理
class FeaturePointManager {
  static const String _logTag = 'FeaturePointManager';
  TransformationService? _transformationService;
  final FeaturePointsHelper _featurePointsHelper = FeaturePointsHelper();
  
  // 特征点列表
  List<FeaturePoint>? _featurePoints;
  
  // 侧面特征点列表
  List<FeaturePoint>? _sideFeaturePoints;
  
  // 缓存有效性标记
  bool _featurePointsCacheValid = false;
  
  // 当前参数的特征点索引
  List<int> _currentParameterPointIndexes = [];
  
  // 当前区域类型
  String? _areaType;
  
  // 当前参数名称
  String? _parameterName;
  
  // 图像尺寸
  Size _imageSize = Size.zero;
  
  // 缓存机制
  final Map<String, List<FeaturePoint>> _areaFeaturePointsCache = {};
  final Map<String, Rect> _areaBoundsCache = {};
  final Map<String, List<FeaturePoint>> _parameterPointsCache = {};
  final Map<String, List<int>> _featurePointIndexCache = {};
  bool _cacheInvalidated = false;
  bool _featurePointsChanged = false;
  
  // 添加缓存统计信息
  int _cacheHits = 0;
  int _cacheMisses = 0;
  
  /// 初始化特征点管理器
  Future<void> initialize() async {
    Logger.flowStart(_logTag, 'initialize');
    
    // 初始化特征点列表
    _featurePoints = [];
    
    // 初始化缓存
    _areaFeaturePointsCache.clear();
    _areaBoundsCache.clear();
    _parameterPointsCache.clear();
    _featurePointIndexCache.clear();
    
    // 初始化特征点辅助工具
    await _featurePointsHelper.initialize();
    
    Logger.flow(_logTag, 'initialize', '✅ 初始化完成');
    Logger.flowEnd(_logTag, 'initialize');
  }
  
  /// 添加特征点
  /// 
  /// 添加一个特征点到特征点列表中
  void addFeaturePoint(FeaturePoint point) {
    Logger.flowStart(_logTag, 'addFeaturePoint');
    
    // 确保特征点列表已初始化
    _featurePoints ??= [];
    
    // 检查是否已存在相同索引的特征点
    final existingIndex = _featurePoints!.indexWhere((p) => p.index == point.index);
    if (existingIndex >= 0) {
      // 如果已存在，则替换
      _featurePoints![existingIndex] = point;
      Logger.flow(_logTag, 'addFeaturePoint', '✅ 替换特征点: 索引=${point.index}, 位置=(${point.x}, ${point.y})');
    } else {
      // 如果不存在，则添加
      _featurePoints!.add(point);
      Logger.flow(_logTag, 'addFeaturePoint', '✅ 添加特征点: 索引=${point.index}, 位置=(${point.x}, ${point.y})');
    }
    
    // 标记缓存无效
    _cacheInvalidated = true;
    _featurePointsChanged = true;
    
    Logger.flowEnd(_logTag, 'addFeaturePoint');
  }
  
  /// 获取所有特征点
  /// 
  /// 判断当前是否应该检查特征点数据
  /// 
  /// 在程序初始化时，我们不应该检查特征点数据
  /// 只有在用户选择图像或执行相关操作后，才应该检查特征点数据
  bool shouldCheckFeaturePoints() {
    // 如果有变形服务并且变形服务应该检查特征点数据
    if (_transformationService != null) {
      return _transformationService!.shouldCheckFeaturePoints();
    }
    
    // 如果没有变形服务，则检查区域和参数名称
    if ((_areaType != null && _areaType!.isNotEmpty) || (_parameterName != null && _parameterName!.isNotEmpty)) {
      return true;
    }
    
    // 如果特征点数据非空，则应该检查
    if (_featurePoints != null && _featurePoints!.isNotEmpty) {
      return true;
    }
    
    // 如果以上条件都不满足，则表示程序刚刚启动，不应该检查特征点数据
    return false;
  }

  /// 更新当前参数的特征点索引
  void updateCurrentParameterPointIndexes() {
    Logger.flowStart(_logTag, 'updateCurrentParameterPointIndexes');
    
    if (_areaType == null || _parameterName == null) {
      Logger.flowWarning(_logTag, 'updateCurrentParameterPointIndexes', '区域类型或参数名称为空，无法更新特征点索引');
      Logger.flowEnd(_logTag, 'updateCurrentParameterPointIndexes');
      return;
    }
    
    Logger.flow(_logTag, 'updateCurrentParameterPointIndexes', '区域类型: $_areaType, 参数名称: $_parameterName');
    
    // 获取特征点索引
    final indexes = getFeaturePointIndexes(_areaType!, _parameterName!);
    
    if (indexes.isEmpty) {
      Logger.flowWarning(_logTag, 'updateCurrentParameterPointIndexes', '获取到的特征点索引为空');
    } else {
      _currentParameterPointIndexes = List<int>.from(indexes);
      Logger.flow(_logTag, 'updateCurrentParameterPointIndexes', '更新特征点索引数量: ${_currentParameterPointIndexes.length}');
    }
    
    // 重置缓存状态
    _cacheInvalidated = false;
    
    Logger.flowEnd(_logTag, 'updateCurrentParameterPointIndexes');
  }

  /// 【关键新增】计算并缓存全局面部中心线
  /// 在特征点设置成功后立即调用，计算并永久缓存面部中心线
  /// [originalFeaturePoints] 原始特征点（未变形）
  void _calculateAndCacheGlobalFacialCenterLine(List<FeaturePoint> originalFeaturePoints) {
    Logger.flowStart(_logTag, '_calculateAndCacheGlobalFacialCenterLine');
    
    // 检查是否有足够的特征点数据
    if (originalFeaturePoints.isEmpty) {
      Logger.flowWarning(_logTag, '_calculateAndCacheGlobalFacialCenterLine',
          '⚠️ 特征点列表为空，无法计算面部中心线');
      Logger.flowEnd(_logTag, '_calculateAndCacheGlobalFacialCenterLine');
      return;
    }
    
    // 检查是否有对应的源图像（从变形服务获取）
    ui.Image? sourceImage;
    if (_transformationService != null) {
      sourceImage = _transformationService!.getSourceImage();
    }
    
    if (sourceImage == null) {
      Logger.flowWarning(_logTag, '_calculateAndCacheGlobalFacialCenterLine',
          '⚠️ 无法获取源图像，使用默认缩放系数计算面部中心线');
    }
    
    try {
      // 计算面部中心线（使用默认缩放系数 1.0）
      if (sourceImage != null) {
        DeformationCacheManager.setGlobalFacialCenterLineX(originalFeaturePoints, sourceImage, 1.0);
        Logger.flow(_logTag, '_calculateAndCacheGlobalFacialCenterLine',
            '✅ 成功计算并缓存全局面部中心线');
      } else {
        Logger.flowWarning(_logTag, '_calculateAndCacheGlobalFacialCenterLine',
            '⚠️ 源图像不可用，跳过面部中心线计算');
      }
    } catch (e) {
      Logger.flowError(_logTag, '_calculateAndCacheGlobalFacialCenterLine',
          '❌ 计算全局面部中心线时出错: ${e.toString()}');
    }
    
    Logger.flowEnd(_logTag, '_calculateAndCacheGlobalFacialCenterLine');
  }

  /// 获取特征点索引列表
  List<int> getFeaturePointIndexes(String areaType, String parameterName) {
    Logger.flowStart(_logTag, 'getFeaturePointIndexes');
    Logger.flow(_logTag, 'getFeaturePointIndexes', '区域类型: $areaType, 参数名称: $parameterName');
    
    // 检查缓存
    final cacheKey = '$areaType:$parameterName';
    
    if (_featurePointIndexCache.containsKey(cacheKey) && 
        (!_cacheInvalidated || !_featurePointsChanged)) {
      _cacheHits++;
      Logger.flow(_logTag, 'getFeaturePointIndexes', '缓存命中 (总命中: $_cacheHits, 总未命中: $_cacheMisses)');
      Logger.flowEnd(_logTag, 'getFeaturePointIndexes');
      return _featurePointIndexCache[cacheKey]!;
    }
    
    _cacheMisses++;
    Logger.flow(_logTag, 'getFeaturePointIndexes', '缓存未命中 (总命中: $_cacheHits, 总未命中: $_cacheMisses)');
    
    // 根据区域类型和参数名称获取特征点索引
    List<int> pointIndexes = [];
    
    try {
      // 使用FeaturePointsHelper获取特征点索引
      final featurePointsHelper = FeaturePointsHelper();
      
      // 将字符串区域类型转换为枚举类型
      FeatureAreaType? featureAreaType;
      switch (areaType.toLowerCase()) {
        case 'nose':
          featureAreaType = FeatureAreaType.nose;
          break;
        case 'eyes':
          featureAreaType = FeatureAreaType.eyes;
          break;
        case 'mouth':
        case 'lips':
          featureAreaType = FeatureAreaType.lips;
          break;
        case 'face':
        case 'face_contour':
          featureAreaType = FeatureAreaType.face_contour;
          break;
        case 'anti_aging':
          featureAreaType = FeatureAreaType.anti_aging;
          break;
        default:
          Logger.flowWarning(_logTag, 'getFeaturePointIndexes', '未知的区域类型: $areaType');
      }
      
      // 如果成功转换了区域类型，使用getFeaturePointIndexes方法
      if (featureAreaType != null) {
        try {
          pointIndexes = featurePointsHelper.getFeaturePointIndexes(featureAreaType, parameterName);
          Logger.flow(_logTag, 'getFeaturePointIndexes', '从FeaturePointsHelper获取到特征点索引: ${pointIndexes.length}个');
        } catch (e) {
          Logger.flowError(_logTag, 'getFeaturePointIndexes', '从FeaturePointsHelper获取特征点索引失败: $e');
        }
      }
      
      // 如果仍然没有找到特征点索引，记录错误
      if (pointIndexes.isEmpty) {
        Logger.flowError(_logTag, 'getFeaturePointIndexes', '未找到匹配的特征点索引');
      }
      
      // 更新缓存
      _featurePointIndexCache[cacheKey] = pointIndexes;
      
      Logger.flow(_logTag, 'getFeaturePointIndexes', '特征点索引获取完成: ${pointIndexes.length}个');
      Logger.flowEnd(_logTag, 'getFeaturePointIndexes');
      
      return pointIndexes;
    } catch (e) {
      Logger.flowError(_logTag, 'getFeaturePointIndexes', '获取特征点索引失败: $e');
      Logger.flowEnd(_logTag, 'getFeaturePointIndexes');
      return [];
    }
  }

  /// 获取所有特征点
  /// 
  /// 返回特征点列表的副本，避免外部修改
  List<FeaturePoint> getFeaturePoints() {
    Logger.flowStart(_logTag, 'getFeaturePoints');
    
    // 确保特征点列表已初始化
    _featurePoints ??= [];
    
    // 返回特征点列表的副本
    final result = List<FeaturePoint>.from(_featurePoints!);
    
    Logger.flow(_logTag, 'getFeaturePoints', '返回 ${result.length} 个特征点');
    Logger.flowEnd(_logTag, 'getFeaturePoints');
    
    return result;
  }

  /// 获取所有侧面特征点
  /// 
  /// 返回侧面特征点列表的副本，避免外部修改
  List<FeaturePoint> getSideFeaturePoints() {
    Logger.flowStart(_logTag, 'getSideFeaturePoints');
    
    // 确保侧面特征点列表已初始化
    _sideFeaturePoints ??= [];
    
    // 返回侧面特征点列表的副本
    final result = List<FeaturePoint>.from(_sideFeaturePoints!);
    
    Logger.flow(_logTag, 'getSideFeaturePoints', '返回 ${result.length} 个侧面特征点');
    Logger.flowEnd(_logTag, 'getSideFeaturePoints');
    
    return result;
  }

  /// 获取特征点列表
  List<FeaturePoint>? get featurePoints {
    Logger.flowStart(_logTag, 'get featurePoints');
    if (_featurePoints == null || _featurePoints!.isEmpty) {
      Logger.flowWarning(_logTag, 'get featurePoints', '⚠️ 特征点列表为空或未初始化');
    } else {
      Logger.flow(_logTag, 'get featurePoints', '✅ 特征点列表包含 ${_featurePoints!.length} 个点');
    }
    Logger.flowEnd(_logTag, 'get featurePoints');
    return _featurePoints;
  }

  set featurePoints(List<FeaturePoint>? points) {
    Logger.flowStart(_logTag, 'set featurePoints');
    if (points == null || points.isEmpty) {
      Logger.flowWarning(_logTag, 'set featurePoints', '⚠️ 设置的特征点列表为空或为null');
      // 清除全局面部中心线缓存
      DeformationCacheManager.clearGlobalFacialCenterLine();
    } else {
      Logger.flow(_logTag, 'set featurePoints', '✅ 设置 ${points.length} 个特征点');
      // 【关键新增】特征点设置成功后立即计算并缓存全局面部中心线
      _calculateAndCacheGlobalFacialCenterLine(points);
    }
    _featurePoints = points;
    _featurePointsChanged = true;
    invalidateCache();
    Logger.flowEnd(_logTag, 'set featurePoints');
  }

  /// 侧面特征点列表的getter和setter
  List<FeaturePoint> get sideFeaturePoints => getSideFeaturePoints();
  
  set sideFeaturePoints(List<FeaturePoint> points) {
    Logger.flowStart(_logTag, 'set sideFeaturePoints');
    
    if (points.isEmpty) {
      Logger.flowWarning(_logTag, 'set sideFeaturePoints', '⚠️ 设置的侧面特征点列表为空');
    } else {
      Logger.flow(_logTag, 'set sideFeaturePoints', '✅ 设置 ${points.length} 个侧面特征点');
    }
    
    // 更新侧面特征点列表
    _sideFeaturePoints = List<FeaturePoint>.from(points);
    
    // 标记缓存无效
    _cacheInvalidated = true;
    _featurePointsChanged = true;
    
    // 清除所有缓存
    invalidateCache();
    
    Logger.flowEnd(_logTag, 'set sideFeaturePoints');
  }

  /// 清除缓存
  void invalidateCache() {
    Logger.flowStart(_logTag, 'invalidateCache');
    
    // 清除所有缓存
    _areaFeaturePointsCache.clear();
    _areaBoundsCache.clear();
    _parameterPointsCache.clear();
    _featurePointIndexCache.clear();
    
    // 清除当前参数特征点索引
    _currentParameterPointIndexes.clear();
    
    // 重置缓存状态标志
    _cacheInvalidated = true;
    _featurePointsCacheValid = false;
    _featurePointsChanged = true;
    
    // 重置缓存统计
    _cacheHits = 0;
    _cacheMisses = 0;
    
    Logger.flow(_logTag, 'invalidateCache', '✅ 所有缓存已清除，缓存统计已重置');
    Logger.flowEnd(_logTag, 'invalidateCache');
  }

  /// 获取当前参数的特征点索引
  List<int> get currentParameterPointIndexes => _currentParameterPointIndexes;

  /// 设置区域类型
  set areaType(String? value) {
    if (value != null) {
      _areaType = value;
    }
  }

  /// 获取区域类型
  String? get areaType => _areaType;

  /// 设置参数名称
  set parameterName(String? value) {
    if (value != null) {
      _parameterName = value;
    }
  }

  /// 获取参数名称
  String? get parameterName => _parameterName;

  /// 设置图像尺寸
  set imageSize(Size size) {
    _imageSize = size;
  }

  /// 获取图像尺寸
  Size get imageSize => _imageSize;

  /// 设置区域类型（带参数方法）
  void setAreaType(String areaType, {bool updateIndexes = true}) {
    Logger.flowStart(_logTag, 'setAreaType');
    
    if (_areaType != areaType) {
      _cacheInvalidated = true;
      _featurePointsChanged = true;
    }
    
    _areaType = areaType;
    
    if (updateIndexes && _parameterName != null && _parameterName!.isNotEmpty) {
      Logger.flow(_logTag, 'setAreaType', '开始更新特征点索引');
      updateCurrentParameterPointIndexes();
    }
    
    Logger.flowEnd(_logTag, 'setAreaType');
  }

  /// 设置参数名称（带参数方法）
  void setParameterName(String parameterName, {bool updateIndexes = true}) {
    Logger.flowStart(_logTag, 'setParameterName');
    
    if (_parameterName != parameterName) {
      _cacheInvalidated = true;
      _featurePointsChanged = true;
    }
    
    _parameterName = parameterName;
    
    if (updateIndexes && _areaType != null && _areaType!.isNotEmpty) {
      Logger.flow(_logTag, 'setParameterName', '开始更新特征点索引');
      updateCurrentParameterPointIndexes();
    }
    
    Logger.flowEnd(_logTag, 'setParameterName');
  }

  /// 获取当前参数的特征点索引（带日志）
  List<int> getCurrentParameterPointIndexes() {
    Logger.flowStart(_logTag, 'getCurrentParameterPointIndexes');
    
    if (_currentParameterPointIndexes.isEmpty) {
      Logger.flowWarning(_logTag, 'getCurrentParameterPointIndexes', '当前参数特征点索引为空');
      
      if (_areaType != null && _parameterName != null) {
        Logger.flow(_logTag, 'getCurrentParameterPointIndexes', '尝试更新当前参数点索引');
        updateCurrentParameterPointIndexes();
      }
      
      if (_currentParameterPointIndexes.isEmpty) {
        Logger.flowWarning(_logTag, 'getCurrentParameterPointIndexes', '更新后当前参数点索引仍为空');
        Logger.flowEnd(_logTag, 'getCurrentParameterPointIndexes');
        return [];
      }
    }
    
    Logger.flow(_logTag, 'getCurrentParameterPointIndexes', '参数: $_parameterName, 特征点索引数量: ${_currentParameterPointIndexes.length}');
    Logger.flowEnd(_logTag, 'getCurrentParameterPointIndexes');
    return _currentParameterPointIndexes;
  }

  /// 更新特征点数据
  void updateFeaturePoints(List<FeaturePoint> points) {
    Logger.flowStart(_logTag, 'updateFeaturePoints');
    Logger.flow(_logTag, 'updateFeaturePoints', '更新特征点数据: ${points.length}个点');
    
    if (points.isEmpty) {
      Logger.flowWarning(_logTag, 'updateFeaturePoints', '特征点数据为空');
      Logger.flowEnd(_logTag, 'updateFeaturePoints');
      return;
    }
    
    // 更新特征点列表
    _featurePoints = List<FeaturePoint>.from(points);
    
    _featurePointsCacheValid = true;
    
    // 标记缓存无效和特征点已变化
    _cacheInvalidated = true;
    _featurePointsChanged = true;
    
    // 清除缓存
    _areaFeaturePointsCache.clear();
    _areaBoundsCache.clear();
    _parameterPointsCache.clear();
    _featurePointIndexCache.clear();
    
    Logger.flow(_logTag, 'updateFeaturePoints', '缓存已清除，缓存无效标志: $_cacheInvalidated, 特征点变化标志: $_featurePointsChanged');
    
    Logger.flow(_logTag, 'updateFeaturePoints', '特征点数据更新完成，全量数据已保存');
    Logger.flowEnd(_logTag, 'updateFeaturePoints');
  }

  /// 更新侧面特征点数据
  void updateSideFeaturePoints(List<FeaturePoint> points) {
    Logger.flowStart(_logTag, 'updateSideFeaturePoints');
    Logger.flow(_logTag, 'updateSideFeaturePoints', '更新侧面特征点数据: ${points.length}个点');
    
    if (points.isEmpty) {
      Logger.flowWarning(_logTag, 'updateSideFeaturePoints', '侧面特征点数据为空');
      Logger.flowEnd(_logTag, 'updateSideFeaturePoints');
      return;
    }
    
    // 更新侧面特征点数据
    _sideFeaturePoints = List<FeaturePoint>.from(points);
    
    Logger.flow(_logTag, 'updateSideFeaturePoints', '侧面特征点数据更新完成');
    Logger.flowEnd(_logTag, 'updateSideFeaturePoints');
  }
}