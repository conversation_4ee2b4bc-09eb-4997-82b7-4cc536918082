import 'dart:ui' as ui;
import '../utils/logger.dart';

/// 图像尺寸管理器
/// 
/// 负责确保图像尺寸在整个变形过程中保持不变
class ImageSizeManager {
  static const String _logTag = 'ImageSizeManager';
  
  // 单例模式
  static final ImageSizeManager _instance = ImageSizeManager._internal();
  
  factory ImageSizeManager() {
    return _instance;
  }
  
  ImageSizeManager._internal();
  
  // 原始图像尺寸
  int _originalWidth = 0;
  int _originalHeight = 0;
  
  // 原始图像哈希码，用于检测图像变化
  int? _originalImageHashCode;
  
  /// 设置原始图像尺寸
  void setOriginalImageSize(ui.Image image) {
    if (image.hashCode != _originalImageHashCode) {
      _originalWidth = image.width;
      _originalHeight = image.height;
      _originalImageHashCode = image.hashCode;
      
      Logger.flow(_logTag, 'setOriginalImageSize', '📏 记录原始图像尺寸: ${_originalWidth}x${_originalHeight}, 哈希码: $_originalImageHashCode');
    }
  }
  
  /// 获取原始图像宽度
  int getOriginalWidth() {
    return _originalWidth;
  }
  
  /// 获取原始图像高度
  int getOriginalHeight() {
    return _originalHeight;
  }
  
  /// 检查图像尺寸是否与原始尺寸一致
  bool checkImageSizeConsistency(ui.Image image) {
    if (_originalWidth == 0 || _originalHeight == 0) {
      Logger.flowWarning(_logTag, 'checkImageSizeConsistency', '⚠️ 原始图像尺寸未设置，无法检查一致性');
      return false;
    }
    
    bool isConsistent = (image.width == _originalWidth && image.height == _originalHeight);
    
    if (!isConsistent) {
      Logger.flowWarning(_logTag, 'checkImageSizeConsistency', '⚠️ 图像尺寸不一致');
      Logger.flowWarning(_logTag, 'checkImageSizeConsistency', '  • 当前尺寸: ${image.width}x${image.height}');
      Logger.flowWarning(_logTag, 'checkImageSizeConsistency', '  • 原始尺寸: ${_originalWidth}x${_originalHeight}');
    } else {
      Logger.flow(_logTag, 'checkImageSizeConsistency', '✅ 图像尺寸一致: ${image.width}x${image.height}');
    }
    
    return isConsistent;
  }
  
  /// 创建具有原始尺寸的图像
  /// 
  /// 将 PictureRecorder 转换为 ui.Image，确保尺寸与原始图像一致
  ui.Image createImageWithOriginalSize(ui.PictureRecorder recorder) {
    if (_originalWidth == 0 || _originalHeight == 0) {
      Logger.flowError(_logTag, 'createImageWithOriginalSize', '❌ 原始图像尺寸未设置，无法创建图像');
      throw Exception('原始图像尺寸未设置，无法创建图像');
    }
    
    final picture = recorder.endRecording();
    final image = picture.toImageSync(_originalWidth, _originalHeight);
    
    Logger.flow(_logTag, 'createImageWithOriginalSize', '📏 创建与原始尺寸一致的图像: ${image.width}x${image.height}');
    
    return image;
  }
  
  /// 重置图像尺寸管理器
  void reset() {
    _originalWidth = 0;
    _originalHeight = 0;
    _originalImageHashCode = null;
    
    Logger.flow(_logTag, 'reset', '🔄 重置图像尺寸管理器');
  }
  
  /// 清除所有数据
  void clear() {
    reset();
  }
}
