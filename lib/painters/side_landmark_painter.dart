import 'package:flutter/material.dart';
import '../core/models/feature_point.dart';
import '../utils/logger.dart';

/// 侧面特征点绘制器
/// 负责在侧面图上绘制特征点，专注于鼻梁、鼻尖和面部轮廓
class SideLandmarkPainter extends CustomPainter {
  final List<FeaturePoint> featurePoints;
  final Set<int> highlightIndexes;
  final double blinkValue;
  
  // 点的大小
  static const double _profilePointSize = 3.0;
  static const double _nosePointSize = 3.5;
  static const double _normalPointSize = 1.5;
  
  // 点的颜色
  static const Color _profileColor = Colors.white;
  static const Color _noseColor = Colors.red;
  static const Color _highlightColor = Colors.cyan;
  
  // 轮廓线颜色
  static const Color _profileLineColor = Colors.white70;
  static const Color _noseLineColor = Colors.red;
  
  // 区域定义
  static const List<int> _nosePoints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // 鼻部特征点索引
  static const List<int> _profilePoints = [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]; // 轮廓特征点索引
  
  SideLandmarkPainter({
    required this.featurePoints,
    required this.highlightIndexes,
    required this.blinkValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // 不绘制任何特征点，直接返回
    Logger.d('侧面特征点绘制', '特征点显示已禁用，跳过绘制');
    return;
  }
  
  @override
  bool shouldRepaint(SideLandmarkPainter oldDelegate) {
    return oldDelegate.featurePoints != featurePoints ||
           oldDelegate.highlightIndexes != highlightIndexes ||
           oldDelegate.blinkValue != blinkValue;
  }
}
