import 'package:flutter/material.dart';
import 'dart:math';
import '../utils/logger.dart';
import '../core/feature_points_data.dart';
import '../core/feature_points_config.dart';

/// 特征点绘制器
class LandmarksPainter extends CustomPainter {
  final List<Map<String, dynamic>> landmarks;  // 特征点数据
  final double scale;                        // 缩放比例
  final Offset offset;                       // 偏移量
  final Size imageSize;                      // 图片大小
  final Set<int>? highlightIndexes;          // 需要高亮的点索引
  final double blinkValue;                   // 闪烁动画值 (0.0-1.0)
  final bool showIndexes;                    // 是否显示索引号
  final List<Map<String, dynamic>>? symmetricPairs; // 对称点对数据
  final bool showSymmetryLines;              // 是否显示对称点连接线
  final Map<int, Offset>? deformations;      // 变形偏移量
  final bool isPointAnimating;               // 是否正在进行特征点动画
  final Color pointAnimationColor;           // 特征点动画颜色
  final double pointAnimationOpacity;        // 特征点动画不透明度
  final bool showPointsAfterDeformation;     // 是否显示变形后的特征点

  // 使用 feature_points_data.dart 中的配置

  const LandmarksPainter({
    required this.landmarks,
    required this.scale,
    required this.offset,
    required this.imageSize,
    this.highlightIndexes,
    this.blinkValue = 1.0,
    this.showIndexes = false,  // 默认不显示索引号
    this.symmetricPairs,       // 对称点对数据
    this.showSymmetryLines = true, // 默认显示对称点连接线
    this.deformations,         // 变形偏移量
    this.isPointAnimating = false, // 默认不在进行特征点动画
    this.pointAnimationColor = Colors.white, // 默认动画颜色为白色
    this.pointAnimationOpacity = 1.0, // 默认不透明度为1.0
    this.showPointsAfterDeformation = false, // 默认不显示变形后的特征点
  });

  // 用于跟踪上次绘制的特征点数量和高亮点数量，减少日志输出
  static int _lastLandmarksCount = 0;
  static int _lastHighlightCount = 0;
  static bool _isFirstPaint = true;
  static DateTime _lastLogTime = DateTime.now();
  static int _consecutivePaints = 0;
  static const int _logThreshold = 50; // 提高阈值，减少日志输出频率
  static bool _hasLoggedInSession = false; // 跟踪当前会话是否已输出过日志
  static Set<String> _reportedMissingPairs = {}; // 记录已报告的缺失点对，避免重复日志
  
  @override
  void paint(Canvas canvas, Size size) {
    try {
      // 计算高亮点数量
      final highlightCount = highlightIndexes?.length ?? 0;
      
      // 添加详细的调试日志，跟踪特征点的绘制过程
      Logger.flowStart('特征点绘制', 'paint');
      Logger.flow('特征点绘制', 'paint', '特征点数量: ${landmarks.length}, 高亮特征点数量: $highlightCount, 闪烁值: $blinkValue');
      Logger.flow('特征点绘制', 'paint', '动画状态: isPointAnimating=$isPointAnimating, 动画颜色=${pointAnimationColor.toString()}, 不透明度=$pointAnimationOpacity');
      Logger.flow('特征点绘制', 'paint', '画布尺寸: $size, 缩放比例: $scale, 偏移量: $offset');
      
      if (highlightIndexes != null && highlightIndexes!.isNotEmpty) {
        Logger.flow('特征点绘制', 'paint', '高亮特征点索引: $highlightIndexes');
      } else {
        Logger.flowWarning('特征点绘制', 'paint', '高亮特征点索引为空');
      }
      
      // 记录当前绘制状态
      _lastLandmarksCount = landmarks.length;
      _lastHighlightCount = highlightCount;
      _lastLogTime = DateTime.now();
      _isFirstPaint = false;
      _hasLoggedInSession = true;
      
      if (landmarks.isEmpty) {
        return;
      }
      
      // 绘制所有特征点
      final paint = Paint()..style = PaintingStyle.fill;
      
      // 创建半透明连接线画笔，根据动画值调整透明度和宽度
      final baseOpacity = 0.45; // 基础透明度（增加50%）
      final maxOpacity = 0.9;  // 最大透明度（增加50%）
      final baseWidth = 1.0;   // 基础线宽
      final maxWidth = 1.5;    // 最大线宽
      
      // 根据blinkValue计算当前透明度和线宽
      final lineOpacity = baseOpacity + (maxOpacity - baseOpacity) * blinkValue;
      final lineWidth = baseWidth + (maxWidth - baseWidth) * blinkValue;
      
      final linePaint = Paint()
        ..color = Colors.white.withOpacity(lineOpacity) // 动态透明度
        ..strokeWidth = lineWidth // 动态线宽
        ..style = PaintingStyle.stroke;
      
      // 创建点索引到坐标的映射
      final Map<int, Offset> pointPositions = {};
      
      // 记录所有特征点的索引，用于调试
      final Set<int> allPointIndexes = {};
      
      // 创建文本绘制器
      final textPainter = TextPainter(
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      
      for (final landmark in landmarks) {
        try {
          final index = landmark['index'] as int?;
          if (index == null) continue;
          
          // 获取坐标并转换为屏幕坐标
          final x = (landmark['x'] is int ? (landmark['x'] as int).toDouble() : landmark['x'] as double);
          final y = (landmark['y'] is int ? (landmark['y'] as int).toDouble() : landmark['y'] as double);
          
          // 检查是否有变形偏移量
          Offset deformation = Offset.zero;
          if (deformations != null && deformations!.containsKey(index)) {
            deformation = deformations![index]!;
          }
          
          // 特征点坐标是像素坐标，需要直接乘以缩放比例并加上偏移量
          // 如果有变形，则应用变形偏移量
          final screenX = (x + deformation.dx) * scale + offset.dx;
          final screenY = (y + deformation.dy) * scale + offset.dy;
          final position = Offset(screenX, screenY);
          
          // 存储点索引到坐标的映射
          pointPositions[index] = position;
          
          // 记录索引
          allPointIndexes.add(index);
          
          // 检查是否是高亮点
          final isHighlight = highlightIndexes?.contains(index) ?? false;
          
          // 设置点的样式
          // 获取特征点类型
          String pointType = 'auxiliary';  // 默认是辅助点
          
          // 检查点的类型
          if (landmark['type'] != null) {
            pointType = landmark['type'] as String;
          } else if (landmark['primary'] == true) {
            pointType = 'primary';
          } else if (landmark['secondary'] == true) {
            pointType = 'secondary';
          }
          
          // 获取点的样式
          final style = pointStyles[pointType]!;
          
          // 计算点的大小和透明度
          double pointSize = style['size'] as double;
          double opacity = style['opacity'] as double;
          
          if (isHighlight) {
            // 高亮点使用闪烁动画值
            final baseSize = style['size'] as double;
            final maxSize = pointAnimationConfig['maxPointSize']!;
            final minOpacity = pointAnimationConfig['minOpacity']!;
            final maxOpacity = pointAnimationConfig['maxOpacity']!;
            
            pointSize = baseSize + (maxSize - baseSize) * blinkValue;
            opacity = minOpacity + (maxOpacity - minOpacity) * blinkValue;
          }
          
          // 暂时禁用光晕效果
          // if (style['hasGlow'] == true) {
          //   final glowPaint = Paint()
          //     ..color = (style['glowColor'] as Color).withOpacity(opacity)
          //     ..style = PaintingStyle.stroke
          //     ..strokeWidth = style['glowThickness'] as double;
          //   canvas.drawCircle(position, (style['glowRadius'] as double), glowPaint);
          // }

          if (showIndexes) {
            // 显示索引号而不是点
            // 设置文本样式 - 字体更小
            final textStyle = TextStyle(
              color: Colors.white,
              fontSize: 4.0,  // 字体再小一号
              fontWeight: FontWeight.normal,
            );
            
            // 设置文本
            textPainter.text = TextSpan(
              text: '$index',
              style: textStyle,
            );
            
            // 布局文本
            textPainter.layout();
            
            // 绘制背景圆圈 - 深蓝色背景
            final circleRadius = max(textPainter.width, textPainter.height) / 2 + 2;
            paint.color = const Color(0xFF0D47A1);  // 深蓝色背景色
            canvas.drawCircle(position, circleRadius, paint);
            
            // 绘制无色圈圈
            final circlePaint = Paint()
              ..color = Colors.transparent
              ..style = PaintingStyle.stroke
              ..strokeWidth = 0.5;  // 细线条
            canvas.drawCircle(position, circleRadius, circlePaint);
            
            // 绘制文本
            textPainter.paint(
              canvas,
              Offset(
                position.dx - textPainter.width / 2,
                position.dy - textPainter.height / 2,
              ),
            );
          } else {
            // 正常绘制点
            Color pointColor;
            double finalOpacity = opacity;
            
            // 判断是否为高亮特征点
            if (isHighlight) {
              // 高亮特征点使用红色呼吸效果，透明度从0到半透明
              pointColor = Colors.red; // 使用红色
              
              // 使用闪烁值计算不透明度，透明度从0到1
              finalOpacity = blinkValue; // 透明度从0到1
              
              // 特征点大小固定为1.5px
              pointSize = 1.5; // 固定大小1.5px
              
              Logger.flow('特征点绘制', 'paint', '绘制高亮特征点 索引=$index, 颜色=红色, 不透明度=$finalOpacity, 点大小=$pointSize');
            } else {
              // 非高亮特征点使用正常样式
              pointColor = style['color'] as Color;
              
              // 仅在调试模式下记录非高亮特征点信息
              if (index % 50 == 0) { // 每50个点记录一次，避免日志过多
                Logger.flow('特征点绘制', 'paint', '绘制非高亮特征点 索引=$index, 颜色=${pointColor.toString()}, 不透明度=$finalOpacity');
              }
            }
            
            paint.color = pointColor.withOpacity(finalOpacity);
            canvas.drawCircle(position, pointSize, paint);
          }
        } catch (e) {
          // 保留错误日志，这是重要的调试信息
          Logger.e('特征点绘制', '处理特征点数据时出错: $e');
          continue;
        }
      }
      
      // 绘制对称点对连接线
      if (symmetricPairs != null && symmetricPairs!.isNotEmpty && showSymmetryLines) {
        // 记录所有特征点索引，用于调试
        final allPointIndexes = landmarks.map((lm) => lm['index'] as int).toSet();
        
        // 记录位置映射中的索引信息
        final positionIndexes = pointPositions.keys.toSet();
        
        // 只在首次绘制或特征点数量发生显著变化时输出日志
        // 输出特征点索引范围信息
        {
          Logger.d('特征点绘制', '当前特征点总数: ${landmarks.length}, 索引范围: ${allPointIndexes.isEmpty ? "空" : "${allPointIndexes.reduce(min)}-${allPointIndexes.reduce(max)}"}');
          Logger.d('特征点绘制', '位置映射中的特征点总数: ${positionIndexes.length}, 索引范围: ${positionIndexes.isEmpty ? "空" : "${positionIndexes.reduce(min)}-${positionIndexes.reduce(max)}"}');
        }
        
        // 比较数据中存在但位置映射中不存在的特征点
        final missingInPositions = allPointIndexes.difference(positionIndexes);
        if (missingInPositions.isNotEmpty) {
          Logger.w('特征点绘制', '在数据中存在但在位置映射中不存在的特征点: ${missingInPositions.length}个');
          if (missingInPositions.length <= 20) {
            Logger.w('特征点绘制', '缺失的位置索引: $missingInPositions');
          } else {
            final sortedMissing = missingInPositions.toList()..sort();
            Logger.w('特征点绘制', '部分缺失的位置索引: ${sortedMissing.take(10).toList()}...${sortedMissing.skip(sortedMissing.length - 10).take(10).toList()}');
          }
        }
        
        // 收集所有缺失的点对，用于统计和分析
        final missingPairs = <Map<String, dynamic>>[];
        final existingPairs = <Map<String, dynamic>>[];
        
        // 绘制对称点对之间的连接线
        for (final pair in symmetricPairs!) {
          final int point1Index = pair['point1'] as int;
          final int point2Index = pair['point2'] as int;
          
          // 检查特征点索引是否在有效范围内
          final bool point1Exists = allPointIndexes.contains(point1Index);
          final bool point2Exists = allPointIndexes.contains(point2Index);
          
          // 连接所有对称点对，不再限制只连接高亮的点
          final point1Position = pointPositions[point1Index];
          final point2Position = pointPositions[point2Index];
          
          // 检查点对是否存在
          if (point1Position == null || point2Position == null) {
            // 记录缺失的点对
            final missingInfo = {
              'pair': '$point1Index-$point2Index',
              'point1_exists_in_data': point1Exists,
              'point2_exists_in_data': point2Exists,
              'point1_has_position': point1Position != null,
              'point2_has_position': point2Position != null,
            };
            missingPairs.add(missingInfo);
            
            // 生成缺失点对的唯一键
            final pairKey = '$point1Index-$point2Index';
            
            // 只在首次遇到这个缺失点对时记录日志
            if (!_reportedMissingPairs.contains(pairKey)) {
              Logger.d('特征点绘制', '对称点对缺失: [$point1Index-$point2Index], '
                    'point1在数据中=${point1Exists ? "存在" : "不存在"}, '
                    'point2在数据中=${point2Exists ? "存在" : "不存在"}, '
                    'point1位置=${point1Position != null ? "有效" : "无效"}, '
                    'point2位置=${point2Position != null ? "有效" : "无效"}');
            }
          } else {
            existingPairs.add({'pair': '$point1Index-$point2Index'});
            canvas.drawLine(point1Position, point2Position, linePaint);
          }
        }
        
        // 输出统计信息 - 减少日志频率
        if (missingPairs.isNotEmpty) {
          // 生成缺失对的唯一标识符
          final missingPairsKey = missingPairs.map((p) => p['pair']).join(',');
          
          // 只在缺失对发生变化时输出日志
          if (!_reportedMissingPairs.contains(missingPairsKey)) {
            _reportedMissingPairs.add(missingPairsKey);
            
            // 如果报告的缺失对过多，清理旧的记录
            if (_reportedMissingPairs.length > 10) {
              _reportedMissingPairs.clear();
              _reportedMissingPairs.add(missingPairsKey);
            }
            
            Logger.d('特征点绘制', '共有 ${missingPairs.length} 对对称点缺失，'
                  '${existingPairs.length} 对对称点正常绘制，'
                  '总计 ${symmetricPairs!.length} 对');
            
            // 分析缺失原因
            final missingInData = missingPairs.where((p) => 
                p['point1_exists_in_data'] == false || 
                p['point2_exists_in_data'] == false).length;
            
            final missingPosition = missingPairs.where((p) => 
                p['point1_exists_in_data'] == true && 
                p['point2_exists_in_data'] == true && 
                (p['point1_has_position'] == false || 
                 p['point2_has_position'] == false)).length;
            
            Logger.d('特征点绘制', '缺失原因分析: '
                  '${missingInData}对在特征点数据中不存在, '
                  '${missingPosition}对在位置映射中不存在');
          }
        }
      }
    } catch (e) {
      // 保留错误日志，这是重要的调试信息
      Logger.e('特征点绘制', '绘制特征点时出错: $e');
    }
  }


  
  @override
  bool shouldRepaint(LandmarksPainter oldDelegate) {
    return landmarks != oldDelegate.landmarks ||
           scale != oldDelegate.scale ||
           offset != oldDelegate.offset ||
           imageSize != oldDelegate.imageSize ||
           highlightIndexes != oldDelegate.highlightIndexes ||
           blinkValue != oldDelegate.blinkValue ||
           showIndexes != oldDelegate.showIndexes ||
           symmetricPairs != oldDelegate.symmetricPairs ||
           isPointAnimating != oldDelegate.isPointAnimating ||
           pointAnimationColor != oldDelegate.pointAnimationColor ||
           pointAnimationOpacity != oldDelegate.pointAnimationOpacity ||
           showPointsAfterDeformation != oldDelegate.showPointsAfterDeformation ||
           showSymmetryLines != oldDelegate.showSymmetryLines;
  }
}
