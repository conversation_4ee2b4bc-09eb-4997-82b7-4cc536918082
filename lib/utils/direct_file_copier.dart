import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import '../utils/logger.dart';

/// 直接文件复制工具
/// 
/// 用于直接复制特定文件到指定目录，不依赖于ResourceManager
class DirectFileCopier {
  static const String _logTag = '直接文件复制器';
  
  /// 复制面部特征点检测脚本到运行时目录
  static Future<void> copyFaceMeshProcessor() async {
    Logger.flowStart(_logTag, 'copyFaceMeshProcessor');
    Logger.flow(_logTag, 'copyFaceMeshProcessor', '开始复制面部特征点检测脚本');
    
    try {
      // 源文件路径
      final sourceFile = '/Users/<USER>/beautifun/core/face_mesh_processor.py';
      
      // 目标目录
      final targetDir = '/Users/<USER>/Library/Containers/com.example.beautifun/Data/core';
      final targetFile = path.join(targetDir, 'face_mesh_processor.py');
      
      Logger.flow(_logTag, 'copyFaceMeshProcessor', '源文件: $sourceFile');
      Logger.flow(_logTag, 'copyFaceMeshProcessor', '目标文件: $targetFile');
      
      // 确保目标目录存在
      final targetDirObj = Directory(targetDir);
      if (!await targetDirObj.exists()) {
        Logger.flow(_logTag, 'copyFaceMeshProcessor', '创建目标目录: $targetDir');
        await targetDirObj.create(recursive: true);
      }
      
      // 检查源文件是否存在
      if (!await File(sourceFile).exists()) {
        Logger.flowError(_logTag, 'copyFaceMeshProcessor', '源文件不存在: $sourceFile');
        Logger.flowEnd(_logTag, 'copyFaceMeshProcessor');
        return;
      }
      
      // 复制文件
      await File(sourceFile).copy(targetFile);
      
      // 设置执行权限
      await Process.run('chmod', ['+x', targetFile]);
      
      Logger.flow(_logTag, 'copyFaceMeshProcessor', '✅ 文件复制成功');
      Logger.flowEnd(_logTag, 'copyFaceMeshProcessor');
    } catch (e) {
      Logger.flowError(_logTag, 'copyFaceMeshProcessor', '复制文件失败: $e');
      Logger.flowEnd(_logTag, 'copyFaceMeshProcessor');
    }
  }
}
