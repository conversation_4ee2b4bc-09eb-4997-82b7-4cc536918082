import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import '../utils/logger.dart';

/// 面部特征点桥接类
/// 
/// 用于调用 core 目录下的 Python 功能进行面部特征点检测
class FaceFeatureBridge {
  /// 是否已初始化
  static bool _initialized = false;
  
  /// 是否使用模拟数据
  static bool _useMockData = false;
  
  /// 初始化桥接
  static Future<bool> initialize() async {
    // 如果已经初始化，直接返回成功
    if (_initialized) {
      Logger.log('FaceFeatureBridge', 'initialize', '✅ [服务初始化] 已经初始化，跳过');
      return true;
    }
    
    Logger.log('FaceFeatureBridge', 'initialize', '🔄 [服务初始化] 开始初始化');
    
    // 默认不使用模拟数据
    _useMockData = false;
    
    // 检查Python是否可用
    final pythonResult = await Process.run(
      await _pythonPath,
      ['--version'],
    );
    
    if (pythonResult.exitCode != 0) {
      Logger.log('FaceFeatureBridge', 'initialize', '🚫 [服务初始化] Python 不可用', LogLevel.error);
      Logger.log('FaceFeatureBridge', 'initialize', '   错误信息: ${pythonResult.stderr}', LogLevel.error);
      return false;
    }
    
    Logger.log('FaceFeatureBridge', 'initialize', '✅ [服务初始化] Python 可用: ${pythonResult.stdout}');
    
    // 检查必要的库是否可用
    final requiredLibraries = ['numpy', 'mediapipe'];
    for (final library in requiredLibraries) {
      final importResult = await Process.run(
        await _pythonPath,
        ['-c', 'import $library'],
      );
      
      if (importResult.exitCode != 0) {
        Logger.log('FaceFeatureBridge', 'initialize', '🚫 [服务初始化] 库 $library 不可用', LogLevel.error);
        Logger.log('FaceFeatureBridge', 'initialize', '   错误信息: ${importResult.stderr}', LogLevel.error);
        return false;
      }
      
      Logger.log('FaceFeatureBridge', 'initialize', '✅ [服务初始化] 库 $library 可用');
    }
    
    // 检查脚本文件是否存在
    final containerPath = await _containerDir;
    final scriptPath = '$containerPath/core/face_mesh_processor.py';
    final scriptFile = File(scriptPath);
    
    if (!await scriptFile.exists()) {
      Logger.log('FaceFeatureBridge', 'initialize', '🚫 [服务初始化] 脚本文件不存在: $scriptPath', LogLevel.error);
      return false;
    }
    
    Logger.log('FaceFeatureBridge', 'initialize', '✅ [服务初始化] 脚本文件存在: $scriptPath');
    
    // 测试Python环境
    final testResult = await testPython();
    if (!testResult) {
      Logger.log('FaceFeatureBridge', 'initialize', '🚫 [服务初始化] Python测试失败', LogLevel.error);
      return false;
    }
    
    Logger.log('FaceFeatureBridge', 'initialize', '✅ [服务初始化] Python测试成功');
    
    // 标记为已初始化
    _initialized = true;
    Logger.log('FaceFeatureBridge', 'initialize', '✅ [服务初始化] 初始化完成，不使用模拟数据');
    
    return true;
  }
  
  /// 获取容器目录
  static Future<String> get _containerDir async {
    // 改为使用项目目录而不是容器目录
    final String containerPath = Directory.current.path;
    
    // 检查目录是否存在
    final Directory containerDir = Directory(containerPath);
    final bool exists = await containerDir.exists();
    
    Logger.log('FaceFeatureBridge', '_containerDir', ' [资源管理器] 使用项目目录: $containerPath');
    Logger.log('FaceFeatureBridge', '_containerDir', '   - 目录存在 ${exists ? '✓' : '✗'}');
    
    return containerPath;
  }
  
  /// 获取 Python 路径
  static Future<String> get _pythonPath async {
    // 使用项目虚拟环境中的Python 3.11，已安装mediapipe 0.10.8
    final String pythonPath = '${Directory.current.path}/venv/bin/python3.11';
    
    Logger.log('FaceFeatureBridge', '_pythonPath', '🐍 [服务配置] 获取Python路径');
    Logger.log('FaceFeatureBridge', '_pythonPath', '   Python路径: $pythonPath');
    return pythonPath;
  }
  
  /// 获取 core 目录路径
  static Future<String> get _coreDir async {
    final String containerPath = await _containerDir;
    return '$containerPath/core';
  }
  
  /// 获取 face_mesh_processor.py 文件路径
  static Future<String> get _faceMeshProcessorPath async {
    final String coreDir = await _coreDir;
    final String scriptPath = '$coreDir/face_mesh_processor.py';
    
    Logger.log('FaceFeatureBridge', '_faceMeshProcessorPath', '📜 [服务配置] 获取特征点检测脚本: $scriptPath');
    
    return scriptPath;
  }
  
  /// 获取测试脚本路径
  static Future<String> get _testPythonPath async {
    final String coreDir = await _coreDir;
    return '$coreDir/test_python.py';
  }
  
  /// 获取测试 MediaPipe 脚本路径
  static Future<String> get _testMediaPipePath async {
    final String coreDir = await _coreDir;
    return '$coreDir/test_mediapipe.py';
  }
  
  /// 检测图片中的面部特征点
  /// 
  /// [imagePath] 图片路径
  /// [imageWidth] 图片宽度
  /// [imageHeight] 图片高度
  /// 
  /// 返回特征点列表，如果检测失败则返回 null
  static Future<List<FeaturePoint>?> detectFacialFeatures(
    String imagePath,
    double imageWidth,
    double imageHeight,
  ) async {
    Logger.flowStart('FaceFeatureBridge', '检测特征点');
    Logger.flow('FaceFeatureBridge', '检测特征点', '🔍 [开始] 开始检测面部特征点');
    Logger.flow('FaceFeatureBridge', '检测特征点', 'ℹ️ [信息] 图片路径: $imagePath');
    Logger.flow('FaceFeatureBridge', '检测特征点', 'ℹ️ [信息] 图片尺寸: ${imageWidth}x${imageHeight}');
    
    // 强制禁用模拟数据
    _useMockData = false;
    
    // 检查图片路径是否为空
    if (imagePath.isEmpty) {
      Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] 图片路径为空');
      Logger.flowEnd('FaceFeatureBridge', '检测特征点');
      return null;
    }
    
    // 检查图片是否存在
    final File imageFile = File(imagePath);
    if (!await imageFile.exists()) {
      Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] 图片不存在: $imagePath');
      Logger.flowEnd('FaceFeatureBridge', '检测特征点');
      return null;
    }
    
    try {
      // 获取脚本路径 - 优先使用 face_mesh_processor.py
      final String scriptPath = await _faceMeshProcessorPath;
      
      // 检查脚本是否存在
      final File scriptFile = File(scriptPath);
      if (!await scriptFile.exists()) {
        Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] 特征点检测脚本不存在: $scriptPath');
        Logger.flowEnd('FaceFeatureBridge', '检测特征点');
        return null;
      }
      
      final String pythonPath = await _pythonPath;
      Logger.flow('FaceFeatureBridge', '检测特征点', '💡 [开始] 调用特征点检测脚本:');
      Logger.flow('FaceFeatureBridge', '检测特征点', '   - 脚本路径: $scriptPath');
      Logger.flow('FaceFeatureBridge', '检测特征点', '   - Python路径: $pythonPath');
      
      // 检查Python可执行文件是否存在
      final File pythonFile = File(pythonPath);
      if (!await pythonFile.exists()) {
        Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] Python可执行文件不存在: $pythonPath');
        Logger.flowEnd('FaceFeatureBridge', '检测特征点');
        return null;
      }
      
      try {
        // 运行 Python 脚本 - 根据face_mesh_processor.py的实现，只需传递图片路径
        final ProcessResult result = await Process.run(
          pythonPath,
          [
            scriptPath,
            imagePath,  // 直接传递图片路径作为第一个参数
          ],
          stdoutEncoding: const Utf8Codec(),
          stderrEncoding: const Utf8Codec(),
        );
        
        // 检查执行结果
        if (result.exitCode != 0) {
          Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] 特征点检测失败');
          Logger.flow('FaceFeatureBridge', '检测特征点', '   - 错误信息: ${result.stderr}');
          Logger.flowEnd('FaceFeatureBridge', '检测特征点');
          return null;
        }
        
        // 解析 JSON 结果
        final String output = result.stdout.toString().trim();
        
        if (output.isEmpty) {
          Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] 特征点检测结果为空');
          Logger.flowEnd('FaceFeatureBridge', '检测特征点');
          return null;
        }
        
        Logger.flow('FaceFeatureBridge', '检测特征点', '✅ [成功] 特征点检测成功');
        Logger.flow('FaceFeatureBridge', '检测特征点', '📊 [调用结果] 输出数据大小: ${output.length} 字节');
        
        // 将 JSON 转换为特征点对象
        final Map<String, dynamic> resultJson = jsonDecode(output);
        
        // 检查结果状态
        if (resultJson['status'] != 'success') {
          Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] 特征点检测状态: ${resultJson['status']}');
          Logger.flow('FaceFeatureBridge', '检测特征点', '   - 错误信息: ${resultJson['message'] ?? "未知错误"}');
          Logger.flowEnd('FaceFeatureBridge', '检测特征点');
          return null;
        }
        
        // 获取特征点列表
        final List<dynamic> landmarks = resultJson['landmarks'];
        Logger.flow('FaceFeatureBridge', '检测特征点', '📊 [调用结果] 检测到 ${landmarks.length} 个特征点');
        
        // 记录处理时间
        if (resultJson.containsKey('processing_time')) {
          Logger.flow('FaceFeatureBridge', '检测特征点', '⏱️ [性能] 处理时间: ${resultJson['processing_time']} 秒');
        }
        
        final List<FeaturePoint> featurePoints = _convertToFeaturePoints(landmarks, imageWidth, imageHeight);
        
        Logger.flow('FaceFeatureBridge', '检测特征点', '✅ [成功] 共检测到 ${featurePoints.length} 个特征点');
        
        // 输出前5个特征点的坐标
        for (int i = 0; i < math.min(5, featurePoints.length); i++) {
          final FeaturePoint point = featurePoints[i];
          Logger.flow('FaceFeatureBridge', '检测特征点', '   - 特征点 $i: (${point.position.dx}, ${point.position.dy})');
        }
        
        Logger.flowEnd('FaceFeatureBridge', '检测特征点');
        return featurePoints;
      } catch (e) {
        Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] Python脚本执行失败:');
        Logger.flow('FaceFeatureBridge', '检测特征点', '   - 错误输出: $e');
        Logger.flowEnd('FaceFeatureBridge', '检测特征点');
        return null;
      }
    } catch (e) {
      Logger.flow('FaceFeatureBridge', '检测特征点', '❌ [错误] 特征点检测过程中发生异常:');
      Logger.flow('FaceFeatureBridge', '检测特征点', '   - 错误信息: $e');
      Logger.flowEnd('FaceFeatureBridge', '检测特征点');
      return null;
    }
  }
  
  /// 将 JSON 数据转换为 FeaturePoint 对象列表
  static List<FeaturePoint> _convertToFeaturePoints(
    List<dynamic> data, 
    double imageWidth, 
    double imageHeight,
  ) {
    final List<FeaturePoint> featurePoints = [];
    
    for (int i = 0; i < data.length; i++) {
      final Map<String, dynamic> point = data[i];
      
      // 提取坐标
      final double x = (point['x'] as num).toDouble();
      final double y = (point['y'] as num).toDouble();
      final double z = point['z'] != null ? (point['z'] as num).toDouble() : 0.0;
      
      // 提取可见度
      final double visibility = point['visibility'] != null 
          ? (point['visibility'] as num).toDouble() 
          : 1.0;
      
      // 提取是否为主要特征点
      final bool isPrimary = point['isPrimary'] as bool? ?? false;
      
      // 提取描述
      final String? description = point['description'] as String?;
      
      // 创建 FeaturePoint 对象
      featurePoints.add(FeaturePoint(
        position: Offset(x, y),
        z: z,
        visibility: visibility,
        isPrimary: isPrimary,
        description: description,
      ));
    }
    
    return featurePoints;
  }
  
  /// 测试 Python 环境
  static Future<bool> testPython() async {
    Logger.log('FaceFeatureBridge', 'testPython', '测试 Python 环境');
    
    try {
      // 获取测试脚本路径
      final String scriptPath = await _testPythonPath;
      
      // 检查脚本是否存在
      final File scriptFile = File(scriptPath);
      if (!await scriptFile.exists()) {
        Logger.log('FaceFeatureBridge', 'testPython', '❌ [错误] 测试脚本不存在: $scriptPath', LogLevel.error);
        return false;
      }
      
      try {
        // 执行测试脚本
        final ProcessResult result = await Process.run(
          await _pythonPath,
          [scriptPath],
        );
        
        // 检查执行结果
        if (result.exitCode != 0) {
          Logger.log('FaceFeatureBridge', 'testPython', '❌ [错误] Python 测试失败', LogLevel.error);
          Logger.log('FaceFeatureBridge', 'testPython', '错误信息: ${result.stderr}', LogLevel.error);
          return false;
        }
        
        Logger.log('FaceFeatureBridge', 'testPython', '✅ [成功] Python 测试成功');
        return true;
      } catch (e) {
        Logger.log('FaceFeatureBridge', 'testPython', '❌ [错误] Python 测试异常: $e', LogLevel.error);
        return false;
      }
    } catch (e) {
      Logger.log('FaceFeatureBridge', 'testPython', '❌ [错误] Python 测试异常: $e', LogLevel.error);
      return false;
    }
  }
}

class FeaturePoint {
  final Offset position;
  final double z;
  final double visibility;
  final bool isPrimary;
  final String? description;

  FeaturePoint({
    required this.position,
    required this.z,
    required this.visibility,
    required this.isPrimary,
    this.description,
  });
}
