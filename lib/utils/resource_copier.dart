import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'logger.dart';
import 'resource_manager.dart';

/// 资源复制工具
/// 
/// 负责将项目中的资源文件复制到运行时目录
class ResourceCopier {
  static const String _logTag = '资源复制器';
  
  /// 复制核心脚本文件到运行时目录
  static Future<void> copyCoreScripts() async {
    Logger.flowStart(_logTag, 'copyCoreScripts');
    Logger.flow(_logTag, 'copyCoreScripts', '开始复制核心脚本文件');
    
    try {
      // 获取项目根目录（硬编码，确保能找到源文件）
      final projectDir = '/Users/<USER>/beautifun';
      Logger.flow(_logTag, 'copyCoreScripts', '项目根目录: $projectDir');
      
      // 获取目标目录
      final dataDir = await ResourceManager.dataDir;
      final targetCoreDir = path.join(dataDir, 'core');
      
      // 确保目标目录存在
      final targetCoreDirObj = Directory(targetCoreDir);
      if (!await targetCoreDirObj.exists()) {
        Logger.flow(_logTag, 'copyCoreScripts', '创建目标目录: $targetCoreDir');
        await targetCoreDirObj.create(recursive: true);
      }
      
      // 源目录 - 使用项目根目录下的core目录
      final sourceCoreDir = path.join(projectDir, 'core');
      Logger.flow(_logTag, 'copyCoreScripts', '源目录: $sourceCoreDir');
      
      // 要复制的文件列表
      final scriptFiles = [
        'face_mesh_processor.py',
        'face_analysis_v2.py',
      ];
      
      // 复制每个文件
      for (final scriptFile in scriptFiles) {
        final sourceFile = path.join(sourceCoreDir, scriptFile);
        final targetFile = path.join(targetCoreDir, scriptFile);
        
        // 检查源文件是否存在
        if (await File(sourceFile).exists()) {
          Logger.flow(_logTag, 'copyCoreScripts', '复制文件: $sourceFile -> $targetFile');
          await File(sourceFile).copy(targetFile);
          
          // 设置执行权限
          if (Platform.isMacOS || Platform.isLinux) {
            try {
              Logger.flow(_logTag, 'copyCoreScripts', '设置执行权限: $targetFile');
              await Process.run('chmod', ['+x', targetFile]);
            } catch (e) {
              Logger.flowWarning(_logTag, 'copyCoreScripts', '设置执行权限失败: $e');
            }
          }
          
          Logger.flow(_logTag, 'copyCoreScripts', '✅ 文件复制成功: $scriptFile');
        } else {
          Logger.flowWarning(_logTag, 'copyCoreScripts', '⚠️ 源文件不存在: $sourceFile');
        }
      }
      
      Logger.flow(_logTag, 'copyCoreScripts', '✅ 核心脚本文件复制完成');
    } catch (e) {
      Logger.flowError(_logTag, 'copyCoreScripts', '❌ 复制核心脚本文件失败: $e');
      rethrow;
    } finally {
      Logger.flowEnd(_logTag, 'copyCoreScripts');
    }
  }
  
  /// 复制资源文件到运行时目录
  static Future<void> copyResources() async {
    Logger.flowStart(_logTag, 'copyResources');
    Logger.flow(_logTag, 'copyResources', '开始复制资源文件');
    
    try {
      await copyCoreScripts();
      Logger.flow(_logTag, 'copyResources', '✅ 所有资源文件复制完成');
    } catch (e) {
      Logger.flowError(_logTag, 'copyResources', '❌ 复制资源文件失败: $e');
      rethrow;
    } finally {
      Logger.flowEnd(_logTag, 'copyResources');
    }
  }
}
