import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AnimationManager {
  // 单例实例
  static final AnimationManager instance = AnimationManager._internal();
  
  // 私有构造函数
  AnimationManager._internal();
  
  // 动画控制器
  AnimationController? _scanningController;
  Animation<double>? _scanningAnimation;
  bool _isScanning = false;
  
  void startScanningAnimation() {
    _isScanning = true;
  }
  
  void stopScanningAnimation() {
    _isScanning = false;
  }
  
  Widget buildScanningAnimation() {
    if (!_isScanning) return Container();
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.purple.withOpacity(0.3),
            Colors.transparent,
            Colors.transparent,
            Colors.purple.withOpacity(0.3),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
    );
  }
  
  Widget buildRippleEffect({
    required VoidCallback onTap,
    required Widget child,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          onTap();
        },
        splashColor: Colors.purple.withOpacity(0.3),
        highlightColor: Colors.purple.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        child: child,
      ),
    );
  }
  
  Widget buildTransitionEffect({
    required bool isActive,
    required Widget child,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      transform: Matrix4.identity()
        ..scale(isActive ? 0.98 : 1.0),
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        opacity: isActive ? 0.8 : 1.0,
        child: child,
      ),
    );
  }
}

// 科技感扫描动画
class ScanningEffect extends StatefulWidget {
  final Widget child;
  final bool isScanning;

  const ScanningEffect({
    Key? key,
    required this.child,
    required this.isScanning,
  }) : super(key: key);

  @override
  State<ScanningEffect> createState() => _ScanningEffectState();
}

class _ScanningEffectState extends State<ScanningEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.linear),
    );

    if (widget.isScanning) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(ScanningEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isScanning != oldWidget.isScanning) {
      if (widget.isScanning) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.isScanning)
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Positioned.fill(
                child: ShaderMask(
                  shaderCallback: (bounds) {
                    return LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: const [
                        Colors.transparent,
                        Color(0x337B61FF),
                        Colors.transparent,
                      ],
                      stops: [
                        0.0,
                        0.5,
                        1.0,
                      ],
                      transform: GradientRotation(_animation.value * 3.14),
                    ).createShader(bounds);
                  },
                  child: Container(
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}

// 渐变过渡效果
class TransitionEffect extends StatefulWidget {
  final Widget child;
  final bool isActive;

  const TransitionEffect({
    Key? key,
    required this.child,
    required this.isActive,
  }) : super(key: key);

  @override
  State<TransitionEffect> createState() => _TransitionEffectState();
}

class _TransitionEffectState extends State<TransitionEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    if (widget.isActive) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(TransitionEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.95 + (_animation.value * 0.05),
          child: Opacity(
            opacity: 0.5 + (_animation.value * 0.5),
            child: widget.child,
          ),
        );
      },
    );
  }
}

// 点击波纹效果
class RippleEffect extends StatelessWidget {
  final Widget child;
  final VoidCallback onTap;

  const RippleEffect({
    Key? key,
    required this.child,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // 添加触觉反馈
          HapticFeedback.lightImpact();
          onTap();
        },
        borderRadius: BorderRadius.circular(38),
        splashColor: const Color(0x337B61FF),
        highlightColor: const Color(0x117B61FF),
        child: child,
      ),
    );
  }
}
