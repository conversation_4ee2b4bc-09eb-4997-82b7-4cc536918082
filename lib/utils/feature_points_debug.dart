import 'package:flutter/material.dart';
import '../utils/logger.dart';
import '../core/feature_points_helper.dart';

/// 特征点调试工具类
class FeaturePointsDebug {
  /// 调试特征点处理流程
  static void debugAreaPoints(FeatureAreaType type, List<int> areaPoints, List<Map<String, dynamic>>? landmarks) {
    Logger.i('特征点调试', '开始调试区域特征点');
    Logger.d('特征点调试', '区域类型: $type');
    Logger.d('特征点调试', '收到特征点数量: ${areaPoints.length}');
    
    // 输出特征点索引
    if (areaPoints.isNotEmpty) {
      Logger.d('特征点调试', '特征点索引: $areaPoints');
    } else {
      Logger.e('特征点调试', '未找到特征点');
      return;
    }
    
    // 统计特征点类型
    if (landmarks != null) {
      Map<String, int> typeCount = {'primary': 0, 'secondary': 0, 'auxiliary': 0, 'unknown': 0};
      Map<int, String> pointTypes = {};
      
      // 统计各类型特征点数量
      for (var landmark in landmarks) {
        final index = landmark['index'] as int;
        final type = landmark['type'] as String? ?? 'unknown';
        pointTypes[index] = type;
        typeCount[type] = (typeCount[type] ?? 0) + 1;
      }
      
      // 统计要显示的特征点类型
      Map<String, int> visibleTypeCount = {'primary': 0, 'secondary': 0, 'auxiliary': 0, 'unknown': 0};
      for (var index in areaPoints) {
        final type = pointTypes[index] ?? 'unknown';
        visibleTypeCount[type] = (visibleTypeCount[type] ?? 0) + 1;
      }
      
      Logger.i('特征点调试', '特征点类型统计:');
      Logger.d('特征点调试', '  • 总特征点: ${landmarks.length}');
      Logger.d('特征点调试', '  • 主要点(primary): ${typeCount['primary']}');
      Logger.d('特征点调试', '  • 次要点(secondary): ${typeCount['secondary']}');
      Logger.d('特征点调试', '  • 辅助点(auxiliary): ${typeCount['auxiliary']}');
      Logger.d('特征点调试', '  • 未知类型: ${typeCount['unknown']}');
      
      Logger.i('特征点调试', '要显示的特征点类型统计:');
      Logger.d('特征点调试', '  • 总计: ${areaPoints.length}');
      Logger.d('特征点调试', '  • 主要点(primary): ${visibleTypeCount['primary']}');
      Logger.d('特征点调试', '  • 次要点(secondary): ${visibleTypeCount['secondary']}');
      Logger.d('特征点调试', '  • 辅助点(auxiliary): ${visibleTypeCount['auxiliary']}');
      Logger.d('特征点调试', '  • 未知类型: ${visibleTypeCount['unknown']}');
      
      // 检查太阳穴区域和V脸塑形特征点
      List<int> templePoints = [139, 71, 68, 104, 368, 301, 298, 132, 361, 58, 288, 169, 394];
      List<int> foundTemplePoints = [];
      for (var index in areaPoints) {
        if (templePoints.contains(index)) {
          foundTemplePoints.add(index);
        }
      }
      
      Logger.i('特征点调试', '太阳穴区域特征点检查:');
      Logger.d('特征点调试', '  • 预期太阳穴点: $templePoints');
      Logger.d('特征点调试', '  • 找到太阳穴点: $foundTemplePoints');
      Logger.d('特征点调试', '  • 找到太阳穴点数量: ${foundTemplePoints.length}');
      
      // 检查是否所有特征点都在landmarks中
      List<int> missingPoints = [];
      for (var index in areaPoints) {
        bool found = false;
        for (var landmark in landmarks) {
          if (landmark['index'] == index) {
            found = true;
            break;
          }
        }
        if (!found) {
          missingPoints.add(index);
        }
      }
      
      if (missingPoints.isNotEmpty) {
        Logger.e('特征点调试', '以下特征点在landmarks中未找到: $missingPoints');
      } else {
        Logger.i('特征点调试', '所有特征点在landmarks中都能找到');
      }
    }
    
    Logger.i('特征点调试', '区域特征点调试完成');
  }

  /// 调试参数特征点
  static void debugParameterPointsConfig({
    required List<int> primaryPoints,
    required List<int> secondaryPoints,
    required List<int> auxiliaryPoints,
    List<Map<String, dynamic>>? landmarks,
  }) {
    Logger.i('特征点调试', '开始调试参数特征点');
    
    // 输出特征点数量
    Logger.d('特征点调试', '主要点数量: ${primaryPoints.length}');
    Logger.d('特征点调试', '次要点数量: ${secondaryPoints.length}');
    Logger.d('特征点调试', '辅助点数量: ${auxiliaryPoints.length}');
    
    // 输出特征点索引
    if (primaryPoints.isNotEmpty) {
      Logger.d('特征点调试', '主要点索引: $primaryPoints');
    }
    if (secondaryPoints.isNotEmpty) {
      Logger.d('特征点调试', '次要点索引: $secondaryPoints');
    }
    if (auxiliaryPoints.isNotEmpty) {
      Logger.d('特征点调试', '辅助点索引: $auxiliaryPoints');
    }
    
    // 检查特征点是否存在于landmarks中
    if (landmarks != null) {
      List<int> missingPoints = [];
      final allPoints = [...primaryPoints, ...secondaryPoints, ...auxiliaryPoints];
      
      for (var index in allPoints) {
        bool found = false;
        for (var landmark in landmarks) {
          if (landmark['index'] == index) {
            found = true;
            break;
          }
        }
        if (!found) {
          missingPoints.add(index);
        }
      }
      
      if (missingPoints.isNotEmpty) {
        Logger.e('特征点调试', '以下特征点在landmarks中未找到: $missingPoints');
      } else {
        Logger.i('特征点调试', '所有特征点在landmarks中都能找到');
      }
    }
    
    Logger.i('特征点调试', '参数特征点调试完成');
  }
  
  /// 调试单一参数特征点列表
  static void debugParameterPoints({
    required List<int> parameterPoints,
    List<Map<String, dynamic>>? landmarks,
  }) {
    Logger.i('特征点调试', '开始调试参数特征点');
    
    // 输出特征点数量
    Logger.d('特征点调试', '参数特征点数量: ${parameterPoints.length}');
    
    // 输出特征点索引
    if (parameterPoints.isNotEmpty) {
      Logger.d('特征点调试', '参数特征点索引: $parameterPoints');
    } else {
      Logger.w('特征点调试', '参数特征点列表为空');
    }
    
    // 检查特征点是否存在于landmarks中
    if (landmarks != null) {
      List<int> missingPoints = [];
      
      for (var index in parameterPoints) {
        bool found = false;
        for (var landmark in landmarks) {
          if (landmark['index'] == index) {
            found = true;
            break;
          }
        }
        if (!found) {
          missingPoints.add(index);
        }
      }
      
      if (missingPoints.isNotEmpty) {
        Logger.e('特征点调试', '以下特征点在landmarks中未找到: $missingPoints');
      } else {
        Logger.i('特征点调试', '所有特征点在landmarks中都能找到');
      }
    }
    
    Logger.i('特征点调试', '参数特征点调试完成');
  }
}
