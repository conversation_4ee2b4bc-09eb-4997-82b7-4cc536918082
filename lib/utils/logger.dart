import 'package:flutter/foundation.dart';

/// 日志级别
enum LogLevel {
  verbose, // 详细日志
  debug,   // 调试日志
  info,    // 信息日志
  warning, // 警告日志
  error,   // 错误日志
  none,    // 不输出日志
}

/// 日志过滤模式
enum LogFilterMode {
  all,      // 显示所有日志
  deform,   // 只显示变形相关日志
  cache,    // 只显示缓存相关日志
  accumulate, // 只显示累积相关日志
  deformSystem, // 显示变形、缓存和累积系统相关日志
  keyOnly,  // 只显示关键变形日志（参数变化、缓存命中、变形周期）
  none,     // 不显示日志
}

/// 日志工具类
class Logger {
  /// 当前日志级别
  static LogLevel _currentLevel = LogLevel.verbose;
  
  /// 当前日志过滤模式
  static LogFilterMode _currentFilterMode = LogFilterMode.all;
  
  /// 是否显示时间戳
  static bool _showTimestamp = true;
  
  /// 是否显示日志级别
  static bool _showLogLevel = true;
  
  /// 是否显示标签
  static bool _showTag = true;
  
  /// 设置日志级别
  static void setLogLevel(LogLevel level) {
    _currentLevel = level;
  }
  
  /// 设置日志过滤模式
  static void setFilterMode(LogFilterMode mode) {
    _currentFilterMode = mode;
    print('✨ 日志过滤模式已设置为: $mode');
  }
  
  /// 设置是否显示时间戳
  static void setShowTimestamp(bool show) {
    _showTimestamp = show;
  }
  
  /// 设置是否显示日志级别
  static void setShowLogLevel(bool show) {
    _showLogLevel = show;
  }
  
  /// 设置是否显示标签
  static void setShowTag(bool show) {
    _showTag = show;
  }
  
  /// 检查是否应该过滤日志
  static bool _shouldFilter(String tag, String flowName, String message) {
    // 如果过滤模式为显示所有日志，则不过滤
    if (_currentFilterMode == LogFilterMode.all) {
      return false;
    }
    
    // 如果过滤模式为不显示日志，则过滤所有日志
    if (_currentFilterMode == LogFilterMode.none) {
      return true;
    }
    
    // 变形、缓存和累积系统相关日志
    if (_currentFilterMode == LogFilterMode.deformSystem) {
      // 包含变形相关关键字
      bool isDeformRelated = 
          flowName.contains('setParameterValue') || 
          flowName.contains('_applyParameterValueToFeaturePoints') ||
          message.contains('变形') || 
          message.contains('特征点') ||
          message.contains('面部中心线');
      
      // 包含缓存相关关键字
      bool isCacheRelated = 
          flowName.contains('cache') || 
          message.contains('缓存') || 
          message.contains('命中') ||
          message.contains('未命中');
      
      // 包含累积相关关键字
      bool isAccumulateRelated = 
          message.contains('累积') || 
          message.contains('累积状态') || 
          message.contains('累积基准') ||
          message.contains('累积基础');
      
      // 如果是变形、缓存或累积相关的日志，则不过滤
      return !(isDeformRelated || isCacheRelated || isAccumulateRelated);
    }
    
    // 只显示变形相关日志
    if (_currentFilterMode == LogFilterMode.deform) {
      bool isDeformRelated = 
          flowName.contains('setParameterValue') || 
          flowName.contains('_applyParameterValueToFeaturePoints') ||
          message.contains('变形') || 
          message.contains('特征点') ||
          message.contains('面部中心线');
      
      return !isDeformRelated;
    }
    
    // 只显示缓存相关日志
    if (_currentFilterMode == LogFilterMode.cache) {
      bool isCacheRelated = 
          flowName.contains('cache') || 
          message.contains('缓存') || 
          message.contains('命中') ||
          message.contains('未命中');
      
      return !isCacheRelated;
    }
    
    // 只显示累积相关日志
    if (_currentFilterMode == LogFilterMode.accumulate) {
      bool isAccumulateRelated = 
          message.contains('累积') || 
          message.contains('累积状态') || 
          message.contains('累积基准') ||
          message.contains('累积基础');
      
      return !isAccumulateRelated;
    }
    
    // 只显示关键变形日志
    if (_currentFilterMode == LogFilterMode.keyOnly) {
      // 关键变形日志包含编号标记（如 1.、2.、3.）或嘴角变形调试
      bool isKeyDeformLog = 
          message.contains('1. 点击参数项') || 
          message.contains('2. 参数值变动') || 
          message.contains('3. 全量缓存键命中') || 
          message.contains('3.1 获取缓存') || 
          message.contains('3.2 缓存未命中') || 
          message.contains('3.2 获取累积数据') || 
          message.contains('3.2 变形成功') || 
          message.contains('3.2 变形后保存缓存') || 
          message.contains('5. 开始新一轮变形周期') ||
          // 嘴角上扬调试日志
          message.contains('MouthCornerTransformation') ||
          message.contains('嘴角上扬变形调用') ||
          message.contains('【关键验证】') ||
          tag.contains('MouthCornerTransformation');
      
      // 特征点管理器日志过滤
      if (tag.contains('特征点管理器')) {
        return true; // 过滤掉特征点管理器的所有日志
      }
      
      // 允许EnhancedParameterSlider的日志通过
      if (tag == 'EnhancedParameterSlider' && flowName == '_onParameterValueChanged') {
        return false; // 不过滤EnhancedParameterSlider的参数变化日志
      }
      
      return !isKeyDeformLog;
    }
    
    // 默认不过滤
    return false;
  }
  
  /// 获取当前时间戳
  static String _getTimestamp() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}.${now.millisecond.toString().padLeft(3, '0')}';
  }
  
  /// 获取日志级别标识
  static String _getLevelTag(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return 'V';
      case LogLevel.debug:
        return 'D';
      case LogLevel.info:
        return 'I';
      case LogLevel.warning:
        return 'W';
      case LogLevel.error:
        return 'E';
      default:
        return '?';
    }
  }
  
  /// 输出日志
  static void _log(LogLevel level, String tag, String message, {String flowName = ''}) {
    if (level.index < _currentLevel.index) return;
    
    // 应用过滤逻辑
    if (_shouldFilter(tag, flowName, message)) return;
    
    final buffer = StringBuffer();
    
    // 添加时间戳
    if (_showTimestamp) {
      buffer.write('[${_getTimestamp()}] ');
    }
    
    // 添加日志级别
    if (_showLogLevel) {
      buffer.write('${_getLevelTag(level)} ');
    }
    
    // 添加标签
    if (_showTag && tag.isNotEmpty) {
      buffer.write('[$tag] ');
    }
    
    // 添加消息
    buffer.write(message);
    
    // 直接使用print输出日志，确保实时显示
    // debugPrint有限流机制，可能导致日志输出卡顿
    print(buffer.toString());
  }
  
  /// 输出详细日志
  static void v(String tag, String message) {
    _log(LogLevel.verbose, tag, message);
  }
  
  /// 输出调试日志
  static void d(String tag, String message) {
    _log(LogLevel.debug, tag, message);
  }
  
  /// 输出信息日志
  static void i(String tag, String message) {
    _log(LogLevel.info, tag, message);
  }
  
  /// 输出警告日志
  static void w(String tag, String message) {
    _log(LogLevel.warning, tag, message);
  }
  
  /// 输出错误日志
  static void e(String tag, String message) {
    _log(LogLevel.error, tag, message);
  }
  
  /// 输出带有流程标记的日志
  static void flow(String tag, String flowName, String step) {
    // 应用过滤逻辑
    if (_shouldFilter(tag, flowName, step)) return;
    
    // 使用一致的格式输出日志
    _log(LogLevel.info, tag, '⟹ $flowName: $step', flowName: flowName);
  }
  
  /// 输出流程开始日志
  static void flowStart(String tag, String flowName) {
    // 应用过滤逻辑
    if (_shouldFilter(tag, flowName, '开始')) return;
    
    // 使用一致的格式输出日志
    _log(LogLevel.info, tag, '🔎 开始: $flowName', flowName: flowName);
  }
  
  /// 输出流程结束日志
  static void flowEnd(String tag, String flowName) {
    // 应用过滤逻辑
    if (_shouldFilter(tag, flowName, '完成')) return;
    
    // 使用一致的格式输出日志
    _log(LogLevel.info, tag, '✅ 完成: $flowName', flowName: flowName);
  }
  
  /// 输出流程警告日志
  static void flowWarning(String tag, String flowName, String warning) {
    // 应用过滤逻辑
    if (_shouldFilter(tag, flowName, warning)) return;
    
    // 使用一致的格式输出日志
    _log(LogLevel.warning, tag, '⚠️ 警告($flowName): $warning', flowName: flowName);
  }
  
  /// 输出流程错误日志
  static void flowError(String tag, String flowName, String error) {
    // 应用过滤逻辑
    if (_shouldFilter(tag, flowName, error)) return;
    
    // 使用一致的格式输出日志
    _log(LogLevel.error, tag, '❌ 错误($flowName): $error', flowName: flowName);
  }
  
  /// 通用日志方法，支持旧代码中的 log 调用
  static void log(String tag, String method, String message, [LogLevel level = LogLevel.info]) {
    // 应用过滤逻辑
    if (_shouldFilter(tag, method, message)) return;
    
    // 缓存相关日志使用特殊图标
    if (message.contains('缓存') || message.contains('参数状态')) {
      _log(level, tag, '💾 $method: $message', flowName: method);
      return;
    }
    
    // 累积相关日志使用特殊图标
    if (message.contains('累积')) {
      _log(level, tag, '💾 $method: $message', flowName: method);
      return;
    }
    
    final formattedMessage = '$method: $message';
    _log(level, tag, formattedMessage, flowName: method);
  }
  
  /// 输出信息级别的日志，格式为 "方法: 消息"
  static void logInfo(String tag, String method, String message) {
    log(tag, method, message, LogLevel.info);
  }
  
  /// 输出警告级别的日志，格式为 "方法: 消息"
  static void logWarning(String tag, String method, String message) {
    log(tag, method, message, LogLevel.warning);
  }
  
  /// 输出错误级别的日志，格式为 "方法: 消息"
  static void logError(String tag, String method, String message) {
    log(tag, method, message, LogLevel.error);
  }
}
