import 'package:flutter/foundation.dart';

/// 临时日志工具类
/// 用于在开发过程中输出日志信息
class Logger {
  /// 输出调试级别的日志
  static void d(String tag, String message) {
    if (kDebugMode) {
      print('🔍 [DEBUG] [$tag] $message');
    }
  }
  
  /// 输出信息级别的日志
  static void i(String tag, String message) {
    if (kDebugMode) {
      print('ℹ️ [INFO] [$tag] $message');
    }
  }
  
  /// 输出警告级别的日志
  static void w(String tag, String message) {
    if (kDebugMode) {
      print('⚠️ [WARN] [$tag] $message');
    }
  }
  
  /// 输出错误级别的日志
  static void e(String tag, String message) {
    if (kDebugMode) {
      print('❌ [ERROR] [$tag] $message');
    }
  }
  
  /// 输出日志
  static void log(String tag, String method, String message) {
    if (kDebugMode) {
      print('📝 [$tag.$method] $message');
    }
  }
}
