import 'dart:io';
import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:file_selector/file_selector.dart';
import 'package:path/path.dart' as path;
import 'logger.dart';

class ImageManager {
  static Future<String?> importImage() async {
    try {
      final typeGroup = XTypeGroup(
        label: '图片',
        extensions: ['jpg', 'jpeg', 'png'],
      );
      final file = await openFile(
        acceptedTypeGroups: [typeGroup],
      );
      return file?.path;
    } catch (e) {
      debugPrint('Error importing image: $e');
      return null;
    }
  }

  /// 从测试数据目录加载测试图片
  static Future<String?> loadTestImage() async {
    try {
      // 获取项目目录
      final projectDir = Directory.current.path;
      // 测试图片路径，按照用户规则统一放在 testdata 目录
      final testImagePath = path.join(projectDir, 'testdata', 'test_face.jpg');
      
      Logger.i('图片管理', '加载测试图片:');
      Logger.i('图片管理', '  • 项目目录: $projectDir');
      Logger.i('图片管理', '  • 测试图片路径: $testImagePath');
      
      // 检查文件是否存在
      if (File(testImagePath).existsSync()) {
        debugPrint('加载测试图片: $testImagePath');
        return testImagePath;
      } else {
        debugPrint('测试图片不存在: $testImagePath');
        return null;
      }
    } catch (e) {
      Logger.e('图片管理', '加载测试图片出错:');
      Logger.e('图片管理', '  • 错误信息: $e');
      Logger.e('图片管理', '  • 错误堆栈: ${e.toString()}');
      return null;
    }
  }

  static Future<Map<String, dynamic>?> processImage(String imagePath) async {
    try {
      debugPrint('\n=== 开始处理图片: ${imagePath.split('/').last} ===');
      debugPrint('输入参数: imagePath = $imagePath');
      
      // 由于移除了image_pipeline.dart的依赖，这里暂时返回空Map
      // 后续需要根据实际需求实现新的处理逻辑
      debugPrint('警告: 图片处理功能待实现');
      return {};
    } catch (e) {
      debugPrint('处理图片时发生错误: $e');
      return null;
    }
  }

  static Map<String, dynamic> _parseFeatures(String output) {
    // TODO: 实现特征点数据解析逻辑
    // 这里需要根据实际的特征点数据结构来调整
    return {};
  }

  static Widget buildImageWidget(
    String imagePath, {
    Map<String, dynamic>? features,
    BoxFit fit = BoxFit.cover,
  }) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: Image.file(
          File(imagePath),
          fit: BoxFit.cover, // 强制使用cover以填满整个区域
          width: double.infinity,
          height: double.infinity,
        ),
      ),
    );
  }

  static Future<Size> _getImageSize(ImageProvider imageProvider) {
    final completer = Completer<Size>();
    imageProvider.resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((info, _) {
        completer.complete(Size(
          info.image.width.toDouble(),
          info.image.height.toDouble(),
        ));
      }),
    );
    return completer.future;
  }

  static Rect? _getFaceRect(Map<String, dynamic> features, Size imageSize) {
    try {
      debugPrint('\n=== 计算头部区域 ===');
      final faceRect = features['face_rect'];
      if (faceRect == null) return null;

      // 获取面部区域信息
      final faceLeft = faceRect['left'].toDouble();
      final faceTop = faceRect['top'].toDouble();
      final faceWidth = faceRect['width'].toDouble();
      final faceHeight = faceRect['height'].toDouble();
      
      // 扩展系数
      const VERTICAL_TOP_RATIO = 1.5;     // 向上扩展150%
      const VERTICAL_BOTTOM_RATIO = 0.5;  // 向下扩展50%
      const HORIZONTAL_RATIO = 0.8;       // 两侧各扩展80%
      
      // 计算扩展尺寸
      final topExtension = faceHeight * VERTICAL_TOP_RATIO;
      final bottomExtension = faceHeight * VERTICAL_BOTTOM_RATIO;
      final sideExtension = faceWidth * HORIZONTAL_RATIO;
      
      // 计算头部区域
      final headRect = Rect.fromLTRB(
        math.max(0.0, faceLeft - sideExtension),
        math.max(0.0, faceTop - topExtension),
        math.min(imageSize.width, faceLeft + faceWidth + sideExtension),
        math.min(imageSize.height, faceTop + faceHeight + bottomExtension)
      );
      
      debugPrint('面部区域: ($faceLeft, $faceTop, ${faceLeft + faceWidth}, ${faceTop + faceHeight})');
      debugPrint('扩展系数: 上${VERTICAL_TOP_RATIO}x, 下${VERTICAL_BOTTOM_RATIO}x, 侧${HORIZONTAL_RATIO}x');
      debugPrint('最终头部区域: ${headRect.toString()}');
      
      return headRect;
    } catch (e) {
      debugPrint('计算头部区域时发生错误: $e');
      return null;
    }
  }
}
