import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

class ResourceManager {
  static bool _initialized = false;
  static const String _containerPath = '/Users/<USER>/Library/Containers/com.example.beautifun/Data';
  static const _coreDir = 'core';
  static const _pythonScripts = [
    'face_mesh_processor.py',
    'face_analysis_v2.py',
  ];

  static Future<String> get _appSupportDirectory async {
    if (Platform.isMacOS) {
      final containerDir = Directory(_containerPath);
      final exists = await containerDir.exists();
      debugPrint(' [资源管理器] 检查容器目录: ${containerDir.path}');
      debugPrint('   - 目录${exists ? "存在 " : "不存在 "}');
      
      if (exists) {
        return _containerPath;
      }
      
      debugPrint(' [资源管理器] 容器目录不存在，尝试创建');
      try {
        await containerDir.create(recursive: true);
        return _containerPath;
      } catch (e) {
        debugPrint(' [资源管理器] 无法创建容器目录: $e');
        debugPrint(' [资源管理器] 回退到应用支持目录');
        final appSupport = await getApplicationSupportDirectory();
        return appSupport.path;
      }
    }
    throw UnimplementedError('不支持的平台');
  }

  static Future<String> get dataDir => _appSupportDirectory;

  static Future<String> get pythonScriptPath async {
    final baseDir = await _appSupportDirectory;
    return p.join(baseDir, _coreDir, _pythonScripts[0]);
  }

  static Future<String> get pythonInterpreterPath async {
    final baseDir = await _appSupportDirectory;
    return p.join(baseDir, 'venv', 'bin', 'python3.11');
  }

  static Future<void> _copyFile(String source, String target) async {
    try {
      final sourceFile = File(source);
      final targetFile = File(target);
      
      if (await sourceFile.exists()) {
        // 确保目标目录存在
        final targetDir = Directory(p.dirname(target));
        if (!await targetDir.exists()) {
          await targetDir.create(recursive: true);
        }
        
        await sourceFile.copy(target);
        // 设置执行权限
        if (p.extension(source) == '.py' || p.basename(source) == 'python3.11') {
          await Process.run('chmod', ['+x', target]);
        }
        debugPrint(' [资源管理器] 成功复制文件: ${p.basename(source)}');
        debugPrint('   从: $source');
        debugPrint('   到: $target');
      } else {
        throw Exception('源文件不存在: $source');
      }
    } catch (e) {
      debugPrint(' [资源管理器] 复制文件失败: ${p.basename(source)}');
      debugPrint('   错误: $e');
      rethrow;
    }
  }

  static Future<void> initialize() async {
    if (_initialized) {
      debugPrint(' [资源管理器] 已经初始化');
      return;
    }

    try {
      final baseDir = await _appSupportDirectory;
      debugPrint(' [资源管理器] 使用基础目录: $baseDir');
      
      // 创建必要的目录
      final coreDir = Directory(p.join(baseDir, _coreDir));
      final venvBinDir = Directory(p.join(baseDir, 'venv', 'bin'));
      
      await coreDir.create(recursive: true);
      await venvBinDir.create(recursive: true);
      
      debugPrint(' [资源管理器] 目录结构:');
      debugPrint('   - 核心目录: ${coreDir.path}');
      debugPrint('   - Python目录: ${venvBinDir.path}');

      // 复制Python脚本
      final sourceDir = Directory('core');
      if (!await sourceDir.exists()) {
        throw Exception('源目录不存在: ${sourceDir.path}');
      }

      for (final script in _pythonScripts) {
        final sourceFile = File(p.join(sourceDir.path, script));
        final targetFile = File(p.join(coreDir.path, script));

        if (!await sourceFile.exists()) {
          throw Exception('源Python脚本不存在: ${sourceFile.path}');
        }

        debugPrint(' [资源管理器] 复制脚本:');
        debugPrint('   - 源: ${sourceFile.path}');
        debugPrint('   - 目标: ${targetFile.path}');

        await sourceFile.copy(targetFile.path);
        
        // 设置执行权限
        if (Platform.isMacOS || Platform.isLinux) {
          await Process.run('chmod', ['+x', targetFile.path]);
        }
      }

      // 复制Python解释器
      debugPrint(' [资源管理器] 复制Python解释器...');
      final interpreterPath = await pythonInterpreterPath;
      final projectRoot = Directory.current.path;
      final sourceInterpreter = p.join(projectRoot, 'venv', 'bin', 'python3.11');
      
      debugPrint(' [资源管理器] Python解释器路径:');
      debugPrint('   - 源: $sourceInterpreter');
      debugPrint('   - 目标: $interpreterPath');
      
      await _copyFile(sourceInterpreter, interpreterPath);

      // 验证文件
      debugPrint(' [资源管理器] 验证文件...');
      
      // 验证Python脚本
      for (final script in _pythonScripts) {
        final file = File(p.join(coreDir.path, script));
        final exists = await file.exists();
        final size = exists ? await file.length() : 0;
        final executable = exists ? (await Process.run('test', ['-x', file.path])).exitCode == 0 : false;
        
        debugPrint('   - $script:');
        debugPrint('     • 存在: ${exists ? "✓" : "✗"}');
        debugPrint('     • 大小: ${size}字节');
        debugPrint('     • 可执行: ${executable ? "✓" : "✗"}');
        
        if (!exists || size == 0) {
          throw Exception('Python脚本验证失败: $script');
        }
      }

      // 验证Python解释器
      final interpreter = File(interpreterPath);
      final exists = await interpreter.exists();
      final size = exists ? await interpreter.length() : 0;
      final executable = exists ? (await Process.run('test', ['-x', interpreter.path])).exitCode == 0 : false;
      
      debugPrint('   - Python解释器:');
      debugPrint('     • 存在: ${exists ? "✓" : "✗"}');
      debugPrint('     • 大小: ${size}字节');
      debugPrint('     • 可执行: ${executable ? "✓" : "✗"}');
      
      if (!exists || size == 0 || !executable) {
        throw Exception('Python解释器验证失败');
      }

      _initialized = true;
      debugPrint(' [资源管理器] 初始化完成');
    } catch (e) {
      debugPrint(' [资源管理器] 初始化失败: $e');
      _initialized = false;
      rethrow;
    }
  }

  static Future<void> cleanup() async {
    try {
      debugPrint(' [资源管理器] 开始清理...');
      
      final baseDir = await _appSupportDirectory;
      final coreDir = Directory(p.join(baseDir, _coreDir));
      
      if (await coreDir.exists()) {
        await coreDir.delete(recursive: true);
        debugPrint(' [资源管理器] 清理完成');
      }
    } catch (e) {
      debugPrint(' [资源管理器] 清理失败: $e');
      rethrow;
    }
  }

  static Future<void> ensureResourcesExist() async {
    if (!_initialized) {
      await initialize();
    }
  }
}
