import 'dart:io';

class ProcessManager {
  static Future<void> killExistingInstances() async {
    try {
      // 先找到所有相关进程
      final List<String> processesToKill = [];
      
      // 1. 使用 lsof 查找 BeautiFun 相关进程
      final lsofResult = await Process.run('lsof', ['-ti', '-c', 'BeautiFun']);
      if (lsofResult.stdout.toString().isNotEmpty) {
        processesToKill.addAll(
          lsofResult.stdout.toString().trim().split('\n')
        );
      }
      
      // 2. 使用 lsof 查找 Flutter 相关进程
      final flutterResult = await Process.run('lsof', ['-ti', '-c', 'Flutter']);
      if (flutterResult.stdout.toString().isNotEmpty) {
        processesToKill.addAll(
          flutterResult.stdout.toString().trim().split('\n')
        );
      }
      
      // 获取当前进程ID
      final currentPid = pid.toString();
      
      // 移除当前进程
      processesToKill.remove(currentPid);
      
      if (processesToKill.isEmpty) {
        print('No existing instances found.');
        return;
      }
      
      print('Found ${processesToKill.length} processes to kill: $processesToKill');
      
      // 先尝试正常终止
      for (var pid in processesToKill) {
        if (pid.isNotEmpty) {
          try {
            print('Attempting to terminate process: $pid');
            await Process.run('kill', ['-TERM', pid]);
          } catch (e) {
            print('Error sending TERM signal to $pid: $e');
          }
        }
      }
      
      // 等待进程退出
      await Future.delayed(const Duration(seconds: 1));
      
      // 检查是否还有进程存活
      bool hasRemainingProcesses = false;
      for (var pid in processesToKill) {
        if (pid.isNotEmpty) {
          try {
            final checkResult = await Process.run('lsof', ['-p', pid]);
            if (checkResult.exitCode == 0) {
              hasRemainingProcesses = true;
              print('Process $pid still running, force killing...');
              await Process.run('kill', ['-9', pid]);
            }
          } catch (e) {
            // 如果进程已经不存在，这是正常的
            print('Process $pid already terminated');
          }
        }
      }
      
      if (hasRemainingProcesses) {
        // 再次等待确保所有进程都已终止
        await Future.delayed(const Duration(seconds: 1));
        
        // 最后检查一次
        for (var pid in processesToKill) {
          if (pid.isNotEmpty) {
            try {
              final finalCheck = await Process.run('lsof', ['-p', pid]);
              if (finalCheck.exitCode == 0) {
                throw Exception('Failed to kill process $pid');
              }
            } catch (e) {
              // 进程已终止
              continue;
            }
          }
        }
      }
      
      print('All existing instances have been terminated.');
      
    } catch (e) {
      print('Error in process management: $e');
      rethrow;
    }
  }
}
