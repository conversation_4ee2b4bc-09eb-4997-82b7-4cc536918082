import 'package:flutter/foundation.dart';

/// 日志级别
enum LogLevel {
  verbose,
  debug,
  info,
  warning,
  error
}

/// BeautiFun应用专用日志工具类
class BeautifyLogger {
  /// 记录详细日志
  /// 
  /// [module] 模块名称
  /// [function] 函数名称
  /// [message] 日志消息
  static void logVerbose(String module, String function, String message) {
    _log(module, function, message, LogLevel.verbose);
  }
  
  /// 记录调试日志
  /// 
  /// [module] 模块名称
  /// [function] 函数名称
  /// [message] 日志消息
  static void logDebug(String module, String function, String message) {
    _log(module, function, message, LogLevel.debug);
  }
  
  /// 记录信息日志
  /// 
  /// [module] 模块名称
  /// [function] 函数名称
  /// [message] 日志消息
  static void logInfo(String module, String function, String message) {
    _log(module, function, message, LogLevel.info);
  }
  
  /// 记录警告日志
  /// 
  /// [module] 模块名称
  /// [function] 函数名称
  /// [message] 日志消息
  static void logWarning(String module, String function, String message) {
    _log(module, function, message, LogLevel.warning);
  }
  
  /// 记录错误日志
  /// 
  /// [module] 模块名称
  /// [function] 函数名称
  /// [message] 日志消息
  /// [error] 错误对象（可选）
  static void logError(String module, String function, String message, [dynamic error]) {
    _log(module, function, message, LogLevel.error, error);
  }
  
  /// 内部日志记录方法
  /// 
  /// [module] 模块名称
  /// [function] 函数名称
  /// [message] 日志消息
  /// [level] 日志级别
  /// [error] 错误对象（可选）
  static void _log(String module, String function, String message, LogLevel level, [dynamic error]) {
    final String timestamp = DateTime.now().toString();
    final String formattedMessage = '[$timestamp] [${_getLevelTag(level)}] [$module] [$function] | $message';
    
    switch (level) {
      case LogLevel.verbose:
        _v(module, formattedMessage);
        break;
      case LogLevel.debug:
        _d(module, formattedMessage);
        break;
      case LogLevel.info:
        _i(module, formattedMessage);
        break;
      case LogLevel.warning:
        _w(module, formattedMessage);
        break;
      case LogLevel.error:
        _e(module, formattedMessage, error);
        break;
    }
  }
  
  /// 获取日志级别标签
  static String _getLevelTag(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return 'VERBOSE';
      case LogLevel.debug:
        return 'DEBUG';
      case LogLevel.info:
        return 'INFO';
      case LogLevel.warning:
        return 'WARNING';
      case LogLevel.error:
        return 'ERROR';
    }
  }
  
  // 简化方法别名，兼容旧代码
  /// 记录信息日志（简化方法）
  static void i(String tag, String message) {
    _i(tag, message);
  }
  
  /// 记录调试日志（简化方法）
  static void d(String tag, String message) {
    _d(tag, message);
  }
  
  /// 记录详细日志（简化方法）
  static void v(String tag, String message) {
    _v(tag, message);
  }
  
  /// 记录警告日志（简化方法）
  static void w(String tag, String message) {
    _w(tag, message);
  }
  
  /// 记录错误日志（简化方法）
  static void e(String tag, String message, [dynamic error]) {
    _e(tag, message, error);
  }
  
  /// 记录一般日志（简化方法）
  static void log(String tag, String function, String message) {
    logInfo(tag, function, message);
  }
  
  // 流程日志方法
  /// 记录流程开始
  static void flowStart(String tag, String flowName) {
    _i(tag, "⏺️ 开始流程: $flowName");
  }
  
  /// 记录流程步骤
  static void flow(String tag, String flowName, String stepMessage) {
    _i(tag, "➡️ 流程[$flowName]: $stepMessage");
  }
  
  /// 记录流程结束
  static void flowEnd(String tag, String flowName) {
    _i(tag, "⏹️ 结束流程: $flowName");
  }
  
  /// 记录流程错误
  static void flowError(String tag, String flowName, String errorMessage) {
    _e(tag, "❌ 流程[$flowName]错误: $errorMessage");
  }
  
  static void _i(String tag, String message) {
    if (kDebugMode) {
      print('💡 [$tag] $message');
    }
  }
  
  static void _d(String tag, String message) {
    if (kDebugMode) {
      print('🔍 [$tag] $message');
    }
  }
  
  static void _v(String tag, String message) {
    if (kDebugMode) {
      print('📝 [$tag] $message');
    }
  }
  
  static void _w(String tag, String message) {
    if (kDebugMode) {
      print('⚠️ [$tag] $message');
    }
  }
  
  static void _e(String tag, String message, [dynamic error]) {
    if (kDebugMode) {
      print('❌ [$tag] $message');
      if (error != null) {
        print('   Error: $error');
      }
    }
  }
}
