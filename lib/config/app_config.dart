import 'package:flutter/material.dart';
import '../utils/logger.dart';
import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';

/// 应用全局配置
class AppConfig {
  static const String _logTag = 'AppConfig';
  
  /// 单例实例
  static final AppConfig _instance = AppConfig._internal();
  
  /// 获取单例实例
  static AppConfig get instance => _instance;
  
  /// 私有构造函数
  AppConfig._internal() {
    Logger.i(_logTag, '初始化应用配置');
  }
  
  /// 变形步长 - 每次点击加减按钮时应用的变形值增量
  double _transformationStepSize = 0.2;
  
  /// 获取变形步长
  double get transformationStepSize => _transformationStepSize;
  
  /// 设置变形步长
  set transformationStepSize(double value) {
    if (value <= 0) {
      Logger.e(_logTag, '变形步长必须大于0，尝试设置的值: $value');
      return;
    }
    Logger.i(_logTag, '设置变形步长: $_transformationStepSize -> $value');
    _transformationStepSize = value;
    
    // 保存配置
    saveConfig().then((_) {
      Logger.d(_logTag, '变形步长已保存到配置文件');
    }).catchError((e) {
      Logger.e(_logTag, '保存变形步长到配置文件失败: $e');
    });
  }
  
  /// 配置文件路径
  String? _configFilePath;
  
  /// 初始化配置
  Future<void> initialize() async {
    Logger.flowStart(_logTag, 'initialize');
    Logger.flow(_logTag, 'initialize', '开始初始化配置');
    
    try {
      // 获取应用文档目录
      final directory = await getApplicationDocumentsDirectory();
      _configFilePath = '${directory.path}/beautifun_config.json';
      Logger.flow(_logTag, 'initialize', '配置文件路径: $_configFilePath');
      
      // 检查配置文件是否存在
      final configFile = File(_configFilePath!);
      if (await configFile.exists()) {
        // 读取配置文件
        final jsonString = await configFile.readAsString();
        final Map<String, dynamic> configMap = json.decode(jsonString);
        Logger.flow(_logTag, 'initialize', '读取到配置: $configMap');
        
        // 加载变形步长
        if (configMap.containsKey('transformationStepSize')) {
          final double stepSize = configMap['transformationStepSize'];
          if (stepSize > 0) {
            _transformationStepSize = stepSize;
            Logger.flow(_logTag, 'initialize', '从配置文件加载变形步长: $_transformationStepSize');
          } else {
            Logger.flowWarning(_logTag, 'initialize', '配置文件中的变形步长无效: $stepSize，使用默认值: $_transformationStepSize');
          }
        }
      } else {
        Logger.flow(_logTag, 'initialize', '配置文件不存在，使用默认配置');
        // 创建默认配置文件
        await saveConfig();
      }
    } catch (e) {
      Logger.flowError(_logTag, 'initialize', '初始化配置异常: $e');
    }
    
    Logger.flow(_logTag, 'initialize', '配置初始化完成，当前变形步长: $_transformationStepSize');
    Logger.flowEnd(_logTag, 'initialize');
  }
  
  /// 保存配置
  Future<void> saveConfig() async {
    Logger.flowStart(_logTag, 'saveConfig');
    Logger.flow(_logTag, 'saveConfig', '开始保存配置');
    
    try {
      if (_configFilePath == null) {
        final directory = await getApplicationDocumentsDirectory();
        _configFilePath = '${directory.path}/beautifun_config.json';
      }
      
      // 创建配置映射
      final Map<String, dynamic> configMap = {
        'transformationStepSize': _transformationStepSize,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      
      // 转换为JSON字符串
      final jsonString = json.encode(configMap);
      
      // 写入文件
      final configFile = File(_configFilePath!);
      await configFile.writeAsString(jsonString);
      
      Logger.flow(_logTag, 'saveConfig', '配置已保存到: $_configFilePath');
      Logger.flow(_logTag, 'saveConfig', '保存的配置: $configMap');
    } catch (e) {
      Logger.flowError(_logTag, 'saveConfig', '保存配置异常: $e');
    }
    
    Logger.flowEnd(_logTag, 'saveConfig');
  }
  
  /// 获取配置信息字符串
  String getConfigInfo() {
    return '应用配置:\n'
        '- 变形步长: $_transformationStepSize';
  }
}
