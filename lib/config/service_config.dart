import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import '../utils/resource_manager.dart';

class ServiceConfig {
  static Future<void> initialize() async {
    await ResourceManager.initialize();
  }

  static Future<String> get pythonPath async {
    // 使用项目虚拟环境中的Python 3.11，已安装mediapipe 0.10.21
    final dataDir = await ResourceManager.dataDir;
    final pythonPath = path.join(dataDir, 'venv', 'bin', 'python3.11');
    debugPrint('🐍 [服务配置] 获取Python路径');
    debugPrint('   Python路径: $pythonPath');
    return pythonPath;
  }

  static Future<String> get corePath async {
    final dataDir = await ResourceManager.dataDir;
    final corePath = path.join(dataDir, 'core');
    debugPrint('📁 [服务配置] 获取核心目录: $corePath');
    return corePath;
  }

  static Future<String> get faceMeshScript async {
    final coreDir = await corePath;
    final scriptPath = path.join(coreDir, 'face_mesh_processor.py');
    debugPrint('📜 [服务配置] 获取特征点检测脚本: $scriptPath');
    return scriptPath;
  }

  static Future<String> get faceAnalysisScript async {
    final coreDir = await corePath;
    final scriptPath = path.join(coreDir, 'face_analysis_v2.py');
    debugPrint('📜 [服务配置] 获取面部分析脚本: $scriptPath');
    return scriptPath;
  }

  static Future<void> validatePaths() async {
    try {
      debugPrint('🔍 [服务配置] 验证路径...');
      
      final paths = [
        await pythonPath,
        await faceMeshScript,
        await faceAnalysisScript,
      ];

      for (final p in paths) {
        final file = File(p);
        if (!await file.exists()) {
          debugPrint('❌ [服务配置] 文件不存在: $p');
          throw Exception('文件不存在: $p');
        }
        debugPrint('✅ [服务配置] 文件存在: $p');
      }

      debugPrint('✅ [服务配置] 所有路径验证通过');
    } catch (e) {
      debugPrint('❌ [服务配置] 路径验证失败: $e');
      rethrow;
    }
  }
}
