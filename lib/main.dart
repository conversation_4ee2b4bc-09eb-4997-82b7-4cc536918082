import 'package:flutter/material.dart';
import 'package:window_size/window_size.dart';
import 'dart:io';
import 'screens/main_screen.dart';
import 'screens/image_import_screen.dart';
import 'screens/mediapipe_test_screen.dart';
import 'utils/process_manager.dart';
import 'utils/resource_copier.dart';
import 'utils/direct_file_copier.dart';
import 'package:provider/provider.dart';
import 'models/image_state.dart';
import 'core/services/image_processing_service.dart';
import 'beautify_feature/services/transformation_service.dart';
import 'config/app_config.dart';
import 'utils/log_helper.dart';  // 导入日志辅助工具
import 'core/parameter_value_manager.dart';  // 导入参数值管理器

Future<void> main() async {
  try {
    // 首先确保Flutter绑定初始化
    WidgetsFlutterBinding.ensureInitialized();
    
    // 设置显示所有日志模式以检查MouthCornerTransformation输出
    // LogHelper.showKeyDeformLogsOnly();  // 暂时注释掉
    
    // 启用关键变形日志，屏蔽特征点动画日志
    LogHelper.showKeyDeformLogsOnly(); 
    print('✅ 已设置关键变形日志模式（用于调试MouthCornerTransformation）');
    
    // 确保参数值管理器中没有任何预设参数值
    ParameterValueManager().clearAllParameters();
    print('✅ 已清除参数值管理器中的所有参数值');
    
    // 初始化应用配置
    print('Initializing app configuration...');
    try {
      await AppConfig.instance.initialize();
      print('Successfully initialized app configuration');
      print('Transformation step size: ${AppConfig.instance.transformationStepSize}');
    } catch (e) {
      print('Failed to initialize app configuration: $e');
      // 使用默认配置继续执行
      print('Using default configuration');
    }
    
    // 初始化图像处理服务
    print('Initializing image processing service...');
    try {
      await ImageProcessingService().initializeService();
      print('Successfully initialized image processing service');
    } catch (e) {
      print('Failed to initialize image processing service: $e');
      exit(1);
    }
    
    // 复制必要的资源文件到运行时目录
    print('Copying resource files...');
    try {
      await ResourceCopier.copyResources();
      print('Successfully copied resource files');
    } catch (e) {
      print('Failed to copy resource files: $e');
      // 继续执行，不退出程序
    }
    
    // 直接复制面部特征点检测脚本
    print('Directly copying face mesh processor script...');
    try {
      await DirectFileCopier.copyFaceMeshProcessor();
      print('Successfully copied face mesh processor script');
    } catch (e) {
      print('Failed to copy face mesh processor script: $e');
      // 继续执行，不退出程序
    }
    
    print('Killing existing instances...');
    // 立即关闭已存在的实例
    try {
      await ProcessManager.killExistingInstances();
      print('Successfully killed existing instances');
    } catch (e) {
      print('Failed to kill existing instances: $e');
      exit(1); // 退出程序
    }
    
    // 等待一下确保进程完全关闭
    await Future.delayed(const Duration(seconds: 1));
    
    if (Platform.isMacOS) {
      print('Configuring window size...');
      // 获取屏幕尺寸
      final screen = await getCurrentScreen();
      if (screen != null) {
        // iPad mini 的宽高比
        const ipadRatio = 2266 / 1488;
        
        // 计算窗口大小（屏幕的80%）
        final screenHeight = screen.visibleFrame.height;
        final screenWidth = screen.visibleFrame.width;
        
        // 根据屏幕宽高比决定是以宽度还是高度为基准
        double windowWidth, windowHeight;
        if (screenWidth / screenHeight > ipadRatio) {
          // 屏幕较宽，以高度为基准
          windowHeight = screenHeight * 0.8;
          windowWidth = windowHeight * ipadRatio;
        } else {
          // 屏幕较窄，以宽度为基准
          windowWidth = screenWidth * 0.8;
          windowHeight = windowWidth / ipadRatio;
        }
        
        // 设置最小和最大窗口大小
        final size = Size(windowWidth, windowHeight);
        setWindowMinSize(size);
        setWindowMaxSize(size);
        
        // 计算窗口位置，使其居中
        final left = (screenWidth - windowWidth) / 2;
        final top = (screenHeight - windowHeight) / 2;
        
        // 设置窗口位置和大小
        setWindowFrame(Rect.fromLTWH(left, top, windowWidth, windowHeight));
      }
      
      // 设置窗口标题
      setWindowTitle('BeautiFun');
    }

    print('Starting application...');
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => ImageState()),
          ChangeNotifierProvider(create: (_) => TransformationService.instance),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e) {
    print('Fatal error during startup: $e');
    exit(1);
  }
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'BeautiFun',
      debugShowCheckedModeBanner: false,
      routes: {
        '/': (context) => const MainScreen(),
        '/image_import': (context) => const ImageImportScreen(),
        '/mediapipe_test': (context) => const MediapipeTestScreen(),
      },
      theme: ThemeData(
        primaryColor: const Color(0xFF7B61FF),
        scaffoldBackgroundColor: const Color(0xFFF5F0FF),
      ),
    );
  }
}
