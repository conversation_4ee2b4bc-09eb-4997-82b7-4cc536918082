import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/image_state.dart';
import '../widgets/preview_area/preview_area.dart';
import '../widgets/preview_area/image_display.dart';
import '../beautify_feature/services/transformation_service.dart';
import '../beautify_feature/ui/center_panel/comparison_controller.dart';
import '../core/services/image_processing_service.dart';
import '../core/services/logger_service.dart';
import '../beautify_feature/services/calibration_service.dart';
import '../widgets/panels/left_panel.dart';
import '../widgets/panels/right_panel.dart';
import '../utils/logger.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({Key? key}) : super(key: key);

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with SingleTickerProviderStateMixin {
  /// 是否处于扩展状态
  bool _expanded = false; // 默认为收起状态
  
  /// 动画控制器
  late AnimationController _animationController;
  
  /// 动画曲线
  late Animation<double> _animation;

  /// 预览区域的全局键
  final GlobalKey<State<PreviewArea>> _previewAreaKey = GlobalKey();

  /// 图片显示区域的全局键
  final GlobalKey<State<ImageDisplay>> _imageDisplayKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    Logger.i('主屏幕', '初始化主屏幕');
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800), // 丝滑缓慢的动画
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut, // 使用缓入缓出曲线使动画更丝滑
    );
    
    // 延迟一帧后添加图片状态监听
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _addImageStateListener();
    });
  }
  
  /// 添加图片状态监听
  void _addImageStateListener() {
    final imageState = Provider.of<ImageState>(context, listen: false);
    imageState.addListener(_onImageStateChanged);
    Logger.i('主屏幕', '已添加图片状态监听');
  }
  
  /// 图片状态变化处理
  void _onImageStateChanged() {
    final imageState = Provider.of<ImageState>(context, listen: false);
    
    // 如果图片路径发生变化，且控制面板处于展开状态，则自动收起控制面板
    if (imageState.frontImagePath != null && _expanded) {
      Logger.i('主屏幕', '检测到图片变化，自动收起控制面板');
      // 使用延迟确保UI更新完成
      Future.delayed(const Duration(milliseconds: 100), () {
        _toggleExpanded();
      });
    }
  }
  
  @override
  void dispose() {
    // 移除图片状态监听
    final imageState = Provider.of<ImageState>(context, listen: false);
    imageState.removeListener(_onImageStateChanged);
    
    _animationController.dispose();
    Logger.i('主屏幕', '销毁主屏幕');
    super.dispose();
  }

  /// 切换扩展状态
  void _toggleExpanded() {
    Logger.i('主屏幕', '切换扩展状态: ${_expanded ? '收起' : '展开'}');
    
    // 如果当前是收起状态，即将展开，则触发缩略图淡出动画
    if (!_expanded) {
      // 获取预览区域状态并触发缩略图淡出动画
      final previewAreaState = _previewAreaKey.currentState;
      if (previewAreaState != null) {
        Logger.i('主屏幕', '触发缩略图淡出动画');
        (previewAreaState as dynamic).startThumbnailFadeAnimation();
      }
    }
    
    setState(() {
      _expanded = !_expanded;
      if (_expanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    
    // 计算最佳预览区宽度
    final availableHeight = screenSize.height - 32;
    final phoneAspectRatio = 1170 / 2532;
    final phoneWidth = availableHeight * phoneAspectRatio;
    
    // 确保宽度不超过屏幕宽度的 80%
    final maxWidth = screenWidth * 0.8;
    final finalWidth = phoneWidth < maxWidth ? phoneWidth : maxWidth;
    
    // 计算左右面板的宽度
    final panelWidth = screenWidth * 0.3; // 左右面板各占30%的宽度
    
    // 计算中央区域的位置
    final centerX = screenWidth / 2;
    final leftPanelStartX = centerX - finalWidth / 2;
    final rightPanelStartX = centerX + finalWidth / 2;
    
    Logger.d('主屏幕', '构建UI: 屏幕宽度=$screenWidth, 面板宽度=$panelWidth, iPhone宽度=$finalWidth');

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => TransformationService.instance),
        ChangeNotifierProvider(create: (_) => ComparisonController()),
      ],
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: const Color(0xFF7B61FF),
          title: const Text('BeautiFun'),
          actions: [
            IconButton(
              icon: const Icon(Icons.science),
              tooltip: 'MediaPipe测试',
              onPressed: () {
                Navigator.pushNamed(context, '/mediapipe_test');
              },
            ),
          ],
        ),
        body: Container(
          color: const Color(0xFFF5F0FF),
          child: Stack(
            children: [
              // 中央预览区域 - 始终显示，放在最底层确保可点击
              Center(
                child: SizedBox(
                  width: finalWidth,
                  child: PreviewArea(
                    key: _previewAreaKey,
                    imageDisplayKey: _imageDisplayKey,
                    showBeautifyButton: true,
                    onBeautifyPressed: _toggleExpanded,
                  ),
                ),
              ),
              
              // 左侧面板 - 从中央向左滑出
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  // 计算左侧面板的起始X位置（从中央预览区左边缘开始）
                  final startX = leftPanelStartX;
                  // 计算目标X位置（左侧面板最终位置）
                  final endX = 0.0;
                  // 计算当前X位置
                  final currentX = startX - (startX - endX) * _animation.value;
                  
                  return Positioned(
                    left: currentX,
                    width: panelWidth,
                    top: 0,
                    bottom: 0,
                    child: IgnorePointer(
                      ignoring: !_expanded, // 只有展开时才接收点击事件
                      child: Opacity(
                        opacity: _animation.value,
                        child: Container(
                          width: panelWidth, // 确保宽度固定为屏幕宽度的30%
                          decoration: BoxDecoration(
                            color: Color.lerp(
                              Colors.transparent, 
                              const Color(0xFF2A2A2A),
                              _animation.value,
                            ),
                          ),
                          child: LeftPanel(imageDisplayKey: _imageDisplayKey),
                        ),
                      ),
                    ),
                  );
                },
              ),
              
              // 右侧面板 - 从中央向右滑出
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  // 计算右侧面板的起始X位置（从中央预览区右边缘开始）
                  final startX = rightPanelStartX;
                  // 计算目标X位置（右侧面板最终位置）
                  final endX = screenWidth - panelWidth; 
                  // 计算当前X位置
                  final currentX = startX + (endX - startX) * _animation.value;
                  
                  return Positioned(
                    left: currentX,
                    width: panelWidth,
                    top: 0,
                    bottom: 0,
                    child: IgnorePointer(
                      ignoring: !_expanded, // 只有展开时才接收点击事件
                      child: Opacity(
                        opacity: _animation.value,
                        child: Container(
                          width: panelWidth,
                          decoration: BoxDecoration(
                            color: Color.lerp(
                              Colors.transparent, 
                              const Color(0xFF2A2A2A),
                              _animation.value,
                            ),
                          ),
                          child: const RightPanel(),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
