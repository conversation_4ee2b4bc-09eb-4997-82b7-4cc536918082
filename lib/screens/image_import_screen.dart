import 'dart:io';
import 'dart:math' as Math;
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../utils/logger.dart';
import 'dart:ui' as ui;
import '../services/face_mesh_service.dart';
import 'package:provider/provider.dart';
import '../models/image_state.dart';

/// 图片导入和显示界面
class ImageImportScreen extends StatefulWidget {
  const ImageImportScreen({Key? key}) : super(key: key);

  @override
  State<ImageImportScreen> createState() => _ImageImportScreenState();
}

class _ImageImportScreenState extends State<ImageImportScreen> with SingleTickerProviderStateMixin {
  File? _selectedImageFile;
  ui.Image? _displayImage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    Logger.i('图片导入界面', '初始化图片导入界面');
    
  }
  
  @override
  void dispose() {
    super.dispose();
  }

  /// 导入图片
  Future<void> _importImage() async {
    try {
      setState(() {
        _isLoading = true;
      });
      
      Logger.i('图片导入界面', '开始导入图片');
      
      // 打开文件选择器
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );
      
      if (result != null && result.files.isNotEmpty) {
        final path = result.files.first.path;
        if (path != null) {
          Logger.i('图片导入界面', '选择了图片: $path');
          
          // 设置选中的图片文件
          final file = File(path);
          setState(() {
            _selectedImageFile = file;
          });
          
          // 加载图片
          await _loadImage(file);
        }
      } else {
        Logger.i('图片导入界面', '用户取消了图片选择');
      }
    } catch (e) {
      Logger.flowError('图片导入界面', '导入图片', '出错: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 加载图片
  Future<void> _loadImage(File file) async {
    try {
      Logger.flowStart('图片导入界面', '_loadImage');
      Logger.flow('图片导入界面', '_loadImage', '🔍 开始加载图片: ${file.path}');
      
      // 读取图片文件
      final bytes = await file.readAsBytes();
      Logger.flow('图片导入界面', '_loadImage', '📦 图片文件大小: ${bytes.length} 字节');
      
      // 解码图片 - 不设置目标尺寸，确保加载原始尺寸的图像
      final codec = await ui.instantiateImageCodec(
        bytes,
        targetWidth: 1024,  // 强制设置目标宽度
        targetHeight: 1478  // 强制设置目标高度
      );
      final frame = await codec.getNextFrame();
      
      setState(() {
        _displayImage = frame.image;
      });
      
      Logger.flow('图片导入界面', '_loadImage', '📏 图片加载成功: ${_displayImage!.width} x ${_displayImage!.height}');
      
      // 输出图片详细信息到控制台，确保在任何模式下都能看到
      print('======== 原始图片信息 ========');
      print('📷 原始图像详细信息:');
      print('  - 哈希码: ${_displayImage!.hashCode}');
      print('  - 宽度: ${_displayImage!.width}');
      print('  - 高度: ${_displayImage!.height}');
      print('  - 内存地址: ${_displayImage.toString()}');
      print('  - 估计内存占用: ${(_displayImage!.width * _displayImage!.height * 4) / 1024} KB');
      print('  - 记录时间: ${DateTime.now().toIso8601String()}');
      print('  - 期望尺寸: 1024x1478');
      print('  - 尺寸匹配: ${_displayImage!.width == 1024 && _displayImage!.height == 1478 ? "是" : "否"}');
      print('============================');
      
      // 添加确认按钮，允许用户确认选择此图片
      _showConfirmationDialog(file.path);
      Logger.flowEnd('图片导入界面', '_loadImage');
    } catch (e) {
      Logger.flowError('图片导入界面', '_loadImage', '❌ 出错: $e');
      Logger.flowEnd('图片导入界面', '_loadImage');
    }
  }

  /// 显示确认对话框
  void _showConfirmationDialog(String imagePath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认使用此图片'),
        content: const Text('是否确认使用此图片？确认后将返回主界面并应用此图片。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              // 关闭对话框
              Navigator.of(context).pop();
              
              // 将图片路径传递给主应用程序并返回主界面
              _confirmImage(imagePath);
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }

  /// 确认使用选中的图片
  void _confirmImage(String imagePath) {
    Logger.i('图片导入界面', '确认使用图片: $imagePath');
    
    // 使用Provider更新ImageState
    final imageState = Provider.of<ImageState>(context, listen: false);
    imageState.setFrontImage(imagePath);
    
    // 返回主界面
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图片导入'),
        centerTitle: true,
        backgroundColor: Colors.blueGrey[800],
      ),
      body: Container(
        color: Colors.grey[200],
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 顶部导入按钮
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _importImage,
              icon: const Icon(Icons.image),
              label: Text(_isLoading ? '导入中...' : '导入图片'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                backgroundColor: Colors.blue[700],
                foregroundColor: Colors.white,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 图片显示区域
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey[400]!),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _displayImage != null
                        ? _buildImageDisplay()
                        : const Center(
                            child: Text(
                              '请导入图片',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                              ),
                            ),
                          ),
              ),
            ),
            
            // 底部信息区域
            if (_displayImage != null)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Text(
                  '图片尺寸: ${_displayImage!.width} x ${_displayImage!.height}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建图片显示组件
  Widget _buildImageDisplay() {
    return Center(
      child: LayoutBuilder(
        builder: (context, constraints) {
          if (_displayImage == null) return const SizedBox.shrink();
          
          // 计算图片实际显示尺寸
          final imageAspectRatio = _displayImage!.width / _displayImage!.height;
          final viewAspectRatio = constraints.maxWidth / constraints.maxHeight;
          
          double displayWidth;
          double displayHeight;
          
          if (imageAspectRatio > viewAspectRatio) {
            // 图片比视图更宽，以宽度为基准
            displayWidth = constraints.maxWidth;
            displayHeight = constraints.maxWidth / imageAspectRatio;
          } else {
            // 图片比视图更高，以高度为基准
            displayHeight = constraints.maxHeight;
            displayWidth = constraints.maxHeight * imageAspectRatio;
          }
          
          Logger.i('图片导入界面', '图片显示尺寸: ${displayWidth.toInt()} x ${displayHeight.toInt()}');
          
          return Stack(
            children: [
              // 图片显示
              SizedBox(
                width: displayWidth,
                height: displayHeight,
                child: CustomPaint(
                  painter: ImageDisplayPainter(image: _displayImage!),
                  size: Size(displayWidth, displayHeight),
                ),
              ),
              
            ],
          );
        },
      ),
    );
  }
  
}

/// 图片显示绘制器
class ImageDisplayPainter extends CustomPainter {
  final ui.Image image;
  
  ImageDisplayPainter({required this.image});
  
  @override
  void paint(Canvas canvas, Size size) {
    try {
      // 计算缩放比例
      final scaleX = size.width / image.width;
      final scaleY = size.height / image.height;
      final scale = scaleX < scaleY ? scaleX : scaleY;
      
      // 计算偏移量，使图片居中
      final scaledWidth = image.width * scale;
      final scaledHeight = image.height * scale;
      final dx = (size.width - scaledWidth) / 2;
      final dy = (size.height - scaledHeight) / 2;
      
      // 设置绘制区域
      final rect = Rect.fromLTWH(dx, dy, scaledWidth, scaledHeight);
      
      // 绘制图片 - 使用高质量模式
      final paint = Paint()
        ..filterQuality = FilterQuality.high
        ..isAntiAlias = true;
        
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
        rect,
        paint,
      );
      
      Logger.i('图片显示绘制器', '绘制图片: 原始尺寸${image.width}x${image.height}, 缩放尺寸${scaledWidth.toInt()}x${scaledHeight.toInt()}');
      Logger.i('图片显示绘制器', '绘制区域: 左上角(${dx.toInt()}, ${dy.toInt()}), 右下角(${(dx + scaledWidth).toInt()}, ${(dy + scaledHeight).toInt()})');
    } catch (e) {
      Logger.flowError('图片显示绘制器', '绘制图片', '失败: $e');
    }
  }
  
  @override
  bool shouldRepaint(covariant ImageDisplayPainter oldDelegate) {
    return oldDelegate.image != image;
  }
}
