import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as path;
import '../utils/logger.dart';
import '../config/service_config.dart';

class FaceMeshService {
  /// 获取面部特征点
  Future<List<List<double>>> getFacialLandmarks(String imagePath) async {
    try {
      Logger.i('面部特征点服务', '开始获取特征点:');
      Logger.i('面部特征点服务', '  - 图片路径: $imagePath');

      final scriptPath = await ServiceConfig.faceMeshScript;
      final pythonPath = await ServiceConfig.pythonPath;
      
      Logger.i('面部特征点服务', '调用特征点检测脚本:');
      Logger.i('面部特征点服务', '  - 脚本路径: $scriptPath');
      Logger.i('面部特征点服务', '  - Python路径: $pythonPath');

      // 检查脚本文件是否存在
      if (!await File(scriptPath).exists()) {
        Logger.flowError('面部特征点服务', '特征点检测脚本', '不存在: $scriptPath');
        throw Exception('特征点检测脚本不存在: $scriptPath');
      }

      // 准备JSON参数
      final params = {
        'input': imagePath,
      };
      final paramsJson = json.encode(params);

      // 调用Python脚本处理图片
      final result = await Process.run(pythonPath, [
        scriptPath,
        '--params',
        "'$paramsJson'", // 用单引号包裹JSON字符串
      ]);

      if (result.exitCode != 0) {
        Logger.flowError('面部特征点服务', '特征点检测', '失败: ${result.stderr}');
        throw Exception('特征点检测失败: ${result.stderr}');
      }

      // 检查输出是否为空
      final stdout = result.stdout as String;
      if (stdout.trim().isEmpty) {
        Logger.flowError('面部特征点服务', '特征点检测', '返回空结果');
        throw Exception('特征点检测返回空结果');
      }
      
      // 输出原始结果以便于调试
      Logger.d('面部特征点服务', '原始输出: $stdout');

      // 尝试解析JSON结果
      Map<String, dynamic> jsonResult;
      try {
        final dynamic parsedJson = json.decode(stdout);
        if (parsedJson == null) {
          Logger.flowError('面部特征点服务', '特征点检测', '返回null结果');
          throw Exception('特征点检测返回null结果');
        }
        
        if (parsedJson is! Map<String, dynamic>) {
          Logger.flowError('面部特征点服务', '特征点检测', '返回的不是有效的JSON对象');
          throw Exception('特征点检测返回的不是有效的JSON对象');
        }
        
        jsonResult = parsedJson;
      } catch (e) {
        Logger.flowError('面部特征点服务', '解析特征点检测结果', '失败: $e');
        throw Exception('解析特征点检测结果失败: $e');
      }
      
      // 检查状态
      if (jsonResult['status'] == 'no_face') {
        Logger.flowError('面部特征点服务', '特征点检测', '未检测到人脸');
        throw Exception('未检测到人脸');
      }
      
      if (jsonResult['status'] == 'error') {
        final errorMsg = jsonResult['message'] ?? '特征点检测失败';
        Logger.flowError('面部特征点服务', '特征点检测', errorMsg);
        throw Exception(errorMsg);
      }
      
      // 检查图像尺寸是否存在
      if (!jsonResult.containsKey('image_size') || jsonResult['image_size'] == null) {
        Logger.w('面部特征点服务', '特征点检测结果中缺少图像尺寸信息，使用默认值');
        // 使用默认的图像尺寸
        jsonResult['image_size'] = {
          'width': 1280.0,
          'height': 720.0
        };
      }
      
      // 检查图像尺寸是否是有效的Map
      final imageSize = jsonResult['image_size'];
      if (imageSize is! Map<String, dynamic>) {
        Logger.flowError('面部特征点服务', '图像尺寸', '格式无效');
        throw Exception('图像尺寸格式无效');
      }
      
      // 检查宽度和高度是否存在
      if (!imageSize.containsKey('width') || !imageSize.containsKey('height')) {
        Logger.flowError('面部特征点服务', '图像尺寸', '信息不完整');
        throw Exception('图像尺寸信息不完整');
      }
      
      // 安全地获取宽度和高度
      final width = (imageSize['width'] is num) ? (imageSize['width'] as num).toDouble() : 0.0;
      final height = (imageSize['height'] is num) ? (imageSize['height'] as num).toDouble() : 0.0;
      
      if (width <= 0 || height <= 0) {
        Logger.flowError('面部特征点服务', '图像尺寸', '无效: 宽度=$width, 高度=$height');
        throw Exception('图像尺寸无效: 宽度=$width, 高度=$height');
      }
      
      Logger.i('面部特征点服务', '图像尺寸:');
      Logger.i('面部特征点服务', '  - 宽度: $width');
      Logger.i('面部特征点服务', '  - 高度: $height');
      
      // 检查特征点列表是否存在
      if (!jsonResult.containsKey('landmarks') || jsonResult['landmarks'] == null) {
        Logger.w('面部特征点服务', '特征点检测结果中缺少特征点数据，使用默认值');
        // 创建一个空的特征点列表
        jsonResult['landmarks'] = [];
      }
      
      // 检查特征点列表是否是有效的List
      final landmarksList = jsonResult['landmarks'];
      if (landmarksList is! List<dynamic>) {
        Logger.flowError('面部特征点服务', '特征点数据', '格式无效');
        throw Exception('特征点数据格式无效');
      }
      
      // 安全地处理特征点列表
      final landmarks = landmarksList.map((point) {
        try {
          if (point is! Map<String, dynamic>) {
            Logger.w('面部特征点服务', '跳过无效的特征点数据');
            return [0.0, 0.0, 0.0, width, height]; // 返回默认值
          }
          
          // 安全地获取坐标值
          final x = (point['x'] is num) ? (point['x'] as num).toDouble() : 0.0;
          final y = (point['y'] is num) ? (point['y'] as num).toDouble() : 0.0;
          final z = (point['z'] is num) ? (point['z'] as num).toDouble() : 0.0;
          final index = (point['index'] is int) ? point['index'] as int : 0;
          final visibility = (point['visibility'] is num) ? point['visibility'] as num : 0.0;
          
          // 归一化坐标（转换为0-1范围）
          final normalizedX = x / width;
          final normalizedY = y / height;
          final normalizedZ = z / width; // 使用宽度作为Z轴的归一化基准
          
          Logger.d('面部特征点服务', '特征点 $index 坐标归一化:');
          Logger.d('面部特征点服务', '  - 原始: ($x, $y, $z)');
          Logger.d('面部特征点服务', '  - 归一化: ($normalizedX, $normalizedY, $normalizedZ)');
          
          return [normalizedX, normalizedY, normalizedZ, width, height];
        } catch (e) {
          Logger.flowError('面部特征点服务', '处理特征点数据', '失败: $e');
          return [0.0, 0.0, 0.0, width, height]; // 返回默认值
        }
      }).toList();
      
      if (landmarks.isEmpty) {
        Logger.flowError('面部特征点服务', '提取特征点', '失败');
        throw Exception('未能提取有效的特征点数据');
      }
      
      Logger.i('面部特征点服务', '特征点检测完成:');
      Logger.i('面部特征点服务', '  - 特征点数量: ${landmarks.length}');

      return landmarks;
    } catch (e) {
      Logger.flowError('面部特征点服务', '特征点检测', '失败: $e');
      rethrow;
    }
  }

  /// 检测特征点（getFacialLandmarks的别名）
  Future<List<List<double>>> detectFeatures(String imagePath) {
    return getFacialLandmarks(imagePath);
  }
}
