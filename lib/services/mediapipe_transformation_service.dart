import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../core/mediapipe_deformation_engine.dart';
import '../core/face_landmark_mapper.dart';
import '../core/delaunay_triangulation.dart';
import '../core/platform_adapters/ml_kit_adapter.dart';

/// MediaPipe变形服务
/// 
/// 负责使用MediaPipe提取面部特征点并应用变形效果
class MediapipeTransformationService {
  /// MediaPipe变形引擎
  final MediapipeDeformationEngine _deformationEngine = MediapipeDeformationEngine();
  
  /// 面部检测器
  final FaceDetector _faceDetector = MLKitAdapter.createFaceDetector(
    enableContours: true,
    enableLandmarks: true,
    enableClassification: true,
    enableTracking: false,
    performanceMode: FaceDetectorMode.accurate,
  );
  
  /// 当前图像路径
  String? _currentImagePath;
  
  /// 当前检测到的面部特征点
  Map<int, Offset>? _currentLandmarks;
  
  /// 当前变形参数
  final Map<String, double> _currentParams = {};
  
  /// 获取当前图像路径
  String? get currentImagePath => _currentImagePath;
  
  /// 获取当前特征点
  Map<int, Offset>? get currentLandmarks => _currentLandmarks;
  
  /// 设置图像
  /// 
  /// [imagePath] 图像路径
  Future<bool> setImage(String imagePath) async {
    try {
      // 检查文件是否存在
      final File imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        print('图像文件不存在: $imagePath');
        return false;
      }
      
      _currentImagePath = imagePath;
      
      // 使用适配器提取特征点
      final List<Face> faces = await MLKitAdapter.processImage(_faceDetector, imagePath);
      
      if (faces.isEmpty) {
        print('未检测到人脸');
        return false;
      }
      
      // 获取第一个检测到的人脸
      final Face face = faces.first;
      
      // 提取面部特征点
      _currentLandmarks = _extractFaceLandmarks(face);
      
      return true;
    } catch (e) {
      print('设置图像时出错: $e');
      return false;
    }
  }
  
  /// 提取面部特征点
  Map<int, Offset> _extractFaceLandmarks(Face face) {
    final Map<int, Offset> landmarks = {};
    
    // 提取面部轮廓点
    if (face.contours[FaceContourType.face] != null) {
      final points = face.contours[FaceContourType.face]!.points;
      for (int i = 0; i < points.length; i++) {
        landmarks[i] = Offset(points[i].x.toDouble(), points[i].y.toDouble());
      }
    }
    
    // 提取左眼点
    if (face.contours[FaceContourType.leftEye] != null) {
      final points = face.contours[FaceContourType.leftEye]!.points;
      for (int i = 0; i < points.length; i++) {
        landmarks[100 + i] = Offset(points[i].x.toDouble(), points[i].y.toDouble());
      }
    }
    
    // 提取右眼点
    if (face.contours[FaceContourType.rightEye] != null) {
      final points = face.contours[FaceContourType.rightEye]!.points;
      for (int i = 0; i < points.length; i++) {
        landmarks[200 + i] = Offset(points[i].x.toDouble(), points[i].y.toDouble());
      }
    }
    
    // 提取鼻子点
    if (face.contours[FaceContourType.noseBridge] != null) {
      final points = face.contours[FaceContourType.noseBridge]!.points;
      for (int i = 0; i < points.length; i++) {
        landmarks[300 + i] = Offset(points[i].x.toDouble(), points[i].y.toDouble());
      }
    }
    
    // 提取嘴唇点
    if (face.contours[FaceContourType.lowerLipBottom] != null) {
      final points = face.contours[FaceContourType.lowerLipBottom]!.points;
      for (int i = 0; i < points.length; i++) {
        landmarks[400 + i] = Offset(points[i].x.toDouble(), points[i].y.toDouble());
      }
    }
    
    return landmarks;
  }
  
  /// 更新变形参数
  /// 
  /// [region] 区域名称
  /// [paramName] 参数名称
  /// [value] 参数值 (-1.0 到 1.0)
  void updateTransformationParam(String region, String paramName, double value) {
    final String key = '${region}_$paramName';
    _currentParams[key] = value;
  }
  
  /// 应用变形
  /// 
  /// 返回变形后的图像路径
  Future<String?> applyTransformation() async {
    if (_currentImagePath == null || _currentLandmarks == null) {
      print('未设置图像或未检测到特征点');
      return null;
    }
    
    try {
      // 计算变形向量
      final Map<int, Offset> deformations = _calculateDeformations();
      
      // 应用变形
      final DeformationResult result = await _deformationEngine.applyDeformation(
        imagePath: _currentImagePath!,
        deformations: deformations,
      );
      
      if (!result.success) {
        print('应用变形失败: ${result.errorMessage}');
        return null;
      }
      
      return result.outputPath;
    } catch (e) {
      print('应用变形时出错: $e');
      return null;
    }
  }
  
  /// 计算变形向量
  Map<int, Offset> _calculateDeformations() {
    final Map<int, Offset> deformations = {};
    
    // 对每个参数计算变形
    _currentParams.forEach((key, value) {
      final List<String> parts = key.split('_');
      if (parts.length < 2) return;
      
      final String region = parts[0];
      final String paramName = parts.sublist(1).join('_');
      
      // 获取区域对应的MediaPipe特征点索引
      final List<int> indices = FaceLandmarkMapper.getMediapipeIndicesForRegion(region);
      
      // 根据参数名称和值计算变形
      switch ('${region}_$paramName') {
        // 眼部变形
        case 'eyes_double_fold':
          _applyEyeDoubleFoldTransform(deformations, indices, value);
          break;
        case 'eyes_canthal_tilt':
          _applyEyeCanthalTiltTransform(deformations, indices, value);
          break;
        case 'eyes_eye_bag_removal':
          _applyEyeBagRemovalTransform(deformations, indices, value);
          break;
        case 'eyes_outer_corner_lift':
          _applyEyeOuterCornerLiftTransform(deformations, indices, value);
          break;
          
        // 鼻部变形
        case 'nose_bridge_height':
          _applyNoseBridgeHeightTransform(deformations, indices, value);
          break;
        case 'nose_tip_adjust':
          _applyNoseTipAdjustTransform(deformations, indices, value);
          break;
        case 'nose_nostril_width':
          _applyNoseNostrilWidthTransform(deformations, indices, value);
          break;
        case 'nose_base_height':
          _applyNoseBaseHeightTransform(deformations, indices, value);
          break;
          
        // 面部轮廓变形
        case 'face_contour_contour_tighten':
          _applyFaceContourTightenTransform(deformations, indices, value);
          break;
        case 'face_contour_chin_adjust':
          _applyFaceChinAdjustTransform(deformations, indices, value);
          break;
        case 'face_contour_cheekbone_adjust':
          _applyFaceCheekboneAdjustTransform(deformations, indices, value);
          break;
        case 'face_contour_face_shape':
          _applyFaceShapeTransform(deformations, indices, value);
          break;
          
        // 唇部变形
        case 'lips_lip_shape':
          _applyLipShapeTransform(deformations, indices, value);
          break;
        case 'lips_mouth_corner':
          _applyMouthCornerTransform(deformations, indices, value);
          break;
          
        // 抗衰老变形
        case 'anti_aging_nasolabial_folds':
          _applyNasolabialFoldsTransform(deformations, indices, value);
          break;
        case 'anti_aging_wrinkle_removal':
          _applyWrinkleRemovalTransform(deformations, indices, value);
          break;
        case 'anti_aging_forehead_fullness':
          _applyForeheadFullnessTransform(deformations, indices, value);
          break;
        case 'anti_aging_facial_firmness':
          _applyFacialFirmnessTransform(deformations, indices, value);
          break;
      }
    });
    
    return deformations;
  }
  
  // 以下是各种变形效果的实现
  
  /// 应用双眼皮变形
  void _applyEyeDoubleFoldTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左眼上缘点
    final List<int> leftEyeUpperIndices = [133, 157, 158, 159, 160, 161, 173];
    // 右眼上缘点
    final List<int> rightEyeUpperIndices = [362, 384, 385, 386, 387, 388, 398];
    
    // 计算变形向量 - 增强效果
    for (final int index in indices) {
      if (leftEyeUpperIndices.contains(index)) {
        deformations[index] = Offset(0, -5.0 * value);
      } else if (rightEyeUpperIndices.contains(index)) {
        deformations[index] = Offset(0, -5.0 * value);
      }
    }
  }
  
  /// 应用开眼角变形
  void _applyEyeCanthalTiltTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左眼内角和外角
    deformations[33] = Offset(0, -1.0 * value);
    deformations[159] = Offset(1.0 * value, -1.5 * value);
    
    // 右眼内角和外角
    deformations[362] = Offset(0, -1.0 * value);
    deformations[263] = Offset(-1.0 * value, -1.5 * value);
  }
  
  /// 应用去眼袋变形
  void _applyEyeBagRemovalTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左眼下方区域
    final List<int> leftEyeBagIndices = [110, 117, 118, 119, 120, 121, 128];
    // 右眼下方区域
    final List<int> rightEyeBagIndices = [338, 345, 346, 347, 348, 349, 357];
    
    // 计算变形向量
    for (final int index in indices) {
      if (leftEyeBagIndices.contains(index)) {
        deformations[index] = Offset(0, 2.0 * value);
      } else if (rightEyeBagIndices.contains(index)) {
        deformations[index] = Offset(0, 2.0 * value);
      }
    }
  }
  
  /// 应用提眼尾变形
  void _applyEyeOuterCornerLiftTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左眼外角
    deformations[159] = Offset(0.5 * value, -2.0 * value);
    deformations[160] = Offset(0.5 * value, -1.5 * value);
    
    // 右眼外角
    deformations[263] = Offset(-0.5 * value, -2.0 * value);
    deformations[387] = Offset(-0.5 * value, -1.5 * value);
  }
  
  /// 应用鼻梁高度变形
  void _applyNoseBridgeHeightTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 鼻梁点
    final List<int> noseBridgeIndices = [4, 5, 6, 197, 195, 196];
    
    // 计算变形向量 - 增强效果
    for (final int index in noseBridgeIndices) {
      if (indices.contains(index)) {
        deformations[index] = Offset(0, -4.0 * value);
      }
    }
  }
  
  /// 应用鼻尖调整变形
  void _applyNoseTipAdjustTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 鼻尖点
    deformations[1] = Offset(0, -2.0 * value);
    deformations[2] = Offset(0, -1.5 * value);
    deformations[3] = Offset(0, -1.5 * value);
  }
  
  /// 应用鼻翼宽度变形
  void _applyNoseNostrilWidthTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左鼻翼
    deformations[94] = Offset(-2.0 * value, 0);
    deformations[129] = Offset(-1.5 * value, 0);
    
    // 右鼻翼
    deformations[324] = Offset(2.0 * value, 0);
    deformations[358] = Offset(1.5 * value, 0);
  }
  
  /// 应用鼻基抬高变形
  void _applyNoseBaseHeightTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 鼻基部点
    final List<int> noseBaseIndices = [94, 129, 1, 2, 3, 324, 358];
    
    // 计算变形向量
    for (final int index in noseBaseIndices) {
      if (indices.contains(index)) {
        deformations[index] = Offset(0, -1.0 * value);
      }
    }
  }
  
  /// 应用轮廓收紧变形
  void _applyFaceContourTightenTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左脸轮廓点
    final List<int> leftContourIndices = [10, 21, 54, 103, 127, 162, 169, 170, 171, 132, 58];
    // 右脸轮廓点
    final List<int> rightContourIndices = [323, 330, 332, 333, 338, 341, 345, 352, 356, 361, 288, 394];
    
    // V脸塑形点
    final List<int> vFacePoints = [169, 394, 132, 361, 58, 288];
    
    // 计算变形向量
    for (final int index in indices) {
      if (leftContourIndices.contains(index)) {
        // 左脸轮廓点向内移动
        deformations[index] = Offset(2.0 * value, 0);
        
        // 对V脸塑形点的特殊处理
        if (index == 169) {
          deformations[index] = Offset(2.2 * value, 0.5 * value); // 左面部下部轮廓点
        } else if (index == 132) {
          deformations[index] = Offset(1.8 * value, 0.3 * value); // 左法令纹下部点
        } else if (index == 58) {
          deformations[index] = Offset(1.5 * value, -0.2 * value); // 左面颊支撑点
        }
      } else if (rightContourIndices.contains(index)) {
        // 右脸轮廓点向内移动
        deformations[index] = Offset(-2.0 * value, 0);
        
        // 对V脸塑形点的特殊处理
        if (index == 394) {
          deformations[index] = Offset(-2.2 * value, 0.5 * value); // 右面部下部轮廓点
        } else if (index == 361) {
          deformations[index] = Offset(-1.8 * value, 0.3 * value); // 右法令纹下部点
        } else if (index == 288) {
          deformations[index] = Offset(-1.5 * value, -0.2 * value); // 右面颊支撑点
        }
      }
    }
  }
  
  /// 应用下巴调整变形
  void _applyFaceChinAdjustTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 下巴点
    deformations[175] = Offset(0, -2.0 * value);
    deformations[152] = Offset(0, -1.5 * value);
    deformations[234] = Offset(0, -2.0 * value);
  }
  
  /// 应用颧骨调整变形
  void _applyFaceCheekboneAdjustTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左颧骨
    deformations[111] = Offset(2.0 * value, 0);
    deformations[117] = Offset(1.5 * value, 0);
    
    // 右颧骨
    deformations[340] = Offset(-2.0 * value, 0);
    deformations[346] = Offset(-1.5 * value, 0);
  }
  
  /// 应用脸型优化变形
  void _applyFaceShapeTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左脸轮廓
    final List<int> leftFaceIndices = [10, 21, 54, 58, 67, 71, 93, 103, 109, 127];
    // 右脸轮廓
    final List<int> rightFaceIndices = [280, 284, 288, 297, 301, 318, 323, 330, 332, 333];
    
    // 计算变形向量 - 增强效果
    for (final int index in indices) {
      if (leftFaceIndices.contains(index)) {
        deformations[index] = Offset(4.0 * value, 0);
      } else if (rightFaceIndices.contains(index)) {
        deformations[index] = Offset(-4.0 * value, 0);
      }
    }
  }
  
  /// 应用唇形调整变形
  void _applyLipShapeTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 上唇中点
    deformations[0] = Offset(0, -1.0 * value);
    deformations[13] = Offset(0, -0.5 * value);
    
    // 下唇中点
    deformations[17] = Offset(0, 1.0 * value);
    deformations[14] = Offset(0, 0.5 * value);
  }
  
  /// 应用嘴唇厚度变形
  void _applyLipThicknessTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 上唇点
    final List<int> upperLipIndices = [61, 62, 72, 73, 74, 76, 77, 78, 80, 81, 82];
    // 下唇点
    final List<int> lowerLipIndices = [84, 85, 86, 87, 88, 89, 90, 91, 95, 146, 178];
    
    // 计算变形向量
    for (final int index in indices) {
      if (upperLipIndices.contains(index)) {
        deformations[index] = Offset(0, -1.0 * value);
      } else if (lowerLipIndices.contains(index)) {
        deformations[index] = Offset(0, 1.0 * value);
      }
    }
  }
  
  /// 应用嘴角上扬变形
  void _applyMouthCornerTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左嘴角
    deformations[61] = Offset(0, -1.5 * value);
    deformations[76] = Offset(0, -1.0 * value);
    
    // 右嘴角
    deformations[291] = Offset(0, -1.5 * value);
    deformations[306] = Offset(0, -1.0 * value);
  }
  
  /// 应用法令纹变形
  void _applyNasolabialFoldsTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左侧法令纹区域
    final List<int> leftNasolabialIndices = [115, 125, 135, 145, 155];
    // 右侧法令纹区域
    final List<int> rightNasolabialIndices = [315, 325, 335, 345, 355];
    
    // 计算变形向量
    for (final int index in indices) {
      if (leftNasolabialIndices.contains(index)) {
        deformations[index] = Offset(1.0 * value, -0.5 * value);
      } else if (rightNasolabialIndices.contains(index)) {
        deformations[index] = Offset(-1.0 * value, -0.5 * value);
      }
    }
  }
  
  /// 应用去皱纹变形
  void _applyWrinkleRemovalTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 额头皱纹区域
    final List<int> foreheadWrinkleIndices = [24, 25, 26, 27, 28, 29, 30];
    // 眼角皱纹区域
    final List<int> eyeWrinkleIndices = [33, 133, 159, 145, 362, 386, 263, 374];
    
    // 计算变形向量
    for (final int index in indices) {
      if (foreheadWrinkleIndices.contains(index)) {
        deformations[index] = Offset(0, -1.0 * value);
      } else if (eyeWrinkleIndices.contains(index)) {
        deformations[index] = Offset(0, -0.5 * value);
      }
    }
  }
  
  /// 应用额头饱满变形
  void _applyForeheadFullnessTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 额头区域
    final List<int> foreheadIndices = [24, 25, 26, 27, 28, 29, 30, 31, 32];
    
    // 计算变形向量
    for (final int index in foreheadIndices) {
      if (indices.contains(index)) {
        deformations[index] = Offset(0, -1.5 * value);
      }
    }
  }
  
  /// 应用面容紧致变形
  void _applyFacialFirmnessTransform(Map<int, Offset> deformations, List<int> indices, double value) {
    // 左脸颊
    final List<int> leftCheekIndices = [115, 125, 135, 145, 155];
    // 右脸颊
    final List<int> rightCheekIndices = [315, 325, 335, 345, 355];
    
    // 计算变形向量
    for (final int index in indices) {
      if (leftCheekIndices.contains(index)) {
        deformations[index] = Offset(1.0 * value, -1.0 * value);
      } else if (rightCheekIndices.contains(index)) {
        deformations[index] = Offset(-1.0 * value, -1.0 * value);
      }
    }
  }
  
  /// 释放资源
  void dispose() {
    try {
      if (!Platform.isMacOS) {
        _faceDetector.close();
      }
    } catch (e) {
      print('关闭面部检测器时出错: $e');
    }
  }
}
