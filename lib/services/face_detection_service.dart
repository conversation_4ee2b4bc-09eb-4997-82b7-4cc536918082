import 'dart:io';
import 'dart:convert'; // Add this line
import 'package:flutter/foundation.dart';
import '../config/service_config.dart';
import 'python_service_manager.dart';

class FaceDetectionService {
  final _pythonService = PythonServiceManager();
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('✓ [面部特征点服务] 已经初始化');
      return;
    }

    try {
      debugPrint('🔄 [面部特征点服务] 初始化服务...');
      await _pythonService.start();
      _isInitialized = true;
      debugPrint('✅ [面部特征点服务] 初始化完成');
    } catch (e) {
      debugPrint('❌ [面部特征点服务] 初始化失败: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  Future<Map<String, dynamic>> detectFeatures(String imagePath) async {
    if (!_isInitialized) {
      debugPrint('⚠️ [面部特征点服务] 服务未初始化，尝试初始化');
      await initialize();
    }

    try {
      debugPrint('🔍 [面部特征点服务] 检测特征点:');
      debugPrint('   - 图片路径: $imagePath');

      final pythonPath = await ServiceConfig.pythonPath;
      final scriptPath = await ServiceConfig.faceMeshScript;

      debugPrint('   - Python路径: $pythonPath');
      debugPrint('   - 脚本路径: $scriptPath');

      if (!await File(scriptPath).exists()) {
        throw Exception('特征点检测脚本不存在: $scriptPath');
      }

      final result = await Process.run(
        pythonPath,
        [scriptPath, imagePath],
        stdoutEncoding: const SystemEncoding(),
        stderrEncoding: const SystemEncoding(),
      );

      if (result.exitCode != 0) {
        debugPrint('❌ [面部特征点服务] 检测失败:');
        debugPrint('   错误: ${result.stderr}');
        throw Exception('特征点检测失败: ${result.stderr}');
      }

      final output = result.stdout.toString().trim();
      debugPrint('✅ [面部特征点服务] 检测完成');
      
      try {
        final Map<String, dynamic> jsonResult = json.decode(output);
        if (jsonResult['status'] == 'no_face') {
          throw Exception('未检测到人脸');
        }
        return jsonResult;
      } catch (e) {
        debugPrint('❌ [面部特征点服务] JSON解析失败: $e');
        debugPrint('   原始输出: $output');
        throw Exception('特征点数据解析失败');
      }
    } catch (e) {
      debugPrint('❌ [面部特征点服务] 检测失败: $e');
      rethrow;
    }
  }

  Future<void> dispose() async {
    if (_isInitialized) {
      debugPrint('🛑 [面部特征点服务] 释放资源...');
      await _pythonService.stop();
      _isInitialized = false;
      debugPrint('✅ [面部特征点服务] 资源已释放');
    }
  }
}
