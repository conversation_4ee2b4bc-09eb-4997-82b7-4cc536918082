import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

import '../utils/logger.dart';

/// 图像处理服务结果
class ImageProcessingResult {
  final bool success;
  final String outputPath;
  final String? error;
  final int processingTimeMs;

  ImageProcessingResult({
    required this.success,
    required this.outputPath,
    this.error,
    required this.processingTimeMs,
  });
}

/// 图像处理服务
/// 
/// 提供图像变形和处理功能
class ImageProcessingService {
  /// 应用变形到图像
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [deformationData] 变形数据
  /// 
  /// 返回处理结果
  static Future<ImageProcessingResult> applyDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    required List<Map<String, dynamic>> deformationData,
  }) async {
    final startTime = DateTime.now();
    
    Logger.log('ImageProcessingService', 'applyDeformation', '🔄 [开始] 应用变形到图像');
    Logger.log('ImageProcessingService', 'applyDeformation', '📃 [参数] 图像路径: $imagePath');
    Logger.log('ImageProcessingService', 'applyDeformation', '📃 [参数] 特征点数量: ${featurePoints.length}');
    Logger.log('ImageProcessingService', 'applyDeformation', '📃 [参数] 变形点数量: ${deformationData.length}');
    
    try {
      // 检查输入文件是否存在
      final inputFile = File(imagePath);
      if (!await inputFile.exists()) {
        Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] 输入文件不存在: $imagePath');
        return ImageProcessingResult(
          success: false,
          outputPath: '',
          error: '输入文件不存在',
          processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
        );
      }
      
      // 生成输出文件路径
      final inputDir = path.dirname(imagePath);
      final inputFileName = path.basenameWithoutExtension(imagePath);
      final inputExt = path.extension(imagePath);
      final outputFileName = '${inputFileName}_deformed${inputExt}';
      var outputPath = path.join(inputDir, outputFileName);
      
      // 构建符合Python脚本期望格式的数据结构
      final Map<String, dynamic> combinedData = {
        'landmarks': featurePoints,
        'deformation_vectors': {},
      };
      
      // 将变形数据转换为正确的格式
      for (final deform in deformationData) {
        if (deform.containsKey('id') && deform.containsKey('dx') && deform.containsKey('dy')) {
          final pointId = deform['id'].toString();
          combinedData['deformation_vectors'][pointId] = {
            'dx': deform['dx'],
            'dy': deform['dy'],
          };
        }
      }
      
      // 将数据转换为JSON
      final combinedDataJson = jsonEncode(combinedData);
      
      Logger.log('ImageProcessingService', 'applyDeformation', '📃 [数据] 已构建合并数据，大小: ${combinedDataJson.length} 字节');
      
      // 获取应用沙盒目录
      final appDocDir = await getApplicationDocumentsDirectory();
      final sandboxCorePath = path.join(appDocDir.path, 'core');
      
      // 确保沙盒中的core目录存在
      final sandboxCoreDir = Directory(sandboxCorePath);
      if (!await sandboxCoreDir.exists()) {
        await sandboxCoreDir.create(recursive: true);
        Logger.log('ImageProcessingService', 'applyDeformation', '📁 [创建] 创建沙盒core目录: $sandboxCorePath');
      }
      
      // 检查Python环境
      try {
        final pythonVersionResult = await Process.run('python3', ['--version']);
        if (pythonVersionResult.exitCode == 0) {
          Logger.log('ImageProcessingService', 'applyDeformation', '✅ [环境] Python版本: ${pythonVersionResult.stdout.toString().trim()}');
        } else {
          Logger.log('ImageProcessingService', 'applyDeformation', '⚠️ [警告] 无法获取Python版本: ${pythonVersionResult.stderr}');
        }
      } catch (e) {
        Logger.log('ImageProcessingService', 'applyDeformation', '⚠️ [警告] 检查Python环境时出错: $e');
      }
      
      // 沙盒中的Python脚本路径
      final sandboxScriptPath = path.join(sandboxCorePath, 'image_deformation.py');
      
      // 每次都重新创建脚本，确保使用最新版本
      final sandboxScriptFile = File(sandboxScriptPath);
      // 删除旧脚本（如果存在）
      if (await sandboxScriptFile.exists()) {
        try {
          await sandboxScriptFile.delete();
          Logger.log('ImageProcessingService', 'applyDeformation', '🗑️ [删除] 删除旧版本Python脚本');
        } catch (e) {
          Logger.log('ImageProcessingService', 'applyDeformation', '⚠️ [警告] 无法删除旧版本Python脚本: $e');
        }
      }
      
      // 创建新脚本
      try {
          // 直接在沙盒中创建Python脚本，而不是从外部复制
          // 这样可以避免沙盒环境下的权限问题
          await sandboxScriptFile.writeAsString('''
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import numpy as np
import argparse
import json
import os
import sys
import time

def log(message):
    """打印日志信息"""
    print(f"[Python] {message}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='图像变形处理工具')
    parser.add_argument('--input', required=True, help='输入图像路径')
    parser.add_argument('--output', required=True, help='输出图像路径')
    parser.add_argument('--data', required=True, help='变形数据JSON字符串')
    return parser.parse_args()

def apply_deformation(image, landmarks, deformation_vectors):
    """应用变形到图像 - 增强版
    
    此版本大幅增强变形效果，确保变形可见
    """
    """应用变形到图像
    
    Args:
        image: 输入图像
        landmarks: 特征点数据
        deformation_vectors: 变形数据，包含特征点ID和位移向量
    
    Returns:
        变形后的图像
    """
    height, width = image.shape[:2]
    log(f"图像尺寸: {width}x{height}")
    
    # 创建网格
    grid_x, grid_y = np.meshgrid(np.arange(width), np.arange(height))
    
    # 创建变形映射
    map_x = grid_x.astype(np.float32)
    map_y = grid_y.astype(np.float32)
    
    # 记录原始图像用于调试
    debug_image = image.copy()
    
    # 变形强度统一标准参数 - 大幅增强版
    scale_factor = 0.05  # 比例值转换为像素单位的系数统一为0.05
    influence_radius = width * 0.15  # 影响半径设置为图像宽度的15%
    point_power = 2.0  # 点的影响力统一为2.0
    weight_factor = 5.0  # 权重因子大幅增加到5.0，确保变形效果明显可见
    falloff_factor = 0.8  # 减弱因子调整为0.8，使变形效果更明显
    
    log(f"变形参数: 比例因子={scale_factor}, 影响半径={influence_radius:.1f}px, 点影响力={point_power}, 权重因子={weight_factor}")
    
    # 应用变形
    for point_id, vector in deformation_vectors.items():
        # 获取位移向量
        dx = vector['dx']
        dy = vector['dy']
        
        # 确保变形向量有足够大的影响
        # 如果变形向量太小，放大它
        min_deform = 1.0  # 提高最小变形阈值到1.0
        if abs(dx) < min_deform or abs(dy) < min_deform:  # 使用or而不是and，确保任一方向都有足够变形
            magnitude = np.sqrt(dx**2 + dy**2)
            if magnitude < 0.0001:  # 真正的零向量才跳过
                continue
            # 放大小向量
            if magnitude > 0:
                scale = max(min_deform / magnitude, 1.0) * 2.0  # 额外乘以2.0进一步放大
                dx *= scale
                dy *= scale
                log(f"放大变形向量: 原始=({vector['dx']:.2f}, {vector['dy']:.2f}), 放大后=({dx:.2f}, {dy:.2f})")
        
        # 查找特征点坐标
        point_x = None
        point_y = None
        for landmark in landmarks:
            if str(landmark['id']) == point_id:
                point_x = landmark['x']
                point_y = landmark['y']
                break
        
        if point_x is not None and point_y is not None:
            log(f"处理特征点 ID:{point_id}, 位置:({point_x:.1f},{point_y:.1f}), 位移:({dx:.2f},{dy:.2f})")
            
            # 计算每个像素到特征点的距离
            dist_x = grid_x - point_x
            dist_y = grid_y - point_y
            dist = np.sqrt(dist_x**2 + dist_y**2)
            
            # 计算影响因子 (距离越远，影响越小)
            # 使用高斯函数计算影响
            influence = np.exp(-(dist**2) / (2 * (influence_radius * falloff_factor)**2))
            
            # 直接使用特征点的位移向量，并应用统一的权重因子
            # 注意：这里使用加法而不是减法，确保变形方向正确
            # 记录实际应用的像素变形量
            pixel_dx = influence * dx * weight_factor
            pixel_dy = influence * dy * weight_factor
            
            # 应用变形
            map_x += pixel_dx
            map_y += pixel_dy
            
            # 记录最大变形量用于调试
            max_pixel_dx = np.max(np.abs(pixel_dx))
            max_pixel_dy = np.max(np.abs(pixel_dy))
            log(f"  - 最大像素变形: dx={max_pixel_dx:.2f}, dy={max_pixel_dy:.2f}")
            
            # 在变形点位置绘制标记，用于调试
            if max_pixel_dx > 1.0 or max_pixel_dy > 1.0:
                log(f"  - 检测到明显变形: 点ID={point_id}, 位置=({point_x:.1f}, {point_y:.1f})")
    
    # 应用变形映射，使用更高质量的插值方法
    deformed_image = cv2.remap(image, map_x, map_y, cv2.INTER_CUBIC, borderMode=cv2.BORDER_REFLECT_101)
    
    return deformed_image

def main():
    """主函数"""
    start_time = time.time()
    log("开始处理图像变形 - 增强版变形算法")
    
    # 解析命令行参数
    args = parse_arguments()
    log(f"输入图像: {args.input}")
    log(f"输出图像: {args.output}")
    
    # 解析JSON数据
    try:
        data = json.loads(args.data)
        landmarks = data['landmarks']
        deformation_vectors = data['deformation_vectors']
        log(f"特征点数量: {len(landmarks)}")
        log(f"变形向量数量: {len(deformation_vectors)}")
    except Exception as e:
        log(f"解析JSON数据失败: {e}")
        sys.exit(1)
    
    # 读取图像
    try:
        image = cv2.imread(args.input)
        if image is None:
            log(f"无法读取图像: {args.input}")
            sys.exit(1)
        log(f"成功读取图像: {args.input}")
    except Exception as e:
        log(f"读取图像失败: {e}")
        sys.exit(1)
    
    # 应用变形
    try:
        deformed_image = apply_deformation(image, landmarks, deformation_vectors)
        log("变形应用完成")
    except Exception as e:
        log(f"应用变形失败: {e}")
        sys.exit(1)
    
    # 保存结果
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(args.output)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        cv2.imwrite(args.output, deformed_image)
        log(f"结果已保存到: {args.output}")
    except Exception as e:
        log(f"保存结果失败: {e}")
        sys.exit(1)
    
    end_time = time.time()
    log(f"处理完成，耗时: {end_time - start_time:.2f}秒")

if __name__ == "__main__":
    main()
''');
          Logger.log('ImageProcessingService', 'applyDeformation', '📝 [创建] 在沙盒中创建Python脚本: $sandboxScriptPath');
          
          // 确保脚本有执行权限
          try {
            await Process.run('chmod', ['+x', sandboxScriptPath]);
            Logger.log('ImageProcessingService', 'applyDeformation', '🔒 [权限] 已设置脚本执行权限');
          } catch (e) {
            Logger.log('ImageProcessingService', 'applyDeformation', '⚠️ [警告] 无法设置脚本执行权限: $e');
          }
        } catch (e) {
          Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] 无法在沙盒中创建Python脚本: $e');
          return ImageProcessingResult(
            success: false,
            outputPath: '',
            error: '无法创建Python脚本: $e',
            processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
          );
        }
      }
      
      // 检查沙盒中的脚本是否存在
      if (!await sandboxScriptFile.exists()) {
        Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] 沙盒中的脚本不存在: $sandboxScriptPath');
        return ImageProcessingResult(
          success: false,
          outputPath: '',
          error: '沙盒中的脚本不存在',
          processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
        );
      }
      
      // 检查脚本权限
      final scriptStat = await sandboxScriptFile.stat();
      final scriptMode = scriptStat.mode;
      final isExecutable = scriptMode & 0x1 != 0; // 检查执行权限位
      Logger.log('ImageProcessingService', 'applyDeformation', '📃 [权限] 脚本权限模式: $scriptMode, 可执行: $isExecutable');
      
      // 如果脚本不可执行，尝试再次设置权限
      if (!isExecutable) {
        try {
          await Process.run('chmod', ['+x', sandboxScriptPath]);
          Logger.log('ImageProcessingService', 'applyDeformation', '🔒 [权限] 重新设置脚本执行权限');
        } catch (e) {
          Logger.log('ImageProcessingService', 'applyDeformation', '⚠️ [警告] 无法重新设置脚本执行权限: $e');
        }
      }
      
      // 确保输入输出路径在沙盒内
      // 检查输入文件是否在沙盒内，如果不在则尝试创建一个副本
      String sandboxInputPath = imagePath;
      
      if (!imagePath.startsWith(appDocDir.path)) {
        // 输入文件不在沙盒内，尝试在沙盒中创建一个副本
        final inputFileName = path.basename(imagePath);
        sandboxInputPath = path.join(appDocDir.path, 'temp_inputs', inputFileName);
        final sandboxInputDir = Directory(path.dirname(sandboxInputPath));
        
        if (!await sandboxInputDir.exists()) {
          await sandboxInputDir.create(recursive: true);
        }
        
        // 检查是否是测试图像
        final isTestImage = imagePath.contains('test_face.jpg');
        
        // 如果是测试图像，优先尝试从应用程序沙盒中查找已存在的副本
        if (isTestImage) {
          final testImageInSandbox = File(sandboxInputPath);
          if (await testImageInSandbox.exists()) {
            Logger.log('ImageProcessingService', 'applyDeformation', '✅ [发现] 沙盒中已存在测试图像: $sandboxInputPath');
            // 使用沙盒中已存在的测试图像
          } else {
            // 尝试从资源包中加载测试图像
            try {
              // 注意：这里假设测试图像已经添加到了应用程序的资源包中
              // 如果没有，需要先将测试图像添加到pubspec.yaml的assets部分
              final bytes = await rootBundle.load('assets/test_face.jpg');
              await testImageInSandbox.writeAsBytes(bytes.buffer.asUint8List());
              Logger.log('ImageProcessingService', 'applyDeformation', '📁 [复制] 从资源包复制测试图像到沙盒: $sandboxInputPath');
            } catch (assetError) {
              Logger.log('ImageProcessingService', 'applyDeformation', '⚠️ [警告] 无法从资源包加载测试图像: $assetError');
              
              // 尝试使用内置的测试图像路径
              final embeddedTestImagePath = path.join(appDocDir.path, 'core', 'test_face.jpg');
              final embeddedTestImage = File(embeddedTestImagePath);
              
              if (await embeddedTestImage.exists()) {
                // 复制内置测试图像到临时目录
                final bytes = await embeddedTestImage.readAsBytes();
                await testImageInSandbox.writeAsBytes(bytes);
                Logger.log('ImageProcessingService', 'applyDeformation', '📁 [复制] 从内置位置复制测试图像到临时目录: $sandboxInputPath');
              } else {
                // 如果内置测试图像也不存在，则创建一个简单的测试图像
                Logger.log('ImageProcessingService', 'applyDeformation', '⚠️ [警告] 内置测试图像不存在，尝试直接读取原始文件');
                
                // 尝试直接读取原始文件
                try {
                  final originalFile = File(imagePath);
                  if (await originalFile.exists()) {
                    final bytes = await originalFile.readAsBytes();
                    await testImageInSandbox.writeAsBytes(bytes);
                    Logger.log('ImageProcessingService', 'applyDeformation', '📁 [复制] 直接读取原始测试图像并复制到沙盒: $sandboxInputPath');
                  } else {
                    Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] 测试图像不存在: $imagePath');
                    return ImageProcessingResult(
                      success: false,
                      outputPath: '',
                      error: '测试图像不存在',
                      processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
                    );
                  }
                } catch (readError) {
                  Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] 无法读取测试图像: $readError');
                  return ImageProcessingResult(
                    success: false,
                    outputPath: '',
                    error: '无法读取测试图像: $readError',
                    processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
                  );
                }
              }
            }
          }
        } else {
          // 非测试图像，尝试直接读取原始文件内容并写入沙盒
          try {
            final originalInputFile = File(imagePath);
            if (await originalInputFile.exists()) {
              final bytes = await originalInputFile.readAsBytes();
              final sandboxFile = File(sandboxInputPath);
              await sandboxFile.writeAsBytes(bytes);
              Logger.log('ImageProcessingService', 'applyDeformation', '📁 [复制] 通过读写方式复制输入文件到沙盒: $sandboxInputPath');
            } else {
              Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] 输入文件不存在: $imagePath');
              return ImageProcessingResult(
                success: false,
                outputPath: '',
                error: '输入文件不存在',
                processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
              );
            }
          } catch (e) {
            Logger.log('ImageProcessingService', 'applyDeformation', '⚠️ [警告] 无法复制文件到沙盒: $e');
            // 继续使用原始路径，但可能会失败
          }
        }
      }
      
      // 确保输出路径在沙盒内
      String sandboxOutputPath = outputPath;
      if (!outputPath.startsWith(appDocDir.path)) {
        final outputFileName = path.basename(outputPath);
        sandboxOutputPath = path.join(appDocDir.path, 'temp_outputs', outputFileName);
        final sandboxOutputDir = Directory(path.dirname(sandboxOutputPath));
        
        if (!await sandboxOutputDir.exists()) {
          await sandboxOutputDir.create(recursive: true);
        }
        
        Logger.log('ImageProcessingService', 'applyDeformation', '📁 [路径] 调整输出路径到沙盒: $sandboxOutputPath');
      }
      
      // 构建命令参数 - 使用--data参数传递合并的JSON数据
      final args = [
        sandboxScriptPath,
        '--input', sandboxInputPath,
        '--output', sandboxOutputPath,
        '--data', combinedDataJson,
      ];
      
      // 记录完整命令
      final fullCommand = 'python3 ${args.join(' ')}';
      Logger.log('ImageProcessingService', 'applyDeformation', '📃 [命令] 完整命令: $fullCommand');
      
      // 执行Python脚本
      Logger.log('ImageProcessingService', 'applyDeformation', '🔄 [执行] 调用Python脚本应用变形');
      
      // 尝试使用不同的方法执行Python脚本
      ProcessResult result;
      try {
        // 方法1: 使用Process.run
        Logger.log('ImageProcessingService', 'applyDeformation', '🔄 [尝试] 使用Process.run执行');
        result = await Process.run('python3', args);
      } catch (e) {
        Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] Process.run执行失败: $e');
        
        // 方法2: 尝试使用ProcessStartInfo
        Logger.log('ImageProcessingService', 'applyDeformation', '🔄 [尝试] 使用Process.start执行');
        try {
          final process = await Process.start('python3', args);
          final stdout = await process.stdout.transform(utf8.decoder).join();
          final stderr = await process.stderr.transform(utf8.decoder).join();
          final exitCode = await process.exitCode;
          
          result = ProcessResult(process.pid, exitCode, stdout, stderr);
        } catch (e2) {
          Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] Process.start也失败: $e2');
          
          // 方法3: 尝试使用shell脚本作为中间层
          Logger.log('ImageProcessingService', 'applyDeformation', '🔄 [尝试] 使用shell脚本作为中间层');
          try {
            // 创建一个临时shell脚本
            final shellScriptPath = path.join(sandboxCorePath, 'run_python.sh');
            final shellScript = File(shellScriptPath);
            await shellScript.writeAsString('''
#!/bin/bash
python3 "\$@"
''');
            
            // 设置shell脚本的执行权限
            await Process.run('chmod', ['+x', shellScriptPath]);
            
            // 使用shell脚本执行Python命令
            final shellArgs = [sandboxScriptPath, '--input', sandboxInputPath, '--output', sandboxOutputPath, '--data', combinedDataJson];
            final shellResult = await Process.run(shellScriptPath, shellArgs);
            result = shellResult;
          } catch (e3) {
            Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] shell脚本方法也失败: $e3');
            return ImageProcessingResult(
              success: false,
              outputPath: '',
              error: '无法执行Python脚本: $e, $e2, $e3',
              processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
            );
          }
        }
      }
      
      // 记录执行输出
      Logger.log('ImageProcessingService', 'applyDeformation', '📃 [输出] 标准输出: ${result.stdout}');
      Logger.log('ImageProcessingService', 'applyDeformation', '📃 [输出] 标准错误: ${result.stderr}');
      
      // 检查执行结果
      if (result.exitCode != 0) {
        final errorMsg = result.stderr.toString();
        Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] 执行变形脚本失败: $errorMsg');
        return ImageProcessingResult(
          success: false,
          outputPath: '',
          error: '执行变形脚本失败: $errorMsg',
          processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
        );
      }
      
      // 检查输出文件是否存在
      final outputFile = File(sandboxOutputPath);
      if (!await outputFile.exists()) {
        Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] 输出文件不存在: $sandboxOutputPath');
        return ImageProcessingResult(
          success: false,
          outputPath: '',
          error: '输出文件不存在',
          processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
        );
      }
      
      // 不再尝试复制输出文件到原始路径，而是直接使用沙盒内的路径
      Logger.log('ImageProcessingService', 'applyDeformation', '📝 [信息] 输出文件在沙盒内: $sandboxOutputPath');
      outputPath = sandboxOutputPath;
      
      // 计算处理时间
      final endTime = DateTime.now();
      final processingTimeMs = endTime.difference(startTime).inMilliseconds;
      
      Logger.log('ImageProcessingService', 'applyDeformation', '✅ [完成] 变形应用成功 | 耗时: ${processingTimeMs}ms');
      Logger.log('ImageProcessingService', 'applyDeformation', '📃 [结果] 输出文件: $outputPath');
      
      return ImageProcessingResult(
        success: true,
        outputPath: outputPath,
        error: '',
        processingTimeMs: processingTimeMs,
      );
    } catch (e) {
      Logger.log('ImageProcessingService', 'applyDeformation', '❌ [异常] 应用变形异常: $e');
      return ImageProcessingResult(
        success: false,
        outputPath: '',
        error: '应用变形异常: $e',
        processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
      );
    }
  }
  
  /// 获取测试图像路径
  Future<String> getTestImagePath() async {
    // 定义可能的测试图像路径
    final List<String> possiblePaths = [];
    
    // 1. 首先尝试使用testdata目录中的测试图像（优先级最高）
    final testdataImagePath = path.join(Directory.current.path, 'testdata', 'test_face.jpg');
    possiblePaths.add(testdataImagePath);
    
    // 2. 尝试从应用程序资源包中获取测试图像路径
    final assetTestImagePath = 'assets/test_face.jpg';
    final executableDir = path.dirname(Platform.resolvedExecutable);
    
    // 3. 应用程序目录下的assets目录
    final appAssetsPath = path.join(executableDir, assetTestImagePath);
    possiblePaths.add(appAssetsPath);
    
    // 4. Flutter资源目录
    final flutterAssetsPath = path.join(executableDir, 'Frameworks', 'App.framework', 
        'Versions', 'A', 'Resources', 'flutter_assets', assetTestImagePath);
    possiblePaths.add(flutterAssetsPath);
    
    // 5. macOS应用程序包内的Flutter资源目录
    final macosFlutterAssetsPath = path.join(executableDir, '..', 'Frameworks', 'App.framework', 
        'Versions', 'A', 'Resources', 'flutter_assets', assetTestImagePath);
    possiblePaths.add(macosFlutterAssetsPath);
    
    // 6. 项目根目录中的测试图像
    final projectRootTestImagePath = path.join(Directory.current.path, 'assets', 'test_face.jpg');
    possiblePaths.add(projectRootTestImagePath);
    
    // 7. 尝试获取应用程序文档目录中的测试图像
    final appDocDir = await getApplicationDocumentsDirectory();
    final sandboxTestImagePath = path.join(appDocDir.path, 'temp_inputs', 'test_face.jpg');
    possiblePaths.add(sandboxTestImagePath);
    
    // 检查所有可能的路径，返回第一个存在的路径
    for (final pathToCheck in possiblePaths) {
      if (await File(pathToCheck).exists()) {
        print('✅ 找到测试图像: $pathToCheck');
        return pathToCheck;
      } else {
        print('⚠️ 测试图像不存在: $pathToCheck');
      }
    }
    
    // 如果所有路径都不存在，尝试使用资源包中的图像
    try {
      await rootBundle.load(assetTestImagePath);
      print('✅ 在资源包中找到测试图像');
      // 如果资源包中存在，返回testdata目录中的路径（作为首选）
      return testdataImagePath;
    } catch (e) {
      print('❌ 无法找到任何测试图像');
      // 如果所有路径都不存在，返回默认路径
      return testdataImagePath;
    }
  }
}
