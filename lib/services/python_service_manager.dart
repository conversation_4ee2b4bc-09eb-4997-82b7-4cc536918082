import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import '../utils/resource_manager.dart';
import '../config/service_config.dart';

class PythonServiceManager {
  Process? _process;
  bool _isRunning = false;

  Future<void> initialize() async {
    try {
      debugPrint('[Python] 开始初始化服务...');
      
      // 检查并确保所需资源存在
      debugPrint('[Python] 检查并确保资源存在...');
      await ResourceManager.ensureResourcesExist();
      
      // 验证所需文件
      debugPrint('[Python] 验证所需文件...');
      await _validateRequiredFiles();
      
      debugPrint('[Python] 服务初始化完成');
    } catch (e, stackTrace) {
      debugPrint('[Python] 初始化服务时出错:');
      debugPrint('  - 错误类型: ${e.runtimeType}');
      debugPrint('  - 错误信息: $e');
      debugPrint('  - 调用堆栈:');
      stackTrace.toString().split('\n').take(5).forEach((line) {
        debugPrint('    $line');
      });
      rethrow;
    }
  }
  
  static Future<void> _validateRequiredFiles() async {
    debugPrint('[Python] 开始验证所需文件...');
    
    final dataDir = await ResourceManager.dataDir;
    final coreDir = Directory(path.join(dataDir, 'core'));
    
    debugPrint('[Python] 核心目录: ${coreDir.path}');
    
    // 检查核心目录是否存在
    if (!await coreDir.exists()) {
      debugPrint('[Python] 错误: 核心目录不存在');
      throw Exception('核心目录不存在: ${coreDir.path}');
    }
    
    final requiredFiles = [
      'face_mesh_processor.py',
      'face_analysis_v2.py',
    ];
    
    debugPrint('[Python] 检查所需文件:');
    
    for (final file in requiredFiles) {
      final filePath = path.join(coreDir.path, file);
      debugPrint('  - 检查文件: $file');
      
      final fileExists = await File(filePath).exists();
      if (!fileExists) {
        debugPrint('    错误: 文件不存在');
        throw Exception('所需文件不存在: $filePath');
      }
      
      // 检查文件大小
      final fileSize = await File(filePath).length();
      if (fileSize == 0) {
        debugPrint('    错误: 文件为空');
        throw Exception('所需文件为空: $filePath');
      }
      
      debugPrint('    文件大小: ${fileSize ~/ 1024} KB');
    }
    
    debugPrint('[Python] 所需文件验证完成');
  }
  
  static Future<void> killExistingInstances() async {
    try {
      debugPrint('[Python] 开始清理现有Python进程...');
      
      if (!Platform.isMacOS) {
        debugPrint('[Python] 警告: 仅支持macOS系统');
        return;
      }
      
      // 获取进程列表
      debugPrint('[Python] 获取Python进程列表...');
      final psResult = await Process.run('ps', ['aux', '|', 'grep', 'python'], 
        runInShell: true,
      );
      
      final processes = psResult.stdout.toString().split('\n')
        .where((line) => line.contains('python') && !line.contains('grep'))
        .toList();
      
      if (processes.isEmpty) {
        debugPrint('[Python] 未发现运行中的Python进程');
        return;
      }
      
      debugPrint('[Python] 发现 ${processes.length} 个Python进程:');
      for (final process in processes) {
        debugPrint('  - $process');
      }
      
      // 结束进程
      debugPrint('[Python] 尝试结束进程...');
      final result = await Process.run('pkill', ['-f', 'python'], 
        runInShell: true,
      );
      
      if (result.exitCode == 0) {
        debugPrint('[Python] 成功结束所有Python进程');
      } else {
        final errorMsg = result.stderr.toString().trim();
        debugPrint('[Python] 结束进程时出错:');
        debugPrint('  - 错误代码: ${result.exitCode}');
        debugPrint('  - 错误信息: ${errorMsg.isEmpty ? '无错误信息' : errorMsg}');
      }
    } catch (e, stackTrace) {
      debugPrint('[Python] 清理进程时出错:');
      debugPrint('  - 错误类型: ${e.runtimeType}');
      debugPrint('  - 错误信息: $e');
      debugPrint('  - 调用堆栈:');
      stackTrace.toString().split('\n').take(5).forEach((line) {
        debugPrint('    $line');
      });
      // 不抛出异常，因为这个操作不是关键性的
    }
  }
  
  Future<void> start() async {
    if (_isRunning) {
      debugPrint(' [Python] ');
      return;
    }

    try {
      debugPrint(' [Python] ...');
      
      // 
      await ServiceConfig.validatePaths();
      
      final pythonPath = await ServiceConfig.pythonPath;
      final scriptPath = await ServiceConfig.faceMeshScript;
      final corePath = await ServiceConfig.corePath;
      
      debugPrint(' [Python] :');
      debugPrint('   - Python: $pythonPath');
      debugPrint('   - : $scriptPath');
      debugPrint('   - : $corePath');

      // 
      if (!await File(pythonPath).exists()) {
        throw Exception('Python: $pythonPath');
      }
      if (!await File(scriptPath).exists()) {
        throw Exception(': $scriptPath');
      }

      // 
      final env = Map<String, String>.from(Platform.environment);
      
      // 
      env['PYTHONPATH'] = corePath;
      env['PYTHONUNBUFFERED'] = '1';
      
      // 
      final pythonUserBase = await _getPythonUserBase();
      debugPrint('   - Python: $pythonUserBase');
      
      if (Platform.isMacOS) {
        env['PATH'] = '/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:${env['PATH']}';
        env['PYTHONUSERBASE'] = pythonUserBase;
      }

      _process = await Process.start(
        pythonPath,
        [scriptPath],
        environment: env,
        runInShell: true,
      );

      _isRunning = true;
      debugPrint(' [Python] ');

      // 
      _process!.stdout.listen(
        (data) => debugPrint(' [Python] : ${String.fromCharCodes(data).trim()}'),
        onError: (error) => debugPrint(' [Python] : $error'),
      );

      _process!.stderr.listen(
        (data) => debugPrint(' [Python] : ${String.fromCharCodes(data).trim()}'),
        onError: (error) => debugPrint(' [Python] : $error'),
      );

      // 
      _process!.exitCode.then((code) {
        _isRunning = false;
        debugPrint(' [Python] , : $code');
      });

    } catch (e) {
      debugPrint(' [Python] : $e');
      _isRunning = false;
      rethrow;
    }
  }

  Future<String> _getPythonUserBase() async {
    if (Platform.isMacOS) {
      return '${Platform.environment['HOME']}/.local';
    }
    throw UnsupportedError('');
  }

  Future<Map<String, dynamic>?> processImage(String imagePath) async {
    if (!_isRunning) {
      debugPrint('[Python] 服务未运行，无法处理图片');
      return {
        'status': 'error',
        'message': '服务未运行，请先启动服务'
      };
    }

    try {
      debugPrint('[Python] 开始处理图片: $imagePath');
      
      // 检查文件是否存在
      final imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        debugPrint('[Python] 错误: 图片文件不存在');
        return {
          'status': 'error',
          'message': '图片文件不存在: $imagePath'
        };
      }
      
      // 检查文件大小
      final fileSize = await imageFile.length();
      debugPrint('[Python] 图片文件大小: ${fileSize ~/ 1024} KB');
      
      if (fileSize == 0) {
        debugPrint('[Python] 错误: 图片文件为空');
        return {
          'status': 'error',
          'message': '图片文件为空'
        };
      }
      
      // 检查核心组件路径
      final scriptPath = await ServiceConfig.faceMeshScript;
      final corePath = await ServiceConfig.corePath;
      final pythonPath = await ServiceConfig.pythonPath;
      
      debugPrint('[Python] 检查核心组件:');
      debugPrint('  - Python路径: $pythonPath');
      debugPrint('  - 脚本路径: $scriptPath');
      debugPrint('  - 核心目录: $corePath');
      
      // 检查文件是否存在
      if (!await File(scriptPath).exists()) {
        debugPrint('[Python] 错误: 特征点检测脚本不存在');
        return {
          'status': 'error',
          'message': '特征点检测脚本不存在: $scriptPath'
        };
      }
      
      // 准备参数
      final params = {
        'image_path': imagePath,
        'detect_landmarks': true,
        'detect_face_mesh': true,
        'detect_face_oval': true,
        'detect_lips': true,
        'detect_eyes': true,
        'detect_eyebrows': true,
        'detect_nose': true,
      };
      
      final paramsJson = jsonEncode(params);
      debugPrint('[Python] 参数配置:');
      debugPrint('  - 图片路径: $imagePath');
      debugPrint('  - 参数配置: $paramsJson');
      
      // 调用特征点检测脚本
      debugPrint('[Python] 开始调用特征点检测脚本...');
      
      final meshResult = await Process.run(
        pythonPath,
        [scriptPath, '--params', paramsJson],
        workingDirectory: corePath,
      );
      
      // 检查返回码
      if (meshResult.exitCode != 0) {
        final errorMsg = meshResult.stderr.toString().trim();
        debugPrint('[Python] 特征点检测失败:');
        debugPrint('  - 错误代码: ${meshResult.exitCode}');
        debugPrint('  - 错误信息: $errorMsg');
        return {
          'status': 'error',
          'message': '特征点检测失败: $errorMsg'
        };
      }
      
      // 解析特征点数据
      debugPrint('[Python] 开始解析特征点数据...');
      
      try {
        final output = meshResult.stdout.toString().trim();
        final meshData = jsonDecode(output) as Map<String, dynamic>;
        
        // 检查数据格式
        if (!meshData.containsKey('landmarks')) {
          debugPrint('[Python] 错误: 特征点数据格式错误');
          debugPrint('  - 缺少必要字段: landmarks');
          debugPrint('  - 实际数据: $output');
          return {
            'status': 'error',
            'message': '特征点数据格式错误: 缺少 landmarks 字段'
          };
        }
        
        // 获取特征点数据
        final landmarks = meshData['landmarks'] as List<dynamic>;
        final landmarkCount = landmarks.length;
        
        debugPrint('[Python] 特征点检测成功:');
        debugPrint('  - 检测到 $landmarkCount 个特征点');
        
        // 返回处理结果
        return {
          'status': 'success',
          'landmarks': landmarks,
        };
      } catch (e) {
        debugPrint('[Python] 解析特征点数据失败:');
        debugPrint('  - 错误类型: ${e.runtimeType}');
        debugPrint('  - 错误信息: $e');
        return {
          'status': 'error',
          'message': '解析特征点数据失败: $e'
        };
      }


    } catch (e, stackTrace) {
      debugPrint('[Python] 处理图片时出错:');
      debugPrint('  - 错误类型: ${e.runtimeType}');
      debugPrint('  - 错误信息: $e');
      debugPrint('  - 调用堆栈:');
      stackTrace.toString().split('\n').take(5).forEach((line) {
        debugPrint('    $line');
      });
      
      return {
        'status': 'error',
        'message': '处理图片时出错: $e',
      };
    }
  }

  Future<void> stop() async {
    if (!_isRunning || _process == null) {
      debugPrint('[Python] 服务未运行，无需停止');
      return;
    }

    try {
      debugPrint('[Python] 开始停止服务...');
      
      if (_process != null) {
        debugPrint('[Python] 发送停止信号到进程...');
        _process!.kill();
        
        debugPrint('[Python] 等待进程退出...');
        final exitCode = await _process!.exitCode;
        debugPrint('[Python] 进程退出码: $exitCode');
      }
      
      _isRunning = false;
      _process = null;
      debugPrint('[Python] 服务已成功停止');
    } catch (e, stackTrace) {
      debugPrint('[Python] 停止服务时出错:');
      debugPrint('  - 错误类型: ${e.runtimeType}');
      debugPrint('  - 错误信息: $e');
      debugPrint('  - 调用堆栈:');
      stackTrace.toString().split('\n').take(5).forEach((line) {
        debugPrint('    $line');
      });
      rethrow;
    }
  }

  bool get isRunning => _isRunning;
}
