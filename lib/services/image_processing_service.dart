import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

import '../utils/logger.dart';
import '../core/models/image_processing_result.dart';
import '../core/services/deformation_service.dart';
import '../core/services/python_script_service.dart';

/// 图像处理服务
/// 
/// 提供图像变形和处理功能
/// 此服务是对核心服务的封装，提供更简单的接口
class ImageProcessingService {
  /// 应用变形到图像
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [deformationData] 变形数据
  /// 
  /// 返回处理结果
  static Future<ImageProcessingResult> applyDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    required List<Map<String, dynamic>> deformationData,
  }) async {
    final startTime = DateTime.now();
    final processId = DateTime.now().millisecondsSinceEpoch.toString();
    
    Logger.log('ImageProcessingService', 'applyDeformation', '🔄 [开始] [进程ID: $processId] 应用变形到图像');
    Logger.log('ImageProcessingService', 'applyDeformation', '📃 [参数] [进程ID: $processId] 图像路径: $imagePath');
    Logger.log('ImageProcessingService', 'applyDeformation', '📃 [参数] [进程ID: $processId] 特征点数量: ${featurePoints.length}');
    Logger.log('ImageProcessingService', 'applyDeformation', '📃 [参数] [进程ID: $processId] 变形点数量: ${deformationData.length}');
    
    // 记录部分特征点样本，确认使用的是最新特征点缓存
    if (featurePoints.isNotEmpty) {
      final samplePoints = featurePoints.take(3).toList();
      Logger.log('ImageProcessingService', 'applyDeformation', '📈 [缓存] [进程ID: $processId] 特征点样本:');
      for (final point in samplePoints) {
        if (point.containsKey('id') && point.containsKey('x') && point.containsKey('y')) {
          final pointId = point['id'];
          final x = point['x'] is int ? (point['x'] as int).toDouble() : point['x'] as double;
          final y = point['y'] is int ? (point['y'] as int).toDouble() : point['y'] as double;
          Logger.log('ImageProcessingService', 'applyDeformation', '  • 点 $pointId: 位置 (${x.toStringAsFixed(2)}, ${y.toStringAsFixed(2)})');
        }
      }
    }
    
    // 记录部分变形数据样本
    if (deformationData.isNotEmpty) {
      final sampleDeformations = deformationData.take(3).toList();
      Logger.log('ImageProcessingService', 'applyDeformation', '📈 [数据] [进程ID: $processId] 变形数据样本:');
      for (final deform in sampleDeformations) {
        if (deform.containsKey('id') && deform.containsKey('dx') && deform.containsKey('dy')) {
          final pointId = deform['id'];
          final dx = deform['dx'] is int ? (deform['dx'] as int).toDouble() : deform['dx'] as double;
          final dy = deform['dy'] is int ? (deform['dy'] as int).toDouble() : deform['dy'] as double;
          Logger.log('ImageProcessingService', 'applyDeformation', '  • 点 $pointId: 偏移量 (${dx.toStringAsFixed(2)}, ${dy.toStringAsFixed(2)})');
        }
      }
    }
    
    try {
      // 检查输入文件是否存在
      final inputFile = File(imagePath);
      if (!await inputFile.exists()) {
        Logger.log('ImageProcessingService', 'applyDeformation', '❌ [错误] [进程ID: $processId] 输入文件不存在: $imagePath');
        return ImageProcessingResult(
          success: false,
          outputPath: '',
          error: '输入文件不存在',
          processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
        );
      }
      
      // 检查输入文件大小
      final fileSize = await inputFile.length();
      final lastModified = await inputFile.lastModified();
      Logger.log('ImageProcessingService', 'applyDeformation', '📁 [文件] [进程ID: $processId] 输入文件信息 | 大小: ${fileSize}字节 | 修改时间: ${lastModified.toString()}');
      
      try {
        // 将变形数据列表转换为映射格式
        final deformationVectors = DeformationService.convertDeformationDataToMap(deformationData);
        Logger.log('ImageProcessingService', 'applyDeformation', '📃 [数据] [进程ID: $processId] 变形向量映射创建成功 | 点数: ${deformationVectors.length}');
        
        // 调用核心服务的向量场变形方法
        Logger.log('ImageProcessingService', 'applyDeformation', '🔄 [调用] [进程ID: $processId] 调用DeformationService.applyVectorFieldDeformation');
        final outputPath = await DeformationService.applyVectorFieldDeformation(
          imagePath: imagePath,
          featurePoints: featurePoints,
          deformationVectors: deformationVectors,
        );
        
        // 检查输出文件是否存在
        final outputFile = File(outputPath);
        final outputExists = await outputFile.exists();
        final outputSize = outputExists ? await outputFile.length() : 0;
        Logger.log('ImageProcessingService', 'applyDeformation', '📁 [文件] [进程ID: $processId] 输出文件检查 | 路径: $outputPath | 存在: $outputExists | 大小: ${outputSize}字节');
        
        // 计算处理时间
        final processingTimeMs = DateTime.now().difference(startTime).inMilliseconds;
        
        Logger.log('ImageProcessingService', 'applyDeformation', '✅ [完成] [进程ID: $processId] 变形应用成功 | 耗时: ${processingTimeMs}ms');
        Logger.log('ImageProcessingService', 'applyDeformation', '📃 [结果] [进程ID: $processId] 输出文件: $outputPath');
        
        return ImageProcessingResult(
          success: true,
          outputPath: outputPath,
          error: '',
          processingTimeMs: processingTimeMs,
        );
      } catch (e) {
        Logger.log('ImageProcessingService', 'applyDeformation', '❌ [异常] 应用变形异常: $e');
        return ImageProcessingResult(
          success: false,
          outputPath: '',
          error: '应用变形异常: $e',
          processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
        );
      }
    } catch (e) {
      Logger.log('ImageProcessingService', 'applyDeformation', '❌ [异常] 应用变形异常: $e');
      return ImageProcessingResult(
        success: false,
        outputPath: '',
        error: '应用变形异常: $e',
        processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
      );
    }
  }

  /// 应用面部轮廓变形
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [contourTighten] 轮廓收紧参数 (-1.0 到 1.0)
  /// [chinAdjust] 下巴调整参数 (-1.0 到 1.0)
  /// [cheekboneAdjust] 颧骨调整参数 (-1.0 到 1.0)
  /// [faceShape] 脸型优化参数 (-1.0 到 1.0)
  /// 
  /// 返回处理结果
  static Future<ImageProcessingResult> applyFaceContourDeformation({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double contourTighten = 0.0,
    double chinAdjust = 0.0,
    double cheekboneAdjust = 0.0,
    double faceShape = 0.0,
  }) async {
    final startTime = DateTime.now();
    
    Logger.log('ImageProcessingService', 'applyFaceContourDeformation', '🔄 [开始] 应用面部轮廓变形');
    
    try {
      // 调用核心服务的面部轮廓变形方法
      final outputPath = await DeformationService.applyFaceContourDeformation(
        imagePath: imagePath,
        featurePoints: featurePoints,
        contourTighten: contourTighten,
        chinAdjust: chinAdjust,
        cheekboneAdjust: cheekboneAdjust,
        faceShape: faceShape,
      );
      
      // 计算处理时间
      final processingTimeMs = DateTime.now().difference(startTime).inMilliseconds;
      
      Logger.log('ImageProcessingService', 'applyFaceContourDeformation', '✅ [完成] 面部轮廓变形成功 | 耗时: ${processingTimeMs}ms');
      
      return ImageProcessingResult(
        success: true,
        outputPath: outputPath,
        error: '',
        processingTimeMs: processingTimeMs,
      );
    } catch (e) {
      Logger.log('ImageProcessingService', 'applyFaceContourDeformation', '❌ [异常] 面部轮廓变形异常: $e');
      
      return ImageProcessingResult(
        success: false,
        outputPath: '',
        error: '面部轮廓变形异常: $e',
        processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
      );
    }
  }

  /// 应用鼻部塑形
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [bridgeHeight] 鼻梁高度参数 (-1.0 到 1.0)
  /// [tipAdjust] 鼻尖调整参数 (-1.0 到 1.0)
  /// [nostrilWidth] 鼻翼宽度参数 (-1.0 到 1.0)
  /// [baseHeight] 鼻基抬高参数 (-1.0 到 1.0)
  /// 
  /// 返回处理结果
  static Future<ImageProcessingResult> applyNoseShaping({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double bridgeHeight = 0.0,
    double tipAdjust = 0.0,
    double nostrilWidth = 0.0,
    double baseHeight = 0.0,
  }) async {
    final startTime = DateTime.now();
    
    Logger.log('ImageProcessingService', 'applyNoseShaping', '🔄 [开始] 应用鼻部塑形');
    
    try {
      // 调用核心服务的鼻部塑形方法
      final outputPath = await DeformationService.applyNoseDeformation(
        imagePath: imagePath,
        featurePoints: featurePoints,
        bridgeHeight: bridgeHeight,
        tipAdjust: tipAdjust,
        nostrilWidth: nostrilWidth,
        baseHeight: baseHeight,
      );
      
      // 计算处理时间
      final processingTimeMs = DateTime.now().difference(startTime).inMilliseconds;
      
      Logger.log('ImageProcessingService', 'applyNoseShaping', '✅ [完成] 鼻部塑形成功 | 耗时: ${processingTimeMs}ms');
      
      return ImageProcessingResult(
        success: true,
        outputPath: outputPath,
        error: '',
        processingTimeMs: processingTimeMs,
      );
    } catch (e) {
      Logger.log('ImageProcessingService', 'applyNoseShaping', '❌ [异常] 鼻部塑形异常: $e');
      
      return ImageProcessingResult(
        success: false,
        outputPath: '',
        error: '鼻部塑形异常: $e',
        processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
      );
    }
  }

  /// 应用眼部美化
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [doubleFold] 双眼皮参数 (-1.0 到 1.0)
  /// [canthalTilt] 开眼角参数 (-1.0 到 1.0)
  /// [eyeBagRemoval] 去眼袋参数 (-1.0 到 1.0)
  /// [outerCornerLift] 提眼尾参数 (-1.0 到 1.0)
  /// 
  /// 返回处理结果
  static Future<ImageProcessingResult> applyEyeBeautification({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double doubleFold = 0.0,
    double canthalTilt = 0.0,
    double eyeBagRemoval = 0.0,
    double outerCornerLift = 0.0,
  }) async {
    final startTime = DateTime.now();
    
    Logger.log('ImageProcessingService', 'applyEyeBeautification', '🔄 [开始] 应用眼部美化');
    
    try {
      // 调用核心服务的眼部美化方法
      final outputPath = await DeformationService.applyEyeDeformation(
        imagePath: imagePath,
        featurePoints: featurePoints,
        doubleFold: doubleFold,
        canthalTilt: canthalTilt,
        eyeBagRemoval: eyeBagRemoval,
        outerCornerLift: outerCornerLift,
      );
      
      // 计算处理时间
      final processingTimeMs = DateTime.now().difference(startTime).inMilliseconds;
      
      Logger.log('ImageProcessingService', 'applyEyeBeautification', '✅ [完成] 眼部美化成功 | 耗时: ${processingTimeMs}ms');
      
      return ImageProcessingResult(
        success: true,
        outputPath: outputPath,
        error: '',
        processingTimeMs: processingTimeMs,
      );
    } catch (e) {
      Logger.log('ImageProcessingService', 'applyEyeBeautification', '❌ [异常] 眼部美化异常: $e');
      
      return ImageProcessingResult(
        success: false,
        outputPath: '',
        error: '眼部美化异常: $e',
        processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
      );
    }
  }

  /// 应用唇部造型
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [lipShape] 唇形调整参数 (-1.0 到 1.0)
  /// [lipThickness] 嘴唇厚度参数 (-1.0 到 1.0)
  /// [mouthCorner] 嘴角上扬参数 (-1.0 到 1.0)
  /// [lipColor] 唇色优化参数 (-1.0 到 1.0)
  /// 
  /// 返回处理结果
  static Future<ImageProcessingResult> applyLipShaping({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double lipShape = 0.0,
    double lipThickness = 0.0,
    double mouthCorner = 0.0,
    double lipColor = 0.0,
  }) async {
    final startTime = DateTime.now();
    
    Logger.log('ImageProcessingService', 'applyLipShaping', '🔄 [开始] 应用唇部造型');
    
    try {
      // 调用核心服务的唇部造型方法
      final outputPath = await DeformationService.applyLipDeformation(
        imagePath: imagePath,
        featurePoints: featurePoints,
        lipShape: lipShape,
        lipThickness: lipThickness,
        mouthCorner: mouthCorner,
        lipColor: lipColor,
      );
      
      // 计算处理时间
      final processingTimeMs = DateTime.now().difference(startTime).inMilliseconds;
      
      Logger.log('ImageProcessingService', 'applyLipShaping', '✅ [完成] 唇部造型成功 | 耗时: ${processingTimeMs}ms');
      
      return ImageProcessingResult(
        success: true,
        outputPath: outputPath,
        error: '',
        processingTimeMs: processingTimeMs,
      );
    } catch (e) {
      Logger.log('ImageProcessingService', 'applyLipShaping', '❌ [异常] 唇部造型异常: $e');
      
      return ImageProcessingResult(
        success: false,
        outputPath: '',
        error: '唇部造型异常: $e',
        processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
      );
    }
  }

  /// 应用抗衰冻龄
  /// 
  /// [imagePath] 输入图像路径
  /// [featurePoints] 特征点数据
  /// [nasolabialFolds] 法令纹参数 (-1.0 到 1.0)
  /// [wrinkleRemoval] 去皱纹参数 (-1.0 到 1.0)
  /// [foreheadFullness] 额头饱满参数 (-1.0 到 1.0)
  /// [facialFirmness] 面容紧致参数 (-1.0 到 1.0)
  /// 
  /// 返回处理结果
  static Future<ImageProcessingResult> applyAntiAging({
    required String imagePath,
    required List<Map<String, dynamic>> featurePoints,
    double nasolabialFolds = 0.0,
    double wrinkleRemoval = 0.0,
    double foreheadFullness = 0.0,
    double facialFirmness = 0.0,
  }) async {
    final startTime = DateTime.now();
    
    Logger.log('ImageProcessingService', 'applyAntiAging', '🔄 [开始] 应用抗衰冻龄');
    
    try {
      // 调用核心服务的抗衰冻龄方法
      final outputPath = await DeformationService.applyAntiAgingDeformation(
        imagePath: imagePath,
        featurePoints: featurePoints,
        nasolabialFolds: nasolabialFolds,
        wrinkleRemoval: wrinkleRemoval,
        foreheadFullness: foreheadFullness,
        facialFirmness: facialFirmness,
      );
      
      // 计算处理时间
      final processingTimeMs = DateTime.now().difference(startTime).inMilliseconds;
      
      Logger.log('ImageProcessingService', 'applyAntiAging', '✅ [完成] 抗衰冻龄成功 | 耗时: ${processingTimeMs}ms');
      
      return ImageProcessingResult(
        success: true,
        outputPath: outputPath,
        error: '',
        processingTimeMs: processingTimeMs,
      );
    } catch (e) {
      Logger.log('ImageProcessingService', 'applyAntiAging', '❌ [异常] 抗衰冻龄异常: $e');
      
      return ImageProcessingResult(
        success: false,
        outputPath: '',
        error: '抗衰冻龄异常: $e',
        processingTimeMs: DateTime.now().difference(startTime).inMilliseconds,
      );
    }
  }

  /// 获取测试图像路径
  static Future<String> getTestImagePath() async {
    try {
      // 首先尝试从应用沙盒目录获取
      final appDocDir = await getApplicationDocumentsDirectory();
      final testImagePath = path.join(appDocDir.path, 'testdata', 'test_face.jpg');
      final testImageFile = File(testImagePath);
      
      if (await testImageFile.exists()) {
        return testImagePath;
      }
      
      // 如果沙盒中不存在，尝试从assets复制
      final assetImageData = await rootBundle.load('assets/images/test_face.jpg');
      final buffer = assetImageData.buffer;
      
      // 确保目录存在
      final testDataDir = Directory(path.dirname(testImagePath));
      if (!await testDataDir.exists()) {
        await testDataDir.create(recursive: true);
      }
      
      // 写入文件
      await testImageFile.writeAsBytes(
        buffer.asUint8List(assetImageData.offsetInBytes, assetImageData.lengthInBytes)
      );
      
      return testImagePath;
    } catch (e) {
      // 如果出错，返回beautifun/testdata/test_face.jpg路径
      return 'beautifun/testdata/test_face.jpg';
    }
  }
}
