import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../utils/image_manager.dart';
import '../utils/logger.dart';
import '../beautify_feature/services/transformation_service.dart';
import '../core/side_profile_processor.dart'; // 新增：导入侧面图处理器

enum ImageDisplayState {
  initial,    // 初始状态
  normal,     // 正常状态
  scanning,   // 扫描中
  enlarged,   // 放大状态
  selecting,  // 风格选择中
}

/// 图片状态管理
class ImageState extends ChangeNotifier {
  String? _frontImagePath;
  String? _sideImagePath;  // 侧脸图片路径
  Map<String, dynamic>? _frontFeatures;
  Map<String, dynamic>? _sideFeatures;  // 侧脸特征点
  ImageDisplayState _frontState = ImageDisplayState.normal;
  bool _buttonsVisible = false;
  bool _journeyStarted = false;
  int? _selectedStyleIndex;
  bool _showSideImage = false;  // 是否显示侧脸图片
  bool _sideImageProcessing = false; // 新增：侧脸图片处理状态
  
  // 防止重复处理相同图像
  String? _lastProcessedImagePath;
  DateTime? _lastProcessTime;
  static const int _minProcessIntervalMillis = 1000; // 最小处理间隔（毫秒）

  // Getters
  String? get frontImagePath => _frontImagePath;
  String? get sideImagePath => _sideImagePath;
  Map<String, dynamic>? get frontFeatures => _frontFeatures;
  Map<String, dynamic>? get sideFeatures => _sideFeatures;
  bool get hasFrontImage => _frontImagePath != null;
  bool get hasSideImage => _sideImagePath != null;
  ImageDisplayState get frontState => _frontState;
  bool get buttonsVisible => _buttonsVisible;
  bool get journeyStarted => _journeyStarted;
  int? get selectedStyleIndex => _selectedStyleIndex;
  bool get showSideImage => _showSideImage && hasSideImage;
  bool get sideImageProcessing => _sideImageProcessing; // 新增：侧脸图片处理状态getter

  void setFrontImage(String path) async {
    // 检查是否在短时间内重复处理相同的图像
    final now = DateTime.now();
    if (_lastProcessedImagePath == path && _lastProcessTime != null) {
      final timeSinceLastProcess = now.difference(_lastProcessTime!).inMilliseconds;
      
      if (timeSinceLastProcess < _minProcessIntervalMillis) {
        Logger.i('ImageState', '⚠️ 防止重复处理: 相同图像($path)在${timeSinceLastProcess}毫秒内被重复处理，已跳过');
        return;
      }
    }
    
    // 更新最后处理的图像信息
    _lastProcessedImagePath = path;
    _lastProcessTime = now;
    
    _frontImagePath = path;
    _frontState = ImageDisplayState.normal;
    _buttonsVisible = true;
    _journeyStarted = false;
    _selectedStyleIndex = null;
    
    // 通知变形服务重置所有变形状态
    final transformationService = TransformationService();
    transformationService.resetAllDeformations();
    
    // 设置原始图像路径
    transformationService.setOriginalImagePath(path);
    Logger.i('ImageState', '设置原始图像路径: $path');
    
    notifyListeners();

    // 只在首次导入时处理图片获取特征点
    if (!_journeyStarted) {
      final features = await ImageManager.processImage(path);
      if (features != null) {
        _frontFeatures = features;
        notifyListeners();
      }
    }
  }

  // 设置侧脸图片
  void setSideImage(String path) async {
    // 注释掉侧面图相关日志
    // Logger.i('ImageState', '设置侧脸图像路径: $path');
    _sideImagePath = path;
    _showSideImage = true;
    _sideImageProcessing = true; // 开始处理
    
    notifyListeners();
    
    // 使用专门的侧面图处理器处理图片
    final sideProfileProcessor = SideProfileProcessor();
    final featurePoints = await sideProfileProcessor.processSideImage(path);
    
    if (featurePoints != null) {
      // 将特征点数据转换为Map格式，保持与前面图片一致的数据结构
      final Map<String, dynamic> features = {
        'landmarks': featurePoints.map((point) => {
          'index': point.index,
          'x': point.x,
          'y': point.y,
          'z': point.z,
          'visibility': point.visibility,
          'confidence': point.confidence,
          'primary': point.isPrimary,
          'secondary': point.isSecondary,
        }).toList(),
      };
      
      _sideFeatures = features;
      // 注释掉侧面图相关日志
      // Logger.i('ImageState', '侧脸特征点识别完成，共 ${featurePoints.length} 个点');
      
      // 将特征点数据传递给变形服务
      final transformationService = TransformationService();
      
      // 只在显示侧面图时才将特征点显示设置为 false
      // 这段代码会影响特征点的呼吸效果显示，所以注释掉
      // if (transformationService.getSimpleDeformationRenderer() != null) {
      //   transformationService.getSimpleDeformationRenderer()!.setShowFeaturePoints(false);
      // }
      
      transformationService.setSideFeaturePoints(featurePoints, path);
      // 注释掉侧面图相关日志
      // Logger.i('ImageState', '侧脸特征点数据已传递给变形服务');
    } else {
      // 注释掉侧面图相关日志
      // Logger.w('ImageState', '侧脸特征点识别失败或为空，使用空数据');
      _sideFeatures = {};
    }
    
    _sideImageProcessing = false; // 处理完成
    notifyListeners();
  }
  
  // 切换侧脸图片显示状态
  void toggleSideImage() {
    if (_sideImagePath != null) {
      _showSideImage = !_showSideImage;
      // 注释掉侧面图相关日志
      // Logger.i('ImageState', '切换侧脸图片显示状态: $_showSideImage');
      notifyListeners();
    }
  }

  void startBeautyJourney() {
    Logger.i('图片状态', '开始美旅启程:');
    Logger.i('图片状态', '  - 当前状态: $_frontState');
    
    // 注意：此处不再直接设置按钮不可见，而是由动画完成后调用hideButtons方法
    _buttonsVisible = false; // 直接设置按钮不可见
    _frontState = ImageDisplayState.scanning;
    _journeyStarted = true;
    
    Logger.i('图片状态', '  - 新状态: $_frontState');
    notifyListeners();
  }
  
  /// 隐藏按钮，由动画完成后调用
  void hideButtons() {
    Logger.i('图片状态', '隐藏按钮');
    _buttonsVisible = false;
    notifyListeners();
  }

  void selectStyle(int index) {
  }

  void setFrontFeatures(Map<String, dynamic> features) {
    _frontFeatures = features;
    notifyListeners();
  }
  
  // 设置侧脸特征点
  void setSideFeatures(Map<String, dynamic> features) {
    _sideFeatures = features;
    notifyListeners();
  }

  void setFrontState(ImageDisplayState state) {
    Logger.i('图片状态', '设置前景状态:');
    Logger.i('图片状态', '  - 新状态: $state');
    _frontState = state;
    notifyListeners();
  }

  void reset() {
    _frontImagePath = null;
    _sideImagePath = null;
    _frontFeatures = null;
    _sideFeatures = null;
    _frontState = ImageDisplayState.initial;
    _buttonsVisible = false;
    _journeyStarted = false;
    _selectedStyleIndex = null;
    _showSideImage = false;
    _sideImageProcessing = false; // 重置处理状态
    notifyListeners();
  }
}
