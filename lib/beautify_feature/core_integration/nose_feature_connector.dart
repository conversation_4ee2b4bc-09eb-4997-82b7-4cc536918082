import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/feature_point.dart';
import '../models/nose_feature_points.dart';
import '../utils/logger.dart';
import '../utils/performance_monitor.dart';
import '../../utils/face_feature_bridge.dart' hide FeaturePoint;

/// 鼻部特征点连接器
/// 
/// 负责连接Flutter UI和Python核心组件，处理鼻部特征点的检测和转换
class NoseFeatureConnector {
  /// 单例实例
  static final NoseFeatureConnector _instance = NoseFeatureConnector._internal();
  
  /// 工厂构造函数
  factory NoseFeatureConnector() => _instance;
  
  /// 日志工具
  final BeautifyLogger _logger = BeautifyLogger();
  
  /// 性能监控工具
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  
  /// 方法通道
  static const MethodChannel _channel = MethodChannel('beautifun/nose_feature_connector');
  
  /// 日志标签
  static const String _logTag = 'NoseFeatureConnector';
  
  /// 是否已初始化
  bool _isInitialized = false;
  
  /// 内部构造函数
  NoseFeatureConnector._internal() {
    _logger.info(
      module: _logTag,
      function: 'init',
      operation: OperationType.init,
      message: '🚀 NoseFeatureConnector 初始化',
    );
  }
  
  /// 初始化连接器
  Future<bool> initialize() async {
    if (_isInitialized) {
      _logger.info(
        module: _logTag,
        function: 'initialize',
        operation: OperationType.init,
        message: '✅ 连接器已初始化，跳过',
      );
      return true;
    }
    
    _logger.info(
      module: _logTag,
      function: 'initialize',
      operation: OperationType.init,
      message: '🔄 开始初始化连接器',
    );
    
    final stopwatch = _performanceMonitor.startTimer();
    
    try {
      // 初始化FaceFeatureBridge
      final bridgeInitialized = await FaceFeatureBridge.initialize();
      if (!bridgeInitialized) {
        _logger.error(
          module: _logTag,
          function: 'initialize',
          operation: OperationType.init,
          message: '❌ FaceFeatureBridge初始化失败',
        );
        return false;
      }
      
      // 检查Python环境
      final pythonAvailable = await _checkPythonEnvironment();
      if (!pythonAvailable) {
        _logger.error(
          module: _logTag,
          function: 'initialize',
          operation: OperationType.init,
          message: '❌ Python环境检查失败',
        );
        return false;
      }
      
      _isInitialized = true;
      
      _logger.info(
        module: _logTag,
        function: 'initialize',
        operation: OperationType.init,
        message: '✅ 连接器初始化成功',
        data: {'elapsedTime': stopwatch.elapsedMilliseconds},
      );
      
      return true;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: 'initialize',
        operation: OperationType.init,
        message: '❌ 连接器初始化异常',
        data: {'error': e.toString()},
      );
      return false;
    }
  }
  
  /// 检查Python环境
  Future<bool> _checkPythonEnvironment() async {
    _logger.info(
      module: _logTag,
      function: '_checkPythonEnvironment',
      operation: OperationType.system,
      message: '🔄 检查Python环境',
    );
    
    try {
      // 调用FaceFeatureBridge检查Python环境
      final result = await FaceFeatureBridge.checkPythonEnvironment();
      
      _logger.info(
        module: _logTag,
        function: '_checkPythonEnvironment',
        operation: OperationType.system,
        message: '✅ Python环境检查结果',
        data: {'result': result},
      );
      
      return result['status'] == 'success';
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '_checkPythonEnvironment',
        operation: OperationType.system,
        message: '❌ Python环境检查异常',
        data: {'error': e.toString()},
      );
      return false;
    }
  }
  
  /// 检测图像中的鼻部特征点
  Future<NoseFeaturePoints?> detectNoseFeaturePoints(String imagePath) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) {
        _logger.error(
          module: _logTag,
          function: 'detectNoseFeaturePoints',
          operation: OperationType.process,
          message: '❌ 连接器未初始化，无法检测特征点',
        );
        return null;
      }
    }
    
    _logger.info(
      module: _logTag,
      function: 'detectNoseFeaturePoints',
      operation: OperationType.process,
      message: '🔄 开始检测鼻部特征点',
      data: {'imagePath': imagePath},
    );
    
    final stopwatch = _performanceMonitor.startTimer();
    
    try {
      // 检查文件是否存在
      final file = File(imagePath);
      if (!await file.exists()) {
        _logger.error(
          module: _logTag,
          function: 'detectNoseFeaturePoints',
          operation: OperationType.process,
          message: '❌ 图像文件不存在',
          data: {'imagePath': imagePath},
        );
        return null;
      }
      
      // 调用FaceFeatureBridge检测特征点
      final result = await FaceFeatureBridge.detectFacialFeatures(imagePath);
      
      if (result['status'] != 'success') {
        _logger.error(
          module: _logTag,
          function: 'detectNoseFeaturePoints',
          operation: OperationType.process,
          message: '❌ 特征点检测失败',
          data: {'error': result['message']},
        );
        return null;
      }
      
      // 提取特征点数据
      final landmarks = result['landmarks'] as List<dynamic>;
      
      _logger.info(
        module: _logTag,
        function: 'detectNoseFeaturePoints',
        operation: OperationType.process,
        message: '✅ 特征点检测成功',
        data: {'landmarkCount': landmarks.length},
      );
      
      // 转换为FeaturePoint对象
      final List<FeaturePoint> featurePoints = [];
      for (int i = 0; i < landmarks.length; i++) {
        final landmark = landmarks[i];
        featurePoints.add(FeaturePoint(
          position: Offset(
            landmark['x'].toDouble(),
            landmark['y'].toDouble(),
          ),
          z: landmark['z']?.toDouble() ?? 0.0,
          visibility: landmark['visibility']?.toDouble() ?? 1.0,
          isPrimary: landmark['has_description'] ?? false,
          confidence: 1.0,
          id: i.toString(),
          description: _getFeaturePointDescription(i),
        ));
      }
      
      // 创建鼻部特征点映射
      final mediapipeToNoseMap = _createMediaPipeToNoseMap();
      
      // 创建鼻部特征点对象
      final noseFeaturePoints = NoseFeaturePoints.fromMediaPipePoints(
        allFeaturePoints: featurePoints,
        mediapipeToNoseMap: mediapipeToNoseMap,
      );
      
      _logger.info(
        module: _logTag,
        function: 'detectNoseFeaturePoints',
        operation: OperationType.result,
        message: '✅ 鼻部特征点提取完成',
        data: {
          'elapsedTime': stopwatch.elapsedMilliseconds,
          'nosePointCount': noseFeaturePoints.points.length,
        },
      );
      
      return noseFeaturePoints;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: 'detectNoseFeaturePoints',
        operation: OperationType.process,
        message: '❌ 特征点检测异常',
        data: {'error': e.toString()},
      );
      return null;
    }
  }
  
  /// 创建MediaPipe索引到鼻部特征点ID的映射
  Map<int, NosePointId> _createMediaPipeToNoseMap() {
    return {
      // 鼻梁特征点
      168: NosePointId.noseBridgeTop,
      6: NosePointId.noseBridgeMiddle,
      4: NosePointId.noseBridgeBottom,
      
      // 鼻尖特征点
      1: NosePointId.noseTip,
      
      // 鼻翼特征点
      97: NosePointId.noseLeftWingTop,
      99: NosePointId.noseLeftWingMiddle,
      100: NosePointId.noseLeftWingBottom,
      327: NosePointId.noseRightWingTop,
      328: NosePointId.noseRightWingMiddle,
      329: NosePointId.noseRightWingBottom,
      
      // 鼻孔特征点
      115: NosePointId.leftNostrilInner,
      166: NosePointId.leftNostrilOuter,
      344: NosePointId.rightNostrilInner,
      392: NosePointId.rightNostrilOuter,
      
      // 鼻基底特征点
      2: NosePointId.noseBase,
      79: NosePointId.noseBaseLeft,
      309: NosePointId.noseBaseRight,
    };
  }
  
  /// 获取特征点描述
  String? _getFeaturePointDescription(int index) {
    // 根据特征点索引返回描述
    final Map<int, String> descriptions = {
      1: '鼻尖',
      2: '鼻基底',
      4: '鼻梁底部',
      6: '鼻梁中部',
      168: '鼻梁顶部',
      79: '左鼻基底',
      309: '右鼻基底',
      100: '左鼻翼',
      329: '右鼻翼',
      115: '左鼻孔',
      344: '右鼻孔',
    };
    
    return descriptions[index];
  }
  
  /// 加载图像
  Future<ui.Image?> loadImage(String imagePath) async {
    _logger.info(
      module: _logTag,
      function: 'loadImage',
      operation: OperationType.process,
      message: '🔄 开始加载图像',
      data: {'imagePath': imagePath},
    );
    
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        _logger.error(
          module: _logTag,
          function: 'loadImage',
          operation: OperationType.process,
          message: '❌ 图像文件不存在',
          data: {'imagePath': imagePath},
        );
        return null;
      }
      
      final bytes = await file.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      
      _logger.info(
        module: _logTag,
        function: 'loadImage',
        operation: OperationType.process,
        message: '✅ 图像加载成功',
        data: {
          'width': frame.image.width,
          'height': frame.image.height,
        },
      );
      
      return frame.image;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: 'loadImage',
        operation: OperationType.process,
        message: '❌ 图像加载异常',
        data: {'error': e.toString()},
      );
      return null;
    }
  }
  
  /// 获取测试图像路径
  Future<String?> getTestImagePath() async {
    _logger.info(
      module: _logTag,
      function: 'getTestImagePath',
      operation: OperationType.system,
      message: '🔄 获取测试图像路径',
    );
    
    try {
      // 检查测试数据目录
      final testDataDir = Directory('testdata');
      if (!await testDataDir.exists()) {
        _logger.warning(
          module: _logTag,
          function: 'getTestImagePath',
          operation: OperationType.system,
          message: '⚠️ 测试数据目录不存在，创建目录',
        );
        await testDataDir.create();
      }
      
      // 查找测试图像
      final testImages = await testDataDir
          .list()
          .where((entity) => 
              entity is File && 
              entity.path.toLowerCase().endsWith('.jpg') || 
              entity.path.toLowerCase().endsWith('.png'))
          .toList();
      
      if (testImages.isEmpty) {
        _logger.warning(
          module: _logTag,
          function: 'getTestImagePath',
          operation: OperationType.system,
          message: '⚠️ 测试数据目录中没有图像',
        );
        return null;
      }
      
      final testImagePath = testImages.first.path;
      
      _logger.info(
        module: _logTag,
        function: 'getTestImagePath',
        operation: OperationType.system,
        message: '✅ 找到测试图像',
        data: {'path': testImagePath},
      );
      
      return testImagePath;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: 'getTestImagePath',
        operation: OperationType.system,
        message: '❌ 获取测试图像路径异常',
        data: {'error': e.toString()},
      );
      return null;
    }
  }
}
