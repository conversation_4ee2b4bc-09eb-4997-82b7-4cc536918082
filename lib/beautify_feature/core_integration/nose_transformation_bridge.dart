import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import '../utils/logger.dart';
import '../utils/performance_monitor.dart';

/// 鼻部变形桥接类
/// 
/// 用于桥接Dart代码和Python实现的鼻部变形算法
class NoseTransformationBridge {
  /// 日志标签
  static const String _logTag = 'NoseTransformationBridge';
  
  /// Python脚本路径
  static const String _pythonScriptPath = 'core/nose_transformation.py';
  
  /// 日志工具
  static final _logger = BeautifyLogger();
  
  /// 检查Python脚本是否存在
  static Future<bool> checkPythonScript() async {
    final scriptFile = File(_pythonScriptPath);
    final exists = await scriptFile.exists();
    
    if (!exists) {
      _logger.error(
        module: _logTag,
        function: '检查脚本',
        operation: OperationType.check,
        message: '鼻部变形Python脚本不存在: $_pythonScriptPath'
      );
    } else {
      _logger.info(
        module: _logTag,
        function: '检查脚本',
        operation: OperationType.check,
        message: '鼻部变形Python脚本存在: $_pythonScriptPath'
      );
    }
    
    return exists;
  }
  
  /// 应用鼻部变形
  /// 
  /// [imagePath] 输入图像路径
  /// [parameters] 变形参数
  /// [outputPath] 输出图像路径，可选
  /// 返回输出图像路径
  static Future<String> applyTransformation({
    required String imagePath,
    required Map<String, dynamic> parameters,
    String? outputPath,
  }) async {
    final stopwatch = Stopwatch()..start();
    _logger.info(
      module: _logTag,
      function: '应用变形',
      operation: OperationType.process,
      message: '开始处理 | 图像路径: $imagePath'
    );
    
    try {
      // 检查Python脚本是否存在
      final scriptExists = await checkPythonScript();
      if (!scriptExists) {
        throw Exception('鼻部变形Python脚本不存在');
      }
      
      // 准备输出路径
      final finalOutputPath = outputPath ?? '${imagePath.substring(0, imagePath.lastIndexOf('.'))}_transformed.jpg';
      
      // 准备参数
      final paramJson = jsonEncode(parameters);
      
      // 准备命令
      final python = Platform.isWindows ? 'python' : 'python3';
      final command = python;
      final arguments = [
        _pythonScriptPath,
        'transform',
        '--input', imagePath,
        '--output', finalOutputPath,
        '--params', paramJson,
      ];
      
      // 记录性能指标
      final performanceMonitor = PerformanceMonitor();
      final perfStopwatch = performanceMonitor.startTimer();
      
      // 执行命令
      _logger.info(
        module: _logTag,
        function: '应用变形',
        operation: OperationType.process,
        message: '执行命令: $command ${arguments.join(' ')}'
      );
      
      final process = await Process.run(command, arguments);
      
      // 记录性能指标
      performanceMonitor.stopTimer(
        stopwatch: perfStopwatch,
        category: 'noseTransformation',
        operation: 'applyTransformation',
        data: {'imagePath': imagePath},
      );
      
      // 检查执行结果
      if (process.exitCode != 0) {
        _logger.error(
          module: _logTag,
          function: '应用变形',
          operation: OperationType.process,
          message: '执行失败: ${process.stderr}'
        );
        throw Exception('执行失败: ${process.stderr}');
      }
      
      // 解析输出
      final output = process.stdout.toString().trim();
      Map<String, dynamic> result;
      try {
        result = jsonDecode(output);
      } catch (e) {
        _logger.error(
          module: _logTag,
          function: '应用变形',
          operation: OperationType.process,
          message: '解析输出失败: $e | 输出: $output'
        );
        throw Exception('解析输出失败: $e');
      }
      
      // 检查结果
      if (result['status'] != 'success') {
        _logger.error(
          module: _logTag,
          function: '应用变形',
          operation: OperationType.process,
          message: '处理失败: ${result['message']}'
        );
        throw Exception('处理失败: ${result['message']}');
      }
      
      // 获取结果路径
      final resultPath = result['output_path'] ?? finalOutputPath;
      
      // 检查输出文件是否存在
      final outputFile = File(resultPath);
      if (!await outputFile.exists()) {
        _logger.error(
          module: _logTag,
          function: '应用变形',
          operation: OperationType.process,
          message: '输出文件不存在: $resultPath'
        );
        throw Exception('输出文件不存在: $resultPath');
      }
      
      _logger.info(
        module: _logTag,
        function: '应用变形',
        operation: OperationType.process,
        message: '处理完成 | 输出路径: $resultPath | 耗时: ${stopwatch.elapsedMilliseconds}ms'
      );
      
      return resultPath;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '应用变形',
        operation: OperationType.process,
        message: '处理失败: $e'
      );
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }
  
  /// 预览鼻部变形
  /// 
  /// [imagePath] 输入图像路径
  /// [parameters] 变形参数
  /// 返回变形后的图像数据
  static Future<Uint8List> previewTransformation({
    required String imagePath,
    required Map<String, dynamic> parameters,
  }) async {
    final stopwatch = Stopwatch()..start();
    _logger.info(
      module: _logTag,
      function: '预览变形',
      operation: OperationType.process,
      message: '开始处理 | 图像路径: $imagePath'
    );
    
    try {
      // 应用变形，获取临时输出路径
      final outputPath = await applyTransformation(
        imagePath: imagePath,
        parameters: parameters,
      );
      
      // 读取输出文件
      final outputFile = File(outputPath);
      final bytes = await outputFile.readAsBytes();
      
      // 删除临时文件
      await outputFile.delete();
      
      _logger.info(
        module: _logTag,
        function: '预览变形',
        operation: OperationType.process,
        message: '处理完成 | 图像大小: ${bytes.length} 字节 | 耗时: ${stopwatch.elapsedMilliseconds}ms'
      );
      
      return bytes;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '预览变形',
        operation: OperationType.process,
        message: '处理失败: $e'
      );
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }
}
