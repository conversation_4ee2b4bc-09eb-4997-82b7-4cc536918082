import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/nose_feature_points.dart';
import '../models/nose_transformation_parameters.dart';
import '../services/nose_feature_service.dart';
import '../utils/temp_logger.dart';
import '../utils/performance_monitor.dart';
import 'nose_transformation_bridge.dart';

/// 鼻部变形服务
class NoseTransformationService {
  /// 日志标签
  static const String _logTag = 'NoseTransformationService';
  
  /// 方法通道
  static const MethodChannel _channel = MethodChannel('com.beautifun.nose_transformation');
  
  /// 鼻部特征服务
  final NoseFeatureService _noseFeatureService = NoseFeatureService();
  
  /// 日志工具
  final _logger = TempLogger();
  
  /// 性能监控工具
  final _performanceMonitor = PerformanceMonitor();
  
  /// 构造函数
  NoseTransformationService();
  
  /// 初始化服务
  Future<void> initialize() async {
    try {
      _logger.info(
        module: _logTag,
        function: '初始化',
        operation: OperationType.init,
        message: '开始初始化鼻部变形服务'
      );
      
      // 初始化鼻部特征服务
      await _noseFeatureService.initialize();
      
      _logger.info(
        module: _logTag,
        function: '初始化',
        operation: OperationType.init,
        message: '鼻部变形服务初始化完成'
      );
      
      return;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '初始化',
        operation: OperationType.init,
        message: '鼻部变形服务初始化失败: $e'
      );
      
      rethrow;
    }
  }
  
  /// 应用鼻部变形
  /// 
  /// [imagePath] 输入图像路径
  /// [parameters] 鼻部变形参数
  /// [outputPath] 输出图像路径
  Future<String> applyNoseTransformation({
    required String imagePath,
    required NoseTransformationParameters parameters,
    String? outputPath,
  }) async {
    final stopwatch = Stopwatch()..start();
    _logger.info(
      module: _logTag,
      function: '应用鼻部变形',
      operation: OperationType.transform,
      message: '开始处理 | 图像路径: $imagePath'
    );
    
    try {
      // 提取鼻部特征点
      final noseFeaturePoints = await _noseFeatureService.extractNoseFeaturePoints(imagePath);
      _logger.info(
        module: _logTag,
        function: '应用鼻部变形',
        operation: OperationType.transform,
        message: '提取鼻部特征点完成 | 耗时: ${stopwatch.elapsedMilliseconds}ms'
      );
      
      // 验证鼻部特征点
      final validationResult = _noseFeatureService.validateNoseFeaturePoints(noseFeaturePoints);
      if (!validationResult) {
        throw Exception('无效的鼻部特征点');
      }
      
      // 准备输出路径
      final finalOutputPath = outputPath ?? '${imagePath.substring(0, imagePath.lastIndexOf('.'))}_transformed.jpg';
      
      // 准备参数
      final paramMap = parameters.toSimpleMap();
      
      // 记录性能指标
      _performanceMonitor.startOperation('NoseTransformation');
      
      // 检查Python脚本是否存在
      final scriptExists = await NoseTransformationBridge.checkPythonScript();
      String result;
      
      if (scriptExists) {
        // 使用Python实现
        result = await NoseTransformationBridge.applyTransformation(
          imagePath: imagePath,
          parameters: paramMap,
          outputPath: finalOutputPath,
        );
      } else {
        // 使用方法通道实现
        final nosePointsJson = _noseFeatureService.noseFeaturePointsToJson(noseFeaturePoints);
        
        final channelResult = await _channel.invokeMethod('applyNoseTransformation', {
          'imagePath': imagePath,
          'outputPath': finalOutputPath,
          'noseFeaturePoints': nosePointsJson,
          'parameters': paramMap,
        });
        
        // 验证结果
        if (channelResult == null || channelResult is! String || channelResult.isEmpty) {
          throw Exception('变形处理失败，返回结果无效');
        }
        
        result = channelResult;
      }
      
      // 记录性能指标
      _performanceMonitor.endOperation('NoseTransformation');
      
      _logger.info(
        module: _logTag,
        function: '应用鼻部变形',
        operation: OperationType.transform,
        message: '处理完成 | 输出路径: $result | 总耗时: ${stopwatch.elapsedMilliseconds}ms'
      );
      
      // 返回输出路径
      return result;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '应用鼻部变形',
        operation: OperationType.transform,
        message: '处理失败: $e'
      );
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }
  
  /// 预览鼻部变形
  /// 
  /// [imagePath] 输入图像路径
  /// [parameters] 鼻部变形参数
  /// 返回变形后的图像字节数据
  Future<Uint8List> previewNoseTransformation({
    required String imagePath,
    required NoseTransformationParameters parameters,
  }) async {
    final stopwatch = Stopwatch()..start();
    _logger.info(
      module: _logTag,
      function: '预览鼻部变形',
      operation: OperationType.preview,
      message: '开始处理 | 图像路径: $imagePath'
    );
    
    try {
      // 提取鼻部特征点
      final noseFeaturePoints = await _noseFeatureService.extractNoseFeaturePoints(imagePath);
      _logger.info(
        module: _logTag,
        function: '预览鼻部变形',
        operation: OperationType.preview,
        message: '提取鼻部特征点完成 | 耗时: ${stopwatch.elapsedMilliseconds}ms'
      );
      
      // 验证鼻部特征点
      final validationResult = _noseFeatureService.validateNoseFeaturePoints(noseFeaturePoints);
      if (!validationResult) {
        throw Exception('无效的鼻部特征点');
      }
      
      // 准备参数
      final paramMap = parameters.toSimpleMap();
      
      // 记录性能指标
      _performanceMonitor.startOperation('NoseTransformationPreview');
      
      // 检查Python脚本是否存在
      final scriptExists = await NoseTransformationBridge.checkPythonScript();
      Uint8List result;
      
      if (scriptExists) {
        // 使用Python实现
        result = await NoseTransformationBridge.previewTransformation(
          imagePath: imagePath,
          parameters: paramMap,
        );
      } else {
        // 使用方法通道实现
        final nosePointsJson = _noseFeatureService.noseFeaturePointsToJson(noseFeaturePoints);
        
        final channelResult = await _channel.invokeMethod('previewNoseTransformation', {
          'imagePath': imagePath,
          'noseFeaturePoints': nosePointsJson,
          'parameters': paramMap,
        });
        
        // 验证结果
        if (channelResult == null || channelResult is! Uint8List || channelResult.isEmpty) {
          throw Exception('预览处理失败，返回结果无效');
        }
        
        result = channelResult;
      }
      
      // 记录性能指标
      _performanceMonitor.endOperation('NoseTransformationPreview');
      
      _logger.info(
        module: _logTag,
        function: '预览鼻部变形',
        operation: OperationType.preview,
        message: '处理完成 | 图像大小: ${result.length} 字节 | 总耗时: ${stopwatch.elapsedMilliseconds}ms'
      );
      
      // 返回图像数据
      return result;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '预览鼻部变形',
        operation: OperationType.preview,
        message: '处理失败: $e'
      );
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }
  
  /// 获取医学建议
  /// 
  /// [parameters] 鼻部变形参数
  /// 返回医学建议列表
  Future<List<String>> getMedicalAdvice(NoseTransformationParameters parameters) async {
    _logger.info(
      module: _logTag,
      function: '获取医学建议',
      operation: OperationType.advice,
      message: '开始处理'
    );
    
    try {
      // 检查超过阈值的参数
      final exceededParams = parameters.getParametersExceedingMedicalAdvice();
      
      if (exceededParams.isEmpty) {
        _logger.info(
          module: _logTag,
          function: '获取医学建议',
          operation: OperationType.advice,
          message: '没有参数超过医学建议阈值'
        );
        return [];
      }
      
      // 构建医学建议
      final advice = <String>[];
      
      for (final paramType in exceededParams) {
        final param = parameters.getParameter(paramType)!;
        
        switch (paramType) {
          case NoseParameterType.bridgeHeight:
            advice.add('鼻梁高度调整较大，可能需要鼻骨或鼻软骨移植手术。');
            break;
          case NoseParameterType.bridgeWidth:
            advice.add('鼻梁宽度调整较大，可能需要鼻骨切除或鼻骨内推手术。');
            break;
          case NoseParameterType.tipLength:
            advice.add('鼻尖长度调整较大，可能需要鼻尖软骨重塑手术。');
            break;
          case NoseParameterType.tipHeight:
            advice.add('鼻尖高度调整较大，可能需要鼻尖软骨支架手术。');
            break;
          case NoseParameterType.tipWidth:
            advice.add('鼻尖宽度调整较大，可能需要鼻尖软骨缩窄手术。');
            break;
          case NoseParameterType.nostrilSize:
            advice.add('鼻孔大小调整较大，可能需要鼻孔缩小手术。');
            break;
          case NoseParameterType.nostrilWidth:
            advice.add('鼻翼宽度调整较大，可能需要鼻翼缩窄手术。');
            break;
          case NoseParameterType.baseHeight:
            advice.add('鼻基底高度调整较大，可能需要鼻基底抬高或降低手术。');
            break;
          case NoseParameterType.baseWidth:
            advice.add('鼻基底宽度调整较大，可能需要鼻基底缩窄手术。');
            break;
        }
        
        // 添加通用建议
        advice.add('参数 ${paramType.toString().split('.').last} 调整幅度较大，建议咨询专业医生评估手术风险和恢复周期。');
      }
      
      _logger.info(
        module: _logTag,
        function: '获取医学建议',
        operation: OperationType.advice,
        message: '处理完成 | 建议数量: ${advice.length}'
      );
      
      return advice;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '获取医学建议',
        operation: OperationType.advice,
        message: '处理失败: $e'
      );
      return ['无法获取医学建议: $e'];
    }
  }
  
  /// 释放资源
  Future<void> dispose() async {
    try {
      _logger.info(
        module: _logTag,
        function: '释放资源',
        operation: OperationType.dispose,
        message: '开始释放鼻部变形服务资源'
      );
      
      // 检查Python脚本是否存在
      final scriptExists = await NoseTransformationBridge.checkPythonScript();
      
      if (!scriptExists) {
        // 调用原生方法释放资源
        await _channel.invokeMethod('dispose');
      }
      
      _logger.info(
        module: _logTag,
        function: '释放资源',
        operation: OperationType.dispose,
        message: '鼻部变形服务资源释放完成'
      );
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '释放资源',
        operation: OperationType.dispose,
        message: '鼻部变形服务资源释放失败: $e'
      );
    }
  }
}
