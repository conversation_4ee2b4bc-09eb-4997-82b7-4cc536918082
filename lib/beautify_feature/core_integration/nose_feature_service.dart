import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/feature_point.dart';
import '../models/nose_feature_points.dart';
import '../utils/logger.dart';
import '../utils/performance_monitor.dart';
import 'face_detection_service.dart';

/// 鼻部特征点服务
/// 
/// 集成核心组件进行鼻部特征点检测和处理
class NoseFeatureService {
  /// 单例实例
  static final NoseFeatureService _instance = NoseFeatureService._internal();
  
  /// 工厂构造函数
  factory NoseFeatureService() => _instance;
  
  /// 日志工具
  final BeautifyLogger _logger = BeautifyLogger();
  
  /// 性能监控工具
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  
  /// 面部特征检测服务
  final FaceDetectionService _faceDetectionService = FaceDetectionService();
  
  /// 方法通道
  static const MethodChannel _channel = MethodChannel('beautifun/nose_feature');
  
  /// 日志标签
  static const String _logTag = 'NoseFeatureService';
  
  /// 内部构造函数
  NoseFeatureService._internal() {
    _logger.info(
      module: _logTag,
      function: 'init',
      operation: OperationType.init,
      message: 'NoseFeatureService initialized',
    );
  }
  
  /// 从图像中提取鼻部特征点
  /// 
  /// [imagePath] 图像路径
  /// 返回鼻部特征点对象
  Future<NoseFeaturePoints> extractNoseFeaturePoints(String imagePath) async {
    _logger.info(
      module: _logTag,
      function: 'extractNoseFeaturePoints',
      operation: OperationType.process,
      message: '开始提取鼻部特征点',
      data: {'imagePath': imagePath},
    );
    
    final stopwatch = _performanceMonitor.startTimer();
    
    try {
      // 调用面部特征检测服务获取所有面部特征点
      final allFacePoints = await _faceDetectionService.detectFacialFeatures(imagePath);
      
      _logger.debug(
        module: _logTag,
        function: 'extractNoseFeaturePoints',
        operation: OperationType.process,
        message: '获取到面部特征点',
        data: {'facePointsCount': allFacePoints.length},
      );
      
      // 将Map格式的特征点转换为FeaturePoint对象
      final List<FeaturePoint> featurePoints = allFacePoints.map((point) {
        return FeaturePoint(
          position: Offset(
            point['x'].toDouble(),
            point['y'].toDouble(),
          ),
          z: point['z']?.toDouble() ?? 0.0,
          visibility: point['visibility']?.toDouble() ?? 1.0,
          isPrimary: false,
          confidence: point['confidence'].toDouble(),
          id: point['id'].toString(),
          description: point['description'],
        );
      }).toList();
      
      // 获取MediaPipe索引到鼻部特征点ID的映射
      // 这里可以使用默认映射，也可以从配置中加载
      final mediapipeToNoseMap = NoseFeaturePoints.loadMediaPipeMapping();
      
      // 检查映射是否为空
      if (mediapipeToNoseMap.isEmpty) {
        print('[${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}] 👃 鼻部特征服务 | extractNoseFeaturePoints | ❌ [错误] MediaPipe映射为空');
        return null;
      }
      
      // 创建鼻部特征点对象
      final noseFeaturePoints = NoseFeaturePoints.fromMediaPipePoints(
        allFeaturePoints: featurePoints,
        mediapipeToNoseMap: mediapipeToNoseMap,
      );
      
      _logger.info(
        module: _logTag,
        function: 'extractNoseFeaturePoints',
        operation: OperationType.result,
        message: '鼻部特征点提取完成',
        data: {
          'nosePointsCount': noseFeaturePoints.points.length,
          'connectionsCount': noseFeaturePoints.connections.length,
        },
      );
      
      return noseFeaturePoints;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: 'extractNoseFeaturePoints',
        operation: OperationType.result,
        message: '鼻部特征点提取出错: $e',
      );
      rethrow;
    } finally {
      _performanceMonitor.stopTimer(
        stopwatch: stopwatch,
        category: 'noseFeatureExtraction',
        operation: 'extractNoseFeaturePoints',
        data: {'imagePath': imagePath},
      );
    }
  }
  
  /// 验证鼻部特征点是否有效
  /// 
  /// [nosePoints] 鼻部特征点对象
  /// 返回验证结果和问题描述
  Map<String, dynamic> validateNoseFeaturePoints(NoseFeaturePoints nosePoints) {
    _logger.info(
      module: _logTag,
      function: 'validateNoseFeaturePoints',
      operation: OperationType.process,
      message: '开始验证鼻部特征点',
      data: {'pointsCount': nosePoints.points.length},
    );
    
    final stopwatch = _performanceMonitor.startTimer();
    
    try {
      final List<String> issues = [];
      
      // 检查必要的特征点是否存在
      final requiredPoints = [
        NosePointId.noseTip,
        NosePointId.noseBase,
        NosePointId.noseBridgeTop,
      ];
      
      for (final pointId in requiredPoints) {
        if (!nosePoints.points.containsKey(pointId)) {
          final issue = '缺少必要的特征点: ${pointId.toString()}';
          issues.add(issue);
          
          _logger.warning(
            module: _logTag,
            function: 'validateNoseFeaturePoints',
            operation: OperationType.process,
            message: issue,
          );
        }
      }
      
      // 检查特征点的可见度
      for (final entry in nosePoints.points.entries) {
        final point = entry.value;
        if (point.visibility < 0.7) {
          final issue = '特征点可见度过低: ${entry.key.toString()}, 可见度: ${point.visibility}';
          issues.add(issue);
          
          _logger.warning(
            module: _logTag,
            function: 'validateNoseFeaturePoints',
            operation: OperationType.process,
            message: issue,
          );
        }
      }
      
      final isValid = issues.isEmpty;
      
      _logger.info(
        module: _logTag,
        function: 'validateNoseFeaturePoints',
        operation: OperationType.result,
        message: '鼻部特征点验证完成',
        data: {
          'isValid': isValid,
          'issuesCount': issues.length,
        },
      );
      
      return {
        'isValid': isValid,
        'issues': issues,
      };
    } finally {
      _performanceMonitor.stopTimer(
        stopwatch: stopwatch,
        category: 'noseFeatureValidation',
        operation: 'validateNoseFeaturePoints',
        data: {'pointsCount': nosePoints.points.length},
      );
    }
  }
  
  /// 将鼻部特征点转换为JSON格式
  /// 
  /// [nosePoints] 鼻部特征点对象
  /// 返回JSON格式的特征点数据
  Map<String, dynamic> noseFeaturePointsToJson(NoseFeaturePoints nosePoints) {
    final Map<String, dynamic> result = {
      'points': {},
      'connections': [],
    };
    
    // 转换特征点
    nosePoints.points.forEach((id, point) {
      result['points'][id.toString()] = point.toJson();
    });
    
    // 转换连接线
    for (final connection in nosePoints.connections) {
      result['connections'].add({
        'from': connection.from.toString(),
        'to': connection.to.toString(),
        'isPrimary': connection.isPrimary,
      });
    }
    
    return result;
  }
  
  /// 获取鼻部特征点性能报告
  Map<String, Map<String, dynamic>> getPerformanceReport() {
    return _performanceMonitor.getAllMetricsStats();
  }
  
  /// 打印鼻部特征点性能报告
  void printPerformanceReport() {
    _performanceMonitor.printReport();
  }
}
