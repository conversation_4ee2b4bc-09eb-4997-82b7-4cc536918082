import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'nose_feature_demo_app.dart';
import '../utils/logger.dart';

/// 主函数
void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();
  
  // 设置日志
  BeautifyLogger().setupLogger(
    logToConsole: true,
    logToFile: true,
    logLevel: LogLevel.debug,
  );
  
  // 设置状态栏颜色
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );
  
  // 设置屏幕方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // 关闭其他正在运行的实例
  await _closeOtherInstances();
  
  // 运行应用
  runApp(const NoseFeatureDemoApp());
}

/// 关闭其他正在运行的实例
Future<void> _closeOtherInstances() async {
  try {
    // 获取当前进程ID
    final currentPid = ProcessInfo.currentPid;
    
    // 获取所有Flutter进程
    final result = await Process.run('ps', ['-ef']);
    final lines = (result.stdout as String).split('\n');
    
    // 查找其他Flutter进程
    for (final line in lines) {
      if (line.contains('flutter') && !line.contains('grep')) {
        final parts = line.split(RegExp(r'\s+')).where((s) => s.isNotEmpty).toList();
        if (parts.length > 1) {
          final pid = int.tryParse(parts[1]);
          if (pid != null && pid != currentPid) {
            // 关闭其他进程
            await Process.run('kill', ['-9', pid.toString()]);
            BeautifyLogger().info(
              module: 'main',
              function: '_closeOtherInstances',
              operation: OperationType.system,
              message: '关闭其他Flutter进程',
              data: {'pid': pid},
            );
          }
        }
      }
    }
  } catch (e) {
    BeautifyLogger().error(
      module: 'main',
      function: '_closeOtherInstances',
      operation: OperationType.system,
      message: '关闭其他实例异常',
      data: {'error': e.toString()},
    );
  }
}
