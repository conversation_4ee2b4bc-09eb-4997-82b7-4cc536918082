import 'package:flutter/material.dart';
import '../ui/demo/nose_feature_demo_panel.dart';

/// 鼻部特征点演示应用
class NoseFeatureDemoApp extends StatelessWidget {
  /// 构造函数
  const NoseFeatureDemoApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Beau<PERSON>Fun 鼻部特征点演示',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        brightness: Brightness.light,
        useMaterial3: true,
      ),
      darkTheme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        brightness: Brightness.dark,
        useMaterial3: true,
      ),
      themeMode: ThemeMode.system,
      home: const NoseFeatureDemoPanel(),
    );
  }
}
