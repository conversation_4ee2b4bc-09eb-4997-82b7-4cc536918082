import 'package:flutter/material.dart';
import 'ui/beautify_screen.dart';
import 'utils/logger.dart';
import 'utils/performance_monitor.dart';
import 'core_integration/nose_feature_service.dart';
import 'models/nose_feature_points.dart';
import 'models/nose_transformation_parameters.dart';
import 'core_integration/nose_transformation_service.dart';
import 'services/nose_transformation_integration_service.dart';
import 'animations/nose_feature_animation.dart';
import 'core_integration/nose_feature_connector.dart';
import 'ui/demo/nose_feature_demo_panel.dart';
import 'demo/nose_feature_demo_app.dart';

/// BeautiFun "炫"功能的主入口类
/// 
/// 此类负责初始化"炫"功能并提供导航到"炫"功能界面的方法
class BeautifyFeature {
  /// 单例实例
  static final BeautifyFeature _instance = BeautifyFeature._internal();
  
  /// 工厂构造函数，返回单例实例
  factory BeautifyFeature() {
    return _instance;
  }
  
  /// 日志工具
  final BeautifyLogger _logger = BeautifyLogger();
  
  /// 性能监控工具
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  
  /// 私有构造函数
  BeautifyFeature._internal() {
    _logger.info(
      module: 'BeautifyFeature',
      function: 'init',
      operation: OperationType.init,
      message: '🚀 BeautifyFeature 初始化',
    );
    
    // 确保关闭其他正在运行的程序实例
    _closeRunningInstances();
  }
  
  /// 关闭其他正在运行的程序实例
  Future<void> _closeRunningInstances() async {
    _logger.info(
      module: 'BeautifyFeature',
      function: '_closeRunningInstances',
      operation: OperationType.system,
      message: '🔄 检查并关闭其他正在运行的程序实例',
    );
    
    // 这里可以添加关闭其他实例的逻辑
    // 例如使用平台通道调用原生代码来检查和关闭其他实例
    
    _logger.info(
      module: 'BeautifyFeature',
      function: '_closeRunningInstances',
      operation: OperationType.system,
      message: '✅ 确保只有当前实例在运行',
    );
  }
  
  /// 导航到"炫"功能界面
  /// 
  /// [context] BuildContext用于导航
  /// [imagePath] 可选的图像路径，如果提供，将在"炫"功能中加载此图像
  static Future<void> navigateToBeautifyScreen(BuildContext context, {String? imagePath}) async {
    final instance = BeautifyFeature();
    
    instance._logger.info(
      module: 'BeautifyFeature',
      function: 'navigateToBeautifyScreen',
      operation: OperationType.ui,
      message: '🔄 导航到BeautifyScreen',
      data: {'imagePath': imagePath},
    );
    
    instance._performanceMonitor.startMonitoring('navigateToBeautifyScreen');
    
    // 导航到BeautifyScreen
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BeautifyScreen(imagePath: imagePath),
      ),
    );
    
    instance._performanceMonitor.stopMonitoring('navigateToBeautifyScreen');
    
    instance._logger.info(
      module: 'BeautifyFeature',
      function: 'navigateToBeautifyScreen',
      operation: OperationType.ui,
      message: '✅ 从BeautifyScreen返回',
    );
  }
  
  /// 获取鼻部特征点服务
  static NoseFeatureService getNoseFeatureService() {
    return NoseFeatureService();
  }
  
  /// 获取鼻部变形服务
  static NoseTransformationService getNoseTransformationService() {
    return NoseTransformationService();
  }
  
  /// 获取鼻部变形集成服务
  static NoseTransformationIntegrationService getNoseTransformationIntegrationService() {
    // 使用单例模式，确保只有一个实例
    static NoseTransformationIntegrationService? _instance;
    if (_instance == null) {
      _instance = NoseTransformationIntegrationService();
    }
    return _instance;
  }
}

/// 提供一个全局访问点
final beautifyFeature = BeautifyFeature();

// 导出鼻部特征点相关类和服务
export 'models/nose_feature_points.dart';
export 'models/nose_transformation_parameters.dart';
export 'core_integration/nose_feature_service.dart';
export 'core_integration/nose_transformation_service.dart';
export 'services/nose_transformation_integration_service.dart';
export 'animations/nose_feature_animation.dart';
export 'core_integration/nose_feature_connector.dart';
export 'ui/demo/nose_feature_demo_panel.dart';
export 'demo/nose_feature_demo_app.dart';
