import 'dart:math' as math;
import 'dart:math' show Random;
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import '../models/feature_point.dart';
import '../utils/temp_logger.dart';
import '../../core/feature_points_helper.dart';
import '../../core/feature_points_data.dart' as fpd;

/// 连接线类
class ConnectingLine {
  final int startPointIndex;
  final int endPointIndex;
  final Color color;
  final double width;
  final bool isPrimary;
  
  ConnectingLine({
    required this.startPointIndex,
    required this.endPointIndex,
    required this.color,
    this.width = 1.0,
    this.isPrimary = false,
  });
}

/// 鼻部特征点高级动画类
/// 实现设计文档中定义的鼻部特征点高级动画效果
class NoseFeatureAnimation extends StatefulWidget {
  final List<FeaturePoint> featurePoints;
  final Size imageSize;
  final Size displaySize;
  final bool showConnectingLines;
  final bool showLabels;
  final Duration scanDuration;
  final VoidCallback? onScanComplete;

  const NoseFeatureAnimation({
    Key? key,
    required this.featurePoints,
    required this.imageSize,
    required this.displaySize,
    this.showConnectingLines = false,
    this.showLabels = true,
    this.scanDuration = const Duration(seconds: 2),
    this.onScanComplete,
  }) : super(key: key);

  @override
  State<NoseFeatureAnimation> createState() => _NoseFeatureAnimationState();
}

class _NoseFeatureAnimationState extends State<NoseFeatureAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scanAnimation;
  bool _scanComplete = false;
  final Map<String, double> _pointDelays = {};

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat();

    _pulseAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    
    // 扫描动画
    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(0.0, 0.3, curve: Curves.easeInOut),
      ),
    );

    // 为每个特征点生成随机延迟
    _generateRandomDelays();

    // 监听扫描动画完成
    _scanAnimation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _scanComplete = true;
        });
        if (widget.onScanComplete != null) {
          widget.onScanComplete!();
        }
      }
    });
    
    // 强制在1秒后设置扫描完成状态，确保特征点显示
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _scanComplete = true;
        });
      }
    });
  }

  void _generateRandomDelays() {
    final random = Random();
    for (var point in widget.featurePoints) {
      // 生成0.0到0.5之间的随机延迟
      _pointDelays[point.id] = random.nextDouble() * 0.5;
    }
  }

  @override
  void didUpdateWidget(NoseFeatureAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.featurePoints != oldWidget.featurePoints) {
      _generateRandomDelays();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          size: widget.displaySize,
          painter: _NoseFeaturePainter(
            featurePoints: widget.featurePoints,
            imageSize: widget.imageSize,
            displaySize: widget.displaySize,
            animationValue: _pulseAnimation.value,
            scanAnimationValue: _scanAnimation.value,
            showConnectingLines: widget.showConnectingLines,
            showLabels: widget.showLabels,
            scanComplete: _scanComplete,
            pointDelays: _pointDelays,
          ),
        );
      },
    );
  }
}

class _NoseFeaturePainter extends CustomPainter {
  final List<FeaturePoint> featurePoints;
  final Size imageSize;
  final Size displaySize;
  final double animationValue;
  final double scanAnimationValue;
  final bool showConnectingLines;
  final bool showLabels;
  final bool scanComplete;
  final Map<String, double> pointDelays;

  _NoseFeaturePainter({
    required this.featurePoints,
    required this.imageSize,
    required this.displaySize,
    required this.animationValue,
    required this.scanAnimationValue,
    required this.showConnectingLines,
    required this.showLabels,
    required this.scanComplete,
    required this.pointDelays,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制扫描效果
    _drawScanEffect(canvas, size);
    
    // 绘制连接线
    if (showConnectingLines) {
      _drawConnectingLines(canvas, size);
    }
    
    // 绘制特征点 - 暂时禁用，使用SimpleDeformationPainter中的特征点绘制
    // _drawFeaturePoints(canvas, size);
    
    // 绘制标签
    if (showLabels) {
      _drawLabels(canvas, size);
    }
  }

  void _drawScanEffect(Canvas canvas, Size size) {
    if (scanAnimationValue < 1.0) {
      final scanPaint = Paint()
        ..color = Colors.cyan.withOpacity(0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;
      
      final scanRadius = scanAnimationValue * size.height;
      canvas.drawCircle(
        Offset(size.width / 2, size.height / 2),
        scanRadius,
        scanPaint,
      );
      
      // 添加扫描线
      final scanLinePaint = Paint()
        ..color = Colors.cyan.withOpacity(0.7)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      for (int i = 0; i < 8; i++) {
        final angle = i * math.pi / 4;
        final x = size.width / 2 + math.cos(angle) * scanRadius;
        final y = size.height / 2 + math.sin(angle) * scanRadius;
        
        canvas.drawLine(
          Offset(size.width / 2, size.height / 2),
          Offset(x, y),
          scanLinePaint,
        );
      }
    }
  }

  void _drawFeaturePoints(Canvas canvas, Size size) {
    // 方法已清空，特征点绘制统一由LandmarksPainter处理
  }

  void _drawConnectingLines(Canvas canvas, Size size) {
    if (featurePoints.length < 2) return;
    
    // 使用 FeaturePointsHelper 获取鼻部特征点
    final helper = FeaturePointsHelper();
    
    // 获取鼻梁高度参数的特征点
    final bridgePoints = helper.getParameterPoints('nose', 'bridge_height');
    
    // 获取鼻尖调整参数的特征点
    final tipPoints = helper.getParameterPoints('nose', 'tip_adjust');
    
    // 获取鼻翼宽度参数的特征点
    final nostrilPoints = helper.getParameterPoints('nose', 'nostril_width');
    
    // 创建鼻部连接线列表
    final List<List<int>> noseConnections = [];
    
    // 如果有鼻梁特征点，添加鼻梁中线
    if (bridgePoints.isNotEmpty) {
      // 按照从上到下的顺序排序
      final sortedBridgePoints = List<int>.from(bridgePoints)..sort();
      noseConnections.add(sortedBridgePoints.take(6).toList());
    }
    
    // 如果有鼻尖特征点，添加鼻尖轮廓
    if (tipPoints.isNotEmpty) {
      // 按照从左到右的顺序排序
      final sortedTipPoints = List<int>.from(tipPoints)..sort();
      noseConnections.add(sortedTipPoints.take(7).toList());
    }
    
    // 如果有鼻翼特征点，添加鼻翼连接线
    if (nostrilPoints.isNotEmpty) {
      // 获取鼻部特征点映射中定义的对称点对
      final symmetryPairs = _getNoseSymmetryPairs();
      
      // 分离左右鼻翼特征点，确保对称性
      final leftNostrilPoints = <int>[];
      final rightNostrilPoints = <int>[];
      
      for (var pointId in nostrilPoints) {
        // 根据特征点ID判断左右
        if (_isLeftNostrilPoint(pointId)) {
          leftNostrilPoints.add(pointId);
        } else if (_isRightNostrilPoint(pointId)) {
          rightNostrilPoints.add(pointId);
        }
      }
      
      // 确保左右鼻翼特征点数量相等
      final minCount = math.min(leftNostrilPoints.length, rightNostrilPoints.length);
      
      // 添加左侧鼻翼连接线
      if (leftNostrilPoints.isNotEmpty) {
        leftNostrilPoints.sort();
        noseConnections.add(leftNostrilPoints.take(minCount).toList());
      }
      
      // 添加右侧鼻翼连接线
      if (rightNostrilPoints.isNotEmpty) {
        rightNostrilPoints.sort();
        noseConnections.add(rightNostrilPoints.take(minCount).toList());
      }
      
      // 添加左右鼻翼对称连接线
      for (var i = 0; i < minCount; i++) {
        if (i < leftNostrilPoints.length && i < rightNostrilPoints.length) {
          noseConnections.add([leftNostrilPoints[i], rightNostrilPoints[i]]);
        }
      }
    }
    
    // 绘制连接线
    for (var connection in noseConnections) {
      final path = Path();
      bool isFirst = true;
      
      for (var idx in connection) {
        // 找到对应ID的特征点
        final pointId = 'point_$idx';
        final point = featurePoints.firstWhere(
          (p) => p.id == pointId,
          orElse: () => featurePoints[0], // 默认使用第一个点
        );
        
        final x = point.position.dx * size.width;
        final y = (imageSize.height - point.position.dy) * size.height / imageSize.height; // Y坐标翻转
        
        if (isFirst) {
          path.moveTo(x, y);
          isFirst = false;
        } else {
          path.lineTo(x, y);
        }
      }
      
      // 创建渐变效果
      final gradient = LinearGradient(
        colors: [
          Colors.cyanAccent.withOpacity(0.7 * animationValue),
          Colors.blue.withOpacity(0.7 * animationValue),
        ],
      ).createShader(Rect.fromPoints(
        Offset(0, 0),
        Offset(size.width, size.height),
      ));
      
      final paint = Paint()
        ..shader = gradient
        ..strokeWidth = 1.5
        ..style = PaintingStyle.stroke;
      
      canvas.drawPath(path, paint);
    }
  }
  
  // 获取鼻部特征点对称点对
  List<List<int>> _getNoseSymmetryPairs() {
    return [
      [49, 279],   // 鼻翼对称点
      [98, 327],   // 轮廓对称点
      [97, 326],   // 轮廓对称点
      [330, 359],  // 鼻孔对称点
      [331, 360],  // 鼻孔对称点
      [332, 361],  // 鼻孔对称点
    ];
  }
  
  // 判断是否为左侧鼻翼特征点
  bool _isLeftNostrilPoint(int pointId) {
    // 左侧鼻翼特征点ID通常较小
    // 这里使用测试目录中的参考实现
    return pointId < 200 && (
      pointId == 49 || pointId == 98 || pointId == 97 || 
      pointId == 330 || pointId == 331 || pointId == 332
    );
  }
  
  // 判断是否为右侧鼻翼特征点
  bool _isRightNostrilPoint(int pointId) {
    // 右侧鼻翼特征点ID通常较大
    // 这里使用测试目录中的参考实现
    return pointId > 200 && (
      pointId == 279 || pointId == 327 || pointId == 326 || 
      pointId == 359 || pointId == 360 || pointId == 361
    );
  }

  void _drawLabels(Canvas canvas, Size size) {
    // 使用 FeaturePointsHelper 获取鼻部主要特征点
    final helper = FeaturePointsHelper();
    
    // 获取所有鼻部参数的特征点
    final bridgePoints = helper.getParameterPoints('nose', 'bridge_height');
    final tipPoints = helper.getParameterPoints('nose', 'tip_adjust');
    final nostrilPoints = helper.getParameterPoints('nose', 'nostril_width');
    final basePoints = helper.getParameterPoints('nose', 'base_height');
    
    // 合并所有主要特征点
    final Set<int> primaryPointIds = {};
    
    // 添加所有参数的主要特征点
    final noseConfig = fpd.beautyAreaConfigs['nose'];
    if (noseConfig != null) {
      // 遍历鼻部所有参数
      for (final param in ['bridge_height', 'tip_adjust', 'nostril_width', 'base_height']) {
        final paramConfig = noseConfig.parameters[param];
        if (paramConfig != null && paramConfig.primaryPoints.isNotEmpty) {
          primaryPointIds.addAll(paramConfig.primaryPoints);
        }
      }
    }
    
    // 只为主要特征点添加标签
    for (var point in featurePoints) {
      // 从 point.id 中提取数字部分
      final idMatch = RegExp(r'point_(\d+)').firstMatch(point.id);
      if (idMatch != null) {
        final pointIdNumber = int.tryParse(idMatch.group(1) ?? '');
        
        if ((pointIdNumber != null && primaryPointIds.contains(pointIdNumber) || point.isPrimary) && 
            point.description != null && 
            point.description!.isNotEmpty) {
          
          final x = point.position.dx * size.width;
          final y = (imageSize.height - point.position.dy) * size.height / imageSize.height; // Y坐标翻转
          
          // 创建文本画笔
          final textStyle = TextStyle(
            color: Colors.white,
            fontSize: 10.0,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                blurRadius: 3.0,
                color: Colors.black.withOpacity(0.7),
                offset: const Offset(1.0, 1.0),
              ),
            ],
          );
          
          // 创建文本绘制器
          final textPainter = TextPainter(
            text: TextSpan(
              text: point.description,
              style: textStyle,
            ),
            textDirection: TextDirection.ltr,
          );
          
          // 布局文本
          textPainter.layout();
          
          // 绘制文本 - 偏移以避免遮挡特征点
          textPainter.paint(
            canvas,
            Offset(x + 10, y - 10),
          );
          
          // 绘制连接线
          final linePaint = Paint()
            ..color = Colors.white.withOpacity(0.7)
            ..strokeWidth = 0.5;
          
          canvas.drawLine(
            Offset(x, y),
            Offset(x + 8, y - 8),
            linePaint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant _NoseFeaturePainter oldDelegate) {
    return animationValue != oldDelegate.animationValue;
  }
}
