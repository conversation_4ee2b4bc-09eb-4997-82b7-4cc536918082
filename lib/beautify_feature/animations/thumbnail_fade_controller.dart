import 'package:flutter/material.dart';
import '../../utils/logger.dart';

/// 缩略图淡出控制器
/// 
/// 控制缩略图向下消失的动画效果
class ThumbnailFadeController extends ChangeNotifier {
  /// 动画控制器
  late AnimationController _fadeController;
  
  /// 缩略图动画列表
  late List<Animation<Offset>> _thumbnailSlideAnimations;
  late List<Animation<double>> _thumbnailFadeAnimations;
  
  /// 是否已完成动画
  bool _isCompleted = false;
  
  /// 动画完成回调
  VoidCallback? _onCompleted;
  
  /// 获取缩略图偏移动画
  Animation<Offset> getSlideAnimation(int index) {
    if (index < 0 || index >= _thumbnailSlideAnimations.length) {
      return const AlwaysStoppedAnimation(Offset.zero);
    }
    return _thumbnailSlideAnimations[index];
  }
  
  /// 获取缩略图淡出动画
  Animation<double> getFadeAnimation(int index) {
    if (index < 0 || index >= _thumbnailFadeAnimations.length) {
      return const AlwaysStoppedAnimation(1.0);
    }
    return _thumbnailFadeAnimations[index];
  }
  
  /// 获取是否已完成动画
  bool get isCompleted => _isCompleted;
  
  /// 初始化控制器
  void initialize(TickerProvider vsync, {VoidCallback? onCompleted}) {
    Logger.i('缩略图淡出控制器', '初始化缩略图淡出控制器');
    
    _onCompleted = onCompleted;
    
    // 初始化动画控制器 - 使用适当的时长确保动画丰满且不拖沉
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),  // 缩短时长使动画更加丰满且不拖沉
      vsync: vsync,
    );
    
    // 添加状态监听器
    _fadeController.addStatusListener(_handleAnimationStatus);
    
    // 初始化缩略图动画 - 改为向右丰满且优雅地滑出
    _thumbnailSlideAnimations = List.generate(
      4,
      (index) {
        // 根据索引交替使用不同的滑出方向，增加视觉多样性
        final Offset endOffset = Offset(2.0, 0.0);  // 统一使用向右水平滑出
            
        return Tween<Offset>(
          begin: Offset.zero,
          end: endOffset,
        ).animate(CurvedAnimation(
          parent: _fadeController,
          curve: Interval(
            0.0 + (index * 0.08),  // 稍微错开开始时间
            0.5 + (index * 0.08),  // 增加动画时长使其更加丰满
            curve: Curves.easeOutQuint,  // 使用更加丰满的曲线，强调结尾的丰满感
          ),
        ));
      },
    );
    
    _thumbnailFadeAnimations = List.generate(
      4,
      (index) => Tween<double>(
        begin: 1.0,
        end: 0.0,
      ).animate(CurvedAnimation(
        parent: _fadeController,
        curve: Interval(
          0.05 + (index * 0.08),  // 与滑动动画同时开始，但稍微滞后
          0.45 + (index * 0.08),  // 与滑动动画同步结束
          curve: Curves.easeOutQuart,  // 使用与滑动相配合的曲线
        ),
      )),
    );
  }
  
  /// 处理动画状态变化
  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      Logger.i('缩略图淡出控制器', '缩略图淡出动画完成');
      _isCompleted = true;
      if (_onCompleted != null) {
        _onCompleted!();
      }
    }
  }
  
  /// 开始动画
  void startAnimation() {
    Logger.i('缩略图淡出控制器', '开始缩略图淡出动画');
    _isCompleted = false;
    _fadeController.forward(from: 0.0);
  }
  
  /// 重置动画
  void reset() {
    Logger.i('缩略图淡出控制器', '重置缩略图淡出动画');
    _isCompleted = false;
    _fadeController.reset();
  }
  
  @override
  void dispose() {
    _fadeController.removeStatusListener(_handleAnimationStatus);
    _fadeController.dispose();
    super.dispose();
  }
}
