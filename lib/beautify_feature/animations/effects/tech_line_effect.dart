import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../../models/feature_point_model.dart';

/// 科技线条效果
/// 
/// 提供特征点科技线条动画效果的实现
/// 此文件是模块化鼻部特征点可视化系统的动画效果层

/// 科技线条效果类型枚举
enum TechLineEffectType {
  /// 垂直线条 - 从特征点垂直延伸的线条
  vertical,
  
  /// 水平线条 - 从特征点水平延伸的线条
  horizontal,
  
  /// 刻度线条 - 带有刻度的线条
  scale,
  
  /// 虚线 - 虚线效果
  dashed,
}

/// 科技线条动画效果类
class TechLineEffect {
  /// 线条效果类型
  final TechLineEffectType type;
  
  /// 线条长度
  final double length;
  
  /// 线条颜色
  final Color color;
  
  /// 线条宽度
  final double width;
  
  /// 构造函数
  TechLineEffect({
    this.type = TechLineEffectType.vertical,
    required this.length,
    required this.color,
    this.width = 1.0,
  });
  
  /// 绘制科技线条效果
  void draw(Canvas canvas, Offset center, double animationValue) {
    switch (type) {
      case TechLineEffectType.vertical:
        _drawVerticalLine(canvas, center, animationValue);
        break;
      case TechLineEffectType.horizontal:
        _drawHorizontalLine(canvas, center, animationValue);
        break;
      case TechLineEffectType.scale:
        _drawScaleLine(canvas, center, animationValue);
        break;
      case TechLineEffectType.dashed:
        _drawDashedLine(canvas, center, animationValue);
        break;
    }
  }
  
  /// 绘制垂直线条
  void _drawVerticalLine(Canvas canvas, Offset center, double animationValue) {
    // 计算线条起点和终点
    final start = Offset(center.dx, center.dy - length / 2);
    final end = Offset(center.dx, center.dy + length / 2);
    
    // 创建线条渐变
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        color.withOpacity(0.0),
        color.withOpacity(0.8),
        color.withOpacity(0.0),
      ],
      stops: const [0.0, 0.5, 1.0],
    );
    
    // 创建线条画笔
    final linePaint = Paint()
      ..shader = gradient.createShader(Rect.fromPoints(start, end))
      ..strokeWidth = width
      ..style = PaintingStyle.stroke;
    
    // 绘制线条
    canvas.drawLine(start, end, linePaint);
    
    // 绘制线条上的动画点
    final pointPhase = (animationValue * 2) % 1.0;
    final pointPosition = Offset(
      center.dx,
      start.dy + (end.dy - start.dy) * pointPhase,
    );
    
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(pointPosition, width * 1.5, pointPaint);
  }
  
  /// 绘制水平线条
  void _drawHorizontalLine(Canvas canvas, Offset center, double animationValue) {
    // 计算线条起点和终点
    final start = Offset(center.dx - length / 2, center.dy);
    final end = Offset(center.dx + length / 2, center.dy);
    
    // 创建线条渐变
    final gradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        color.withOpacity(0.0),
        color.withOpacity(0.8),
        color.withOpacity(0.0),
      ],
      stops: const [0.0, 0.5, 1.0],
    );
    
    // 创建线条画笔
    final linePaint = Paint()
      ..shader = gradient.createShader(Rect.fromPoints(start, end))
      ..strokeWidth = width
      ..style = PaintingStyle.stroke;
    
    // 绘制线条
    canvas.drawLine(start, end, linePaint);
    
    // 绘制线条上的动画点
    final pointPhase = (animationValue * 2) % 1.0;
    final pointPosition = Offset(
      start.dx + (end.dx - start.dx) * pointPhase,
      center.dy,
    );
    
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(pointPosition, width * 1.5, pointPaint);
  }
  
  /// 绘制刻度线条
  void _drawScaleLine(Canvas canvas, Offset center, double animationValue) {
    // 保存画布状态
    canvas.save();
    
    // 移动画布原点到中心点
    canvas.translate(center.dx, center.dy);
    
    // 计算线条起点和终点
    final start = Offset(0, -length / 2);
    final end = Offset(0, length / 2);
    
    // 创建线条画笔
    final linePaint = Paint()
      ..color = color.withOpacity(0.7)
      ..strokeWidth = width
      ..style = PaintingStyle.stroke;
    
    // 绘制主线条
    canvas.drawLine(start, end, linePaint);
    
    // 绘制刻度
    final scaleCount = 10;
    final scaleLength = length / 20;
    final scaleSpacing = length / scaleCount;
    
    for (int i = 0; i <= scaleCount; i++) {
      // 计算刻度位置
      final scaleY = start.dy + i * scaleSpacing;
      
      // 计算刻度不透明度（基于动画值）
      final scalePhase = (animationValue * 2 + i / scaleCount) % 1.0;
      final scaleOpacity = math.sin(scalePhase * math.pi) * 0.7;
      
      // 创建刻度画笔
      final scalePaint = Paint()
        ..color = color.withOpacity(scaleOpacity)
        ..strokeWidth = width * 0.8
        ..style = PaintingStyle.stroke;
      
      // 绘制刻度线
      canvas.drawLine(
        Offset(-scaleLength, scaleY),
        Offset(scaleLength, scaleY),
        scalePaint,
      );
    }
    
    // 恢复画布状态
    canvas.restore();
  }
  
  /// 绘制虚线
  void _drawDashedLine(Canvas canvas, Offset center, double animationValue) {
    // 保存画布状态
    canvas.save();
    
    // 移动画布原点到中心点
    canvas.translate(center.dx, center.dy);
    
    // 计算虚线偏移
    final dashOffset = (animationValue * 10) % 10.0;
    
    // 创建虚线画笔
    final dashPaint = Paint()
      ..color = color.withOpacity(0.7)
      ..strokeWidth = width
      ..style = PaintingStyle.stroke;
    
    // 绘制垂直虚线
    final dashPath = Path();
    final dashLength = 5.0;
    final gapLength = 3.0;
    final totalLength = length;
    
    double currentPos = -totalLength / 2 - dashOffset;
    
    while (currentPos < totalLength / 2) {
      // 添加虚线段
      dashPath.moveTo(0, currentPos);
      dashPath.lineTo(0, currentPos + dashLength);
      
      // 更新位置
      currentPos += dashLength + gapLength;
    }
    
    // 绘制虚线
    canvas.drawPath(dashPath, dashPaint);
    
    // 恢复画布状态
    canvas.restore();
  }
  
  /// 为特征点绘制科技线条效果
  void drawForPoint(Canvas canvas, FeaturePoint point, double animationValue) {
    // 只为高亮或选中的点绘制科技线条效果
    if (point.state == FeaturePointState.highlighted || 
        point.state == FeaturePointState.selected) {
      
      // 根据点的重要性选择不同的线条效果
      TechLineEffectType effectType;
      double lineLength;
      
      if (point.isPrimary) {
        // 主要特征点使用刻度线
        effectType = TechLineEffectType.scale;
        lineLength = point.animatedRadius * 15.0;
      } else if (point.isSecondary) {
        // 次要特征点使用虚线
        effectType = TechLineEffectType.dashed;
        lineLength = point.animatedRadius * 10.0;
      } else {
        // 普通特征点使用垂直线
        effectType = TechLineEffectType.vertical;
        lineLength = point.animatedRadius * 8.0;
      }
      
      // 创建科技线条效果
      final techLineEffect = TechLineEffect(
        type: effectType,
        length: lineLength,
        color: point.color,
        width: point.animatedRadius * 0.3,
      );
      
      // 绘制科技线条效果
      techLineEffect.draw(canvas, point.position, animationValue);
    }
  }
}
