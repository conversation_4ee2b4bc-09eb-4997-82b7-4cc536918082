import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../../models/feature_point_model.dart';

/// 脉冲动画效果
/// 
/// 提供特征点脉冲动画效果的实现
/// 此文件是模块化鼻部特征点可视化系统的动画效果层

/// 脉冲效果类型枚举
enum PulseEffectType {
  /// 简单脉冲 - 简单的缩放效果
  simple,
  
  /// 多重脉冲 - 多个波形叠加的复杂脉冲效果
  multi,
  
  /// 呼吸脉冲 - 模拟呼吸的缓慢脉冲效果
  breathing,
  
  /// 科技脉冲 - 带有科技感的脉冲效果
  tech,
}

/// 脉冲动画效果类
class PulseEffect {
  /// 脉冲效果类型
  final PulseEffectType type;
  
  /// 脉冲速度（1.0为正常速度）
  final double speed;
  
  /// 脉冲强度（1.0为正常强度）
  final double intensity;
  
  /// 脉冲颜色
  final Color color;
  
  /// 构造函数
  PulseEffect({
    this.type = PulseEffectType.simple,
    this.speed = 1.0,
    this.intensity = 1.0,
    required this.color,
  });
  
  /// 绘制脉冲效果
  void draw(Canvas canvas, FeaturePoint point, double animationValue) {
    switch (type) {
      case PulseEffectType.simple:
        _drawSimplePulse(canvas, point, animationValue);
        break;
      case PulseEffectType.multi:
        _drawMultiPulse(canvas, point, animationValue);
        break;
      case PulseEffectType.breathing:
        _drawBreathingPulse(canvas, point, animationValue);
        break;
      case PulseEffectType.tech:
        _drawTechPulse(canvas, point, animationValue);
        break;
    }
  }
  
  /// 绘制简单脉冲效果
  void _drawSimplePulse(Canvas canvas, FeaturePoint point, double animationValue) {
    // 计算脉冲半径
    final baseRadius = point.animatedRadius;
    final maxPulseRadius = baseRadius * 3.0;
    
    // 计算当前脉冲半径和不透明度
    final pulsePhase = (animationValue * speed) % 1.0;
    final pulseRadius = baseRadius + (maxPulseRadius - baseRadius) * pulsePhase;
    final pulseOpacity = (1.0 - pulsePhase) * 0.7 * intensity;
    
    // 创建脉冲画笔
    final pulsePaint = Paint()
      ..color = color.withOpacity(pulseOpacity)
      ..style = PaintingStyle.fill;
    
    // 绘制脉冲圆
    canvas.drawCircle(point.position, pulseRadius, pulsePaint);
  }
  
  /// 绘制多重脉冲效果
  void _drawMultiPulse(Canvas canvas, FeaturePoint point, double animationValue) {
    // 基础参数
    final baseRadius = point.animatedRadius;
    final maxPulseRadius = baseRadius * 4.0;
    
    // 绘制多个脉冲波
    for (int i = 0; i < 3; i++) {
      // 计算每个脉冲波的相位
      final phaseOffset = i * 0.33;
      final pulsePhase = ((animationValue * speed) + phaseOffset) % 1.0;
      
      // 计算当前脉冲半径和不透明度
      final pulseRadius = baseRadius + (maxPulseRadius - baseRadius) * pulsePhase;
      final pulseOpacity = (1.0 - pulsePhase) * 0.5 * intensity;
      
      // 创建脉冲画笔
      final pulsePaint = Paint()
        ..color = color.withOpacity(pulseOpacity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5;
      
      // 绘制脉冲圆
      canvas.drawCircle(point.position, pulseRadius, pulsePaint);
    }
  }
  
  /// 绘制呼吸脉冲效果
  void _drawBreathingPulse(Canvas canvas, FeaturePoint point, double animationValue) {
    // 基础参数
    final baseRadius = point.animatedRadius;
    
    // 使用正弦函数创建呼吸效果
    final breathPhase = math.sin(animationValue * speed * math.pi * 2);
    final breathFactor = 0.5 + 0.5 * breathPhase; // 0.0-1.0
    
    // 计算呼吸半径和不透明度
    final breathRadius = baseRadius * (1.0 + breathFactor * intensity);
    final breathOpacity = 0.3 + 0.7 * breathFactor;
    
    // 创建呼吸效果画笔
    final breathPaint = Paint()
      ..color = color.withOpacity(breathOpacity)
      ..style = PaintingStyle.fill;
    
    // 绘制呼吸效果
    canvas.drawCircle(point.position, breathRadius, breathPaint);
  }
  
  /// 绘制科技脉冲效果
  void _drawTechPulse(Canvas canvas, FeaturePoint point, double animationValue) {
    // 基础参数
    final baseRadius = point.animatedRadius;
    final maxPulseRadius = baseRadius * 3.5;
    
    // 计算主脉冲
    final mainPulsePhase = (animationValue * speed) % 1.0;
    final mainPulseRadius = baseRadius + (maxPulseRadius - baseRadius) * mainPulsePhase;
    final mainPulseOpacity = (1.0 - mainPulsePhase) * 0.6 * intensity;
    
    // 创建主脉冲画笔
    final mainPulsePaint = Paint()
      ..color = color.withOpacity(mainPulseOpacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // 绘制主脉冲圆
    canvas.drawCircle(point.position, mainPulseRadius, mainPulsePaint);
    
    // 添加科技风格的十字线
    if (point.state == FeaturePointState.highlighted || 
        point.state == FeaturePointState.selected) {
      // 计算十字线长度
      final crossLength = maxPulseRadius * 0.7;
      
      // 创建十字线画笔
      final crossPaint = Paint()
        ..color = color.withOpacity(0.7)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 0.5;
      
      // 绘制水平线
      canvas.drawLine(
        Offset(point.position.dx - crossLength, point.position.dy),
        Offset(point.position.dx + crossLength, point.position.dy),
        crossPaint,
      );
      
      // 绘制垂直线
      canvas.drawLine(
        Offset(point.position.dx, point.position.dy - crossLength),
        Offset(point.position.dx, point.position.dy + crossLength),
        crossPaint,
      );
      
      // 添加刻度线
      _drawTechScaleLines(canvas, point, crossLength, crossPaint);
    }
  }
  
  /// 绘制科技风格的刻度线
  void _drawTechScaleLines(
    Canvas canvas, 
    FeaturePoint point, 
    double crossLength, 
    Paint paint
  ) {
    // 刻度线长度
    final scaleLength = crossLength * 0.15;
    
    // 刻度线间距
    final scaleSpacing = crossLength / 4;
    
    // 绘制水平刻度线
    for (int i = 1; i <= 4; i++) {
      // 左侧刻度线
      final leftX = point.position.dx - i * scaleSpacing;
      canvas.drawLine(
        Offset(leftX, point.position.dy - scaleLength / 2),
        Offset(leftX, point.position.dy + scaleLength / 2),
        paint,
      );
      
      // 右侧刻度线
      final rightX = point.position.dx + i * scaleSpacing;
      canvas.drawLine(
        Offset(rightX, point.position.dy - scaleLength / 2),
        Offset(rightX, point.position.dy + scaleLength / 2),
        paint,
      );
    }
    
    // 绘制垂直刻度线
    for (int i = 1; i <= 4; i++) {
      // 上方刻度线
      final topY = point.position.dy - i * scaleSpacing;
      canvas.drawLine(
        Offset(point.position.dx - scaleLength / 2, topY),
        Offset(point.position.dx + scaleLength / 2, topY),
        paint,
      );
      
      // 下方刻度线
      final bottomY = point.position.dy + i * scaleSpacing;
      canvas.drawLine(
        Offset(point.position.dx - scaleLength / 2, bottomY),
        Offset(point.position.dx + scaleLength / 2, bottomY),
        paint,
      );
    }
  }
}
