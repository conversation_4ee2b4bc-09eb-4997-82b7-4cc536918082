import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../../models/feature_point_model.dart';

/// 扫描动画效果
/// 
/// 提供特征点扫描动画效果的实现
/// 此文件是模块化鼻部特征点可视化系统的动画效果层

/// 扫描效果类型枚举
enum ScanEffectType {
  /// 圆形扫描 - 从中心向外扩散的圆形扫描效果
  circular,
  
  /// 雷达扫描 - 旋转的雷达扫描效果
  radar,
  
  /// 网格扫描 - 网格状的扫描效果
  grid,
  
  /// 波浪扫描 - 波浪状的扫描效果
  wave,
}

/// 扫描动画效果类
class ScanEffect {
  /// 扫描效果类型
  final ScanEffectType type;
  
  /// 扫描速度（1.0为正常速度）
  final double speed;
  
  /// 扫描颜色
  final Color color;
  
  /// 扫描半径
  final double radius;
  
  /// 构造函数
  ScanEffect({
    this.type = ScanEffectType.circular,
    this.speed = 1.0,
    required this.color,
    required this.radius,
  });
  
  /// 绘制扫描效果
  void draw(Canvas canvas, Offset center, double animationValue) {
    switch (type) {
      case ScanEffectType.circular:
        _drawCircularScan(canvas, center, animationValue);
        break;
      case ScanEffectType.radar:
        _drawRadarScan(canvas, center, animationValue);
        break;
      case ScanEffectType.grid:
        _drawGridScan(canvas, center, animationValue);
        break;
      case ScanEffectType.wave:
        _drawWaveScan(canvas, center, animationValue);
        break;
    }
  }
  
  /// 绘制圆形扫描效果
  void _drawCircularScan(Canvas canvas, Offset center, double animationValue) {
    // 计算扫描环的半径和不透明度
    final scanPhase = (animationValue * speed) % 1.0;
    final scanRadius = radius * scanPhase;
    final scanOpacity = (1.0 - scanPhase) * 0.7;
    
    // 创建扫描环画笔
    final scanPaint = Paint()
      ..color = color.withOpacity(scanOpacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    
    // 绘制扫描环
    canvas.drawCircle(center, scanRadius, scanPaint);
    
    // 绘制内部填充
    final fillPaint = Paint()
      ..color = color.withOpacity(scanOpacity * 0.2)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, scanRadius, fillPaint);
  }
  
  /// 绘制雷达扫描效果
  void _drawRadarScan(Canvas canvas, Offset center, double animationValue) {
    // 保存画布状态
    canvas.save();
    
    // 移动画布原点到中心点
    canvas.translate(center.dx, center.dy);
    
    // 计算旋转角度
    final rotationAngle = animationValue * speed * 2 * math.pi;
    
    // 旋转画布
    canvas.rotate(rotationAngle);
    
    // 创建雷达扇形路径
    final radarPath = Path()
      ..moveTo(0, 0)
      ..arcTo(
        Rect.fromCircle(center: Offset.zero, radius: radius),
        -math.pi / 8,
        math.pi / 4,
        false,
      )
      ..lineTo(0, 0);
    
    // 创建雷达扇形画笔
    final radarPaint = Paint()
      ..color = color.withOpacity(0.5)
      ..style = PaintingStyle.fill;
    
    // 绘制雷达扇形
    canvas.drawPath(radarPath, radarPaint);
    
    // 绘制雷达圆环
    final ringPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // 绘制多个同心圆
    for (int i = 1; i <= 3; i++) {
      final ringRadius = radius * i / 3;
      canvas.drawCircle(Offset.zero, ringRadius, ringPaint);
    }
    
    // 绘制十字线
    final crossPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    canvas.drawLine(
      Offset(-radius, 0),
      Offset(radius, 0),
      crossPaint,
    );
    
    canvas.drawLine(
      Offset(0, -radius),
      Offset(0, radius),
      crossPaint,
    );
    
    // 恢复画布状态
    canvas.restore();
  }
  
  /// 绘制网格扫描效果
  void _drawGridScan(Canvas canvas, Offset center, double animationValue) {
    // 计算网格大小
    final gridSize = radius * 2;
    
    // 计算网格单元格大小
    final cellSize = gridSize / 8;
    
    // 计算网格左上角坐标
    final gridLeft = center.dx - radius;
    final gridTop = center.dy - radius;
    
    // 创建网格画笔
    final gridPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    // 绘制水平网格线
    for (int i = 0; i <= 8; i++) {
      final y = gridTop + i * cellSize;
      canvas.drawLine(
        Offset(gridLeft, y),
        Offset(gridLeft + gridSize, y),
        gridPaint,
      );
    }
    
    // 绘制垂直网格线
    for (int i = 0; i <= 8; i++) {
      final x = gridLeft + i * cellSize;
      canvas.drawLine(
        Offset(x, gridTop),
        Offset(x, gridTop + gridSize),
        gridPaint,
      );
    }
    
    // 绘制扫描线
    final scanPhase = (animationValue * speed) % 1.0;
    final scanY = gridTop + gridSize * scanPhase;
    
    final scanPaint = Paint()
      ..color = color.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    
    canvas.drawLine(
      Offset(gridLeft, scanY),
      Offset(gridLeft + gridSize, scanY),
      scanPaint,
    );
    
    // 绘制扫描区域
    final scanAreaPaint = Paint()
      ..color = color.withOpacity(0.1)
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(
      Rect.fromLTRB(gridLeft, gridTop, gridLeft + gridSize, scanY),
      scanAreaPaint,
    );
  }
  
  /// 绘制波浪扫描效果
  void _drawWaveScan(Canvas canvas, Offset center, double animationValue) {
    // 保存画布状态
    canvas.save();
    
    // 移动画布原点到中心点
    canvas.translate(center.dx, center.dy);
    
    // 计算波浪相位
    final wavePhase = animationValue * speed * 2 * math.pi;
    
    // 创建波浪画笔
    final wavePaint = Paint()
      ..color = color.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // 绘制多个波浪圆环
    for (int i = 0; i < 3; i++) {
      // 计算当前波浪的相位偏移
      final phaseOffset = i * math.pi / 3;
      final currentPhase = wavePhase + phaseOffset;
      
      // 计算波浪半径
      final waveRadius = radius * (0.5 + 0.1 * math.sin(currentPhase));
      
      // 创建波浪路径
      final wavePath = Path();
      
      for (double angle = 0; angle < 2 * math.pi; angle += 0.1) {
        // 计算波浪半径变化
        final waveAmplitude = 0.1 * radius * math.sin(8 * angle + currentPhase);
        final currentRadius = waveRadius + waveAmplitude;
        
        // 计算点坐标
        final x = currentRadius * math.cos(angle);
        final y = currentRadius * math.sin(angle);
        
        // 添加到路径
        if (angle == 0) {
          wavePath.moveTo(x, y);
        } else {
          wavePath.lineTo(x, y);
        }
      }
      
      // 闭合路径
      wavePath.close();
      
      // 绘制波浪
      canvas.drawPath(wavePath, wavePaint);
    }
    
    // 恢复画布状态
    canvas.restore();
  }
  
  /// 为特征点绘制扫描效果
  void drawForPoint(Canvas canvas, FeaturePoint point, double animationValue) {
    // 只为高亮或选中的点绘制扫描效果
    if (point.state == FeaturePointState.highlighted || 
        point.state == FeaturePointState.selected) {
      // 计算扫描半径
      final scanRadius = point.animatedRadius * 5.0;
      
      // 创建扫描效果
      final scanEffect = ScanEffect(
        type: point.isPrimary ? ScanEffectType.radar : ScanEffectType.circular,
        speed: 0.8,
        color: point.color,
        radius: scanRadius,
      );
      
      // 绘制扫描效果
      scanEffect.draw(canvas, point.position, animationValue);
    }
  }
}
