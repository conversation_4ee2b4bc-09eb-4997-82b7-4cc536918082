import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../../models/feature_point_model.dart';

/// 发光效果
/// 
/// 提供特征点发光动画效果的实现
/// 此文件是模块化鼻部特征点可视化系统的动画效果层

/// 发光效果类型枚举
enum GlowEffectType {
  /// 简单发光 - 基础的发光效果
  simple,
  
  /// 脉冲发光 - 带有脉冲动画的发光效果
  pulse,
  
  /// 渐变发光 - 带有颜色渐变的发光效果
  gradient,
  
  /// 光晕发光 - 带有多层光晕的发光效果
  halo,
}

/// 发光动画效果类
class GlowEffect {
  /// 发光效果类型
  final GlowEffectType type;
  
  /// 发光半径
  final double radius;
  
  /// 发光颜色
  final Color color;
  
  /// 发光强度（1.0为正常强度）
  final double intensity;
  
  /// 构造函数
  GlowEffect({
    this.type = GlowEffectType.simple,
    required this.radius,
    required this.color,
    this.intensity = 1.0,
  });
  
  /// 绘制发光效果
  void draw(Canvas canvas, Offset center, double animationValue) {
    switch (type) {
      case GlowEffectType.simple:
        _drawSimpleGlow(canvas, center, animationValue);
        break;
      case GlowEffectType.pulse:
        _drawPulseGlow(canvas, center, animationValue);
        break;
      case GlowEffectType.gradient:
        _drawGradientGlow(canvas, center, animationValue);
        break;
      case GlowEffectType.halo:
        _drawHaloGlow(canvas, center, animationValue);
        break;
    }
  }
  
  /// 绘制简单发光效果
  void _drawSimpleGlow(Canvas canvas, Offset center, double animationValue) {
    // 创建发光画笔
    final glowPaint = Paint()
      ..color = color.withOpacity(0.3 * intensity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, radius * 0.5);
    
    // 绘制发光圆
    canvas.drawCircle(center, radius, glowPaint);
  }
  
  /// 绘制脉冲发光效果
  void _drawPulseGlow(Canvas canvas, Offset center, double animationValue) {
    // 计算脉冲半径
    final pulsePhase = (animationValue * 2) % 1.0;
    final pulseRadius = radius * (0.8 + 0.4 * math.sin(pulsePhase * math.pi));
    
    // 计算脉冲不透明度
    final pulseOpacity = 0.2 + 0.3 * math.sin(pulsePhase * math.pi);
    
    // 创建发光画笔
    final glowPaint = Paint()
      ..color = color.withOpacity(pulseOpacity * intensity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, pulseRadius * 0.5);
    
    // 绘制发光圆
    canvas.drawCircle(center, pulseRadius, glowPaint);
  }
  
  /// 绘制渐变发光效果
  void _drawGradientGlow(Canvas canvas, Offset center, double animationValue) {
    // 计算渐变发光半径
    final glowRect = Rect.fromCircle(center: center, radius: radius);
    
    // 创建渐变
    final gradient = RadialGradient(
      center: Alignment.center,
      radius: 1.0,
      colors: [
        color.withOpacity(0.7 * intensity),
        color.withOpacity(0.3 * intensity),
        color.withOpacity(0.0),
      ],
      stops: const [0.0, 0.5, 1.0],
    );
    
    // 创建发光画笔
    final glowPaint = Paint()
      ..shader = gradient.createShader(glowRect)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, radius * 0.2);
    
    // 绘制发光圆
    canvas.drawCircle(center, radius, glowPaint);
  }
  
  /// 绘制光晕发光效果
  void _drawHaloGlow(Canvas canvas, Offset center, double animationValue) {
    // 绘制多层光晕
    for (int i = 0; i < 3; i++) {
      // 计算当前光晕层的相位偏移
      final phaseOffset = i * 0.33;
      final currentPhase = (animationValue + phaseOffset) % 1.0;
      
      // 计算光晕半径
      final haloRadius = radius * (0.6 + 0.4 * i);
      
      // 计算光晕不透明度
      final haloOpacity = 0.1 + 0.2 * math.sin(currentPhase * math.pi * 2);
      
      // 创建光晕画笔
      final haloPaint = Paint()
        ..color = color.withOpacity(haloOpacity * intensity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = radius * 0.1
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, radius * 0.2);
      
      // 绘制光晕圆
      canvas.drawCircle(center, haloRadius, haloPaint);
    }
    
    // 绘制中心发光
    final centerPaint = Paint()
      ..color = color.withOpacity(0.5 * intensity)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, radius * 0.3);
    
    canvas.drawCircle(center, radius * 0.5, centerPaint);
  }
  
  /// 为特征点绘制发光效果
  void drawForPoint(Canvas canvas, FeaturePoint point, double animationValue) {
    // 根据点的状态决定是否绘制发光效果
    if (point.state == FeaturePointState.highlighted || 
        point.state == FeaturePointState.selected) {
      
      // 根据点的重要性选择不同的发光效果
      GlowEffectType effectType;
      double glowRadius;
      double intensity;
      
      if (point.isPrimary) {
        // 主要特征点使用光晕发光
        effectType = GlowEffectType.halo;
        glowRadius = point.animatedRadius * 3.0;
        intensity = 1.2;
      } else if (point.isSecondary) {
        // 次要特征点使用渐变发光
        effectType = GlowEffectType.gradient;
        glowRadius = point.animatedRadius * 2.5;
        intensity = 1.0;
      } else {
        // 普通特征点使用脉冲发光
        effectType = GlowEffectType.pulse;
        glowRadius = point.animatedRadius * 2.0;
        intensity = 0.8;
      }
      
      // 创建发光效果
      final glowEffect = GlowEffect(
        type: effectType,
        radius: glowRadius,
        color: point.color,
        intensity: intensity,
      );
      
      // 绘制发光效果
      glowEffect.draw(canvas, point.position, animationValue);
    } else if (point.state == FeaturePointState.active) {
      // 活动状态的点使用简单发光
      final glowEffect = GlowEffect(
        type: GlowEffectType.simple,
        radius: point.animatedRadius * 1.5,
        color: point.color,
        intensity: 0.5,
      );
      
      // 绘制发光效果
      glowEffect.draw(canvas, point.position, animationValue);
    }
  }
  
  /// 为连接线绘制发光效果
  void drawForLine(Canvas canvas, Offset start, Offset end, Color color, double width, double animationValue) {
    // 创建线条发光画笔
    final glowPaint = Paint()
      ..color = color.withOpacity(0.3 * intensity)
      ..strokeWidth = width * 2.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, width * 2.0);
    
    // 绘制发光线条
    canvas.drawLine(start, end, glowPaint);
  }
}
