import 'dart:math';
import 'dart:math' as math;
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:beautifun/beautify_feature/models/feature_point.dart';
import '../utils/temp_logger.dart';
import 'package:beautifun/utils/face_feature_bridge.dart' as bridge;
import 'package:beautifun/widgets/preview_area/face_feature_animation/face_analysis_controller.dart';
import 'package:beautifun/widgets/preview_area/face_feature_animation/face_analysis_view.dart';
import 'package:beautifun/beautify_feature/services/transformation_service.dart';

/// 特征点动画类
/// 实现设计文档中定义的特征点动画效果
class FeaturePointsAnimation extends StatefulWidget {
  final List<List<double>>? landmarks;
  final List<FeaturePoint>? featurePoints;
  final Size displaySize;
  final double? imageWidth;
  final double? imageHeight;
  final String? imagePath;
  final bool showAnimation;
  final bool showLabels;
  final bool showConnectingLines;
  final bool showDebugInfo;
  final Function(FeaturePointsAnimation)? onAnimationCreated;
  
  // 静态引用当前状态
  static _FeaturePointsAnimationState? _currentState;
  
  const FeaturePointsAnimation({
    Key? key,
    this.landmarks,
    this.featurePoints,
    required this.displaySize,
    this.imageWidth,
    this.imageHeight,
    this.imagePath,
    this.showAnimation = true,
    this.showLabels = true,
    this.showConnectingLines = true,
    this.showDebugInfo = false,
    this.onAnimationCreated,
  }) : super(key: key);
  
  @override
  _FeaturePointsAnimationState createState() {
    final state = _FeaturePointsAnimationState();
    _currentState = state;
    return state;
  }
  
  /// 更新特征点高亮状态（静态方法）
  static void updateFeaturePoints() {
    TempLogger.logInfo('FeaturePointsAnimation', 'updateFeaturePoints', '🔄 [更新] 开始更新特征点高亮状态');
    
    if (_currentState != null) {
      _currentState!.updateFeaturePoints();
      TempLogger.logInfo('FeaturePointsAnimation', 'updateFeaturePoints', '✅ [特征点高亮] 已调用 State 的 updateFeaturePoints 方法');
    } else {
      TempLogger.logError('FeaturePointsAnimation', 'updateFeaturePoints', '❌ [特征点高亮] State 尚未初始化');
    }
    
    TempLogger.logInfo('FeaturePointsAnimation', 'updateFeaturePoints', '🔚 [出口] 完成特征点高亮状态更新');
  }
  
  /// 更新特征点高亮状态（实例方法）
  void updateFeaturePoints() {
    TempLogger.logInfo('FeaturePointsAnimation', 'updateFeaturePoints', '🔄 [实例方法] 调用静态方法更新特征点高亮状态');
    FeaturePointsAnimation.updateFeaturePoints();
  }
}
