import 'package:flutter/material.dart';

/// 扫描动画组件
/// 
/// 实现科技感扫描线动画效果
class ScanningAnimation extends StatelessWidget {
  /// 动画控制器
  final AnimationController controller;
  
  /// 扫描线颜色
  final Color scanLineColor;
  
  /// 网格颜色
  final Color gridColor;
  
  const ScanningAnimation({
    Key? key,
    required this.controller,
    this.scanLineColor = const Color(0xFF7B61FF),
    this.gridColor = const Color(0x337B61FF),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return CustomPaint(
          painter: _ScanningPainter(
            progress: controller.value,
            scanLineColor: scanLineColor,
            gridColor: gridColor,
          ),
          child: Container(),
        );
      },
    );
  }
}

/// 扫描动画绘制器
class _ScanningPainter extends CustomPainter {
  /// 动画进度 (0.0 - 1.0)
  final double progress;
  
  /// 扫描线颜色
  final Color scanLineColor;
  
  /// 网格颜色
  final Color gridColor;
  
  /// 网格线间距
  final double gridSpacing;
  
  /// 扫描线宽度
  final double scanLineWidth;
  
  _ScanningPainter({
    required this.progress,
    required this.scanLineColor,
    required this.gridColor,
    this.gridSpacing = 20.0,
    this.scanLineWidth = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制网格
    _drawGrid(canvas, size);
    
    // 绘制扫描线
    _drawScanLine(canvas, size);
    
    // 绘制边框
    _drawBorder(canvas, size);
  }
  
  /// 绘制网格
  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = gridColor
      ..strokeWidth = 0.5;
    
    // 绘制水平线
    for (double y = 0; y <= size.height; y += gridSpacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
    
    // 绘制垂直线
    for (double x = 0; x <= size.width; x += gridSpacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }
  
  /// 绘制扫描线
  void _drawScanLine(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = scanLineColor
      ..strokeWidth = scanLineWidth;
    
    // 计算扫描线位置
    final y = size.height * progress;
    
    // 绘制扫描线
    canvas.drawLine(
      Offset(0, y),
      Offset(size.width, y),
      paint,
    );
    
    // 绘制扫描线光晕效果
    final glowPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          scanLineColor.withOpacity(0.0),
          scanLineColor.withOpacity(0.5),
          scanLineColor.withOpacity(0.0),
        ],
      ).createShader(
        Rect.fromCenter(
          center: Offset(size.width / 2, y),
          width: size.width,
          height: 20,
        ),
      );
    
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(size.width / 2, y),
        width: size.width,
        height: 20,
      ),
      glowPaint,
    );
  }
  
  /// 绘制边框
  void _drawBorder(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = scanLineColor.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(_ScanningPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
