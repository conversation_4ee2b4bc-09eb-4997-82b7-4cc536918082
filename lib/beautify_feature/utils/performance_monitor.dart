import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'temp_logger.dart';

/// 性能监控工具类
/// 
/// 用于监控"炫"功能的性能指标
class PerformanceMonitor {
  /// 单例实例
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  
  /// 工厂构造函数
  factory PerformanceMonitor() => _instance;
  
  /// 内部构造函数
  PerformanceMonitor._internal();
  
  /// 日志工具
  final TempLogger _logger = TempLogger();
  
  /// 性能指标记录
  final Map<String, Queue<Duration>> _metrics = {};
  
  /// 性能指标阈值（毫秒）
  final Map<String, int> _thresholds = {
    'animation': 16, // 60fps
    'faceDetection': 200,
    'imageProcessing': 100,
    'uiRendering': 16,
    'parameterAdjustment': 50,
  };
  
  /// 当前操作的开始时间
  final Map<String, DateTime> _operationStartTimes = {};
  
  /// 开始记录操作
  void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
    _logger.info(
      module: 'PerformanceMonitor',
      function: 'startOperation',
      operation: OperationType.info,
      message: '开始操作: $operationName',
    );
  }
  
  /// 结束记录操作并返回耗时
  Duration endOperation(String operationName) {
    final startTime = _operationStartTimes[operationName];
    if (startTime == null) {
      _logger.warning(
        module: 'PerformanceMonitor',
        function: 'endOperation',
        operation: OperationType.warning,
        message: '未找到操作的开始时间: $operationName',
      );
      return Duration.zero;
    }
    
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    
    // 记录性能指标
    if (!_metrics.containsKey(operationName)) {
      _metrics[operationName] = Queue<Duration>();
    }
    
    _metrics[operationName]!.add(duration);
    
    // 保持队列长度不超过100
    while (_metrics[operationName]!.length > 100) {
      _metrics[operationName]!.removeFirst();
    }
    
    // 检查是否超过阈值
    final threshold = _thresholds[operationName] ?? 100;
    if (duration.inMilliseconds > threshold) {
      _logger.warning(
        module: 'PerformanceMonitor',
        function: 'endOperation',
        operation: OperationType.warning,
        message: '操作耗时超过阈值: $operationName, 耗时: ${duration.inMilliseconds}ms, 阈值: ${threshold}ms',
      );
    }
    
    _logger.info(
      module: 'PerformanceMonitor',
      function: 'endOperation',
      operation: OperationType.info,
      message: '结束操作: $operationName, 耗时: ${duration.inMilliseconds}ms',
    );
    
    return duration;
  }
  
  /// 开始计时
  /// 
  /// 返回计时器ID，用于停止计时
  Stopwatch startTimer() {
    final stopwatch = Stopwatch()..start();
    return stopwatch;
  }
  
  /// 停止计时并记录性能指标
  /// 
  /// [stopwatch] 计时器
  /// [category] 性能指标类别
  /// [operation] 操作名称
  /// [data] 附加数据
  void stopTimer({
    required Stopwatch stopwatch,
    required String category,
    required String operation,
    Map<String, dynamic>? data,
  }) {
    stopwatch.stop();
    final duration = stopwatch.elapsed;
    
    // 记录性能指标
    if (!_metrics.containsKey(category)) {
      _metrics[category] = Queue<Duration>();
    }
    
    final queue = _metrics[category]!;
    if (queue.length >= 100) {
      queue.removeFirst();
    }
    queue.add(duration);
    
    // 检查性能指标是否超过阈值
    final threshold = _thresholds[category] ?? 100;
    if (duration.inMilliseconds > threshold) {
      _logger.warning(
        module: 'PerformanceMonitor',
        function: category,
        operation: OperationType.warning,
        message: '操作耗时超过阈值: $operation, 耗时: ${duration.inMilliseconds}ms, 阈值: ${threshold}ms',
        data: data,
      );
    } else {
      _logger.info(
        module: 'PerformanceMonitor',
        function: category,
        operation: OperationType.info,
        message: '结束操作: $operation, 耗时: ${duration.inMilliseconds}ms',
        data: data,
      );
    }
  }
  
  /// 获取性能指标统计
  /// 
  /// [category] 性能指标类别
  Map<String, dynamic> getMetricsStats(String category) {
    if (!_metrics.containsKey(category) || _metrics[category]!.isEmpty) {
      return {
        'count': 0,
        'min': 0,
        'max': 0,
        'avg': 0,
        'p90': 0,
      };
    }
    
    final queue = _metrics[category]!;
    final count = queue.length;
    final min = queue.reduce((a, b) => a < b ? a : b).inMilliseconds;
    final max = queue.reduce((a, b) => a > b ? a : b).inMilliseconds;
    final avg = queue.fold<int>(0, (sum, duration) => sum + duration.inMilliseconds) ~/ count;
    
    // 计算P90
    final sortedDurations = queue.toList()
      ..sort((a, b) => a.inMilliseconds.compareTo(b.inMilliseconds));
    final p90Index = (count * 0.9).floor();
    final p90 = sortedDurations[p90Index].inMilliseconds;
    
    return {
      'count': count,
      'min': min,
      'max': max,
      'avg': avg,
      'p90': p90,
    };
  }
  
  /// 获取所有性能指标统计
  Map<String, Map<String, dynamic>> getAllMetricsStats() {
    final result = <String, Map<String, dynamic>>{};
    for (final category in _metrics.keys) {
      result[category] = getMetricsStats(category);
    }
    return result;
  }
  
  /// 打印性能报告
  void printReport() {
    final stats = getAllMetricsStats();
    
    _logger.info(
      module: 'PerformanceMonitor',
      function: 'report',
      operation: OperationType.info,
      message: '性能报告',
      data: stats,
    );
    
    // 检查性能问题
    for (final category in stats.keys) {
      final categoryStat = stats[category]!;
      final threshold = _thresholds[category] ?? 100;
      
      if (categoryStat['p90'] > threshold) {
        _logger.warning(
          module: 'PerformanceMonitor',
          function: 'report',
          operation: OperationType.warning,
          message: '性能问题: $category 的P90值 (${categoryStat['p90']}ms) 超过阈值 ${threshold}ms',
          data: categoryStat,
        );
      }
    }
  }
}
