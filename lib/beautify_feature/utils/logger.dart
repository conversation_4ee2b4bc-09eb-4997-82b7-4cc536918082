import 'package:flutter/foundation.dart';

/// 日志级别枚举
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// 操作类型枚举
enum OperationType {
  init,
  process,
  ui,
  animation,
  parameter,
  result,
  check,
  transform,
  preview,
  advice,
  dispose,
  set,
  test,
  start,
  end,
  error,
  info,
  update,
  initialize,
}

/// 日志工具类
/// 
/// 用于记录"炫"功能的调试日志
class BeautifyLogger {
  /// 单例实例
  static final BeautifyLogger _instance = BeautifyLogger._internal();
  
  /// 工厂构造函数
  factory BeautifyLogger() => _instance;
  
  /// 内部构造函数
  BeautifyLogger._internal();
  
  /// 记录日志
  /// 
  /// [level] 日志级别
  /// [module] 模块名称
  /// [function] 功能名称
  /// [operation] 操作类型
  /// [message] 日志消息
  /// [data] 附加数据
  void log({
    required LogLevel level,
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) {
    // 构建日志消息
    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.toString().split('.').last.toUpperCase();
    final operationStr = operation.toString().split('.').last.toUpperCase();
    
    String logMessage = '[$timestamp] [$levelStr] [$module] [$function] [$operationStr] | $message';
    
    // 添加附加数据
    if (data != null && data.isNotEmpty) {
      logMessage += '\nData: $data';
    }
    
    // 输出日志
    switch (level) {
      case LogLevel.debug:
        debugPrint(logMessage);
        break;
      case LogLevel.info:
        debugPrint('\x1B[32m$logMessage\x1B[0m'); // 绿色
        break;
      case LogLevel.warning:
        debugPrint('\x1B[33m$logMessage\x1B[0m'); // 黄色
        break;
      case LogLevel.error:
        debugPrint('\x1B[31m$logMessage\x1B[0m'); // 红色
        break;
    }
  }
  
  /// 记录调试日志
  void debug({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) {
    log(
      level: LogLevel.debug,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: data,
    );
  }
  
  /// 记录信息日志
  void info({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) {
    log(
      level: LogLevel.info,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: data,
    );
  }
  
  /// 记录警告日志
  void warning({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) {
    log(
      level: LogLevel.warning,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: data,
    );
  }
  
  /// 记录错误日志
  void error({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) {
    log(
      level: LogLevel.error,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: data,
    );
  }
  
  /// 记录性能日志
  void performance({
    required String module,
    required String function,
    required String operation,
    required Duration duration,
    Map<String, dynamic>? data,
  }) {
    final message = '执行耗时: ${duration.inMilliseconds}ms';
    log(
      level: LogLevel.info,
      module: module,
      function: function,
      operation: OperationType.process,
      message: message,
      data: data,
    );
  }
  
  /// 记录参数调整日志
  void parameterAdjustment({
    required String module,
    required String parameter,
    required dynamic oldValue,
    required dynamic newValue,
  }) {
    final message = '参数调整: $parameter 从 $oldValue 到 $newValue';
    log(
      level: LogLevel.debug,
      module: module,
      function: 'parameterAdjustment',
      operation: OperationType.parameter,
      message: message,
      data: {
        'parameter': parameter,
        'oldValue': oldValue,
        'newValue': newValue,
      },
    );
  }
  
  /// 静态调试日志方法
  static void logDebug(String module, String function, String message, {Map<String, dynamic>? data}) {
    BeautifyLogger().debug(
      module: module,
      function: function,
      operation: OperationType.process,
      message: message,
      data: data,
    );
  }
  
  /// 静态信息日志方法
  static void logInfo(String module, String function, String message, {Map<String, dynamic>? data}) {
    BeautifyLogger().info(
      module: module,
      function: function,
      operation: OperationType.process,
      message: message,
      data: data,
    );
  }
  
  /// 静态警告日志方法
  static void logWarning(String module, String function, String message, {Map<String, dynamic>? data}) {
    BeautifyLogger().warning(
      module: module,
      function: function,
      operation: OperationType.process,
      message: message,
      data: data,
    );
  }
  
  /// 静态错误日志方法
  static void logError(String module, String function, String message, {Map<String, dynamic>? data}) {
    BeautifyLogger().error(
      module: module,
      function: function,
      operation: OperationType.process,
      message: message,
      data: data,
    );
  }
}
