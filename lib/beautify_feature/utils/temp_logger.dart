import 'package:flutter/foundation.dart';

/// 操作类型枚举，用于标识日志记录的操作类型
enum OperationType {
  /// 初始化操作
  init,
  
  /// 开始操作
  start,
  
  /// 处理操作
  process,
  
  /// 信息操作
  info,
  
  /// 结果操作
  result,
  
  /// 错误操作
  error,
  
  /// 警告操作
  warning,
  
  /// 测试操作
  test,
  
  /// 释放资源操作
  dispose,
  
  /// 更新操作
  update,
  
  /// 变形操作
  transform,
  
  /// 预览操作
  preview,
  
  /// 结束操作
  end,
  
  /// 初始化操作（完整形式）
  initialize,
  
  /// 医学建议操作
  advice
}

/// 临时日志工具类
/// 
/// 用于解决 Logger 冲突问题
class TempLogger {
  /// 记录信息日志（静态方法）
  static void logInfo(String module, String function, String message, {dynamic data}) {
    if (kDebugMode) {
      print('💡 [$module] [$function] | $message');
      if (data != null) {
        print('   数据: $data');
      }
    }
  }
  
  /// 记录调试日志（静态方法）
  static void logDebug(String module, String function, String message, {dynamic data}) {
    if (kDebugMode) {
      print('🔍 [$module] [$function] | $message');
      if (data != null) {
        print('   数据: $data');
      }
    }
  }
  
  /// 记录警告日志（静态方法）
  static void logWarning(String module, String function, String message, {dynamic data}) {
    if (kDebugMode) {
      print('⚠️ [$module] [$function] | $message');
      if (data != null) {
        print('   数据: $data');
      }
    }
  }
  
  /// 记录错误日志（静态方法）
  static void logError(String module, String function, String message, {dynamic data}) {
    if (kDebugMode) {
      print('❌ [$module] [$function] | $message');
      if (data != null) {
        print('   数据: $data');
      }
    }
  }
  
  /// 记录普通日志（静态方法）
  static void log(String module, String function, String message, {dynamic data}) {
    if (kDebugMode) {
      print('📝 [$module] [$function] | $message');
      if (data != null) {
        print('   数据: $data');
      }
    }
  }
  
  /// 实例方法 - 兼容 BeautifyLogger 接口
  void info({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    dynamic data,
  }) {
    final operationStr = operation.toString().split('.').last;
    TempLogger.logInfo(module, function, '[$operationStr] $message', data: data);
  }
  
  /// 实例方法 - 兼容 BeautifyLogger 接口
  void error({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    dynamic data,
  }) {
    final operationStr = operation.toString().split('.').last;
    TempLogger.logError(module, function, '[$operationStr] $message', data: data);
  }
  
  /// 实例方法 - 兼容 BeautifyLogger 接口
  void warning({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    dynamic data,
  }) {
    final operationStr = operation.toString().split('.').last;
    TempLogger.logWarning(module, function, '[$operationStr] $message', data: data);
  }
}
