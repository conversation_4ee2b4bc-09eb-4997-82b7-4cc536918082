import 'package:beautifun/beautify_feature/models/beautify_parameters.dart';
import 'package:beautifun/beautify_feature/models/case_model.dart';
import 'package:beautifun/beautify_feature/models/medical_advice.dart';
import 'package:beautifun/beautify_feature/utils/logger.dart';

/// 示例数据服务
class SampleDataService {
  /// 单例实例
  static final SampleDataService _instance = SampleDataService._internal();
  
  /// 工厂构造函数
  factory SampleDataService() => _instance;
  
  /// 日志工具
  final BeautifyLogger _logger = BeautifyLogger();
  
  /// 内部构造函数
  SampleDataService._internal() {
    _logger.info(
      module: 'SampleDataService',
      function: 'init',
      operation: OperationType.init,
      message: 'SampleDataService initialized',
    );
  }
  
  /// 获取示例医学建议
  List<MedicalAdvice> getSampleMedicalAdvices() {
    _logger.info(
      module: 'SampleDataService',
      function: 'getSampleMedicalAdvices',
      operation: OperationType.process,
      message: '获取示例医学建议',
    );
    
    return [
      const MedicalAdvice(
        title: '鼻梁高度调整',
        description: '鼻梁高度增加超过30%可能需要使用假体植入，建议咨询专业医生评估手术可行性。',
        riskLevel: RiskLevel.medium,
        recoveryDays: 14,
        relatedParameters: ['nose_bridgeHeight'],
      ),
      const MedicalAdvice(
        title: '下颌线调整',
        description: '下颌线调整幅度较大，可能需要通过下颌角截骨手术实现，术后需要严格遵循医嘱进行恢复。',
        riskLevel: RiskLevel.high,
        recoveryDays: 30,
        relatedParameters: ['face_jawline'],
      ),
      const MedicalAdvice(
        title: '眼睛大小调整',
        description: '眼睛大小调整可通过双眼皮手术和开眼角手术实现，术后需要注意防水和防感染。',
        riskLevel: RiskLevel.low,
        recoveryDays: 7,
        relatedParameters: ['eye_eyeSize', 'eye_doubleLid'],
      ),
      const MedicalAdvice(
        title: '唇形调整',
        description: '唇形调整可通过注射玻尿酸实现，效果可维持6-12个月，几乎无恢复期。',
        riskLevel: RiskLevel.low,
        recoveryDays: 1,
        relatedParameters: ['lip_lipShape', 'lip_lipThickness'],
      ),
      const MedicalAdvice(
        title: '皱纹改善',
        description: '皱纹改善可通过注射肉毒素实现，效果可维持3-6个月，适合额头和眼角细纹。',
        riskLevel: RiskLevel.low,
        recoveryDays: 2,
        relatedParameters: ['antiAging_wrinkle'],
      ),
    ];
  }
  
  /// 获取示例医学分析结果
  MedicalAnalysisResult getSampleMedicalAnalysisResult() {
    _logger.info(
      module: 'SampleDataService',
      function: 'getSampleMedicalAnalysisResult',
      operation: OperationType.process,
      message: '获取示例医学分析结果',
    );
    
    return MedicalAnalysisResult(
      overallRisk: RiskLevel.medium,
      advices: getSampleMedicalAdvices(),
    );
  }
  
  /// 获取示例案例列表
  List<CaseModel> getSampleCases() {
    _logger.info(
      module: 'SampleDataService',
      function: 'getSampleCases',
      operation: OperationType.process,
      message: '获取示例案例列表',
    );
    
    return [
      CaseModel(
        id: 'case001',
        title: '面部轮廓优化',
        description: '通过调整下颌线和颧骨，使面部轮廓更加柔和自然。',
        type: CaseType.face,
        beforeImagePath: 'assets/images/cases/face_before_001.jpg',
        afterImagePath: 'assets/images/cases/face_after_001.jpg',
        parameters: const BeautifyParameters(
          face: FaceParameters(
            faceShape: 0.7,
            jawline: 0.6,
            cheekbone: 0.4,
            chin: 0.5,
            forehead: 0.5,
          ),
        ),
        recoveryDays: 21,
        difficulty: 3,
        satisfaction: 5,
        tags: ['面部轮廓', '下颌线', '颧骨'],
      ),
      CaseModel(
        id: 'case002',
        title: '鼻部精细调整',
        description: '通过调整鼻梁高度和鼻尖形态，使鼻部更加挺拔精致。',
        type: CaseType.nose,
        beforeImagePath: 'assets/images/cases/nose_before_001.jpg',
        afterImagePath: 'assets/images/cases/nose_after_001.jpg',
        parameters: const BeautifyParameters(
          nose: NoseParameters(
            bridgeHeight: 0.8,
            bridgeWidth: 0.4,
            tipHeight: 0.6,
            tipWidth: 0.4,
            nostrilWidth: 0.5,
          ),
        ),
        recoveryDays: 14,
        difficulty: 2,
        satisfaction: 4,
        tags: ['鼻梁', '鼻尖', '精细调整'],
      ),
      CaseModel(
        id: 'case003',
        title: '眼部放大术',
        description: '通过调整眼睛大小和双眼皮，使眼睛更加明亮有神。',
        type: CaseType.eye,
        beforeImagePath: 'assets/images/cases/eye_before_001.jpg',
        afterImagePath: 'assets/images/cases/eye_after_001.jpg',
        parameters: const BeautifyParameters(
          eye: EyeParameters(
            eyeSize: 0.7,
            eyeDistance: 0.5,
            eyeCorner: 0.6,
            doubleLid: 0.7,
            eyelid: 0.5,
          ),
        ),
        recoveryDays: 7,
        difficulty: 2,
        satisfaction: 5,
        tags: ['眼睛放大', '双眼皮', '明亮眼神'],
      ),
      CaseModel(
        id: 'case004',
        title: '唇部丰满术',
        description: '通过调整唇形和唇厚度，使唇部更加丰满性感。',
        type: CaseType.lip,
        beforeImagePath: 'assets/images/cases/lip_before_001.jpg',
        afterImagePath: 'assets/images/cases/lip_after_001.jpg',
        parameters: const BeautifyParameters(
          lip: LipParameters(
            lipShape: 0.6,
            lipThickness: 0.7,
            lipWidth: 0.5,
            lipPeak: 0.6,
            lipCorner: 0.5,
          ),
        ),
        recoveryDays: 1,
        difficulty: 1,
        satisfaction: 4,
        tags: ['唇部丰满', '性感唇形', '快速恢复'],
      ),
      CaseModel(
        id: 'case005',
        title: '全面抗衰术',
        description: '通过调整皱纹、肤质和黑眼圈，使面部更加年轻有活力。',
        type: CaseType.antiAging,
        beforeImagePath: 'assets/images/cases/antiaging_before_001.jpg',
        afterImagePath: 'assets/images/cases/antiaging_after_001.jpg',
        parameters: const BeautifyParameters(
          antiAging: AntiAgingParameters(
            wrinkle: 0.2,
            skinTexture: 0.8,
            skinTone: 0.7,
            darkCircle: 0.3,
            eyeBag: 0.3,
          ),
        ),
        recoveryDays: 3,
        difficulty: 2,
        satisfaction: 5,
        tags: ['抗衰', '年轻活力', '皱纹改善'],
      ),
      CaseModel(
        id: 'case006',
        title: '综合美颜术',
        description: '通过综合调整面部、鼻部、眼部和唇部，使整体面容更加和谐美丽。',
        type: CaseType.comprehensive,
        beforeImagePath: 'assets/images/cases/comprehensive_before_001.jpg',
        afterImagePath: 'assets/images/cases/comprehensive_after_001.jpg',
        parameters: const BeautifyParameters(
          face: FaceParameters(
            faceShape: 0.6,
            jawline: 0.5,
            cheekbone: 0.4,
            chin: 0.5,
            forehead: 0.5,
          ),
          nose: NoseParameters(
            bridgeHeight: 0.7,
            bridgeWidth: 0.4,
            tipHeight: 0.6,
            tipWidth: 0.4,
            nostrilWidth: 0.5,
          ),
          eye: EyeParameters(
            eyeSize: 0.6,
            eyeDistance: 0.5,
            eyeCorner: 0.6,
            doubleLid: 0.6,
            eyelid: 0.5,
          ),
          lip: LipParameters(
            lipShape: 0.6,
            lipThickness: 0.6,
            lipWidth: 0.5,
            lipPeak: 0.6,
            lipCorner: 0.5,
          ),
        ),
        recoveryDays: 30,
        difficulty: 4,
        satisfaction: 5,
        tags: ['综合美颜', '整体和谐', '面部重塑'],
      ),
    ];
  }
  
  /// 根据类型获取示例案例列表
  List<CaseModel> getSampleCasesByType(CaseType type) {
    _logger.info(
      module: 'SampleDataService',
      function: 'getSampleCasesByType',
      operation: OperationType.process,
      message: '根据类型获取示例案例列表',
      data: {'type': type.toString()},
    );
    
    return getSampleCases().where((caseModel) => caseModel.type == type).toList();
  }
}
