import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

import '../../core/feature_points_helper.dart';
import '../../core/image_deformation_engine.dart';
import '../../utils/logger.dart';
import '../../widgets/preview_area/landmarks_overlay.dart';
import '../../core/deformation_area_renderer.dart';
import '../../core/deformation_area_type.dart'; 
import '../models/feature_point.dart';
import '../models/nose_parameter.dart'; 
import 'transformation/area_transformation_handler.dart';

/// 自定义TickerProvider实现
class _TransformationServiceTickerProvider extends TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) => Ticker(onTick);
}

/// 增强版变形效果服务
/// 
/// 在原有变形服务基础上增加变形区域渲染功能
class TransformationServiceEnhanced extends ChangeNotifier {
  static const String _logTag = 'TransformationServiceEnhanced';
  
  // 单例实例
  static final TransformationServiceEnhanced _instance = TransformationServiceEnhanced._internal();
  
  // 工厂构造函数
  factory TransformationServiceEnhanced() {
    return _instance;
  }
  
  // 获取单例实例的静态方法
  static TransformationServiceEnhanced getInstance() {
    return _instance;
  }
  
  // 变形区域渲染器
  final DeformationAreaRenderer _deformationAreaRenderer = DeformationAreaRenderer();
  DeformationAreaRenderer get deformationAreaRenderer => _deformationAreaRenderer;
  
  // 当前选中的区域
  String _selectedArea = '';
  String get selectedArea => _selectedArea;
  
  // 当前选中的参数
  String _selectedParameter = '';
  String get selectedParameter => _selectedParameter;
  
  // 特征点数据
  List<FeaturePoint>? _featurePoints;
  List<FeaturePoint>? get featurePoints => _featurePoints;
  
  // 动画控制器
  AnimationController? _animationController;
  
  // 内部构造函数
  TransformationServiceEnhanced._internal() {
    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: _TransformationServiceTickerProvider(),
      duration: const Duration(milliseconds: 300),
    );
    
    Logger.i(_logTag, 'TransformationServiceEnhanced._internal', '初始化增强版变形服务单例实例');
  }
  
  /// 设置特征点数据
  void setFeaturePoints(List<FeaturePoint> points) {
    Logger.i(_logTag, 'setFeaturePoints', '设置特征点数据，点数量: ${points.length}');
    _featurePoints = points;
    notifyListeners();
  }
  
  /// 设置选中的区域
  void setSelectedArea(String area) {
    if (_selectedArea != area) {
      Logger.i(_logTag, 'setSelectedArea', '设置选中区域: $area');
      _selectedArea = area;
      
      // 隐藏变形区域
      _deformationAreaRenderer.hideDeformationArea();
      
      notifyListeners();
    }
  }
  
  /// 设置选中的参数
  void setSelectedParameter(String parameter) {
    if (_selectedParameter != parameter) {
      Logger.i(_logTag, 'setSelectedParameter', '设置选中参数: $parameter');
      _selectedParameter = parameter;
      
      // 隐藏变形区域
      _deformationAreaRenderer.hideDeformationArea();
      
      notifyListeners();
    }
  }
  
  /// 可视化参数影响区域
  Future<void> visualizeParameterInfluence({
    required String area,
    required String parameter,
    required double paramValue,
    required List<FeaturePoint> affectedPoints,
  }) async {
    print(' [TransformationServiceEnhanced.visualizeParameterInfluence] 入口');
    print(' [TransformationServiceEnhanced.visualizeParameterInfluence] 区域=$area, 参数=$parameter, 值=$paramValue, 点数量=${affectedPoints.length}');
    
    Logger.i(_logTag, 'visualizeParameterInfluence', ' [入口] 开始可视化参数影响区域');
    Logger.d(_logTag, 'visualizeParameterInfluence', '  • 区域: $area');
    Logger.d(_logTag, 'visualizeParameterInfluence', '  • 参数: $parameter');
    Logger.d(_logTag, 'visualizeParameterInfluence', '  • 参数值: $paramValue');
    Logger.d(_logTag, 'visualizeParameterInfluence', '  • 受影响点数量: ${affectedPoints.length}');
    
    print(' [TransformationServiceEnhanced] 开始可视化参数影响区域:');
    print(' [TransformationServiceEnhanced] 区域: $area, 参数: $parameter, 值: $paramValue');
    print(' [TransformationServiceEnhanced] 受影响点数量: ${affectedPoints.length}');
    
    try {
      // 确定变形区域类型
      print(' [TransformationServiceEnhanced] 正在调用 _determineDeformationAreaType($area, $parameter)');
      final DeformationAreaType areaType = _determineDeformationAreaType(area, parameter);
      Logger.d(_logTag, 'visualizeParameterInfluence', '  • 变形区域类型: $areaType');
      print(' [TransformationServiceEnhanced] 变形区域类型: $areaType');
      
      // 计算变形区域边界
      print(' [TransformationServiceEnhanced] 正在调用 _calculateAreaBounds()');
      final Rect areaBounds = _calculateAreaBounds(affectedPoints);
      Logger.d(_logTag, 'visualizeParameterInfluence', '  • 变形区域边界: $areaBounds');
      print(' [TransformationServiceEnhanced] 变形区域边界: $areaBounds');
      
      // 确保变形区域渲染器是可见的
      Logger.i(_logTag, 'visualizeParameterInfluence', ' [执行] 显示变形区域渲染器');
      print(' [TransformationServiceEnhanced] 正在调用 _deformationAreaRenderer.showDeformationArea()');
      _deformationAreaRenderer.showDeformationArea();
      print(' [TransformationServiceEnhanced] 显示变形区域渲染器完成');
      
      // 更新变形区域渲染器
      Logger.i(_logTag, 'visualizeParameterInfluence', ' [执行] 更新变形区域配置');
      print(' [TransformationServiceEnhanced] 正在调用 _deformationAreaRenderer.updateDeformationArea()');
      _deformationAreaRenderer.updateDeformationArea(
        areaType: areaType,
        affectedPoints: affectedPoints,
        areaBounds: areaBounds,
        intensity: 1.0,
        paramValue: paramValue,
      );
      print(' [TransformationServiceEnhanced] 更新变形区域配置完成');
      
      // 检查变形区域渲染器状态
      Logger.i(_logTag, 'visualizeParameterInfluence', '变形区域渲染器状态:');
      Logger.d(_logTag, 'visualizeParameterInfluence', '  • 可见性: ${_deformationAreaRenderer.isVisible}');
      Logger.d(_logTag, 'visualizeParameterInfluence', '  • 区域类型: ${_deformationAreaRenderer.areaType}');
      Logger.d(_logTag, 'visualizeParameterInfluence', '  • 强度: ${_deformationAreaRenderer.intensity}');
      
      print(' [TransformationServiceEnhanced] 变形区域渲染器状态:');
      print(' [TransformationServiceEnhanced] 可见性: ${_deformationAreaRenderer.isVisible}');
      print(' [TransformationServiceEnhanced] 区域类型: ${_deformationAreaRenderer.areaType}');
      print(' [TransformationServiceEnhanced] 强度: ${_deformationAreaRenderer.intensity}');
      
      // 再次确保变形区域渲染器是可见的
      print(' [TransformationServiceEnhanced] 正在再次调用 _deformationAreaRenderer.showDeformationArea()');
      _deformationAreaRenderer.showDeformationArea();
      print(' [TransformationServiceEnhanced] 再次确保变形区域渲染器可见完成');
      
      Logger.i(_logTag, 'visualizeParameterInfluence', ' [退出] 参数影响区域可视化完成');
      print(' [TransformationServiceEnhanced] 参数影响区域可视化完成');
    } catch (e) {
      Logger.e(_logTag, 'visualizeParameterInfluence', ' [错误] 可视化参数影响区域失败: $e');
      print(' [TransformationServiceEnhanced] 可视化参数影响区域失败: $e');
      if (e is Error) {
        Logger.e(_logTag, 'visualizeParameterInfluence', '堆栈跟踪: ${e.stackTrace}');
        print(' [TransformationServiceEnhanced] 堆栈跟踪: ${e.stackTrace}');
      }
    }
    
    print(' [TransformationServiceEnhanced.visualizeParameterInfluence] 出口');
  }
  
  /// 确定变形区域类型
  DeformationAreaType _determineDeformationAreaType(String area, String parameter) {
    // 根据区域和参数确定变形区域类型
    
    // 鼻部区域
    if (area == 'nose') {
      if (parameter == 'bridge_height' || parameter == 'tip_adjust') {
        return DeformationAreaType.tps;  // 鼻梁高度和鼻尖调整使用薄板样条插值（红色）
      } else if (parameter == 'nostril_width') {
        return DeformationAreaType.vectorField;  // 鼻翼宽度使用向量场变形（绿色）
      } else if (parameter == 'base_height') {
        return DeformationAreaType.mesh;  // 鼻基抬高使用网格变形（蓝色）
      }
    }
    
    // 眼部区域
    else if (area == 'eyes') {
      if (parameter == 'double_fold' || parameter == 'canthal_tilt') {
        return DeformationAreaType.tps;  // 双眼皮和开眼角使用薄板样条插值（红色）
      } else if (parameter == 'eye_bag_removal') {
        return DeformationAreaType.local;  // 去眼袋使用局部变形（黄色）
      } else if (parameter == 'outer_corner_lift') {
        return DeformationAreaType.vectorField;  // 提眼尾使用向量场变形（绿色）
      }
    }
    
    // 唇部区域
    else if (area == 'lips') {
      if (parameter == 'lip_shape') {
        return DeformationAreaType.tps;  // 唇形调整使用薄板样条插值（红色）
      } else if (parameter == 'mouth_corner') {
        return DeformationAreaType.vectorField;  // 嘴角上扬使用向量场变形（绿色）
      }
    }
    
    // 面部轮廓区域
    else if (area == 'face_contour') {
      if (parameter == 'contour_tighten') {
        return DeformationAreaType.mesh;  // 轮廓收紧使用网格变形（蓝色）
      } else if (parameter == 'chin_adjust') {
        return DeformationAreaType.triangulation;  // 下巴调整使用三角剖分变形（紫色）
      } else if (parameter == 'cheekbone_adjust') {
        return DeformationAreaType.vectorField;  // 颧骨调整使用向量场变形（绿色）
      } else if (parameter == 'face_shape') {
        return DeformationAreaType.tps;  // 脸型优化使用薄板样条插值（红色）
      }
    }
    
    // 抗衰冻龄区域
    else if (area == 'anti_aging') {
      if (parameter == 'nasolabial_folds' || parameter == 'wrinkle_removal') {
        return DeformationAreaType.local;  // 法令纹和去皱纹使用局部变形（黄色）
      } else if (parameter == 'forehead_fullness') {
        return DeformationAreaType.mesh;  // 额头饱满使用网格变形（蓝色）
      } else if (parameter == 'facial_firmness') {
        return DeformationAreaType.vectorField;  // 面容紧致使用向量场变形（绿色）
      }
    }
    
    // 默认使用局部变形
    return DeformationAreaType.local;  // 默认使用局部变形（黄色）
  }
  
  /// 计算变形区域边界
  Rect _calculateAreaBounds(List<FeaturePoint> points) {
    if (points.isEmpty) {
      return Rect.zero;
    }
    
    // 找出所有点的最小和最大坐标
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = double.negativeInfinity;
    double maxY = double.negativeInfinity;
    
    for (var point in points) {
      if (point.x < minX) minX = point.x;
      if (point.y < minY) minY = point.y;
      if (point.x > maxX) maxX = point.x;
      if (point.y > maxY) maxY = point.y;
    }
    
    // 扩大边界，确保变形区域足够大
    const padding = 50.0;
    return Rect.fromLTRB(
      minX - padding,
      minY - padding,
      maxX + padding,
      maxY + padding,
    );
  }
  
  /// 隐藏变形区域
  void hideDeformationArea() {
    Logger.i(_logTag, 'hideDeformationArea', '隐藏变形区域');
    _deformationAreaRenderer.hideDeformationArea();
  }
  
  /// 重写获取变形区域渲染器方法
  @override
  DeformationAreaRenderer? _getDeformationAreaRenderer() {
    print(' [TransformationServiceEnhanced] 返回变形区域渲染器');
    return _deformationAreaRenderer;
  }
  
  /// 重写可视化指定区域参数的影响范围方法
  Future<void> visualizeParameterInfluenceForArea(String area, String parameter) async {
    print(' [TransformationServiceEnhanced.visualizeParameterInfluenceForArea] 入口 - 区域=$area, 参数=$parameter');
    Logger.i(_logTag, '开始可视化参数影响区域');
    
    // 获取特征点辅助工具
    final helper = FeaturePointsHelper();
    
    // 获取参数特征点
    final paramPoints = helper.getParameterPoints(area, parameter);
    if (paramPoints.isEmpty) {
      print(' [TransformationServiceEnhanced] 没有找到参数特征点: 区域=$area, 参数=$parameter');
      Logger.w(_logTag, '没有找到参数特征点');
      return;
    }
    
    print(' [TransformationServiceEnhanced] 找到参数特征点: ${paramPoints.length}个');
    
    // 获取受影响的特征点
    List<FeaturePoint> affectedPoints = [];
    if (_featurePoints != null) {
      for (var index in paramPoints) {
        try {
          // 在特征点列表中查找匹配的点
          var point = _featurePoints!.firstWhere(
            (fp) => fp.index == index, 
            orElse: () => FeaturePoint(index: -1, x: 0, y: 0)
          );
          
          if (point.index != -1) {
            affectedPoints.add(point);
          }
        } catch (e) {
          print(' [TransformationServiceEnhanced] 处理特征点时出错: $e');
        }
      }
    }
    
    // 过滤掉无效的特征点
    final validAffectedPoints = affectedPoints.where((fp) => fp.index != -1).toList();
    
    if (validAffectedPoints.isEmpty) {
      print(' [TransformationServiceEnhanced] 没有有效的受影响特征点');
      Logger.w(_logTag, '没有有效的受影响特征点');
      return;
    }
    
    print(' [TransformationServiceEnhanced] 有效受影响特征点: ${validAffectedPoints.length}个');
    
    // 计算区域边界
    final areaBounds = _calculateAreaBounds(validAffectedPoints);
    print(' [TransformationServiceEnhanced] 计算的区域边界: $areaBounds');
    
    // 确定变形区域类型
    final DeformationAreaType areaType = _determineDeformationAreaType(area, parameter);
    print(' [TransformationServiceEnhanced] 确定的变形区域类型: $areaType');
    
    // 确保变形区域渲染器可见
    _deformationAreaRenderer.showDeformationArea();
    
    // 更新变形区域
    _deformationAreaRenderer.updateDeformationArea(
      areaType: areaType,
      affectedPoints: validAffectedPoints,
      areaBounds: areaBounds,
      intensity: 1.0,
      paramValue: 0.0,
    );
    
    Logger.i(_logTag, '变形区域可视化完成');
    print(' [TransformationServiceEnhanced.visualizeParameterInfluenceForArea] 出口 - 变形区域可视化完成');
  }
  
  /// 获取变形区域渲染器
  DeformationAreaRenderer get deformationAreaRenderer => _deformationAreaRenderer;
}
