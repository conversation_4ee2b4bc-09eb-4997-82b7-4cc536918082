import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/core/simple_deformation_renderer.dart';
import 'package:beautifun/core/feature_point_manager.dart';
import 'package:beautifun/core/feature_points_helper.dart';
import 'package:beautifun/core/deformation_area_type.dart';
import 'package:flutter/scheduler.dart';

import 'package:beautifun/core/transform_type.dart';
import 'package:beautifun/core/deformation_cache_manager.dart';
import 'package:beautifun/core/parameter_value_manager.dart';

import '../../core/simple_deformation_renderer.dart';
import '../../core/models/feature_point.dart';
import '../../core/feature_points_helper.dart';
import '../../core/deformation_utils.dart';
import '../../utils/logger.dart';
import '../../widgets/preview_area/landmarks_overlay.dart';
import '../../beautify_feature/models/nose_parameter.dart';

/// 自定义TickerProvider实现
class _TransformationServiceTickerProvider extends TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) => Ticker(onTick);
}

/// 变形服务类
/// 
/// 负责管理和应用各种变形效果
class TransformationService extends ChangeNotifier {
  static const String _logTag = '变形服务';
  
  /// 单例实例
  static TransformationService? _instance;
  
  /// 防止重复处理相同图像
  String? _lastProcessedImagePath;
  DateTime? _lastProcessTime;
  static const int _minProcessIntervalMillis = 1000; // 最小处理间隔（毫秒）
  
  /// 调试模式开关，控制详细日志输出
  final bool _isDebugMode = false;
  
  /// 特征点管理器
  late FeaturePointManager featurePointManager;
  
  /// 获取单例实例
  static TransformationService get instance {
    if (_instance == null) {
      _instance = TransformationService._();
      Logger.i(_logTag, '初始化单例实例');
    }
    return _instance!;
  }
  
  /// 确保实例已初始化
  Future<void> ensureInitialized() async {
    Logger.flowStart(_logTag, 'ensureInitialized');
    
    // 初始化特征点管理器
    if (featurePointManager == null) {
      Logger.flow(_logTag, 'ensureInitialized', '初始化特征点管理器');
      featurePointManager = FeaturePointManager();
      await featurePointManager.initialize();
    }
    
    // 确保变形渲染器已初始化
    await ensureDeformationRendererInitialized();
    
    Logger.flow(_logTag, 'ensureInitialized', '✅ 初始化完成');
    Logger.flowEnd(_logTag, 'ensureInitialized');
  }
  
  /// 静态初始化方法
  static Future<void> staticInitialize() async {
    if (_instance == null) {
      _instance = TransformationService._();
      Logger.logInfo(_logTag, 'staticInitialize', '等待初始化完成');
      await _instance!._initializeAsync();
    }
  }
  
  /// 【修复】变形参数值 - 扁平化结构，不再使用区域分组
  final Map<String, double> _transformationParams = {
    // 鼻部参数
    'bridge_height': 0.0,
    'bridge_width': 0.0,
    'tip_adjust': 0.0,
    'nostril_width': 0.0,
    'base_height': 0.0,
    
    // 眼部参数
    'double_fold': 0.0,      // 双眼皮
    'canthal_tilt': 0.0,     // 开眼角
    'eye_bag_removal': 0.0,  // 去眼袋
    'outer_corner_lift': 0.0, // 提眼尾
    
    // 唇部参数
    'lip_shape': 0.0,        // 唇形调整
    'mouth_corner': 0.0,     // 嘴角上扬
    
    // 面部轮廓参数
    'contour_tighten': 0.0,  // 轮廓收紧
    'v_chin': 0.0,           // V型下巴
    'cheekbone_adjust': 0.0, // 颧骨调整
    'face_shape': 0.0,       // 脸型优化
    
    // 抗衰老参数
    'nasolabial_folds': 0.0,   // 法令纹
    'wrinkle_removal': 0.0,    // 去皱纹
    'forehead_fullness': 0.0,  // 额头饱满
    'facial_firmness': 0.0,    // 面容紧致
  };
  
  /// 特征点数据
  List<FeaturePoint> _featurePoints = [];
  
  /// 变形区域渲染器
  SimpleDeformationRenderer? _deformationAreaRenderer;
  
  /// 特征点覆盖层Key
  GlobalKey<LandmarksOverlayState>? _landmarksKey;
  
  /// 特征点ID缓存
  final Map<String, List<int>> _featurePointIdsCache = {};
  
  /// 特征点索引缓存
  final Map<String, List<int>> _featurePointIndexCache = {};
  
  /// 区域点缓存
  final Map<String, List<Offset>> _areaPointsCache = {};
  
  /// 图像缓存
  final Map<String, ui.Image> _imageCache = {};
  
  /// Ticker提供者
  final _tickerProvider = _TransformationServiceTickerProvider();
  
  /// 呼吸动画控制器
  AnimationController? _breathingController;
  
  /// 呼吸动画
  Animation<double>? _breathingAnimation;
  
  /// 呼吸值
  double _breathingValue = 0.0;
  
  /// 当前选中的区域
  String? _selectedArea;
  
  /// 当前选中的参数
  String? _selectedParameter;
  
  /// 原始图像路径
  String? _originalImagePath;
  
  /// 变形后的图像路径
  String? _transformedImagePath;
  
  /// 是否正在变形
  bool _isDeforming = false;
  
  /// 当前区域类型
  String? _currentAreaType;
  
  /// 当前参数名称
  String? _currentParameterName;
  
  /// 当前参数值
  double? _currentParameterValue;
  
  /// 侧面图像路径
  String? _sideImagePath;
  
  /// 公共构造函数
  TransformationService() {
    Logger.i(_logTag, '创建新的变形服务实例');
    _initializeAsync();
  }
  
  /// 私有构造函数
  TransformationService._() {
    _initializeAsync();
  }
  
  /// 异步初始化
  Future<void> _initializeAsync() async {
    Logger.flowStart(_logTag, '异步初始化');
    
    // 初始化特征点管理器
    featurePointManager = FeaturePointManager();
    await featurePointManager.initialize();
    
    // 初始化呼吸动画控制器
    _breathingController = AnimationController(
      vsync: _tickerProvider,
      duration: const Duration(seconds: 3),
    );
    
    // 创建呼吸动画
    _breathingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _breathingController!,
        curve: Curves.easeInOut,
      ),
    );
    
    // 添加动画监听器
    _breathingAnimation!.addListener(() {
      _breathingValue = _breathingAnimation!.value;
      notifyListeners();
    });
    
    // 启动呼吸动画
    _breathingController!.repeat(reverse: true);
    
    Logger.flow(_logTag, '异步初始化', '✅ 初始化完成');
    Logger.flowEnd(_logTag, '异步初始化');
  }
  
  /// 确保渲染器已初始化
  Future<void> ensureDeformationRendererInitialized() async {
    Logger.flowStart(_logTag, '确保渲染器初始化');
    
    if (_deformationAreaRenderer == null) {
      Logger.flow(_logTag, '确保渲染器初始化', '🔍 [开始] 初始化变形区域渲染器');
      
      // 使用SimpleDeformationRenderer的单例实例
      // 这确保了在整个应用中使用同一个实例，共享缓存和累积状态
      _deformationAreaRenderer = SimpleDeformationRenderer.instance;
      
      // 如果实例未初始化，进行初始化
      if (!_deformationAreaRenderer!.isInitialized) {
        await _deformationAreaRenderer!.initialize();
      }
      
      Logger.flow(_logTag, '确保渲染器初始化', '✅ [完成] 变形区域渲染器已初始化');
    } else {
      Logger.flow(_logTag, '确保渲染器初始化', 'ℹ️ [信息] 渲染器已经初始化');
    }
    
    Logger.flowEnd(_logTag, '确保渲染器初始化');
  }
  
  /// 设置区域类型和参数名称
  Future<void> setAreaAndParameter(String area, String paramName) async {
    Logger.flowStart(_logTag, '设置区域和参数');
    Logger.flow(_logTag, '设置区域和参数', '🔍 [开始] 区域: $area, 参数: $paramName');
    
    await ensureDeformationRendererInitialized();
    
    // 记录旧的区域和参数
    final oldArea = _selectedArea;
    final oldParameter = _selectedParameter;
    Logger.flow(_logTag, '设置区域和参数', '🔄 区域变化: $oldArea -> $area, 参数变化: $oldParameter -> $paramName');
    
    // 如果参数变化，检查并记录参数值
    if (oldParameter != paramName) {
      // 获取ParameterValueManager实例
      final parameterValueManager = ParameterValueManager();
      
      // 【修复】直接使用参数名称，不包含区域前缀
      
      // 检查ParameterValueManager中是否存在该参数值
      final hasExistingValue = parameterValueManager.containsParameter(paramName);
      final existingValue = hasExistingValue ? parameterValueManager.getValue(paramName) : 0.0;
      
      // 重要：在切换参数时，保留参数值管理器中的值，确保在已有值的基础上累积变形
      if (hasExistingValue) {
        Logger.flow(_logTag, '设置区域和参数', '📊 参数值管理器中存在值: $existingValue，将使用该值作为基础');
      } else {
        Logger.flow(_logTag, '设置区域和参数', '🔄 参数项 "$paramName" 没有历史值，将使用默认值0.0');
      }
    }
    
    // 设置区域类型
    _deformationAreaRenderer!.setArea(area);
    
    // 设置参数名称
    _deformationAreaRenderer!.setParameter(paramName);
    
    // 更新选中的区域和参数
    _selectedArea = area;
    _selectedParameter = paramName;
    
    Logger.flow(_logTag, '设置区域和参数', '✅ [完成] 区域和参数已设置');
    Logger.flowEnd(_logTag, '设置区域和参数');
    notifyListeners();
  }
  
  // 防抖动定时器
  Timer? _parameterDebounceTimer;
  
  /// 设置参数值
  /// 
  /// [paramValue] 参数值
  /// [isIncreasing] 用户是否点击了加号按钮，true表示点击加号，false表示点击减号
  void setParameterValue(double paramValue, {bool? isIncreasing}) {
    Logger.flowStart(_logTag, '设置参数值');
    Logger.flow(_logTag, '设置参数值', '🔍 [开始] 值: $paramValue, 区域=${_selectedArea}, 参数=${_selectedParameter}, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"}');
    
    // 使用防抖动机制避免短时间内多次触发变形操作
    if (_parameterDebounceTimer != null) {
      _parameterDebounceTimer!.cancel();
    }
    
    // 设置防抖动定时器，确保短时间内只触发一次变形
    _parameterDebounceTimer = Timer(Duration(milliseconds: 50), () {
      if (_deformationAreaRenderer != null) {
        // 直接使用setParameterValue方法，不使用setValue
        Logger.flow(_logTag, '设置参数值', '🔄 [变形] 调用setParameterValue: 值=$paramValue, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"}');
        _deformationAreaRenderer!.setParameterValue(paramValue, isIncreasing: isIncreasing);
        
        // 如果有选中的区域和参数，更新参数值
        if (_selectedArea != null && _selectedParameter != null) {
          updateTransformationValue(_selectedArea!, _selectedParameter!, paramValue);
        }
        
        // 不需要额外调用repaint()，因为setParameterValue已经触发了重绘
        // 移除这行代码可以避免多次触发变形操作
        // _deformationAreaRenderer!.repaint();
      } else {
        Logger.flowError(_logTag, '设置参数值', '❌ 渲染器未初始化');
      }
      
      Logger.flow(_logTag, '设置参数值', '✅ [完成] 参数值已设置');
      Logger.flowEnd(_logTag, '设置参数值');
      notifyListeners();
    });
  }
  
  /// 获取特征点
  FeaturePoint? getFeaturePoint(int pointId) {
    Logger.flowStart(_logTag, '获取特征点');
    Logger.flow(_logTag, '获取特征点', '🔍 [搜索] 特征点ID: $pointId');
    
    // 检查特征点列表是否为空
    if (_featurePoints.isEmpty) {
      Logger.flowWarning(_logTag, '获取特征点', '⚠️ 特征点列表为空，无法获取特征点');
      Logger.flowEnd(_logTag, '获取特征点');
      return null;
    }
    
    // 查找特征点
    try {
      final point = _featurePoints.firstWhere(
        (p) => p.index == pointId,
        orElse: () => throw Exception('未找到特征点'),
      );
      
      Logger.flow(_logTag, '获取特征点', '✅ [找到] 特征点: ${point.index}, 坐标: (${point.x}, ${point.y})');
      Logger.flowEnd(_logTag, '获取特征点');
      return point;
    } catch (e) {
      Logger.flowError(_logTag, '获取特征点', '❌ [错误] 未找到特征点: $pointId');
      Logger.flowEnd(_logTag, '获取特征点');
      return null;
    }
  }
  
  /// 获取特征点
  FeaturePoint? getFeaturePointByIndex(int index) {
    Logger.flowStart(_logTag, '获取特征点');
    Logger.flow(_logTag, '获取特征点', '🔍 [搜索] 特征点索引: $index');
    
    // 获取特征点列表
    final points = _featurePoints;
    
    // 检查特征点列表是否为空
    if (points == null || points.isEmpty) {
      Logger.flowWarning(_logTag, '获取特征点', '⚠️ 特征点列表为空，无法获取特征点');
      Logger.flowEnd(_logTag, '获取特征点');
      return null;
    }
    
    // 检查特征点是否存在
    if (index >= 0 && index < points.length) {
      return points[index];
    }
    
    Logger.flowError(_logTag, '获取特征点', '❌ [错误] 未找到特征点: $index');
    Logger.flowEnd(_logTag, '获取特征点');
    return null;
  }
  
  /// 初始化方法
  Future<void> initialize() async {
    Logger.flowStart(_logTag, '初始化');
    
    // 确保渲染器已初始化
    await ensureDeformationRendererInitialized();
    
    Logger.flow(_logTag, '初始化', '✅ [完成] 变形服务初始化完成');
    Logger.flowEnd(_logTag, '初始化');
    notifyListeners();
    return Future.value();
  }
  
  /// 设置选中的参数
  void setSelectedParameter(String paramName) {
    Logger.flowStart(_logTag, '设置选中参数');
    Logger.flow(_logTag, '设置选中参数', '🔍 [开始] 参数: $paramName');
    
    _selectedParameter = paramName;
    
    Logger.flow(_logTag, '设置选中参数', '✅ [完成] 选中参数已设置: $paramName');
    Logger.flowEnd(_logTag, '设置选中参数');
    notifyListeners();
  }
  
  /// 更新变形参数
  /// 更新变形参数
  /// 
  /// [areaName] 区域名称
  /// [paramName] 参数名称
  /// [value] 参数值
  /// [isIncreasing] 用户是否点击了加号按钮，true表示点击加号，false表示点击减号
  void updateTransformationParam(String areaName, String paramName, double value, {bool? isIncreasing}) {
    Logger.flowStart(_logTag, '更新变形参数');
    Logger.flow(_logTag, '更新变形参数', '🔍 [开始] 区域: $areaName, 参数: $paramName, 值: $value, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"}');
    
    // 更新参数值，并传递用户点击的按钮信息
    updateTransformationValue(areaName, paramName, value, isIncreasing: isIncreasing);
  
    // 不再需要单独调用setParameterValue，因为updateTransformationValue已经处理了这一步
    // 这样可以避免重复调用导致的问题
    
    Logger.flow(_logTag, '更新变形参数', '✅ [完成] 变形参数已更新');
    Logger.flowEnd(_logTag, '更新变形参数');
  }
  
  /// 设置变形参数
  /// 
  /// [areaType] 区域类型
  /// [parameterName] 参数名称
  /// [value] 参数值
  /// [isIncreasing] 用户是否点击了加号按钮，true表示点击加号，false表示点击减号
  Future<void> setTransformParameter(String areaType, String parameterName, double value, {bool? isIncreasing}) async {
    Logger.flowStart(_logTag, 'setTransformParameter');
    Logger.i(_logTag, '!!!!! [变形参数] 设置变形参数: 区域=$areaType, 参数=$parameterName, 值=$value, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"} !!!!!');
    
    // 设置区域和参数
    await setAreaAndParameter(areaType, parameterName);
    
    // 设置参数值，并传递变形方向信息
    setParameterValue(value, isIncreasing: isIncreasing);
    
    Logger.flow(_logTag, 'setTransformParameter', '✅ [完成] 设置变形参数');
    Logger.flowEnd(_logTag, 'setTransformParameter');
  }
  
  /// 可视化参数影响
  /// 
  /// [areaType] 区域类型
  /// [parameterName] 参数名称
  /// [value] 参数值
  /// [isIncreasing] 用户是否点击了加号按钮，true表示点击加号，false表示点击减号
  Future<void> visualizeParameterInfluenceByName(String areaType, String parameterName, double value, {bool? isIncreasing}) async {
    Logger.flowStart(_logTag, 'visualizeParameterInfluenceByName');
    Logger.flow(_logTag, 'visualizeParameterInfluenceByName', '▶️ 开始: 可视化参数影响');
    Logger.flow(_logTag, 'visualizeParameterInfluenceByName', '📊 参数信息 - 区域: $areaType, 参数: $parameterName, 值: $value, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"}');
    
    // 如果没有提供isIncreasing参数，则报错并退出
    if (isIncreasing == null) {
      Logger.flowError(_logTag, 'visualizeParameterInfluenceByName', '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
      Logger.flowEnd(_logTag, 'visualizeParameterInfluenceByName');
      return;
    }
    
    await setTransformParameter(areaType, parameterName, value, isIncreasing: isIncreasing);
    
    // 设置变形区域可见性为 true
    if (_deformationAreaRenderer != null) {
      Logger.flow(_logTag, 'visualizeParameterInfluenceByName', '🔄 设置变形区域可见性为 true');
      _deformationAreaRenderer!.setVisible(true);
      
      // 确保变形类型设置正确
      if (parameterName == 'nostril_width') {
        Logger.flow(_logTag, 'visualizeParameterInfluenceByName', '🔄 设置变形类型为 local (局部变形)');
        _deformationAreaRenderer!.setTransformTypeDirectly(TransformType.local);
      }
      
      _deformationAreaRenderer!.repaint();
      Logger.flow(_logTag, 'visualizeParameterInfluenceByName', '🔄 已触发渲染器重绘');
    } else {
      Logger.flowError(_logTag, 'visualizeParameterInfluenceByName', '❌ 变形区域渲染器为空，无法设置可见性');
    }
    
    Logger.flow(_logTag, 'visualizeParameterInfluenceByName', '✅ 完成: 可视化参数影响');
    Logger.flowEnd(_logTag, 'visualizeParameterInfluenceByName');
  }
  
  /// 可视化区域参数影响
  Future<void> visualizeParameterInfluenceForArea(String areaName, String paramName) async {
    Logger.flowStart(_logTag, '可视化区域参数影响');
    Logger.flow(_logTag, '可视化区域参数影响', '🔍 [开始] 区域: $areaName, 参数: $paramName');
    
    // 调用参数影响可视化方法
    await visualizeParameterEffect(areaName, paramName, showCoordinateSystem: true);
    
    Logger.flow(_logTag, '可视化区域参数影响', '✅ [完成] 区域参数影响已可视化');
    Logger.flowEnd(_logTag, '可视化区域参数影响');
    notifyListeners();
  }
  
  /// 可视化参数对区域的影响
  Future<void> visualizeParameterEffect(String area, String paramName, {bool showCoordinateSystem = false}) async {
    Logger.flowStart(_logTag, '可视化参数影响');
    Logger.flow(_logTag, '可视化参数影响', '🔍 [开始] 区域: $area, 参数: $paramName');
    
    // 检查是否有特征点数据
    if (_featurePoints.isEmpty) {
      Logger.flowWarning(_logTag, '可视化参数影响', '⚠️ [警告] 特征点数据为空，无法可视化参数影响');
      Logger.flowEnd(_logTag, '可视化参数影响');
      return;
    }
    
    // 检查渲染器是否初始化
    if (_deformationAreaRenderer == null) {
      Logger.flow(_logTag, '可视化参数影响', '🔍 [开始] 变形区域渲染器未初始化，尝试初始化');
      await ensureDeformationRendererInitialized();
      
      // 再次检查渲染器是否初始化成功
      if (_deformationAreaRenderer == null) {
        Logger.flowError(_logTag, '可视化参数影响', '❌ [错误] 变形区域渲染器初始化失败');
        Logger.flowEnd(_logTag, '可视化参数影响');
        return;
      }
      
      Logger.flow(_logTag, '可视化参数影响', '✅ [完成] 变形区域渲染器已初始化');
    }
    
    // 检查是否有区域特征点数据
    List<Offset> areaPoints = getAreaPointsByName(area);
    if (areaPoints.isEmpty) {
      Logger.flowWarning(_logTag, '可视化参数影响', '⚠️ [警告] 区域特征点数据为空，无法可视化参数影响');
      Logger.flowEnd(_logTag, '可视化参数影响');
      return;
    }
    
    // 根据区域和参数名称设置正确的变形类型
    TransformType transformType = TransformType.vectorField; // 默认使用向量场变形
    
    // 根据区域和参数名称确定变形类型
    if (area == 'nose') {
      if (paramName == 'nostril_width') {
        transformType = TransformType.local;
        Logger.flow(_logTag, '可视化参数影响', '🔄 [设置] 鼻翼宽度参数使用局部变形');
      } else if (paramName.contains('bridge_height')) {
        transformType = TransformType.tps;
        Logger.flow(_logTag, '可视化参数影响', '🔄 [设置] 鼻梁高度参数使用TPS变形');
      } else {
        transformType = TransformType.tps;
        Logger.flow(_logTag, '可视化参数影响', '🔄 [设置] 其他鼻子参数使用TPS变形');
      }
    } else if (area == 'eyes' || area == 'eye') {
      transformType = TransformType.tps;
      Logger.flow(_logTag, '可视化参数影响', '🔄 [设置] 眼睛区域使用TPS变形');
    } else if (area == 'lips' || area == 'mouth') {
      transformType = TransformType.vectorField;
      Logger.flow(_logTag, '可视化参数影响', '🔄 [设置] 嘴唇区域使用向量场变形');
    } else if (area == 'face_contour' || area == 'face') {
      transformType = TransformType.mesh;
      Logger.flow(_logTag, '可视化参数影响', '🔄 [设置] 脸部轮廓使用网格变形');
    }
    
    // 设置变形类型
    if (_deformationAreaRenderer != null) {
      Logger.flow(_logTag, '可视化参数影响', '🔄 [设置] 变形类型: $transformType, 区域: $area, 参数: $paramName');
      
      // 先设置区域和参数名称
      _deformationAreaRenderer!.setArea(area);
      _deformationAreaRenderer!.setParameter(paramName);
      _deformationAreaRenderer!.setShowCoordinateSystem(showCoordinateSystem);
      
      Logger.flow(_logTag, '可视化参数影响', '✅ [完成] 变形类型已设置为: $transformType');
    } else {
      Logger.flowError(_logTag, '可视化参数影响', '❌ [错误] 变形区域渲染器为空');
    }
    
    // 确保特征点数据已设置到渲染器
    if (_featurePoints.isNotEmpty) {
      Logger.flow(_logTag, '可视化参数影响', '🔄 [更新] 正在更新渲染器特征点数据，共 ${_featurePoints.length} 个点');
      _deformationAreaRenderer!.updateFeaturePoints(_featurePoints);
      Logger.flow(_logTag, '可视化参数影响', '✅ [完成] 特征点数据已更新到渲染器');
    }
    
    // 获取参数值
    double paramValue = getTransformationValue(area, paramName);
    _deformationAreaRenderer!.setParameterValue(paramValue);
    Logger.flow(_logTag, '可视化参数影响', 'ℹ️ [信息] 参数值: $paramValue');
    
    // 最后设置可见性，确保所有数据都已准备好
    _deformationAreaRenderer!.setVisible(true);
    
    // 强制重绘，确保渲染器使用最新的特征点数据
    _deformationAreaRenderer!.forceRepaint();
    
    // 通知监听器，确保 UI 更新
    notifyListeners();
    
    Logger.flow(_logTag, '可视化参数影响', '✅ [完成] 参数影响已可视化');
    Logger.flowEnd(_logTag, '可视化参数影响');
  }
  
  /// 设置变形区域渲染器
  Future<void> setDeformationAreaRenderer(SimpleDeformationRenderer renderer) async {
    Logger.flowStart(_logTag, 'setDeformationAreaRenderer');
    Logger.flow(_logTag, 'setDeformationAreaRenderer', '▶️ 开始: 设置变形区域渲染器');
    
    _deformationAreaRenderer = renderer;
    
    // 确保渲染器已初始化
    if (_deformationAreaRenderer != null) {
      Logger.flow(_logTag, 'setDeformationAreaRenderer', '✅ 变形区域渲染器已设置');
      
      // 设置变形类型
      if (_currentAreaType != null && _currentParameterName != null) {
        Logger.flow(_logTag, 'setDeformationAreaRenderer', '🔄 设置变形类型 - 区域: $_currentAreaType, 参数: $_currentParameterName');
        
        // 设置变形类型
        _deformationAreaRenderer!.setTransformTypeByParameter(_currentAreaType!, _currentParameterName!);
        
        // 设置参数值
        if (_currentParameterValue != null) {
          Logger.flow(_logTag, 'setDeformationAreaRenderer', '🔢 设置参数值: $_currentParameterValue');
          _deformationAreaRenderer!.setParameterValue(_currentParameterValue!);
        } else {
          Logger.flowError(_logTag, 'setDeformationAreaRenderer', '⚠️ 当前参数值为空');
        }
        
        // 调用渲染器的重绘方法
        _deformationAreaRenderer!.repaint();
        Logger.flow(_logTag, 'setDeformationAreaRenderer', '🔄 已触发渲染器重绘');
      } else {
        Logger.flowError(_logTag, 'setDeformationAreaRenderer', '⚠️ 当前区域类型或参数名称为空，无法设置变形类型');
      }
    } else {
      Logger.flowError(_logTag, 'setDeformationAreaRenderer', '❌ 变形区域渲染器为空，无法设置');
    }
    
    Logger.flow(_logTag, 'setDeformationAreaRenderer', '✅ [完成] 设置变形区域渲染器');
    Logger.flowEnd(_logTag, 'setDeformationAreaRenderer');
  }
  
  /// 获取所有特征点
  List<FeaturePoint>? getFeaturePoints() {
    Logger.flowStart(_logTag, '获取所有特征点');
    
    // 检查内部特征点列表是否为空
    if (_featurePoints == null || _featurePoints.isEmpty) {
      // 判断是否应该检查特征点数据
      if (shouldCheckFeaturePoints()) {
        // 如果应该检查特征点数据，则输出错误信息并退出程序
        Logger.flowError(_logTag, '获取所有特征点', '❗️ 致命错误: 特征点数据为空');
        Logger.flowError(_logTag, '获取所有特征点', '当前区域: $selectedArea');
        Logger.flowError(_logTag, '获取所有特征点', '原始图像路径: $_originalImagePath');
        Logger.flowError(_logTag, '获取所有特征点', '变形图像路径: $_transformedImagePath');
        
        // 输出错误信息并退出程序
        Logger.flowError(_logTag, '获取所有特征点', '程序即将退出，特征点数据不应为空');
        Logger.flowEnd(_logTag, '获取所有特征点');
        
        // 强制退出程序
        exit(1);
      } else {
        // 如果不应该检查特征点数据（程序刚启动），则返回空列表
        Logger.flow(_logTag, '获取所有特征点', '程序刚启动，特征点数据尚未准备好');
        Logger.flowEnd(_logTag, '获取所有特征点');
        return null;
      }
    }
    
    Logger.flow(_logTag, '获取所有特征点', '特征点数量: ${_featurePoints.length}');
    Logger.flowEnd(_logTag, '获取所有特征点');
    return List<FeaturePoint>.from(_featurePoints);
  }
  
  // 【关键新增】获取源图像
  /// 返回当前的源图像，用于面部中心线计算
  ui.Image? getSourceImage() {
    Logger.flowStart(_logTag, 'getSourceImage');
    
    // 优先从图像缓存中获取
    if (_originalImagePath != null && _originalImagePath!.isNotEmpty) {
      final cachedImage = getImageFromCache(_originalImagePath!);
      if (cachedImage != null) {
        Logger.flow(_logTag, 'getSourceImage', 
            '✅ 从缓存获取源图像: 尺寸=${cachedImage.width}x${cachedImage.height}');
        Logger.flowEnd(_logTag, 'getSourceImage');
        return cachedImage;
      }
    }
    
    // 如果缓存中没有，尝试从渲染器获取
    if (_deformationAreaRenderer != null) {
      // 渲染器可能有源图像的引用
      Logger.flow(_logTag, 'getSourceImage', 
          '⚠️ 缓存中无源图像，尝试从渲染器获取');
      // 注意：这里需要渲染器提供获取源图像的方法
      // 现在先返回null，让计算面部中心线的部分使用默认的处理
    }
    
    Logger.flowWarning(_logTag, 'getSourceImage', 
        '⚠️ 无法获取源图像，返回null');
    Logger.flowEnd(_logTag, 'getSourceImage');
    return null;
  }
  
  /// 判断当前是否应该检查特征点数据
  /// 
  /// 在程序初始化时，我们不应该检查特征点数据
  /// 只有在用户选择图像或执行相关操作后，才应该检查特征点数据
  bool shouldCheckFeaturePoints() {
    // 如果用户已经选择了图像，则应该检查特征点数据
    if (_originalImagePath != null && _originalImagePath!.isNotEmpty) {
      return true;
    }
    
    // 如果用户已经选择了区域或参数，则应该检查特征点数据
    if (selectedArea != null && selectedArea!.isNotEmpty) {
      return true;
    }
    
    // 如果特征点数据非空，则应该检查
    if (_featurePoints.isNotEmpty) {
      return true;
    }
    
    // 如果以上条件都不满足，则表示程序刚刚启动，不应该检查特征点数据
    return false;
  }
  
  /// 内部获取特征点方法，仅供 FeaturePointManager 调用，避免循环调用
  List<FeaturePoint>? _getFeaturePointsInternal() {
    Logger.flowStart(_logTag, '_getFeaturePointsInternal');
    
    // 直接返回内部存储的特征点数据，不进行额外的处理
    final result = _featurePoints.isEmpty ? null : List<FeaturePoint>.from(_featurePoints);
    
    Logger.flow(_logTag, '_getFeaturePointsInternal', '返回 ${result?.length ?? 0} 个特征点');
    Logger.flowEnd(_logTag, '_getFeaturePointsInternal');
    
    return result;
  }
  
  /// 设置特征点覆盖层的引用
  void setLandmarksKey(GlobalKey<LandmarksOverlayState> key) {
    Logger.flowStart(_logTag, '设置特征点覆盖层');
    
    _landmarksKey = key;
    
    Logger.flowEnd(_logTag, '设置特征点覆盖层');
  }
  
  /// 获取区域点
  List<Offset> getAreaPointsByName(String areaName) {
    Logger.flowStart(_logTag, '获取区域点');
    Logger.flow(_logTag, '获取区域点', '🔍 [开始] 区域名称: $areaName');
    
    // 检查特征点数据是否为空
    if (_featurePoints.isEmpty) {
      Logger.flowError(_logTag, '获取区域点', '❌ [错误] 特征点数据为空');
      Logger.flowEnd(_logTag, '获取区域点');
      return [];
    }
    
    // 检查缓存
    if (_areaPointsCache.containsKey(areaName)) {
      Logger.flow(_logTag, '获取区域点', 'ℹ️ [信息] 使用缓存数据');
      Logger.flowEnd(_logTag, '获取区域点');
      return _areaPointsCache[areaName]!;
    }
    
    List<int> pointIds = [];
    List<Offset> areaPoints = [];
    final featurePointsHelper = FeaturePointsHelper();
    
    try {
      // 使用辅助工具获取区域点ID
      pointIds = featurePointsHelper.getAreaPointIds(areaName);
      
      // 根据ID获取特征点位置
      for (var id in pointIds) {
        var point = _featurePoints.firstWhere(
          (p) => p.index == id,
          orElse: () {
            Logger.flowError(_logTag, '获取区域点', '❌ [错误] 未找到ID为 $id 的特征点');
            return FeaturePoint(index: -1, x: 0, y: 0);
          },
        );
        
        // 只添加有效的特征点
        if (point.index != -1) {
          areaPoints.add(point.position);
        }
      }
      
      // 缓存结果
      _areaPointsCache[areaName] = areaPoints;
      
      Logger.flow(_logTag, '获取区域点', '✅ [完成] 获取到 ${areaPoints.length} 个区域点');
    } catch (e) {
      Logger.flowError(_logTag, '获取区域点', '❌ [错误] 获取区域点失败: $e');
    }
    
    Logger.flowEnd(_logTag, '获取区域点');
    return areaPoints;
  }
  
  /// 重置所有变形效果
  void resetAllDeformations() {
    Logger.flowStart(_logTag, '重置所有变形效果');
    
    // 重置变形区域渲染器
    if (_deformationAreaRenderer != null) {
      _deformationAreaRenderer!.setVisible(false);
    }
    
    // 重置所有参数值
    _resetAllParameterValues();
    
    Logger.flowEnd(_logTag, '重置所有变形效果');
    notifyListeners();
  }
  
  /// 重置所有参数值
  void _resetAllParameterValues() {
    Logger.flowStart(_logTag, '重置所有参数值');
    
    // 【修复】重置所有参数值为0
    _transformationParams.forEach((param, _) {
      _transformationParams[param] = 0.0;
    });
    
    Logger.flowEnd(_logTag, '重置所有参数值');
  }
  
  /// 重置所有参数值
  void _resetAllParameters() {
    Logger.flowStart(_logTag, '_resetAllParameters');
    
    // 【修复】重置所有参数值为0
    _transformationParams.forEach((param, _) {
      _transformationParams[param] = 0.0;
    });
    
    // 如果变形渲染器已初始化，重置其参数
    if (_deformationAreaRenderer != null) {
      // 设置区域和参数为空
      _deformationAreaRenderer!.setArea('none');
      _deformationAreaRenderer!.setParameterName('');
      _deformationAreaRenderer!.setParameterValue(0.0);
      Logger.flow(_logTag, '_resetAllParameters', '变形渲染器参数已重置');
    }
    
    Logger.flow(_logTag, '_resetAllParameters', '✅ 所有参数值已重置为默认值 0.0');
    Logger.flowEnd(_logTag, '_resetAllParameters');
  }
  
  /// 获取变形参数值
  double getTransformationValue(String area, String paramName) {
    Logger.flowStart(_logTag, '获取变形参数值');
    Logger.flow(_logTag, '获取变形参数值', '🔍 [开始] 参数: $paramName');
    
    // 【修复】优先从ParameterValueManager获取参数值
    final parameterValueManager = ParameterValueManager();
    if (parameterValueManager.containsParameter(paramName)) {
      final value = parameterValueManager.getValue(paramName);
      Logger.flow(_logTag, '获取变形参数值', '✅ 从参数管理器获取值: $paramName = $value');
      Logger.flowEnd(_logTag, '获取变形参数值');
      return value;
    }
    
    // 【修复】备选方案：从扁平化的本地参数映射中获取
    if (!_transformationParams.containsKey(paramName)) {
      Logger.flowError(_logTag, '获取变形参数值', '❌ [错误] 参数不存在: $paramName');
      Logger.flowEnd(_logTag, '获取变形参数值');
      return 0.0;
    }
    
    // 获取参数值
    final value = _transformationParams[paramName] ?? 0.0;
    
    Logger.flow(_logTag, '获取变形参数值', '✅ [完成] 参数值: $value');
    Logger.flowEnd(_logTag, '获取变形参数值');
    return value;
  }
  
  /// 【修复】获取所有变形参数值
  /// 
  /// 返回所有变形参数值，用于缓存机制
  /// 返回的结构为扁平化的 {参数名称: 参数值}
  Map<String, double>? getTransformationParams() {
    Logger.flowStart(_logTag, 'getTransformationParams');
    
    // 检查参数是否为空
    if (_transformationParams.isEmpty) {
      Logger.flowWarning(_logTag, 'getTransformationParams', '⚠️ 变形参数为空');
      Logger.flowEnd(_logTag, 'getTransformationParams');
      return null;
    }
    
    // 创建一个深拷贝，避免外部修改影响内部数据
    final result = Map<String, double>.from(_transformationParams);
    
    Logger.flow(_logTag, 'getTransformationParams', '✅ 获取到 ${result.length} 个参数值');
    
    // 记录详细的参数值信息，用于调试
    result.forEach((param, value) {
      if (value != 0.0) { // 只记录非零参数值，减少日志量
        Logger.flow(_logTag, 'getTransformationParams', '  - $param: $value');
      }
    });
    
    Logger.flowEnd(_logTag, 'getTransformationParams');
    return result;
  }
  
  /// 获取参数值 (getTransformationValue的别名)
  /// 
  /// 用于获取指定区域和参数名称的变形参数值
  /// 
  /// [area] 区域名称，如'nose'、'eyes'等
  /// [paramName] 参数名称，如'bridge_height'、'tip_adjust'等
  /// 返回参数值，如果参数不存在则返回null
  double? getParameterValue(String area, String paramName) {
    try {
      return getTransformationValue(area, paramName);
    } catch (e) {
      Logger.e(_logTag, '获取参数值失败: $e');
      return null;
    }
  }
  
  /// 清除缓存
  void clearCache() {
    Logger.flowStart(_logTag, '清除缓存');
    Logger.flow(_logTag, '清除缓存', '🔍 [开始] 清除所有缓存数据');
    
    // 清除特征点ID缓存
    if (_featurePointIdsCache.isNotEmpty) {
      Logger.flow(_logTag, '清除缓存', '🗑 清除特征点ID缓存: ${_featurePointIdsCache.length} 项');
      _featurePointIdsCache.clear();
    }
    
    // 清除特征点索引缓存
    if (_featurePointIndexCache.isNotEmpty) {
      Logger.flow(_logTag, '清除缓存', '🗑 清除特征点索引缓存: ${_featurePointIndexCache.length} 项');
      _featurePointIndexCache.clear();
    }
    
    // 如果有渲染器，清除渲染器的缓存
    // 但不清除累积状态，因为这里不是图像重新导入
    if (_deformationAreaRenderer != null) {
      Logger.flow(_logTag, '清除缓存', '🗑 清除渲染器缓存，保留累积状态');
      _deformationAreaRenderer!.clearCache(onlyWhenImageChanged: false);
    }
    
    // 如果有特征点管理器，清除特征点管理器的缓存
    if (featurePointManager != null) {
      Logger.flow(_logTag, '清除缓存', '🗑 清除特征点管理器缓存');
      featurePointManager.invalidateCache();
    }
    
    Logger.flow(_logTag, '清除缓存', '✅ [完成] 所有缓存已清除');
    Logger.flowEnd(_logTag, '清除缓存');
  }
  
  /// 更新变形参数值
void updateTransformationValue(String area, String paramName, double value, {bool? isIncreasing}) {
  // 将参数值四舍五入为1位小数，确保精度一致
  double roundedValue = double.parse(value.toStringAsFixed(1));
  
  // 【修复】完全忽略区域信息，只使用纯参数名
  // 添加流程日志
  print('🔄 [流程1] 用户点击${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未知"}按钮');
  print('🔄 [流程2] TransformationService.updateTransformationValue - 参数: $paramName, 值: $roundedValue (忽略区域: $area)');
  print('🔄 [流程3] 检查参数值变化方向: ${isIncreasing != null ? (isIncreasing ? "增大" : "减小") : "未指定"}');
  
  Logger.flowStart(_logTag, '更新变形参数');
  Logger.flow(_logTag, '更新变形参数', '🔍 [开始] 参数: $paramName, 值: $roundedValue, 变形方向: ${isIncreasing != null ? (isIncreasing ? "增大" : "减小") : "未指定"} (区域信息已忽略: $area)');
  
  // 【修复】使用ParameterValueManager获取旧值，直接使用参数名
  final parameterValueManager = ParameterValueManager();
  final oldValue = parameterValueManager.containsParameter(paramName) 
      ? parameterValueManager.getValue(paramName) 
      : 0.0;
  
  final changeAmount = roundedValue - oldValue;
  
  // 变形方向唯一根据用户点击的加号或减号来确定，与参数值的变化和正负完全无关
  String changeDirection;
  if (isIncreasing != null) {
    // 使用用户点击的按钮信息确定变形方向
    changeDirection = isIncreasing ? "增大" : "减小";
  } else {
    // 如果没有提供用户点击信息，则根据参数值变化推断方向（不推荐）
    Logger.flowWarning(_logTag, '更新变形参数', '⚠️ 未提供变形方向信息，根据参数值变化推断方向');
    changeDirection = changeAmount > 0 ? "增大" : (changeAmount < 0 ? "减小" : "不变");
  }
  
  // 1. 记录点击参数项名称和当前值
  Logger.i(_logTag, '⟹ 更新变形参数: 📈 1. 点击参数项 "$paramName" 当前值: $oldValue');
  
  // 2. 记录参数值变化和变形方向
  Logger.i(_logTag, '⟹ 更新变形参数: 📈 2. 参数值变动: 从 $oldValue 到 $roundedValue, 变化量: ${changeAmount.toStringAsFixed(1)}, 变形方向: $changeDirection');
  
  // 5. 记录完整生命周期
  Logger.i(_logTag, '⟹ 更新变形参数: 📈 5. 开始新一轮变形周期: 参数项=$paramName, 新值=$roundedValue');
  
  // 如果有渲染器，更新渲染器的参数值
  if (_deformationAreaRenderer != null) {
    print('🔄 [流程4] 设置变形渲染器参数');
    
    Logger.flow(_logTag, '更新变形参数', '📈 更新渲染器的参数值，使用1位小数精度: $roundedValue');
    
    // 检查是否所有参数值都变为0，如果是则优先检查zero_state缓存
    if (roundedValue == 0.0) {
      // 使用ParameterValueManager检查所有参数是否为0
      final parameterValueManager = ParameterValueManager();
      bool allParametersZero = true;
      
      // 遍历所有参数，查找非零参数
      parameterValueManager.getAllParameters().forEach((param, value) {
        // 排除当前正在设置为0的参数
        if (param != paramName && value != 0.0) {
          allParametersZero = false;
        }
      });
      
      if (allParametersZero) {
        Logger.flowError(_logTag, '更新变形参数', '🔍🔍🔍 所有参数值均为0，检查zero_state缓存');
        print('🔍🔍🔍 检测到参数重置：所有参数值均为0，将尝试使用zero_state缓存');
        
        // 检查zero_state缓存是否存在
        final cacheManager = _deformationAreaRenderer!.getCacheManager();
        if (cacheManager != null) {
          final zeroStateCache = cacheManager.find({'zero_state': 0.0});
          if (zeroStateCache != null) {
            Logger.flowError(_logTag, '更新变形参数', '✅✅✅ 找到zero_state缓存，将使用缓存而不重新创建渲染器');
            print('✅✅✅ 找到现有zero_state缓存，将使用缓存绘制而不重新创建渲染器 ✅✅✅');
          } else {
            Logger.flowError(_logTag, '更新变形参数', '⚠️⚠️⚠️ 未找到zero_state缓存，这是一个错误状态！');
            print('❌❌❌ 错误：所有参数值均为0，但未找到zero_state缓存！ ❌❌❌');
          }
        }
      }
    }
    
    // 【修复】检查当前参数是否与上一次选中的参数不同，不再使用区域判断
    bool isNewParameter = _selectedParameter != paramName;
  
    // 如果是新参数，记录日志
    if (isNewParameter) {
      Logger.flow(_logTag, '更新变形参数', '🔄 切换参数: ${_selectedParameter} -> $paramName');
      
      // 如果是新参数，检查是否有历史值
      double initialValue = parameterValueManager.containsParameter(paramName) 
          ? parameterValueManager.getValue(paramName) 
          : 0.0;
      
      // 如果有历史值，使用历史值，否则使用新值
      if (initialValue != 0.0) {
        Logger.flow(_logTag, '更新变形参数', '📚 参数 $paramName 有历史值: $initialValue，保持不变');
      } else {
        // 如果没有历史值，使用新值
        Logger.flow(_logTag, '更新变形参数', '🆕 参数 $paramName 没有历史值，使用新值: $roundedValue');
        // 重要：在这里不更新参数值管理器，而是在调用setParameterValue后更新
        Logger.flow(_logTag, '更新变形参数', '🔔 延迟更新参数值管理器，确保变形正确执行');
      }
    } else {
      // 如果是同一参数，在调用setParameterValue后更新参数值
      Logger.flow(_logTag, '更新变形参数', '🔔 延迟更新参数值管理器，确保变形正确执行');
    }
  
    // 【修复】只设置参数名称，不再设置区域
    _deformationAreaRenderer!.setParameter(paramName);
    
    // 【修复】更新扁平化的本地参数映射
    _transformationParams[paramName] = roundedValue;
    
    // 获取参数的真实当前值，确保日志记录准确
    final realCurrentValue = parameterValueManager.containsParameter(paramName) 
        ? parameterValueManager.getValue(paramName) 
        : 0.0;
  
    Logger.flow(_logTag, '更新变形参数', '📊 参数 $paramName 的真实当前值: $realCurrentValue，将传递给渲染器');
  
    // 传递真实当前值和用户点击信息给渲染器
    _deformationAreaRenderer!.setParameterValue(roundedValue, isIncreasing: isIncreasing);
    
    // 重要：在调用setParameterValue方法后更新参数值管理器
    // 这样确保在变形计算完成后才更新参数值
    parameterValueManager.setValue(paramName, roundedValue);
    Logger.flow(_logTag, '更新变形参数', '💾 变形完成后更新参数值管理器: $paramName = $roundedValue');
    Logger.flow(_logTag, '更新变形参数', '✅ 渲染器参数已更新');
  } else {
    Logger.flowWarning(_logTag, '更新变形参数', '⚠️ 渲染器未初始化，无法更新渲染器的参数值');
    
    // 即使没有渲染器，也更新参数值管理器
    // 使用ParameterValueManager更新参数值
  
    // 【修复】更新扁平化的本地参数映射
    _transformationParams[paramName] = roundedValue;
  }
  
  Logger.flow(_logTag, '更新变形参数', '✅ [完成] 参数已更新: $paramName = $roundedValue');
  Logger.flowEnd(_logTag, '更新变形参数');
  notifyListeners();
}
  
  /// 获取特征点ID
  List<int> getFeaturePointIds(String areaName, String paramName) {
    Logger.flowStart(_logTag, '获取特征点ID');
    Logger.flow(_logTag, '获取特征点ID', '🔍 [开始] 区域: $areaName, 参数: $paramName');
    
    // 检查缓存
    String cacheKey = '${areaName}_${paramName}';
    if (_featurePointIdsCache.containsKey(cacheKey)) {
      Logger.flow(_logTag, '获取特征点ID', 'ℹ️ [信息] 使用缓存数据');
      Logger.flowEnd(_logTag, '获取特征点ID');
      return _featurePointIdsCache[cacheKey]!;
    }
    
    List<int> result = [];
    
    // 创建特征点辅助工具实例
    final featurePointsHelper = FeaturePointsHelper();
    
    // 确保区域名称格式正确
    String area = areaName;
    if (area == 'face') {
      area = 'face_contour';
    }
    
    // 将 paramName 转换为 FeaturePointsHelper 中使用的参数名称格式
    String parameterName = paramName;
    
    // 鼻子参数名称映射
    if (area == 'nose') {
      if (paramName == 'bridge_height') {
        parameterName = 'nasal_midline';  // 鼻梁高度对应鼻梁复合体
        
        // 测试环境中直接返回测试特征点索引
        if (featurePointManager != null && featurePointManager!.getFeaturePoints().isNotEmpty) {
          // 检查是否存在鼻梁顶部和鼻尖特征点
          final points = featurePointManager!.getFeaturePoints();
          final hasNasalBridge = points.any((p) => p.index == 168);
          final hasNoseTip = points.any((p) => p.index == 6);
          
          if (hasNasalBridge && hasNoseTip) {
            result = [168, 6]; // 鼻梁顶部和鼻尖
            _featurePointIdsCache[cacheKey] = result;
            Logger.flow(_logTag, '获取特征点ID', '✅ [完成] 使用测试特征点: ${result.length} 个');
            Logger.flowEnd(_logTag, '获取特征点ID');
            return result;
          }
        }
      } else if (paramName == 'bridge_width') {
        parameterName = 'nasal_midline';  // 鼻梁宽度也对应鼻梁复合体
      } else if (paramName == 'tip_adjust' || paramName == 'tip_height') {
        parameterName = 'tip_complex';  // 鼻尖调整对应鼻尖复合体
        
        // 测试环境中直接返回测试特征点索引
        if (featurePointManager != null && featurePointManager!.getFeaturePoints().isNotEmpty) {
          // 检查是否存在鼻尖特征点
          final points = featurePointManager!.getFeaturePoints();
          final hasNoseTip = points.any((p) => p.index == 6);
          
          if (hasNoseTip) {
            result = [6]; // 鼻尖
            _featurePointIdsCache[cacheKey] = result;
            Logger.flow(_logTag, '获取特征点ID', '✅ [完成] 使用测试特征点: ${result.length} 个');
            Logger.flowEnd(_logTag, '获取特征点ID');
            return result;
          }
        }
      } else if (paramName == 'nostril_width') {
        parameterName = 'alar_complex';  // 鼻孔宽度对应鼻翼复合体
        
        // 使用实际存在的特征点
        if (featurePointManager != null && featurePointManager!.getFeaturePoints().isNotEmpty) {
          final points = featurePointManager!.getFeaturePoints();
          
          Logger.flow(_logTag, '获取特征点ID', '🔍 [分析] 特征点总数: ${points.length}');
          
          // 尝试查找鼻翼相关的特征点
          List<int> nostrilIndexes = [];
          
          // 首先检查是否有名称包含鼻翼相关关键词的特征点
          for (int i = 0; i < points.length; i++) {
            final point = points[i];
            if (point.name != null && 
                (point.name!.toLowerCase().contains('nostril') || 
                 point.name!.toLowerCase().contains('alar') || 
                 point.name!.toLowerCase().contains('nose'))) {
              nostrilIndexes.add(i);
              Logger.flow(_logTag, '获取特征点ID', '✅ [发现] 找到鼻翼相关特征点: 索引=$i, 名称=${point.name}');
            }
          }
          
          // 如果没有找到鼻翼相关特征点，尝试使用左右对称的点
          if (nostrilIndexes.isEmpty) {
            // 计算所有点的中心点
            double sumX = 0;
            if (points == null || points.isEmpty) {
              Logger.flowError(_logTag, '计算对称点', '特征点列表为空，无法计算对称点');
              Logger.flowEnd(_logTag, '计算对称点');
              return [];
            }
            
            for (final point in points) {
              sumX += point.x;
            }
            double centerX = sumX / points.length;
            
            // 找出距离中心最远的左右两个点
            double maxLeftDistance = 0;
            double maxRightDistance = 0;
            int leftIndex = -1;
            int rightIndex = -1;
            
            for (int i = 0; i < points.length; i++) {
              final point = points[i];
              double distanceFromCenter = point.x - centerX;
              
              if (distanceFromCenter < 0 && -distanceFromCenter > maxLeftDistance) {
                maxLeftDistance = -distanceFromCenter;
                leftIndex = i;
              } else if (distanceFromCenter > 0 && distanceFromCenter > maxRightDistance) {
                maxRightDistance = distanceFromCenter;
                rightIndex = i;
              }
            }
            
            if (leftIndex >= 0) {
              nostrilIndexes.add(leftIndex);
              Logger.flow(_logTag, '获取特征点ID', '✅ [替代] 使用最左侧点作为左鼻翼点: 索引=$leftIndex');
            }
            
            if (rightIndex >= 0) {
              nostrilIndexes.add(rightIndex);
              Logger.flow(_logTag, '获取特征点ID', '✅ [替代] 使用最右侧点作为右鼻翼点: 索引=$rightIndex');
            }
          }
          
          if (nostrilIndexes.isNotEmpty) {
            result = nostrilIndexes;
            _featurePointIdsCache[cacheKey] = result;
            Logger.flow(_logTag, '获取特征点ID', '✅ [完成] 使用实际特征点: ${result.length} 个');
            Logger.flowEnd(_logTag, '获取特征点ID');
            return result;
          } else {
            Logger.flowWarning(_logTag, '获取特征点ID', '⚠️ 无法找到任何可用的鼻翼特征点');
          }
        }
      } else if (paramName == 'base_height') {
        parameterName = 'base_complex';  // 鼻基高度对应鼻基底复合体
      }
    }
    // 眼睛参数名称映射
    else if (area == 'eyes') {
      if (paramName == 'eye_size' || paramName == 'size') {
        parameterName = 'eye_size';
      } else if (paramName == 'eye_distance' || paramName == 'distance') {
        parameterName = 'eye_distance';
      } else if (paramName == 'double_fold') {
        parameterName = 'double_fold';
      } else if (paramName == 'canthal_tilt') {
        parameterName = 'canthal_tilt';
      } else if (paramName == 'eye_bag_removal') {
        parameterName = 'eye_bag_removal';
      } else if (paramName == 'outer_corner_lift') {
        parameterName = 'outer_corner_lift';
      }
    }
    // 嘴巴参数名称映射
    else if (area == 'mouth') {
      if (paramName == 'lip_size') {
        parameterName = 'lip_size';
      } else if (paramName == 'lip_width') {
        parameterName = 'lip_width';
      }
    }
    
    // 根据区域和参数名称获取特征点ID
    try {
      // 对于鼻翼宽度，如果前面的方法没有找到有效的特征点，返回空列表
      if (paramName == 'nostril_width' && area == 'nose') {
        Logger.flowWarning(_logTag, '获取特征点ID', '⚠️ [警告] 无法找到鼻翼特征点，返回空列表');
        result = [];
      } else {
        // 对于其他参数，使用辅助工具获取特征点ID
        result = featurePointsHelper.getFeaturePointIds(area, parameterName);
      }
      
      // 缓存结果
      _featurePointIdsCache[cacheKey] = result;
      
      Logger.flow(_logTag, '获取特征点ID', '✅ [完成] 获取到 ${result.length} 个特征点ID');
    } catch (e) {
      Logger.flowError(_logTag, '获取特征点ID', '❌ [错误] 获取特征点ID失败: $e');
    }
    
    Logger.flowEnd(_logTag, '获取特征点ID');
    return result;
  }
  
  /// 获取参数对应的特征点索引
  /// 
  /// [areaType] 区域类型
  /// [paramName] 参数名称
  /// 返回特征点索引列表
  List<int> getParameterPointIndexes(String areaType, String paramName) {
    Logger.flowStart(_logTag, 'getParameterPointIndexes');
    Logger.flow(_logTag, 'getParameterPointIndexes', '🔍 获取参数点索引: 区域=$areaType, 参数=$paramName');
    
    // 特殊处理鼻翼宽度参数
    if (areaType == 'nose' && paramName == 'nostril_width') {
      Logger.flow(_logTag, 'getParameterPointIndexes', '🔍 [特殊处理] 鼻翼宽度参数');
      
      // 获取实际特征点
      final points = getFeaturePoints();
      if (points == null || points.isEmpty) {
        Logger.flowWarning(_logTag, 'getParameterPointIndexes', '⚠️ 特征点列表为空，无法获取鼻翼特征点');
        Logger.flowEnd(_logTag, 'getParameterPointIndexes');
        return [];
      }
      
      Logger.flow(_logTag, 'getParameterPointIndexes', '🔍 [分析] 实际特征点数量: ${points.length}');
      
      // 尝试查找鼻翼相关的特征点
      List<int> validIndexes = [];
      
      // 首先检查是否有名称包含鼻翼相关关键词的特征点
      for (int i = 0; i < points.length; i++) {
        final point = points[i];
        if (point.name != null && 
            (point.name!.toLowerCase().contains('nostril') || 
             point.name!.toLowerCase().contains('alar') || 
             point.name!.toLowerCase().contains('nose'))) {
          validIndexes.add(i);
          Logger.flow(_logTag, 'getParameterPointIndexes', '✅ [发现] 找到鼻翼相关特征点: 索引=$i, 名称=${point.name}');
        }
      }
      
      // 如果没有找到鼻翼相关特征点，尝试使用左右对称的点
      if (validIndexes.isEmpty) {
        // 计算所有点的中心点
        double sumX = 0;
        if (points == null || points.isEmpty) {
          Logger.flowError(_logTag, '计算对称点', '特征点列表为空，无法计算对称点');
          Logger.flowEnd(_logTag, '计算对称点');
          return [];
        }
        
        for (final point in points) {
          sumX += point.x;
        }
        double centerX = sumX / points.length;
        
        // 找出距离中心最远的左右两个点
        double maxLeftDistance = 0;
        double maxRightDistance = 0;
        int leftIndex = -1;
        int rightIndex = -1;
        
        for (int i = 0; i < points.length; i++) {
          final point = points[i];
          double distanceFromCenter = point.x - centerX;
          
          if (distanceFromCenter < 0 && -distanceFromCenter > maxLeftDistance) {
            maxLeftDistance = -distanceFromCenter;
            leftIndex = i;
          } else if (distanceFromCenter > 0 && distanceFromCenter > maxRightDistance) {
            maxRightDistance = distanceFromCenter;
            rightIndex = i;
          }
        }
        
        if (leftIndex >= 0) {
          validIndexes.add(leftIndex);
          Logger.flow(_logTag, 'getParameterPointIndexes', '✅ [替代] 使用最左侧点作为左鼻翼点: 索引=$leftIndex');
        }
        
        if (rightIndex >= 0) {
          validIndexes.add(rightIndex);
          Logger.flow(_logTag, 'getParameterPointIndexes', '✅ [替代] 使用最右侧点作为右鼻翼点: 索引=$rightIndex');
        }
      }
      
      if (validIndexes.isNotEmpty) {
        Logger.flow(_logTag, 'getParameterPointIndexes', '✅ [完成] 获取到${validIndexes.length}个有效特征点索引');
        Logger.flowEnd(_logTag, 'getParameterPointIndexes');
        return validIndexes;
      } else {
        Logger.flowWarning(_logTag, 'getParameterPointIndexes', '⚠️ 无法找到有效的鼻翼特征点');
        Logger.flowEnd(_logTag, 'getParameterPointIndexes');
        return [];
      }
    }
    
    // 对于其他参数，调用现有方法获取特征点ID
    final indexes = getFeaturePointIds(areaType, paramName);
    
    // 验证索引是否有效
    List<int> validIndexes = [];
    final points = getFeaturePoints();
    
    if (points != null && points.isNotEmpty) {
      for (final index in indexes) {
        if (index >= 0 && index < points.length) {
          validIndexes.add(index);
        } else {
          Logger.flowWarning(_logTag, 'getParameterPointIndexes', '⚠️ 无效索引: $index (超出范围)');
        }
      }
      
      if (validIndexes.isEmpty && indexes.isNotEmpty) {
        Logger.flowWarning(_logTag, 'getParameterPointIndexes', '⚠️ 所有索引都无效，尝试使用替代索引');
        
        // 如果所有索引都无效，尝试使用前几个点
        int maxPoints = math.min(5, points.length);
        for (int i = 0; i < maxPoints; i++) {
          validIndexes.add(i);
        }
        Logger.flow(_logTag, 'getParameterPointIndexes', '✅ [替代] 使用前$maxPoints个点作为替代');
      }
    }
    
    if (validIndexes.isEmpty) {
      Logger.flowWarning(_logTag, 'getParameterPointIndexes', '⚠️ 未找到有效的特征点索引: 区域=$areaType, 参数=$paramName');
    } else {
      Logger.flow(_logTag, 'getParameterPointIndexes', '✅ 获取到${validIndexes.length}个有效特征点索引');
    }
    
    Logger.flowEnd(_logTag, 'getParameterPointIndexes');
    // 只返回有效的索引，而不是返回原始的可能无效的索引
    return validIndexes;
  }
  
  /// 从缓存获取图像
  ui.Image? getImageFromCache(String key) {
    Logger.flowStart(_logTag, '从缓存获取图像');
    Logger.flow(_logTag, '从缓存获取图像', '🔍 [开始] 键: $key');
    
    if (_imageCache.containsKey(key)) {
      Logger.flow(_logTag, '从缓存获取图像', '✅ [完成] 找到缓存图像');
      Logger.flowEnd(_logTag, '从缓存获取图像');
      return _imageCache[key];
    }
    
    Logger.flow(_logTag, '从缓存获取图像', '⚠️ [警告] 缓存中没有图像: $key');
    Logger.flowEnd(_logTag, '从缓存获取图像');
    return null;
  }
  
  /// 将图像添加到缓存
  void addImageToCache(String key, ui.Image image) {
    Logger.flowStart(_logTag, '添加图像到缓存');
    Logger.flow(_logTag, '添加图像到缓存', '🔍 [开始] 键: $key');
    
    _imageCache[key] = image;
    
    Logger.flow(_logTag, '添加图像到缓存', '✅ [完成] 图像已添加到缓存');
    Logger.flowEnd(_logTag, '添加图像到缓存');
  }
  
  /// 清除图像缓存
  void clearImageCache() {
    Logger.flowStart(_logTag, '清除图像缓存');
    
    _imageCache.clear();
    
    Logger.flow(_logTag, '清除图像缓存', '✅ [完成] 图像缓存已清除');
    Logger.flowEnd(_logTag, '清除图像缓存');
  }
  

  
  /// 初始化变形渲染器
  Future<void> initializeDeformationRenderer() async {
    Logger.flowStart(_logTag, '初始化变形渲染器');
    
    // 使用 SimpleDeformationRenderer 的单例实例，避免重复创建
    if (_deformationAreaRenderer == null) {
      _deformationAreaRenderer = SimpleDeformationRenderer.instance;
      
      // 设置默认尺寸
      final defaultSize = Size(400, 600);
      _deformationAreaRenderer?.setImageSize(defaultSize);
      
      // 初始时隐藏渲染器
      _deformationAreaRenderer?.setVisible(false);
      
      Logger.flow(_logTag, '初始化变形渲染器', '✅ 使用单例实例初始化渲染器成功');
    } else {
      Logger.flow(_logTag, '初始化变形渲染器', 'ℹ️ 渲染器实例已存在，跳过初始化');
    }
    
    Logger.flowEnd(_logTag, '初始化变形渲染器');
  }
  
  /// 设置原始图像路径
  Future<void> setOriginalImagePath(String path) async {
    Logger.flowStart(_logTag, '设置原始图像路径');
    Logger.flow(_logTag, '设置原始图像路径', '🔍 [开始] 路径: $path');
    
    // 检查是否在短时间内重复处理相同的图像
    final now = DateTime.now();
    if (_lastProcessedImagePath == path && _lastProcessTime != null) {
      final timeSinceLastProcess = now.difference(_lastProcessTime!).inMilliseconds;
      
      if (timeSinceLastProcess < _minProcessIntervalMillis) {
        Logger.flowWarning(_logTag, '设置原始图像路径', 
            '⚠️ 防止重复处理: 相同图像($path)在${timeSinceLastProcess}毫秒内被重复处理，已跳过');
        Logger.flowEnd(_logTag, '设置原始图像路径');
        return;
      }
    }
    
    // 更新最后处理的图像信息
    _lastProcessedImagePath = path;
    _lastProcessTime = now;
    
    // 如果路径发生变化，清除所有缓存和累积状态
    if (_originalImagePath != path) {
      Logger.flow(_logTag, '设置原始图像路径', '🧹 [清理] 图像路径已变化，清除所有缓存和累积状态');
      
      // 【关键新增】新图像导入时清除全局面部中心线缓存
      Logger.flow(_logTag, '设置原始图像路径', 
          '🗑️ [关键] 清除全局面部中心线缓存，等待新图像特征识别后重新计算');
      DeformationCacheManager.clearGlobalFacialCenterLine();
      
      // 清除变形服务的缓存
      clearCache();
      
      // 清除图像缓存
      clearImageCache();
      
      // 清除渲染器的缓存和累积状态
      if (_deformationAreaRenderer != null) {
        Logger.flow(_logTag, '设置原始图像路径', '🧹 [清理] 清除渲染器的缓存和累积状态');
        // 图像路径变化时，应清除累积状态，所以传入onlyWhenImageChanged=true
        _deformationAreaRenderer!.clearCache(onlyWhenImageChanged: true);
        
        // 获取特征点管理器并清除缓存
        final featurePointManager = _deformationAreaRenderer!.getFeaturePointManager();
        if (featurePointManager != null) {
          Logger.flow(_logTag, '设置原始图像路径', '🧹 [清理] 清除特征点管理器的缓存');
          featurePointManager.invalidateCache();
        }
      }
      
      // 清除所有参数值缓存
      _resetAllParameters();
      Logger.flow(_logTag, '设置原始图像路径', '🧹 [清理] 所有参数值已重置');
      
      // 重置当前选中的区域和参数
      // 使用现有的方法重置区域和参数
      if (_deformationAreaRenderer != null) {
        _deformationAreaRenderer!.setArea('none');
        _deformationAreaRenderer!.setParameterName('');
        Logger.flow(_logTag, '设置原始图像路径', '🧹 [清理] 当前选中的区域和参数已重置');
      }
    }
    
    // 保存原始图像路径
    _originalImagePath = path;
    
    // 如果变形渲染器已初始化，则尝试加载图像并设置到渲染器中
    if (_deformationAreaRenderer != null) {
      Logger.flow(_logTag, '设置原始图像路径', '🖼️ [加载] 尝试加载图像并设置到渲染器中');
      
      // 检查图像是否已在缓存中
      final cachedImage = getImageFromCache(path);
      if (cachedImage != null) {
        Logger.flow(_logTag, '设置原始图像路径', '✅ [缓存] 从缓存中获取到图像');
        _deformationAreaRenderer!.setImage(cachedImage);
        _deformationAreaRenderer!.setImageSize(Size(cachedImage.width.toDouble(), cachedImage.height.toDouble()));
      } else {
        Logger.flow(_logTag, '设置原始图像路径', '⚠️ [警告] 图像不在缓存中，需要异步加载');
        
        // 异步加载图像
        _loadImageAsync(path).then((image) {
          if (image != null) {
            Logger.flow(_logTag, '设置原始图像路径', '✅ [异步] 图像加载成功，设置到渲染器中');
            _deformationAreaRenderer!.setImage(image);
            _deformationAreaRenderer!.setImageSize(Size(image.width.toDouble(), image.height.toDouble()));
            
            // 添加到缓存
            addImageToCache(path, image);
            
            // 通知监听器
            notifyListeners();
          } else {
            Logger.flowError(_logTag, '设置原始图像路径', '❌ [错误] 图像加载失败');
          }
        });
      }
    } else {
      Logger.flowWarning(_logTag, '设置原始图像路径', '⚠️ 变形渲染器未初始化，无法设置图像');
    }
    
    Logger.flow(_logTag, '设置原始图像路径', '✅ [完成] 原始图像路径已设置');
    Logger.flowEnd(_logTag, '设置原始图像路径');
    notifyListeners();
  }
  
  /// 异步加载图像
  Future<ui.Image?> _loadImageAsync(String path) async {
    Logger.flowStart(_logTag, '异步加载图像');
    Logger.flow(_logTag, '异步加载图像', '🔍 [开始] 路径: $path');
    
    try {
      final file = File(path);
      if (!file.existsSync()) {
        Logger.flowError(_logTag, '异步加载图像', '❌ [错误] 文件不存在: $path');
        Logger.flowEnd(_logTag, '异步加载图像');
        return null;
      }
      
      final bytes = await file.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frameInfo = await codec.getNextFrame();
      final image = frameInfo.image;
      
      Logger.flow(_logTag, '异步加载图像', '✅ [完成] 图像加载成功，尺寸: ${image.width}x${image.height}');
      Logger.flowEnd(_logTag, '异步加载图像');
      return image;
    } catch (e) {
      Logger.flowError(_logTag, '异步加载图像', '❌ [错误] 加载图像时发生异常: $e');
      Logger.flowEnd(_logTag, '异步加载图像');
      return null;
    }
  }
  
  /// 获取选中的区域
  String? get selectedArea => _selectedArea;
  
  /// 获取变形后的图像路径
  String? get transformedImagePath => _transformedImagePath;
  
  /// 是否正在变形
  bool get isDeforming => _isDeforming;
  
  /// 设置选中的区域
  void setSelectedArea(String areaName) {
    Logger.flowStart(_logTag, '设置选中区域');
    Logger.flow(_logTag, '设置选中区域', '🔍 [开始] 区域: $areaName');
    
    _selectedArea = areaName;
    
    Logger.flow(_logTag, '设置选中区域', '✅ [完成] 选中区域已设置: $areaName');
    Logger.flowEnd(_logTag, '设置选中区域');
    notifyListeners();
  }
  
  /// 获取简单变形渲染器
  SimpleDeformationRenderer? getSimpleDeformationRenderer() {
    if (_deformationAreaRenderer == null) {
      Logger.flowStart(_logTag, '获取简单变形渲染器');
      Logger.flow(_logTag, '获取简单变形渲染器', '🔍 [开始] 变形区域渲染器未初始化，尝试获取单例实例');
      
      try {
        // 使用 SimpleDeformationRenderer 的单例实例，避免重复创建
        _deformationAreaRenderer = SimpleDeformationRenderer.instance;
        
        Logger.flow(_logTag, '获取简单变形渲染器', '✅ [完成] 成功获取变形区域渲染器单例实例');
        Logger.flowEnd(_logTag, '获取简单变形渲染器');
      } catch (e) {
        Logger.flowError(_logTag, '获取简单变形渲染器', '❌ [错误] 获取变形区域渲染器单例实例失败: $e');
        Logger.flowEnd(_logTag, '获取简单变形渲染器');
        return null;
      }
    }
    
    return _deformationAreaRenderer;
  }
  
  /// 获取当前图像尺寸
  /// 
  /// 返回当前处理图像的尺寸，如果渲染器未初始化则返回 Size.zero
  Size getImageSize() {
    Logger.flowStart(_logTag, 'getImageSize');
    
    if (_deformationAreaRenderer == null) {
      Logger.flowWarning(_logTag, 'getImageSize', '⚠️ 变形区域渲染器未初始化');
      Logger.flowEnd(_logTag, 'getImageSize');
      return Size.zero;
    }
    
    final size = _deformationAreaRenderer!.imageSize;
    Logger.flow(_logTag, 'getImageSize', '✅ 获取到图像尺寸: $size');
    Logger.flowEnd(_logTag, 'getImageSize');
    return size;
  }
  
  /// 设置特征点数据
  Future<void> setFeaturePoints(List<FeaturePoint> featurePoints, {String? imagePath}) async {
    Logger.flowStart(_logTag, '设置特征点');
    Logger.flow(_logTag, '设置特征点', '接收到 ${featurePoints.length} 个特征点');
    
    _featurePoints = featurePoints;
    
    // 确保 featurePointManager 已初始化
    if (featurePointManager == null) {
      Logger.flow(_logTag, '设置特征点', '🔄 [初始化] TransformationService 的 featurePointManager');
      featurePointManager = FeaturePointManager();
      await featurePointManager.initialize();
      Logger.flow(_logTag, '设置特征点', '✅ [初始化] TransformationService 的 featurePointManager 已完成');
    }
    
    // 设置特征点数据到 TransformationService 的 featurePointManager
    try {
      Logger.flow(_logTag, '设置特征点', '💾 [设置] 将特征点数据设置到 TransformationService 的 FeaturePointManager');
      featurePointManager.featurePoints = featurePoints;
      Logger.flow(_logTag, '设置特征点', '✅ [设置] 特征点数据已设置到 TransformationService 的 FeaturePointManager');
    } catch (e) {
      Logger.flowError(_logTag, '设置特征点', '❌ [错误] 设置 TransformationService 的 FeaturePointManager 失败: ${e.toString()}');
    }
    
    // 确保渲染器已初始化
    if (_deformationAreaRenderer == null) {
      Logger.flow(_logTag, '设置特征点', '🔄 [初始化] 等待变形渲染器初始化');
      await ensureDeformationRendererInitialized();
      
      if (_deformationAreaRenderer == null) {
        Logger.flowError(_logTag, '设置特征点', '❌ [错误] 变形渲染器初始化失败');
        Logger.flowEnd(_logTag, '设置特征点');
        notifyListeners();
        return;
      }
      Logger.flow(_logTag, '设置特征点', '✅ [初始化] 变形渲染器初始化完成');
    }
    
    // 设置特征点数据到 SimpleDeformationRenderer 的 FeaturePointManager
    final rendererFeaturePointManager = _deformationAreaRenderer!.getFeaturePointManager();
    if (rendererFeaturePointManager != null) {
      Logger.flow(_logTag, '设置特征点', '💾 [设置] 将特征点数据设置到 SimpleDeformationRenderer 的 FeaturePointManager');
      rendererFeaturePointManager.featurePoints = featurePoints;
      Logger.flow(_logTag, '设置特征点', '✅ [设置] 特征点数据已设置到 SimpleDeformationRenderer 的 FeaturePointManager');
    } else {
      Logger.flowError(_logTag, '设置特征点', '❌ [错误] SimpleDeformationRenderer 的特征点管理器为空');
    }
    
    // 如果提供了图像路径，设置原始图像路径
    if (imagePath != null) {
      Logger.flow(_logTag, '设置特征点', 'ℹ️ [信息] 设置原始图像路径: $imagePath');
      _originalImagePath = imagePath;
      
      // 检查图像是否已在缓存中
      final cachedImage = getImageFromCache(imagePath);
      if (cachedImage != null) {
        Logger.flow(_logTag, '设置特征点', '✅ [缓存] 从缓存中获取到图像');
        _deformationAreaRenderer!.setImage(cachedImage);
        _deformationAreaRenderer!.setImageSize(Size(cachedImage.width.toDouble(), cachedImage.height.toDouble()));
      } else {
        Logger.flow(_logTag, '设置特征点', '🔄 [加载] 开始加载图像');
        final image = await _loadImageAsync(imagePath);
        if (image != null) {
          Logger.flow(_logTag, '设置特征点', '✅ [加载] 图像加载成功');
          _deformationAreaRenderer!.setImage(image);
          _deformationAreaRenderer!.setImageSize(Size(image.width.toDouble(), image.height.toDouble()));
          addImageToCache(imagePath, image);
        } else {
          Logger.flowError(_logTag, '设置特征点', '❌ [错误] 图像加载失败');
        }
      }
    }
    
    // 更新特征点数据到渲染器
    // 诊断日志 - 确认渲染器和图像状态
    Logger.flowError(_logTag, '设置特征点', '🔄 [更新] 更新渲染器特征点数据');
    print('🔍 诊断: 渲染器实例ID: ${_deformationAreaRenderer.hashCode}, 缓存管理器实例ID: ${_deformationAreaRenderer!.getCacheManager()?.hashCode}');
    // 使用getImageStatus()方法获取图像状态
    final imageStatus = _deformationAreaRenderer!.getImageStatus();
    print('🔍 诊断: 图像状态: ${imageStatus['hasImage'] ? "有效" : "为空"}, 图像尺寸: ${imageStatus['width']}x${imageStatus['height']}');
    
    // 通知渲染器更新特征点 - 这里应该创建zero_state缓存
    _deformationAreaRenderer!.updateFeaturePoints(featurePoints);
    
    // 重要验证：确保零状态缓存已正确创建
    final zeroStateParams = {'zero_state': 0.0};
    final cacheManager = _deformationAreaRenderer!.getCacheManager();
    final verifyCache = cacheManager?.find(zeroStateParams);
    
    if (verifyCache != null && verifyCache.deformedImage != null) {
      // 零状态缓存创建成功
      Logger.flowError(_logTag, '设置特征点', '✅✅✅ zero_state缓存验证成功! 图像尺寸: ${verifyCache.deformedImage!.width}x${verifyCache.deformedImage!.height}, 特征点数: ${verifyCache.deformedFeaturePoints?.length ?? 0}');
      print('✅✅✅ zero_state缓存验证成功! 图像尺寸: ${verifyCache.deformedImage!.width}x${verifyCache.deformedImage!.height}');
      
      // 验证缓存中的特征点
      if (verifyCache.deformedFeaturePoints != null && verifyCache.deformedFeaturePoints!.isNotEmpty) {
        print('✅ zero_state缓存包含 ${verifyCache.deformedFeaturePoints!.length} 个特征点');
      } else {
        print('⚠️ zero_state缓存中特征点缺失!');
      }
    } else {
      // 零状态缓存创建失败
      Logger.flowError(_logTag, '设置特征点', '❌❌❌ 严重警告: zero_state缓存创建失败! 这将导致变形模块崩溃');
      print('❌❌❌ 严重警告: zero_state缓存创建失败! 这将导致变形模块崩溃');
    }
    
    // 清除所有相关缓存
    _featurePointIdsCache.clear();
    _areaPointsCache.clear();
    
    Logger.flow(_logTag, '设置特征点', '✅ [完成] 特征点数据已更新');
    Logger.flowEnd(_logTag, '设置特征点');
    notifyListeners();
  }
  
  /// 设置侧面特征点数据
  Future<void> setSideFeaturePoints(List<FeaturePoint> sideFeaturePoints, String imagePath) async {
    Logger.flowStart(_logTag, '设置侧面特征点');
    Logger.flow(_logTag, '设置侧面特征点', '接收到 ${sideFeaturePoints.length} 个侧面特征点');
    
    // 保存侧面特征点数据
    featurePointManager.sideFeaturePoints = sideFeaturePoints;
    Logger.flow(_logTag, '设置侧面特征点', '✅ [设置] 侧面特征点数据已设置到 FeaturePointManager');
    
    // 如果渲染器未初始化，则初始化
    if (_deformationAreaRenderer == null) {
      Logger.flow(_logTag, '设置侧面特征点', '🔄 [初始化] 等待变形渲染器初始化');
      await ensureDeformationRendererInitialized();
      
      if (_deformationAreaRenderer == null) {
        Logger.flowError(_logTag, '设置侧面特征点', '❌ [错误] 变形渲染器初始化失败');
        Logger.flowEnd(_logTag, '设置侧面特征点');
        notifyListeners();
        return;
      }
      Logger.flow(_logTag, '设置侧面特征点', '✅ [初始化] 变形渲染器初始化完成');
    }
    
    // 确保侧面图不显示特征点
    if (_deformationAreaRenderer != null) {
      // 注释掉侧面图相关日志
      // Logger.flow(_logTag, '设置侧面特征点', '🔧 [配置] 设置侧面图像显示特征点');
      _deformationAreaRenderer!.setShowFeaturePoints(true);
    }
    
    // 设置侧面特征点数据到渲染器的特征点管理器
    final rendererFeaturePointManager = _deformationAreaRenderer!.getFeaturePointManager();
    if (rendererFeaturePointManager != null) {
      Logger.flow(_logTag, '设置侧面特征点', '💾 [设置] 将侧面特征点数据设置到 SimpleDeformationRenderer 的 FeaturePointManager');
      rendererFeaturePointManager.sideFeaturePoints = sideFeaturePoints;
      Logger.flow(_logTag, '设置侧面特征点', '✅ [设置] 侧面特征点数据已设置到 SimpleDeformationRenderer 的 FeaturePointManager');
    } else {
      Logger.flowError(_logTag, '设置侧面特征点', '❌ [错误] SimpleDeformationRenderer 的特征点管理器为空');
    }
    
    // 保存侧面图像路径
    _sideImagePath = imagePath;
    // 注释掉侧面图相关日志
    // Logger.flow(_logTag, '设置侧面特征点', 'ℹ️ [信息] 设置侧面图像路径: $imagePath');
    
    // 加载侧面图像
    // 注释掉侧面图相关日志
    // Logger.flow(_logTag, '设置侧面特征点', '🔄 [加载] 开始加载侧面图像');
    final sideImage = await _loadImageAsync(imagePath);
    if (sideImage != null) {
      // 注释掉侧面图相关日志
      // Logger.flow(_logTag, '设置侧面特征点', '✅ [加载] 侧面图像加载成功');
      _deformationAreaRenderer!.setSideImage(sideImage);
      addImageToCache('side_$imagePath', sideImage);
    } else {
      // 注释掉侧面图相关日志
      // Logger.flowError(_logTag, '设置侧面特征点', '❌ [错误] 侧面图像加载失败');
    }
    
    // 更新侧面特征点数据到渲染器
    Logger.flow(_logTag, '设置侧面特征点', '🔄 [更新] 更新渲染器侧面特征点数据');
    _deformationAreaRenderer!.updateSideFeaturePoints(sideFeaturePoints);
    
    // 再次确保侧面图不显示特征点（防止被其他操作覆盖）
    if (_deformationAreaRenderer != null) {
      // 注释掉侧面图相关日志
      // Logger.flow(_logTag, '设置侧面特征点', '🔧 [配置] 最终确认侧面图像显示特征点');
      _deformationAreaRenderer!.setShowFeaturePoints(true);
    }
    
    Logger.flow(_logTag, '设置侧面特征点', '✅ [完成] 侧面特征点数据已更新');
    Logger.flowEnd(_logTag, '设置侧面特征点');
    notifyListeners();
  }
  
  /// 对变形后的图片进行特征点识别
  /// 
  /// [deformedImage] 变形后的图片
  /// 返回识别到的特征点列表
  Future<List<FeaturePoint>?> recognizeFeaturePoints(ui.Image deformedImage) async {
    Logger.flowStart(_logTag, 'recognizeFeaturePoints');
    Logger.flow(_logTag, 'recognizeFeaturePoints', '🔍 开始对变形后图片进行特征点识别');
    
    // 直接使用原始图像尺寸，不进行尺寸调整
    ui.Image imageToUse = deformedImage;
    Logger.flow(_logTag, 'recognizeFeaturePoints', '📷 使用原始图像尺寸: ${imageToUse.width}x${imageToUse.height}');
    
    // 仅在调试模式下输出图像信息
    if (_isDebugMode) {
      Logger.flow(_logTag, 'recognizeFeaturePoints', '📷 原始图像信息: 尺寸=${imageToUse.width}x${imageToUse.height}, 哈希码=${imageToUse.hashCode}');
    }
    
    // 获取当前特征点数据作为参考
    final currentFeaturePoints = featurePointManager.getFeaturePoints();
    Logger.flow(_logTag, 'recognizeFeaturePoints', '📊 当前特征点数量: ${currentFeaturePoints.length}');
    
    // 输出前3个当前特征点的坐标
    if (currentFeaturePoints.length >= 3) {
      Logger.flow(_logTag, 'recognizeFeaturePoints', '📍 前3个当前特征点坐标:');
      for (int i = 0; i < 3; i++) {
        final point = currentFeaturePoints[i];
        Logger.flow(_logTag, 'recognizeFeaturePoints', '   点[${point.index}]: (${point.x.toStringAsFixed(1)}, ${point.y.toStringAsFixed(1)})');
      }
    }
    
    // 将图像转换为字节数据，用于特征点识别
    final byteData = await imageToUse.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) {
      Logger.flowError(_logTag, 'recognizeFeaturePoints', '❌ 无法将图像转换为字节数据');
      Logger.flowEnd(_logTag, 'recognizeFeaturePoints');
      return null;
    }
    
    // 保存图像到临时文件
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/deformed_image_${DateTime.now().millisecondsSinceEpoch}.png');
    await tempFile.writeAsBytes(byteData.buffer.asUint8List());
    
    Logger.flow(_logTag, 'recognizeFeaturePoints', '💾 变形后图像已保存到临时文件: ${tempFile.path}');
    
    // 调用Python脚本进行特征点识别
    // 确保使用的是变形后的图像
    Logger.flow(_logTag, 'recognizeFeaturePoints', '🐍 调用Python脚本进行特征点识别');
    final result = await _runFeaturePointDetection(tempFile.path);
    
    if (result == null || result.isEmpty) {
      Logger.flowError(_logTag, 'recognizeFeaturePoints', '❌ 特征点识别失败或结果为空');
      Logger.flowEnd(_logTag, 'recognizeFeaturePoints');
      
      // 清理临时文件
      if (await tempFile.exists()) {
        await tempFile.delete();
      }
      
      return null;
    }
    
    // 解析特征点识别结果
    Logger.flow(_logTag, 'recognizeFeaturePoints', '🔄 解析特征点识别结果');
    final recognizedFeaturePoints = _parseFeaturePointDetectionResult(result, currentFeaturePoints);
    
    // 清理临时文件
    if (await tempFile.exists()) {
      await tempFile.delete();
    }
    
    if (recognizedFeaturePoints.isEmpty) {
      Logger.flowError(_logTag, 'recognizeFeaturePoints', '❌ 解析特征点识别结果失败或结果为空');
      Logger.flowEnd(_logTag, 'recognizeFeaturePoints');
      return null;
    }
    
    // 输出前3个识别到的特征点的坐标
    if (recognizedFeaturePoints.length >= 3) {
      Logger.flow(_logTag, 'recognizeFeaturePoints', '📍 前3个识别到的特征点坐标:');
      for (int i = 0; i < 3; i++) {
        final point = recognizedFeaturePoints[i];
        Logger.flow(_logTag, 'recognizeFeaturePoints', '   点[${point.index}]: (${point.x.toStringAsFixed(1)}, ${point.y.toStringAsFixed(1)})');
      }
    }
    
    Logger.flow(_logTag, 'recognizeFeaturePoints', '✅ 特征点识别成功，识别到 ${recognizedFeaturePoints.length} 个特征点');
    Logger.flowEnd(_logTag, 'recognizeFeaturePoints');
    return recognizedFeaturePoints;
  }
  
  /// 运行特征点检测
  /// 
  /// [imagePath] 图像路径
  /// 返回特征点检测结果
  Future<String?> _runFeaturePointDetection(String imagePath) async {
    Logger.flowStart(_logTag, '_runFeaturePointDetection');
    Logger.flow(_logTag, '_runFeaturePointDetection', '🔍 开始运行特征点检测');
    
    try {
      // 使用项目根目录中的脚本路径
      final scriptPath = '/Users/<USER>/beautifun/core/face_mesh_processor.py';
      
      // 检查脚本是否存在
      final scriptFile = File(scriptPath);
      if (!await scriptFile.exists()) {
        Logger.flowError(_logTag, '_runFeaturePointDetection', '❌ 特征点检测脚本不存在: $scriptPath');
        Logger.flowEnd(_logTag, '_runFeaturePointDetection');
        return null;
      }
      
      // 构建命令
      final command = 'python3.11 "$scriptPath" --image "$imagePath" --mode detect_only';
      
      // 执行命令
      Logger.flow(_logTag, '_runFeaturePointDetection', '🔄 执行命令: $command');
      final result = await Process.run('/bin/bash', ['-c', command]);
      
      if (result.exitCode != 0) {
        Logger.flowError(_logTag, '_runFeaturePointDetection', '❌ 特征点检测失败，退出码: ${result.exitCode}');
        Logger.flowError(_logTag, '_runFeaturePointDetection', '错误输出: ${result.stderr}');
        Logger.flowEnd(_logTag, '_runFeaturePointDetection');
        return null;
      }
      
      Logger.flow(_logTag, '_runFeaturePointDetection', '✅ 特征点检测成功');
      Logger.flowEnd(_logTag, '_runFeaturePointDetection');
      return result.stdout.toString();
    } catch (e, stackTrace) {
      Logger.flowError(_logTag, '_runFeaturePointDetection', '❌ 运行特征点检测时发生异常: $e');
      Logger.flowError(_logTag, '_runFeaturePointDetection', '堆栈跟踪: $stackTrace');
      Logger.flowEnd(_logTag, '_runFeaturePointDetection');
      return null;
    }
  }
  
  /// 解析特征点检测结果
  /// 
  /// [result] 特征点检测结果
  /// [currentFeaturePoints] 当前特征点数据
  /// 返回解析后的特征点列表
  List<FeaturePoint> _parseFeaturePointDetectionResult(String result, List<FeaturePoint> currentFeaturePoints) {
    Logger.flowStart(_logTag, '_parseFeaturePointDetectionResult');
    Logger.flow(_logTag, '_parseFeaturePointDetectionResult', '🔍 开始解析特征点检测结果');
    
    try {
      // 解析结果
      // 注意：这里需要根据实际的结果格式进行解析
      // 假设结果是JSON格式的特征点数据
      final lines = result.split('\n');
      final featurePointsData = lines.where((line) => line.contains('landmarks:')).join('\n');
      
      if (featurePointsData.isEmpty) {
        Logger.flowError(_logTag, '_parseFeaturePointDetectionResult', '❌ 无法从结果中提取特征点数据');
        Logger.flowEnd(_logTag, '_parseFeaturePointDetectionResult');
        return [];
      }
      
      // 解析特征点数据
      // 注意：这里需要根据实际的数据格式进行解析
      // 假设数据格式是：landmarks: x1,y1,z1 x2,y2,z2 ...
      final landmarksMatch = RegExp(r'landmarks: (.+)').firstMatch(featurePointsData);
      if (landmarksMatch == null) {
        Logger.flowError(_logTag, '_parseFeaturePointDetectionResult', '❌ 无法匹配特征点数据');
        Logger.flowEnd(_logTag, '_parseFeaturePointDetectionResult');
        return [];
      }
      
      final landmarksStr = landmarksMatch.group(1);
      if (landmarksStr == null || landmarksStr.isEmpty) {
        Logger.flowError(_logTag, '_parseFeaturePointDetectionResult', '❌ 特征点数据为空');
        Logger.flowEnd(_logTag, '_parseFeaturePointDetectionResult');
        return [];
      }
      
      // 解析坐标
      final coordinates = landmarksStr.trim().split(' ');
      final recognizedFeaturePoints = <FeaturePoint>[];
      
      Logger.flow(_logTag, '_parseFeaturePointDetectionResult', '📊 解析到 ${coordinates.length} 个坐标点，当前特征点数量: ${currentFeaturePoints.length}');
      
      // 确保图像尺寸为1024x1478
      final targetWidth = 1024;
      final targetHeight = 1478;
      
      for (int i = 0; i < coordinates.length && i < currentFeaturePoints.length; i++) {
        final coords = coordinates[i].split(',');
        if (coords.length >= 3) {
          // 解析坐标值
          double x = double.tryParse(coords[0]) ?? 0.0;
          double y = double.tryParse(coords[1]) ?? 0.0;
          final z = double.tryParse(coords[2]) ?? 0.0;
          
          // 确保坐标在图像范围内
          if (x < 0) x = 0;
          if (x >= targetWidth) x = targetWidth - 1;
          if (y < 0) y = 0;
          if (y >= targetHeight) y = targetHeight - 1;
          
          // 创建新的特征点，保留原始特征点的其他属性
          final originalPoint = currentFeaturePoints[i];
          
          // 记录坐标变化
          if (i < 10 || i % 50 == 0) {
            Logger.flow(_logTag, '_parseFeaturePointDetectionResult', '   点[${originalPoint.index}]坐标变化: (${originalPoint.x.toStringAsFixed(1)}, ${originalPoint.y.toStringAsFixed(1)}) -> (${x.toStringAsFixed(1)}, ${y.toStringAsFixed(1)})');
          }
          
          final newPoint = FeaturePoint(
            id: originalPoint.id,
            index: originalPoint.index,
            name: originalPoint.name,
            x: x,
            y: y,
            z: z,
            visibility: originalPoint.visibility,
            confidence: originalPoint.confidence,
            isPrimary: originalPoint.isPrimary,
            opacity: originalPoint.opacity,
            size: originalPoint.size,
            color: originalPoint.color,
          );
          
          recognizedFeaturePoints.add(newPoint);
        }
      }
      
      // 输出前5个解析后的特征点坐标
      if (recognizedFeaturePoints.length >= 5) {
        Logger.flow(_logTag, '_parseFeaturePointDetectionResult', '📍 前5个解析后的特征点坐标:');
        for (int i = 0; i < 5; i++) {
          final point = recognizedFeaturePoints[i];
          Logger.flow(_logTag, '_parseFeaturePointDetectionResult', '   点[${point.index}]: (${point.x.toStringAsFixed(1)}, ${point.y.toStringAsFixed(1)})');
        }
      }
      
      Logger.flow(_logTag, '_parseFeaturePointDetectionResult', '✅ 解析特征点检测结果成功，解析到 ${recognizedFeaturePoints.length} 个特征点');
      Logger.flowEnd(_logTag, '_parseFeaturePointDetectionResult');
      return recognizedFeaturePoints;
    } catch (e, stackTrace) {
      Logger.flowError(_logTag, '_parseFeaturePointDetectionResult', '❌ 解析特征点检测结果时发生异常: $e');
      Logger.flowError(_logTag, '_parseFeaturePointDetectionResult', '堆栈跟踪: $stackTrace');
      Logger.flowEnd(_logTag, '_parseFeaturePointDetectionResult');
      return [];
    }
  }
}
