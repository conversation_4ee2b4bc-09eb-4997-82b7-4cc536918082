import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../core_integration/nose_transformation_service.dart';
import '../models/nose_transformation_parameters.dart';
import '../models/feature_point.dart';
import '../models/nose_feature_points.dart';
import '../utils/logger.dart';
import '../utils/performance_monitor.dart';
import 'transformation_service.dart';
import '../animations/feature_points_animation.dart' as animation;

/// 鼻部变形集成服务
/// 
/// 负责协调鼻部变形的各个组件，包括参数控制、变形服务和预览面板
class NoseTransformationIntegrationService extends ChangeNotifier {
  /// 日志标签
  static const String _logTag = 'NoseTransformationIntegrationService';
  
  /// 鼻部变形服务
  final NoseTransformationService _noseTransformationService;
  
  /// 变形服务
  final TransformationService _transformationService;
  
  /// 当前图像路径
  String? _currentImagePath;
  
  /// 变形参数
  NoseTransformationParameters _parameters;
  
  /// 是否正在处理
  bool _isProcessing = false;
  bool get isProcessing => _isProcessing;
  
  /// 医学建议
  List<String> _medicalAdvice = [];
  List<String> get medicalAdvice => _medicalAdvice;
  
  /// 预览图像数据
  Uint8List? _previewImageData;
  Uint8List? get previewImageData => _previewImageData;
  
  /// 是否自动预览
  bool _autoPreview = true;
  
  /// 日志工具
  final _logger = BeautifyLogger();
  
  /// 性能监控工具
  final _performanceMonitor = PerformanceMonitor();
  
  /// 当前选中的特征点ID
  String? _selectedFeaturePointId;
  String? get selectedFeaturePointId => _selectedFeaturePointId;
  
  /// 特征点动画控制器
  animation.FeaturePointsAnimation? _featurePointsAnimationController;
  
  /// 构造函数
  NoseTransformationIntegrationService({
    NoseTransformationService? noseTransformationService,
    TransformationService? transformationService,
  }) : 
    _noseTransformationService = noseTransformationService ?? NoseTransformationService(),
    _transformationService = transformationService ?? TransformationService(),
    _parameters = NoseTransformationParameters.createDefault() {
    _initialize();
  }
  
  /// 初始化服务
  Future<void> _initialize() async {
    try {
      _logger.info(
        module: _logTag,
        function: '初始化',
        operation: OperationType.init,
        message: '开始初始化鼻部变形集成服务'
      );
      
      // 启动性能监控
      final stopwatch = _performanceMonitor.startTimer();
      
      // 初始化鼻部变形服务
      await _noseTransformationService.initialize();
      
      // 停止性能监控
      _performanceMonitor.stopTimer(
        stopwatch: stopwatch,
        category: 'noseTransformation',
        operation: 'initialize',
        data: {},
      );
      
      _logger.info(
        module: _logTag,
        function: '初始化',
        operation: OperationType.init,
        message: '鼻部变形集成服务初始化完成'
      );
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '初始化',
        operation: OperationType.init,
        message: '鼻部变形集成服务初始化失败: $e'
      );
    }
  }
  
  /// 设置当前图像路径
  Future<void> setImagePath(String imagePath) async {
    if (_currentImagePath != imagePath) {
      _currentImagePath = imagePath;
      _previewImageData = null;
      notifyListeners();
      
      _logger.info(
        module: _logTag,
        function: '设置图像路径',
        operation: OperationType.process,
        message: '图像路径: $imagePath'
      );
    }
  }
  
  /// 更新参数
  Future<void> updateParameter(NoseParameterType type, double value) async {
    if (_parameters == null) {
      _logger.info(
        module: _logTag,
        function: '更新参数',
        operation: OperationType.process,
        message: '参数对象为空，无法更新'
      );
      return;
    }
    
    try {
      // 获取旧值
      final oldValue = _parameters.getParameter(type)?.value ?? 0.0;
      
      // 记录参数更新
      debugPrint('===== 参数调整事件 =====');
      debugPrint('区域: 鼻部');
      debugPrint('参数类型: ${type.toString().split('.').last}');
      debugPrint('参数值: $value (旧值: $oldValue)');
      
      _logger.info(
        module: _logTag,
        function: '更新参数',
        operation: OperationType.start,
        message: '开始更新参数: ${type.toString()} = $value (旧值: $oldValue)'
      );
      
      // 更新参数
      _parameters.updateParameter(type, value);
      
      // 检查参数是否超过医学建议阈值
      final paramValue = _parameters.getParameter(type);
      if (paramValue != null) {
        final exceedsThreshold = paramValue.exceedsMedicalAdviceThreshold();
        debugPrint('是否超过医学建议阈值: ${exceedsThreshold ? "是" : "否"}');
        
        _logger.info(
          module: _logTag,
          function: '更新参数',
          operation: OperationType.info,
          message: '参数 ${type.toString()} 更新后值: ${paramValue.value}, 是否超过阈值: ${exceedsThreshold}'
        );
      }
      
      // 获取影响的特征点
      final affectedFeaturePoints = _getAffectedFeaturePoints(type);
      
      // 区分主要和次要特征点
      final primaryPoints = affectedFeaturePoints.where((p) => p.isPrimary).toList();
      final secondaryPoints = affectedFeaturePoints.where((p) => !p.isPrimary).toList();
      
      debugPrint('受影响特征点: ${affectedFeaturePoints.length}个');
      debugPrint('主要特征点: ${primaryPoints.length}个');
      if (primaryPoints.isNotEmpty) {
        debugPrint('主要特征点ID: ${primaryPoints.map((p) => p.id).join(', ')}');
      }
      debugPrint('次要特征点: ${secondaryPoints.length}个');
      debugPrint('===========================');
      
      // 更新医学建议
      await _updateMedicalAdvice();
      
      // 触发预览更新
      if (_autoPreview) {
        debugPrint('触发预览更新');
        _logger.info(
          module: _logTag,
          function: '更新参数',
          operation: OperationType.process,
          message: '参数更新后触发预览'
        );
        await _previewTransformation();
      } else {
        _logger.info(
          module: _logTag,
          function: '更新参数',
          operation: OperationType.info,
          message: '自动预览已禁用，不触发预览'
        );
      }
      
      debugPrint('===== 参数调整流程完成 =====');
      
      _logger.info(
        module: _logTag,
        function: '更新参数',
        operation: OperationType.end,
        message: '参数更新完成: ${type.toString()} = $value | 影响特征点: 主要(${primaryPoints.length})个, 次要(${secondaryPoints.length})个'
      );
      
      notifyListeners();
    } catch (e) {
      debugPrint('===== 参数调整失败 =====');
      debugPrint('错误: $e');
      
      _logger.error(
        module: _logTag,
        function: '更新参数',
        operation: OperationType.error,
        message: '更新参数失败: $e'
      );
    }
  }
  
  /// 获取受参数影响的特征点
  List<FeaturePoint> _getAffectedFeaturePoints(NoseParameterType type) {
    debugPrint('获取受影响特征点: ${type.toString().split('.').last}');
    
    // 这里简化处理，实际应用中应根据参数类型返回对应的特征点
    switch (type) {
      case NoseParameterType.bridgeHeight:
        return [
          FeaturePoint(position: Offset(0.5, 0.3), id: 'nose_bridge_top', isPrimary: true, confidence: 0.95, description: '鼻梁顶部'),
          FeaturePoint(position: Offset(0.5, 0.35), id: 'nose_bridge_middle', isPrimary: true, confidence: 0.95, description: '鼻梁中部'),
          FeaturePoint(position: Offset(0.48, 0.32), id: 'nose_bridge_left', isPrimary: false, confidence: 0.9, description: '鼻梁左侧'),
          FeaturePoint(position: Offset(0.52, 0.32), id: 'nose_bridge_right', isPrimary: false, confidence: 0.9, description: '鼻梁右侧'),
        ];
      case NoseParameterType.bridgeWidth:
        return [
          FeaturePoint(position: Offset(0.48, 0.32), id: 'nose_bridge_left', isPrimary: true, confidence: 0.95, description: '鼻梁左侧'),
          FeaturePoint(position: Offset(0.52, 0.32), id: 'nose_bridge_right', isPrimary: true, confidence: 0.95, description: '鼻梁右侧'),
          FeaturePoint(position: Offset(0.47, 0.34), id: 'nose_bridge_left_lower', isPrimary: false, confidence: 0.9, description: '鼻梁左侧下部'),
          FeaturePoint(position: Offset(0.53, 0.34), id: 'nose_bridge_right_lower', isPrimary: false, confidence: 0.9, description: '鼻梁右侧下部'),
        ];
      case NoseParameterType.tipHeight:
        return [
          FeaturePoint(position: Offset(0.5, 0.4), id: 'nose_tip', isPrimary: true, confidence: 0.95, description: '鼻尖'),
          FeaturePoint(position: Offset(0.5, 0.38), id: 'nose_tip_upper', isPrimary: false, confidence: 0.9, description: '鼻尖上部'),
          FeaturePoint(position: Offset(0.5, 0.42), id: 'nose_tip_lower', isPrimary: false, confidence: 0.9, description: '鼻尖下部'),
        ];
      case NoseParameterType.tipWidth:
        return [
          FeaturePoint(position: Offset(0.48, 0.4), id: 'nose_tip_left', isPrimary: true, confidence: 0.95, description: '鼻尖左侧'),
          FeaturePoint(position: Offset(0.52, 0.4), id: 'nose_tip_right', isPrimary: true, confidence: 0.95, description: '鼻尖右侧'),
          FeaturePoint(position: Offset(0.5, 0.4), id: 'nose_tip', isPrimary: false, confidence: 0.9, description: '鼻尖中心'),
        ];
      case NoseParameterType.tipLength:
        return [
          FeaturePoint(position: Offset(0.5, 0.4), id: 'nose_tip', isPrimary: true, confidence: 0.95, description: '鼻尖'),
          FeaturePoint(position: Offset(0.5, 0.42), id: 'nose_tip_bottom', isPrimary: true, confidence: 0.95, description: '鼻尖底部'),
          FeaturePoint(position: Offset(0.48, 0.41), id: 'nose_tip_left_bottom', isPrimary: false, confidence: 0.9, description: '鼻尖左下'),
          FeaturePoint(position: Offset(0.52, 0.41), id: 'nose_tip_right_bottom', isPrimary: false, confidence: 0.9, description: '鼻尖右下'),
        ];
      case NoseParameterType.nostrilSize:
        return [
          FeaturePoint(position: Offset(0.47, 0.43), id: 'left_nostril', isPrimary: true, confidence: 0.95, description: '左鼻孔'),
          FeaturePoint(position: Offset(0.53, 0.43), id: 'right_nostril', isPrimary: true, confidence: 0.95, description: '右鼻孔'),
          FeaturePoint(position: Offset(0.46, 0.44), id: 'left_nostril_outer', isPrimary: false, confidence: 0.9, description: '左鼻孔外侧'),
          FeaturePoint(position: Offset(0.54, 0.44), id: 'right_nostril_outer', isPrimary: false, confidence: 0.9, description: '右鼻孔外侧'),
          FeaturePoint(position: Offset(0.48, 0.44), id: 'left_nostril_inner', isPrimary: false, confidence: 0.9, description: '左鼻孔内侧'),
          FeaturePoint(position: Offset(0.52, 0.44), id: 'right_nostril_inner', isPrimary: false, confidence: 0.9, description: '右鼻孔内侧'),
        ];
      default:
        debugPrint('未知参数类型: $type');
        return [];
    }
  }
  
  /// 应用变形
  Future<String?> applyTransformation() async {
    if (_currentImagePath == null) {
      _logger.warning(
        module: _logTag,
        function: '应用变形',
        operation: OperationType.process,
        message: '图像路径为空'
      );
      return null;
    }
    
    _isProcessing = true;
    notifyListeners();
    
    _logger.info(
      module: _logTag,
      function: '应用变形',
      operation: OperationType.start,
      message: '开始应用变形 | 图像路径: $_currentImagePath'
    );
    
    try {
      // 启动性能监控
      final stopwatch = _performanceMonitor.startTimer();
      
      // 在测试环境中，创建一个模拟的输出路径
      if (Platform.environment.containsKey('FLUTTER_TEST') || kDebugMode) {
        _logger.info(
          module: _logTag,
          function: '应用变形',
          operation: OperationType.test,
          message: '测试环境中使用模拟数据'
        );
        
        // 生成测试输出路径
        final inputFile = File(_currentImagePath!);
        final inputFileName = inputFile.path.split('/').last;
        final outputFileName = 'transformed_${inputFileName}';
        final outputPath = '${inputFile.parent.path}/$outputFileName';
        
        // 复制输入文件到输出路径
        await inputFile.copy(outputPath);
        
        _logger.info(
          module: _logTag,
          function: '应用变形',
          operation: OperationType.test,
          message: '变形应用完成 | 输出路径: $outputPath'
        );
        
        // 更新当前图像路径
        _currentImagePath = outputPath;
        
        // 更新预览图像
        await triggerPreview();
        
        // 更新医学建议
        await _updateMedicalAdvice();
        
        _isProcessing = false;
        notifyListeners();
        
        return outputPath;
      }
      
      // 应用变形
      final outputPath = await _noseTransformationService.applyNoseTransformation(
        imagePath: _currentImagePath!,
        parameters: _parameters,
      );
      
      // 停止性能监控
      _performanceMonitor.stopTimer(
        stopwatch: stopwatch,
        category: 'noseTransformation',
        operation: 'applyTransformation',
        data: {'inputPath': _currentImagePath, 'outputPath': outputPath},
      );
      
      _logger.info(
        module: _logTag,
        function: '应用变形',
        operation: OperationType.process,
        message: '变形应用完成 | 输出路径: $outputPath'
      );
      
      // 更新当前图像路径
      _currentImagePath = outputPath;
      
      // 更新预览图像
      await _previewTransformation();
      
      // 更新医学建议
      await _updateMedicalAdvice();
      
      _isProcessing = false;
      notifyListeners();
      
      return outputPath;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '应用变形',
        operation: OperationType.process,
        message: '变形应用失败: $e'
      );
      
      _isProcessing = false;
      notifyListeners();
      
      return null;
    }
  }
  
  /// 预览变形
  Future<Uint8List?> _previewTransformation() async {
    if (_currentImagePath == null) {
      _logger.warning(
        module: _logTag,
        function: '预览变形',
        operation: OperationType.process,
        message: '图像路径为空'
      );
      return null;
    }
    
    debugPrint('===== 预览更新事件 =====');
    debugPrint('区域: 鼻部');
    debugPrint('操作: 更新预览图像');
    debugPrint('图像路径: ${_currentImagePath!.split('/').last}');
    
    _logger.info(
      module: _logTag,
      function: '预览变形',
      operation: OperationType.start,
      message: '开始预览变形 | 图像路径: $_currentImagePath | 参数: ${_parameters?.toSimpleMap()}'
    );
    
    try {
      // 启动性能监控
      final stopwatch = _performanceMonitor.startTimer();
      
      // 预览变形
      final previewData = await _noseTransformationService.previewNoseTransformation(
        imagePath: _currentImagePath!,
        parameters: _parameters,
      );
      
      // 停止性能监控
      _performanceMonitor.stopTimer(
        stopwatch: stopwatch,
        category: 'noseTransformation',
        operation: 'previewTransformation',
        data: {'imagePath': _currentImagePath},
      );
      
      _previewImageData = previewData;
      
      // 计算性能指标
      final elapsedTime = stopwatch.elapsedMilliseconds;
      final dataSize = previewData.length / 1024; // 转换为KB
      
      debugPrint('预览完成: ${elapsedTime}ms');
      debugPrint('数据大小: ${dataSize.toStringAsFixed(2)}KB');
      debugPrint('===========================');
      
      _logger.info(
        module: _logTag,
        function: '预览变形',
        operation: OperationType.end,
        message: '预览完成 | 数据大小: ${previewData.length} 字节 | 耗时: ${elapsedTime}ms'
      );
      
      // 确保通知监听器更新UI
      notifyListeners();
      
      return previewData;
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '预览变形',
        operation: OperationType.error,
        message: '预览失败: $e'
      );
      
      return null;
    }
  }
  
  /// 触发预览变形（用于测试）
  Future<Uint8List?> triggerPreview() async {
    if (_currentImagePath == null) {
      return null;
    }
    
    try {
      // 在测试环境中，创建一个模拟的预览图像数据
      if (Platform.environment.containsKey('FLUTTER_TEST') || kDebugMode) {
        _logger.info(
          module: _logTag,
          function: '触发预览',
          operation: OperationType.test,
          message: '测试环境中使用模拟数据'
        );
        
        // 创建一个简单的模拟图像数据
        final mockData = Uint8List.fromList(List.generate(100, (index) => index % 256));
        _previewImageData = mockData;
        
        _logger.info(
          module: _logTag,
          function: '触发预览',
          operation: OperationType.test,
          message: '创建模拟预览数据成功 | 数据大小: ${mockData.length} 字节'
        );
        
        notifyListeners();
        return _previewImageData;
      }
      
      // 正常环境中，调用预览变形方法
      return _previewTransformation();
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '触发预览',
        operation: OperationType.test,
        message: '触发预览失败: $e'
      );
      return null;
    }
  }
  
  /// 获取指定类型的参数
  NoseParameterValue? getParameterValue(NoseParameterType type) {
    return _parameters?.getParameter(type);
  }
  
  /// 获取鼻部特征点
  Future<List<FeaturePoint>?> getNoseFeaturePoints() async {
    if (_currentImagePath == null) {
      _logger.info(
        module: _logTag,
        function: '获取鼻部特征点',
        operation: OperationType.process,
        message: '图像路径为空'
      );
      return null;
    }
    
    _logger.info(
      module: _logTag,
      function: '获取鼻部特征点',
      operation: OperationType.process,
      message: '开始获取鼻部特征点 | 图像路径: $_currentImagePath'
    );
    
    try {
      // 启动性能监控
      final stopwatch = _performanceMonitor.startTimer();
      
      // 在测试环境中，创建一个模拟的特征点列表
      if (Platform.environment.containsKey('FLUTTER_TEST') || kDebugMode) {
        _logger.info(
          module: _logTag,
          function: '获取鼻部特征点',
          operation: OperationType.process,
          message: '测试环境中使用模拟数据'
        );
        
        // 创建模拟的MediaPipe特征点列表
        final mockMediaPipePoints = List.generate(
          500, // MediaPipe通常有468个特征点
          (index) => FeaturePoint(
            position: Offset(index.toDouble(), index.toDouble()),
            confidence: 0.9,
            id: index.toString(),
          ),
        );
        
        // 获取默认映射
        final mapping = NoseFeaturePoints.createDefaultMediaPipeMapping();
        
        // 从模拟数据创建鼻部特征点
        final nosePoints = NoseFeaturePoints.fromMediaPipePoints(
          allFeaturePoints: mockMediaPipePoints,
          mediapipeToNoseMap: mapping,
        );
        
        // 停止性能监控
        _performanceMonitor.stopTimer(
          stopwatch: stopwatch,
          category: 'noseTransformation',
          operation: 'getNoseFeaturePoints',
          data: {'imagePath': _currentImagePath, 'mode': 'mock'},
        );
        
        _logger.info(
          module: _logTag,
          function: '获取鼻部特征点',
          operation: OperationType.process,
          message: '模拟特征点获取完成 | 特征点数量: ${nosePoints.getAllPoints().length}'
        );
        
        return nosePoints.getAllPoints();
      }
      
      // TODO: 实际环境中调用服务获取特征点
      // final featurePoints = await _noseTransformationService.getNoseFeaturePoints(_currentImagePath!);
      
      // 临时创建一些模拟数据
      final mockMediaPipePoints = List.generate(
        500, // MediaPipe通常有468个特征点
        (index) => FeaturePoint(
          position: Offset(index.toDouble(), index.toDouble()),
          confidence: 0.9,
          id: index.toString(),
        ),
      );
      
      // 获取默认映射
      final mapping = NoseFeaturePoints.createDefaultMediaPipeMapping();
      
      // 从模拟数据创建鼻部特征点
      final nosePoints = NoseFeaturePoints.fromMediaPipePoints(
        allFeaturePoints: mockMediaPipePoints,
        mediapipeToNoseMap: mapping,
      );
      
      // 停止性能监控
      _performanceMonitor.stopTimer(
        stopwatch: stopwatch,
        category: 'noseTransformation',
        operation: 'getNoseFeaturePoints',
        data: {'imagePath': _currentImagePath},
      );
      
      _logger.info(
        module: _logTag,
        function: '获取鼻部特征点',
        operation: OperationType.process,
        message: '特征点获取完成 | 特征点数量: ${nosePoints.getAllPoints().length}'
      );
      
      return nosePoints.getAllPoints();
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '获取鼻部特征点',
        operation: OperationType.process,
        message: '特征点获取失败: $e'
      );
      
      return null;
    }
  }
  
  /// 选择特征点
  void selectFeaturePoint(String featurePointId) {
    BeautifyLogger.logInfo(_logTag, 'selectFeaturePoint', '选择特征点: $featurePointId');
    _selectedFeaturePointId = featurePointId;
    
    // 根据特征点更新参数
    _updateParametersBasedOnFeaturePoint(featurePointId);
    
    // 触发预览更新
    triggerPreview();
    
    // 更新医学建议
    _updateMedicalAdvice();
    
    // 通知监听器
    notifyListeners();
  }
  
  /// 根据特征点更新参数
  void _updateParametersBasedOnFeaturePoint(String featurePointId) {
    BeautifyLogger.logInfo(_logTag, '_updateParametersBasedOnFeaturePoint', '根据特征点更新参数: $featurePointId');
    
    // 根据特征点ID更新相应的参数
    // 这里需要根据实际的特征点ID和参数映射关系进行实现
    switch (featurePointId) {
      case 'nose_tip':
        _parameters = _parameters.copyWith(
          tipHeight: _parameters.tipHeight + 0.1,
        );
        break;
      case 'nose_bridge':
        _parameters = _parameters.copyWith(
          bridgeHeight: _parameters.bridgeHeight + 0.1,
        );
        break;
      case 'nose_width':
        _parameters = _parameters.copyWith(
          width: _parameters.width - 0.1,
        );
        break;
      case 'nostril':
        _parameters = _parameters.copyWith(
          nostrilSize: _parameters.nostrilSize - 0.1,
        );
        break;
      default:
        // 对于未知的特征点ID，不做任何参数更新
        BeautifyLogger.logWarning(_logTag, '_updateParametersBasedOnFeaturePoint', '未知的特征点ID: $featurePointId');
        break;
    }
    
    BeautifyLogger.logInfo(_logTag, '_updateParametersBasedOnFeaturePoint', '参数更新完成: $_parameters');
  }
  
  /// 更新医学建议
  Future<void> _updateMedicalAdvice() async {
    debugPrint('===== 医学建议更新事件 =====');
    debugPrint('区域: 鼻部');
    debugPrint('操作: 更新医学建议');
    
    BeautifyLogger.logInfo(_logTag, '_updateMedicalAdvice', '开始更新医学建议');
    
    try {
      // 获取超过医学建议阈值的参数
      final exceedingParams = _parameters.getParametersExceedingMedicalAdvice();
      
      debugPrint('超过阈值的参数数量: ${exceedingParams.length}');
      if (exceedingParams.isNotEmpty) {
        debugPrint('超过阈值的参数: ${exceedingParams.map((p) => p.toString().split('.').last).join(', ')}');
      }
      
      BeautifyLogger.logInfo(_logTag, '_updateMedicalAdvice', '超过阈值的参数: $exceedingParams');
      
      // 如果没有参数超过阈值，则清空医学建议
      if (exceedingParams.isEmpty) {
        _medicalAdvice = [];
        debugPrint('医学建议: 无');
        BeautifyLogger.logInfo(_logTag, '_updateMedicalAdvice', '没有参数超过医学建议阈值，清空医学建议');
        notifyListeners();
        return;
      }
      
      // 为每个超过阈值的参数创建医学建议
      _medicalAdvice = exceedingParams.map((param) {
        final paramType = param.toString().split('.').last;
        final paramValue = _parameters.getParameter(param);
        if (paramValue == null) {
          return '参数 $paramType 不存在';
        }
        
        // 获取受影响的特征点
        final affectedPoints = _getAffectedFeaturePoints(param);
        final primaryPointsCount = affectedPoints.where((p) => p.isPrimary).length;
        
        switch (param) {
          case NoseParameterType.bridgeHeight:
            return '鼻梁高度 (${paramValue.value.toStringAsFixed(2)}) 过高，可能导致不自然的效果。建议适当降低或考虑其他选项。影响${primaryPointsCount}个主要特征点。';
          case NoseParameterType.bridgeWidth:
            return '鼻梁宽度 (${paramValue.value.toStringAsFixed(2)}) 过宽，可能导致面部比例失调。建议适当减小或结合其他参数调整。影响${primaryPointsCount}个主要特征点。';
          case NoseParameterType.tipHeight:
            return '鼻尖高度 (${paramValue.value.toStringAsFixed(2)}) 过高，可能导致不自然的效果。建议适当降低或考虑其他选项。影响${primaryPointsCount}个主要特征点。';
          case NoseParameterType.tipWidth:
            return '鼻尖宽度 (${paramValue.value.toStringAsFixed(2)}) 过宽，可能导致面部比例失调。建议适当减小或结合其他参数调整。影响${primaryPointsCount}个主要特征点。';
          case NoseParameterType.tipLength:
            return '鼻尖长度 (${paramValue.value.toStringAsFixed(2)}) 过长，可能导致不协调的面部比例。建议适当减小或结合其他参数调整。影响${primaryPointsCount}个主要特征点。';
          case NoseParameterType.nostrilSize:
            return '鼻孔大小 (${paramValue.value.toStringAsFixed(2)}) 过大，可能导致不自然的效果。建议适当减小或考虑其他选项。影响${primaryPointsCount}个主要特征点。';
          default:
            return '参数 $paramType 的值 (${paramValue.value.toStringAsFixed(2)}) 超过了医学建议阈值，可能导致不自然的效果。影响${primaryPointsCount}个主要特征点。';
        }
      }).toList();
      
      BeautifyLogger.logInfo(_logTag, '_updateMedicalAdvice', '更新医学建议完成 | 建议数量: ${_medicalAdvice.length} | 建议内容: $_medicalAdvice');
      
      notifyListeners();
    } catch (e) {
      BeautifyLogger.logError(_logTag, '_updateMedicalAdvice', '更新医学建议失败: $e');
      _medicalAdvice = ['无法生成医学建议，请稍后再试。'];
      notifyListeners();
    }
  }
  
  /// 获取特征点描述信息
  String? getFeaturePointDescription(String featurePointId) {
    BeautifyLogger.logInfo(_logTag, 'getFeaturePointDescription', '获取特征点描述: $featurePointId');
    
    // 根据特征点ID返回相应的描述信息
    switch (featurePointId) {
      case 'nose_tip':
        return '鼻尖 - 调整可改变鼻尖高度和形状';
      case 'nose_bridge':
        return '鼻梁 - 调整可改变鼻梁高度和形状';
      case 'nose_width':
        return '鼻翼宽度 - 调整可改变鼻子的宽度';
      case 'nostril':
        return '鼻孔 - 调整可改变鼻孔大小和形状';
      default:
        BeautifyLogger.logWarning(_logTag, 'getFeaturePointDescription', '未知的特征点ID: $featurePointId');
        return null;
    }
  }
  
  /// 获取特征点相关的医学建议
  List<String> getFeaturePointMedicalAdvice(String featurePointId) {
    BeautifyLogger.logInfo(_logTag, 'getFeaturePointMedicalAdvice', '获取特征点医学建议: $featurePointId');
    
    // 根据特征点ID返回相应的医学建议
    switch (featurePointId) {
      case 'nose_tip':
        return [
          '鼻尖调整应适度，过度调整可能导致不自然的外观',
          '鼻尖手术恢复期通常为2-3周',
          '术后可能出现短暂的肿胀和瘀伤'
        ];
      case 'nose_bridge':
        return [
          '鼻梁高度调整应考虑整体面部比例',
          '鼻梁手术恢复期通常为3-4周',
          '术后需避免剧烈运动和佩戴眼镜'
        ];
      case 'nose_width':
        return [
          '鼻翼缩窄手术应考虑呼吸功能',
          '过度缩窄可能影响呼吸通畅',
          '术后恢复期通常为2-3周'
        ];
      case 'nostril':
        return [
          '鼻孔形状调整应保持左右对称',
          '术后可能需要特殊护理以维持形状',
          '恢复期通常为1-2周'
        ];
      default:
        BeautifyLogger.logWarning(_logTag, 'getFeaturePointMedicalAdvice', '未知的特征点ID: $featurePointId');
        return [];
    }
  }
  
  /// 重置所有参数
  Future<void> resetAllParameters() async {
    _logger.info(
      module: _logTag,
      function: '重置参数',
      operation: OperationType.process,
      message: '重置所有参数'
    );
    
    // 重置参数
    _parameters = NoseTransformationParameters.createDefault();
    
    // 清空医学建议
    _medicalAdvice = [];
    
    // 清空预览数据
    _previewImageData = null;
    
    // 同步重置变形服务中的参数
    _transformationService.resetAllTransformations();
    
    notifyListeners();
  }
  
  /// 释放资源
  Future<void> dispose() async {
    _logger.info(
      module: _logTag,
      function: '释放资源',
      operation: OperationType.dispose,
      message: '开始释放鼻部变形集成服务资源'
    );
    
    try {
      // 启动性能监控
      final stopwatch = _performanceMonitor.startTimer();
      
      // 释放鼻部变形服务资源
      await _noseTransformationService.dispose();
      
      // 停止性能监控
      _performanceMonitor.stopTimer(
        stopwatch: stopwatch,
        category: 'noseTransformation',
        operation: 'dispose',
        data: {},
      );
      
      _logger.info(
        module: _logTag,
        function: '释放资源',
        operation: OperationType.dispose,
        message: '鼻部变形集成服务资源释放完成'
      );
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: '释放资源',
        operation: OperationType.dispose,
        message: '鼻部变形集成服务资源释放失败: $e'
      );
    }
    
    super.dispose();
  }
  
  /// 更新特征点高亮状态
  void updateFeaturePointsHighlight() {
    try {
      // 获取当前选中的区域
      final transformationService = TransformationService.getInstance();
      final selectedArea = transformationService.selectedArea;
      
      BeautifyLogger.logInfo(_logTag, 'updateFeaturePointsHighlight', '✅ [特征点高亮] 更新特征点高亮状态');
      BeautifyLogger.logInfo(_logTag, 'updateFeaturePointsHighlight', '   选中区域: $selectedArea');
      BeautifyLogger.logInfo(_logTag, 'updateFeaturePointsHighlight', '   操作: 强制更新特征点高亮');
      
      // 通知监听器，触发UI更新
      notifyListeners();
      
      // 如果有特征点动画控制器，直接调用更新方法
      if (_featurePointsAnimationController != null) {
        _featurePointsAnimationController!.updateFeaturePoints();
        BeautifyLogger.logInfo(_logTag, 'updateFeaturePointsHighlight', '   状态: 已调用特征点动画控制器更新方法');
      } else {
        BeautifyLogger.logWarning(_logTag, 'updateFeaturePointsHighlight', '   警告: 特征点动画控制器未初始化');
      }
    } catch (e) {
      _logger.error(
        module: _logTag,
        function: 'updateFeaturePointsHighlight',
        operation: OperationType.update,
        message: '更新特征点高亮状态失败: $e'
      );
      BeautifyLogger.logError(_logTag, 'updateFeaturePointsHighlight', '❌ [特征点高亮] 更新失败: $e');
    }
  }
  
  /// 设置特征点动画控制器
  void setFeaturePointsAnimationController(animation.FeaturePointsAnimation controller) {
    _logger.info(
      module: _logTag,
      function: 'setFeaturePointsAnimationController',
      operation: OperationType.initialize,
      message: '设置特征点动画控制器'
    );
    
    _featurePointsAnimationController = controller;
    BeautifyLogger.logInfo(_logTag, 'setFeaturePointsAnimationController', '✅ [特征点动画] 控制器设置成功');
    BeautifyLogger.logInfo(_logTag, 'setFeaturePointsAnimationController', '   控制器类型: ${controller.runtimeType}');
    BeautifyLogger.logInfo(_logTag, 'setFeaturePointsAnimationController', '   显示尺寸: ${controller.displaySize}');
    
    // 立即更新特征点高亮状态
    updateFeaturePointsHighlight();
  }
}
