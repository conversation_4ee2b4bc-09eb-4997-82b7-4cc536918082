import 'dart:math' show Point, sqrt, pow, min;
import '../models/facial_feature_mapping.dart';

class FacialFeatureMappingService {
  // 特征点到区域的多重映射
  final Map<int, Map<String, PointRole>> _pointToRegionMapping = {};
  
  // 区域定义映射
  final Map<String, FacialRegion> _regions = {};

  // 容差配置
  static const double _baseToleranceDistance = 2.0;  // 基础容差（毫米）
  static const double _maxToleranceFactor = 0.08;    // 最大容差因子

  FacialFeatureMappingService() {
    _initializeMapping();
  }

  void _initializeMapping() {
    // 初始化每个区域的映射
    RegionConfig.primaryPoints.forEach((regionName, points) {
      var regionPoints = <int, PointRole>{};
      
      // 添加主要特征点
      for (var point in points) {
        regionPoints[point] = PointRole.primary;
        _addToPointMapping(point, regionName, PointRole.primary);
      }

      // 添加次要特征点
      var secondaryPoints = RegionConfig.secondaryPoints[regionName] ?? [];
      for (var point in secondaryPoints) {
        regionPoints[point] = PointRole.secondary;
        _addToPointMapping(point, regionName, PointRole.secondary);
      }

      // 创建区域定义
      _regions[regionName] = FacialRegion(
        name: regionName,
        points: regionPoints,
        symmetryPairs: RegionConfig.symmetryPairs[regionName] ?? [],
      );
    });

    // 处理共享点
    RegionConfig.sharedPoints.forEach((regionName, sharedConfig) {
      sharedConfig.forEach((sharedRegion, points) {
        for (var point in points) {
          _addToPointMapping(point, sharedRegion, PointRole.auxiliary);
        }
      });
    });
  }

  void _addToPointMapping(int pointIndex, String regionName, PointRole role) {
    _pointToRegionMapping.putIfAbsent(pointIndex, () => {});
    _pointToRegionMapping[pointIndex]![regionName] = role;
  }

  /// 获取特征点在特定区域中的角色
  PointRole getPointRole(int pointIndex, String regionName) {
    return _pointToRegionMapping[pointIndex]?[regionName] ?? PointRole.auxiliary;
  }

  /// 获取特征点所属的所有区域
  List<String> getPointRegions(int pointIndex) {
    return _pointToRegionMapping[pointIndex]?.keys.toList() ?? [];
  }

  /// 检查特征点对是否在容差范围内
  bool isWithinTolerance(Point<double> p1, Point<double> p2, {double? customTolerance}) {
    double distance = _calculateDistance(p1, p2);
    double tolerance = customTolerance ?? _calculateDynamicTolerance(distance);
    return distance <= tolerance;
  }

  /// 计算两点之间的距离
  double _calculateDistance(Point<double> p1, Point<double> p2) {
    return sqrt(pow(p2.x - p1.x, 2) + pow(p2.y - p1.y, 2));
  }

  /// 计算动态容差
  double _calculateDynamicTolerance(double distance) {
    double dynamicFactor = min(distance * 0.05, _maxToleranceFactor);
    return _baseToleranceDistance * (1 + dynamicFactor);
  }

  /// 检查区域对称性
  Map<String, bool> checkRegionSymmetry(String regionName, List<Point<double>> points) {
    var results = <String, bool>{};
    var symmetryPairs = _regions[regionName]?.symmetryPairs ?? [];
    
    for (var pair in symmetryPairs) {
      if (pair.length == 2) {
        var p1 = points[pair[0]];
        var p2 = points[pair[1]];
        results['${pair[0]}-${pair[1]}'] = isWithinTolerance(p1, p2);
      }
    }
    
    return results;
  }

  /// 获取区域的所有特征点
  List<int> getRegionPoints(String regionName, {PointRole? role}) {
    var region = _regions[regionName];
    if (region == null) return [];

    if (role == null) {
      return region.points.keys.toList();
    }

    return region.points.entries
        .where((entry) => entry.value == role)
        .map((entry) => entry.key)
        .toList();
  }

  /// 判断特征点是否为主要特征点
  bool isPrimaryPoint(int pointIndex, String regionName) {
    return getPointRole(pointIndex, regionName) == PointRole.primary;
  }

  /// 获取特征点的所有角色
  Map<String, PointRole> getPointAllRoles(int pointIndex) {
    return Map<String, PointRole>.from(_pointToRegionMapping[pointIndex] ?? {});
  }
}
