import 'dart:math';
import 'package:flutter/material.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/core/feature_points_helper.dart';
import 'package:beautifun/beautify_feature/services/transformation/feature_points_handler.dart';
import 'package:beautifun/core/deformation_direction_handler.dart';

/// 区域变形处理器
/// 
/// 负责处理不同区域的变形参数
class AreaTransformationHandler {
  // 单例实例
  static final AreaTransformationHandler _instance = AreaTransformationHandler._internal();
  
  // 工厂构造函数
  factory AreaTransformationHandler() => _instance;
  
  // 内部构造函数
  AreaTransformationHandler._internal() {
    Logger.log('AreaTransformationHandler', '_internal', '🔄 [初始化] 区域变形处理器初始化');
  }
  
  // 特征点处理器
  final _featurePointsHandler = FeaturePointsHandler();
  
  // 变形方向处理器
  final _directionHandler = DeformationDirectionHandler();
  
  /// 获取区域类型
  String getFeatureAreaType(String area) {
    final normalizedArea = _normalizeAreaName(area);
    switch (normalizedArea) {
      case 'face_contour':
        return 'face';
      case 'eyes':
        return 'eyes';
      case 'nose':
        return 'nose';
      case 'lips':
        return 'lips';
      case 'anti_aging':
        return 'anti_aging';
      default:
        return '';
    }
  }
  
  /// 标准化区域名称
  String _normalizeAreaName(String area) {
    switch (area.toLowerCase()) {
      case 'face':
      case 'face_contour':
      case 'facecontour':
        return 'face_contour';
      case 'eye':
      case 'eyes':
        return 'eyes';
      case 'nose':
        return 'nose';
      case 'lip':
      case 'lips':
        return 'lips';
      case 'anti_aging':
      case 'antiaging':
        return 'anti_aging';
      default:
        return '';
    }
  }
  
  /// 计算鼻子区域变形向量
  Offset calculateNoseDeformation(
    String parameter,
    double paramValue,
    int pointId,
    Map<String, dynamic> point,
    List<Map<String, dynamic>> featurePoints
  ) {
    Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
      '🔍 [开始] 计算鼻子变形 | 参数: $parameter | 值: $paramValue | 点ID: $pointId');
    
    // 获取鼻尖点作为中心参考点
    Map<String, dynamic> tipPoint = <String, dynamic>{};
    
    // 先尝试获取ID为1的鼻尖点
    try {
      tipPoint = featurePoints.firstWhere(
        (p) => p['id'] == 1 || p['id'].toString() == '1',
        orElse: () => <String, dynamic>{},
      );
    } catch (e) {
      Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
        '⚠️ [警告] 找不到ID为1的鼻尖点: $e');
    }
    
    // 如果找不到ID为1的鼻尖点，尝试获取ID为19的鼻尖点
    if (tipPoint.isEmpty) {
      try {
        tipPoint = featurePoints.firstWhere(
          (p) => p['id'] == 19 || p['id'].toString() == '19',
          orElse: () => <String, dynamic>{},
        );
        
        if (tipPoint.isNotEmpty) {
          Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
            '✅ [成功] 使用替代鼻尖点(ID=19): ${tipPoint['id']}');
        }
      } catch (e) {
        Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
          '⚠️ [警告] 找不到ID为19的鼻尖点: $e');
      }
    } else {
      Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
        '✅ [成功] 找到鼻尖点(ID=1): ${tipPoint['id']}');
    }
    
    // 如果仍然找不到鼻尖点，使用默认值
    if (tipPoint.isEmpty) {
      tipPoint = {'id': 1, 'x': 0, 'y': 0};
      Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
        '⚠️ [警告] 使用默认鼻尖点坐标');
    }
    
    // 从特征点数据中提取x坐标
    double tipX = (tipPoint['x'] as num).toDouble();
    double currentX = (point['x'] as num).toDouble();
    
    // 判断点在左侧还是右侧
    bool isLeftSide = currentX < tipX;
    
    Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
      '🔍 [计算] 坐标判断 | 鼻尖X: $tipX | 当前X: $currentX | 左侧: $isLeftSide');
    
    // 使用变形方向处理器计算变形向量
    Offset deformation;
    
    switch (parameter) {
      case 'nostril_width':
        // 鼻翼宽度：使用专门的鼻翼宽度变形计算
        // 根据记忆中的信息，保持参数值的原始正负性，不需要反转
        double effectiveParamValue = paramValue;
        
        Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
          '🔍 [调试] 鼻翼宽度变形参数处理 | 原始值: $paramValue | ' +
          '有效值: $effectiveParamValue | 点ID: $pointId | 左侧: $isLeftSide');
        
        // 增加变形强度，确保效果更明显
        double intensity = 20.0; // 从15.0增加到20.0
        
        // 如果参数值为0，返回零向量
        if (effectiveParamValue == 0) {
          Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
            '⚠️ [跳过] 鼻翼宽度参数值为0，返回零向量');
          deformation = Offset.zero;
          break;
        }
        
        // 检查参数值是否太小
        if (effectiveParamValue.abs() < 0.1) {
          // 如果参数值很小，放大参数值以确保效果可见
          double amplifiedValue = effectiveParamValue < 0 ? -0.3 : 0.3;
          Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
            '💡 [增强] 放大鼻翼宽度参数值 | 原始值: $effectiveParamValue | 放大值: $amplifiedValue');
          effectiveParamValue = amplifiedValue;
        }
        
        // 记录变形计算前的详细信息
        Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
          '🔍 [详细] 调用变形方向处理器计算鼻翼宽度变形 | ' +
          '参数值: $effectiveParamValue | 左侧: $isLeftSide | 强度: $intensity');
        
        // 调用变形方向处理器计算变形向量
        deformation = _directionHandler.calculateNostrilWidthDeformation(
          effectiveParamValue, isLeftSide, intensity: intensity
        );
        
        // 检查变形向量是否太小
        double magnitude = sqrt(deformation.dx * deformation.dx + deformation.dy * deformation.dy);
        Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
          '📊 [统计] 计算得到的变形向量 | 向量: $deformation | 大小: $magnitude');
        
        // 如果变形向量太小，强制设置最小变形量
        if (magnitude < 0.5) {
          // 设置一个足够大的最小变形量，确保效果可见
          double minDeformation = 1.0;
          
          // 根据鼻翼宽度变形的对称性原则计算变形方向
          double dx = 0.0;
          
          if (effectiveParamValue < 0) { // 缩小鼻翼
            // 左侧点向右移动（向中心），右侧点向左移动（向中心）
            dx = isLeftSide ? minDeformation : -minDeformation;
          } else { // 增宽鼻翼
            // 左侧点向左移动（远离中心），右侧点向右移动（远离中心）
            dx = isLeftSide ? -minDeformation : minDeformation;
          }
          
          deformation = Offset(dx, 0);
          
          Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
            '💡 [增强] 强制设置最小变形向量 | 原始大小: $magnitude | ' +
            '新向量: $deformation | 参数值: $effectiveParamValue | 左侧: $isLeftSide');
        }
        
        // 验证变形向量的正确性
        bool isCorrectDirection = true;
        String expectedDirection = '';
        
        if (effectiveParamValue < 0) { // 缩小鼻翼
          // 左侧点应该向右（dx > 0），右侧点应该向左（dx < 0）
          isCorrectDirection = (isLeftSide && deformation.dx > 0) || (!isLeftSide && deformation.dx < 0);
          expectedDirection = isLeftSide ? '向右(向中心)' : '向左(向中心)';
        } else { // 增宽鼻翼
          // 左侧点应该向左（dx < 0），右侧点应该向右（dx > 0）
          isCorrectDirection = (isLeftSide && deformation.dx < 0) || (!isLeftSide && deformation.dx > 0);
          expectedDirection = isLeftSide ? '向左(远离中心)' : '向右(远离中心)';
        }
        
        if (!isCorrectDirection) {
          Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
            '⚠️ [警告] 变形方向验证失败! | 参数值: $effectiveParamValue | 左侧: $isLeftSide | ' +
            '期望方向: $expectedDirection | 实际dx: ${deformation.dx}');
          
          // 强制纠正方向
          double correctedDx = 0.0;
          double correctionMagnitude = 1.0; // 足够大的变形量
          
          if (effectiveParamValue < 0) { // 缩小鼻翼
            correctedDx = isLeftSide ? correctionMagnitude : -correctionMagnitude;
          } else { // 增宽鼻翼
            correctedDx = isLeftSide ? -correctionMagnitude : correctionMagnitude;
          }
          
          deformation = Offset(correctedDx, 0);
          
          Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
            '🔧 [纠正] 强制纠正变形方向 | 新向量: $deformation');
        }
        
        // 记录最终的变形向量
        magnitude = sqrt(deformation.dx * deformation.dx + deformation.dy * deformation.dy);
        Logger.log('AreaTransformationHandler', 'calculateNoseDeformation', 
          '✅ [结果] 鼻翼宽度变形: 点ID=$pointId, 参数值=$effectiveParamValue, 左侧=$isLeftSide, ' +
          'dx=${deformation.dx.toStringAsFixed(6)}, dy=${deformation.dy.toStringAsFixed(6)} | 大小=${magnitude.toStringAsFixed(6)}');
        break;
        
      case 'bridge_height':
        // 鼻梁高度：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.upward, intensity: 0.3
        );
        break;
        
      case 'tip_adjust':
        // 鼻尖调整：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.upward, intensity: 0.3
        );
        break;
        
      case 'base_height':
        // 鼻基抬高：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.upward, intensity: 0.3
        );
        break;
        
      default:
        deformation = Offset.zero;
    }
    
    return deformation;
  }
  
  /// 计算眼睛区域变形向量
  Offset calculateEyesDeformation(
    String parameter,
    double paramValue,
    int pointId,
    Map<String, dynamic> point,
    List<Map<String, dynamic>> featurePoints
  ) {
    // 获取鼻梁起始点作为中心参考点
    final centerPoint = featurePoints.firstWhere(
      (p) => p['id'] == 168,  // 鼻梁起始点ID为168
      orElse: () => {'id': 168, 'x': 0, 'y': 0}
    );
    
    // 从特征点数据中提取x坐标
    double centerX = (centerPoint['x'] as num).toDouble();
    double currentX = (point['x'] as num).toDouble();
    
    // 判断点在左侧还是右侧
    bool isLeftSide = currentX < centerX;
    
    // 使用变形方向处理器计算变形向量
    Offset deformation;
    
    switch (parameter) {
      case 'double_fold':
        // 双眼皮：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.upward, intensity: 0.3
        );
        break;
        
      case 'canthal_tilt':
        // 开眼角：混合方向偏移
        deformation = _directionHandler.calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.outward,
          yDirectionType: DeformationDirectionType.upward,
          xIntensity: 0.3,
          yIntensity: 0.3
        );
        break;
        
      case 'eye_bag_removal':
        // 去眼袋：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.upward, intensity: 0.3
        );
        break;
        
      case 'outer_corner_lift':
        // 提眼尾：混合方向偏移
        deformation = _directionHandler.calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.outward,
          yDirectionType: DeformationDirectionType.upward,
          xIntensity: 0.3,
          yIntensity: 0.3
        );
        break;
        
      default:
        deformation = Offset.zero;
    }
    
    return deformation;
  }
  
  /// 计算面部轮廓区域变形向量
  Offset calculateFaceContourDeformation(
    String parameter,
    double paramValue,
    int pointId,
    Map<String, dynamic> point,
    List<Map<String, dynamic>> featurePoints
  ) {
    // 获取面部中心点
    final centerX = _calculateFaceCenterX(featurePoints);
    final currentX = (point['x'] as num).toDouble();
    
    // 判断点在左侧还是右侧
    bool isLeftSide = currentX < centerX;
    
    // 使用变形方向处理器计算变形向量
    Offset deformation;
    
    switch (parameter) {
      case 'contour_tighten':
        // 轮廓收紧：水平方向偏移
        deformation = _directionHandler.calculateHorizontalDeformation(
          paramValue, isLeftSide, DeformationDirectionType.inward, intensity: 0.3
        );
        break;
        
      case 'chin_adjust':
        // 下巴调整：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.downward, intensity: 0.3
        );
        break;
        
      case 'cheekbone_adjust':
        // 颧骨调整：水平方向偏移
        deformation = _directionHandler.calculateHorizontalDeformation(
          paramValue, isLeftSide, DeformationDirectionType.outward, intensity: 0.3
        );
        break;
        
      case 'face_shape':
        // 脸型优化：混合方向偏移
        deformation = _directionHandler.calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.outward,
          yDirectionType: DeformationDirectionType.upward,
          xIntensity: 0.3,
          yIntensity: 0.3
        );
        break;
        
      default:
        deformation = Offset.zero;
    }
    
    return deformation;
  }
  
  /// 计算嘴唇区域变形向量
  Offset calculateLipsDeformation(
    String parameter,
    double paramValue,
    int pointId,
    Map<String, dynamic> point,
    List<Map<String, dynamic>> featurePoints
  ) {
    // 获取嘴唇中心点
    final centerX = _calculateLipsCenterX(featurePoints);
    final currentX = (point['x'] as num).toDouble();
    
    // 判断点在左侧还是右侧
    bool isLeftSide = currentX < centerX;
    
    // 使用变形方向处理器计算变形向量
    Offset deformation;
    
    switch (parameter) {
      case 'lip_shape':
        // 唇形调整：混合方向偏移
        deformation = _directionHandler.calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.outward,
          yDirectionType: DeformationDirectionType.downward,
          xIntensity: 0.3,
          yIntensity: 0.3
        );
        break;
        
      case 'mouth_corner':
        // 嘴角上扬：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.upward, intensity: 0.3
        );
        break;
        
      default:
        deformation = Offset.zero;
    }
    
    return deformation;
  }
  
  /// 计算抗衰老区域变形向量
  Offset calculateAntiAgingDeformation(
    String parameter,
    double paramValue,
    int pointId,
    Map<String, dynamic> point,
    List<Map<String, dynamic>> featurePoints
  ) {
    // 获取面部中心点
    final centerX = _calculateFaceCenterX(featurePoints);
    final currentX = (point['x'] as num).toDouble();
    
    // 判断点在左侧还是右侧
    bool isLeftSide = currentX < centerX;
    
    // 使用变形方向处理器计算变形向量
    Offset deformation;
    
    switch (parameter) {
      case 'nasolabial_folds':
        // 法令纹：混合方向偏移
        deformation = _directionHandler.calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.inward,
          yDirectionType: DeformationDirectionType.upward,
          xIntensity: 0.3,
          yIntensity: 0.3
        );
        break;
        
      case 'wrinkle_removal':
        // 去皱纹：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.upward, intensity: 0.3
        );
        break;
        
      case 'forehead_fullness':
        // 额头饱满：垂直方向偏移
        deformation = _directionHandler.calculateVerticalDeformation(
          paramValue, DeformationDirectionType.upward, intensity: 0.3
        );
        break;
        
      case 'facial_firmness':
        // 面容紧致：混合方向偏移
        deformation = _directionHandler.calculateMixedDeformation(
          paramValue, 
          isLeftSide,
          xDirectionType: DeformationDirectionType.inward,
          yDirectionType: DeformationDirectionType.upward,
          xIntensity: 0.3,
          yIntensity: 0.3
        );
        break;
        
      default:
        deformation = Offset.zero;
    }
    
    return deformation;
  }
  
  /// 计算变形向量
  Offset calculateDeformationVector(
    String area,
    String parameter,
    double paramValue,
    int pointId,
    Map<String, dynamic> point,
    List<Map<String, dynamic>> featurePoints
  ) {
    switch (area) {
      case 'nose':
        return calculateNoseDeformation(parameter, paramValue, pointId, point, featurePoints);
      case 'eyes':
        return calculateEyesDeformation(parameter, paramValue, pointId, point, featurePoints);
      case 'face_contour':
        return calculateFaceContourDeformation(parameter, paramValue, pointId, point, featurePoints);
      case 'lips':
        return calculateLipsDeformation(parameter, paramValue, pointId, point, featurePoints);
      case 'anti_aging':
        return calculateAntiAgingDeformation(parameter, paramValue, pointId, point, featurePoints);
      default:
        return Offset.zero;
    }
  }
  
  /// 计算面部中心点的X坐标
  double _calculateFaceCenterX(List<Map<String, dynamic>> featurePoints) {
    // 尝试获取鼻尖点作为面部中心点
    try {
      // 首先尝试获取ID为1的鼻尖点
      final tipPoint = featurePoints.firstWhere(
        (p) => p['id'] == 1,
        orElse: () => <String, dynamic>{},
      );
      
      if (tipPoint.isNotEmpty) {
        return (tipPoint['x'] as num).toDouble();
      }
      
      // 如果找不到ID为1的鼻尖点，尝试获取ID为19的鼻尖点
      final alternateTipPoint = featurePoints.firstWhere(
        (p) => p['id'] == 19,
        orElse: () => <String, dynamic>{},
      );
      
      if (alternateTipPoint.isNotEmpty) {
        return (alternateTipPoint['x'] as num).toDouble();
      }
      
      // 如果仍然找不到鼻尖点，计算所有点的平均X坐标
      Logger.log('AreaTransformationHandler', '_calculateFaceCenterX', '⚠️ [警告] 找不到鼻尖点，使用平均值');
      double sumX = 0;
      for (final point in featurePoints) {
        sumX += (point['x'] as num).toDouble();
      }
      return sumX / featurePoints.length;
    } catch (e) {
      // 捕获并记录错误
      Logger.log('AreaTransformationHandler', '_calculateFaceCenterX', '❌ [错误] 计算面部中心异常: $e');
      
      // 如果发生异常，返回一个合理的默认值
      return 0.0;
    }
  }
  
  /// 计算嘴唇中心点的X坐标
  double _calculateLipsCenterX(List<Map<String, dynamic>> featurePoints) {
    // 尝试获取上唇中心点
    try {
      // 首先尝试获取ID为13的上唇中心点
      final lipPoint = featurePoints.firstWhere(
        (p) => p['id'] == 13,
        orElse: () => <String, dynamic>{},
      );
      
      if (lipPoint.isNotEmpty) {
        return (lipPoint['x'] as num).toDouble();
      }
      
      // 如果找不到ID为13的上唇中心点，尝试获取ID为61的嘴唇中心点
      final alternateLipPoint = featurePoints.firstWhere(
        (p) => p['id'] == 61,
        orElse: () => <String, dynamic>{},
      );
      
      if (alternateLipPoint.isNotEmpty) {
        return (alternateLipPoint['x'] as num).toDouble();
      }
      
      // 如果仍然找不到嘴唇中心点，尝试使用面部中心点
      Logger.log('AreaTransformationHandler', '_calculateLipsCenterX', '⚠️ [警告] 找不到嘴唇中心点，使用面部中心点');
      return _calculateFaceCenterX(featurePoints);
    } catch (e) {
      // 如果找不到上唇中心点，使用面部中心点
      return _calculateFaceCenterX(featurePoints);
    }
  }
}
