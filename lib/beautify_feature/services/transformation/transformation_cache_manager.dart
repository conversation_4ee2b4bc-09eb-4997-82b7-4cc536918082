import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:beautifun/utils/logger.dart';

/// 变形缓存管理器
/// 
/// 负责管理图像和特征点的缓存，包括全局初始缓存、参数初始缓存和当前状态缓存
class TransformationCacheManager {
  // 单例实例
  static final TransformationCacheManager _instance = TransformationCacheManager._internal();
  
  // 工厂构造函数
  factory TransformationCacheManager() => _instance;
  
  // 内部构造函数
  TransformationCacheManager._internal() {
    _logTime('初始化', '✅ [完成] 变形缓存管理器初始化');
  }
  
  // ==================== 图像缓存 ====================
  
  // 原始图像
  ui.Image? _originalImage;
  ui.Image? get originalImage => _originalImage;
  
  // 当前图像
  ui.Image? _currentImage;
  ui.Image? get currentImage => _currentImage;
  
  // 原始图像路径
  String? _originalImagePath;
  String? get originalImagePath => _originalImagePath;
  
  // 当前图像路径
  String? _currentImagePath;
  String? get currentImagePath => _currentImagePath;
  
  // 变形后的图像路径
  String? _transformedImagePath;
  String? get transformedImagePath => _transformedImagePath;
  
  // 变形后的图像路径（用于累积变形）
  String? _deformedImagePath;
  String? get deformedImagePath => _deformedImagePath;
  
  // 参数初始图像缓存，记录每个参数开始变形时的图像状态
  final Map<String, String> _paramInitialImagePaths = {};
  Map<String, String> get paramInitialImagePaths => _paramInitialImagePaths;
  
  // ==================== 特征点缓存 ====================
  
  // 原始特征点数据
  List<Map<String, dynamic>>? _originalFeaturePoints;
  List<Map<String, dynamic>>? get originalFeaturePoints => _originalFeaturePoints;
  
  // 当前特征点数据
  List<Map<String, dynamic>>? _featurePoints;
  List<Map<String, dynamic>>? get featurePoints => _featurePoints;
  set featurePoints(List<Map<String, dynamic>>? value) {
    _featurePoints = value;
  }
  
  // 变形前特征点数据的备份
  List<Map<String, dynamic>>? _deformationFeaturePointsBackup;
  List<Map<String, dynamic>>? get deformationFeaturePointsBackup => _deformationFeaturePointsBackup;
  
  // 参数状态缓存，记录每个参数开始变形时的特征点状态
  final Map<String, List<Map<String, dynamic>>> _paramInitialFeaturePoints = {};
  Map<String, List<Map<String, dynamic>>> get paramInitialFeaturePoints => _paramInitialFeaturePoints;
  
  // ==================== 缓存管理方法 ====================
  
  /// 获取当前时间戳 HH:MM:SS 格式
  String _getTimeStamp() {
    final now = DateTime.now();
    return '[${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}]';
  }
  
  /// 记录日志
  void _logTime(String method, String message) {
    final timestamp = _getTimeStamp();
    print('$timestamp 📋 缓存管理器 | $method | $message');
  }
  
  /// 设置原始图像
  void setOriginalImage(ui.Image image) {
    _logTime('设置原始图像', '📥 [开始] 设置原始图像');
    
    _originalImage = image;
    _currentImage = image;
    
    _logTime('设置原始图像', '✅ [完成] 原始图像已设置');
  }
  
  /// 设置原始图像路径
  void setOriginalImagePath(String path) {
    _logTime('设置原始图像', '🔍 [开始] 路径: ${path.split('/').last}');
    
    _originalImagePath = path;
    _currentImagePath = path;
    
    _logTime('设置原始图像', '✅ [完成] 路径已设置');
  }
  
  /// 设置变形图像路径
  void setTransformedImagePath(String path) {
    _logTime('设置变形图像', '🔍 [开始] 路径: ${path.split('/').last}');
    
    _transformedImagePath = path;
    _deformedImagePath = path;
    
    _logTime('设置变形图像', '✅ [完成] 路径已设置');
  }
  
  /// 设置原始特征点
  void setOriginalFeaturePoints(List<Map<String, dynamic>> points) {
    _logTime('设置原始点', '📥 [开始] 点数: ${points.length}');
    
    _originalFeaturePoints = points.map((point) => Map<String, dynamic>.from(point)).toList();
    _featurePoints = points.map((point) => Map<String, dynamic>.from(point)).toList();
    
    _logTime('设置原始点', '✅ [完成] 点数: ${points.length}');
  }
  
  /// 备份变形前的特征点
  void backupFeaturePointsBeforeDeformation() {
    if (_featurePoints != null) {
      _logTime('备份变形前点', '📦 [开始] 点数: ${_featurePoints!.length}');
      
      _deformationFeaturePointsBackup = _featurePoints!.map((point) => Map<String, dynamic>.from(point)).toList();
      
      _logTime('备份变形前点', '✅ [完成] 点数: ${_deformationFeaturePointsBackup!.length}');
    } else {
      _logTime('备份变形前点', '❌ [失败] 当前特征点为空');
    }
  }
  
  /// 保存参数初始状态
  void saveParameterInitialState(String paramKey) {
    // 保存当前特征点状态作为该参数的初始状态
    if (_featurePoints != null) {
      _logTime('保存参数状态', '📦 [开始] 参数: $paramKey');
      
      _paramInitialFeaturePoints[paramKey] = _featurePoints!.map((point) => Map<String, dynamic>.from(point)).toList();
      
      _logTime('保存参数状态', '✅ [完成] 参数: $paramKey | 点数: ${_featurePoints!.length}');
    } else {
      _logTime('保存参数状态', '❌ [失败] 当前特征点为空');
    }
    
    // 保存当前图像路径作为该参数的初始图像
    if (_deformedImagePath != null) {
      _paramInitialImagePaths[paramKey] = _deformedImagePath!;
      _logTime('保存参数状态', '📦 [缓存] 图像路径 | 参数: $paramKey');
    } else if (_currentImagePath != null) {
      // 如果没有变形图像路径，使用当前图像路径
      _paramInitialImagePaths[paramKey] = _currentImagePath!;
      _logTime('保存参数状态', '📦 [缓存] 图像路径（使用当前图像） | 参数: $paramKey');
    }
  }
  
  /// 恢复参数初始状态
  bool restoreParameterInitialState(String paramKey) {
    _logTime('恢复参数状态', '🔄 [开始] 参数: $paramKey');
    
    // 如果有该参数的初始特征点缓存，直接恢复
    if (_paramInitialFeaturePoints.containsKey(paramKey)) {
      // 恢复该参数的初始特征点状态
      final initialPoints = _paramInitialFeaturePoints[paramKey]!;
      
      // 更新当前特征点
      _featurePoints = initialPoints.map((point) => Map<String, dynamic>.from(point)).toList();
      
      _logTime('恢复参数状态', '✅ [完成] 参数: $paramKey');
      
      // 恢复该参数的初始图像路径（如果有）
      if (_paramInitialImagePaths.containsKey(paramKey)) {
        final initialImagePath = _paramInitialImagePaths[paramKey]!;
        _deformedImagePath = initialImagePath;
        _transformedImagePath = initialImagePath;
        
        _logTime('恢复参数状态', '🔄 [恢复] 参数初始图像 | 参数: $paramKey | 路径: $initialImagePath');
      }
      
      return true;
    }
    
    return false;
  }
  
  /// 重置所有缓存
  void resetAllCache() {
    _logTime('重置缓存', '🔄 [开始] 原始点: ${_originalFeaturePoints?.length ?? 0} | 参数缓存: ${_paramInitialFeaturePoints.length}项');
    
    // 清空参数初始状态缓存
    _paramInitialFeaturePoints.clear();
    _paramInitialImagePaths.clear();
    
    // 恢复原始特征点数据
    if (_originalFeaturePoints != null && _originalFeaturePoints!.isNotEmpty) {
      _featurePoints = _originalFeaturePoints!.map((point) => Map<String, dynamic>.from(point)).toList();
    }
    
    _logTime('重置缓存', '🧹 [清理] 所有缓存已重置');
  }
  
  /// 加载图像
  Future<ui.Image?> loadImage(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        final bytes = await file.readAsBytes();
        final codec = await ui.instantiateImageCodec(bytes);
        final frame = await codec.getNextFrame();
        return frame.image;
      }
    } catch (e) {
      _logTime('加载图像', '❌ [ERROR] 加载图像失败: $e');
    }
    return null;
  }
  
  /// 加载变形后的图像
  Future<ui.Image?> loadTransformedImage() async {
    if (_transformedImagePath == null) {
      _logTime('加载变形图像', '⚠️ [ERROR] 变形图像路径为空');
      return null;
    }
    
    final filename = _transformedImagePath!.split('/').last;
    _logTime('加载变形图像', '📥 [开始] 加载变形图像: $filename');
    
    try {
      // 确保文件存在
      final file = File(_transformedImagePath!);
      if (!await file.exists()) {
        _logTime('加载变形图像', '⚠️ [ERROR] 变形图像文件不存在: $_transformedImagePath');
        return null;
      }
      
      // 加载图像
      final bytes = await file.readAsBytes();
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      _currentImage = frame.image;
      
      _logTime('加载变形图像', '✅ [完成] 变形图像加载成功: $filename');
      
      return _currentImage;
    } catch (e) {
      _logTime('加载变形图像', '❌ [ERROR] 加载变形图像失败: $e');
      return null;
    }
  }
}
