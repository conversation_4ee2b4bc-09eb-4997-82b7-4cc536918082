import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/utils/feature_points_helper.dart';
import 'package:beautifun/beautify_feature/widgets/landmarks_overlay.dart';
import 'package:beautifun/beautify_feature/services/transformation/transformation_cache_manager.dart';
import 'package:beautifun/beautify_feature/services/transformation/universal_deformation_calculator.dart';
import 'package:beautifun/beautify_feature/services/transformation/image_transformation_handler.dart';

/// 自定义TickerProvider实现
class _TransformationServiceTickerProvider extends TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) {
    return Ticker(onTick);
  }
}

/// 变形效果服务
/// 
/// 负责管理和应用面部变形效果
class TransformationService extends ChangeNotifier {
  // 构造函数
  TransformationService() {
    // 初始化变形参数映射
    _transformationParams = {
      'face_contour': {},
      'eyes': {},
      'nose': {},
      'lips': {},
      'anti_aging': {},
    };
    
    Logger.log('TransformationService', 'constructor', '🔄 [INFO] 初始化服务');
  }
  
  // ==================== 依赖组件 ====================
  
  // 缓存管理器
  final _cacheManager = TransformationCacheManager();
  
  // 通用变形计算器
  final _deformationCalculator = UniversalDeformationCalculator();
  
  // 图像变形处理器
  final _imageHandler = ImageTransformationHandler();
  
  // ==================== 状态变量 ====================
  
  // 选中的区域
  String _selectedArea = '';
  String get selectedArea => _selectedArea;
  
  // 选中的参数
  String? _selectedParameter;
  String? get selectedParameter => _selectedParameter;
  
  // 变形参数映射
  late final Map<String, Map<String, double>> _transformationParams;
  
  // 当前变形数据
  final Map<int, Offset> _currentDeformations = {};
  
  // 变形完成标志
  bool _deformationCompleted = false;
  
  // 是否正在处理
  bool _isProcessing = false;
  bool get isProcessing => _isProcessing;
  
  // 是否正在变形
  bool _isDeforming = false;
  bool get isDeforming => _isDeforming;
  
  // 是否正在加载变形图像
  bool _isLoadingTransformedImage = false;
  
  // 是否显示变形
  bool _isShowingDeformation = false;
  bool get isShowingDeformation => _isShowingDeformation;
  
  // 特征点覆盖层引用
  GlobalKey<LandmarksOverlayState>? _landmarksKey;
  
  // ==================== 公共方法 ====================
  
  /// 获取区域类型
  String _getFeatureAreaType(String area) {
    final normalizedArea = _normalizeAreaName(area);
    switch (normalizedArea) {
      case 'face_contour':
        return 'face';
      case 'eyes':
        return 'eyes';
      case 'nose':
        return 'nose';
      case 'lips':
        return 'lips';
      case 'anti_aging':
        return 'anti_aging';
      default:
        return '';
    }
  }
  
  /// 获取区域对应的特征点
  List<int> _getAreaPoints(String area) {
    final normalizedArea = _normalizeAreaName(area);
    return FeaturePointsHelper().getAreaPoints(normalizedArea);
  }
  
  /// 标准化区域名称
  String _normalizeAreaName(String area) {
    switch (area.toLowerCase()) {
      case 'face':
      case 'face_contour':
      case 'facecontour':
        return 'face_contour';
      case 'eye':
      case 'eyes':
        return 'eyes';
      case 'nose':
        return 'nose';
      case 'lip':
      case 'lips':
        return 'lips';
      case 'anti_aging':
      case 'antiaging':
        return 'anti_aging';
      default:
        return '';
    }
  }
  
  /// 获取区域参数
  Map<String, double> getAreaParams(String area) {
    final normalizedArea = _normalizeAreaName(area);
    if (normalizedArea.isEmpty) {
      return {};
    }
    
    return _transformationParams[normalizedArea] ?? {};
  }
  
  /// 设置原始图像
  void setOriginalImage(ui.Image image) {
    _cacheManager.setOriginalImage(image);
    notifyListeners();
  }
  
  /// 设置特征点数据
  void setFeaturePoints(List<Map<String, dynamic>> featurePoints, {String? imagePath}) {
    // 设置原始特征点
    _cacheManager.setOriginalFeaturePoints(featurePoints);
    
    // 设置图像路径
    if (imagePath != null) {
      _cacheManager.setOriginalImagePath(imagePath);
    }
    
    // 重置变形状态
    resetAllDeformations();
    
    notifyListeners();
  }
  
  /// 设置选中的区域
  void setSelectedArea(String area) {
    final normalizedArea = _normalizeAreaName(area);
    if (normalizedArea.isEmpty) {
      return;
    }
    
    _selectedArea = normalizedArea;
    _selectedParameter = null;
    
    notifyListeners();
    
    Logger.log('TransformationService', 'setSelectedArea', '✅ [INFO] 区域设置完成: $normalizedArea');
  }
  
  /// 更新变形参数
  void updateTransformationParam(String area, String paramName, double value) {
    final normalizedArea = _normalizeAreaName(area);
    if (normalizedArea.isEmpty) {
      return;
    }
    
    // 确保区域参数映射存在
    _transformationParams[normalizedArea] ??= {};
    
    // 获取旧值
    final oldValue = _transformationParams[normalizedArea]![paramName] ?? 0.0;
    
    // 如果值没有变化，跳过处理
    if (oldValue == value) {
      Logger.log('TransformationService', 'updateTransformationParam', 
        '⚠️ [跳过] 参数值未变化 | 区域: $normalizedArea | 参数: $paramName | 值: $value');
      return;
    }
    
    // 生成参数唯一标识符
    final paramKey = '${normalizedArea}_${paramName}';
    
    // 检查是否是从0开始变化（首次激活该参数）
    if (oldValue == 0.0 && value != 0.0) {
      // 保存当前状态作为该参数的初始状态
      _cacheManager.saveParameterInitialState(paramKey);
    }
    
    // 检查是否是回到0（取消该参数的变形效果）
    if (value == 0.0 && oldValue != 0.0) {
      // 如果有该参数的初始状态缓存，直接恢复
      if (_cacheManager.restoreParameterInitialState(paramKey)) {
        // 更新参数值
        _transformationParams[normalizedArea]![paramName] = value;
        
        // 设置选中的区域和参数
        _selectedArea = normalizedArea;
        _selectedParameter = paramName;
        
        // 重置变形完成状态
        _deformationCompleted = false;
        
        // 应用所有非零参数的累积变形
        _applyAllDeformations();
        
        notifyListeners();
        return;
      }
    }
    
    // 更新参数值
    _transformationParams[normalizedArea]![paramName] = value;
    
    // 设置选中的区域和参数
    _selectedArea = normalizedArea;
    _selectedParameter = paramName;
    
    // 重置变形完成状态，允许新的变形处理
    _deformationCompleted = false;
    
    // 应用变形处理
    _applyDeformationToImage();
    
    notifyListeners();
  }
  
  /// 应用所有非零参数的累积变形
  Future<void> _applyAllDeformations() async {
    Logger.log('TransformationService', '_applyAllDeformations', '🔄 [入口] 应用所有非零参数的累积变形');
    
    // 从原始状态开始
    if (_cacheManager.originalFeaturePoints != null) {
      _cacheManager.featurePoints = _cacheManager.originalFeaturePoints!.map((point) => Map<String, dynamic>.from(point)).toList();
    }
    
    // 清空当前变形数据
    _currentDeformations.clear();
    
    // 按区域和参数顺序应用所有非零参数的变形
    for (final area in _transformationParams.keys) {
      final params = _transformationParams[area] ?? {};
      for (final entry in params.entries) {
        final paramName = entry.key;
        final paramValue = entry.value;
        
        // 跳过值为0的参数
        if (paramValue == 0.0) continue;
        
        // 临时设置当前选中的区域和参数
        final oldArea = _selectedArea;
        final oldParam = _selectedParameter;
        
        _selectedArea = area;
        _selectedParameter = paramName;
        
        // 应用变形
        await _applyDeformationToImage();
        
        // 恢复原来选中的区域和参数
        _selectedArea = oldArea;
        _selectedParameter = oldParam;
      }
    }
    
    Logger.log('TransformationService', '_applyAllDeformations', '✅ [完成] 应用所有非零参数的累积变形');
  }
  
  /// 应用变形到图像
  Future<void> _applyDeformationToImage() async {
    final callId = DateTime.now().millisecondsSinceEpoch;
    
    Logger.log('TransformationService', '_applyDeformationToImage', 
      '🔄 [调用ID: $callId] [入口] 开始应用变形 | 区域: $_selectedArea | 参数: $_selectedParameter');
    
    // 设置处理状态
    _isDeforming = true;
    notifyListeners();
    
    try {
      // 检查特征点数据是否有效
      final hasFeaturePoints = _cacheManager.featurePoints != null && _cacheManager.featurePoints!.isNotEmpty;
      if (!hasFeaturePoints) {
        Logger.log('TransformationService', '_applyDeformationToImage', 
          '⚠️ [调用ID: $callId] [跳过] 无特征点数据，跳过处理');
        return;
      }
      
      // 检查图像路径是否有效
      if (_cacheManager.currentImagePath == null || _cacheManager.currentImagePath!.isEmpty) {
        Logger.log('TransformationService', '_applyDeformationToImage', 
          '⚠️ [调用ID: $callId] [跳过] 无当前图像路径，跳过处理');
        return;
      }
      
      // 备份变形前的特征点数据
      _cacheManager.backupFeaturePointsBeforeDeformation();
      
      // 计算变形数据
      _currentDeformations.clear();
      
      // 根据变形参数计算变形数据
      if (_selectedArea.isNotEmpty && _selectedParameter != null) {
        final paramValue = _transformationParams[_selectedArea]![_selectedParameter!] ?? 0.0;
        if (paramValue != 0.0) {
          // 计算变形向量
          _currentDeformations.addAll(
            _deformationCalculator.calculateDeformations(
              _selectedArea,
              _selectedParameter!,
              paramValue,
              _cacheManager.featurePoints!
            )
          );
          
          // 更新特征点数据
          _deformationCalculator.updateFeaturePointsWithDeformations(
            _cacheManager.featurePoints!,
            _currentDeformations
          );
          
          // 调用变形引擎处理图像
          await _imageHandler.applyDeformationToImage(
            _cacheManager.currentImagePath!,
            _currentDeformations,
            _cacheManager.featurePoints!,
            callId: callId.toString()
          );
        }
      }
      
      // 更新特征点覆盖层
      _updateLandmarksOverlay(
        isAnimating: true,
        animationColor: Colors.blue,
        deformations: _currentDeformations,
      );
      
      // 设置变形完成状态
      _deformationCompleted = true;
      
      // 更新UI
      _isShowingDeformation = true;
      notifyListeners();
      
      Logger.log('TransformationService', '_applyDeformationToImage', 
        '✅ [调用ID: $callId] [完成] 变形处理完成');
    } catch (e) {
      Logger.log('TransformationService', '_applyDeformationToImage', 
        '❗ [调用ID: $callId] [ERROR] 变形处理异常: $e');
    } finally {
      // 重置处理状态
      _isDeforming = false;
      notifyListeners();
    }
  }
  
  // 更新特征点覆盖层的状态
  void _updateLandmarksOverlay({
    bool? isAnimating,
    Color? animationColor,
    double? animationOpacity,
    bool? showAfterDeformation,
    Map<int, Offset>? deformations,
  }) {
    if (_landmarksKey != null && _landmarksKey!.currentState != null) {
      _landmarksKey!.currentState!.updateState(
        isAnimating: isAnimating,
        animationColor: animationColor,
        animationOpacity: animationOpacity,
        showAfterDeformation: showAfterDeformation,
        deformations: deformations,
      );
    }
  }
  
  // 设置特征点覆盖层的引用
  void setLandmarksKey(GlobalKey<LandmarksOverlayState> key) {
    _landmarksKey = key;
    Logger.log('TransformationService', 'setLandmarksKey', '🔑 [设置] 特征点覆盖层引用');
  }
  
  // 设置选中的参数
  void setSelectedParameter(String paramName) {
    if (_selectedArea.isEmpty) {
      Logger.log('TransformationService', 'setSelectedParameter', '⚠️ [ERROR] 未选中区域');
      return;
    }
    
    // 生成参数唯一标识符
    final paramKey = '${_selectedArea}_${paramName}';
    
    // 获取当前参数值
    final currentValue = _transformationParams[_selectedArea]?[paramName] ?? 0.0;
    
    // 如果参数值为0且没有初始状态缓存，保存当前状态作为初始状态
    if (currentValue == 0.0) {
      _cacheManager.saveParameterInitialState(paramKey);
    }
    
    _selectedParameter = paramName;
    
    // 获取参数对应的特征点
    List<int> pointsToHighlight = FeaturePointsHelper().getParameterPoints(_selectedArea, paramName);
    
    // 通知监听器
    notifyListeners();
    
    Logger.log('TransformationService', 'setSelectedParameter', '✅ [INFO] 参数设置完成');
  }
  
  // 获取参数对应的特征点
  List<int> _getParameterPoints(String area, String paramName) {
    return FeaturePointsHelper().getParameterPoints(area, paramName);
  }
  
  // 重置所有变形
  void resetAllDeformations() {
    Logger.log('TransformationService', 'resetAllDeformations', '🔄 [入口] 重置所有变形');
    
    // 清空所有变形参数
    for (final area in _transformationParams.keys) {
      _transformationParams[area]?.clear();
    }
    
    // 重置变形状态
    _isShowingDeformation = false;
    _deformationCompleted = false;
    _isProcessing = false;
    
    // 清空变形数据
    _currentDeformations.clear();
    
    // 重置所有缓存
    _cacheManager.resetAllCache();
    
    // 更新特征点覆盖层
    _updateLandmarksOverlay(
      isAnimating: false,
      showAfterDeformation: false,
      deformations: {},
    );
    
    // 通知监听器
    notifyListeners();
    
    Logger.log('TransformationService', 'resetAllDeformations', '✅ [完成] 所有变形已重置，缓存已清理');
  }
  
  // 重置所有变形参数 (别名方法，调用resetAllDeformations)
  void resetAllTransformations() {
    Logger.log('TransformationService', 'resetAllTransformations', '🔄 [入口] 调用resetAllDeformations');
    resetAllDeformations();
  }
  
  // 清除选中的参数
  void clearSelectedParameter() {
    _selectedParameter = null;
    notifyListeners();
  }
  
  // 释放资源
  @override
  void dispose() {
    // 释放资源
    super.dispose();
  }
  
  // 获取当前图像
  ui.Image? get currentImage => _cacheManager.currentImage;
  
  // 获取变形图像路径
  String? get transformedImagePath => _cacheManager.transformedImagePath;
  
  // 获取当前特征点
  List<Map<String, dynamic>>? get featurePoints => _cacheManager.featurePoints;
}
