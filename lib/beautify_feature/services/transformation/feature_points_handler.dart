import 'dart:math';
import 'package:flutter/material.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/core/feature_points_helper.dart';
import 'package:beautifun/core/feature_points_data.dart';

/// 特征点处理器
/// 
/// 负责处理特征点的更新和变形
class FeaturePointsHandler {
  // 单例实例
  static final FeaturePointsHandler _instance = FeaturePointsHandler._internal();
  
  // 工厂构造函数
  factory FeaturePointsHandler() => _instance;
  
  // 内部构造函数
  FeaturePointsHandler._internal() {
    Logger.log('FeaturePointsHandler', '_internal', '🔄 [初始化] 特征点处理器初始化');
  }
  
  /// 获取区域对应的特征点
  List<int> getAreaPoints(String area) {
    // 将字符串区域名称转换为枚举类型
    FeatureAreaType areaType = _getFeatureAreaType(area);
    return FeaturePointsHelper().getAreaPoints(areaType);
  }
  
  /// 将字符串区域名称转换为枚举类型
  FeatureAreaType _getFeatureAreaType(String area) {
    switch (area.toLowerCase()) {
      case 'face_contour':
        return FeatureAreaType.face_contour;
      case 'nose':
        return FeatureAreaType.nose;
      case 'eyes':
        return FeatureAreaType.eyes;
      case 'lips':
        return FeatureAreaType.lips;
      case 'anti_aging':
        return FeatureAreaType.anti_aging;
      default:
        return FeatureAreaType.none;
    }
  }
  
  /// 获取参数对应的特征点
  List<int> getParameterPoints(String area, String paramName) {
    // 直接使用字符串参数，因为getParameterPoints方法接受字符串参数
    return FeaturePointsHelper().getParameterPoints(area, paramName);
  }
  
  /// 克隆特征点数据
  List<Map<String, dynamic>> cloneFeaturePoints(List<Map<String, dynamic>> points) {
    return points.map((point) => Map<String, dynamic>.from(point)).toList();
  }
  
  /// 更新特征点位置
  void updateFeaturePointsPosition(
    List<Map<String, dynamic>> points,
    Map<int, Offset> deformations
  ) {
    int updatedPoints = 0;
    
    // 遍历变形数据
    for (final entry in deformations.entries) {
      final pointId = entry.key;
      final deformation = entry.value;
      
      // 查找特征点
      final pointIndex = points.indexWhere((point) => point['id'] == pointId);
      if (pointIndex != -1) {
        // 获取原始坐标
        final originalX = (points[pointIndex]['x'] as num).toDouble();
        final originalY = (points[pointIndex]['y'] as num).toDouble();
        
        // 应用变形
        points[pointIndex]['x'] = originalX + deformation.dx;
        points[pointIndex]['y'] = originalY + deformation.dy;
        
        updatedPoints++;
      }
    }
    
    Logger.log('FeaturePointsHandler', 'updateFeaturePointsPosition', 
      '✅ [完成] 更新特征点位置 | 更新点数: $updatedPoints / ${deformations.length}');
  }
  
  /// 计算特征点的边界框
  Rect calculateBoundingBox(List<Map<String, dynamic>> points) {
    if (points.isEmpty) {
      return Rect.zero;
    }
    
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = double.negativeInfinity;
    double maxY = double.negativeInfinity;
    
    for (final point in points) {
      final x = (point['x'] as num).toDouble();
      final y = (point['y'] as num).toDouble();
      
      minX = min(minX, x);
      minY = min(minY, y);
      maxX = max(maxX, x);
      maxY = max(maxY, y);
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }
  
  /// 计算特征点的中心
  Offset calculateCenter(List<Map<String, dynamic>> points) {
    if (points.isEmpty) {
      return Offset.zero;
    }
    
    double sumX = 0;
    double sumY = 0;
    
    for (final point in points) {
      final x = (point['x'] as num).toDouble();
      final y = (point['y'] as num).toDouble();
      
      sumX += x;
      sumY += y;
    }
    
    return Offset(sumX / points.length, sumY / points.length);
  }
  
  /// 计算两个特征点集合之间的差异
  Map<int, Offset> calculatePointsDifference(
    List<Map<String, dynamic>> points1,
    List<Map<String, dynamic>> points2
  ) {
    final Map<int, Offset> differences = {};
    
    // 确保两个集合有相同数量的点
    if (points1.length != points2.length) {
      Logger.log('FeaturePointsHandler', 'calculatePointsDifference', 
        '⚠️ [警告] 特征点集合大小不匹配: ${points1.length} vs ${points2.length}');
      return differences;
    }
    
    // 遍历特征点
    for (int i = 0; i < points1.length; i++) {
      final point1 = points1[i];
      final point2 = points2[i];
      
      // 确保点ID匹配
      if (point1['id'] != point2['id']) {
        continue;
      }
      
      final id = point1['id'] as int;
      final x1 = (point1['x'] as num).toDouble();
      final y1 = (point1['y'] as num).toDouble();
      final x2 = (point2['x'] as num).toDouble();
      final y2 = (point2['y'] as num).toDouble();
      
      // 计算差异
      final dx = x2 - x1;
      final dy = y2 - y1;
      
      // 如果差异足够大，记录下来
      if (dx.abs() > 0.01 || dy.abs() > 0.01) {
        differences[id] = Offset(dx, dy);
      }
    }
    
    return differences;
  }
  
  /// 获取特征点的坐标
  Offset getPointPosition(List<Map<String, dynamic>> points, int pointId) {
    try {
      final point = points.firstWhere((p) => p['id'] == pointId);
      final x = (point['x'] as num).toDouble();
      final y = (point['y'] as num).toDouble();
      return Offset(x, y);
    } catch (e) {
      return Offset.zero;
    }
  }
  
  /// 计算特征点之间的距离
  double calculateDistance(List<Map<String, dynamic>> points, int pointId1, int pointId2) {
    final pos1 = getPointPosition(points, pointId1);
    final pos2 = getPointPosition(points, pointId2);
    
    return (pos1 - pos2).distance;
  }
}
