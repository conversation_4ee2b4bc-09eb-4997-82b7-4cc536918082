import 'dart:math';
import 'package:flutter/material.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/utils/feature_points_helper.dart';
import 'package:beautifun/core/deformation_direction_handler.dart';

/// 通用变形计算器
/// 
/// 负责计算变形向量和处理变形逻辑，确保所有变形参数可以准确重置到初始状态
class UniversalDeformationCalculator {
  // 单例实例
  static final UniversalDeformationCalculator _instance = UniversalDeformationCalculator._internal();
  
  // 工厂构造函数
  factory UniversalDeformationCalculator() => _instance;
  
  // 内部构造函数
  UniversalDeformationCalculator._internal() {
    Logger.log('UniversalDeformationCalculator', '_internal', '🔄 [初始化] 通用变形计算器初始化');
  }
  
  // 变形方向处理器
  final _directionHandler = DeformationDirectionHandler();
  
  /// 计算变形向量
  /// 
  /// 根据区域、参数和特征点计算变形向量
  Map<int, Offset> calculateDeformations(
    String area, 
    String parameter, 
    double paramValue, 
    List<Map<String, dynamic>> featurePoints
  ) {
    final Map<int, Offset> deformations = {};
    
    // 获取参数对应的特征点
    final paramPoints = FeaturePointsHelper().getParameterPoints(area, parameter);
    
    Logger.log('UniversalDeformationCalculator', 'calculateDeformations', 
      '📊 [计算] 参数特征点 | 区域: $area | 参数: $parameter | ' +
      '值: $paramValue | 点数: ${paramPoints.length}');
    
    // 如果参数值为0，返回空的变形数据
    if (paramValue == 0.0) {
      return deformations;
    }
    
    // 获取面部中心参考点
    final centerPoint = _getCenterReferencePoint(area, featurePoints);
    
    // 根据区域和参数计算变形向量
    for (final pointId in paramPoints) {
      // 查找特征点数据
      final pointData = featurePoints.firstWhere(
        (point) => point['id'] == pointId,
        orElse: () => {'id': pointId, 'x': 0, 'y': 0}
      );
      
      // 判断点在左侧还是右侧
      final currentX = (pointData['x'] as num).toDouble();
      final centerX = (centerPoint['x'] as num).toDouble();
      final isLeftSide = currentX < centerX;
      
      // 使用变形方向处理器计算变形向量
      final deformationVector = _directionHandler.calculateDeformation(
        area, parameter, paramValue, isLeftSide
      );
      
      // 保存变形向量
      deformations[pointId] = deformationVector;
      
      Logger.log('UniversalDeformationCalculator', 'calculateDeformations', 
        '🔍 [调试] 变形向量计算: 点ID=$pointId, 参数值=$paramValue, 左侧=$isLeftSide, ' +
        'dx=${deformationVector.dx}, dy=${deformationVector.dy}');
    }
    
    return deformations;
  }
  
  /// 获取中心参考点
  Map<String, dynamic> _getCenterReferencePoint(String area, List<Map<String, dynamic>> featurePoints) {
    // 根据区域获取中心参考点ID
    int centerPointId = 1; // 默认使用鼻尖点
    
    switch (area) {
      case 'nose':
        centerPointId = 1; // 鼻尖点
        break;
      case 'eyes':
        centerPointId = 168; // 鼻梁起始点
        break;
      case 'face_contour':
        centerPointId = 1; // 鼻尖点
        break;
      case 'lips':
        centerPointId = 13; // 上唇中心点
        break;
      case 'anti_aging':
        centerPointId = 1; // 鼻尖点
        break;
    }
    
    // 查找中心参考点
    try {
      return featurePoints.firstWhere((point) => point['id'] == centerPointId);
    } catch (e) {
      // 如果找不到指定的中心点，计算所有点的平均位置作为中心点
      double sumX = 0;
      double sumY = 0;
      
      for (final point in featurePoints) {
        sumX += (point['x'] as num).toDouble();
        sumY += (point['y'] as num).toDouble();
      }
      
      return {
        'id': -1,
        'x': sumX / featurePoints.length,
        'y': sumY / featurePoints.length,
      };
    }
  }
  
  /// 更新特征点位置
  /// 
  /// 根据变形向量更新特征点位置
  void updateFeaturePointsWithDeformations(
    List<Map<String, dynamic>> featurePoints,
    Map<int, Offset> deformations
  ) {
    int updatedPoints = 0;
    
    // 遍历变形数据
    for (final entry in deformations.entries) {
      final pointId = entry.key;
      final deformation = entry.value;
      
      // 查找特征点
      final pointIndex = featurePoints.indexWhere((point) => point['id'] == pointId);
      if (pointIndex != -1) {
        // 获取原始坐标
        final originalX = (featurePoints[pointIndex]['x'] as num).toDouble();
        final originalY = (featurePoints[pointIndex]['y'] as num).toDouble();
        
        // 应用变形
        featurePoints[pointIndex]['x'] = originalX + deformation.dx;
        featurePoints[pointIndex]['y'] = originalY + deformation.dy;
        
        updatedPoints++;
      }
    }
    
    Logger.log('UniversalDeformationCalculator', 'updateFeaturePointsWithDeformations', 
      '✅ [完成] 更新特征点数据 | 更新点数: $updatedPoints / ${deformations.length}');
  }
  
  /// 计算特征点的边界框
  Rect calculateBoundingBox(List<Map<String, dynamic>> points) {
    if (points.isEmpty) {
      return Rect.zero;
    }
    
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = double.negativeInfinity;
    double maxY = double.negativeInfinity;
    
    for (final point in points) {
      final x = (point['x'] as num).toDouble();
      final y = (point['y'] as num).toDouble();
      
      minX = min(minX, x);
      minY = min(minY, y);
      maxX = max(maxX, x);
      maxY = max(maxY, y);
    }
    
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }
}
