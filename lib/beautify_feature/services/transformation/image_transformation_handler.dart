import 'dart:io';
import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/core/image_deformation_engine.dart';
import 'package:beautifun/beautify_feature/services/transformation/transformation_cache_manager.dart';

/// 图像变形处理器
/// 
/// 负责处理图像的变形应用和加载
class ImageTransformationHandler {
  // 单例实例
  static final ImageTransformationHandler _instance = ImageTransformationHandler._internal();
  
  // 工厂构造函数
  factory ImageTransformationHandler() => _instance;
  
  // 内部构造函数
  ImageTransformationHandler._internal() {
    Logger.log('ImageTransformationHandler', '_internal', '🔄 [初始化] 图像变形处理器初始化');
  }
  
  // 缓存管理器
  final _cacheManager = TransformationCacheManager();
  
  // 状态标志
  bool _isDeforming = false;
  bool _isLoadingTransformedImage = false;
  
  /// 应用变形到图像
  /// 
  /// 根据变形向量应用变形到图像
  Future<bool> applyDeformationToImage(
    String imagePath,
    Map<int, Offset> deformations,
    List<Map<String, dynamic>> featurePoints,
    {String? callId}
  ) async {
    final id = callId ?? DateTime.now().millisecondsSinceEpoch.toString();
    
    // 如果正在变形或加载图像，跳过处理
    if (_isDeforming || _isLoadingTransformedImage) {
      Logger.log('ImageTransformationHandler', 'applyDeformationToImage', 
        '⚠️ [调用ID: $id] [跳过] 正在处理中，跳过新的变形请求');
      return false;
    }
    
    // 设置处理状态
    _isDeforming = true;
    
    try {
      Logger.log('ImageTransformationHandler', 'applyDeformationToImage', 
        '🔄 [调用ID: $id] [处理] 开始调用变形引擎 | 变形点数: ${deformations.length}');
      
      // 创建变形引擎实例
      final deformationEngine = ImageDeformationEngine();
      
      // 异步调用变形引擎
      final result = await deformationEngine.applyDeformation(
        imagePath: imagePath,
        deformations: deformations,
        landmarks: featurePoints,
      );
      
      // 处理变形结果
      if (result.success) {
        Logger.log('ImageTransformationHandler', 'applyDeformationToImage', 
          '✅ [调用ID: $id] [成功] 变形处理成功 | 输出路径: ${result.outputPath}');
        
        // 设置变形图像路径
        _cacheManager.setTransformedImagePath(result.transformedImagePath);
        
        // 加载变形后的图像
        await loadTransformedImage();
        
        return true;
      } else {
        Logger.log('ImageTransformationHandler', 'applyDeformationToImage', 
          '❌ [调用ID: $id] [失败] 变形处理失败 | 错误: ${result.errorMessage}');
        return false;
      }
    } catch (e) {
      Logger.log('ImageTransformationHandler', 'applyDeformationToImage', 
        '❗ [调用ID: $id] [ERROR] 变形处理异常: $e');
      return false;
    } finally {
      // 重置处理状态
      _isDeforming = false;
    }
  }
  
  /// 加载变形后的图像
  Future<ui.Image?> loadTransformedImage() async {
    // 设置加载状态
    _isLoadingTransformedImage = true;
    
    try {
      // 调用缓存管理器加载图像
      final image = await _cacheManager.loadTransformedImage();
      return image;
    } finally {
      // 重置加载状态
      _isLoadingTransformedImage = false;
    }
  }
  
  /// 检查变形图像是否有效
  Future<bool> isTransformedImageValid() async {
    final transformedImagePath = _cacheManager.transformedImagePath;
    if (transformedImagePath == null || transformedImagePath.isEmpty) {
      return false;
    }
    
    try {
      final file = File(transformedImagePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }
}
