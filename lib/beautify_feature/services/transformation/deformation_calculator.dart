import 'dart:math';
import 'package:flutter/material.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/utils/feature_points_helper.dart';

/// 变形计算器
/// 
/// 负责计算变形向量和处理变形逻辑
class DeformationCalculator {
  // 单例实例
  static final DeformationCalculator _instance = DeformationCalculator._internal();
  
  // 工厂构造函数
  factory DeformationCalculator() => _instance;
  
  // 内部构造函数
  DeformationCalculator._internal() {
    Logger.log('DeformationCalculator', '_internal', '🔄 [初始化] 变形计算器初始化');
  }
  
  /// 计算变形向量
  /// 
  /// 根据区域、参数和特征点计算变形向量
  Map<int, Offset> calculateDeformations(
    String area, 
    String parameter, 
    double paramValue, 
    List<Map<String, dynamic>> featurePoints
  ) {
    final Map<int, Offset> deformations = {};
    
    // 获取参数对应的特征点
    final paramPoints = FeaturePointsHelper().getParameterPoints(area, parameter);
    
    Logger.log('DeformationCalculator', 'calculateDeformations', 
      '📊 [计算] 参数特征点 | 区域: $area | 参数: $parameter | ' +
      '值: $paramValue | 点数: ${paramPoints.length}');
    
    // 如果参数值为0，返回空的变形数据
    if (paramValue == 0.0) {
      return deformations;
    }
    
    // 根据区域和参数计算变形向量
    for (final pointId in paramPoints) {
      // 查找特征点数据
      final pointData = featurePoints.firstWhere(
        (point) => point['id'] == pointId,
        orElse: () => {'id': pointId, 'x': 0, 'y': 0}
      );
      
      // 计算变形向量
      final Offset deformationVector = _calculateDeformationVector(
        area, parameter, paramValue, pointId, pointData, featurePoints
      );
      
      // 保存变形向量
      deformations[pointId] = deformationVector;
    }
    
    return deformations;
  }
  
  /// 计算单个点的变形向量
  /// 
  /// 根据区域、参数、参数值和点ID计算变形向量
  Offset _calculateDeformationVector(
    String area, 
    String parameter, 
    double paramValue, 
    int pointId, 
    Map<String, dynamic> point,
    List<Map<String, dynamic>> featurePoints
  ) {
    double xFactor = 0.0;
    double yFactor = 0.0;
    
    // 根据区域和参数设置偏移方向
    switch (area) {
      case 'nose':
        if (parameter == 'nostril_width') {
          // 鼻翼宽度：水平方向偏移
          // 获取鼻尖点作为中心参考点
          final tipPoint = featurePoints.firstWhere(
            (p) => p['id'] == 1,  // 鼻尖点ID为1
            orElse: () => {'id': 1, 'x': 0, 'y': 0}
          );
          
          // 从特征点数据中提取x坐标
          double tipX = (tipPoint['x'] as num).toDouble();
          double currentX = (point['x'] as num).toDouble();
          
          // 判断点在左侧还是右侧
          bool isLeftSide = currentX < tipX;
          
          // 根据参数值的正负和点的位置确定变形方向
          if (paramValue < 0) { // 缩小鼻翼
            // 向中心移动
            xFactor = isLeftSide ? paramValue.abs() * 0.3 : -paramValue.abs() * 0.3;
          } else { // 增宽鼻翼
            // 向外移动
            xFactor = isLeftSide ? -paramValue * 0.3 : paramValue * 0.3;
          }
          
          yFactor = 0.0;
          Logger.log('DeformationCalculator', '_calculateDeformationVector', 
            '🔍 [调试] 鼻翼宽度变形: 点ID=$pointId, 参数值=$paramValue, 左侧=$isLeftSide, xFactor=$xFactor, yFactor=$yFactor');
        } else if (parameter == 'bridge_height') {
          // 鼻梁高度：垂直方向偏移
          xFactor = 0.0;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        } else if (parameter == 'tip_adjust') {
          // 鼻尖调整：垂直方向偏移
          xFactor = 0.0;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        } else if (parameter == 'base_height') {
          // 鼻基抬高：垂直方向偏移
          xFactor = 0.0;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        }
        break;
      
      case 'face_contour':
        if (parameter == 'contour_tighten') {
          // 轮廓收紧：水平方向偏移
          // 获取面部中心点
          final centerX = _calculateFaceCenterX(featurePoints);
          final currentX = (point['x'] as num).toDouble();
          
          // 判断点在左侧还是右侧
          bool isLeftSide = currentX < centerX;
          
          // 根据参数值的正负和点的位置确定变形方向
          if (paramValue < 0) { // 放松轮廓
            // 向外移动
            xFactor = isLeftSide ? paramValue * 0.3 : -paramValue * 0.3;
          } else { // 收紧轮廓
            // 向内移动
            xFactor = isLeftSide ? -paramValue * 0.3 : paramValue * 0.3;
          }
          
          yFactor = 0.0;
        } else if (parameter == 'chin_adjust') {
          // 下巴调整：垂直方向偏移
          xFactor = 0.0;
          yFactor = paramValue * 0.3; // 正值表示向下偏移
        } else if (parameter == 'cheekbone_adjust') {
          // 颧骨调整：水平方向偏移
          // 获取面部中心点
          final centerX = _calculateFaceCenterX(featurePoints);
          final currentX = (point['x'] as num).toDouble();
          
          // 判断点在左侧还是右侧
          bool isLeftSide = currentX < centerX;
          
          // 根据参数值的正负和点的位置确定变形方向
          if (paramValue < 0) { // 减小颧骨
            // 向内移动
            xFactor = isLeftSide ? -paramValue * 0.3 : paramValue * 0.3;
          } else { // 增大颧骨
            // 向外移动
            xFactor = isLeftSide ? paramValue * 0.3 : -paramValue * 0.3;
          }
          
          yFactor = 0.0;
        } else if (parameter == 'face_shape') {
          // 脸型优化：混合方向偏移
          xFactor = paramValue * 0.3;
          yFactor = paramValue * 0.3;
        }
        break;
      
      case 'eyes':
        if (parameter == 'double_fold') {
          // 双眼皮：垂直方向偏移
          xFactor = 0.0;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        } else if (parameter == 'canthal_tilt') {
          // 开眼角：混合方向偏移
          xFactor = paramValue * 0.3;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        } else if (parameter == 'eye_bag_removal') {
          // 去眼袋：垂直方向偏移
          xFactor = 0.0;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        } else if (parameter == 'outer_corner_lift') {
          // 提眼尾：混合方向偏移
          xFactor = paramValue * 0.3;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        }
        break;
      
      case 'lips':
        if (parameter == 'lip_shape') {
          // 唇形调整：混合方向偏移
          xFactor = paramValue * 0.3;
          yFactor = paramValue * 0.3;
        } else if (parameter == 'mouth_corner') {
          // 嘴角上扬：垂直方向偏移
          xFactor = 0.0;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        }
        break;
      
      case 'anti_aging':
        if (parameter == 'nasolabial_folds') {
          // 法令纹：混合方向偏移
          xFactor = -paramValue * 0.3; // 负值表示向内偏移
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        } else if (parameter == 'wrinkle_removal') {
          // 去皱纹：混合方向偏移
          xFactor = 0.0;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        } else if (parameter == 'forehead_fullness') {
          // 额头饱满：垂直方向偏移
          xFactor = 0.0;
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        } else if (parameter == 'facial_firmness') {
          // 面容紧致：混合方向偏移
          xFactor = -paramValue * 0.3; // 负值表示向内偏移
          yFactor = -paramValue * 0.3; // 负值表示向上偏移
        }
        break;
    }
    
    return Offset(xFactor, yFactor);
  }
  
  /// 计算面部中心点的X坐标
  double _calculateFaceCenterX(List<Map<String, dynamic>> featurePoints) {
    // 尝试获取鼻尖点作为面部中心点
    try {
      final tipPoint = featurePoints.firstWhere((p) => p['id'] == 1);
      return (tipPoint['x'] as num).toDouble();
    } catch (e) {
      // 如果找不到鼻尖点，计算所有点的平均X坐标
      double sumX = 0;
      for (final point in featurePoints) {
        sumX += (point['x'] as num).toDouble();
      }
      return sumX / featurePoints.length;
    }
  }
  
  /// 更新特征点位置
  /// 
  /// 根据变形向量更新特征点位置
  void updateFeaturePointsWithDeformations(
    List<Map<String, dynamic>> featurePoints,
    Map<int, Offset> deformations
  ) {
    int updatedPoints = 0;
    
    // 遍历变形数据
    for (final entry in deformations.entries) {
      final pointId = entry.key;
      final deformation = entry.value;
      
      // 查找特征点
      final pointIndex = featurePoints.indexWhere((point) => point['id'] == pointId);
      if (pointIndex != -1) {
        // 获取原始坐标
        final originalX = (featurePoints[pointIndex]['x'] as num).toDouble();
        final originalY = (featurePoints[pointIndex]['y'] as num).toDouble();
        
        // 应用变形
        featurePoints[pointIndex]['x'] = originalX + deformation.dx;
        featurePoints[pointIndex]['y'] = originalY + deformation.dy;
        
        updatedPoints++;
      }
    }
    
    Logger.log('DeformationCalculator', 'updateFeaturePointsWithDeformations', 
      '✅ [完成] 更新特征点数据 | 更新点数: $updatedPoints / ${deformations.length}');
  }
}
