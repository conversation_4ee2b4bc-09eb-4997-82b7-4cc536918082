import 'dart:collection';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'logging_service.dart';

/// 性能监控服务类，用于监控应用程序的性能指标
class PerformanceMonitorService {
  /// 单例实例
  static final PerformanceMonitorService _instance = PerformanceMonitorService._internal();
  
  /// 日志服务实例
  final LoggingService _logger = LoggingService();
  
  /// 性能数据文件路径
  String? _performanceFilePath;
  
  /// 性能数据文件对象
  File? _performanceFile;
  
  /// 性能数据缓冲区
  final List<Map<String, dynamic>> _performanceBuffer = [];
  
  /// 缓冲区大小
  static const int _bufferSize = 50;
  
  /// 是否已初始化
  bool _isInitialized = false;
  
  /// 性能数据历史记录
  final LinkedHashMap<String, List<double>> _performanceHistory = LinkedHashMap();
  
  /// 性能阈值配置
  final Map<String, double> _performanceThresholds = {
    'faceDetection': 500.0, // 毫秒
    'imageProcessing': 300.0, // 毫秒
    'renderAnimation': 16.67, // 毫秒 (60fps)
    'uiInteraction': 100.0, // 毫秒
  };
  
  /// 获取单例实例
  factory PerformanceMonitorService() {
    return _instance;
  }
  
  /// 内部构造函数
  PerformanceMonitorService._internal();
  
  /// 初始化性能监控服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final directory = await getApplicationDocumentsDirectory();
      final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.now());
      _performanceFilePath = '${directory.path}/beautifun_performance/performance_$dateStr.json';
      
      // 确保性能数据目录存在
      final perfDir = Directory('${directory.path}/beautifun_performance');
      if (!await perfDir.exists()) {
        await perfDir.create(recursive: true);
      }
      
      _performanceFile = File(_performanceFilePath!);
      if (!await _performanceFile!.exists()) {
        await _performanceFile!.create(recursive: true);
        await _performanceFile!.writeAsString('[]');
      } else {
        // 加载现有性能数据
        try {
          final content = await _performanceFile!.readAsString();
          if (content.isNotEmpty) {
            final List<dynamic> data = jsonDecode(content);
            for (var item in data) {
              final category = item['category'] as String;
              final duration = item['duration'] as double;
              
              if (!_performanceHistory.containsKey(category)) {
                _performanceHistory[category] = [];
              }
              
              _performanceHistory[category]!.add(duration);
            }
          }
        } catch (e) {
          _logger.error(
            module: 'PerformanceMonitorService',
            function: 'initialize',
            operation: OperationType.initialize,
            message: '加载性能数据失败',
            error: e,
          );
        }
      }
      
      _isInitialized = true;
      
      // 记录初始化日志
      _logger.info(
        module: 'PerformanceMonitorService',
        function: 'initialize',
        operation: OperationType.initialize,
        message: '性能监控服务初始化完成',
      );
    } catch (e) {
      _logger.error(
        module: 'PerformanceMonitorService',
        function: 'initialize',
        operation: OperationType.initialize,
        message: '初始化性能监控服务失败',
        error: e,
      );
    }
  }
  
  /// 开始计时
  Stopwatch startTimer() {
    final stopwatch = Stopwatch();
    stopwatch.start();
    return stopwatch;
  }
  
  /// 停止计时并记录性能数据
  void stopTimer({
    required Stopwatch stopwatch,
    required String category,
    required String operation,
    Map<String, dynamic>? data,
  }) async {
    stopwatch.stop();
    final duration = stopwatch.elapsedMilliseconds.toDouble();
    
    // 记录性能数据
    await recordPerformance(
      category: category,
      operation: operation,
      duration: duration,
      data: data,
    );
    
    // 检查性能是否超过阈值
    if (_performanceThresholds.containsKey(category) && 
        duration > _performanceThresholds[category]!) {
      _logger.warning(
        module: 'PerformanceMonitorService',
        function: 'stopTimer',
        operation: OperationType.performance,
        message: '性能超过阈值: $category - $operation',
        data: {
          'duration': duration,
          'threshold': _performanceThresholds[category],
          'data': data,
        },
      );
    }
  }
  
  /// 记录性能数据
  Future<void> recordPerformance({
    required String category,
    required String operation,
    required double duration,
    Map<String, dynamic>? data,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    final timestamp = DateTime.now().toIso8601String();
    
    final performanceData = {
      'timestamp': timestamp,
      'category': category,
      'operation': operation,
      'duration': duration,
      'data': data,
    };
    
    // 添加到历史记录
    if (!_performanceHistory.containsKey(category)) {
      _performanceHistory[category] = [];
    }
    
    _performanceHistory[category]!.add(duration);
    
    // 限制历史记录大小
    if (_performanceHistory[category]!.length > 100) {
      _performanceHistory[category]!.removeAt(0);
    }
    
    // 添加到缓冲区
    _performanceBuffer.add(performanceData);
    
    // 记录性能日志
    _logger.info(
      module: 'PerformanceMonitorService',
      function: 'recordPerformance',
      operation: OperationType.performance,
      message: '记录性能数据: $category - $operation',
      data: {
        'duration': duration,
        'data': data,
      },
    );
    
    // 缓冲区满时写入文件
    if (_performanceBuffer.length >= _bufferSize) {
      await _flushBuffer();
    }
  }
  
  /// 将缓冲区内容写入文件
  Future<void> _flushBuffer() async {
    if (!_isInitialized || _performanceBuffer.isEmpty) return;
    
    try {
      // 读取现有数据
      String content = await _performanceFile!.readAsString();
      List<dynamic> existingData = [];
      
      if (content.isNotEmpty) {
        try {
          existingData = jsonDecode(content);
        } catch (e) {
          _logger.error(
            module: 'PerformanceMonitorService',
            function: '_flushBuffer',
            operation: OperationType.process,
            message: '解析性能数据文件失败',
            error: e,
          );
          existingData = [];
        }
      }
      
      // 添加新数据
      existingData.addAll(_performanceBuffer);
      
      // 写入文件
      await _performanceFile!.writeAsString(
        jsonEncode(existingData),
        flush: true,
      );
      
      _performanceBuffer.clear();
    } catch (e) {
      _logger.error(
        module: 'PerformanceMonitorService',
        function: '_flushBuffer',
        operation: OperationType.process,
        message: '写入性能数据文件失败',
        error: e,
      );
    }
  }
  
  /// 获取性能统计数据
  Map<String, Map<String, double>> getPerformanceStats() {
    final Map<String, Map<String, double>> stats = {};
    
    for (final category in _performanceHistory.keys) {
      final List<double> durations = _performanceHistory[category]!;
      
      if (durations.isEmpty) continue;
      
      // 计算平均值
      final double average = durations.reduce((a, b) => a + b) / durations.length;
      
      // 计算最小值
      final double min = durations.reduce((a, b) => a < b ? a : b);
      
      // 计算最大值
      final double max = durations.reduce((a, b) => a > b ? a : b);
      
      // 计算中位数
      final List<double> sorted = List.from(durations)..sort();
      final double median = sorted.length.isOdd 
          ? sorted[sorted.length ~/ 2] 
          : (sorted[sorted.length ~/ 2 - 1] + sorted[sorted.length ~/ 2]) / 2;
      
      // 计算标准差
      final double mean = average;
      final double variance = durations.map((x) => pow(x - mean, 2)).reduce((a, b) => a + b) / durations.length;
      final double stdDev = sqrt(variance);
      
      stats[category] = {
        'average': average,
        'min': min,
        'max': max,
        'median': median,
        'stdDev': stdDev,
      };
    }
    
    return stats;
  }
  
  /// 计算平方
  double pow(double x, int exponent) {
    double result = 1.0;
    for (int i = 0; i < exponent; i++) {
      result *= x;
    }
    return result;
  }
  
  /// 计算平方根
  double sqrt(double x) {
    if (x <= 0) return 0;
    
    double guess = x / 2;
    for (int i = 0; i < 10; i++) {
      guess = (guess + x / guess) / 2;
    }
    
    return guess;
  }
  
  /// 获取性能阈值
  Map<String, double> getPerformanceThresholds() {
    return Map.from(_performanceThresholds);
  }
  
  /// 设置性能阈值
  void setPerformanceThreshold(String category, double threshold) {
    _performanceThresholds[category] = threshold;
    
    _logger.info(
      module: 'PerformanceMonitorService',
      function: 'setPerformanceThreshold',
      operation: OperationType.process,
      message: '设置性能阈值: $category - $threshold',
    );
  }
  
  /// 获取性能历史数据
  Map<String, List<double>> getPerformanceHistory() {
    final Map<String, List<double>> history = {};
    
    for (final category in _performanceHistory.keys) {
      history[category] = List.from(_performanceHistory[category]!);
    }
    
    return history;
  }
  
  /// 清除性能数据
  Future<void> clearPerformanceData() async {
    if (!_isInitialized) return;
    
    try {
      await _flushBuffer(); // 确保所有数据都写入文件
      await _performanceFile!.writeAsString('[]'); // 清空文件
      
      _performanceHistory.clear();
      
      _logger.info(
        module: 'PerformanceMonitorService',
        function: 'clearPerformanceData',
        operation: OperationType.process,
        message: '性能数据已清除',
      );
    } catch (e) {
      _logger.error(
        module: 'PerformanceMonitorService',
        function: 'clearPerformanceData',
        operation: OperationType.process,
        message: '清除性能数据失败',
        error: e,
      );
    }
  }
  
  /// 生成性能报告
  Future<String> generatePerformanceReport() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    await _flushBuffer(); // 确保所有数据都写入文件
    
    final stats = getPerformanceStats();
    final thresholds = getPerformanceThresholds();
    
    final StringBuffer report = StringBuffer();
    report.writeln('# 性能监控报告');
    report.writeln('生成时间: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}');
    report.writeln();
    
    report.writeln('## 性能统计');
    report.writeln('| 类别 | 平均值 (ms) | 最小值 (ms) | 最大值 (ms) | 中位数 (ms) | 标准差 | 阈值 (ms) | 状态 |');
    report.writeln('|------|------------|------------|------------|------------|--------|-----------|------|');
    
    for (final category in stats.keys) {
      final stat = stats[category]!;
      final threshold = thresholds[category] ?? double.infinity;
      final status = stat['average']! > threshold ? '⚠️ 超过阈值' : '✅ 正常';
      
      report.writeln(
        '| $category | ${stat['average']!.toStringAsFixed(2)} | ${stat['min']!.toStringAsFixed(2)} | '
        '${stat['max']!.toStringAsFixed(2)} | ${stat['median']!.toStringAsFixed(2)} | '
        '${stat['stdDev']!.toStringAsFixed(2)} | ${threshold.toStringAsFixed(2)} | $status |'
      );
    }
    
    report.writeln();
    report.writeln('## 性能趋势');
    
    for (final category in _performanceHistory.keys) {
      final durations = _performanceHistory[category]!;
      if (durations.isEmpty) continue;
      
      report.writeln('### $category');
      report.writeln('最近 ${durations.length} 次操作的性能趋势:');
      
      // 简单的ASCII图表
      final int width = 50;
      final double max = durations.reduce((a, b) => a > b ? a : b);
      final double min = durations.reduce((a, b) => a < b ? a : b);
      final double range = max - min > 0 ? max - min : 1;
      
      for (int i = 0; i < durations.length; i++) {
        final double value = durations[i];
        final int barLength = ((value - min) / range * width).round();
        final String bar = '█' * barLength;
        report.writeln('${i.toString().padLeft(3)}: $bar ${value.toStringAsFixed(2)} ms');
      }
      
      report.writeln();
    }
    
    report.writeln('## 性能建议');
    
    for (final category in stats.keys) {
      final stat = stats[category]!;
      final threshold = thresholds[category] ?? double.infinity;
      
      if (stat['average']! > threshold) {
        report.writeln('- **$category**: 平均性能 (${stat['average']!.toStringAsFixed(2)} ms) 超过阈值 (${threshold.toStringAsFixed(2)} ms)');
        
        // 根据不同类别提供具体建议
        switch (category) {
          case 'faceDetection':
            report.writeln('  - 考虑优化面部检测算法或降低处理图像的分辨率');
            report.writeln('  - 检查是否有不必要的重复检测');
            break;
          case 'imageProcessing':
            report.writeln('  - 考虑使用更高效的图像处理算法');
            report.writeln('  - 检查是否可以减少处理步骤或降低处理质量');
            break;
          case 'renderAnimation':
            report.writeln('  - 减少动画复杂度或帧率');
            report.writeln('  - 检查是否有不必要的重绘');
            break;
          case 'uiInteraction':
            report.writeln('  - 优化UI组件的构建和布局计算');
            report.writeln('  - 减少不必要的状态更新');
            break;
          default:
            report.writeln('  - 检查代码中的性能瓶颈');
            report.writeln('  - 考虑使用更高效的算法或数据结构');
        }
      }
    }
    
    return report.toString();
  }
  
  /// 释放资源
  Future<void> dispose() async {
    await _flushBuffer(); // 确保所有数据都写入文件
  }
}
