import 'dart:typed_data';
import 'package:flutter/material.dart';

import '../../core/models/feature_point.dart';
import '../../utils/beautify_logger.dart';
import '../models/nose_parameter.dart';
import 'transformation_service.dart';

/// 鼻部变形集成服务
/// 
/// 负责处理鼻部特征点的变形和渲染
class NoseTransformationIntegrationService extends ChangeNotifier {
  /// 日志标签
  static const String _logTag = 'NoseTransformationIntegrationService';
  
  /// 当前参数类型
  NoseParameterType? _currentParameterType;
  NoseParameterType? get currentParameterType => _currentParameterType;
  
  /// 选中的参数类型
  NoseParameterType? _selectedParameterType;
  NoseParameterType? get selectedParameterType => _selectedParameterType;
  
  /// 变形服务
  final TransformationService _transformationService;
  
  /// 当前图像路径
  String? _currentImagePath;
  String? get currentImagePath => _currentImagePath;
  
  /// 是否正在处理
  final bool _isProcessing = false;
  bool get isProcessing => _isProcessing;
  
  /// 医学建议
  final List<String> _medicalAdvice = [];
  List<String> get medicalAdvice => _medicalAdvice;
  
  /// 预览图像数据
  Uint8List? _previewImageData;
  Uint8List? get previewImageData => _previewImageData;
  
  /// 鼻部参数列表
  final List<NoseParameter> _parameters = [];
  List<NoseParameter> get parameters => _parameters;
  
  /// 初始化标志
  bool _initialized = false;
  
  /// 构造函数
  NoseTransformationIntegrationService({
    TransformationService? transformationService,
  }) : _transformationService = transformationService ?? TransformationService() {
    BeautifyLogger.logInfo(_logTag, 'constructor', '创建鼻部变形集成服务实例');
    // 在构造函数中启动初始化，但不等待其完成
    _initialize();
  }
  
  /// 确保服务已初始化
  Future<void> ensureInitialized() async {
    if (!_initialized) {
      BeautifyLogger.logInfo(_logTag, 'ensureInitialized', '等待初始化完成');
      await _initialize();
    }
  }
  
  /// 初始化服务
  Future<void> _initialize() async {
    if (_initialized) {
      BeautifyLogger.logInfo(_logTag, '_initialize', '服务已初始化，跳过');
      return;
    }
    
    try {
      BeautifyLogger.flowStart(_logTag, '初始化');
      BeautifyLogger.logInfo(_logTag, '_initialize', '开始初始化鼻部变形集成服务');
      
      // 初始化变形服务
      await _transformationService.initialize();
      BeautifyLogger.logInfo(_logTag, '_initialize', '变形服务初始化完成');
      
      // 标记为已初始化
      _initialized = true;
      
      BeautifyLogger.logInfo(_logTag, '_initialize', '鼻部变形集成服务初始化完成');
      BeautifyLogger.flowEnd(_logTag, '初始化');
    } catch (e) {
      BeautifyLogger.logError(_logTag, '_initialize', '鼻部变形集成服务初始化失败: $e');
      BeautifyLogger.flowError(_logTag, '初始化', '失败: $e');
    }
  }
  
  /// 设置当前图像路径
  Future<void> setImagePath(String imagePath) async {
    if (_currentImagePath != imagePath) {
      _currentImagePath = imagePath;
      _previewImageData = null;
      
      BeautifyLogger.logInfo(_logTag, 'setImagePath', '图像路径已设置: $imagePath');
      
      notifyListeners();
    }
  }
  
  /// 选择参数
  void selectParameter(NoseParameterType parameterType) {
    BeautifyLogger.flowStart(_logTag, 'selectParameter');
    
    if (_selectedParameterType != parameterType) {
      BeautifyLogger.logInfo(_logTag, 'selectParameter', '选择参数: $parameterType');
      _selectedParameterType = parameterType;
      
      // 获取参数名称
      final parameterName = _getParameterNameFromType(parameterType);
      
      // 更新变形渲染器参数
      BeautifyLogger.logInfo(_logTag, 'selectParameter', '更新变形渲染器参数: $parameterName');
      updateDeformationRendererParameter(parameterName);
      
      // 通知监听器更新UI
      notifyListeners();
    } else {
      BeautifyLogger.logInfo(_logTag, 'selectParameter', '参数已选中: $parameterType');
    }
    
    BeautifyLogger.flowEnd(_logTag, 'selectParameter');
  }
  
  /// 更新变形渲染器参数
  void updateDeformationRendererParameter(String parameterName) {
    BeautifyLogger.flowStart(_logTag, 'updateDeformationRendererParameter');
    
    try {
      // 确保变形服务已初始化
      if (!_initialized) {
        BeautifyLogger.logWarning(_logTag, 'updateDeformationRendererParameter', '服务尚未初始化');
        BeautifyLogger.flowEnd(_logTag, 'updateDeformationRendererParameter');
        return;
      }
      
      // 获取变形渲染器
      final renderer = _transformationService.getSimpleDeformationRenderer();
      if (renderer == null) {
        BeautifyLogger.logError(_logTag, 'updateDeformationRendererParameter', '获取变形渲染器失败');
        BeautifyLogger.flowEnd(_logTag, 'updateDeformationRendererParameter');
        return;
      }
      
      // 设置区域类型为鼻部
      renderer.setArea('nose');
      
      // 设置参数名称
      renderer.setParameter(parameterName);
      
      // 确保变形类型设置正确
      if (parameterName == 'nostril_width') {
        BeautifyLogger.logInfo(_logTag, 'updateDeformationRendererParameter', '设置变形类型为 local (局部变形)');
        renderer.setTransformTypeDirectly(TransformType.local);
      } else {
        // 其他参数使用默认的mesh变形类型
        BeautifyLogger.logInfo(_logTag, 'updateDeformationRendererParameter', '设置变形类型为 mesh (网格变形)');
        renderer.setTransformTypeDirectly(TransformType.mesh);
      }
      
      BeautifyLogger.logInfo(_logTag, 'updateDeformationRendererParameter', '更新变形渲染器参数: $parameterName');
      
      // 强制重绘
      renderer.forceRepaint();
      
      BeautifyLogger.logInfo(_logTag, 'updateDeformationRendererParameter', '强制重绘完成');
    } catch (e) {
      BeautifyLogger.logError(_logTag, 'updateDeformationRendererParameter', '更新变形渲染器参数失败: $e');
    }
    
    BeautifyLogger.flowEnd(_logTag, 'updateDeformationRendererParameter');
  }
  
  /// 可视化参数影响区域
  Future<void> visualizeParameterInfluence(NoseParameterType paramType) async {
    BeautifyLogger.logInfo(_logTag, 'visualizeParameterInfluence', '开始可视化参数影响区域 | 参数类型: $paramType');
    
    try {
      // 确保服务已初始化
      await ensureInitialized();
      
      final affectedPoints = _getAffectedFeaturePoints(paramType);
      
      if (affectedPoints.isEmpty) {
        BeautifyLogger.logWarning(_logTag, 'visualizeParameterInfluence', 
          '未找到受影响的特征点 | 参数类型: $paramType');
        return;
      }
      
      // 更新当前参数类型
      _currentParameterType = paramType;
      
      // 调用变形服务的可视化方法
      // 使用visualizeParameterInfluenceByName方法替代visualizeParameterInfluence方法
      final paramName = _getParameterNameFromType(paramType);
      await _transformationService.visualizeParameterInfluenceByName('nose', paramName, 0.0);
      
      BeautifyLogger.logInfo(_logTag, 'visualizeParameterInfluence', 
          '找到${affectedPoints.length}个受影响的特征点 | 参数类型: $paramType');
      
      // 通知监听器
      notifyListeners();
    } catch (e) {
      BeautifyLogger.logError(_logTag, 'visualizeParameterInfluence', 
          '可视化参数影响区域失败: $e');
    }
  }
  
  /// 获取受参数影响的特征点
  List<FeaturePoint> _getAffectedFeaturePoints(NoseParameterType type) {
    BeautifyLogger.logInfo(_logTag, '_getAffectedFeaturePoints', '开始获取受影响的特征点 | 参数类型: $type');
    
    // 模拟获取特征点的过程
    List<FeaturePoint> points = [];
    
    // 根据参数类型返回不同的特征点集合
    switch (type) {
      case NoseParameterType.bridgeHeight:
        // 鼻梁高度影响的特征点
        points = [
          const FeaturePoint(index: 1, x: 100, y: 100, name: '鼻梁中心点'),
          const FeaturePoint(index: 2, x: 95, y: 95, name: '鼻梁左侧点'),
          const FeaturePoint(index: 3, x: 105, y: 95, name: '鼻梁右侧点'),
        ];
        break;
      case NoseParameterType.bridgeWidth:
        // 鼻梁宽度影响的特征点
        points = [
          const FeaturePoint(index: 4, x: 90, y: 100, name: '鼻梁左边缘'),
          const FeaturePoint(index: 5, x: 110, y: 100, name: '鼻梁右边缘'),
        ];
        break;
      case NoseParameterType.tipAdjust:
        // 鼻尖调整影响的特征点
        points = [
          const FeaturePoint(index: 6, x: 100, y: 120, name: '鼻尖中心'),
          const FeaturePoint(index: 7, x: 95, y: 115, name: '鼻尖左侧'),
          const FeaturePoint(index: 8, x: 105, y: 115, name: '鼻尖右侧'),
        ];
        break;
      case NoseParameterType.nostrilWidth:
        // 鼻翅宽度影响的特征点
        points = [
          const FeaturePoint(index: 9, x: 85, y: 110, name: '左鼻翅'),
          const FeaturePoint(index: 10, x: 115, y: 110, name: '右鼻翅'),
        ];
        break;
      case NoseParameterType.baseHeight:
        // 鼻基抬高影响的特征点
        points = [
          const FeaturePoint(index: 11, x: 100, y: 130, name: '鼻基中心'),
          const FeaturePoint(index: 12, x: 90, y: 125, name: '鼻基左侧'),
          const FeaturePoint(index: 13, x: 110, y: 125, name: '鼻基右侧'),
        ];
        break;
      case NoseParameterType.tipLength:
        // 鼻尖长度影响的特征点
        points = [
          const FeaturePoint(index: 14, x: 100, y: 115, name: '鼻尖上端'),
          const FeaturePoint(index: 15, x: 100, y: 125, name: '鼻尖下端'),
        ];
        break;
      case NoseParameterType.tipHeight:
        // 鼻尖高度影响的特征点
        points = [
          const FeaturePoint(index: 16, x: 100, y: 118, name: '鼻尖高度中心'),
          const FeaturePoint(index: 17, x: 95, y: 118, name: '鼻尖高度左侧'),
          const FeaturePoint(index: 18, x: 105, y: 118, name: '鼻尖高度右侧'),
        ];
        break;
      case NoseParameterType.tipWidth:
        // 鼻尖宽度影响的特征点
        points = [
          const FeaturePoint(index: 19, x: 95, y: 120, name: '鼻尖宽度左侧'),
          const FeaturePoint(index: 20, x: 105, y: 120, name: '鼻尖宽度右侧'),
        ];
        break;
      case NoseParameterType.nostrilSize:
        // 鼻孔大小影响的特征点
        points = [
          const FeaturePoint(index: 21, x: 90, y: 115, name: '左鼻孔中心'),
          const FeaturePoint(index: 22, x: 110, y: 115, name: '右鼻孔中心'),
        ];
        break;
      case NoseParameterType.baseWidth:
        // 鼻基宽度影响的特征点
        points = [
          const FeaturePoint(index: 23, x: 85, y: 130, name: '鼻基宽度左端'),
          const FeaturePoint(index: 24, x: 115, y: 130, name: '鼻基宽度右端'),
        ];
        break;
    }
    
    // 记录日志
    if (points.isEmpty) {
      BeautifyLogger.logWarning(_logTag, '_getAffectedFeaturePoints', '未找到受影响的特征点 | 参数类型: $type');
    } else {
      BeautifyLogger.logInfo(_logTag, '_getAffectedFeaturePoints', '找到${points.length}个受影响的特征点 | 参数类型: $type');
      
      // 输出每个特征点的详细信息，便于调试
      for (int i = 0; i < points.length; i++) {
        final point = points[i];
        BeautifyLogger.logDebug(_logTag, '_getAffectedFeaturePoints', '特征点[$i]: index=${point.index}, 名称=${point.name}, 坐标=(${point.x}, ${point.y})');
      }
    }
    
    return points;
  }
  
  /// 重置所有参数
  void resetAllParameters() {
    _currentParameterType = null;
    _previewImageData = null;
    _medicalAdvice.clear();
    
    BeautifyLogger.logInfo(_logTag, 'resetAllParameters', '所有参数已重置');
    
    notifyListeners();
  }
  
  /// 更新参数值
  void updateParameter(NoseParameterType type, double value, {bool? isIncreasing}) {
    BeautifyLogger.flowStart(_logTag, 'updateParameter');
    BeautifyLogger.logInfo(_logTag, 'updateParameter', '更新参数 | 类型: $type, 值: $value, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"}');
    
    try {
      // 获取参数对象
      final parameter = _parameters.firstWhere((p) => p.type == type);
      
      // 更新参数值
      parameter.value = value;
      
      // 检查特征点数据是否存在
      final hasFeaturePoints = _checkFeaturePointsExist(type);
      if (!hasFeaturePoints) {
        BeautifyLogger.logWarning(_logTag, 'updateParameter', '特征点数据不存在，触发参数选择 | 类型: $type');
        // 如果特征点数据不存在，触发参数选择逻辑
        selectParameter(type);
      } else {
        BeautifyLogger.logInfo(_logTag, 'updateParameter', '特征点数据存在，直接应用变形 | 类型: $type, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"}');
        // 如果特征点数据存在，直接应用变形，并传递变形方向信息
        _applyTransformation(type, value, isIncreasing: isIncreasing);
      }
      
      // 通知监听器
      notifyListeners();
    } catch (e) {
      BeautifyLogger.logError(_logTag, 'updateParameter', '更新参数失败: $e');
    }
    
    BeautifyLogger.flowEnd(_logTag, 'updateParameter');
  }
  
  /// 检查特征点数据是否存在
  bool _checkFeaturePointsExist(NoseParameterType type) {
    BeautifyLogger.logInfo(_logTag, '_checkFeaturePointsExist', '检查特征点数据 | 类型: $type');
    
    try {
      // 获取变形渲染器
      final renderer = _transformationService.getSimpleDeformationRenderer();
      if (renderer == null) {
        BeautifyLogger.logError(_logTag, '_checkFeaturePointsExist', '获取变形渲染器失败');
        return false;
      }
      
      // 获取特征点管理器
      final featurePointManager = renderer.getFeaturePointManager();
      if (featurePointManager == null) {
        BeautifyLogger.logError(_logTag, '_checkFeaturePointsExist', '获取特征点管理器失败');
        return false;
      }
      
      // 获取当前参数的特征点索引
      final indexes = featurePointManager.getCurrentParameterPointIndexes();
      
      final hasData = indexes.isNotEmpty;
      BeautifyLogger.logInfo(_logTag, '_checkFeaturePointsExist', 
          '特征点数据${hasData ? "存在" : "不存在"} | 类型: $type, 索引数量: ${indexes.length}');
      
      return hasData;
    } catch (e) {
      BeautifyLogger.logError(_logTag, '_checkFeaturePointsExist', '检查特征点数据失败: $e');
      return false;
    }
  }
  
  /// 应用变形
  /// 应用变形
  /// 
  /// 公共方法，用于应用当前选中的变形参数
  void applyTransformation({bool? isIncreasing}) {
    BeautifyLogger.flowStart(_logTag, 'applyTransformation');
    BeautifyLogger.logInfo(_logTag, 'applyTransformation', '开始应用当前选中的变形参数, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"}');
    
    try {
      // 检查当前选中的参数类型
      if (_selectedParameterType == null) {
        BeautifyLogger.logWarning(_logTag, 'applyTransformation', '未选中任何参数类型');
        return;
      }
      
      // 获取参数值
      final parameter = getParameterValue(_selectedParameterType!);
      if (parameter == null) {
        BeautifyLogger.logError(_logTag, 'applyTransformation', '未找到选中参数的值');
        return;
      }
      
      // 如果没有提供isIncreasing参数，则报错并退出
      if (isIncreasing == null) {
        BeautifyLogger.logError(_logTag, 'applyTransformation', '错误: 必须提供isIncreasing参数来确定变形方向');
        BeautifyLogger.flowEnd(_logTag, 'applyTransformation');
        return;
      }
      
      // 调用内部方法应用变形，传递isIncreasing参数
      _applyTransformation(_selectedParameterType!, parameter.value, isIncreasing: isIncreasing);
      
      BeautifyLogger.logInfo(_logTag, 'applyTransformation', '变形应用成功');
    } catch (e) {
      BeautifyLogger.logError(_logTag, 'applyTransformation', '应用变形失败: $e');
    }
    
    BeautifyLogger.flowEnd(_logTag, 'applyTransformation');
  }
  
  void _applyTransformation(NoseParameterType type, double value, {bool? isIncreasing}) {
    BeautifyLogger.flowStart(_logTag, '_applyTransformation');
    BeautifyLogger.logInfo(_logTag, '_applyTransformation', '应用变形 | 类型: $type, 值: $value, 点击按钮: ${isIncreasing != null ? (isIncreasing ? "加号" : "减号") : "未指定"}');
    
    try {
      // 获取参数名称
      final parameterName = _getParameterNameFromType(type);
      
      // 如果没有提供isIncreasing参数，则报错并退出
      if (isIncreasing == null) {
        BeautifyLogger.logError(_logTag, '_applyTransformation', '❌ 错误: 必须提供isIncreasing参数来确定变形方向');
        BeautifyLogger.flowEnd(_logTag, '_applyTransformation');
        return;
      }
      
      // 应用变形 - 使用visualizeParameterInfluenceByName方法，传递isIncreasing参数
      _transformationService.visualizeParameterInfluenceByName('nose', parameterName, value, isIncreasing: isIncreasing);
      
      BeautifyLogger.logInfo(_logTag, '_applyTransformation', '变形应用成功 | 类型: $type, 值: $value, 点击按钮: ${isIncreasing ? "加号" : "减号"}');
    } catch (e) {
      BeautifyLogger.logError(_logTag, '_applyTransformation', '应用变形失败: $e');
    }
    
    BeautifyLogger.flowEnd(_logTag, '_applyTransformation');
  }
  
  /// 将NoseParameterType转换为参数名称字符串
  String _getParameterNameFromType(NoseParameterType type) {
    switch (type) {
      case NoseParameterType.bridgeHeight:
        return 'nose_bridge_height';
      // 移除了bridgeWidth的情况，因为在当前的NoseParameterType枚举中不存在这个值
      case NoseParameterType.tipAdjust:
        return 'nose_tip_adjust';
      case NoseParameterType.nostrilWidth:
        return 'nostril_width';
      case NoseParameterType.baseHeight:
        return 'base_height';
      default:
        return 'nose_height';
    }
  }
  
  /// 获取参数值
  NoseParameter? getParameterValue(NoseParameterType type) {
    try {
      // 确保参数列表不为空
      if (_parameters.isEmpty) {
        BeautifyLogger.logError(_logTag, 'getParameterValue', '参数列表为空');
        return null;
      }
      
      // 使用显式类型转换确保类型安全
      for (final param in _parameters) {
        if (param.type == type) {
          return param;
        }
      }
      
      // 如果没有找到匹配的参数，返回null
      BeautifyLogger.logError(_logTag, 'getParameterValue', '未找到类型为 $type 的参数');
      return null;
    } catch (e) {
      BeautifyLogger.logError(_logTag, 'getParameterValue', '获取参数失败: $e');
      return null;
    }
  }
}
