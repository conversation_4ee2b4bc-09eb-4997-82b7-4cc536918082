import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

import '../../core/feature_points_data.dart';
import '../../core/feature_points_helper.dart';
import '../../core/image_deformation_engine.dart';
import '../../services/image_processing_service.dart';
import '../../utils/logger.dart';
import '../../widgets/preview_area/landmarks_overlay.dart';

/// 自定义TickerProvider实现
class _TransformationServiceTickerProvider extends TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) {
    return Ticker(onTick);
  }
}

/// 变形效果服务
/// 
/// 负责管理和应用面部变形效果
class TransformationService extends ChangeNotifier {
  // 单例模式
  static TransformationService? _instance;
  factory TransformationService() {
    _instance ??= TransformationService._internal();
    return _instance!;
  }
  
  // 私有构造函数
  TransformationService._internal() {
    // 初始化变形参数映射
    _transformationParams = Map.fromEntries(
      beautyAreaConfigs.keys.map((area) => MapEntry(area, {}))
    );
    
    // 初始化TickerProvider
    _tickerProvider = _TransformationServiceTickerProvider();
    
    // 强制刷新特征点配置缓存，确保使用最新的配置
    FeaturePointsHelper().forceRefreshConfigCache();
    Logger.log('TransformationService', 'constructor', '🔄 [INFO] 初始化服务');
  }
  
  // 自定义TickerProvider
  late final _TransformationServiceTickerProvider _tickerProvider;
  
  // 原始图像
  ui.Image? _originalImage;
  ui.Image? get originalImage => _originalImage;
  
  // 变形后的图像
  ui.Image? _transformedImage;
  ui.Image? get transformedImage => _transformedImage;
  
  // 当前图像路径
  String? _currentImagePath;
  
  // 特征点数据
  List<Map<String, dynamic>>? _featurePoints;
  List<Map<String, dynamic>>? get featurePoints => _featurePoints;
  
  // 原始特征点数据的备份
  List<Map<String, dynamic>>? _originalFeaturePoints;
  
  // 变形前特征点数据的备份
  List<Map<String, dynamic>>? _deformationFeaturePointsBackup;
  
  // 变形后的图像路径
  String? _transformedImagePath;
  String? get transformedImagePath => _transformedImagePath;
  
  // 变形后的图像路径（新版本）
  String? _deformedImagePath;
  String? get deformedImagePath => _deformedImagePath;
  
  // 变形状态
  bool _isDeforming = false;
  bool get isDeforming => _isDeforming;
  
  // 处理中状态，用于UI禁用控件
  bool _isProcessing = false;
  bool get isProcessing => _isProcessing || _isDeforming || _isLoadingTransformedImage;
  
  // 是否显示变形结果
  bool _isShowingDeformation = false;
  bool get isShowingDeformation => _isShowingDeformation;
  
  // 当前选中的面部区域
  String _selectedArea = '';
  String get selectedArea => _selectedArea;
  
  // 当前选中的参数
  String? _selectedParameter;
  String? get selectedParameter => _selectedParameter;
  
  // 变形参数映射
  late final Map<String, Map<String, double>> _transformationParams;
  
  // 变形完成标志
  bool _deformationCompleted = false;
  
  // 是否正在加载变形图像
  bool _isLoadingTransformedImage = false;
  
  // 当前变形数据
  final Map<int, Offset> _currentDeformations = {};
  
  // 特征区域类型
  FeatureAreaType _getFeatureAreaType(String area) {
    switch (area.toLowerCase()) {
      case 'face_contour':
        return FeatureAreaType.faceContour;
      case 'nose':
        return FeatureAreaType.nose;
      case 'eyes':
        return FeatureAreaType.eyes;
      case 'lips':
        return FeatureAreaType.lips;
      case 'anti_aging':
        return FeatureAreaType.antiAging;
      default:
        Logger.log('TransformationService', '_getFeatureAreaType', '⚠️ [ERROR] 未知区域类型: $area');
        return FeatureAreaType.unknown;
    }
  }
  
  // 从 feature_points_data.dart 获取区域特征点
  List<int> _getAreaPoints(String area) {
    final areaConfig = beautyAreaConfigs[area];
    if (areaConfig == null) {
      Logger.log('TransformationService', '_getAreaPoints', '⚠️ [ERROR] 未找到区域配置: $area');
      return [];
    }
    return areaConfig.region.boundaryPoints;
  }
  
  // 标准化区域名称
  String _normalizeAreaName(String area) {
    // 确保区域名称是小写的
    final normalizedArea = area.toLowerCase();
    
    // 检查是否是有效的区域名称
    if (!beautyAreaConfigs.containsKey(normalizedArea)) {
      Logger.log('TransformationService', 'setSelectedArea', '⚠️ [ERROR] 未知区域类型: $normalizedArea');
      return '';
    }
    
    return normalizedArea;
  }
  
  // 获取区域参数
  Map<String, double> getAreaParams(String area) {
    final normalizedArea = _normalizeAreaName(area);
    if (normalizedArea.isEmpty) {
      return {};
    }
    
    return _transformationParams[normalizedArea] ?? {};
  }
  
  // 设置原始图像
  void setOriginalImage(ui.Image image) {
    _originalImage = image;
    _transformedImage = null;
    _isShowingDeformation = false;
    _deformationCompleted = false;
    notifyListeners();
  }
  
  // 设置特征点数据
  void setFeaturePoints(List<Map<String, dynamic>> featurePoints, {String? imagePath}) {
    _featurePoints = featurePoints;
    
    // 备份原始特征点数据
    _originalFeaturePoints = featurePoints.map((point) => Map<String, dynamic>.from(point)).toList();
    
    // 设置当前图像路径
    if (imagePath != null) {
      _currentImagePath = imagePath;
    }
    
    notifyListeners();
  }
  
  // 设置选中的面部区域
  void setSelectedArea(String area) {
    final normalizedArea = _normalizeAreaName(area);
    if (normalizedArea.isEmpty) {
      return;
    }
    
    _selectedArea = normalizedArea;
    final areaType = _getFeatureAreaType(normalizedArea);
    
    // 清除选中的参数
    _selectedParameter = null;
    
    notifyListeners();
  }
  
  // 更新变形参数
  void updateTransformationParam(String area, String paramName, double value) {
    final normalizedArea = _normalizeAreaName(area);
    if (normalizedArea.isEmpty) {
      return;
    }
    
    // 确保区域参数映射存在
    _transformationParams[normalizedArea] ??= {};
    
    // 获取旧值
    final oldValue = _transformationParams[normalizedArea]![paramName] ?? 0.0;
    
    // 如果值没有变化，跳过处理
    if (oldValue == value) {
      Logger.log('TransformationService', 'updateTransformationParam', 
        '⚠️ [跳过] 参数值未变化 | 区域: $normalizedArea | 参数: $paramName | 值: $value');
      return;
    }
    
    // 更新参数值
    _transformationParams[normalizedArea]![paramName] = value;
    
    // 设置选中的区域和参数
    _selectedArea = normalizedArea;
    _selectedParameter = paramName;
    
    // 重置变形完成状态，允许新的变形处理
    _deformationCompleted = false;
    
    notifyListeners();
  }
  
  // 使用变形数据更新特征点
  void _updateFeaturePointsWithDeformations(Map<int, Offset> deformations) {
    if (_featurePoints == null || _featurePoints!.isEmpty) {
      Logger.log('TransformationService', '_updateFeaturePointsWithDeformations', 
        '⚠️ [ERROR] 无特征点数据');
      return;
    }
    
    if (deformations.isEmpty) {
      Logger.log('TransformationService', '_updateFeaturePointsWithDeformations', 
        '⚠️ [WARNING] 无变形数据');
      return;
    }
    
    Logger.log('TransformationService', '_updateFeaturePointsWithDeformations', 
      '🔄 [开始] 更新特征点数据 | 特征点数: ${_featurePoints!.length} | 变形点数: ${deformations.length}');
    
    // 更新特征点数据
    int updatedPoints = 0;
    for (final entry in deformations.entries) {
      final pointId = entry.key;
      final offset = entry.value;
      
      // 查找对应的特征点
      final pointIndex = _featurePoints!.indexWhere((point) => point['id'] == pointId);
      if (pointIndex != -1) {
        // 确保特征点有坐标数据
        if (_featurePoints![pointIndex].containsKey('x') && _featurePoints![pointIndex].containsKey('y')) {
          // 获取原始坐标
          final originalX = _featurePoints![pointIndex]['x'] as double;
          final originalY = _featurePoints![pointIndex]['y'] as double;
          
          // 应用变形偏移
          _featurePoints![pointIndex]['x'] = originalX + offset.dx;
          _featurePoints![pointIndex]['y'] = originalY + offset.dy;
          
          updatedPoints++;
        } else {
          Logger.log('TransformationService', '_updateFeaturePointsWithDeformations', 
            '⚠️ [ERROR] 特征点缺少坐标数据 | 点ID: $pointId');
        }
      }
    }
    
    Logger.log('TransformationService', '_updateFeaturePointsWithDeformations', 
      '✅ [完成] 更新特征点数据 | 更新点数: $updatedPoints / ${deformations.length}');
  }
  
  // 更新特征点覆盖层的状态
  void _updateLandmarksOverlay({
    bool? isAnimating,
    Color? animationColor,
    double? animationOpacity,
    bool? showAfterDeformation,
    Map<int, Offset>? deformations,
  }) {
    // 实现特征点覆盖层更新逻辑
  }
  
  // 设置特征点覆盖层的引用
  void setLandmarksKey(GlobalKey<LandmarksOverlayState> key) {
    // 实现设置特征点覆盖层引用的逻辑
    Logger.log('TransformationService', 'setLandmarksKey', '🔑 [设置] 特征点覆盖层引用');
  }
  
  // 设置选中的参数
  void setSelectedParameter(String paramName) {
    if (_selectedArea.isEmpty) {
      Logger.log('TransformationService', 'setSelectedParameter', '⚠️ [ERROR] 未选中区域');
      return;
    }
    
    _selectedParameter = paramName;
    
    // 获取参数对应的特征点
    List<int> pointsToHighlight = _getParameterPoints(_selectedArea, paramName);
    
    // 通知监听器
    notifyListeners();
    
    Logger.log('TransformationService', 'setSelectedParameter', '✅ [INFO] 参数设置完成');
  }
  
  // 获取参数对应的特征点
  List<int> _getParameterPoints(String area, String paramName) {
    // 实现获取参数对应特征点的逻辑
    return [];
  }
  
  // 重置所有变形
  void resetAllDeformations() {
    Logger.log('TransformationService', 'resetAllDeformations', '🔄 [入口] 重置所有变形');
    
    // 清空所有变形参数
    for (final area in _transformationParams.keys) {
      _transformationParams[area]?.clear();
    }
    
    // 重置变形状态
    _isShowingDeformation = false;
    _deformationCompleted = false;
    _isProcessing = false;
    
    // 清空变形数据
    _currentDeformations.clear();
    
    // 恢复原始特征点数据
    if (_originalFeaturePoints != null && _originalFeaturePoints!.isNotEmpty) {
      _featurePoints = _originalFeaturePoints!.map((point) => Map<String, dynamic>.from(point)).toList();
    }
    
    // 更新特征点覆盖层
    _updateLandmarksOverlay(
      isAnimating: false,
      showAfterDeformation: false,
      deformations: {},
    );
    
    // 通知监听器
    notifyListeners();
    
    Logger.log('TransformationService', 'resetAllDeformations', '✅ [完成] 所有变形已重置，缓存已清理');
  }
  
  // 重置所有变形参数 (别名方法，调用resetAllDeformations)
  void resetAllTransformations() {
    Logger.log('TransformationService', 'resetAllTransformations', '🔄 [入口] 调用resetAllDeformations');
    resetAllDeformations();
  }
  
  // 清除选中的参数
  void clearSelectedParameter() {
    _selectedParameter = null;
    notifyListeners();
  }
  
  @override
  void dispose() {
    // 清理资源
    super.dispose();
  }
}
