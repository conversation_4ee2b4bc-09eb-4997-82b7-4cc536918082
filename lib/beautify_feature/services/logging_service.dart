import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

/// 操作类型枚举，用于标识日志记录的操作类型
enum OperationType {
  /// 初始化操作
  initialize,
  
  /// 处理操作
  process,
  
  /// 结果操作
  result,
  
  /// 错误操作
  error,
  
  /// 警告操作
  warning,
  
  /// 用户交互操作
  userInteraction,
  
  /// 性能监控操作
  performance,
}

/// 日志级别枚举，用于标识日志记录的级别
enum LogLevel {
  /// 调试级别，用于开发调试
  debug,
  
  /// 信息级别，用于记录一般信息
  info,
  
  /// 警告级别，用于记录警告信息
  warning,
  
  /// 错误级别，用于记录错误信息
  error,
  
  /// 严重错误级别，用于记录严重错误信息
  critical,
}

/// 日志记录服务类，用于记录应用程序的日志信息
class LoggingService {
  /// 单例实例
  static final LoggingService _instance = LoggingService._internal();
  
  /// 日志文件路径
  String? _logFilePath;
  
  /// 日志文件对象
  File? _logFile;
  
  /// 日志缓冲区
  final List<String> _logBuffer = [];
  
  /// 缓冲区大小
  static const int _bufferSize = 50;
  
  /// 是否已初始化
  bool _isInitialized = false;
  
  /// 获取单例实例
  factory LoggingService() {
    return _instance;
  }
  
  /// 内部构造函数
  LoggingService._internal();
  
  /// 初始化日志服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final directory = await getApplicationDocumentsDirectory();
      final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.now());
      _logFilePath = '${directory.path}/beautifun_logs/beautify_$dateStr.log';
      
      // 确保日志目录存在
      final logDir = Directory('${directory.path}/beautifun_logs');
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }
      
      _logFile = File(_logFilePath!);
      if (!await _logFile!.exists()) {
        await _logFile!.create(recursive: true);
      }
      
      _isInitialized = true;
      
      // 记录初始化日志
      debug(
        module: 'LoggingService',
        function: 'initialize',
        operation: OperationType.initialize,
        message: '日志服务初始化完成',
      );
    } catch (e) {
      print('初始化日志服务失败: $e');
    }
  }
  
  /// 记录调试级别日志
  void debug({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) {
    _log(
      level: LogLevel.debug,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: data,
    );
  }
  
  /// 记录信息级别日志
  void info({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) {
    _log(
      level: LogLevel.info,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: data,
    );
  }
  
  /// 记录警告级别日志
  void warning({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) {
    _log(
      level: LogLevel.warning,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: data,
    );
  }
  
  /// 记录错误级别日志
  void error({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    final Map<String, dynamic> errorData = data ?? {};
    if (error != null) {
      errorData['error'] = error.toString();
    }
    if (stackTrace != null) {
      errorData['stackTrace'] = stackTrace.toString();
    }
    
    _log(
      level: LogLevel.error,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: errorData,
    );
  }
  
  /// 记录严重错误级别日志
  void critical({
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    final Map<String, dynamic> errorData = data ?? {};
    if (error != null) {
      errorData['error'] = error.toString();
    }
    if (stackTrace != null) {
      errorData['stackTrace'] = stackTrace.toString();
    }
    
    _log(
      level: LogLevel.critical,
      module: module,
      function: function,
      operation: operation,
      message: message,
      data: errorData,
    );
  }
  
  /// 内部日志记录方法
  void _log({
    required LogLevel level,
    required String module,
    required String function,
    required OperationType operation,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    final timestamp = DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(DateTime.now());
    final levelStr = level.toString().split('.').last.toUpperCase();
    final operationStr = operation.toString().split('.').last;
    
    String logMessage = '[$timestamp] [$levelStr] [$module] [$function] [$operationStr] | $message';
    
    if (data != null && data.isNotEmpty) {
      final dataStr = const JsonEncoder.withIndent('  ').convert(data);
      logMessage += '\nData: $dataStr';
    }
    
    // 打印到控制台（仅在调试模式下）
    if (kDebugMode) {
      print(logMessage);
    }
    
    // 添加到缓冲区
    _logBuffer.add('$logMessage\n');
    
    // 缓冲区满时写入文件
    if (_logBuffer.length >= _bufferSize) {
      await _flushBuffer();
    }
  }
  
  /// 将缓冲区内容写入文件
  Future<void> _flushBuffer() async {
    if (!_isInitialized || _logBuffer.isEmpty) return;
    
    try {
      await _logFile!.writeAsString(
        _logBuffer.join(''),
        mode: FileMode.append,
        flush: true,
      );
      _logBuffer.clear();
    } catch (e) {
      if (kDebugMode) {
        print('写入日志文件失败: $e');
      }
    }
  }
  
  /// 获取日志文件路径
  String? getLogFilePath() {
    return _logFilePath;
  }
  
  /// 获取最近的日志内容
  Future<String> getRecentLogs({int lines = 100}) async {
    if (!_isInitialized) return '';
    
    try {
      await _flushBuffer(); // 确保所有日志都写入文件
      
      final content = await _logFile!.readAsString();
      final allLines = content.split('\n');
      
      if (allLines.length <= lines) {
        return content;
      }
      
      return allLines.sublist(allLines.length - lines).join('\n');
    } catch (e) {
      if (kDebugMode) {
        print('读取日志文件失败: $e');
      }
      return '';
    }
  }
  
  /// 清除日志文件
  Future<void> clearLogs() async {
    if (!_isInitialized) return;
    
    try {
      await _flushBuffer(); // 确保所有日志都写入文件
      await _logFile!.writeAsString(''); // 清空文件
      
      info(
        module: 'LoggingService',
        function: 'clearLogs',
        operation: OperationType.process,
        message: '日志文件已清除',
      );
    } catch (e) {
      if (kDebugMode) {
        print('清除日志文件失败: $e');
      }
    }
  }
  
  /// 释放资源
  Future<void> dispose() async {
    await _flushBuffer(); // 确保所有日志都写入文件
  }
}
