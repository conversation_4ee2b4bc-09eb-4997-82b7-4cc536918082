import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import '../../core/image_deformation_engine.dart';
import '../../utils/logger.dart';

/// 图像变形服务
///
/// 负责处理图像变形操作，连接TransformationService与ImageDeformationEngine
class ImageDeformationService {
  // 单例模式
  static ImageDeformationService? _instance;
  factory ImageDeformationService() {
    _instance ??= ImageDeformationService._internal();
    return _instance!;
  }

  ImageDeformationService._internal() {
    Logger.log('ImageDeformationService', 'constructor', '🔄 [INFO] 初始化图像变形服务');
  }

  // 变形引擎
  final ImageDeformationEngine _deformationEngine = ImageDeformationEngine();

  /// 应用变形
  ///
  /// [imagePath] 原始图像路径
  /// [deformations] 变形数据，键为特征点索引，值为偏移量
  /// [landmarks] 特征点数据
  Future<DeformationResult> applyDeformation({
    required String imagePath,
    required Map<int, Offset> deformations,
    required List<Map<String, dynamic>> landmarks,
  }) async {
    Logger.log(
        'ImageDeformationService', 'applyDeformation', '🔄 [INFO] 开始应用变形');
    Logger.log('ImageDeformationService', 'applyDeformation',
        '📃 [DEBUG] 图像路径: $imagePath');
    Logger.log('ImageDeformationService', 'applyDeformation',
        '📃 [DEBUG] 变形点数: ${deformations.length}');
    Logger.log('ImageDeformationService', 'applyDeformation',
        '📃 [DEBUG] 特征点数: ${landmarks.length}');

    try {
      // 调用变形引擎
      final result = await _deformationEngine.applyDeformation(
        imagePath: imagePath,
        deformations: deformations,
        landmarks: landmarks,
      );

      if (result.success) {
        Logger.log(
            'ImageDeformationService', 'applyDeformation', '✅ [INFO] 变形成功');
        Logger.log('ImageDeformationService', 'applyDeformation',
            '📃 [DEBUG] 输出路径: ${result.outputPath}');
      } else {
        Logger.log('ImageDeformationService', 'applyDeformation',
            '❌ [ERROR] 变形失败: ${result.errorMessage}');
      }

      return result;
    } catch (e) {
      Logger.log(
          'ImageDeformationService', 'applyDeformation', '❌ [ERROR] 变形异常: $e');
      return DeformationResult(
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 准备特征点数据
  ///
  /// 将特征点数据转换为变形引擎需要的格式
  List<Map<String, dynamic>> prepareFeaturePoints(
      List<Map<String, dynamic>> featurePoints) {
    Logger.log(
        'ImageDeformationService', 'prepareFeaturePoints', '🔄 [INFO] 准备特征点数据');
    Logger.log('ImageDeformationService', 'prepareFeaturePoints',
        '📃 [DEBUG] 原始特征点数: ${featurePoints.length}');

    // 转换特征点格式
    final landmarks = featurePoints.map((point) {
      return {
        'index': point['index'],
        'x': point['x'],
        'y': point['y'],
      };
    }).toList();

    Logger.log(
        'ImageDeformationService', 'prepareFeaturePoints', '✅ [INFO] 特征点准备完成');
    return landmarks;
  }
}
