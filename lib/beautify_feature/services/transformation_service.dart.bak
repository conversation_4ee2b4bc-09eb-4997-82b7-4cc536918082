import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/core/simple_deformation_renderer.dart';
import 'package:beautifun/core/feature_point_manager.dart';
import 'package:beautifun/core/feature_points_helper.dart';
import 'package:beautifun/core/deformation_area_type.dart';
import 'package:flutter/scheduler.dart';
import 'package:beautifun/core/nose_transform.dart' as nt;

import '../../core/simple_deformation_renderer.dart';
import '../../core/models/feature_point.dart';
import '../../core/feature_points_helper.dart';
import '../../core/deformation_utils.dart';
import '../../utils/logger.dart';
import '../../widgets/preview_area/landmarks_overlay.dart';
import '../../beautify_feature/models/nose_parameter.dart';

/// 自定义TickerProvider实现
class _TransformationServiceTickerProvider extends TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) => Ticker(onTick);
}

/// 变形效果服务
/// 
/// 负责管理和应用面部变形效果
class TransformationService extends ChangeNotifier {
  static const String _logTag = 'TransformationService';
  
  // 单例实例
  static final TransformationService _instance = TransformationService._internal();
  static TransformationService get instance => _instance;
  
  // 工厂构造函数
  factory TransformationService() {
    return _instance;
  }
  
  // 原始图像路径
  String? _originalImagePath;
  
  // 变形后的图像路径
  String? _transformedImagePath;
  String? get transformedImagePath => _transformedImagePath;
  
  // 是否正在进行变形处理
  bool _isTransforming = false;
  bool get isTransforming => _isTransforming;
  
  // 是否正在变形
  bool _isDeforming = false;
  bool get isDeforming => _isDeforming;
  
  // 是否已加载图像
  bool _isImageLoaded = false;
  bool get isImageLoaded => _isImageLoaded;
  
  // 是否显示特征点
  bool _showFeaturePoints = true;
  bool get showFeaturePoints => _showFeaturePoints;
  set showFeaturePoints(bool value) {
    if (_showFeaturePoints != value) {
      _showFeaturePoints = value;
      notifyListeners();
    }
  }
  
  // 原始图像
  ui.Image? _originalImage;
  ui.Image? get originalImage => _originalImage;
  
  // 特征点数据
  List<Map<String, dynamic>>? _featurePoints;
  List<Map<String, dynamic>>? get featurePoints => _featurePoints;
  
  // 当前选中的面部区域
  String _selectedArea = '';
  String get selectedArea => _selectedArea;
  
  // 当前选中的参数
  String? _selectedParameter;
  String? get selectedParameter => _selectedParameter;
  
  // 变形参数
  final Map<String, Map<String, double>> _transformationParams = {};
  
  // 当前变形列表
  final List<Map<String, dynamic>> _currentDeformations = [];
  
  // 变形是否完成
  bool _deformationCompleted = false;
  
  // 特征点标记的GlobalKey
  GlobalKey<LandmarksOverlayState>? _landmarksKey;
  
  // 动画控制器
  late final AnimationController _animationController;
  
  // 特征点缓存
  final Map<String, List<FeaturePoint>> _featurePointCache = {};
  
  // 特征点ID缓存
  final Map<String, List<int>> _featurePointIdsCache = {};
  
  // 区域点缓存
  final Map<String, List<Offset>> _areaPointsCache = {};
  
  // 特征点ID映射表
  final Map<int, String> _featurePointIdMap = {};
  
  // 变形区域渲染器
  SimpleDeformationRenderer? _deformationAreaRenderer;
  
  // 初始化标志
  bool _initialized = false;
  Size? _imageSize;
  
  List<Map<String, dynamic>>? _pendingFeaturePoints;
  
  // 内部构造函数
  TransformationService._internal() : 
    _animationController = AnimationController(
      vsync: _TransformationServiceTickerProvider(),
      duration: const Duration(milliseconds: 300),
    ) {
    _initFeaturePointIdMap();
    
    Logger.i(_logTag, '初始化单例实例');
  }
  
  /// 确保服务已初始化
  Future<void> ensureInitialized() async {
    if (!_initialized) {
      Logger.logInfo(_logTag, 'ensureInitialized', '等待初始化完成');
      await initialize();
    }
  }
  
  // 初始化特征点ID映射表
  void _initFeaturePointIdMap() {
    // 示例映射，实际应该从配置或API获取
    _featurePointIdMap[1] = 'nose_tip';
    _featurePointIdMap[2] = 'nose_bridge_top';
    _featurePointIdMap[3] = 'nose_bridge_bottom';
    _featurePointIdMap[4] = 'nose_left_contour';
    _featurePointIdMap[5] = 'nose_right_contour';
    
    Logger.logInfo(_logTag, '_initFeaturePointIdMap', '初始化特征点ID映射表: ${_featurePointIdMap.length}个映射');
  }
  
  /// 初始化服务
  Future<void> initialize() async {
    Logger.flowStart(_logTag, '初始化');
    Logger.flow(_logTag, '初始化', '🔍 [开始] 初始化变形系统基础组件');
    
    if (_initialized) {
      Logger.flow(_logTag, '初始化', 'ℹ️ [信息] 变形系统已经初始化，跳过');
      Logger.flowEnd(_logTag, '初始化');
      return;
    }
    
    try {
      // 设置默认图像尺寸
      _imageSize = const Size(200, 200);
      Logger.flow(_logTag, '初始化', 'ℹ️ [信息] 设置默认图像尺寸: $_imageSize');
      
      // 初始化变形区域渲染器
      _initializeDeformationRenderer();
      
      // 标记为已初始化
      _initialized = true;
      
      Logger.flow(_logTag, '初始化', '✅ [完成] 变形系统已准备就绪，等待用户操作');
      Logger.flowEnd(_logTag, '初始化');
      
      // 通知监听器
      notifyListeners();
    } catch (e) {
      Logger.flow(_logTag, '初始化', '❌ [错误] 变形系统初始化失败: $e');
      Logger.flowEnd(_logTag, '初始化');
      rethrow;
    }
  }
  
  void _initializeDeformationRenderer() {
    Logger.flowStart(_logTag, '准备渲染器');
    Logger.flow(_logTag, '准备渲染器', '🔍 [开始] 创建特征点管理器');
    
    if (_deformationAreaRenderer != null) {
      Logger.flow(_logTag, '准备渲染器', 'ℹ️ [信息] 变形区域渲染器已存在，跳过初始化');
      Logger.flowEnd(_logTag, '准备渲染器');
      return;
    }
    
    // 初始化渲染器
    _initializeRenderer();
    
    Logger.flow(_logTag, '准备渲染器', 'ℹ️ [信息] 创建变形渲染器(不可见状态)');
    
    // 确保渲染器可见
    _deformationAreaRenderer?.setVisible(false); // 初始化时默认不可见，直到用户选择参数
    
    // 设置默认图像尺寸，确保绘制器能够正常工作
    final defaultSize = _imageSize ?? const Size(400, 400);
    Logger.flow(_logTag, '准备渲染器', 'ℹ️ [信息] 设置图像尺寸: $defaultSize');
    _deformationAreaRenderer?.setImageSize(defaultSize);
    
    // 不再设置默认区域和参数，等待用户实际选择
    Logger.flow(_logTag, '准备渲染器', '✅ [完成] 渲染器已准备就绪，等待用户选择参数');
    
    // 检查是否有待处理的特征点数据
    if (_pendingFeaturePoints != null && _pendingFeaturePoints!.isNotEmpty) {
      Logger.flow(_logTag, '准备渲染器', 'ℹ️ [信息] 发现待处理的特征点数据，应用这些数据');
      
      // 临时保存待处理的特征点数据
      final pendingPoints = _pendingFeaturePoints!;
      // 清空待处理数据，避免重复应用
      _pendingFeaturePoints = null;
      
      Logger.flow(_logTag, '准备渲染器', '✅ [完成] 渲染器已设置，正在应用待处理的特征点');
      Logger.flowEnd(_logTag, '准备渲染器');
      
      // 应用特征点数据
      setFeaturePoints(pendingPoints);
    } else {
      Logger.flow(_logTag, '准备渲染器', '✅ [完成] 渲染器已设置');
      Logger.flowEnd(_logTag, '准备渲染器');
    }
  }
  
  void _initializeRenderer() {
    if (_deformationAreaRenderer == null) {
      // 创建特征点管理器
      final featurePointManager = FeaturePointManager();
      
      // 创建变形区域渲染器，传入特征点管理器和当前服务实例
      _deformationAreaRenderer = SimpleDeformationRenderer(
        featurePointManager: featurePointManager,
        transformationService: this,
      );
      
      // 设置渲染器属性
      _deformationAreaRenderer?.setVisible(false); // 初始化时默认不可见，直到用户选择参数
      
      // 设置默认图像尺寸
      final defaultSize = _imageSize ?? const Size(400, 400);
      _deformationAreaRenderer?.setImageSize(defaultSize);
      
      // 不再设置默认区域和参数，等待用户实际选择
      
      // 检查是否有待处理的特征点数据
      if (_pendingFeaturePoints != null && _pendingFeaturePoints!.isNotEmpty) {
        
        // 临时保存待处理的特征点数据
        final pendingPoints = _pendingFeaturePoints!;
        // 清空待处理数据，避免重复应用
        _pendingFeaturePoints = null;
        
        // 应用特征点数据，但避免递归调用
        // 直接处理特征点数据而不是调用setFeaturePoints
        _applyPendingFeaturePoints(pendingPoints);
      }
    }
  }
  
  // 直接应用特征点数据，避免递归调用
  void _applyPendingFeaturePoints(List<Map<String, dynamic>> points) {
    
    if (_deformationAreaRenderer == null) {
      
      // 存储特征点数据，以便稍后使用
      _pendingFeaturePoints = points;
      
      return;
    }
    
    // 获取特征点管理器
    final featurePointManager = _deformationAreaRenderer!.getFeaturePointManager();
    if (featurePointManager == null) {
      
      return;
    }
    
    // 将特征点数据转换为 FeaturePoint 对象列表
    List<FeaturePoint> featurePointObjects = [];
    for (var point in points) {
      try {
        if (point.containsKey('index') && point.containsKey('x') && point.containsKey('y')) {
          var index = point['index'] as int;
          var name = point['name'] as String?;
          var x = (point['x'] as num).toDouble();
          var y = (point['y'] as num).toDouble();
          var z = point['z'] != null ? (point['z'] as num).toDouble() : 0.0;
          var visibility = point['visibility'] != null ? (point['visibility'] as num).toDouble() : 1.0;
          var confidence = point['confidence'] != null ? (point['confidence'] as num).toDouble() : 1.0;
          
          var featurePoint = FeaturePoint(
            index: index,
            name: name,
            x: x,
            y: y,
            z: z,
            visibility: visibility,
            confidence: confidence,
          );
          
          featurePointObjects.add(featurePoint);
        }
      } catch (e) {
        
      }
    }
    
    // 更新特征点管理器中的特征点数据
    if (featurePointObjects.isNotEmpty) {
      
      featurePointManager.updateFeaturePoints(featurePointObjects);
      _deformationAreaRenderer!.updateFeaturePoints(featurePointObjects);
    } else {
      
    }
  }
  
  /// 设置原始图像路径
  Future<void> setOriginalImagePath(String path) async {
    if (_originalImagePath != path) {
      _originalImagePath = path;
      _isImageLoaded = true;
      
      Logger.logInfo(_logTag, 'setOriginalImagePath', '设置原始图像路径: $path');
      
      // 重置变形状态
      _resetTransformationState();
      
      notifyListeners();
    }
  }
  
  /// 重置变形状态
  void _resetTransformationState() {
    Logger.flowStart(_logTag, '重置变形状态');
    Logger.flow(_logTag, '重置变形状态', '🔍 [开始] 清除所有变形参数和状态');
    
    _transformedImagePath = null;
    _isTransforming = false;
    _isDeforming = false;
    _currentDeformations.clear();
    _transformationParams.clear();
    
    Logger.flow(_logTag, '重置变形状态', '✅ [完成] 变形状态已重置');
    Logger.flowEnd(_logTag, '重置变形状态');
  }
  
  /// 获取变形区域渲染器
  SimpleDeformationRenderer? getSimpleDeformationRenderer() {
    Logger.flowStart(_logTag, '获取变形区域渲染器');
    
    if (_deformationAreaRenderer == null) {
      Logger.flow(_logTag, '获取变形区域渲染器', '⚠️ [警告] 变形区域渲染器未初始化');
      Logger.flowEnd(_logTag, '获取变形区域渲染器');
    } else {
      Logger.flow(_logTag, '获取变形区域渲染器', '✅ [完成] 返回变形区域渲染器');
      Logger.flowEnd(_logTag, '获取变形区域渲染器');
    }
    
    return _deformationAreaRenderer;
  /// 设置变形区域渲染器
  void setDeformationAreaRenderer(SimpleDeformationRenderer renderer) {
    Logger.flowStart(_logTag, '设置变形区域渲染器');
    Logger.flow(_logTag, '设置变形区域渲染器', '🔍 [开始] 设置渲染器');
    
    _deformationAreaRenderer = renderer;
    
    // 检查是否有待处理的特征点数据
    if (_pendingFeaturePoints != null && _pendingFeaturePoints!.isNotEmpty) {
      Logger.flow(_logTag, '设置变形区域渲染器', 'ℹ️ [信息] 发现待处理的特征点数据，应用这些数据');
      
      // 临时保存待处理的特征点数据
      final pendingPoints = _pendingFeaturePoints!;
      // 清空待处理数据，避免重复应用
      _pendingFeaturePoints = null;
      
      Logger.flow(_logTag, '设置变形区域渲染器', '✅ [完成] 渲染器已设置，正在应用待处理的特征点');
      Logger.flowEnd(_logTag, '设置变形区域渲染器');
      
      // 应用特征点数据

      setFeaturePoints(pendingPoints);
    } else {
      Logger.flow(_logTag, '设置变形区域渲染器', '✅ [完成] 渲染器已设置');
      Logger.flowEnd(_logTag, '设置变形区域渲染器');
    }
  }
  
  /// 可视化参数影响
    Logger.flowStart(_logTag, '可视化参数影响');
    
    try {
      // 1. 获取渲染器
      final renderer = getSimpleDeformationRenderer();
      if (renderer == null) {
        Logger.flowError(_logTag, '可视化参数影响', '渲染器未初始化');
        Logger.flowEnd(_logTag, '可视化参数影响');
        return;
      }
      
      // 2. 获取参数信息
      String areaName = 'nose';
      String paramName = paramType.name;
      Logger.flow(_logTag, '可视化参数影响', '处理区域:$areaName 参数:$paramName');
      
      // 3. 获取特征点ID
      final pointIds = getFeaturePointIds(areaName, paramName);
      if (pointIds.isEmpty) {
        Logger.flowWarning(_logTag, '可视化参数影响', '参数($paramName)无关联特征点ID');
        Logger.flowEnd(_logTag, '可视化参数影响');
        return;
      }
      Logger.flow(_logTag, '可视化参数影响', '获取特征点ID:${pointIds.length}个');
      
      // 4. 检查特征点数据
      if (_featurePoints == null || _featurePoints!.isEmpty) {
        Logger.flowWarning(_logTag, '可视化参数影响', '特征点数据为空');
        Logger.flowEnd(_logTag, '可视化参数影响');
        return;
      }
      Logger.flow(_logTag, '可视化参数影响', '总特征点数量:${_featurePoints!.length}个');
      
      // 5. 筛选特征点
      final filteredPoints = _featurePoints!.where((point) => 
        point is Map && point.containsKey('index') && pointIds.contains(point['index'])).toList();
      
      if (filteredPoints.isEmpty) {
        Logger.flowWarning(_logTag, '可视化参数影响', '筛选后无有效特征点');
        Logger.flowEnd(_logTag, '可视化参数影响');
        return;
      }
      Logger.flow(_logTag, '可视化参数影响', '筛选特征点:${filteredPoints.length}个');
      
      // 6. 更新渲染器的特征点
      Logger.flow(_logTag, '可视化参数影响', '更新渲染器特征点');
      renderer.updateFeaturePoints(filteredPoints);
      
      // 7. 创建影响区域
      final influenceArea = DeformationUtils.createInfluenceArea(areaName, paramName);
      if (influenceArea.isEmpty) {
        Logger.flowWarning(_logTag, '可视化参数影响', '无法创建影响区域');
        Logger.flowEnd(_logTag, '可视化参数影响');
        return;
      }
      Logger.flow(_logTag, '可视化参数影响', '创建影响区域:${influenceArea.length}个点');
      
      // 8. 设置高亮区域
      Logger.flow(_logTag, '可视化参数影响', '设置高亮区域');
      renderer.setHighlightArea(influenceArea, areaName, paramName);
      
      // 9. 设置可见性
      Logger.flow(_logTag, '可视化参数影响', '设置渲染器可见性');
      renderer.setVisible(true);
      renderer.setShowFeaturePoints(true);
      
      Logger.flow(_logTag, '可视化参数影响', '参数可视化完成');
      Logger.flowEnd(_logTag, '可视化参数影响');
      
      // 10. 通知监听器
      notifyListeners();
    } catch (e) {
      Logger.flowError(_logTag, '可视化参数影响', '处理错误:$e');
      Logger.flowEnd(_logTag, '可视化参数影响');
    }
  }
  }
  
  /// 获取特征点ID
  List<int> getFeaturePointIds(String areaName, String paramName) {
    Logger.flowStart(_logTag, '获取特征点ID');
    Logger.flow(_logTag, '获取特征点ID', '🔍 [开始] 区域: $areaName, 参数: $paramName');
    
    // 检查缓存
    String cacheKey = '${areaName}_${paramName}';
    if (_featurePointIdsCache.containsKey(cacheKey)) {
      Logger.flow(_logTag, '获取特征点ID', 'ℹ️ [信息] 使用缓存数据');
      Logger.flowEnd(_logTag, '获取特征点ID');
      return _featurePointIdsCache[cacheKey]!;
    }
    
    List<int> result = [];
    
    // 创建特征点辅助工具实例
    final featurePointsHelper = FeaturePointsHelper();
    
    // 确保区域名称格式正确
    String area = areaName;
    if (area == 'face') {
      area = 'face_contour';
    }
    
    // 将 paramName 转换为 FeaturePointsHelper 中使用的参数名称格式
    String parameterName = paramName;
    
    // 鼻子参数名称映射
    if (area == 'nose') {
      if (paramName == 'bridgeHeight' || paramName == 'bridge_height') {
        parameterName = 'nasal_midline';  // 鼻梁高度对应鼻梁复合体
      } else if (paramName == 'bridgeWidth' || paramName == 'bridge_width') {
        parameterName = 'nasal_midline';  // 鼻梁宽度也对应鼻梁复合体
      } else if (paramName == 'tipAdjust' || paramName == 'tip_adjust' || paramName == 'tip_height') {
        parameterName = 'tip_complex';  // 鼻尖调整对应鼻尖复合体
      } else if (paramName == 'nostrilWidth' || paramName == 'nostril_width') {
        parameterName = 'alar_complex';  // 鼻孔宽度对应鼻翼复合体
      } else if (paramName == 'baseHeight' || paramName == 'base_height') {
        parameterName = 'base_complex';  // 鼻基高度对应鼻基底复合体
      }
    }
    // 眼睛参数名称映射
    else if (area == 'eyes') {
      if (paramName == 'eyeSize' || paramName == 'eye_size' || paramName == 'size') {
        parameterName = 'eye_size';
      } else if (paramName == 'eyeDistance' || paramName == 'eye_distance' || paramName == 'distance') {
        parameterName = 'eye_distance';
      }
    }
    // 嘴巴参数名称映射
    else if (area == 'mouth') {
      if (paramName == 'lipSize' || paramName == 'lip_size') {
        parameterName = 'lip_size';
      } else if (paramName == 'lipWidth' || paramName == 'lip_width') {
        parameterName = 'lip_width';
      }
    }
    
    // 根据区域和参数名称获取特征点ID
    try {
      result = featurePointsHelper.getFeaturePointIds(area, parameterName);
      
      // 缓存结果
      _featurePointIdsCache[cacheKey] = result;
      
      Logger.flow(_logTag, '获取特征点ID', '✅ [完成] 获取到 ${result.length} 个特征点ID');
    } catch (e) {
      Logger.flowError(_logTag, '获取特征点ID', '❌ [错误] 获取特征点ID失败: $e');
    }
    
    Logger.flowEnd(_logTag, '获取特征点ID');
    return result;
  }
  
  /// 可视化参数对区域的影响
  void visualizeParameterInfluenceForArea(String areaName, String paramName) {
    Logger.flowStart(_logTag, '可视化参数影响区域');
    Logger.flow(_logTag, '可视化参数影响区域', '🔍 [开始] 区域: $areaName, 参数: $paramName');
    
    try {
      // 获取渲染器
      final renderer = getSimpleDeformationRenderer();
      if (renderer == null) {
        Logger.flow(_logTag, '可视化参数影响区域', '⚠️ [错误] 变形区域渲染器未初始化');
        Logger.flowEnd(_logTag, '可视化参数影响区域');
        return;
      }
      
      // 获取特征点ID
      final pointIds = getFeaturePointIds(areaName, paramName);
      if (pointIds.isEmpty) {
        Logger.flow(_logTag, '可视化参数影响区域', '⚠️ [警告] 未找到特征点ID');
        Logger.flowEnd(_logTag, '可视化参数影响区域');
        return;
      }
      
      Logger.flow(_logTag, '可视化参数影响区域', 'ℹ️ [信息] 找到特征点ID: ${pointIds.length}个');
      
      // 检查特征点数据
      if (_featurePoints == null || _featurePoints!.isEmpty) {
        Logger.flow(_logTag, '可视化参数影响区域', '⚠️ [警告] 特征点数据为空');
        
        // 尝试从待处理数据中恢复
        if (_pendingFeaturePoints != null && _pendingFeaturePoints!.isNotEmpty) {
          Logger.flow(_logTag, '可视化参数影响区域', 'ℹ️ [信息] 发现待处理的特征点数据，应用这些数据');
          
          // 临时保存待处理的特征点数据
          final pendingPoints = _pendingFeaturePoints!;
          // 清空待处理数据，避免重复应用
          _pendingFeaturePoints = null;
          
          // 应用特征点数据
          setFeaturePoints(pendingPoints);
        } else {
          Logger.flow(_logTag, '可视化参数影响区域', '⚠️ [错误] 无可用特征点数据');
          Logger.flowEnd(_logTag, '可视化参数影响区域');
          return;
        }
      }
      
      // 筛选特征点数据
      final filteredPoints = _featurePoints!.where((point) => 
        point is Map && point.containsKey('index') && pointIds.contains(point['index'])).toList();
      
      if (filteredPoints.isEmpty) {
        Logger.flow(_logTag, '可视化参数影响区域', '⚠️ [警告] 筛选后的特征点为空');
        Logger.flowEnd(_logTag, '可视化参数影响区域');
        return;
      }
      
      Logger.flow(_logTag, '可视化参数影响区域', 'ℹ️ [信息] 筛选后的特征点: ${filteredPoints.length}个');
      
      // 更新渲染器特征点
      renderer.updateFeaturePoints(filteredPoints);
      
      // 创建并设置影响区域
      final influenceArea = DeformationUtils.createInfluenceArea(areaName, paramName);
      renderer.setHighlightArea(influenceArea, areaName, paramName);
      
      // 设置渲染器可见
      renderer.setVisible(true);
      renderer.setShowFeaturePoints(true);
      
      Logger.flow(_logTag, '可视化参数影响区域', '✅ [完成] 影响区域已可视化');
      Logger.flowEnd(_logTag, '可视化参数影响区域');
      
      // 通知监听器
      notifyListeners();
    } catch (e) {
      Logger.flow(_logTag, '可视化参数影响区域', '⚠️ [错误] 失败: $e');
      Logger.flowEnd(_logTag, '可视化参数影响区域');
    }
  }
  
  /// 设置原始图像
  void setOriginalImage(ui.Image image) {
    Logger.flowStart(_logTag, '导入图像');
    Logger.flow(_logTag, '导入图像', '🔍 [开始] 设置原始图像');
    
    _originalImage = image;
    
    // 记录图像尺寸信息
    String imageSizeInfo = '${image.width}x${image.height}';
    Logger.flow(_logTag, '导入图像', '✅ [完成] 图像尺寸: $imageSizeInfo');
    Logger.flowEnd(_logTag, '导入图像');
    
    notifyListeners();
  }
  
  /// 设置特征点数据
  void setFeaturePoints(List<Map<String, dynamic>> points, {String? imagePath}) {
    Logger.flowStart(_logTag, '设置特征点');
    Logger.flow(_logTag, '设置特征点', '🔍 [开始] 设置特征点数据，数量: ${points.length}');
    
    // 存储特征点数据
    _featurePoints = points;
    
    // 如果提供了图像路径，则更新原始图像路径
    if (imagePath != null) {
      _originalImagePath = imagePath;
      Logger.flow(_logTag, '设置特征点', 'ℹ️ [信息] 更新原始图像路径: $imagePath');
    }
    
    // 获取变形区域渲染器
    var renderer = getSimpleDeformationRenderer();
    if (renderer == null) {
      Logger.flow(_logTag, '设置特征点', 'ℹ️ [信息] 变形区域渲染器不可用，尝试初始化');
      
      // 尝试初始化渲染器
      _initializeDeformationRenderer();
      
      // 再次获取渲染器
      renderer = getSimpleDeformationRenderer();
      
      // 如果仍然无法获取渲染器，则存储特征点数据以便稍后使用
      if (renderer == null) {
        Logger.flow(_logTag, '设置特征点', 'ℹ️ [信息] 变形区域渲染器初始化失败，仅存储特征点数据');
        _pendingFeaturePoints = points;
        Logger.flow(_logTag, '设置特征点', 'ℹ️ [信息] 已存储特征点数据，等待渲染器初始化后使用');
        Logger.flowEnd(_logTag, '设置特征点');
      } else {
        Logger.flow(_logTag, '设置特征点', '✅ [成功] 变形区域渲染器已初始化');
      }
    }
    
    // 如果渲染器可用，则更新特征点数据
    if (renderer != null) {
      Logger.flow(_logTag, '设置特征点', 'ℹ️ [信息] 变形区域渲染器可用，更新特征点数据');
      
      // 获取特征点管理器
      final featurePointManager = renderer.getFeaturePointManager();
      if (featurePointManager != null) {
        Logger.flow(_logTag, '设置特征点', 'ℹ️ [信息] 特征点管理器可用，更新特征点数据');
        
        // 将特征点数据转换为 FeaturePoint 对象列表
        List<FeaturePoint> featurePointObjects = [];
        for (var point in points) {
          try {
            if (point.containsKey('index') && point.containsKey('x') && point.containsKey('y')) {
              var index = point['index'] as int;
              var name = point['name'] as String?;
              var x = (point['x'] as num).toDouble();
              var y = (point['y'] as num).toDouble();
              var z = point['z'] != null ? (point['z'] as num).toDouble() : 0.0;
              var visibility = point['visibility'] != null ? (point['visibility'] as num).toDouble() : 1.0;
              var confidence = point['confidence'] != null ? (point['confidence'] as num).toDouble() : 1.0;
              
              var featurePoint = FeaturePoint(
                index: index,
                name: name,
                x: x,
                y: y,
                z: z,
                visibility: visibility,
                confidence: confidence,
              );
              
              featurePointObjects.add(featurePoint);
            } else {
              Logger.flow(_logTag, '设置特征点', '⚠️ [警告] 特征点数据缺少必要字段: $point');
            }
          } catch (e) {
            Logger.flow(_logTag, '设置特征点', '⚠️ [错误] 处理特征点数据时发生错误: $e');
          }
        }
        
        // 只通过渲染器更新特征点，避免重复调用特征点管理器的updateFeaturePoints方法
        // 因为渲染器的updateFeaturePoints方法内部会调用特征点管理器的updateFeaturePoints方法
        if (featurePointObjects.isNotEmpty) {
          Logger.flow(_logTag, '设置特征点', 'ℹ️ [信息] 更新特征点，特征点数量: ${featurePointObjects.length}');
          
          renderer.updateFeaturePoints(featurePointObjects);
          
          Logger.flow(_logTag, '设置特征点', '✅ [完成] 特征点数据已设置');
        } else {
          Logger.flow(_logTag, '设置特征点', '⚠️ [警告] 转换后的特征点数据为空');
          Logger.flowError(_logTag, '设置特征点', '转换后的特征点数据为空，无法进行变形操作');
        }
        
        Logger.flowEnd(_logTag, '设置特征点');
      } else {
        Logger.flow(_logTag, '设置特征点', '⚠️ [警告] 特征点管理器不可用');
      }
    }
    
    notifyListeners();
  }
  
  /// 获取特征点数据
  List<dynamic>? getFeaturePoints() {
    Logger.flowStart(_logTag, '获取特征点');
    Logger.flow(_logTag, '获取特征点', '🔍 [开始] 获取特征点数据');
    
    // 如果已有特征点数据，直接返回
    if (_featurePoints != null && _featurePoints!.isNotEmpty) {
      Logger.flow(_logTag, '获取特征点', '✅ [完成] 返回特征点数据，数量: ${_featurePoints!.length}');
      Logger.flowEnd(_logTag, '获取特征点');
      return _featurePoints;
    }
    
    // 如果没有特征点数据，记录错误日志
    Logger.flow(_logTag, '获取特征点', '⚠️ [错误] 没有可用的特征点数据');
    Logger.flowError(_logTag, '获取特征点', '没有可用的特征点数据');
    
    // 返回空值
    return null;
  }
  
  /// 获取所有特征点
  List<FeaturePoint> getFeaturePoints() {
    Logger.flowStart(_logTag, '获取所有特征点');
    
    if (_featurePoints.isEmpty) {
      Logger.flowError(_logTag, '获取所有特征点', '特征点数据为空');
      Logger.flowEnd(_logTag, '获取所有特征点');
      return [];
    }
    
    Logger.flow(_logTag, '获取所有特征点', '特征点数量: ${_featurePoints.length}');
    Logger.flowEnd(_logTag, '获取所有特征点');
    return _featurePoints;
  }
  
  /// 设置特征点覆盖层的引用
  void setLandmarksKey(GlobalKey<LandmarksOverlayState> key) {
    Logger.flowStart(_logTag, '设置特征点覆盖层');
    Logger.flow(_logTag, '设置特征点覆盖层', '🔍 [开始] 设置特征点覆盖层引用');
    
    _landmarksKey = key;
    
    if (_pendingFeaturePoints != null) {
      Logger.flow(_logTag, '设置特征点覆盖层', 'ℹ️ [信息] 发现待处理的特征点数据，应用这些数据');
      setFeaturePoints(_pendingFeaturePoints!);
      _pendingFeaturePoints = null;
    }
    
    Logger.flow(_logTag, '设置特征点覆盖层', '✅ [完成] 特征点覆盖层引用已设置');
    Logger.flowEnd(_logTag, '设置特征点覆盖层');
  }
  
  /// 设置选中的面部区域
  void setSelectedArea(String area) {
    Logger.flowStart(_logTag, '设置区域');
    Logger.flow(_logTag, '设置区域', '🔍 [开始] 设置选中区域: $area');
    
    if (_selectedArea == area) {
      Logger.flow(_logTag, '设置区域', 'ℹ️ [信息] 区域未变化，跳过处理');
      Logger.flowEnd(_logTag, '设置区域');
      return;
    }
    
    _selectedArea = area;
    
    Logger.flow(_logTag, '设置区域', '✅ [完成] 区域已设置: $area');
    Logger.flowEnd(_logTag, '设置区域');
    notifyListeners();
  }
  
  /// 设置选中的参数
  void setSelectedParameter(String paramName) {
    Logger.flowStart(_logTag, '设置参数');
    Logger.flow(_logTag, '设置参数', '🔍 [开始] 设置选中参数: $paramName');
    
    if (_selectedParameter == paramName) {
      Logger.flow(_logTag, '设置参数', 'ℹ️ [信息] 参数未变化，跳过处理');
      Logger.flowEnd(_logTag, '设置参数');
      return;
    }
    
    _selectedParameter = paramName;
    
    // 检查参数是否有效
    if (paramName.isEmpty) {
      Logger.flow(_logTag, '设置参数', '⚠️ [警告] 参数名称为空');
      Logger.flowEnd(_logTag, '设置参数');
      return;
    }
    
    Logger.flow(_logTag, '设置参数', '✅ [完成] 参数已设置: $paramName');
    Logger.flowEnd(_logTag, '设置参数');
    notifyListeners();
  }
  
  /// 更新变形参数
  void updateTransformationParam(String area, String paramName, double value) {
    Logger.flowStart(_logTag, '更新变形参数');
    Logger.flow(_logTag, '更新变形参数', '🔍 [开始] 区域: $area, 参数: $paramName, 值: $value');
    
    // 确保区域参数映射存在
    _transformationParams[area] ??= {};
    
    // 检查参数是否有效
    if (area.isEmpty || paramName.isEmpty) {
      Logger.flow(_logTag, '更新变形参数', '⚠️ [警告] 区域或参数名称为空');
      Logger.flowError(_logTag, '更新变形参数', '区域或参数名称为空');
      return;
    }
    
    // 更新参数值
    _transformationParams[area]![paramName] = value;
    
    Logger.flow(_logTag, '更新变形参数', '✅ [完成] 参数已更新: $area - $paramName = $value');
    Logger.flowEnd(_logTag, '更新变形参数');
    notifyListeners();
  }
  }
  
  /// 获取区域点
  List<Offset> getAreaPointsByName(String areaName) {
    Logger.flowStart(_logTag, '获取区域点');
    Logger.flow(_logTag, '获取区域点', '🔍 [开始] 区域: $areaName');
    
    // 检查缓存
    if (_areaPointsCache.containsKey(areaName)) {
      Logger.flow(_logTag, '获取区域点', 'ℹ️ [信息] 使用缓存数据');
      Logger.flowEnd(_logTag, '获取区域点');
      return _areaPointsCache[areaName]!;
    }
    
    List<Offset> areaPoints = [];
    
    // 确保特征点数据不为空
    if (_featurePoints.isEmpty) {
      Logger.flowError(_logTag, '获取区域点', '❌ [错误] 特征点数据为空');
      Logger.flowEnd(_logTag, '获取区域点');
      return [];
    }
    
    // 根据区域名称获取特征点ID
    List<int> pointIds = [];
    
    // 创建特征点辅助工具实例
    final featurePointsHelper = FeaturePointsHelper();
    
    try {
      // 使用辅助工具获取区域点ID
      pointIds = featurePointsHelper.getAreaPointIds(areaName);
      
      // 根据ID获取特征点位置
      for (var id in pointIds) {
        var point = _featurePoints.firstWhere(
          (p) => p.id == id,
          orElse: () => FeaturePoint(id: -1, position: const Offset(0, 0)),
        );
        
        // 只添加有效的特征点
        if (point.id != -1) {
          areaPoints.add(point.position);
        }
      }
      
      // 缓存结果
      _areaPointsCache[areaName] = areaPoints;
      
      Logger.flow(_logTag, '获取区域点', '✅ [完成] 获取到 ${areaPoints.length} 个区域点');
    } catch (e) {
      Logger.flowError(_logTag, '获取区域点', '❌ [错误] 获取区域点失败: $e');
    }
    
    Logger.flowEnd(_logTag, '获取区域点');
    return areaPoints;
  }
  
  /// 重置所有变形效果
  void resetAllDeformations() {
    Logger.flowStart(_logTag, '重置所有变形效果');
    
    // 重置变形区域渲染器
    if (_deformationAreaRenderer != null) {
      _deformationAreaRenderer!.setVisible(false);
    }
    
    // 重置所有参数值
    _resetAllParameterValues();
    
    Logger.flowEnd(_logTag, '重置所有变形效果');
  }
  
  /// 重置所有参数值
  void _resetAllParameterValues() {
    Logger.flowStart(_logTag, '重置所有参数值');
    
    // 遍历所有区域
    _transformationParams.forEach((area, params) {
      // 遍历区域内的所有参数
      params.forEach((param, _) {
        // 重置参数值为0
        _transformationParams[area]![param] = 0.0;
      });
    });
    
    Logger.flowEnd(_logTag, '重置所有参数值');
  }
  
  /// 从缓存获取图像
  ui.Image? getCachedImage(String imagePath) {
    Logger.flowStart(_logTag, '获取缓存图像');
    Logger.flow(_logTag, '获取缓存图像', '🔍 [开始] 路径: $imagePath');
    
    // 如果当前图像路径与请求的图像路径相同，直接返回原始图像
    if (_originalImagePath == imagePath && _originalImage != null) {
      Logger.flow(_logTag, '获取缓存图像', '✅ [完成] 找到缓存图像');
      Logger.flowEnd(_logTag, '获取缓存图像');
      return _originalImage;
    }
    
    Logger.flow(_logTag, '获取缓存图像', '⚠️ [警告] 未找到缓存图像');
    Logger.flowEnd(_logTag, '获取缓存图像');
    return null;
  }
  
  /// 确保变形渲染器已初始化
  Future<void> ensureDeformationRendererInitialized() async {
    Logger.flowStart(_logTag, '确保渲染器初始化');
    Logger.flow(_logTag, '确保渲染器初始化', '🔍 [开始] 检查渲染器状态');
    
    if (_deformationAreaRenderer == null) {
      Logger.flow(_logTag, '确保渲染器初始化', 'ℹ️ [信息] 渲染器未初始化，开始初始化');
      _initializeDeformationRenderer();
      Logger.flow(_logTag, '确保渲染器初始化', '✅ [完成] 渲染器已初始化');
    } else {
      Logger.flow(_logTag, '确保渲染器初始化', 'ℹ️ [信息] 渲染器已初始化，无需操作');
    }
    
    Logger.flowEnd(_logTag, '确保渲染器初始化');
  }
  
  /// 确保变形区域渲染器已初始化
  Future<void> ensureDeformationRendererInitialized() async {
    Logger.flowStart(_logTag, '确保变形区域渲染器已初始化');
    
    if (_deformationAreaRenderer == null) {
      Logger.flow(_logTag, '确保变形区域渲染器已初始化', '渲染器未初始化，开始初始化');
      await initializeDeformationRenderer();
    } else {
      Logger.flow(_logTag, '确保变形区域渲染器已初始化', '渲染器已初始化');
    }
    
    Logger.flowEnd(_logTag, '确保变形区域渲染器已初始化');
    return;
  }
  
  /// 初始化变形渲染器
  Future<void> initializeDeformationRenderer() async {
    Logger.flowStart(_logTag, '初始化变形渲染器');
    
    // 创建特征点管理器
    final featurePointManager = FeaturePointManager(transformationService: this);
    
    // 创建变形区域渲染器
    _deformationAreaRenderer = SimpleDeformationRenderer(
      featurePointManager: featurePointManager,
      transformationService: this,
    );
    
    // 设置默认尺寸
    final defaultSize = Size(400, 600);
    _deformationAreaRenderer?.setImageSize(defaultSize);
    
    // 初始时隐藏渲染器
    _deformationAreaRenderer?.setVisible(false);
    
    Logger.flowEnd(_logTag, '初始化变形渲染器');
  }
}
