import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import '../../utils/logger.dart';
import '../../widgets/preview_area/landmarks_overlay.dart';

/// TransformationService 的动画扩展类
/// 
/// 包含所有与动画相关的方法
mixin TransformationServiceAnimations {
  // 动画状态
  bool _isPointAnimating = false;
  Color _pointAnimationColor = Colors.white;
  double _pointAnimationOpacity = 1.0;
  Timer? _pointAnimationTimer;
  int _pointAnimationRepeatCount = 0;
  double _animationProgress = 0.0;
  
  // 变形动画状态
  bool _isAnimatingDeformation = false;
  // _targetDeformations 在 TransformationService 中已经定义，这里不再重复定义
  Map<int, Offset> _currentDeformations = {};
  Timer? _deformationAnimationTimer;
  
  // 获取目标变形数据
  Map<int, Offset> get targetDeformations => {}; // 这个将由实现类提供
  
  // 特征点覆盖层引用
  GlobalKey<LandmarksOverlayState>? _landmarksKey;
  
  // 通知监听器方法 (由 ChangeNotifier 提供)
  void notifyListeners();
  
  // 缓动函数 - 使动画更平滑
  double _easeInOutCubic(double t) {
    return t < 0.5
        ? 4 * t * t * t
        : 1 - pow(-2 * t + 2, 3) / 2;
  }
  
  // 开始特征点动画
  void startPointAnimation(List<int> paramPoints) {
    // 重置重复计数
    _pointAnimationRepeatCount = 0;
    
    // 开始特征点动画循环
    animatePointsToTarget(paramPoints);
  }
  
  // 特征点从原位置到目标位置的动画
  void animatePointsToTarget(List<int> paramPoints) {
    // 如果已经完成3次动画，停止
    if (_pointAnimationRepeatCount >= 3) {
      _isPointAnimating = false;
      // 开始变形动画
      startDeformationAnimation(paramPoints);
      return;
    }
    
    // 增加重复计数
    _pointAnimationRepeatCount++;
    Logger.log('TransformationService', 'animatePointsToTarget', '🔄 [动画] 特征点动画 | 第$_pointAnimationRepeatCount次');
    
    // 重置动画状态
    _animationProgress = 0.0;
    _pointAnimationColor = Colors.white;
    _pointAnimationOpacity = 1.0;
    
    // 通知UI更新
    notifyListeners();
    
    // 创建动画定时器 - 1秒内完成动画
    final animationDuration = Duration(milliseconds: 1000);
    final startTime = DateTime.now();
    
    _pointAnimationTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      final now = DateTime.now();
      final elapsed = now.difference(startTime);
      
      // 计算动画进度 (0.0 到 1.0)
      _animationProgress = (elapsed.inMilliseconds / animationDuration.inMilliseconds).clamp(0.0, 1.0);
      
      // 使用缓动函数使动画更平滑
      final easedProgress = _easeInOutCubic(_animationProgress);
      
      // 更新当前变形数据 - 用于显示特征点位置
      final updatedDeformations = <int, Offset>{};
      
      // 确保处理所有参数点，而不仅仅是_targetDeformations中的点
      for (final pointIndex in paramPoints) {
        // 检查是否有目标变形数据
        if (targetDeformations.containsKey(pointIndex)) {
          final targetOffset = targetDeformations[pointIndex]!;
          final currentOffset = Offset.zero; // 从原始位置开始
          
          // 计算插值后的偏移
          final dx = currentOffset.dx + (targetOffset.dx - currentOffset.dx) * easedProgress;
          final dy = currentOffset.dy + (targetOffset.dy - currentOffset.dy) * easedProgress;
          
          updatedDeformations[pointIndex] = Offset(dx, dy);
        } else {
          // 如果没有目标变形数据，使用零偏移
          updatedDeformations[pointIndex] = Offset.zero;
        }
      }
      
      // 更新当前变形数据
      _currentDeformations = updatedDeformations;
      
      // 根据动画进度更新颜色
      if (_animationProgress > 0.5) {
        // 从白色渐变到红色
        _pointAnimationColor = Color.lerp(Colors.white, Colors.red, (_animationProgress - 0.5) * 2)!;
      }
      
      // 更新特征点覆盖层
      updateLandmarksOverlay(
        isAnimating: true,
        animationColor: _pointAnimationColor,
        animationOpacity: _pointAnimationOpacity,
        deformations: _currentDeformations
      );
      
      // 当动画完成时
      if (_animationProgress >= 1.0) {
        _pointAnimationOpacity = 1.0;
        
        // 创建淡出定时器
        final fadeOutDuration = Duration(milliseconds: 500);
        final fadeOutStartTime = DateTime.now();
        
        // 取消当前动画定时器
        timer.cancel();
        
        // 创建淡出定时器
        Timer.periodic(const Duration(milliseconds: 16), (fadeTimer) {
          final fadeElapsed = DateTime.now().difference(fadeOutStartTime);
          final fadeProgress = (fadeElapsed.inMilliseconds / fadeOutDuration.inMilliseconds).clamp(0.0, 1.0);
          
          // 更新不透明度
          _pointAnimationOpacity = 1.0 - fadeProgress;
          
          // 通知UI更新
          notifyListeners();
          
          // 淡出完成后，开始下一次动画或结束
          if (fadeProgress >= 1.0) {
            fadeTimer.cancel();
            
            // 继续下一次动画
            animatePointsToTarget(paramPoints);
          }
        });
      }
      
      // 通知UI更新
      notifyListeners();
    });
  }
  
  // 开始变形动画
  void startDeformationAnimation(List<int> paramPoints) {
    // 记录函数调用时间
    final functionStartTime = DateTime.now();
    
    // 记录当前状态
    Logger.log('TransformationService', 'startDeformationAnimation', '📊 [DEBUG] 当前状态:');
    Logger.log('TransformationService', 'startDeformationAnimation', '  • 参数点数量: ${paramPoints.length}');
    Logger.log('TransformationService', 'startDeformationAnimation', '  • 目标变形数据点数: ${targetDeformations.length}');
    Logger.log('TransformationService', 'startDeformationAnimation', '  • 当前变形数据点数: ${_currentDeformations.length}');
    Logger.log('TransformationService', 'startDeformationAnimation', '  • 当前是否在动画中: $_isAnimatingDeformation');
    
    if (targetDeformations.isEmpty) {
      Logger.log('TransformationService', 'startDeformationAnimation', '⚠️ [ERROR] 无目标变形数据');
      return;
    }
    
    Logger.log('TransformationService', 'startDeformationAnimation', '🔄 [开始] 面部变形动画 | 变形点数: ${targetDeformations.length}');
    
    // 检查参数点是否在变形数据中
    int matchedPoints = 0;
    double maxDeformationMagnitude = 0.0;
    int maxDeformationPoint = -1;
    
    Logger.log('TransformationService', 'startDeformationAnimation', '📊 [DEBUG] 参数点变形目标详情:');
    
    for (final pointIndex in paramPoints) {
      if (targetDeformations.containsKey(pointIndex)) {
        matchedPoints++;
        final offset = targetDeformations[pointIndex]!;
        final magnitude = sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
        
        // 记录最大变形量
        if (magnitude > maxDeformationMagnitude) {
          maxDeformationMagnitude = magnitude;
          maxDeformationPoint = pointIndex;
        }
        
        Logger.log('TransformationService', 'startDeformationAnimation', '  • 点 $pointIndex | 目标偏移: (${offset.dx.toStringAsFixed(2)}, ${offset.dy.toStringAsFixed(2)}) | 变形量: ${magnitude.toStringAsFixed(2)}');
      } else {
        Logger.log('TransformationService', 'startDeformationAnimation', '⚠️ [WARNING] 参数点不在变形数据中: $pointIndex');
      }
    }
    
    Logger.log('TransformationService', 'startDeformationAnimation', '📊 [DEBUG] 参数点匹配情况: $matchedPoints/${paramPoints.length} (${(matchedPoints / paramPoints.length * 100).toStringAsFixed(1)}%)');
    
    if (maxDeformationPoint >= 0) {
      Logger.log('TransformationService', 'startDeformationAnimation', '📊 [DEBUG] 最大变形点: $maxDeformationPoint | 变形量: ${maxDeformationMagnitude.toStringAsFixed(2)}');
    }
    
    // 输出变形数据统计信息
    int nonZeroDeformations = 0;
    double totalMagnitude = 0.0;
    double maxMagnitude = 0.0;
    int maxMagnitudePoint = -1;
    
    for (final entry in targetDeformations.entries) {
      final offset = entry.value;
      final magnitude = sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
      
      if (magnitude > 0.01) { // 忽略非常小的变形
        nonZeroDeformations++;
        totalMagnitude += magnitude;
        
        if (magnitude > maxMagnitude) {
          maxMagnitude = magnitude;
          maxMagnitudePoint = entry.key;
        }
      }
    }
    
    final averageMagnitude = nonZeroDeformations > 0 ? totalMagnitude / nonZeroDeformations : 0.0;
    
    Logger.log('TransformationService', 'startDeformationAnimation', '📊 [DEBUG] 变形数据统计:');
    Logger.log('TransformationService', 'startDeformationAnimation', '  • 总变形点数: ${targetDeformations.length}');
    Logger.log('TransformationService', 'startDeformationAnimation', '  • 有效变形点数: $nonZeroDeformations');
    Logger.log('TransformationService', 'startDeformationAnimation', '  • 平均变形量: ${averageMagnitude.toStringAsFixed(2)}');
    Logger.log('TransformationService', 'startDeformationAnimation', '  • 最大变形点: $maxMagnitudePoint | 变形量: ${maxMagnitude.toStringAsFixed(2)}');
    
    // 输出一些变形数据样本
    final samplePoints = targetDeformations.keys.take(5).toList();
    Logger.log('TransformationService', 'startDeformationAnimation', '📊 [DEBUG] 变形数据样本:');
    for (final pointIndex in samplePoints) {
      final offset = targetDeformations[pointIndex]!;
      final magnitude = sqrt(offset.dx * offset.dx + offset.dy * offset.dy);
      Logger.log('TransformationService', 'startDeformationAnimation', '  • 点 $pointIndex | 偏移: (${offset.dx.toStringAsFixed(2)}, ${offset.dy.toStringAsFixed(2)}) | 变形量: ${magnitude.toStringAsFixed(2)}');
    }
    
    // 设置动画状态
    _isAnimatingDeformation = true;
    _animationProgress = 0.0;
    
    // 通知UI更新
    notifyListeners();
    
    // 创建动画定时器 - 500毫秒内完成动画
    final animationDuration = Duration(milliseconds: 500);
    final startTime = DateTime.now();
    
    _deformationAnimationTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      final now = DateTime.now();
      final elapsed = now.difference(startTime);
      
      // 计算动画进度 (0.0 到 1.0)
      _animationProgress = (elapsed.inMilliseconds / animationDuration.inMilliseconds).clamp(0.0, 1.0);
      
      // 使用缓动函数使动画更平滑
      final easedProgress = _easeInOutCubic(_animationProgress);
      
      // 更新当前变形数据 - 用于显示特征点位置
      final updatedDeformations = <int, Offset>{};
      
      for (final entry in targetDeformations.entries) {
        final pointIndex = entry.key;
        final targetOffset = entry.value;
        
        // 计算插值后的偏移
        final dx = targetOffset.dx * easedProgress;
        final dy = targetOffset.dy * easedProgress;
        
        updatedDeformations[pointIndex] = Offset(dx, dy);
      }
      
      // 更新当前变形数据
      _currentDeformations = updatedDeformations;
      
      // 更新特征点覆盖层
      updateLandmarksOverlay(
        isAnimating: false,
        showAfterDeformation: true,
        deformations: _currentDeformations
      );
      
      // 当动画完成时
      if (_animationProgress >= 1.0) {
        _isAnimatingDeformation = false;
        timer.cancel();
        
        // 通知UI更新
        notifyListeners();
        
        // 记录函数执行时间
        final functionEndTime = DateTime.now();
        final executionTime = functionEndTime.difference(functionStartTime).inMilliseconds;
        Logger.log('TransformationService', 'startDeformationAnimation', '⏱️ [性能] 函数执行时间: ${executionTime}ms');
      }
      
      // 通知UI更新
      notifyListeners();
    });
  }
  
  // 更新特征点覆盖层
  void updateLandmarksOverlay({
    bool isAnimating = false,
    Color animationColor = Colors.white,
    double animationOpacity = 1.0,
    bool showAfterDeformation = false,
    Map<int, Offset>? deformations,
  }) {
    if (_landmarksKey?.currentState != null) {
      _landmarksKey!.currentState!.updatePointAnimation(
        isAnimating: isAnimating,
        animationColor: animationColor,
        animationOpacity: animationOpacity,
        showAfterDeformation: showAfterDeformation,
        deformations: deformations,
      );
    }
  }
  
  // 设置特征点覆盖层的引用
  void setLandmarksKey(GlobalKey<LandmarksOverlayState> key) {
    // 存储特征点覆盖层的引用，用于更新动画效果
    _landmarksKey = key;
    Logger.log('TransformationService', 'setLandmarksKey', '🔑 [设置] 特征点覆盖层引用');
  }
}
