import 'package:flutter/material.dart';

/// 三栏布局组件
/// 
/// 实现了固定比例的三栏布局：30% : 40% : 30%（左控制区 : 中央预览区 : 右交互区）
class ThreeColumnLayout extends StatelessWidget {
  /// 左侧面板组件
  final Widget leftPanel;
  
  /// 中央面板组件
  final Widget centerPanel;
  
  /// 右侧面板组件
  final Widget rightPanel;
  
  /// 布局比例常量
  static const double leftRatio = 0.3;
  static const double centerRatio = 0.4;
  static const double rightRatio = 0.3;
  
  const ThreeColumnLayout({
    Key? key,
    required this.leftPanel,
    required this.centerPanel,
    required this.rightPanel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取可用宽度
    final screenWidth = MediaQuery.of(context).size.width;
    
    // 计算各面板宽度
    final leftWidth = screenWidth * leftRatio;
    final centerWidth = screenWidth * centerRatio;
    final rightWidth = screenWidth * rightRatio;
    
    debugPrint('ThreeColumnLayout: Screen width: $screenWidth');
    debugPrint('ThreeColumnLayout: Panel widths - Left: $leftWidth, Center: $centerWidth, Right: $rightWidth');
    
    return Row(
      children: [
        // 左侧面板 - 完全透明
        SizedBox(
          width: leftWidth,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.transparent,
              border: Border(
                right: BorderSide(
                  color: Colors.transparent,
                  width: 0,
                ),
              ),
            ),
            child: leftPanel,
          ),
        ),
        
        // 中央面板
        SizedBox(
          width: centerWidth,
          child: centerPanel,
        ),
        
        // 右侧面板 - 完全透明
        SizedBox(
          width: rightWidth,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.transparent,
              border: Border(
                left: BorderSide(
                  color: Colors.transparent,
                  width: 0,
                ),
              ),
            ),
            child: rightPanel,
          ),
        ),
      ],
    );
  }
}
