import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../animations/nose_feature_animation.dart';
import '../../core_integration/nose_feature_connector.dart';
import '../../models/nose_feature_points.dart';
import '../../utils/logger.dart';
import '../../utils/performance_monitor.dart';

/// 鼻部特征点演示面板
class NoseFeatureDemoPanel extends StatefulWidget {
  /// 构造函数
  const NoseFeatureDemoPanel({Key? key}) : super(key: key);

  @override
  State<NoseFeatureDemoPanel> createState() => _NoseFeatureDemoPanelState();
}

class _NoseFeatureDemoPanelState extends State<NoseFeatureDemoPanel> with SingleTickerProviderStateMixin {
  /// 日志工具
  final BeautifyLogger _logger = BeautifyLogger();
  
  /// 性能监控工具
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  
  /// 鼻部特征点连接器
  final NoseFeatureConnector _connector = NoseFeatureConnector();
  
  /// 图像
  ui.Image? _image;
  
  /// 鼻部特征点
  NoseFeaturePoints? _noseFeaturePoints;
  
  /// 加载状态
  bool _isLoading = false;
  
  /// 错误信息
  String? _errorMessage;
  
  /// 动画控制器
  late AnimationController _animationController;
  
  /// 高亮特征点ID
  String? _highlightedPointId;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 5000),
    )..repeat();
    
    // 加载测试图像
    _loadTestImage();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  /// 加载测试图像
  Future<void> _loadTestImage() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    
    _logger.info(
      module: 'NoseFeatureDemoPanel',
      function: '_loadTestImage',
      operation: OperationType.process,
      message: '🔄 开始加载测试图像',
    );
    
    try {
      // 初始化连接器
      final initialized = await _connector.initialize();
      if (!initialized) {
        setState(() {
          _isLoading = false;
          _errorMessage = '连接器初始化失败';
        });
        return;
      }
      
      // 获取测试图像路径
      final testImagePath = await _connector.getTestImagePath();
      if (testImagePath == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = '未找到测试图像';
        });
        return;
      }
      
      // 加载图像
      final image = await _connector.loadImage(testImagePath);
      if (image == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = '图像加载失败';
        });
        return;
      }
      
      // 检测特征点
      final noseFeaturePoints = await _connector.detectNoseFeaturePoints(testImagePath);
      if (noseFeaturePoints == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = '特征点检测失败';
        });
        return;
      }
      
      // 更新状态
      setState(() {
        _image = image;
        _noseFeaturePoints = noseFeaturePoints;
        _isLoading = false;
      });
      
      _logger.info(
        module: 'NoseFeatureDemoPanel',
        function: '_loadTestImage',
        operation: OperationType.result,
        message: '✅ 测试图像加载成功',
        data: {
          'imageWidth': image.width,
          'imageHeight': image.height,
          'featurePointCount': noseFeaturePoints.points.length,
        },
      );
    } catch (e) {
      _logger.error(
        module: 'NoseFeatureDemoPanel',
        function: '_loadTestImage',
        operation: OperationType.process,
        message: '❌ 测试图像加载异常',
        data: {'error': e.toString()},
      );
      
      setState(() {
        _isLoading = false;
        _errorMessage = '加载异常: ${e.toString()}';
      });
    }
  }
  
  /// 高亮特征点
  void _highlightFeaturePoint(String pointId) {
    if (_noseFeaturePoints == null) return;
    
    setState(() {
      // 如果点击的是当前高亮的点，则取消高亮
      if (_highlightedPointId == pointId) {
        _highlightedPointId = null;
        _noseFeaturePoints!.clearHighlights();
      } else {
        _highlightedPointId = pointId;
        _noseFeaturePoints!.highlightPoint(pointId);
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('鼻部特征点演示'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _loadTestImage,
            tooltip: '重新加载',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }
  
  /// 构建主体内容
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('加载中...'),
          ],
        ),
      );
    }
    
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              '错误: $_errorMessage',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadTestImage,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }
    
    if (_image == null || _noseFeaturePoints == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.image_not_supported, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            const Text('无图像或特征点数据'),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadTestImage,
              child: const Text('加载测试图像'),
            ),
          ],
        ),
      );
    }
    
    return Column(
      children: [
        Expanded(
          child: Center(
            child: InteractiveViewer(
              minScale: 0.5,
              maxScale: 4.0,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 图像
                  RawImage(
                    image: _image,
                    fit: BoxFit.contain,
                  ),
                  
                  // 特征点动画
                  AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return CustomPaint(
                        size: Size(_image!.width.toDouble(), _image!.height.toDouble()),
                        painter: NoseFeatureAnimation(
                          noseFeaturePoints: _noseFeaturePoints!,
                          animationValue: _animationController.value,
                          showLabels: true,
                          showConnectingLines: true,
                          showTechEffect: true,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // 控制面板
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[200],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                '特征点列表',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 120,
                child: _buildFeaturePointList(),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  /// 构建特征点列表
  Widget _buildFeaturePointList() {
    if (_noseFeaturePoints == null) {
      return const Center(
        child: Text('无特征点数据'),
      );
    }
    
    final points = _noseFeaturePoints!.points;
    
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: points.length,
      itemBuilder: (context, index) {
        final point = points[index];
        final isHighlighted = point.isHighlighted;
        
        return GestureDetector(
          onTap: () => _highlightFeaturePoint(point.id),
          child: Container(
            width: 100,
            margin: const EdgeInsets.only(right: 8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isHighlighted ? Colors.blue.withOpacity(0.2) : Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isHighlighted ? Colors.blue : Colors.grey[300]!,
                width: isHighlighted ? 2 : 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  point.description ?? '未命名点',
                  style: TextStyle(
                    fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
                    color: isHighlighted ? Colors.blue : Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'ID: ${point.id}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '位置: (${point.position.dx.toStringAsFixed(1)}, ${point.position.dy.toStringAsFixed(1)})',
                  style: const TextStyle(
                    fontSize: 10,
                    color: Colors.grey,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: point.isPrimary ? Colors.green[100] : Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    point.isPrimary ? '主要' : '次要',
                    style: TextStyle(
                      fontSize: 10,
                      color: point.isPrimary ? Colors.green[800] : Colors.grey[800],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
