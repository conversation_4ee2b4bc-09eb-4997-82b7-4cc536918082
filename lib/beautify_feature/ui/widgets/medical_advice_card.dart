import 'package:flutter/material.dart';

/// 医学建议卡片组件
/// 
/// 显示医学建议的详细信息
class MedicalAdviceCard extends StatelessWidget {
  /// 建议标题
  final String title;
  
  /// 建议描述
  final String description;
  
  /// 风险等级 (0-3: 低、中、高、极高)
  final int riskLevel;
  
  /// 恢复时间
  final String recoveryTime;
  
  /// 风险等级标签
  final List<String> riskLabels;
  
  /// 风险等级颜色
  final List<Color> riskColors;
  
  const MedicalAdviceCard({
    Key? key,
    required this.title,
    required this.description,
    required this.riskLevel,
    required this.recoveryTime,
    this.riskLabels = const ['低风险', '中等风险', '高风险', '极高风险'],
    this.riskColors = const [
      Color(0xFF4CAF50), // 绿色 - 低风险
      Color(0xFFFFC107), // 黄色 - 中等风险
      Color(0xFFFF9800), // 橙色 - 高风险
      Color(0xFFF44336), // 红色 - 极高风险
    ],
  }) : assert(riskLevel >= 0 && riskLevel < 4),
       super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和风险等级
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF333333),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: riskColors[riskLevel].withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    riskLabels[riskLevel],
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: riskColors[riskLevel],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 描述
            Text(
              description,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
              ),
            ),
            const SizedBox(height: 12),
            
            // 恢复时间
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  size: 16,
                  color: Color(0xFF999999),
                ),
                const SizedBox(width: 4),
                const Text(
                  '恢复时间:',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF999999),
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  recoveryTime,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF7B61FF),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
