import 'package:flutter/material.dart';

/// 参数滑块组件
/// 
/// 提供统一的参数调整滑块界面
class ParameterSlider extends StatelessWidget {
  /// 参数标签
  final String label;
  
  /// 当前值
  final double value;
  
  /// 最小值
  final double min;
  
  /// 最大值
  final double max;
  
  /// 值变化回调
  final ValueChanged<double> onChanged;
  
  /// 分割数量
  final int divisions;
  
  /// 是否显示数值标签
  final bool showValueLabel;
  
  /// 是否启用滑块
  final bool enabled;
  
  const ParameterSlider({
    Key? key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.min = 0.0,
    this.max = 1.0,
    this.divisions = 100,
    this.showValueLabel = true,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
              ),
            ),
            if (showValueLabel)
              Text(
                value.toStringAsFixed(1),
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF7B61FF),
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: const Color(0xFF7B61FF),
            inactiveTrackColor: const Color(0xFFE8DBFF),
            thumbColor: const Color(0xFF7B61FF),
            overlayColor: const Color(0x297B61FF),
            valueIndicatorColor: const Color(0xFF7B61FF),
            valueIndicatorTextStyle: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            label: value.toStringAsFixed(1),
            onChanged: enabled ? onChanged : null,
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
