import 'package:flutter/material.dart';

/// 风险指示器组件
/// 
/// 显示风险等级并允许调整
class RiskIndicator extends StatelessWidget {
  /// 风险等级 (0-3: 低、中、高、极高)
  final int riskLevel;
  
  /// 风险等级变化回调
  final ValueChanged<int> onChanged;
  
  /// 风险等级标签
  final List<String> riskLabels;
  
  /// 风险等级颜色
  final List<Color> riskColors;
  
  const RiskIndicator({
    Key? key,
    required this.riskLevel,
    required this.onChanged,
    this.riskLabels = const ['低风险', '中等风险', '高风险', '极高风险'],
    this.riskColors = const [
      Color(0xFF4CAF50), // 绿色 - 低风险
      Color(0xFFFFC107), // 黄色 - 中等风险
      Color(0xFFFF9800), // 橙色 - 高风险
      Color(0xFFF44336), // 红色 - 极高风险
    ],
  }) : assert(riskLevel >= 0 && riskLevel < riskLabels.length),
       super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 风险等级标题
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '风险等级',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF666666),
              ),
            ),
            Text(
              riskLabels[riskLevel],
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: riskColors[riskLevel],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // 风险等级指示器
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.grey[200],
          ),
          child: Row(
            children: List.generate(
              riskLabels.length,
              (index) => Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.horizontal(
                      left: index == 0 ? const Radius.circular(4) : Radius.zero,
                      right: index == riskLabels.length - 1
                          ? const Radius.circular(4)
                          : Radius.zero,
                    ),
                    color: index <= riskLevel
                        ? riskColors[index]
                        : Colors.transparent,
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        
        // 风险等级选择器
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: List.generate(
            riskLabels.length,
            (index) => GestureDetector(
              onTap: () => onChanged(index),
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: index == riskLevel
                      ? riskColors[index]
                      : Colors.grey[300],
                  border: Border.all(
                    color: index == riskLevel
                        ? riskColors[index]
                        : Colors.grey[400]!,
                    width: 2,
                  ),
                ),
                child: index == riskLevel
                    ? const Icon(
                        Icons.check,
                        size: 16,
                        color: Colors.white,
                      )
                    : null,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
