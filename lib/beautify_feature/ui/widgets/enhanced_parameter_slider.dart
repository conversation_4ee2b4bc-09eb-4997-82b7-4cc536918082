import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../utils/logger.dart';
// 已移除DeformationProcessor的导入
import '../../../beautify_feature/services/transformation_service.dart';
import '../../../core/parameter_value_manager.dart';
import 'parameter_slider.dart';

/// 增强版参数滑块组件
/// 
/// 在原有参数滑块的基础上，添加对变形处理器的支持
class EnhancedParameterSlider extends StatefulWidget {
  /// 日志标签
  static const _logTag = 'EnhancedParameterSlider';
  
  /// 参数标签
  final String label;
  
  /// 当前值
  final double value;
  
  /// 最小值
  final double min;
  
  /// 最大值
  final double max;
  
  /// 值变化回调
  final ValueChanged<double> onChanged;
  
  /// 分割数量
  final int divisions;
  
  /// 是否显示数值标签
  final bool showValueLabel;
  
  /// 是否启用滑块
  final bool enabled;
  
  /// 区域类型
  final String areaType;
  
  /// 参数名称
  final String parameterName;
  
  /// 构造函数
  const EnhancedParameterSlider({
    Key? key,
    required this.label,
    required this.value,
    required this.onChanged,
    required this.areaType,
    required this.parameterName,
    this.min = -1.0,
    this.max = 1.0,
    this.divisions = 100,
    this.showValueLabel = true,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<EnhancedParameterSlider> createState() => _EnhancedParameterSliderState();
}

class _EnhancedParameterSliderState extends State<EnhancedParameterSlider> {
  /// 日志标签
  static const _logTag = 'EnhancedParameterSliderState';
  
  @override
  Widget build(BuildContext context) {
    return ParameterSlider(
      label: widget.label,
      value: widget.value,
      min: widget.min,
      max: widget.max,
      divisions: widget.divisions,
      showValueLabel: widget.showValueLabel,
      enabled: widget.enabled,
      onChanged: _onParameterValueChanged,
    );
  }
  
  /// 参数值变化处理
  void _onParameterValueChanged(double value) {
    Logger.flowStart(_logTag, '_onParameterValueChanged');
    
    // 【修复】直接从参数管理器获取当前参数的旧值
    final parameterValueManager = ParameterValueManager();
    final oldValue = parameterValueManager.containsParameter(widget.parameterName) 
        ? parameterValueManager.getValue(widget.parameterName) 
        : 0.0;
    final changeAmount = value - oldValue;
    final changeDirection = changeAmount > 0 ? "增大" : (changeAmount < 0 ? "减小" : "不变");
    
    // 1. 记录点击参数项名称和当前值
    Logger.flow(_logTag, '_onParameterValueChanged', '📊 1. 点击参数项: ${widget.parameterName} 当前值: $oldValue');
    
    // 2. 记录参数值变化和变形方向
    Logger.flow(_logTag, '_onParameterValueChanged', '📊 2. 参数值变动: 从 $oldValue 到 $value, 变化量: $changeAmount, 变形方向: $changeDirection');
    
    // 保持现有代码不变
    widget.onChanged(value);
    
    // 获取特征点管理器
    final featurePointManager = transformationService.featurePointManager;
    final featurePoints = featurePointManager.getFeaturePoints();
    
    // 检查特征点数据是否已经加载
    if (featurePoints.isEmpty) {
      Logger.flowWarning(_logTag, '_onParameterValueChanged', '❌ 特征点数据尚未加载，无法处理变形。请确保图像已经加载并完成特征点检测。');
      Logger.flowEnd(_logTag, '_onParameterValueChanged');
      return;
    }
    
    // 【修复】直接使用参数名，不传递区域类型
    TransformationService.instance.updateTransformationValue(
      '',  // 传递空字符串，不使用区域类型
      widget.parameterName,
      value,
    );
    
    Logger.flowEnd(_logTag, '_onParameterValueChanged');
  }
}
