import 'package:flutter/material.dart';
import '../widgets/risk_indicator.dart';
import '../widgets/medical_advice_card.dart';

/// 医美分析组件
/// 
/// 显示医美分析结果和建议
class MedicalAnalysis extends StatefulWidget {
  const MedicalAnalysis({Key? key}) : super(key: key);

  @override
  State<MedicalAnalysis> createState() => _MedicalAnalysisState();
}

class _MedicalAnalysisState extends State<MedicalAnalysis> {
  /// 风险等级
  int _riskLevel = 1; // 0-3: 低、中、高、极高
  
  /// 医学建议列表
  final List<Map<String, dynamic>> _medicalAdvices = [
    {
      'title': '面部轮廓调整',
      'description': '建议适度调整，保持面部自然比例。',
      'riskLevel': 1,
      'recoveryTime': '7-10天',
    },
    {
      'title': '鼻部微调',
      'description': '鼻梁高度适中，可以考虑轻微调整鼻尖。',
      'riskLevel': 1,
      'recoveryTime': '5-7天',
    },
    {
      'title': '眼部调整',
      'description': '眼距比例协调，可以考虑轻微增加眼睛大小。',
      'riskLevel': 2,
      'recoveryTime': '7-14天',
    },
  ];
  
  @override
  void initState() {
    super.initState();
    debugPrint('MedicalAnalysis initialized');
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分析标题
          const Text(
            '医学美容分析',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // 风险指示器
          RiskIndicator(
            riskLevel: _riskLevel,
            onChanged: (value) {
              setState(() {
                _riskLevel = value;
              });
              debugPrint('Risk level changed to: $value');
            },
          ),
          const SizedBox(height: 16),
          
          // 医学建议列表
          Expanded(
            child: ListView.builder(
              itemCount: _medicalAdvices.length,
              itemBuilder: (context, index) {
                final advice = _medicalAdvices[index];
                return MedicalAdviceCard(
                  title: advice['title'],
                  description: advice['description'],
                  riskLevel: advice['riskLevel'],
                  recoveryTime: advice['recoveryTime'],
                );
              },
            ),
          ),
          
          // 底部免责声明
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: const Text(
              '免责声明：本分析仅供参考，不构成医疗建议。请在进行任何医美手术前咨询专业医生。',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
