import 'package:flutter/material.dart';

/// 成功案例展示组件
/// 
/// 展示成功的医美案例
class CaseGallery extends StatefulWidget {
  const CaseGallery({Key? key}) : super(key: key);

  @override
  State<CaseGallery> createState() => _CaseGalleryState();
}

class _CaseGalleryState extends State<CaseGallery> {
  /// 当前选中的案例类型
  int _selectedCaseType = 0;
  
  /// 案例类型列表
  final List<String> _caseTypes = [
    '面部轮廓',
    '鼻部调整',
    '眼部调整',
    '唇部调整',
  ];
  
  /// 模拟案例数据
  final Map<String, List<Map<String, dynamic>>> _casesData = {
    '面部轮廓': [
      {
        'title': '面部轮廓优化',
        'description': '通过微调下颌线和颧骨，实现更加精致的面部轮廓。',
        'beforeImage': 'assets/cases/face_before_1.jpg',
        'afterImage': 'assets/cases/face_after_1.jpg',
      },
      {
        'title': '脸型修饰',
        'description': '调整脸型比例，打造完美的黄金分割面部。',
        'beforeImage': 'assets/cases/face_before_2.jpg',
        'afterImage': 'assets/cases/face_after_2.jpg',
      },
    ],
    '鼻部调整': [
      {
        'title': '鼻梁提升',
        'description': '提升鼻梁高度，塑造立体感。',
        'beforeImage': 'assets/cases/nose_before_1.jpg',
        'afterImage': 'assets/cases/nose_after_1.jpg',
      },
      {
        'title': '鼻尖精雕',
        'description': '精细调整鼻尖形态，提升整体和谐度。',
        'beforeImage': 'assets/cases/nose_before_2.jpg',
        'afterImage': 'assets/cases/nose_after_2.jpg',
      },
    ],
    '眼部调整': [
      {
        'title': '双眼皮塑形',
        'description': '创建自然双眼皮，放大眼睛视觉效果。',
        'beforeImage': 'assets/cases/eye_before_1.jpg',
        'afterImage': 'assets/cases/eye_after_1.jpg',
      },
      {
        'title': '眼角提升',
        'description': '提升外眼角，打造灵动眼神。',
        'beforeImage': 'assets/cases/eye_before_2.jpg',
        'afterImage': 'assets/cases/eye_after_2.jpg',
      },
    ],
    '唇部调整': [
      {
        'title': '唇形优化',
        'description': '调整唇部轮廓，塑造饱满自然的唇形。',
        'beforeImage': 'assets/cases/lip_before_1.jpg',
        'afterImage': 'assets/cases/lip_after_1.jpg',
      },
      {
        'title': '唇部丰满',
        'description': '增加唇部丰满度，打造性感唇形。',
        'beforeImage': 'assets/cases/lip_before_2.jpg',
        'afterImage': 'assets/cases/lip_after_2.jpg',
      },
    ],
  };
  
  @override
  void initState() {
    super.initState();
    debugPrint('CaseGallery initialized');
  }
  
  @override
  Widget build(BuildContext context) {
    // 获取当前选中类型的案例列表
    final currentCases = _casesData[_caseTypes[_selectedCaseType]] ?? [];
    
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Text(
            '成功案例展示',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // 案例类型选择器
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _caseTypes.length,
              itemBuilder: (context, index) {
                final isSelected = index == _selectedCaseType;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCaseType = index;
                    });
                    debugPrint('Selected case type: ${_caseTypes[index]}');
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? const Color(0xFF7B61FF)
                          : const Color(0xFFE8DBFF),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      _caseTypes[index],
                      style: TextStyle(
                        color: isSelected ? Colors.white : const Color(0xFF666666),
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          
          // 案例列表
          Expanded(
            child: currentCases.isEmpty
                ? const Center(
                    child: Text(
                      '暂无案例',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF999999),
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: currentCases.length,
                    itemBuilder: (context, index) {
                      final caseData = currentCases[index];
                      return _CaseCard(
                        title: caseData['title'],
                        description: caseData['description'],
                        beforeImage: caseData['beforeImage'],
                        afterImage: caseData['afterImage'],
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}

/// 案例卡片组件
class _CaseCard extends StatelessWidget {
  /// 案例标题
  final String title;
  
  /// 案例描述
  final String description;
  
  /// 前图像路径
  final String beforeImage;
  
  /// 后图像路径
  final String afterImage;
  
  const _CaseCard({
    Key? key,
    required this.title,
    required this.description,
    required this.beforeImage,
    required this.afterImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 8),
            
            // 描述
            Text(
              description,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
              ),
            ),
            const SizedBox(height: 12),
            
            // 前后对比图
            Row(
              children: [
                // 前图
                Expanded(
                  child: Column(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          height: 100,
                          color: Colors.grey[300],
                          child: Center(
                            child: Text(
                              '前',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '调整前',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 箭头
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: const Icon(
                    Icons.arrow_forward,
                    color: Color(0xFF7B61FF),
                  ),
                ),
                
                // 后图
                Expanded(
                  child: Column(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          height: 100,
                          color: Colors.grey[300],
                          child: Center(
                            child: Text(
                              '后',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '调整后',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
