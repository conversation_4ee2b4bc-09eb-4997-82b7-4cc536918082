import 'package:flutter/material.dart';
import 'medical_analysis.dart';
import 'animation_showcase.dart';
import 'case_gallery.dart';
import '../../../widgets/cache_preview_panel.dart';

/// 右侧交互面板
/// 
/// 包含医美分析区域、动画展示区和成功案例区
class InteractionPanel extends StatefulWidget {
  const InteractionPanel({Key? key}) : super(key: key);

  @override
  State<InteractionPanel> createState() => _InteractionPanelState();
}

class _InteractionPanelState extends State<InteractionPanel> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    debugPrint('InteractionPanel initialized');
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 选项卡栏
        Container(
          color: const Color(0xFFF5F0FF),
          child: TabBar(
            controller: _tabController,
            labelColor: const Color(0xFF7B61FF),
            unselectedLabelColor: Colors.grey,
            indicatorColor: const Color(0xFF7B61FF),
            tabs: const [
              Tab(text: '变形预览'),
              Tab(text: '医美分析'),
              Tab(text: '动画展示'),
              Tab(text: '成功案例'),
            ],
          ),
        ),
        
        // 选项卡内容
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: const [
              CachePreviewPanel(),
              MedicalAnalysis(),
              AnimationShowcase(),
              CaseGallery(),
            ],
          ),
        ),
      ],
    );
  }
}
