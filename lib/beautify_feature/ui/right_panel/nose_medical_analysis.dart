import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../utils/logger.dart';
import '../../services/nose_transformation_integration_service.dart';
import '../widgets/risk_indicator.dart';
import '../widgets/medical_advice_card.dart';

/// 鼻部医美分析组件
/// 
/// 显示鼻部变形的医学分析结果和建议
class NoseMedicalAnalysis extends StatefulWidget {
  const NoseMedicalAnalysis({Key? key}) : super(key: key);

  @override
  State<NoseMedicalAnalysis> createState() => _NoseMedicalAnalysisState();
}

class _NoseMedicalAnalysisState extends State<NoseMedicalAnalysis> {
  /// 风险等级
  int _riskLevel = 1; // 0-3: 低、中、高、极高
  
  @override
  void initState() {
    super.initState();
    BeautifyLogger.logInfo('NoseMedicalAnalysis', 'initState', '初始化鼻部医学分析组件');
  }
  
  /// 计算整体风险等级
  int _calculateOverallRiskLevel(List<String> advices) {
    if (advices.isEmpty) {
      return 0;
    }
    
    // 根据建议内容计算风险等级
    int highRiskCount = 0;
    int mediumRiskCount = 0;
    
    for (final advice in advices) {
      if (advice.contains('高风险') || advice.contains('不建议')) {
        highRiskCount++;
      } else if (advice.contains('中等风险') || advice.contains('谨慎')) {
        mediumRiskCount++;
      }
    }
    
    if (highRiskCount > 1) {
      return 3; // 极高风险
    } else if (highRiskCount == 1) {
      return 2; // 高风险
    } else if (mediumRiskCount > 0) {
      return 1; // 中等风险
    } else {
      return 0; // 低风险
    }
  }
  
  /// 解析医学建议
  List<Map<String, dynamic>> _parseMedicalAdvices(List<String> advices) {
    final result = <Map<String, dynamic>>[];
    
    if (advices.isEmpty) {
      // 默认建议
      return [
        {
          'title': '鼻部微调',
          'description': '暂无具体建议，请调整参数后查看。',
          'riskLevel': 0,
          'recoveryTime': '未知',
        }
      ];
    }
    
    // 解析每条建议
    for (int i = 0; i < advices.length; i++) {
      final advice = advices[i];
      
      // 提取标题（取前10个字符）
      String title = '鼻部调整建议 ${i + 1}';
      if (advice.length > 10) {
        title = advice.substring(0, 10) + '...';
      }
      
      // 提取风险等级
      int riskLevel = 0;
      if (advice.contains('高风险') || advice.contains('不建议')) {
        riskLevel = 2;
      } else if (advice.contains('中等风险') || advice.contains('谨慎')) {
        riskLevel = 1;
      }
      
      // 提取恢复时间
      String recoveryTime = '7-14天';
      if (advice.contains('恢复期') || advice.contains('恢复时间')) {
        final regex = RegExp(r'(\d+[-~]\d+[天周月])');
        final match = regex.firstMatch(advice);
        if (match != null) {
          recoveryTime = match.group(1) ?? recoveryTime;
        }
      }
      
      result.add({
        'title': title,
        'description': advice,
        'riskLevel': riskLevel,
        'recoveryTime': recoveryTime,
      });
    }
    
    return result;
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<NoseTransformationIntegrationService>(
      builder: (context, integrationService, child) {
        // 获取医学建议
        final medicalAdvices = integrationService.medicalAdvice;
        
        // 计算风险等级
        final calculatedRiskLevel = _calculateOverallRiskLevel(medicalAdvices);
        if (_riskLevel != calculatedRiskLevel) {
          _riskLevel = calculatedRiskLevel;
        }
        
        // 解析医学建议
        final parsedAdvices = _parseMedicalAdvices(medicalAdvices);
        
        return Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 分析标题
              const Text(
                '鼻部医学分析',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF7B61FF),
                ),
              ),
              const SizedBox(height: 16),
              
              // 风险指示器
              RiskIndicator(
                riskLevel: _riskLevel,
                onChanged: (value) {
                  setState(() {
                    _riskLevel = value;
                  });
                  BeautifyLogger.logInfo('NoseMedicalAnalysis', 'onRiskLevelChanged', '风险等级变更为: $value');
                },
              ),
              const SizedBox(height: 16),
              
              // 医学建议列表
              Expanded(
                child: ListView.builder(
                  itemCount: parsedAdvices.length,
                  itemBuilder: (context, index) {
                    final advice = parsedAdvices[index];
                    return MedicalAdviceCard(
                      title: advice['title'],
                      description: advice['description'],
                      riskLevel: advice['riskLevel'],
                      recoveryTime: advice['recoveryTime'],
                    );
                  },
                ),
              ),
              
              // 底部免责声明
              Container(
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: const Text(
                  '免责声明：本分析仅供参考，不构成医疗建议。请在进行任何医美手术前咨询专业医生。',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
