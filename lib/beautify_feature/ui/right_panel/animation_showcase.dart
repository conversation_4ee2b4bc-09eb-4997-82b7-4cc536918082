import 'package:flutter/material.dart';

/// 动画展示组件
/// 
/// 展示变形动画效果
class AnimationShowcase extends StatefulWidget {
  const AnimationShowcase({Key? key}) : super(key: key);

  @override
  State<AnimationShowcase> createState() => _AnimationShowcaseState();
}

class _AnimationShowcaseState extends State<AnimationShowcase> with SingleTickerProviderStateMixin {
  /// 动画控制器
  late AnimationController _controller;
  
  /// 变形动画
  late Animation<double> _morphAnimation;
  
  /// 当前选中的动画类型
  int _selectedAnimationType = 0;
  
  /// 动画类型列表
  final List<String> _animationTypes = [
    '面部轮廓',
    '鼻部调整',
    '眼部调整',
    '唇部调整',
  ];
  
  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    // 初始化变形动画
    _morphAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
    
    debugPrint('AnimationShowcase initialized');
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  /// 播放动画
  void _playAnimation() {
    if (_controller.isAnimating) {
      _controller.stop();
      debugPrint('Animation stopped');
    } else {
      _controller.reset();
      _controller.forward();
      debugPrint('Animation started: ${_animationTypes[_selectedAnimationType]}');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Text(
            '动画效果展示',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // 动画类型选择器
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _animationTypes.length,
              itemBuilder: (context, index) {
                final isSelected = index == _selectedAnimationType;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedAnimationType = index;
                    });
                    debugPrint('Selected animation type: ${_animationTypes[index]}');
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? const Color(0xFF7B61FF)
                          : const Color(0xFFE8DBFF),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      _animationTypes[index],
                      style: TextStyle(
                        color: isSelected ? Colors.white : const Color(0xFF666666),
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          
          // 动画预览区域
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Stack(
                children: [
                  // 动画内容
                  Center(
                    child: AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return _buildAnimationContent();
                      },
                    ),
                  ),
                  
                  // 播放按钮
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: FloatingActionButton(
                      mini: true,
                      backgroundColor: const Color(0xFF7B61FF),
                      onPressed: _playAnimation,
                      child: Icon(
                        _controller.isAnimating ? Icons.pause : Icons.play_arrow,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 动画进度条
          const SizedBox(height: 8),
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LinearProgressIndicator(
                    value: _controller.value,
                    backgroundColor: const Color(0xFFE8DBFF),
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Color(0xFF7B61FF),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '变形进度: ${(_controller.value * 100).toInt()}%',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF666666),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
  
  /// 构建动画内容
  Widget _buildAnimationContent() {
    // 根据选中的动画类型构建不同的动画内容
    switch (_selectedAnimationType) {
      case 0: // 面部轮廓
        return _buildFacialContourAnimation();
      case 1: // 鼻部调整
        return _buildNoseAnimation();
      case 2: // 眼部调整
        return _buildEyeAnimation();
      case 3: // 唇部调整
        return _buildLipAnimation();
      default:
        return Container();
    }
  }
  
  /// 构建面部轮廓动画
  Widget _buildFacialContourAnimation() {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.transparent,
        border: Border.all(
          color: const Color(0xFF7B61FF),
          width: 2,
        ),
      ),
      child: CustomPaint(
        painter: _FaceMorphPainter(
          progress: _morphAnimation.value,
        ),
      ),
    );
  }
  
  /// 构建鼻部动画
  Widget _buildNoseAnimation() {
    return Container(
      width: 100,
      height: 150,
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: Border.all(
          color: const Color(0xFF7B61FF),
          width: 2,
        ),
      ),
      child: CustomPaint(
        painter: _NoseMorphPainter(
          progress: _morphAnimation.value,
        ),
      ),
    );
  }
  
  /// 构建眼部动画
  Widget _buildEyeAnimation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 80,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.transparent,
            border: Border.all(
              color: const Color(0xFF7B61FF),
              width: 2,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: CustomPaint(
            painter: _EyeMorphPainter(
              progress: _morphAnimation.value,
            ),
          ),
        ),
        const SizedBox(width: 20),
        Container(
          width: 80,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.transparent,
            border: Border.all(
              color: const Color(0xFF7B61FF),
              width: 2,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: CustomPaint(
            painter: _EyeMorphPainter(
              progress: _morphAnimation.value,
            ),
          ),
        ),
      ],
    );
  }
  
  /// 构建唇部动画
  Widget _buildLipAnimation() {
    return Container(
      width: 120,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: Border.all(
          color: const Color(0xFF7B61FF),
          width: 2,
        ),
        borderRadius: BorderRadius.circular(30),
      ),
      child: CustomPaint(
        painter: _LipMorphPainter(
          progress: _morphAnimation.value,
        ),
      ),
    );
  }
}

/// 面部变形绘制器
class _FaceMorphPainter extends CustomPainter {
  final double progress;
  
  _FaceMorphPainter({required this.progress});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF7B61FF).withOpacity(0.5)
      ..style = PaintingStyle.fill;
    
    // 原始椭圆
    final originalRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: size.width * 0.8,
      height: size.height,
    );
    
    // 目标椭圆
    final targetRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: size.width * 0.6,
      height: size.height * 0.9,
    );
    
    // 当前椭圆
    final currentRect = Rect.lerp(originalRect, targetRect, progress)!;
    
    canvas.drawOval(currentRect, paint);
  }
  
  @override
  bool shouldRepaint(_FaceMorphPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

/// 鼻部变形绘制器
class _NoseMorphPainter extends CustomPainter {
  final double progress;
  
  _NoseMorphPainter({required this.progress});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF7B61FF).withOpacity(0.5)
      ..style = PaintingStyle.fill;
    
    // 绘制鼻梁
    final bridgePath = Path();
    bridgePath.moveTo(size.width / 2 - 10, 0);
    bridgePath.lineTo(size.width / 2 + 10, 0);
    bridgePath.lineTo(size.width / 2 + 20 * (1 - progress), size.height * 0.7);
    bridgePath.lineTo(size.width / 2 - 20 * (1 - progress), size.height * 0.7);
    bridgePath.close();
    
    canvas.drawPath(bridgePath, paint);
    
    // 绘制鼻尖
    canvas.drawCircle(
      Offset(size.width / 2, size.height * 0.8),
      10 + 5 * progress,
      paint,
    );
  }
  
  @override
  bool shouldRepaint(_NoseMorphPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

/// 眼部变形绘制器
class _EyeMorphPainter extends CustomPainter {
  final double progress;
  
  _EyeMorphPainter({required this.progress});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF7B61FF).withOpacity(0.5)
      ..style = PaintingStyle.fill;
    
    // 原始椭圆
    final originalRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: size.width * 0.5,
      height: size.height * 0.5,
    );
    
    // 目标椭圆
    final targetRect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: size.width * 0.7,
      height: size.height * 0.7,
    );
    
    // 当前椭圆
    final currentRect = Rect.lerp(originalRect, targetRect, progress)!;
    
    canvas.drawOval(currentRect, paint);
    
    // 绘制瞳孔
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      5 + 3 * progress,
      Paint()..color = Colors.black,
    );
  }
  
  @override
  bool shouldRepaint(_EyeMorphPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

/// 唇部变形绘制器
class _LipMorphPainter extends CustomPainter {
  final double progress;
  
  _LipMorphPainter({required this.progress});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF7B61FF).withOpacity(0.5)
      ..style = PaintingStyle.fill;
    
    // 上唇
    final upperLipPath = Path();
    upperLipPath.moveTo(0, size.height / 2);
    upperLipPath.quadraticBezierTo(
      size.width / 2,
      size.height / 2 - 20 * progress,
      size.width,
      size.height / 2,
    );
    upperLipPath.lineTo(size.width, size.height / 2 - 2);
    upperLipPath.quadraticBezierTo(
      size.width / 2,
      size.height / 2 - 2 - 15 * progress,
      0,
      size.height / 2 - 2,
    );
    upperLipPath.close();
    
    // 下唇
    final lowerLipPath = Path();
    lowerLipPath.moveTo(0, size.height / 2);
    lowerLipPath.quadraticBezierTo(
      size.width / 2,
      size.height / 2 + 25 * progress,
      size.width,
      size.height / 2,
    );
    lowerLipPath.lineTo(size.width, size.height / 2 + 2);
    lowerLipPath.quadraticBezierTo(
      size.width / 2,
      size.height / 2 + 2 + 20 * progress,
      0,
      size.height / 2 + 2,
    );
    lowerLipPath.close();
    
    canvas.drawPath(upperLipPath, paint);
    canvas.drawPath(lowerLipPath, paint);
  }
  
  @override
  bool shouldRepaint(_LipMorphPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
