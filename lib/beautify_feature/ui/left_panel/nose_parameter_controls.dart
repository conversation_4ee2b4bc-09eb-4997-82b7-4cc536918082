import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:beautifun/utils/logger.dart';
import 'package:beautifun/beautify_feature/models/nose_transformation_parameters.dart';
import 'package:beautifun/beautify_feature/services/nose_transformation_integration_service.dart';
import 'package:beautifun/beautify_feature/services/transformation_service.dart';
import '../../../core/deformation_area_renderer.dart';
import '../../services/transformation_service_enhanced.dart';

/// 鼻部参数控制面板
class NoseParameterControls extends StatelessWidget {
  const NoseParameterControls({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final integrationService = Provider.of<NoseTransformationIntegrationService>(context);
    
    // 确保选择鼻部区域
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 获取TransformationService实例
      final transformationService = TransformationService.getInstance();
      // 选择鼻部区域
      transformationService.setSelectedArea('鼻部');
      
      // 强制更新特征点高亮状态
      integrationService.updateFeaturePointsHighlight();
      
      debugPrint('===== 控制面板加载事件 =====');
      debugPrint('面板: 鼻部参数控制面板');
      debugPrint('操作: 自动选择鼻部区域');
      debugPrint('目标: 高亮鼻部特征点');
      debugPrint('状态: 已触发特征点高亮更新');
      debugPrint('============================');
      Logger.log('NoseParameterControls', 'build', '控制面板加载 | 面板: 鼻部参数 | 操作: 自动选择鼻部区域 | 状态: 已触发特征点高亮更新');
    });
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '鼻部调整',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF7B61FF),
              ),
            ),
            const SizedBox(height: 16),
            
            // 鼻梁高度
            NoseParameterSlider(
              label: '鼻梁高度',
              paramType: NoseParameterType.bridgeHeight,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.bridgeHeight, value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 鼻梁宽度
            NoseParameterSlider(
              label: '鼻梁宽度',
              paramType: NoseParameterType.bridgeWidth,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.bridgeWidth, value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 鼻尖长度
            NoseParameterSlider(
              label: '鼻尖长度',
              paramType: NoseParameterType.tipLength,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.tipLength, value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 鼻尖高度
            NoseParameterSlider(
              label: '鼻尖高度',
              paramType: NoseParameterType.tipHeight,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.tipHeight, value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 鼻尖宽度
            NoseParameterSlider(
              label: '鼻尖宽度',
              paramType: NoseParameterType.tipWidth,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.tipWidth, value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 鼻孔大小
            NoseParameterSlider(
              label: '鼻孔大小',
              paramType: NoseParameterType.nostrilSize,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.nostrilSize, value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 鼻翼宽度
            NoseParameterSlider(
              label: '鼻翼宽度',
              paramType: NoseParameterType.nostrilWidth,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.nostrilWidth, value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 鼻基底高度
            NoseParameterSlider(
              label: '鼻基底高度',
              paramType: NoseParameterType.baseHeight,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.baseHeight, value);
              },
            ),
            
            const SizedBox(height: 12),
            
            // 鼻基底宽度
            NoseParameterSlider(
              label: '鼻基底宽度',
              paramType: NoseParameterType.baseWidth,
              onChanged: (value) {
                integrationService.updateParameter(NoseParameterType.baseWidth, value);
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// 鼻部参数滑块
class NoseParameterSlider extends StatefulWidget {
  final String label;
  final NoseParameterType paramType;
  final ValueChanged<double> onChanged;
  final double? initialValue;
  final double? minValue;
  final double? maxValue;

  const NoseParameterSlider({
    Key? key,
    required this.label,
    required this.paramType,
    required this.onChanged,
    this.initialValue,
    this.minValue,
    this.maxValue,
  }) : super(key: key);

  @override
  State<NoseParameterSlider> createState() => _NoseParameterSliderState();
}

class _NoseParameterSliderState extends State<NoseParameterSlider> {
  late double _value;
  late double _min;
  late double _max;
  bool _isHovering = false;
  
  // 防抖动定时器
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    
    // 设置参数初始值和约束范围
    // 不再使用默认参数，而是使用配置或当前参数
    if (widget.initialValue != null) {
      _value = widget.initialValue!;
    } else {
      _value = 0.0;
    }
    
    // 使用参数约束，如果没有提供则使用合理的默认范围
    _min = widget.minValue ?? -0.5;
    _max = widget.maxValue ?? 0.5;
    
    BeautifyLogger.logInfo('NoseParameterSlider', 'initState', 
      '初始化参数滑块: ${widget.label}, 类型=${widget.paramType}, 值=$_value, 范围=$_min-$_max'
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取集成服务实例
    final integrationService = Provider.of<NoseTransformationIntegrationService>(context, listen: false);
    
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovering = true;
        });
        // 异步调用，不阻塞UI
        _visualizeParameterInfluence(integrationService);
      },
      onExit: (_) => setState(() {
        _isHovering = false;
        // 隐藏影响区域
        if (integrationService.influenceController.visible) {
          integrationService.influenceController.hide();
        }
      }),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.label,
                style: TextStyle(
                  fontSize: 14,
                  color: _isHovering ? const Color(0xFF7B61FF) : Colors.black87,
                  fontWeight: _isHovering ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              Text(
                '${(_value * 100).round()}%',
                style: TextStyle(
                  fontSize: 14,
                  color: _isHovering ? const Color(0xFF7B61FF) : Colors.black54,
                ),
              ),
            ],
          ),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: const Color(0xFF7B61FF),
              inactiveTrackColor: const Color(0xFFE8DBFF),
              thumbColor: const Color(0xFF7B61FF),
              overlayColor: const Color(0x297B61FF),
              trackHeight: 4,
              thumbShape: const RoundSliderThumbShape(
                enabledThumbRadius: 10,
              ),
              overlayShape: const RoundSliderOverlayShape(
                overlayRadius: 20,
              ),
            ),
            child: Slider(
              value: _value,
              min: _min,
              max: _max,
              divisions: 10, // 添加分割数，使滑块变为离散值
              label: '${_value.toInt()}', // 显示当前值
              onChanged: (value) {
                // 记录参数调整前的值
                final oldValue = _value;
                
                // 更新内部状态
                setState(() {
                  _value = value;
                });
                
                // 使用防抖动机制调用回调函数
                // 如果正在连续调整滑块，只在最后一次调整时触发变形
                if (_debounceTimer != null) {
                  _debounceTimer!.cancel();
                }
                
                // 设置150毫秒的防抖时间
                _debounceTimer = Timer(Duration(milliseconds: 150), () {
                  // 只调用一次回调函数，触发变形操作
                  // TransformationService中已经有防抖动机制，不需要再调用_visualizeParameterInfluence
                  widget.onChanged(value);
                  
                  BeautifyLogger.logInfo('NoseParameterSlider', 'onChanged', 
                    '调整参数: 类型=${widget.paramType}, 值=$value, 旧值=$oldValue, 防抖动生效'
                  );
                });
              },
            ),
          ),
        ],
      ),
    );
  }
  
  /// 可视化参数影响区域
  Future<void> _visualizeParameterInfluence(NoseTransformationIntegrationService service) async {
    // 记录调用前的详细日志
    BeautifyLogger.logInfo('NoseParameterSlider', '_visualizeParameterInfluence', 
      '开始显示参数影响区域: 类型=${widget.paramType}, 值=${_value}'
    );
    
    // 检查服务实例是否有效
    if (service == null) {
      BeautifyLogger.logError('NoseParameterSlider', '_visualizeParameterInfluence', 
        '服务实例为空, 无法显示影响区域'
      );
      return;
    }
    
    try {
      // 调用服务方法显示影响区域
      await service.visualizeParameterInfluence(widget.paramType);
      
      // 记录调用后的状态
      BeautifyLogger.logInfo('NoseParameterSlider', '_visualizeParameterInfluence', 
        '已调用显示参数影响区域: 类型=${widget.paramType}'
      );
    } catch (e) {
      BeautifyLogger.logError('NoseParameterSlider', '_visualizeParameterInfluence', 
        '显示参数影响区域失败: $e'
      );
    }
  }
}
