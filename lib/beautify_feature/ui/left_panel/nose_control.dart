import 'package:flutter/material.dart';
import '../../services/transformation_service.dart';
import '../widgets/enhanced_parameter_slider.dart';

/// 鼻部控制组件
/// 
/// 提供鼻部相关参数的调整控制
class NoseControl extends StatefulWidget {
  const NoseControl({Key? key}) : super(key: key);

  @override
  State<NoseControl> createState() => _NoseControlState();
}

class _NoseControlState extends State<NoseControl> {
  // 鼻部参数
  double _noseHeight = 0.0;
  double _noseWidth = 0.0;
  double _noseBridge = 0.0;
  double _noseTip = 0.0;
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '鼻部调整',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // 鼻高
          EnhancedParameterSlider(
            label: '鼻高',
            value: _noseHeight,
            min: -1.0,
            max: 1.0,
            areaType: 'nose',
            parameterName: 'bridge_height',
            enabled: !TransformationService.instance.isProcessing,
            onChanged: (value) {
              setState(() {
                _noseHeight = value;
              });
              TransformationService.instance.updateTransformationValue('nose', 'bridge_height', value);
            },
          ),
          
          // 鼻宽
          EnhancedParameterSlider(
            label: '鼻宽',
            value: _noseWidth,
            min: -1.0,
            max: 1.0,
            areaType: 'nose',
            parameterName: 'width',
            enabled: !TransformationService.instance.isProcessing,
            onChanged: (value) {
              setState(() {
                _noseWidth = value;
              });
              TransformationService.instance.updateTransformationValue('nose', 'width', value);
            },
          ),
          
          // 鼻梁
          EnhancedParameterSlider(
            label: '鼻梁',
            value: _noseBridge,
            min: -1.0,
            max: 1.0,
            areaType: 'nose',
            parameterName: 'bridge_width',
            enabled: !TransformationService.instance.isProcessing,
            onChanged: (value) {
              setState(() {
                _noseBridge = value;
              });
              TransformationService.instance.updateTransformationValue('nose', 'bridge_width', value);
            },
          ),
          
          // 鼻尖
          EnhancedParameterSlider(
            label: '鼻尖',
            value: _noseTip,
            min: -1.0,
            max: 1.0,
            areaType: 'nose',
            parameterName: 'tip_height',
            enabled: !TransformationService.instance.isProcessing,
            onChanged: (value) {
              setState(() {
                _noseTip = value;
              });
              TransformationService.instance.updateTransformationValue('nose', 'tip_height', value);
            },
          ),
        ],
      ),
    );
  }
}
