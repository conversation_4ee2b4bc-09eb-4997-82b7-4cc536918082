import 'package:flutter/material.dart';
import '../widgets/parameter_slider.dart';

/// 面部控制组件
/// 
/// 提供面部相关参数的调整控制
class FaceControl extends StatefulWidget {
  const FaceControl({Key? key}) : super(key: key);

  @override
  State<FaceControl> createState() => _FaceControlState();
}

class _FaceControlState extends State<FaceControl> {
  // 面部参数
  double _faceShape = 0.0;
  double _jawline = 0.0;
  double _cheekbones = 0.0;
  double _faceLength = 0.0;
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '面部调整',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // 面部形状
          ParameterSlider(
            label: '面部形状',
            value: _faceShape,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _faceShape = value;
              });
              debugPrint('Face shape adjusted to: $value');
            },
          ),
          
          // 下颌线
          ParameterSlider(
            label: '下颌线',
            value: _jawline,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _jawline = value;
              });
              debugPrint('Jawline adjusted to: $value');
            },
          ),
          
          // 颧骨
          ParameterSlider(
            label: '颧骨',
            value: _cheekbones,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _cheekbones = value;
              });
              debugPrint('Cheekbones adjusted to: $value');
            },
          ),
          
          // 脸长
          ParameterSlider(
            label: '脸长',
            value: _faceLength,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _faceLength = value;
              });
              debugPrint('Face length adjusted to: $value');
            },
          ),
        ],
      ),
    );
  }
}
