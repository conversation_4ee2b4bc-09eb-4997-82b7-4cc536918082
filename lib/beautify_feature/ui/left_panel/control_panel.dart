import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../widgets/control_panel/parameter_controls.dart';
import '../../../widgets/control_panel/operation_buttons.dart';
import '../../../utils/logger.dart';
import '../../services/transformation_service.dart';

/// 左侧控制面板
/// 
/// 包含面部、鼻部、眼部、唇部和抗衰调整模块
class ControlPanel extends StatefulWidget {
  const ControlPanel({Key? key}) : super(key: key);

  @override
  State<ControlPanel> createState() => _ControlPanelState();
}

class _ControlPanelState extends State<ControlPanel> {
  @override
  void initState() {
    super.initState();
    Logger.log('ControlPanel', 'initState', '初始化控制面板');
  }
  
  @override
  void dispose() {
    Logger.log('ControlPanel', 'dispose', '释放控制面板资源');
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final transformationService = Provider.of<TransformationService>(context);
    
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 标题
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFFF5F0FF),
              border: Border(
                bottom: BorderSide(
                  color: Color(0xFFE8DBFF),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.face_retouching_natural,
                  color: Color(0xFF7B61FF),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '面部美容整形',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF7B61FF),
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(
                    Icons.info_outline,
                    color: Color(0xFF7B61FF),
                  ),
                  onPressed: () {
                    // 显示帮助信息
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('使用帮助'),
                        content: const Text(
                          '1. 选择需要调整的面部区域\n'
                          '2. 调整参数滑块\n'
                          '3. 使用对比按钮查看前后效果\n'
                          '4. 满意后点击应用按钮\n'
                          '5. 如需重新开始，点击重置按钮'
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('了解了'),
                          ),
                        ],
                      ),
                    );
                    Logger.log('ControlPanel', 'showHelp', '显示帮助信息');
                  },
                ),
              ],
            ),
          ),
          
          // 参数控制区域
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: ParameterControls(),
              ),
            ),
          ),
          
          // 操作按钮
          const OperationButtons(),
        ],
      ),
    );
  }
}
