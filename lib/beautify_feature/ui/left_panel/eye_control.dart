import 'package:flutter/material.dart';
import '../widgets/parameter_slider.dart';

/// 眼部控制组件
/// 
/// 提供眼部相关参数的调整控制
class EyeControl extends StatefulWidget {
  const EyeControl({Key? key}) : super(key: key);

  @override
  State<EyeControl> createState() => _EyeControlState();
}

class _EyeControlState extends State<EyeControl> {
  // 眼部参数
  double _eyeSize = 0.0;
  double _eyeDistance = 0.0;
  double _eyeHeight = 0.0;
  double _eyeAngle = 0.0;
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '眼部调整',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // 眼睛大小
          ParameterSlider(
            label: '眼睛大小',
            value: _eyeSize,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _eyeSize = value;
              });
              debugPrint('Eye size adjusted to: $value');
            },
          ),
          
          // 眼距
          ParameterSlider(
            label: '眼距',
            value: _eyeDistance,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _eyeDistance = value;
              });
              debugPrint('Eye distance adjusted to: $value');
            },
          ),
          
          // 眼高
          ParameterSlider(
            label: '眼高',
            value: _eyeHeight,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _eyeHeight = value;
              });
              debugPrint('Eye height adjusted to: $value');
            },
          ),
          
          // 眼角度
          ParameterSlider(
            label: '眼角度',
            value: _eyeAngle,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _eyeAngle = value;
              });
              debugPrint('Eye angle adjusted to: $value');
            },
          ),
        ],
      ),
    );
  }
}
