import 'package:flutter/material.dart';
import '../widgets/parameter_slider.dart';

/// 唇部控制组件
/// 
/// 提供唇部相关参数的调整控制
class LipControl extends StatefulWidget {
  const LipControl({Key? key}) : super(key: key);

  @override
  State<LipControl> createState() => _LipControlState();
}

class _LipControlState extends State<LipControl> {
  // 唇部参数
  double _lipFullness = 0.0;
  double _lipWidth = 0.0;
  double _lipCurvature = 0.0;
  double _lipRatio = 0.0;
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '唇部调整',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // 唇部丰满度
          ParameterSlider(
            label: '丰满度',
            value: _lipFullness,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _lipFullness = value;
              });
              debugPrint('Lip fullness adjusted to: $value');
            },
          ),
          
          // 唇部宽度
          ParameterSlider(
            label: '唇宽',
            value: _lipWidth,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _lipWidth = value;
              });
              debugPrint('Lip width adjusted to: $value');
            },
          ),
          
          // 唇部曲度
          ParameterSlider(
            label: '唇曲度',
            value: _lipCurvature,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _lipCurvature = value;
              });
              debugPrint('Lip curvature adjusted to: $value');
            },
          ),
          
          // 上下唇比例
          ParameterSlider(
            label: '上下唇比例',
            value: _lipRatio,
            min: -1.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _lipRatio = value;
              });
              debugPrint('Lip ratio adjusted to: $value');
            },
          ),
        ],
      ),
    );
  }
}
