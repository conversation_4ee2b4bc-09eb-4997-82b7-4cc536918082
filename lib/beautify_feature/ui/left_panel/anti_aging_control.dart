import 'package:flutter/material.dart';
import '../widgets/parameter_slider.dart';

/// 抗衰控制组件
/// 
/// 提供抗衰相关参数的调整控制
class AntiAgingControl extends StatefulWidget {
  const AntiAgingControl({Key? key}) : super(key: key);

  @override
  State<AntiAgingControl> createState() => _AntiAgingControlState();
}

class _AntiAgingControlState extends State<AntiAgingControl> {
  // 抗衰参数
  double _wrinkleReduction = 0.0;
  double _skinTightening = 0.0;
  double _textureImprovement = 0.0;
  double _spotRemoval = 0.0;
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '抗衰调整',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // 皱纹减少
          ParameterSlider(
            label: '皱纹减少',
            value: _wrinkleReduction,
            min: 0.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _wrinkleReduction = value;
              });
              debugPrint('Wrinkle reduction adjusted to: $value');
            },
          ),
          
          // 皮肤紧致
          ParameterSlider(
            label: '皮肤紧致',
            value: _skinTightening,
            min: 0.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _skinTightening = value;
              });
              debugPrint('Skin tightening adjusted to: $value');
            },
          ),
          
          // 肤质改善
          ParameterSlider(
            label: '肤质改善',
            value: _textureImprovement,
            min: 0.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _textureImprovement = value;
              });
              debugPrint('Texture improvement adjusted to: $value');
            },
          ),
          
          // 色斑去除
          ParameterSlider(
            label: '色斑去除',
            value: _spotRemoval,
            min: 0.0,
            max: 1.0,
            enabled: !TransformationService().isProcessing,
            onChanged: (value) {
              setState(() {
                _spotRemoval = value;
              });
              debugPrint('Spot removal adjusted to: $value');
            },
          ),
        ],
      ),
    );
  }
}
