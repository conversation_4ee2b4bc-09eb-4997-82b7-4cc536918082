import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../utils/face_feature_bridge.dart' hide FeaturePoint;
import '../../../utils/logger.dart';
import '../../animations/scanning_animation.dart';
import '../../services/transformation_service.dart';
import '../../services/nose_transformation_integration_service.dart';
import '../../utils/temp_logger.dart';
import 'comparison_controller.dart';
import 'comparison_button.dart';
import 'package:beautifun/beautify_feature/animations/feature_points_animation.dart';
import 'package:beautifun/beautify_feature/models/feature_point.dart';
import 'package:beautifun/painters/landmark_painter.dart';
import '../widgets/deformation_influence_overlay.dart';

/// 中央预览面板
/// 
/// 显示图像预览、特征点和变形效果
class PreviewPanel extends StatefulWidget {
  /// 可选的图像路径
  final String? imagePath;
  
  const PreviewPanel({Key? key, this.imagePath}) : super(key: key);

  @override
  State<PreviewPanel> createState() => _PreviewPanelState();
}

class _PreviewPanelState extends State<PreviewPanel> with SingleTickerProviderStateMixin {
  // 图像相关
  ui.Image? _image;
  double _imageWidth = 0;
  double _imageHeight = 0;
  bool _isLoading = false;
  bool _isProcessing = false;
  
  // 特征点相关
  List<FeaturePoint> _featurePoints = [];
  List<FeaturePoint> _visiblePoints = []; // 当前显示的特征点
  
  // 扫描动画相关
  late AnimationController _scanAnimationController;
  bool _showScanAnimation = false;
  
  // 变形服务和对比控制器
  late TransformationService _transformationService;
  late ComparisonController _comparisonController;
  
  // 鼻部特征点显示控制
  bool _showNoseFeatures = true;
  bool _showLabels = false;
  
  @override
  void initState() {
    super.initState();
    
    TempLogger.logInfo('PreviewPanel', 'initState', '初始化预览面板');
    
    // 初始化扫描动画控制器
    _scanAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _onScanAnimationCompleted();
      }
    });
    
    // 初始化变形服务和对比控制器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _transformationService = Provider.of<TransformationService>(context, listen: false);
      _comparisonController = Provider.of<ComparisonController>(context, listen: false);
      
      // 监听变形图像变化
      _transformationService.addListener(_onTransformationChanged);
    });
    
    // 加载图片
    if (widget.imagePath != null && widget.imagePath!.isNotEmpty) {
      _loadImage();
    }
  }
  
  @override
  void didUpdateWidget(PreviewPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果图像路径发生变化，则重新加载图像
    if (widget.imagePath != oldWidget.imagePath) {
      TempLogger.logInfo('PreviewPanel', 'didUpdateWidget', '图片路径变化: ${widget.imagePath}');
      
      setState(() {
        _isLoading = true;
        _featurePoints = [];
      });
      
      if (widget.imagePath != null && widget.imagePath!.isNotEmpty) {
        _loadImage();
      }
    }
  }
  
  @override
  void dispose() {
    TempLogger.logInfo('PreviewPanel', 'dispose', '清理资源');
    _scanAnimationController.dispose();
    
    // 移除变形服务监听器
    if (_transformationService != null) {
      _transformationService.removeListener(_onTransformationChanged);
    }
    
    TempLogger.logInfo('PreviewPanel', 'dispose', '资源清理完成');
    super.dispose();
  }
  
  /// 变形服务状态变化回调
  void _onTransformationChanged() {
    // 当变形图像路径变化时，显示对比按钮
    if (_transformationService.transformedImagePath != null) {
      setState(() {
        // 更新状态，触发重绘
      });
    }
  }

  /// 清除所有可见特征点
  void clearVisiblePoints() {
    final now = DateTime.now();
    final timestamp = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}.${now.millisecond.toString().padLeft(3, '0')}';
    
    Logger.log('PreviewPanel', 'clearVisiblePoints', '🔄 [INFO] 开始清除可见特征点');
    Logger.log('PreviewPanel', 'clearVisiblePoints', '📃 [DEBUG] 当前状态:');
    Logger.log('PreviewPanel', 'clearVisiblePoints', '  • 可见点数量: ${_visiblePoints.length}');
    Logger.log('PreviewPanel', 'clearVisiblePoints', '  • 可见点列表: ${_visiblePoints.map((p) => p.id).toList()}');
    
    setState(() {
      _visiblePoints = [];
    });
    
    Logger.log('PreviewPanel', 'clearVisiblePoints', '✅ [INFO] 清除完成');
  }

  /// 更新可见特征点
  void updateVisiblePoints(List<FeaturePoint> points) {
    final now = DateTime.now();
    final timestamp = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}.${now.millisecond.toString().padLeft(3, '0')}';
    
    Logger.log('PreviewPanel', 'updateVisiblePoints', '🔄 [INFO] 开始更新可见特征点');
    Logger.log('PreviewPanel', 'updateVisiblePoints', '📃 [DEBUG] 当前状态:');
    Logger.log('PreviewPanel', 'updateVisiblePoints', '  • 原有点数量: ${_visiblePoints.length}');
    Logger.log('PreviewPanel', 'updateVisiblePoints', '  • 原有点列表: ${_visiblePoints.map((p) => p.id).toList()}');
    Logger.log('PreviewPanel', 'updateVisiblePoints', '📃 [DEBUG] 新特征点:');
    Logger.log('PreviewPanel', 'updateVisiblePoints', '  • 点数量: ${points.length}');
    Logger.log('PreviewPanel', 'updateVisiblePoints', '  • 点列表: ${points.map((p) => p.id).toList()}');
    
    setState(() {
      _visiblePoints = points;
    });
    
    Logger.log('PreviewPanel', 'updateVisiblePoints', '✅ [INFO] 更新完成');
  }

  /// 延迟显示特征点
  Future<void> showPointsWithDelay(List<FeaturePoint> points, {Duration delay = const Duration(seconds: 1)}) async {
    final now = DateTime.now();
    final timestamp = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}.${now.millisecond.toString().padLeft(3, '0')}';
    
    Logger.log('PreviewPanel', 'showPointsWithDelay', '🔄 [INFO] 开始延迟显示特征点');
    Logger.log('PreviewPanel', 'showPointsWithDelay', '📃 [DEBUG] 输入参数:');
    Logger.log('PreviewPanel', 'showPointsWithDelay', '  • 点数量: ${points.length}');
    Logger.log('PreviewPanel', 'showPointsWithDelay', '  • 点列表: ${points.map((p) => p.id).toList()}');
    Logger.log('PreviewPanel', 'showPointsWithDelay', '  • 延迟时间: ${delay.inMilliseconds}ms');
    
    // 先清除当前显示的点
    Logger.log('PreviewPanel', 'showPointsWithDelay', '📝 [DEBUG] 清除当前点');
    clearVisiblePoints();
    
    // 等待指定时间
    Logger.log('PreviewPanel', 'showPointsWithDelay', '📝 [DEBUG] 开始等待: ${delay.inMilliseconds}ms');
    await Future.delayed(delay);
    
    // 显示新的特征点
    if (mounted) {
      Logger.log('PreviewPanel', 'showPointsWithDelay', '📝 [DEBUG] 显示新特征点');
      updateVisiblePoints(points);
      Logger.log('PreviewPanel', 'showPointsWithDelay', '✅ [INFO] 延迟显示完成');
    } else {
      Logger.log('PreviewPanel', 'showPointsWithDelay', '⚠️ [WARN] 组件已卸载，取消显示');
    }
  }
  
  /// 加载图片并获取特征点
  Future<void> _loadImage() async {
    if (widget.imagePath == null || widget.imagePath!.isEmpty) {
      TempLogger.logError('PreviewPanel', 'loadImage', '图片路径为空');
      setState(() {
        _isLoading = false;
      });
      return;
    }
    
    if (_isProcessing) {
      TempLogger.logWarning('PreviewPanel', 'loadImage', '正在处理图片，忽略此次加载');
      return;
    }
    
    setState(() {
      _isLoading = true;
      _isProcessing = true;
      _featurePoints = [];
    });
    
    TempLogger.logInfo('PreviewPanel', 'loadImage', '开始加载图片: ${widget.imagePath}');
    
    try {
      // 检查图片是否存在
      final File imageFile = File(widget.imagePath!);
      if (!await imageFile.exists()) {
        TempLogger.logError('PreviewPanel', 'loadImage', '图片不存在: ${widget.imagePath}');
        setState(() {
          _isLoading = false;
          _isProcessing = false;
        });
        return;
      }
      
      // 读取图片数据
      final Uint8List imageBytes = await imageFile.readAsBytes();
      
      // 解码图片
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;
      
      TempLogger.logInfo('PreviewPanel', 'loadImage', '图片尺寸: ${image.width}x${image.height}');
      
      setState(() {
        _image = image;
        _imageWidth = image.width.toDouble();
        _imageHeight = image.height.toDouble();
        _isLoading = false;
      });
      
      // 获取特征点
      await _detectFeaturePoints();
      
    } catch (e) {
      TempLogger.logError('PreviewPanel', 'loadImage', '加载图片失败: $e');
      setState(() {
        _isLoading = false;
        _isProcessing = false;
      });
    }
  }
  
  /// 检测特征点
  Future<void> _detectFeaturePoints() async {
    if (widget.imagePath == null || _image == null) {
      TempLogger.logError('PreviewPanel', '_detectFeaturePoints', '图片未加载，无法检测特征点');
      setState(() {
        _isProcessing = false;
      });
      return;
    }
    
    TempLogger.logInfo('PreviewPanel', '_detectFeaturePoints', '开始检测特征点');
    
    try {
      // 调用 FaceFeatureBridge 检测特征点
      final featurePoints = await FaceFeatureBridge.detectFacialFeatures(
        widget.imagePath!,
        _imageWidth,
        _imageHeight,
      );
      
      if (featurePoints == null || featurePoints.isEmpty) {
        TempLogger.logWarning('PreviewPanel', '_detectFeaturePoints', '未检测到特征点');
        setState(() {
          _isProcessing = false;
          _showScanAnimation = false;  // 确保扫描动画停止
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('未检测到面部特征点')),
          );
        }
        return;
      }
      
      TempLogger.logInfo('PreviewPanel', '_detectFeaturePoints', '检测到 ${featurePoints.length} 个特征点');
      
      // 输出前几个特征点的坐标
      for (int i = 0; i < math.min(5, featurePoints.length); i++) {
        TempLogger.logInfo('PreviewPanel', '_detectFeaturePoints', 
          '特征点 $i: (${featurePoints[i].position.dx}, ${featurePoints[i].position.dy})');
      }
      
      // 转换为 FeaturePoint 类型，保留原始特征点ID
      final List<FeaturePoint> convertedPoints = featurePoints.map((point) {
        // 生成特征点ID - 使用索引
        final id = 'point_${featurePoints.indexOf(point)}';
        
        return FeaturePoint.fromCoordinates(
          x: point.position.dx,
          y: point.position.dy,
          z: point.z,
          isPrimary: point.isPrimary,
          description: point.description,
          id: id,
          confidence: point.visibility,
        );
      }).toList();
      
      // 更新特征点
      setState(() {
        _featurePoints = convertedPoints;
        _visiblePoints = convertedPoints; // 初始时显示所有特征点
        _isProcessing = false;
        // 确保默认显示特征点
        _showNoseFeatures = true;
      });
      
      TempLogger.logInfo('PreviewPanel', '_detectFeaturePoints', '特征点处理完成');
      
      // 启动扫描动画
      _startScanningAnimation();
      
    } catch (e) {
      TempLogger.logError('PreviewPanel', '_detectFeaturePoints', '检测特征点失败: $e');
      setState(() {
        _isProcessing = false;
        _showScanAnimation = false;  // 确保扫描动画停止
      });
    }
  }
  
  /// 启动扫描动画
  void _startScanningAnimation() {
    TempLogger.logInfo('PreviewPanel', 'startScanningAnimation', '开始扫描动画');
    setState(() {
      _showScanAnimation = true;
    });
    _scanAnimationController.reset();
    _scanAnimationController.forward();
  }
  
  /// 扫描动画完成回调
  void _onScanAnimationCompleted() {
    TempLogger.logInfo('PreviewPanel', 'scanAnimationListener', '扫描动画完成');
    setState(() {
      _showScanAnimation = false;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    _transformationService = Provider.of<TransformationService>(context, listen: false);
    _comparisonController = Provider.of<ComparisonController>(context, listen: false);
    
    return Stack(
      children: [
        // 空状态提示
        if (_image == null && !_isLoading && !_isProcessing)
          _buildEmptyState(),
          
        // 加载状态
        if (_isLoading)
          _buildLoadingState(),
          
        // 处理状态
        if (_isProcessing)
          _buildProcessingState(),
          
        // 图像预览 (包含特征点动画)
        if (_image != null && !_isLoading && !_isProcessing)
          _buildImagePreview(),
          
        // 扫描动画
        if (_showScanAnimation)
          ScanningAnimation(
            controller: _scanAnimationController,
          ),
          
        // 注意: 对比按钮现在在_buildImagePreview中显示
          
        // 变形状态指示器
        Consumer<TransformationService>(
          builder: (context, transformationService, child) {
            if (transformationService.isDeforming) {
              return Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(const Color(0xFF7B61FF)),
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }
  
  /// 构建图像预览
  Widget _buildImagePreview() {
    return Consumer2<TransformationService, ComparisonController>(
      builder: (context, transformationService, comparisonController, child) {
        // 记录日志
        final hasTransformedImage = transformationService.transformedImagePath != null;
        if (hasTransformedImage) {
          TempLogger.logInfo('PreviewPanel', '_buildImagePreview', 
            '变形图像路径: ${transformationService.transformedImagePath}');
        }
        
        return Stack(
          alignment: Alignment.center,
          children: [
            // 底层图像 - 根据对比状态显示原始图像或变形后的图像
            Center(
              child: comparisonController.isComparing
                ? RawImage(
                    image: _image,
                    fit: BoxFit.contain,
                    width: _imageWidth,
                    height: _imageHeight,
                  )
                : hasTransformedImage
                  ? Image.file(
                      File(transformationService.transformedImagePath!),
                      fit: BoxFit.contain,
                      width: _imageWidth,
                      height: _imageHeight,
                    )
                  : RawImage(
                      image: _image,
                      fit: BoxFit.contain,
                      width: _imageWidth,
                      height: _imageHeight,
                    ),
            ),
            
            // 特征点动画层
            if (_visiblePoints.isNotEmpty && comparisonController.isComparing)
              Center(
                child: _buildFeaturePointsAnimation(),
              ),
              
            // 变形影响区域覆盖层 - 显示参数调整的影响区域
            // 始终显示变形影响区域覆盖层，由控制器决定是否可见
            // 
            // 【2025-05-05】临时禁用DeformationInfluenceOverlayBuilder组件
            // 原因：该组件在变形过程中会在底部显示一个放大的图像预览，与主图像区域的变形结果重复
            // 影响：禁用后，用户将无法看到参数调整的实时影响区域可视化
            // 如需恢复：删除下面的注释符号，恢复原始功能
            // const DeformationInfluenceOverlayBuilder(),
            
            // 记录当前状态
            Builder(builder: (context) {
              TempLogger.logInfo('PreviewPanel', '_buildImagePreview', 
                '当前状态: 对比模式=${comparisonController.isComparing}, 有变形图像=$hasTransformedImage');
              return const SizedBox.shrink();
            }),
              
            // 对比按钮 - 只在有变形后图像时显示
            if (hasTransformedImage)
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const ComparisonButton(),
                      const SizedBox(height: 8),
                      Text(
                        '按住查看原图',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
  
  /// 构建特征点动画
  Widget _buildFeaturePointsAnimation() {
    // 记录特征点数量
    TempLogger.logInfo('PreviewPanel', '_buildFeaturePointsAnimation', '特征点数量: ${_visiblePoints.length}');
    
    // 确保图像尺寸有效
    final imageWidth = _imageWidth > 0 ? _imageWidth : 1.0;
    final imageHeight = _imageHeight > 0 ? _imageHeight : 1.0;
    
    // 计算图像在屏幕上的实际显示尺寸
    final screenSize = MediaQuery.of(context).size;
    final imageAspectRatio = imageWidth / imageHeight;
    final screenAspectRatio = screenSize.width / screenSize.height;
    
    double displayWidth, displayHeight;
    if (imageAspectRatio > screenAspectRatio) {
      // 图像更宽，以宽度为基准
      displayWidth = screenSize.width * 0.8; // 使用80%的屏幕宽度
      displayHeight = displayWidth / imageAspectRatio;
    } else {
      // 图像更高，以高度为基准
      displayHeight = screenSize.height * 0.8; // 使用80%的屏幕高度
      displayWidth = displayHeight * imageAspectRatio;
    }
    
    TempLogger.logDebug('PreviewPanel', '_buildFeaturePointsAnimation', '图像尺寸: ${imageWidth}x${imageHeight}, 显示尺寸: ${displayWidth}x${displayHeight}');
    
    // 将特征点转换为landmarks格式
    final List<List<double>> landmarks = _visiblePoints.map((point) {
      return [point.position.dx, point.position.dy, point.z ?? 0.0];
    }).toList();
    
    // 获取特征区域服务
    final featureAreaService = Provider.of<FeatureAreaService>(context);
    
    // 打印变形区域渲染器状态
    TempLogger.logInfo('PreviewPanel', '_buildFeaturePointsAnimation', 
      '变形区域渲染器: ${_transformationService.deformationAreaRenderer != null ? "已初始化" : "未初始化"}');
    if (_transformationService.deformationAreaRenderer != null) {
      TempLogger.logInfo('PreviewPanel', '_buildFeaturePointsAnimation', 
        '变形区域可见性: ${_transformationService.deformationAreaRenderer.isVisible}');
      TempLogger.logInfo('PreviewPanel', '_buildFeaturePointsAnimation', 
        '变形区域类型: ${_transformationService.deformationAreaRenderer.areaType}');
    }
    
    return Stack(
      children: [
        // 使用LandmarksPainter绘制特征点
        CustomPaint(
          size: Size(displayWidth, displayHeight),
          painter: LandmarksPainter(
            landmarks: landmarks,
            scale: 1.0,
            offset: Offset.zero,
            imageSize: Size(imageWidth, imageHeight),
            highlightIndexes: null,  // 由TransformationService控制高亮
            blinkValue: 1.0,
            showIndexes: false,  // 不显示索引号
          ),
        ),

        // 特征点动画
        FeaturePointsAnimation(
          landmarks: landmarks,
          featurePoints: _featurePoints,
          displaySize: Size(displayWidth, displayHeight),
          imageWidth: imageWidth,
          imageHeight: imageHeight,
          showAnimation: true,
          showLabels: _showLabels,
          showConnectingLines: false,
          showDebugInfo: false,
          deformationAreaRenderer: _transformationService.deformationAreaRenderer,
        ),
      ],
    );
  }
  
  /// 构建空状态提示
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_search,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '请选择一张图片开始',
            style: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 18,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建加载状态
  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
      ),
    );
  }
  
  /// 构建处理状态
  Widget _buildProcessingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
      ),
    );
  }
}
