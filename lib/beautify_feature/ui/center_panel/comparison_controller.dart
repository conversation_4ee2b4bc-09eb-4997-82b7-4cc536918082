import 'package:flutter/material.dart';
import '../../../utils/logger.dart';

/// 比较控制器
/// 
/// 管理变形前后的图像对比功能
class ComparisonController extends ChangeNotifier {
  /// 是否正在显示对比
  bool _isComparing = false;
  bool get isComparing => _isComparing;
  
  /// 开始对比
  void startComparison() {
    if (!_isComparing) {
      _isComparing = true;
      notifyListeners();
      Logger.log('ComparisonController', 'startComparison', '开始对比显示');
    }
  }
  
  /// 结束对比
  void endComparison() {
    if (_isComparing) {
      _isComparing = false;
      notifyListeners();
      Logger.log('ComparisonController', 'endComparison', '结束对比显示');
    }
  }
  
  /// 切换对比状态
  void toggleComparison() {
    _isComparing = !_isComparing;
    notifyListeners();
    Logger.log('ComparisonController', 'toggleComparison', '切换对比状态: $_isComparing');
  }
}
