import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'comparison_controller.dart';
import '../../../utils/logger.dart';

/// 对比按钮组件
/// 
/// 用于在预览区底部显示对比按钮，按下时显示原始图像，松开时显示变形后图像
class ComparisonButton extends StatelessWidget {
  const ComparisonButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final comparisonController = Provider.of<ComparisonController>(context);
    
    return GestureDetector(
      onTapDown: (_) {
        Logger.log('ComparisonButton', 'onTapDown', '按下对比按钮');
        comparisonController.startComparison();
      },
      onTapUp: (_) {
        Logger.log('ComparisonButton', 'onTapUp', '松开对比按钮');
        comparisonController.endComparison();
      },
      onTapCancel: () {
        Logger.log('ComparisonButton', 'onTapCancel', '取消对比按钮');
        comparisonController.endComparison();
      },
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            Icons.compare_arrows,
            color: comparisonController.isComparing 
                ? const Color(0xFF7B61FF) 
                : Colors.grey.shade700,
            size: 28,
          ),
        ),
      ),
    );
  }
}
