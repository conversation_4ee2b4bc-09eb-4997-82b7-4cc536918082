import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:math' as Math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../utils/logger.dart';
import '../../services/nose_transformation_integration_service.dart';
import '../../animations/scanning_animation.dart';
import '../../animations/feature_points_animation' as animation;
import 'comparison_button.dart';
import 'comparison_controller.dart';

/// 鼻部预览面板
/// 
/// 显示鼻部变形的预览效果
class NosePreviewPanel extends StatefulWidget {
  /// 图像路径
  final String? imagePath;
  
  /// 集成服务
  final NoseTransformationIntegrationService? integrationService;
  
  const NosePreviewPanel({
    Key? key, 
    this.imagePath,
    this.integrationService,
  }) : super(key: key);

  @override
  State<NosePreviewPanel> createState() => _NosePreviewPanelState();
}

class _NosePreviewPanelState extends State<NosePreviewPanel> with SingleTickerProviderStateMixin {
  // 图像相关
  ui.Image? _originalImage;
  ui.Image? _previewImage;
  double _imageWidth = 0;
  double _imageHeight = 0;
  bool _isLoading = false;
  
  // 特征点相关
  List<animation.FeaturePoint> _featurePoints = [];
  
  // 扫描动画相关
  late AnimationController _scanAnimationController;
  bool _showScanAnimation = false;
  
  // 对比控制器
  late ComparisonController _comparisonController;
  
  // 集成服务
  late NoseTransformationIntegrationService _integrationService;
  
  // 特征点信息
  String? _selectedFeaturePointDescription;
  List<String> _selectedFeaturePointMedicalAdvice = [];
  
  @override
  void initState() {
    super.initState();
    
    // 初始化对比控制器
    _comparisonController = ComparisonController();
    
    // 初始化扫描动画控制器
    _scanAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _showScanAnimation = false;
        });
      }
    });
    
    // 初始化集成服务
    _integrationService = widget.integrationService ?? NoseTransformationIntegrationService();
    
    // 加载图像
    if (widget.imagePath != null) {
      _loadImage();
    }
  }
  
  @override
  void didUpdateWidget(NosePreviewPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果图像路径发生变化，则重新加载图像
    if (widget.imagePath != oldWidget.imagePath) {
      setState(() {
        _isLoading = true;
      });
      
      if (widget.imagePath != null) {
        _loadImage();
      }
      
      BeautifyLogger.logInfo('NosePreviewPanel', 'didUpdateWidget', '图像路径变化: ${widget.imagePath}');
    }
  }
  
  @override
  void dispose() {
    _scanAnimationController.dispose();
    super.dispose();
  }
  
  /// 加载图像
  Future<void> _loadImage() async {
    if (widget.imagePath == null || widget.imagePath!.isEmpty) {
      BeautifyLogger.logWarning('NosePreviewPanel', 'loadImage', '图像路径为空');
      return;
    }
    
    BeautifyLogger.logInfo('NosePreviewPanel', 'loadImage', '开始加载图像: ${widget.imagePath}');
    
    try {
      setState(() {
        _isLoading = true;
      });
      
      // 设置集成服务的图像路径
      _integrationService.setImagePath(widget.imagePath!);
      
      // 加载原始图像
      final ByteData data = await rootBundle.load(widget.imagePath!);
      final Uint8List bytes = data.buffer.asUint8List();
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      final ui.FrameInfo fi = await codec.getNextFrame();
      final ui.Image image = fi.image;
      
      setState(() {
        _originalImage = image;
        _imageWidth = image.width.toDouble();
        _imageHeight = image.height.toDouble();
        _isLoading = false;
      });
      
      BeautifyLogger.logInfo('NosePreviewPanel', 'loadImage', '图像加载完成: ${_imageWidth}x${_imageHeight}');
      
      // 获取特征点
      _loadFeaturePoints();
      
      // 延迟启动扫描动画
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _startScanningAnimation();
        }
      });
    } catch (e) {
      BeautifyLogger.logError('NosePreviewPanel', 'loadImage', '图像加载失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 加载特征点
  Future<void> _loadFeaturePoints() async {
    BeautifyLogger.logInfo('NosePreviewPanel', 'loadFeaturePoints', '开始加载特征点');
    
    try {
      // 获取特征点
      final featurePoints = await _integrationService.getNoseFeaturePoints();
      
      if (featurePoints != null && featurePoints.isNotEmpty) {
        setState(() {
          _featurePoints = featurePoints.map((point) => 
            animation.FeaturePoint(
              position: point.position,
              isPrimary: point.isPrimary,
              id: point.id,
              description: point.description,
            )
          ).toList();
        });
        
        BeautifyLogger.logInfo(
          'NosePreviewPanel', 
          'loadFeaturePoints', 
          '特征点加载完成: ${_featurePoints.length} | 主要特征点: ${_featurePoints.where((p) => p.isPrimary).length}'
        );
      } else {
        BeautifyLogger.logWarning('NosePreviewPanel', 'loadFeaturePoints', '没有找到特征点');
        setState(() {
          _featurePoints = [];
        });
      }
    } catch (e) {
      BeautifyLogger.logError('NosePreviewPanel', 'loadFeaturePoints', '特征点加载失败: $e');
      setState(() {
        _featurePoints = [];
      });
    }
  }
  
  /// 启动扫描动画
  void _startScanningAnimation() {
    BeautifyLogger.logInfo('NosePreviewPanel', 'startScanningAnimation', '开始扫描动画');
    setState(() {
      _showScanAnimation = true;
    });
    _scanAnimationController.forward(from: 0);
  }
  
  /// 更新预览图像
  Future<void> _updatePreviewImage(Uint8List? imageData) async {
    if (imageData == null) {
      BeautifyLogger.logWarning('NosePreviewPanel', 'updatePreviewImage', '预览图像数据为空');
      return;
    }
    
    try {
      // 解码图像数据
      final ui.Codec codec = await ui.instantiateImageCodec(imageData);
      final ui.FrameInfo fi = await codec.getNextFrame();
      final ui.Image image = fi.image;
      
      setState(() {
        _previewImage = image;
      });
      
      BeautifyLogger.logInfo('NosePreviewPanel', 'updatePreviewImage', '预览图像更新完成: ${image.width}x${image.height}');
    } catch (e) {
      BeautifyLogger.logError('NosePreviewPanel', 'updatePreviewImage', '预览图像更新失败: $e');
    }
  }
  
  /// 处理图像上的点击事件
  void _handleTapOnImage(Offset tapPosition, Size displaySize) {
    BeautifyLogger.logInfo('NosePreviewPanel', '_handleTapOnImage', '图像点击位置: $tapPosition');
    
    // 查找点击的特征点
    animation.FeaturePoint? tappedPoint;
    double minDistance = 20.0; // 点击容差，单位为像素
    
    for (var point in _featurePoints) {
      // 计算点击位置与特征点的距离
      final dx = tapPosition.dx - (point.position.dx * displaySize.width);
      final dy = tapPosition.dy - (point.position.dy * displaySize.height);
      final distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < minDistance) {
        tappedPoint = point;
        minDistance = distance;
      }
    }
    
    if (tappedPoint != null) {
      BeautifyLogger.logInfo(
        'NosePreviewPanel', 
        '_handleTapOnImage', 
        '点击了特征点: ID=${tappedPoint.id}, 描述=${tappedPoint.description}, 位置=${tappedPoint.position}'
      );
      
      // 更新特征点高亮状态
      setState(() {
        for (var point in _featurePoints) {
          // 取消所有特征点的高亮状态
          point.isHighlighted = false;
        }
        
        // 设置点击的特征点为高亮状态
        tappedPoint.isHighlighted = true;
      });
      
      // 通知集成服务特征点被选中
      if (widget.integrationService != null && tappedPoint.id != null) {
        widget.integrationService!.selectFeaturePoint(tappedPoint.id!);
        
        // 获取特征点描述和医学建议
        setState(() {
          _selectedFeaturePointDescription = 
              widget.integrationService!.getFeaturePointDescription(tappedPoint.id!);
          _selectedFeaturePointMedicalAdvice = 
              widget.integrationService!.getFeaturePointMedicalAdvice(tappedPoint.id!);
        });
      }
    } else {
      // 如果没有点击到任何特征点，取消所有高亮
      setState(() {
        for (var point in _featurePoints) {
          point.isHighlighted = false;
        }
        _selectedFeaturePointDescription = null;
        _selectedFeaturePointMedicalAdvice = [];
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: _integrationService),
        ChangeNotifierProvider.value(value: _comparisonController),
      ],
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black,
          border: Border.all(
            color: Colors.grey.shade800,
            width: 1,
          ),
        ),
        child: ClipRect(
          child: Stack(
            children: [
              // 中心内容
              Positioned.fill(
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // 图像背景
                    if (_originalImage != null)
                      Consumer2<NoseTransformationIntegrationService, ComparisonController>(
                        builder: (context, integrationService, comparisonController, child) {
                          // 如果有预览数据，则更新预览图像
                          if (integrationService.previewImageData != null && _previewImage == null) {
                            _updatePreviewImage(integrationService.previewImageData);
                          }
                          
                          // 根据对比状态决定显示原始图像还是预览图像
                          final displayImage = comparisonController.isComparing
                              ? _originalImage
                              : _previewImage ?? _originalImage;
                          
                          return Center(
                            child: RawImage(
                              image: displayImage,
                              fit: BoxFit.contain,
                            ),
                          );
                        },
                      ),
                    
                    // 加载指示器
                    if (_isLoading)
                      const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.purple),
                        ),
                      ),
                    
                    // 扫描动画
                    if (_showScanAnimation)
                      ScanningAnimation(
                        controller: _scanAnimationController,
                      ),
                    
                    // 特征点动画
                    if (_originalImage != null && _featurePoints.isNotEmpty)
                      LayoutBuilder(
                        builder: (context, constraints) {
                          // 计算图片实际显示尺寸
                          final imageAspectRatio = _imageWidth / _imageHeight;
                          final viewAspectRatio = constraints.maxWidth / constraints.maxHeight;
                          
                          double displayWidth;
                          double displayHeight;
                          
                          if (imageAspectRatio > viewAspectRatio) {
                            // 图片比视图更宽，以宽度为基准
                            displayWidth = constraints.maxWidth;
                            displayHeight = constraints.maxWidth / imageAspectRatio;
                          } else {
                            // 图片比视图更高，以高度为基准
                            displayHeight = constraints.maxHeight;
                            displayWidth = constraints.maxHeight * imageAspectRatio;
                          }
                          
                          BeautifyLogger.logInfo('NosePreviewPanel', 'build', '特征点动画容器尺寸: ${displayWidth}x${displayHeight}');
                          
                          return SizedBox(
                            width: displayWidth,
                            height: displayHeight,
                            child: GestureDetector(
                              onTapDown: (details) {
                                _handleTapOnImage(details.localPosition, Size(displayWidth, displayHeight));
                              },
                              child: animation.FeaturePointsAnimation(
                                imageWidth: _imageWidth,
                                imageHeight: _imageHeight,
                                featurePoints: _featurePoints,
                                displaySize: Size(displayWidth, displayHeight),
                                onAnimationCreated: (controller) {
                                  // 将动画控制器注册到集成服务
                                  _integrationService.setFeaturePointsAnimationController(controller);
                                  BeautifyLogger.logInfo('NosePreviewPanel', 'build', '特征点动画控制器已注册到集成服务');
                                },
                              ),
                            ),
                          );
                        },
                      ),
                      
                    // 特征点信息显示
                    if (_selectedFeaturePointDescription != null)
                      Positioned(
                        top: 20,
                        left: 0,
                        right: 0,
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.7),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _selectedFeaturePointDescription!,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                if (_selectedFeaturePointMedicalAdvice.isNotEmpty) ...[
                                  const SizedBox(height: 8),
                                  ...(_selectedFeaturePointMedicalAdvice.map((advice) => 
                                    Padding(
                                      padding: const EdgeInsets.only(top: 4),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            "• ",
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                            ),
                                          ),
                                          Expanded(
                                            child: Text(
                                              advice,
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  ).toList()),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                    
                    // 底部对比按钮
                    Positioned(
                      bottom: 20,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: const ComparisonButton(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
