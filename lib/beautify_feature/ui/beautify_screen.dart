import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'layout/three_column_layout.dart';
import 'left_panel/control_panel.dart';
import 'center_panel/preview_panel.dart';
import 'center_panel/comparison_controller.dart';
import 'right_panel/interaction_panel.dart';
import '../../utils/image_manager.dart';
import '../../utils/logger.dart';
import '../../utils/face_feature_bridge.dart' hide FeaturePoint;
import '../services/transformation_service.dart';
import '../utils/temp_logger.dart';

/// BeautiFun "炫"功能的主界面
/// 
/// 实现了三栏布局，包括左侧控制面板、中央预览面板和右侧交互面板
class BeautifyScreen extends StatefulWidget {
  /// 可选的图像路径，用于初始化预览面板
  final String? imagePath;
  
  const BeautifyScreen({Key? key, this.imagePath}) : super(key: key);

  @override
  State<BeautifyScreen> createState() => _BeautifyScreenState();
}

class _BeautifyScreenState extends State<BeautifyScreen> with SingleTickerProviderStateMixin {
  /// 当前选择的图片路径
  String? _currentImagePath;
  
  /// 变形服务
  late final TransformationService _transformationService;
  
  /// 对比控制器
  late final ComparisonController _comparisonController;
  
  /// 是否正在加载图片
  bool _isLoadingImage = false;
  
  /// 是否已初始化
  bool _isInitialized = false;
  
  @override
  void initState() {
    super.initState();
    TempLogger.logInfo('BeautifyScreen', 'initState', '🚀 [初始化] 开始初始化炫界面');
    
    // 初始化服务
    _transformationService = TransformationService();
    _comparisonController = ComparisonController();
    
    // 初始化 FaceFeatureBridge
    _initializeFaceFeatureBridge();
    
    // 如果有初始图片路径，加载图片
    _currentImagePath = widget.imagePath;
    if (_currentImagePath != null && _currentImagePath!.isNotEmpty) {
      TempLogger.logInfo('BeautifyScreen', 'initState', '📷 [图片] 有初始图片路径: $_currentImagePath');
      _loadImage();
    } else {
      TempLogger.logInfo('BeautifyScreen', 'initState', '📷 [图片] 无初始图片路径');
    }
    
    _isInitialized = true;
    TempLogger.logInfo('BeautifyScreen', 'initState', '✅ [初始化] 炫界面初始化完成');
  }
  
  /// 初始化 FaceFeatureBridge
  Future<void> _initializeFaceFeatureBridge() async {
    TempLogger.logInfo('BeautifyScreen', '_initializeFaceFeatureBridge', '🔄 [服务初始化] 开始初始化 FaceFeatureBridge');
    
    try {
      final success = await FaceFeatureBridge.initialize();
      if (success) {
        TempLogger.logInfo('BeautifyScreen', '_initializeFaceFeatureBridge', '✅ [服务初始化] FaceFeatureBridge 初始化成功');
      } else {
        TempLogger.logError('BeautifyScreen', '_initializeFaceFeatureBridge', '❌ [服务初始化] FaceFeatureBridge 初始化失败');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('特征点检测服务初始化失败')),
          );
        }
      }
    } catch (e) {
      TempLogger.logError('BeautifyScreen', '_initializeFaceFeatureBridge', '❌ [服务初始化] FaceFeatureBridge 初始化异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('特征点检测服务初始化异常: $e')),
        );
      }
    }
  }
  
  /// 选择图片
  Future<void> _selectImage() async {
    if (_isLoadingImage) {
      TempLogger.logWarning('BeautifyScreen', '_selectImage', '⚠️ [图片选择] 正在加载图片，忽略此次选择');
      return;
    }
    
    TempLogger.logInfo('BeautifyScreen', '_selectImage', '🔄 [图片选择] 开始选择图片');
    
    try {
      final imagePath = await ImageManager.importImage();
      if (imagePath != null) {
        TempLogger.logInfo('BeautifyScreen', '_selectImage', '✅ [图片选择] 选择了新图片: $imagePath');
        setState(() {
          _currentImagePath = imagePath;
          _isLoadingImage = true;
        });
        
        // 加载图片
        await _loadImage();
      } else {
        TempLogger.logInfo('BeautifyScreen', '_selectImage', '⚠️ [图片选择] 未选择图片');
      }
    } catch (e) {
      TempLogger.logError('BeautifyScreen', '_selectImage', '❌ [图片选择] 选择图片异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('选择图片失败: $e')),
        );
      }
    }
  }
  
  /// 加载测试图片
  Future<void> _loadTestImage() async {
    if (_isLoadingImage) {
      TempLogger.logWarning('BeautifyScreen', '_loadTestImage', '⚠️ [测试] 正在加载图片，忽略此次测试');
      return;
    }
    
    TempLogger.logInfo('BeautifyScreen', '_loadTestImage', '🔄 [测试] 开始加载测试图片');
    
    try {
      final testImagePath = await ImageManager.loadTestImage();
      if (testImagePath != null) {
        TempLogger.logInfo('BeautifyScreen', '_loadTestImage', '✅ [测试] 加载测试图片成功: $testImagePath');
        setState(() {
          _currentImagePath = testImagePath;
          _isLoadingImage = true;
        });
        
        // 加载图片
        await _loadImage();
      } else {
        TempLogger.logError('BeautifyScreen', '_loadTestImage', '❌ [测试] 加载测试图片失败');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('加载测试图片失败')),
          );
        }
      }
    } catch (e) {
      TempLogger.logError('BeautifyScreen', '_loadTestImage', '❌ [测试] 加载测试图片异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载测试图片失败: $e')),
        );
      }
    }
  }
  
  /// 加载图片并获取特征点
  Future<void> _loadImage() async {
    if (_currentImagePath == null || _currentImagePath!.isEmpty) {
      TempLogger.logError('BeautifyScreen', '_loadImage', '❌ [图片加载] 图片路径为空');
      setState(() {
        _isLoadingImage = false;
      });
      return;
    }
    
    TempLogger.logInfo('BeautifyScreen', '_loadImage', '🔄 [图片加载] 开始加载图片: $_currentImagePath');
    
    try {
      // 检查图片是否存在
      final File imageFile = File(_currentImagePath!);
      if (!await imageFile.exists()) {
        TempLogger.logError('BeautifyScreen', '_loadImage', '❌ [图片加载] 图片不存在: $_currentImagePath');
        setState(() {
          _isLoadingImage = false;
        });
        return;
      }
      
      // 记录文件调用信息
      TempLogger.logInfo('BeautifyScreen', '_loadImage', '📂 [文件调用] 调用路径: $_currentImagePath');
      TempLogger.logInfo('BeautifyScreen', '_loadImage', '📂 [文件调用] 文件大小: ${await imageFile.length()} 字节');
      TempLogger.logInfo('BeautifyScreen', '_loadImage', '📂 [文件调用] 最后修改时间: ${await imageFile.lastModified()}');
      
      // 通知 PreviewPanel 更新图片
      setState(() {
        _isLoadingImage = false;
      });
      
      TempLogger.logInfo('BeautifyScreen', '_loadImage', '✅ [图片加载] 图片加载完成');
    } catch (e) {
      TempLogger.logError('BeautifyScreen', '_loadImage', '❌ [图片加载] 加载图片失败: $e');
      setState(() {
        _isLoadingImage = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: _transformationService),
        ChangeNotifierProvider.value(value: _comparisonController),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: const Center(child: Text('炫 - 美容整形可视化')),
          backgroundColor: const Color(0xFF7B61FF),
          foregroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actions: [
            // 添加测试按钮
            IconButton(
              icon: const Icon(Icons.science),
              tooltip: '加载测试图片',
              onPressed: _loadTestImage,
            ),
            // 添加图片选择按钮
            IconButton(
              icon: const Icon(Icons.image),
              tooltip: '选择图片',
              onPressed: _selectImage,
            ),
          ],
        ),
        body: ThreeColumnLayout(
          leftPanel: ControlPanel(),
          centerPanel: PreviewPanel(imagePath: _currentImagePath),
          rightPanel: InteractionPanel(),
        ),
      ),
    );
  }
}
