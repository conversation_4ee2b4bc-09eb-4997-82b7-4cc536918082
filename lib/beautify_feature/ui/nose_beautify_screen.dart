import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/logger.dart';
import '../services/nose_transformation_integration_service.dart';
import 'left_panel/nose_parameter_controls.dart';
import 'center_panel/nose_preview_panel.dart';
import 'right_panel/nose_medical_analysis.dart';
import '../animations/scanning_animation.dart';

/// 鼻部美化屏幕
/// 
/// 整合鼻部变形的各个组件，包括参数控制面板、预览面板和医学分析面板
class NoseBeautifyScreen extends StatefulWidget {
  /// 图像路径
  final String? imagePath;
  
  const NoseBeautifyScreen({Key? key, this.imagePath}) : super(key: key);

  @override
  State<NoseBeautifyScreen> createState() => _NoseBeautifyScreenState();
}

class _NoseBeautifyScreenState extends State<NoseBeautifyScreen> {
  /// 集成服务
  late NoseTransformationIntegrationService _integrationService;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化集成服务
    _integrationService = NoseTransformationIntegrationService();
    
    // 设置图像路径
    if (widget.imagePath != null) {
      _integrationService.setImagePath(widget.imagePath!);
    }
    
    BeautifyLogger.logInfo('NoseBeautifyScreen', 'initState', '初始化鼻部美化屏幕');
  }
  
  @override
  void dispose() {
    // 释放资源
    _integrationService.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _integrationService,
      child: Scaffold(
        body: Container(
          color: Colors.white,
          child: Row(
            children: [
              // 左侧控制面板（30%）
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.3,
                child: Column(
                  children: [
                    // 顶部标题
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      alignment: Alignment.centerLeft,
                      child: const Text(
                        '鼻部美化',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF7B61FF),
                        ),
                      ),
                    ),
                    
                    // 参数控制面板
                    const Expanded(
                      child: NoseParameterControls(),
                    ),
                    
                    // 底部操作按钮
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // 重置按钮
                          ElevatedButton(
                            onPressed: () {
                              _integrationService.resetAllParameters();
                              BeautifyLogger.logInfo('NoseBeautifyScreen', 'onResetPressed', '重置所有参数');
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[200],
                              foregroundColor: Colors.black,
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            ),
                            child: const Text('重置'),
                          ),
                          
                          // 应用按钮
                          ElevatedButton(
                            onPressed: () async {
                              final outputPath = await _integrationService.applyTransformation();
                              if (outputPath != null) {
                                BeautifyLogger.logInfo('NoseBeautifyScreen', 'onApplyPressed', '应用变形成功: $outputPath');
                                // 显示成功提示
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('鼻部美化应用成功！'),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              } else {
                                BeautifyLogger.logError('NoseBeautifyScreen', 'onApplyPressed', '应用变形失败');
                                // 显示失败提示
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('鼻部美化应用失败，请重试。'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF7B61FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            ),
                            child: const Text('应用'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              // 中央预览面板（40%）
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.4,
                child: NosePreviewPanel(
                  imagePath: widget.imagePath,
                  integrationService: _integrationService,
                ),
              ),
              
              // 右侧交互面板（30%）
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.3,
                child: const NoseMedicalAnalysis(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
