/// 鼻部特征点类型定义
/// 
/// 定义了鼻部特征点的类型和分类，用于标识不同位置的鼻部特征点
/// 此文件是模块化鼻部特征点可视化系统的基础数据模型

/// 鼻部特征点ID枚举
enum NosePointId {
  /// 鼻梁顶部
  noseBridgeTop,
  
  /// 鼻梁中部
  noseBridgeMiddle,
  
  /// 鼻梁底部
  noseBridgeBottom,
  
  /// 鼻尖
  noseTip,
  
  /// 左鼻翼顶部
  noseLeftWingTop,
  
  /// 左鼻翼中部
  noseLeftWingMiddle,
  
  /// 左鼻翼底部
  noseLeftWingBottom,
  
  /// 右鼻翼顶部
  noseRightWingTop,
  
  /// 右鼻翼中部
  noseRightWingMiddle,
  
  /// 右鼻翼底部
  noseRightWingBottom,
  
  /// 左鼻孔内侧
  leftNostrilInner,
  
  /// 左鼻孔外侧
  leftNostrilOuter,
  
  /// 右鼻孔内侧
  rightNostrilInner,
  
  /// 右鼻孔外侧
  rightNostrilOuter,
  
  /// 鼻基底中心
  noseBase,
  
  /// 鼻基底左侧
  noseBaseLeft,
  
  /// 鼻基底右侧
  noseBaseRight,
}

/// 鼻部区域枚举
enum NoseRegion {
  /// 鼻梁区域
  bridge,
  
  /// 鼻尖区域
  tip,
  
  /// 鼻翼区域
  wings,
  
  /// 鼻孔区域
  nostrils,
  
  /// 鼻基底区域
  base,
}

/// 鼻部特征点重要性分类
enum NosePointImportance {
  /// 主要特征点 - 在面部识别和美容整形中具有关键作用
  primary,
  
  /// 次要特征点 - 辅助定位和连接主要特征点
  secondary,
  
  /// 参考特征点 - 用于提供额外的空间参考
  reference,
}

/// 鼻部特征点连接类型
enum NoseConnectionType {
  /// 解剖学连接 - 基于真实解剖学结构的连接
  anatomical,
  
  /// 辅助连接 - 用于可视化辅助的非解剖学连接
  auxiliary,
  
  /// 参考连接 - 用于显示参考线的连接
  reference,
}

/// 鼻部特征点映射工具类
class NosePointMapper {
  /// 将鼻部特征点ID映射到对应的区域
  static NoseRegion mapToRegion(NosePointId pointId) {
    switch (pointId) {
      case NosePointId.noseBridgeTop:
      case NosePointId.noseBridgeMiddle:
      case NosePointId.noseBridgeBottom:
        return NoseRegion.bridge;
      
      case NosePointId.noseTip:
        return NoseRegion.tip;
      
      case NosePointId.noseLeftWingTop:
      case NosePointId.noseLeftWingMiddle:
      case NosePointId.noseLeftWingBottom:
      case NosePointId.noseRightWingTop:
      case NosePointId.noseRightWingMiddle:
      case NosePointId.noseRightWingBottom:
        return NoseRegion.wings;
      
      case NosePointId.leftNostrilInner:
      case NosePointId.leftNostrilOuter:
      case NosePointId.rightNostrilInner:
      case NosePointId.rightNostrilOuter:
        return NoseRegion.nostrils;
      
      case NosePointId.noseBase:
      case NosePointId.noseBaseLeft:
      case NosePointId.noseBaseRight:
        return NoseRegion.base;
    }
  }
  
  /// 将鼻部特征点ID映射到重要性分类
  static NosePointImportance mapToImportance(NosePointId pointId) {
    switch (pointId) {
      case NosePointId.noseTip:
      case NosePointId.noseBridgeTop:
      case NosePointId.noseBase:
      case NosePointId.noseLeftWingBottom:
      case NosePointId.noseRightWingBottom:
        return NosePointImportance.primary;
      
      case NosePointId.noseBridgeMiddle:
      case NosePointId.noseBridgeBottom:
      case NosePointId.leftNostrilInner:
      case NosePointId.rightNostrilInner:
        return NosePointImportance.secondary;
      
      default:
        return NosePointImportance.reference;
    }
  }
  
  /// 获取鼻部特征点的中文描述
  static String getDescription(NosePointId pointId) {
    switch (pointId) {
      case NosePointId.noseBridgeTop:
        return '鼻梁顶部';
      case NosePointId.noseBridgeMiddle:
        return '鼻梁中部';
      case NosePointId.noseBridgeBottom:
        return '鼻梁底部';
      case NosePointId.noseTip:
        return '鼻尖';
      case NosePointId.noseLeftWingTop:
        return '左鼻翼顶部';
      case NosePointId.noseLeftWingMiddle:
        return '左鼻翼中部';
      case NosePointId.noseLeftWingBottom:
        return '左鼻翼底部';
      case NosePointId.noseRightWingTop:
        return '右鼻翼顶部';
      case NosePointId.noseRightWingMiddle:
        return '右鼻翼中部';
      case NosePointId.noseRightWingBottom:
        return '右鼻翼底部';
      case NosePointId.leftNostrilInner:
        return '左鼻孔内侧';
      case NosePointId.leftNostrilOuter:
        return '左鼻孔外侧';
      case NosePointId.rightNostrilInner:
        return '右鼻孔内侧';
      case NosePointId.rightNostrilOuter:
        return '右鼻孔外侧';
      case NosePointId.noseBase:
        return '鼻基底中心';
      case NosePointId.noseBaseLeft:
        return '鼻基底左侧';
      case NosePointId.noseBaseRight:
        return '鼻基底右侧';
    }
  }
  
  /// 获取MediaPipe索引到鼻部特征点ID的映射
  static Map<int, NosePointId> getMediaPipeToNoseMap() {
    return {
      // 鼻梁特征点
      168: NosePointId.noseBridgeTop,
      6: NosePointId.noseBridgeMiddle,
      4: NosePointId.noseBridgeBottom,
      
      // 鼻尖特征点
      1: NosePointId.noseTip,
      
      // 鼻翼特征点
      97: NosePointId.noseLeftWingTop,
      99: NosePointId.noseLeftWingMiddle,
      100: NosePointId.noseLeftWingBottom,
      327: NosePointId.noseRightWingTop,
      328: NosePointId.noseRightWingMiddle,
      329: NosePointId.noseRightWingBottom,
      
      // 鼻孔特征点
      115: NosePointId.leftNostrilInner,
      166: NosePointId.leftNostrilOuter,
      344: NosePointId.rightNostrilInner,
      392: NosePointId.rightNostrilOuter,
      
      // 鼻基底特征点
      2: NosePointId.noseBase,
      79: NosePointId.noseBaseLeft,
      309: NosePointId.noseBaseRight,
    };
  }
}
