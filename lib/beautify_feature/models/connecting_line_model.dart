import 'dart:ui';
import 'nose_point_types.dart';

/// 连接线模型
/// 
/// 定义了特征点之间的连接线，包括连接的点、线型、颜色等属性
/// 此文件是模块化鼻部特征点可视化系统的基础数据模型

/// 连接线样式枚举
enum LineStyle {
  /// 实线
  solid,
  
  /// 虚线
  dashed,
  
  /// 点线
  dotted,
  
  /// 渐变线
  gradient,
}

/// 连接线模型类
class ConnectingLine {
  /// 起始点ID
  final String startPointId;
  
  /// 结束点ID
  final String endPointId;
  
  /// 线条样式
  final LineStyle style;
  
  /// 线条宽度
  final double width;
  
  /// 线条颜色
  final Color color;
  
  /// 渐变色结束颜色（仅在style为gradient时有效）
  final Color? endColor;
  
  /// 是否为主要连接线
  final bool isPrimary;
  
  /// 连接线类型
  final NoseConnectionType type;
  
  /// 是否高亮显示
  bool isHighlighted;
  
  /// 动画值（0.0-1.0）
  double animationValue;
  
  /// 构造函数
  ConnectingLine({
    required this.startPointId,
    required this.endPointId,
    this.style = LineStyle.solid,
    this.width = 1.0,
    required this.color,
    this.endColor,
    this.isPrimary = false,
    this.type = NoseConnectionType.auxiliary,
    this.isHighlighted = false,
    this.animationValue = 0.0,
  });
  
  /// 创建一个连接线的副本
  ConnectingLine copyWith({
    String? startPointId,
    String? endPointId,
    LineStyle? style,
    double? width,
    Color? color,
    Color? endColor,
    bool? isPrimary,
    NoseConnectionType? type,
    bool? isHighlighted,
    double? animationValue,
  }) {
    return ConnectingLine(
      startPointId: startPointId ?? this.startPointId,
      endPointId: endPointId ?? this.endPointId,
      style: style ?? this.style,
      width: width ?? this.width,
      color: color ?? this.color,
      endColor: endColor ?? this.endColor,
      isPrimary: isPrimary ?? this.isPrimary,
      type: type ?? this.type,
      isHighlighted: isHighlighted ?? this.isHighlighted,
      animationValue: animationValue ?? this.animationValue,
    );
  }
  
  /// 获取当前动画状态下的线条宽度
  double get animatedWidth {
    if (isHighlighted) {
      // 高亮状态下线条宽度有脉动效果
      final pulseEffect = 0.5 * (1 + (animationValue * 2 - 1).abs());
      return width * (1 + pulseEffect);
    }
    return width;
  }
  
  /// 获取当前动画状态下的线条颜色
  Color get animatedColor {
    if (isHighlighted) {
      // 高亮状态下颜色更亮
      return color.withOpacity(0.7 + 0.3 * animationValue);
    }
    return color;
  }
  
  /// 获取当前动画状态下的线条结束颜色
  Color? get animatedEndColor {
    if (endColor == null) return null;
    
    if (isHighlighted) {
      // 高亮状态下颜色更亮
      return endColor!.withOpacity(0.7 + 0.3 * animationValue);
    }
    return endColor;
  }
  
  /// 获取虚线样式的虚线间隔
  List<double> getDashPattern() {
    switch (style) {
      case LineStyle.solid:
        return [];
      case LineStyle.dashed:
        return [6, 3];
      case LineStyle.dotted:
        return [2, 2];
      case LineStyle.gradient:
        return [];
    }
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConnectingLine &&
        other.startPointId == startPointId &&
        other.endPointId == endPointId;
  }
  
  @override
  int get hashCode => startPointId.hashCode ^ endPointId.hashCode;
}

/// 鼻部连接线工厂类
class NoseConnectionFactory {
  /// 创建鼻梁连接线
  static List<ConnectingLine> createBridgeConnections() {
    return [
      ConnectingLine(
        startPointId: NosePointId.noseBridgeTop.toString(),
        endPointId: NosePointId.noseBridgeMiddle.toString(),
        style: LineStyle.solid,
        width: 2.0,
        color: const Color(0xFF4FC3F7),
        endColor: const Color(0xFF2196F3),
        isPrimary: true,
        type: NoseConnectionType.anatomical,
      ),
      ConnectingLine(
        startPointId: NosePointId.noseBridgeMiddle.toString(),
        endPointId: NosePointId.noseBridgeBottom.toString(),
        style: LineStyle.solid,
        width: 2.0,
        color: const Color(0xFF2196F3),
        endColor: const Color(0xFF1976D2),
        isPrimary: true,
        type: NoseConnectionType.anatomical,
      ),
      ConnectingLine(
        startPointId: NosePointId.noseBridgeBottom.toString(),
        endPointId: NosePointId.noseTip.toString(),
        style: LineStyle.solid,
        width: 2.0,
        color: const Color(0xFF1976D2),
        endColor: const Color(0xFF0D47A1),
        isPrimary: true,
        type: NoseConnectionType.anatomical,
      ),
    ];
  }
  
  /// 创建鼻翼连接线
  static List<ConnectingLine> createWingConnections() {
    return [
      // 左鼻翼
      ConnectingLine(
        startPointId: NosePointId.noseLeftWingTop.toString(),
        endPointId: NosePointId.noseLeftWingMiddle.toString(),
        style: LineStyle.solid,
        width: 1.5,
        color: const Color(0xFF4CAF50),
        isPrimary: false,
        type: NoseConnectionType.anatomical,
      ),
      ConnectingLine(
        startPointId: NosePointId.noseLeftWingMiddle.toString(),
        endPointId: NosePointId.noseLeftWingBottom.toString(),
        style: LineStyle.solid,
        width: 1.5,
        color: const Color(0xFF4CAF50),
        isPrimary: false,
        type: NoseConnectionType.anatomical,
      ),
      
      // 右鼻翼
      ConnectingLine(
        startPointId: NosePointId.noseRightWingTop.toString(),
        endPointId: NosePointId.noseRightWingMiddle.toString(),
        style: LineStyle.solid,
        width: 1.5,
        color: const Color(0xFF4CAF50),
        isPrimary: false,
        type: NoseConnectionType.anatomical,
      ),
      ConnectingLine(
        startPointId: NosePointId.noseRightWingMiddle.toString(),
        endPointId: NosePointId.noseRightWingBottom.toString(),
        style: LineStyle.solid,
        width: 1.5,
        color: const Color(0xFF4CAF50),
        isPrimary: false,
        type: NoseConnectionType.anatomical,
      ),
      
      // 鼻尖到鼻翼底部的连接
      ConnectingLine(
        startPointId: NosePointId.noseTip.toString(),
        endPointId: NosePointId.noseLeftWingBottom.toString(),
        style: LineStyle.solid,
        width: 1.5,
        color: const Color(0xFF0D47A1),
        endColor: const Color(0xFF4CAF50),
        isPrimary: true,
        type: NoseConnectionType.anatomical,
      ),
      ConnectingLine(
        startPointId: NosePointId.noseTip.toString(),
        endPointId: NosePointId.noseRightWingBottom.toString(),
        style: LineStyle.solid,
        width: 1.5,
        color: const Color(0xFF0D47A1),
        endColor: const Color(0xFF4CAF50),
        isPrimary: true,
        type: NoseConnectionType.anatomical,
      ),
    ];
  }
  
  /// 创建鼻孔连接线
  static List<ConnectingLine> createNostrilConnections() {
    return [
      // 左鼻孔
      ConnectingLine(
        startPointId: NosePointId.noseLeftWingBottom.toString(),
        endPointId: NosePointId.leftNostrilOuter.toString(),
        style: LineStyle.dashed,
        width: 1.0,
        color: const Color(0xFFFF9800),
        isPrimary: false,
        type: NoseConnectionType.auxiliary,
      ),
      ConnectingLine(
        startPointId: NosePointId.leftNostrilOuter.toString(),
        endPointId: NosePointId.leftNostrilInner.toString(),
        style: LineStyle.dashed,
        width: 1.0,
        color: const Color(0xFFFF9800),
        isPrimary: false,
        type: NoseConnectionType.auxiliary,
      ),
      ConnectingLine(
        startPointId: NosePointId.leftNostrilInner.toString(),
        endPointId: NosePointId.noseBase.toString(),
        style: LineStyle.dashed,
        width: 1.0,
        color: const Color(0xFFFF9800),
        isPrimary: false,
        type: NoseConnectionType.auxiliary,
      ),
      
      // 右鼻孔
      ConnectingLine(
        startPointId: NosePointId.noseRightWingBottom.toString(),
        endPointId: NosePointId.rightNostrilOuter.toString(),
        style: LineStyle.dashed,
        width: 1.0,
        color: const Color(0xFFFF9800),
        isPrimary: false,
        type: NoseConnectionType.auxiliary,
      ),
      ConnectingLine(
        startPointId: NosePointId.rightNostrilOuter.toString(),
        endPointId: NosePointId.rightNostrilInner.toString(),
        style: LineStyle.dashed,
        width: 1.0,
        color: const Color(0xFFFF9800),
        isPrimary: false,
        type: NoseConnectionType.auxiliary,
      ),
      ConnectingLine(
        startPointId: NosePointId.rightNostrilInner.toString(),
        endPointId: NosePointId.noseBase.toString(),
        style: LineStyle.dashed,
        width: 1.0,
        color: const Color(0xFFFF9800),
        isPrimary: false,
        type: NoseConnectionType.auxiliary,
      ),
    ];
  }
  
  /// 创建鼻基底连接线
  static List<ConnectingLine> createBaseConnections() {
    return [
      ConnectingLine(
        startPointId: NosePointId.noseBaseLeft.toString(),
        endPointId: NosePointId.noseBase.toString(),
        style: LineStyle.solid,
        width: 1.5,
        color: const Color(0xFF9C27B0),
        isPrimary: false,
        type: NoseConnectionType.anatomical,
      ),
      ConnectingLine(
        startPointId: NosePointId.noseBase.toString(),
        endPointId: NosePointId.noseBaseRight.toString(),
        style: LineStyle.solid,
        width: 1.5,
        color: const Color(0xFF9C27B0),
        isPrimary: false,
        type: NoseConnectionType.anatomical,
      ),
    ];
  }
  
  /// 创建所有鼻部连接线
  static List<ConnectingLine> createAllConnections() {
    return [
      ...createBridgeConnections(),
      ...createWingConnections(),
      ...createNostrilConnections(),
      ...createBaseConnections(),
    ];
  }
}
