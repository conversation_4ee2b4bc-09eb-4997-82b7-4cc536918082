/// 鼻子参数类型枚举
/// 
/// 定义了鼻子美化功能中的各种参数类型
enum NoseParameterType {
  /// 鼻梁高度
  bridgeHeight,
  
  /// 鼻尖调整
  tipAdjust,
  
  /// 鼻翼宽度
  nostrilWidth,
  
  /// 鼻基抬高
  baseHeight,
  
  /// 鼻梁宽度
  bridgeWidth,
  
  /// 鼻尖长度
  tipLength,
  
  /// 鼻尖高度
  tipHeight,
  
  /// 鼻尖宽度
  tipWidth,
  
  /// 鼻孔大小
  nostrilSize,
  
  /// 鼻基宽度
  baseWidth,
}

/// 参数约束类
/// 
/// 定义了参数的最小值和最大值
class ParameterConstraint {
  /// 最小值
  final double min;
  
  /// 最大值
  final double max;
  
  /// 构造函数
  const ParameterConstraint(this.min, this.max);
}

/// 鼻子参数类
/// 
/// 定义了鼻子美化功能中的参数
class NoseParameter {
  /// 参数类型
  final NoseParameterType type;
  
  /// 参数值
  double value;
  
  /// 参数约束
  final ParameterConstraint constraint;
  
  /// 构造函数
  NoseParameter(this.type, this.value, {this.constraint = const ParameterConstraint(-1.0, 1.0)});
}

/// 鼻子参数类型扩展方法
extension NoseParameterTypeExtension on NoseParameterType {
  /// 获取参数名称
  String get name {
    switch (this) {
      case NoseParameterType.bridgeHeight:
        return 'bridge_height';
      case NoseParameterType.tipAdjust:
        return 'tip_adjust';
      case NoseParameterType.nostrilWidth:
        return 'nostril_width';
      case NoseParameterType.baseHeight:
        return 'base_height';
      case NoseParameterType.bridgeWidth:
        return 'bridge_width';
      case NoseParameterType.tipLength:
        return 'tip_length';
      case NoseParameterType.tipHeight:
        return 'tip_height';
      case NoseParameterType.tipWidth:
        return 'tip_width';
      case NoseParameterType.nostrilSize:
        return 'nostril_size';
      case NoseParameterType.baseWidth:
        return 'base_width';
    }
  }
  
  /// 从参数名称获取参数类型
  static NoseParameterType? fromName(String name) {
    switch (name) {
      case 'bridge_height':
        return NoseParameterType.bridgeHeight;
      case 'tip_adjust':
        return NoseParameterType.tipAdjust;
      case 'nostril_width':
        return NoseParameterType.nostrilWidth;
      case 'base_height':
        return NoseParameterType.baseHeight;
      case 'bridge_width':
        return NoseParameterType.bridgeWidth;
      case 'tip_length':
        return NoseParameterType.tipLength;
      case 'tip_height':
        return NoseParameterType.tipHeight;
      case 'tip_width':
        return NoseParameterType.tipWidth;
      case 'nostril_size':
        return NoseParameterType.nostrilSize;
      case 'base_width':
        return NoseParameterType.baseWidth;
      default:
        return null;
    }
  }
}
