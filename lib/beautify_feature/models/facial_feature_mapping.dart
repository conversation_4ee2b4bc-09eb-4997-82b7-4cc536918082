import 'package:flutter/foundation.dart';

/// 特征点在区域中的角色
enum PointRole {
  primary,    // 主要特征点
  secondary,  // 次要特征点
  auxiliary   // 辅助特征点
}

/// 面部区域定义
class FacialRegion {
  final String name;
  final Map<int, PointRole> points;
  final List<List<int>> symmetryPairs;

  const FacialRegion({
    required this.name,
    required this.points,
    this.symmetryPairs = const [],
  });
}

/// 特征点在各区域中的角色映射
class FeaturePoint {
  final int index;
  final Map<String, PointRole> regionRoles;

  const FeaturePoint({
    required this.index,
    required this.regionRoles,
  });
}

/// 区域配置数据
class RegionConfig {
  static const Map<String, List<int>> primaryPoints = {
    'leftEye': [33, 133, 159, 145],
    'rightEye': [362, 263, 386, 374],
    'nose': [4, 49, 279, 197],
    'mouth': [61, 291, 0, 17, 405],
    'facialContour': [10, 338, 152, 432],
  };

  static const Map<String, List<int>> secondaryPoints = {
    'leftEye': [157, 158, 160, 161, 162, 144, 146, 147, 148, 149],
    'rightEye': [384, 385, 387, 388, 389, 373, 375, 376, 377, 378],
    'nose': [98, 97, 2, 326, 327, 168, 193, 122, 351, 417],
    'mouth': [40, 39, 37, 0, 267, 269, 270, 321, 314, 17, 84, 91, 146],
    'facialContour': [162, 21, 54, 103, 67, 109, 389, 251, 284, 332, 297, 338],
  };

  static const Map<String, Map<String, List<int>>> sharedPoints = {
    'nose': {
      'eyeRegion': [197, 168],
      'mouthRegion': [2, 326],
    },
    'mouth': {
      'noseRegion': [0, 17],
      'chinRegion': [146, 91],
    },
    'facialContour': {
      'eyeRegion': [10, 338],
      'cheekRegion': [162, 389],
    },
  };

  static const Map<String, List<List<int>>> symmetryPairs = {
    'eyes': [
      [33, 362],   // 外眼角对
      [133, 263],  // 内眼角对
      [159, 386],  // 上眼睑中点对
      [145, 374],  // 下眼睑中点对
    ],
    'nose': [
      [49, 279],   // 鼻翼对
      [98, 327],   // 鼻翼轮廓对
    ],
    'mouth': [
      [61, 291],   // 嘴角对
      [40, 270],   // 上唇对
      [84, 314],   // 下唇对
    ],
  };
}
