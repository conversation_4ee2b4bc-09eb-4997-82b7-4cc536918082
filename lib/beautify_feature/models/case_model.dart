import 'package:beautifun/beautify_feature/models/beautify_parameters.dart';

/// 案例类型枚举
enum CaseType {
  /// 面部整形
  face,
  
  /// 鼻部整形
  nose,
  
  /// 眼部整形
  eye,
  
  /// 唇部整形
  lip,
  
  /// 抗衰整形
  antiAging,
  
  /// 综合整形
  comprehensive,
}

/// 案例模型
class CaseModel {
  /// 案例ID
  final String id;
  
  /// 案例标题
  final String title;
  
  /// 案例描述
  final String description;
  
  /// 案例类型
  final CaseType type;
  
  /// 前图像路径
  final String beforeImagePath;
  
  /// 后图像路径
  final String afterImagePath;
  
  /// 美容参数
  final BeautifyParameters parameters;
  
  /// 恢复时间（天）
  final int recoveryDays;
  
  /// 难度（1-5）
  final int difficulty;
  
  /// 满意度（1-5）
  final int satisfaction;
  
  /// 标签
  final List<String> tags;
  
  /// 构造函数
  const CaseModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.beforeImagePath,
    required this.afterImagePath,
    required this.parameters,
    required this.recoveryDays,
    required this.difficulty,
    required this.satisfaction,
    this.tags = const [],
  });
  
  /// 从JSON创建案例
  factory CaseModel.fromJson(Map<String, dynamic> json) {
    return CaseModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: _parseCaseType(json['type']),
      beforeImagePath: json['beforeImagePath'],
      afterImagePath: json['afterImagePath'],
      parameters: BeautifyParameters.fromJson(json['parameters']),
      recoveryDays: json['recoveryDays'],
      difficulty: json['difficulty'],
      satisfaction: json['satisfaction'],
      tags: json.containsKey('tags')
          ? List<String>.from(json['tags'])
          : const [],
    );
  }
  
  /// 解析案例类型
  static CaseType _parseCaseType(String value) {
    switch (value.toLowerCase()) {
      case 'face':
        return CaseType.face;
      case 'nose':
        return CaseType.nose;
      case 'eye':
        return CaseType.eye;
      case 'lip':
        return CaseType.lip;
      case 'antiaging':
        return CaseType.antiAging;
      case 'comprehensive':
        return CaseType.comprehensive;
      default:
        return CaseType.comprehensive;
    }
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': _caseTypeToString(type),
      'beforeImagePath': beforeImagePath,
      'afterImagePath': afterImagePath,
      'parameters': parameters.toJson(),
      'recoveryDays': recoveryDays,
      'difficulty': difficulty,
      'satisfaction': satisfaction,
      'tags': tags,
    };
  }
  
  /// 案例类型转字符串
  static String _caseTypeToString(CaseType type) {
    switch (type) {
      case CaseType.face:
        return 'face';
      case CaseType.nose:
        return 'nose';
      case CaseType.eye:
        return 'eye';
      case CaseType.lip:
        return 'lip';
      case CaseType.antiAging:
        return 'antiaging';
      case CaseType.comprehensive:
        return 'comprehensive';
    }
  }
  
  /// 获取案例类型文本
  String get typeText {
    switch (type) {
      case CaseType.face:
        return '面部整形';
      case CaseType.nose:
        return '鼻部整形';
      case CaseType.eye:
        return '眼部整形';
      case CaseType.lip:
        return '唇部整形';
      case CaseType.antiAging:
        return '抗衰整形';
      case CaseType.comprehensive:
        return '综合整形';
    }
  }
  
  /// 获取恢复时间文本
  String get recoveryTimeText {
    if (recoveryDays <= 0) {
      return '无需恢复期';
    } else if (recoveryDays < 7) {
      return '$recoveryDays天';
    } else if (recoveryDays < 30) {
      final weeks = (recoveryDays / 7).ceil();
      return '$weeks周';
    } else {
      final months = (recoveryDays / 30).ceil();
      return '$months个月';
    }
  }
  
  /// 获取难度文本
  String get difficultyText {
    switch (difficulty) {
      case 1:
        return '极易';
      case 2:
        return '简单';
      case 3:
        return '一般';
      case 4:
        return '困难';
      case 5:
        return '极难';
      default:
        return '未知';
    }
  }
  
  /// 获取满意度文本
  String get satisfactionText {
    switch (satisfaction) {
      case 1:
        return '不满意';
      case 2:
        return '一般';
      case 3:
        return '满意';
      case 4:
        return '很满意';
      case 5:
        return '非常满意';
      default:
        return '未知';
    }
  }
  
  @override
  String toString() {
    return 'CaseModel(id: $id, title: $title, type: $type)';
  }
}
