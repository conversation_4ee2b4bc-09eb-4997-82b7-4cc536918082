/// 风险等级枚举
enum RiskLevel {
  /// 低风险
  low,
  
  /// 中风险
  medium,
  
  /// 高风险
  high,
}

/// 医学建议模型
class MedicalAdvice {
  /// 建议标题
  final String title;
  
  /// 建议描述
  final String description;
  
  /// 风险等级
  final RiskLevel riskLevel;
  
  /// 恢复时间（天）
  final int recoveryDays;
  
  /// 相关参数
  final List<String> relatedParameters;
  
  /// 构造函数
  const MedicalAdvice({
    required this.title,
    required this.description,
    required this.riskLevel,
    required this.recoveryDays,
    this.relatedParameters = const [],
  });
  
  /// 从JSON创建医学建议
  factory MedicalAdvice.fromJson(Map<String, dynamic> json) {
    return MedicalAdvice(
      title: json['title'],
      description: json['description'],
      riskLevel: _parseRiskLevel(json['riskLevel']),
      recoveryDays: json['recoveryDays'],
      relatedParameters: json.containsKey('relatedParameters')
          ? List<String>.from(json['relatedParameters'])
          : const [],
    );
  }
  
  /// 解析风险等级
  static RiskLevel _parseRiskLevel(String value) {
    switch (value.toLowerCase()) {
      case 'low':
        return RiskLevel.low;
      case 'medium':
        return RiskLevel.medium;
      case 'high':
        return RiskLevel.high;
      default:
        return RiskLevel.low;
    }
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'riskLevel': _riskLevelToString(riskLevel),
      'recoveryDays': recoveryDays,
      'relatedParameters': relatedParameters,
    };
  }
  
  /// 风险等级转字符串
  static String _riskLevelToString(RiskLevel level) {
    switch (level) {
      case RiskLevel.low:
        return 'low';
      case RiskLevel.medium:
        return 'medium';
      case RiskLevel.high:
        return 'high';
    }
  }
  
  /// 获取风险等级颜色
  String get riskColor {
    switch (riskLevel) {
      case RiskLevel.low:
        return '#4CAF50'; // 绿色
      case RiskLevel.medium:
        return '#FFC107'; // 黄色
      case RiskLevel.high:
        return '#F44336'; // 红色
    }
  }
  
  /// 获取风险等级文本
  String get riskText {
    switch (riskLevel) {
      case RiskLevel.low:
        return '低风险';
      case RiskLevel.medium:
        return '中风险';
      case RiskLevel.high:
        return '高风险';
    }
  }
  
  /// 获取恢复时间文本
  String get recoveryTimeText {
    if (recoveryDays <= 0) {
      return '无需恢复期';
    } else if (recoveryDays < 7) {
      return '$recoveryDays天';
    } else if (recoveryDays < 30) {
      final weeks = (recoveryDays / 7).ceil();
      return '$weeks周';
    } else {
      final months = (recoveryDays / 30).ceil();
      return '$months个月';
    }
  }
  
  @override
  String toString() {
    return 'MedicalAdvice(title: $title, riskLevel: $riskLevel, recoveryDays: $recoveryDays)';
  }
}

/// 医学分析结果模型
class MedicalAnalysisResult {
  /// 总体风险评估
  final RiskLevel overallRisk;
  
  /// 医学建议列表
  final List<MedicalAdvice> advices;
  
  /// 构造函数
  const MedicalAnalysisResult({
    required this.overallRisk,
    required this.advices,
  });
  
  /// 从JSON创建医学分析结果
  factory MedicalAnalysisResult.fromJson(Map<String, dynamic> json) {
    return MedicalAnalysisResult(
      overallRisk: MedicalAdvice._parseRiskLevel(json['overallRisk']),
      advices: (json['advices'] as List)
          .map((advice) => MedicalAdvice.fromJson(advice))
          .toList(),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'overallRisk': MedicalAdvice._riskLevelToString(overallRisk),
      'advices': advices.map((advice) => advice.toJson()).toList(),
    };
  }
  
  /// 获取最大恢复时间
  int get maxRecoveryDays {
    if (advices.isEmpty) {
      return 0;
    }
    
    return advices
        .map((advice) => advice.recoveryDays)
        .reduce((max, days) => days > max ? days : max);
  }
  
  /// 获取最大恢复时间文本
  String get maxRecoveryTimeText {
    final days = maxRecoveryDays;
    
    if (days <= 0) {
      return '无需恢复期';
    } else if (days < 7) {
      return '$days天';
    } else if (days < 30) {
      final weeks = (days / 7).ceil();
      return '$weeks周';
    } else {
      final months = (days / 30).ceil();
      return '$months个月';
    }
  }
  
  /// 获取总体风险文本
  String get overallRiskText {
    switch (overallRisk) {
      case RiskLevel.low:
        return '低风险';
      case RiskLevel.medium:
        return '中风险';
      case RiskLevel.high:
        return '高风险';
    }
  }
  
  /// 获取总体风险颜色
  String get overallRiskColor {
    switch (overallRisk) {
      case RiskLevel.low:
        return '#4CAF50'; // 绿色
      case RiskLevel.medium:
        return '#FFC107'; // 黄色
      case RiskLevel.high:
        return '#F44336'; // 红色
    }
  }
  
  @override
  String toString() {
    return 'MedicalAnalysisResult(overallRisk: $overallRisk, advices: ${advices.length})';
  }
}
