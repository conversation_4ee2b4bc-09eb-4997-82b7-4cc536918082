import 'dart:ui';
import 'package:flutter/foundation.dart';
import '../../core/models/feature_point.dart';

/// 鼻部特征点标识符枚举
/// 根据MediaPipe Face Mesh模型的索引定义
enum NosePointId {
  // 鼻梁特征点
  noseBridgeTop, // 鼻梁顶部
  noseBridgeMiddle, // 鼻梁中部
  noseBridgeBottom, // 鼻梁底部
  
  // 鼻尖特征点
  noseTip, // 鼻尖
  
  // 鼻翼特征点
  noseLeftWingTop, // 左鼻翼顶部
  noseLeftWingMiddle, // 左鼻翼中部
  noseLeftWingBottom, // 左鼻翼底部
  noseRightWingTop, // 右鼻翼顶部
  noseRightWingMiddle, // 右鼻翼中部
  noseRightWingBottom, // 右鼻翼底部
  
  // 鼻孔特征点
  leftNostrilInner, // 左鼻孔内侧
  leftNostrilOuter, // 左鼻孔外侧
  rightNostrilInner, // 右鼻孔内侧
  rightNostrilOuter, // 右鼻孔外侧
  
  // 鼻基底特征点
  noseBase, // 鼻基底中心
  noseBaseLeft, // 鼻基底左侧
  noseBaseRight, // 鼻基底右侧
}

/// 鼻部特征点连接线定义
class NoseConnection {
  final NosePointId from;
  final NosePointId to;
  final bool isPrimary;
  
  const NoseConnection(this.from, this.to, {this.isPrimary = false});
}

/// 鼻部特征点管理类
class NoseFeaturePoints {
  /// 鼻部特征点映射表
  final Map<NosePointId, FeaturePoint> points;
  
  /// 鼻部特征点连接线列表
  final List<NoseConnection> connections;
  
  /// 日志标签
  static const String _logTag = 'NoseFeaturePoints';
  
  /// 构造函数
  NoseFeaturePoints({
    required this.points,
    required this.connections,
  }) {
    _logInitialization();
  }
  
  /// 记录初始化日志
  void _logInitialization() {
    if (kDebugMode) {
      print('[$_logTag] [初始化] | 特征点数量: ${points.length}, 连接线数量: ${connections.length}');
      points.forEach((id, point) {
        print('[$_logTag] [特征点] [${id.toString()}] | 位置: (${point.x.toStringAsFixed(2)}, ${point.y.toStringAsFixed(2)}), 主要: ${point.isPrimary}');
      });
    }
  }
  
  /// 从MediaPipe特征点列表创建鼻部特征点
  /// [allFeaturePoints] 所有面部特征点列表
  /// [mediapipeToNoseMap] MediaPipe索引到鼻部特征点ID的映射
  static NoseFeaturePoints fromMediaPipePoints({
    required List<FeaturePoint> allFeaturePoints,
    required Map<int, NosePointId> mediapipeToNoseMap,
  }) {
    if (kDebugMode) {
      print('[$_logTag] [fromMediaPipePoints] [开始] | 输入特征点数量: ${allFeaturePoints.length}');
    }
    
    final Map<NosePointId, FeaturePoint> nosePoints = {};
    
    // 根据映射关系提取鼻部特征点
    mediapipeToNoseMap.forEach((mediapipeIndex, noseId) {
      if (mediapipeIndex < allFeaturePoints.length) {
        final point = allFeaturePoints[mediapipeIndex];
        // 设置主要特征点
        final isPrimary = _isPrimaryPoint(noseId);
        nosePoints[noseId] = point.copyWith(
          name: noseId.toString(),
          isPrimary: isPrimary,
        );
        
        if (kDebugMode) {
          print('[$_logTag] [特征点映射] [${noseId.toString()}] | MediaPipe索引: $mediapipeIndex, 主要: $isPrimary');
        }
      } else {
        if (kDebugMode) {
          print('[$_logTag] [错误] [特征点映射] | MediaPipe索引超出范围: $mediapipeIndex');
        }
      }
    });
    
    // 创建连接线
    final connections = _loadConnections();
    
    if (kDebugMode) {
      print('[$_logTag] [fromMediaPipePoints] [完成] | 提取鼻部特征点数量: ${nosePoints.length}, 连接线数量: ${connections.length}');
    }
    
    return NoseFeaturePoints(
      points: nosePoints,
      connections: connections,
    );
  }
  
  /// 判断是否为主要特征点
  static bool _isPrimaryPoint(NosePointId id) {
    // 主要特征点列表
    const primaryPoints = {
      NosePointId.noseTip,
      NosePointId.noseBase,
      NosePointId.noseBridgeTop,
      NosePointId.leftNostrilOuter,
      NosePointId.rightNostrilOuter,
    };
    
    return primaryPoints.contains(id);
  }
  
  /// 获取特征点描述
  static String _getPointDescription(NosePointId id) {
    switch (id) {
      case NosePointId.noseBridgeTop:
        return '鼻梁顶部';
      case NosePointId.noseBridgeMiddle:
        return '鼻梁中部';
      case NosePointId.noseBridgeBottom:
        return '鼻梁底部';
      case NosePointId.noseTip:
        return '鼻尖';
      case NosePointId.noseLeftWingTop:
        return '左鼻翼顶部';
      case NosePointId.noseLeftWingMiddle:
        return '左鼻翼中部';
      case NosePointId.noseLeftWingBottom:
        return '左鼻翼底部';
      case NosePointId.noseRightWingTop:
        return '右鼻翼顶部';
      case NosePointId.noseRightWingMiddle:
        return '右鼻翼中部';
      case NosePointId.noseRightWingBottom:
        return '右鼻翼底部';
      case NosePointId.leftNostrilInner:
        return '左鼻孔内侧';
      case NosePointId.leftNostrilOuter:
        return '左鼻孔外侧';
      case NosePointId.rightNostrilInner:
        return '右鼻孔内侧';
      case NosePointId.rightNostrilOuter:
        return '右鼻孔外侧';
      case NosePointId.noseBase:
        return '鼻基底中心';
      case NosePointId.noseBaseLeft:
        return '鼻基底左侧';
      case NosePointId.noseBaseRight:
        return '鼻基底右侧';
    }
  }
  
  /// 加载鼻部特征点连接关系
  static List<NoseConnection> _loadConnections() {
    // 记录日志
    if (kDebugMode) {
      print('[$_logTag] [加载] [连接关系] | 加载鼻部特征点连接关系');
    }
    
    // 这里应该从配置文件加载连接关系，而不是使用硬编码的默认值
    // 如果无法加载配置，则记录错误并返回空列表
    try {
      // TODO: 从配置文件加载连接关系
      // 临时返回连接关系，但这应该被替换为从配置加载的实现
      return [
        // 鼻梁连接线 (主要)
        NoseConnection(NosePointId.noseBridgeTop, NosePointId.noseBridgeMiddle, isPrimary: true),
        NoseConnection(NosePointId.noseBridgeMiddle, NosePointId.noseBridgeBottom, isPrimary: true),
        NoseConnection(NosePointId.noseBridgeBottom, NosePointId.noseTip, isPrimary: true),
        
        // 鼻尖到鼻基底连接线 (主要)
        NoseConnection(NosePointId.noseTip, NosePointId.noseBase, isPrimary: true),
        
        // 鼻基底连接线 (主要)
        NoseConnection(NosePointId.noseBaseLeft, NosePointId.noseBase, isPrimary: true),
        NoseConnection(NosePointId.noseBase, NosePointId.noseBaseRight, isPrimary: true),
        
        // 左鼻翼连接线
        NoseConnection(NosePointId.noseLeftWingTop, NosePointId.noseLeftWingMiddle),
        NoseConnection(NosePointId.noseLeftWingMiddle, NosePointId.noseLeftWingBottom),
        NoseConnection(NosePointId.noseLeftWingBottom, NosePointId.leftNostrilOuter),
        
        // 右鼻翼连接线
        NoseConnection(NosePointId.noseRightWingTop, NosePointId.noseRightWingMiddle),
        NoseConnection(NosePointId.noseRightWingMiddle, NosePointId.noseRightWingBottom),
        NoseConnection(NosePointId.noseRightWingBottom, NosePointId.rightNostrilOuter),
        
        // 鼻孔连接线
        NoseConnection(NosePointId.leftNostrilInner, NosePointId.leftNostrilOuter),
        NoseConnection(NosePointId.rightNostrilInner, NosePointId.rightNostrilOuter),
        
        // 鼻翼底部到鼻基底连接线
        NoseConnection(NosePointId.noseLeftWingBottom, NosePointId.noseBaseLeft),
        NoseConnection(NosePointId.noseRightWingBottom, NosePointId.noseBaseRight),
      ];
    } catch (e) {
      if (kDebugMode) {
        print('[$_logTag] [错误] [连接关系] | 加载连接关系失败: $e');
      }
      return [];
    }
  }
  
  /// 获取所有特征点列表
  List<FeaturePoint> getAllPoints() {
    return points.values.toList();
  }
  
  /// 获取主要特征点列表
  List<FeaturePoint> getPrimaryPoints() {
    return points.values.where((point) => point.isPrimary).toList();
  }
  
  /// 获取次要特征点列表
  List<FeaturePoint> getSecondaryPoints() {
    return points.values.where((point) => !point.isPrimary).toList();
  }
  
  /// 获取特定特征点
  FeaturePoint? getPoint(NosePointId id) {
    return points[id];
  }
  
  /// 获取主要连接线
  List<NoseConnection> getPrimaryConnections() {
    return connections.where((conn) => conn.isPrimary).toList();
  }
  
  /// 获取次要连接线
  List<NoseConnection> getSecondaryConnections() {
    return connections.where((conn) => !conn.isPrimary).toList();
  }
  
  /// 加载MediaPipe索引到鼻部特征点ID的映射
  /// 这些索引值需要根据实际使用的MediaPipe Face Mesh模型进行调整
  static Map<int, NosePointId> loadMediaPipeMapping() {
    // 记录日志
    if (kDebugMode) {
      print('[$_logTag] [加载] [MediaPipe映射] | 加载MediaPipe索引到鼻部特征点ID的映射');
    }
    
    // 这里应该从配置文件加载映射关系，而不是使用硬编码的默认值
    // 如果无法加载配置，则记录错误并返回空映射
    try {
      // TODO: 从配置文件加载映射关系
      // 临时返回映射，但这应该被替换为从配置加载的实现
      return {
        // 这些索引值需要根据实际的MediaPipe Face Mesh模型进行调整
        1: NosePointId.noseBridgeTop,
        4: NosePointId.noseBridgeMiddle,
        6: NosePointId.noseBridgeBottom,
        9: NosePointId.noseTip,
        
        197: NosePointId.noseLeftWingTop,
        419: NosePointId.noseLeftWingMiddle,
        242: NosePointId.noseLeftWingBottom,
        
        417: NosePointId.noseRightWingTop,
        141: NosePointId.noseRightWingMiddle,
        22: NosePointId.noseRightWingBottom,
        
        235: NosePointId.leftNostrilInner,
        44: NosePointId.leftNostrilOuter,
        
        455: NosePointId.rightNostrilInner,
        266: NosePointId.rightNostrilOuter,
        
        2: NosePointId.noseBase,
        98: NosePointId.noseBaseLeft,
        327: NosePointId.noseBaseRight,
      };
    } catch (e) {
      if (kDebugMode) {
        print('[$_logTag] [错误] [MediaPipe映射] | 加载MediaPipe映射失败: $e');
      }
      return {};
    }
  }
  
  /// 创建一个空的鼻部特征点集合
  factory NoseFeaturePoints.empty() {
    if (kDebugMode) {
      print('[$_logTag] [创建] [空特征点集] | 创建空的鼻部特征点集合');
    }
    
    return NoseFeaturePoints(
      points: {},
      connections: _loadConnections(),
    );
  }
}
