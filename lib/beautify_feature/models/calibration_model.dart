import 'package:beautifun/core/services/logger_service.dart';
import 'package:beautifun/beautify_feature/models/feature_area_type.dart';
import 'package:beautifun/beautify_feature/models/feature_point.dart';

/// 校准模型
/// 存储特征区域校准的参数和结果
class CalibrationModel {
  /// 校准的特征区域类型
  final FeatureAreaType areaType;
  
  /// 原始特征点列表
  final List<FeaturePoint> originalPoints;
  
  /// 校准后的特征点列表
  final List<FeaturePoint> calibratedPoints;
  
  /// 校准置信度分数 (0.0-1.0)
  final double confidenceScore;
  
  /// 应用的校准规则列表
  final List<String> appliedRules;
  
  /// 校准时间
  final DateTime calibrationTime;
  
  /// 医学指标
  final Map<String, dynamic> medicalMetrics;
  
  /// 校准建议
  final List<CalibrationSuggestion> suggestions;
  
  /// 校准是否有效
  final bool isValid;
  
  /// 构造函数
  CalibrationModel({
    required this.areaType,
    required this.originalPoints,
    required this.calibratedPoints,
    required this.confidenceScore,
    required this.appliedRules,
    required this.medicalMetrics,
    this.suggestions = const [],
    this.isValid = true,
    DateTime? calibrationTime,
  }) : calibrationTime = calibrationTime ?? DateTime.now() {
    LoggerService.instance.logInfo(
      'CalibrationModel', 
      'Created calibration model for ${areaType.toString().split('.').last} with ${calibratedPoints.length} points'
    );
  }
  
  /// 从JSON创建校准模型
  factory CalibrationModel.fromJson(Map<String, dynamic> json) {
    return CalibrationModel(
      areaType: FeatureAreaType.values.firstWhere(
        (e) => e.toString().split('.').last == json['areaType'],
        orElse: () => FeatureAreaType.none,
      ),
      originalPoints: (json['originalPoints'] as List)
          .map((p) => FeaturePoint.fromJson(p))
          .toList(),
      calibratedPoints: (json['calibratedPoints'] as List)
          .map((p) => FeaturePoint.fromJson(p))
          .toList(),
      confidenceScore: json['confidenceScore'].toDouble(),
      appliedRules: (json['appliedRules'] as List).cast<String>(),
      medicalMetrics: json['medicalMetrics'] as Map<String, dynamic>,
      suggestions: json['suggestions'] != null
          ? (json['suggestions'] as List)
              .map((s) => CalibrationSuggestion.fromJson(s))
              .toList()
          : [],
      isValid: json['isValid'] ?? true,
      calibrationTime: DateTime.parse(json['calibrationTime']),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'areaType': areaType.toString().split('.').last,
      'originalPoints': originalPoints.map((p) => {
        'x': p.position.dx,
        'y': p.position.dy,
        'z': p.z,
        'visibility': p.visibility,
        'isPrimary': p.isPrimary,
        'confidence': p.confidence,
        'id': p.id,
        'description': p.description,
        'isHighlighted': p.isHighlighted,
      }).toList(),
      'calibratedPoints': calibratedPoints.map((p) => {
        'x': p.position.dx,
        'y': p.position.dy,
        'z': p.z,
        'visibility': p.visibility,
        'isPrimary': p.isPrimary,
        'confidence': p.confidence,
        'id': p.id,
        'description': p.description,
        'isHighlighted': p.isHighlighted,
      }).toList(),
      'confidenceScore': confidenceScore,
      'appliedRules': appliedRules,
      'medicalMetrics': medicalMetrics,
      'suggestions': suggestions.map((s) => s.toJson()).toList(),
      'isValid': isValid,
      'calibrationTime': calibrationTime.toIso8601String(),
    };
  }
  
  /// 创建副本并更新特定属性
  CalibrationModel copyWith({
    FeatureAreaType? areaType,
    List<FeaturePoint>? originalPoints,
    List<FeaturePoint>? calibratedPoints,
    double? confidenceScore,
    List<String>? appliedRules,
    Map<String, dynamic>? medicalMetrics,
    List<CalibrationSuggestion>? suggestions,
    bool? isValid,
    DateTime? calibrationTime,
  }) {
    return CalibrationModel(
      areaType: areaType ?? this.areaType,
      originalPoints: originalPoints ?? this.originalPoints,
      calibratedPoints: calibratedPoints ?? this.calibratedPoints,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      appliedRules: appliedRules ?? this.appliedRules,
      medicalMetrics: medicalMetrics ?? this.medicalMetrics,
      suggestions: suggestions ?? this.suggestions,
      isValid: isValid ?? this.isValid,
      calibrationTime: calibrationTime ?? this.calibrationTime,
    );
  }
  
  /// 获取原始点和校准点的差异
  List<PointDifference> getPointDifferences() {
    List<PointDifference> differences = [];
    
    for (int i = 0; i < originalPoints.length; i++) {
      if (i < calibratedPoints.length) {
        final original = originalPoints[i];
        final calibrated = calibratedPoints[i];
        
        if (original.id == calibrated.id) {
          differences.add(PointDifference(
            pointId: original.id,
            originalPosition: original.position,
            calibratedPosition: calibrated.position,
            originalZ: original.z,
            calibratedZ: calibrated.z,
          ));
        }
      }
    }
    
    return differences;
  }
  
  /// 获取校准中最大的位移
  PointDifference getMaxDisplacement() {
    final differences = getPointDifferences();
    if (differences.isEmpty) {
      throw Exception('No point differences available');
    }
    
    PointDifference maxDiff = differences.first;
    double maxDistance = maxDiff.displacementDistance;
    
    for (var diff in differences) {
      if (diff.displacementDistance > maxDistance) {
        maxDistance = diff.displacementDistance;
        maxDiff = diff;
      }
    }
    
    return maxDiff;
  }
  
  /// 获取校准中平均位移
  double getAverageDisplacement() {
    final differences = getPointDifferences();
    if (differences.isEmpty) {
      return 0.0;
    }
    
    double totalDistance = 0.0;
    for (var diff in differences) {
      totalDistance += diff.displacementDistance;
    }
    
    return totalDistance / differences.length;
  }
  
  /// 获取特定医学指标
  dynamic getMedicalMetric(String metricName) {
    return medicalMetrics[metricName];
  }
  
  /// 获取所有医学指标名称
  List<String> getMedicalMetricNames() {
    return medicalMetrics.keys.toList();
  }
  
  /// 获取风险等级建议
  List<CalibrationSuggestion> getRiskSuggestions() {
    return suggestions.where((s) => s.type == SuggestionType.risk).toList();
  }
  
  /// 获取美学建议
  List<CalibrationSuggestion> getAestheticSuggestions() {
    return suggestions.where((s) => s.type == SuggestionType.aesthetic).toList();
  }
  
  /// 获取医学建议
  List<CalibrationSuggestion> getMedicalSuggestions() {
    return suggestions.where((s) => s.type == SuggestionType.medical).toList();
  }
  
  /// 检查校准是否超过安全阈值
  bool exceedsSafetyThreshold(double threshold) {
    return getAverageDisplacement() > threshold;
  }
}

/// 点位差异
/// 记录原始点和校准点之间的差异
class PointDifference {
  /// 点位ID
  final String pointId;
  
  /// 原始位置
  final Offset originalPosition;
  
  /// 校准后位置
  final Offset calibratedPosition;
  
  /// 原始Z坐标
  final double originalZ;
  
  /// 校准后Z坐标
  final double calibratedZ;
  
  /// 构造函数
  PointDifference({
    required this.pointId,
    required this.originalPosition,
    required this.calibratedPosition,
    required this.originalZ,
    required this.calibratedZ,
  });
  
  /// 获取位移距离
  double get displacementDistance {
    final dx = calibratedPosition.dx - originalPosition.dx;
    final dy = calibratedPosition.dy - originalPosition.dy;
    return (dx * dx + dy * dy).sqrt();
  }
  
  /// 获取Z轴位移
  double get zDisplacement => calibratedZ - originalZ;
  
  /// 获取位移方向（弧度）
  double get displacementDirection {
    final dx = calibratedPosition.dx - originalPosition.dx;
    final dy = calibratedPosition.dy - originalPosition.dy;
    return (dy).atan2(dx);
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'pointId': pointId,
      'originalPosition': {
        'x': originalPosition.dx,
        'y': originalPosition.dy,
      },
      'calibratedPosition': {
        'x': calibratedPosition.dx,
        'y': calibratedPosition.dy,
      },
      'originalZ': originalZ,
      'calibratedZ': calibratedZ,
      'displacementDistance': displacementDistance,
      'zDisplacement': zDisplacement,
      'displacementDirection': displacementDirection,
    };
  }
}

/// 建议类型
enum SuggestionType {
  /// 风险建议
  risk,
  
  /// 美学建议
  aesthetic,
  
  /// 医学建议
  medical,
}

/// 风险等级
enum RiskLevel {
  /// 低风险
  low,
  
  /// 中等风险
  medium,
  
  /// 高风险
  high,
}

/// 校准建议
/// 提供关于校准的建议和警告
class CalibrationSuggestion {
  /// 建议ID
  final String id;
  
  /// 建议类型
  final SuggestionType type;
  
  /// 建议标题
  final String title;
  
  /// 建议描述
  final String description;
  
  /// 风险等级（如果适用）
  final RiskLevel? riskLevel;
  
  /// 相关点位ID列表
  final List<String> relatedPointIds;
  
  /// 相关医学指标
  final Map<String, dynamic>? relatedMetrics;
  
  /// 构造函数
  CalibrationSuggestion({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    this.riskLevel,
    this.relatedPointIds = const [],
    this.relatedMetrics,
  });
  
  /// 从JSON创建校准建议
  factory CalibrationSuggestion.fromJson(Map<String, dynamic> json) {
    return CalibrationSuggestion(
      id: json['id'],
      type: SuggestionType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      title: json['title'],
      description: json['description'],
      riskLevel: json['riskLevel'] != null
          ? RiskLevel.values.firstWhere(
              (e) => e.toString().split('.').last == json['riskLevel'],
            )
          : null,
      relatedPointIds: json['relatedPointIds'] != null
          ? (json['relatedPointIds'] as List).cast<String>()
          : [],
      relatedMetrics: json['relatedMetrics'],
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'title': title,
      'description': description,
      'riskLevel': riskLevel?.toString().split('.').last,
      'relatedPointIds': relatedPointIds,
      'relatedMetrics': relatedMetrics,
    };
  }
}
