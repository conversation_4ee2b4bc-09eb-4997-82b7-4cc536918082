/// 美容参数管理类
class BeautifyParameters {
  /// 面部参数
  final FaceParameters face;
  
  /// 鼻部参数
  final NoseParameters nose;
  
  /// 眼部参数
  final EyeParameters eye;
  
  /// 唇部参数
  final LipParameters lip;
  
  /// 抗衰参数
  final AntiAgingParameters antiAging;
  
  /// 构造函数
  const BeautifyParameters({
    this.face = const FaceParameters(),
    this.nose = const NoseParameters(),
    this.eye = const EyeParameters(),
    this.lip = const LipParameters(),
    this.antiAging = const AntiAgingParameters(),
  });
  
  /// 从JSON创建参数
  factory BeautifyParameters.fromJson(Map<String, dynamic> json) {
    return BeautifyParameters(
      face: json.containsKey('face')
          ? FaceParameters.fromJson(json['face'])
          : const FaceParameters(),
      nose: json.containsKey('nose')
          ? NoseParameters.fromJson(json['nose'])
          : const NoseParameters(),
      eye: json.containsKey('eye')
          ? EyeParameters.fromJson(json['eye'])
          : const EyeParameters(),
      lip: json.containsKey('lip')
          ? LipParameters.fromJson(json['lip'])
          : const LipParameters(),
      antiAging: json.containsKey('antiAging')
          ? AntiAgingParameters.fromJson(json['antiAging'])
          : const AntiAgingParameters(),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'face': face.toJson(),
      'nose': nose.toJson(),
      'eye': eye.toJson(),
      'lip': lip.toJson(),
      'antiAging': antiAging.toJson(),
    };
  }
  
  /// 转换为扁平化参数映射
  Map<String, double> toFlatMap() {
    final result = <String, double>{};
    
    // 添加面部参数
    face.toJson().forEach((key, value) {
      result['face_$key'] = value;
    });
    
    // 添加鼻部参数
    nose.toJson().forEach((key, value) {
      result['nose_$key'] = value;
    });
    
    // 添加眼部参数
    eye.toJson().forEach((key, value) {
      result['eye_$key'] = value;
    });
    
    // 添加唇部参数
    lip.toJson().forEach((key, value) {
      result['lip_$key'] = value;
    });
    
    // 添加抗衰参数
    antiAging.toJson().forEach((key, value) {
      result['antiAging_$key'] = value;
    });
    
    return result;
  }
  
  /// 创建参数副本
  BeautifyParameters copyWith({
    FaceParameters? face,
    NoseParameters? nose,
    EyeParameters? eye,
    LipParameters? lip,
    AntiAgingParameters? antiAging,
  }) {
    return BeautifyParameters(
      face: face ?? this.face,
      nose: nose ?? this.nose,
      eye: eye ?? this.eye,
      lip: lip ?? this.lip,
      antiAging: antiAging ?? this.antiAging,
    );
  }
  
  /// 重置所有参数
  BeautifyParameters reset() {
    return const BeautifyParameters();
  }
  
  @override
  String toString() {
    return 'BeautifyParameters(face: $face, nose: $nose, eye: $eye, lip: $lip, antiAging: $antiAging)';
  }
}

/// 面部参数类
class FaceParameters {
  /// 脸型（0.0-1.0，0.5为默认）
  final double faceShape;
  
  /// 下颌线（0.0-1.0，0.5为默认）
  final double jawline;
  
  /// 颧骨（0.0-1.0，0.5为默认）
  final double cheekbone;
  
  /// 下巴（0.0-1.0，0.5为默认）
  final double chin;
  
  /// 额头（0.0-1.0，0.5为默认）
  final double forehead;
  
  /// 构造函数
  const FaceParameters({
    this.faceShape = 0.5,
    this.jawline = 0.5,
    this.cheekbone = 0.5,
    this.chin = 0.5,
    this.forehead = 0.5,
  });
  
  /// 从JSON创建参数
  factory FaceParameters.fromJson(Map<String, dynamic> json) {
    return FaceParameters(
      faceShape: json['faceShape']?.toDouble() ?? 0.5,
      jawline: json['jawline']?.toDouble() ?? 0.5,
      cheekbone: json['cheekbone']?.toDouble() ?? 0.5,
      chin: json['chin']?.toDouble() ?? 0.5,
      forehead: json['forehead']?.toDouble() ?? 0.5,
    );
  }
  
  /// 转换为JSON
  Map<String, double> toJson() {
    return {
      'faceShape': faceShape,
      'jawline': jawline,
      'cheekbone': cheekbone,
      'chin': chin,
      'forehead': forehead,
    };
  }
  
  /// 创建参数副本
  FaceParameters copyWith({
    double? faceShape,
    double? jawline,
    double? cheekbone,
    double? chin,
    double? forehead,
  }) {
    return FaceParameters(
      faceShape: faceShape ?? this.faceShape,
      jawline: jawline ?? this.jawline,
      cheekbone: cheekbone ?? this.cheekbone,
      chin: chin ?? this.chin,
      forehead: forehead ?? this.forehead,
    );
  }
  
  @override
  String toString() {
    return 'FaceParameters(faceShape: $faceShape, jawline: $jawline, cheekbone: $cheekbone, chin: $chin, forehead: $forehead)';
  }
}

/// 鼻部参数类
class NoseParameters {
  /// 鼻梁高度（0.0-1.0，0.5为默认）
  final double bridgeHeight;
  
  /// 鼻梁宽度（0.0-1.0，0.5为默认）
  final double bridgeWidth;
  
  /// 鼻尖高度（0.0-1.0，0.5为默认）
  final double tipHeight;
  
  /// 鼻尖宽度（0.0-1.0，0.5为默认）
  final double tipWidth;
  
  /// 鼻翼宽度（0.0-1.0，0.5为默认）
  final double nostrilWidth;
  
  /// 高级鼻部变形参数
  final Map<String, double>? advancedParameters;
  
  /// 构造函数
  const NoseParameters({
    this.bridgeHeight = 0.5,
    this.bridgeWidth = 0.5,
    this.tipHeight = 0.5,
    this.tipWidth = 0.5,
    this.nostrilWidth = 0.5,
    this.advancedParameters,
  });
  
  /// 从JSON创建参数
  factory NoseParameters.fromJson(Map<String, dynamic> json) {
    // 处理高级参数
    Map<String, double>? advancedParams;
    if (json.containsKey('advancedParameters')) {
      advancedParams = <String, double>{};
      final advancedJson = json['advancedParameters'] as Map<String, dynamic>;
      advancedJson.forEach((key, value) {
        if (value is num) {
          advancedParams![key] = value.toDouble();
        }
      });
    }
    
    return NoseParameters(
      bridgeHeight: json['bridgeHeight']?.toDouble() ?? 0.5,
      bridgeWidth: json['bridgeWidth']?.toDouble() ?? 0.5,
      tipHeight: json['tipHeight']?.toDouble() ?? 0.5,
      tipWidth: json['tipWidth']?.toDouble() ?? 0.5,
      nostrilWidth: json['nostrilWidth']?.toDouble() ?? 0.5,
      advancedParameters: advancedParams,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{
      'bridgeHeight': bridgeHeight,
      'bridgeWidth': bridgeWidth,
      'tipHeight': tipHeight,
      'tipWidth': tipWidth,
      'nostrilWidth': nostrilWidth,
    };
    
    if (advancedParameters != null) {
      result['advancedParameters'] = advancedParameters;
    }
    
    return result;
  }
  
  /// 创建参数副本
  NoseParameters copyWith({
    double? bridgeHeight,
    double? bridgeWidth,
    double? tipHeight,
    double? tipWidth,
    double? nostrilWidth,
    Map<String, double>? advancedParameters,
  }) {
    return NoseParameters(
      bridgeHeight: bridgeHeight ?? this.bridgeHeight,
      bridgeWidth: bridgeWidth ?? this.bridgeWidth,
      tipHeight: tipHeight ?? this.tipHeight,
      tipWidth: tipWidth ?? this.tipWidth,
      nostrilWidth: nostrilWidth ?? this.nostrilWidth,
      advancedParameters: advancedParameters ?? this.advancedParameters,
    );
  }
  
  /// 从高级变形参数创建
  factory NoseParameters.fromTransformationParameters(Map<String, double> transformParams) {
    // 提取基本参数
    final bridgeHeight = transformParams['bridgeHeight'] ?? 0.5;
    final bridgeWidth = transformParams['bridgeWidth'] ?? 0.5;
    final tipHeight = transformParams['tipHeight'] ?? 0.5;
    final tipWidth = transformParams['tipWidth'] ?? 0.5;
    final nostrilWidth = transformParams['nostrilWidth'] ?? 0.5;
    
    // 创建高级参数映射
    final advancedParams = <String, double>{};
    transformParams.forEach((key, value) {
      if (!['bridgeHeight', 'bridgeWidth', 'tipHeight', 'tipWidth', 'nostrilWidth'].contains(key)) {
        advancedParams[key] = value;
      }
    });
    
    return NoseParameters(
      bridgeHeight: bridgeHeight,
      bridgeWidth: bridgeWidth,
      tipHeight: tipHeight,
      tipWidth: tipWidth,
      nostrilWidth: nostrilWidth,
      advancedParameters: advancedParams.isNotEmpty ? advancedParams : null,
    );
  }
  
  @override
  String toString() {
    return 'NoseParameters(bridgeHeight: $bridgeHeight, bridgeWidth: $bridgeWidth, tipHeight: $tipHeight, tipWidth: $tipWidth, nostrilWidth: $nostrilWidth, advancedParameters: $advancedParameters)';
  }
}

/// 眼部参数类
class EyeParameters {
  /// 眼睛大小（0.0-1.0，0.5为默认）
  final double eyeSize;
  
  /// 眼距（0.0-1.0，0.5为默认）
  final double eyeDistance;
  
  /// 眼角（0.0-1.0，0.5为默认）
  final double eyeCorner;
  
  /// 双眼皮（0.0-1.0，0.5为默认）
  final double doubleLid;
  
  /// 眼睑（0.0-1.0，0.5为默认）
  final double eyelid;
  
  /// 构造函数
  const EyeParameters({
    this.eyeSize = 0.5,
    this.eyeDistance = 0.5,
    this.eyeCorner = 0.5,
    this.doubleLid = 0.5,
    this.eyelid = 0.5,
  });
  
  /// 从JSON创建参数
  factory EyeParameters.fromJson(Map<String, dynamic> json) {
    return EyeParameters(
      eyeSize: json['eyeSize']?.toDouble() ?? 0.5,
      eyeDistance: json['eyeDistance']?.toDouble() ?? 0.5,
      eyeCorner: json['eyeCorner']?.toDouble() ?? 0.5,
      doubleLid: json['doubleLid']?.toDouble() ?? 0.5,
      eyelid: json['eyelid']?.toDouble() ?? 0.5,
    );
  }
  
  /// 转换为JSON
  Map<String, double> toJson() {
    return {
      'eyeSize': eyeSize,
      'eyeDistance': eyeDistance,
      'eyeCorner': eyeCorner,
      'doubleLid': doubleLid,
      'eyelid': eyelid,
    };
  }
  
  /// 创建参数副本
  EyeParameters copyWith({
    double? eyeSize,
    double? eyeDistance,
    double? eyeCorner,
    double? doubleLid,
    double? eyelid,
  }) {
    return EyeParameters(
      eyeSize: eyeSize ?? this.eyeSize,
      eyeDistance: eyeDistance ?? this.eyeDistance,
      eyeCorner: eyeCorner ?? this.eyeCorner,
      doubleLid: doubleLid ?? this.doubleLid,
      eyelid: eyelid ?? this.eyelid,
    );
  }
  
  @override
  String toString() {
    return 'EyeParameters(eyeSize: $eyeSize, eyeDistance: $eyeDistance, eyeCorner: $eyeCorner, doubleLid: $doubleLid, eyelid: $eyelid)';
  }
}

/// 唇部参数类
class LipParameters {
  /// 唇形（0.0-1.0，0.5为默认）
  final double lipShape;
  
  /// 唇厚度（0.0-1.0，0.5为默认）
  final double lipThickness;
  
  /// 唇宽度（0.0-1.0，0.5为默认）
  final double lipWidth;
  
  /// 唇峰（0.0-1.0，0.5为默认）
  final double lipPeak;
  
  /// 唇角（0.0-1.0，0.5为默认）
  final double lipCorner;
  
  /// 构造函数
  const LipParameters({
    this.lipShape = 0.5,
    this.lipThickness = 0.5,
    this.lipWidth = 0.5,
    this.lipPeak = 0.5,
    this.lipCorner = 0.5,
  });
  
  /// 从JSON创建参数
  factory LipParameters.fromJson(Map<String, dynamic> json) {
    return LipParameters(
      lipShape: json['lipShape']?.toDouble() ?? 0.5,
      lipThickness: json['lipThickness']?.toDouble() ?? 0.5,
      lipWidth: json['lipWidth']?.toDouble() ?? 0.5,
      lipPeak: json['lipPeak']?.toDouble() ?? 0.5,
      lipCorner: json['lipCorner']?.toDouble() ?? 0.5,
    );
  }
  
  /// 转换为JSON
  Map<String, double> toJson() {
    return {
      'lipShape': lipShape,
      'lipThickness': lipThickness,
      'lipWidth': lipWidth,
      'lipPeak': lipPeak,
      'lipCorner': lipCorner,
    };
  }
  
  /// 创建参数副本
  LipParameters copyWith({
    double? lipShape,
    double? lipThickness,
    double? lipWidth,
    double? lipPeak,
    double? lipCorner,
  }) {
    return LipParameters(
      lipShape: lipShape ?? this.lipShape,
      lipThickness: lipThickness ?? this.lipThickness,
      lipWidth: lipWidth ?? this.lipWidth,
      lipPeak: lipPeak ?? this.lipPeak,
      lipCorner: lipCorner ?? this.lipCorner,
    );
  }
  
  @override
  String toString() {
    return 'LipParameters(lipShape: $lipShape, lipThickness: $lipThickness, lipWidth: $lipWidth, lipPeak: $lipPeak, lipCorner: $lipCorner)';
  }
}

/// 抗衰参数类
class AntiAgingParameters {
  /// 皱纹（0.0-1.0，0.5为默认）
  final double wrinkle;
  
  /// 肤质（0.0-1.0，0.5为默认）
  final double skinTexture;
  
  /// 肤色（0.0-1.0，0.5为默认）
  final double skinTone;
  
  /// 黑眼圈（0.0-1.0，0.5为默认）
  final double darkCircle;
  
  /// 眼袋（0.0-1.0，0.5为默认）
  final double eyeBag;
  
  /// 构造函数
  const AntiAgingParameters({
    this.wrinkle = 0.5,
    this.skinTexture = 0.5,
    this.skinTone = 0.5,
    this.darkCircle = 0.5,
    this.eyeBag = 0.5,
  });
  
  /// 从JSON创建参数
  factory AntiAgingParameters.fromJson(Map<String, dynamic> json) {
    return AntiAgingParameters(
      wrinkle: json['wrinkle']?.toDouble() ?? 0.5,
      skinTexture: json['skinTexture']?.toDouble() ?? 0.5,
      skinTone: json['skinTone']?.toDouble() ?? 0.5,
      darkCircle: json['darkCircle']?.toDouble() ?? 0.5,
      eyeBag: json['eyeBag']?.toDouble() ?? 0.5,
    );
  }
  
  /// 转换为JSON
  Map<String, double> toJson() {
    return {
      'wrinkle': wrinkle,
      'skinTexture': skinTexture,
      'skinTone': skinTone,
      'darkCircle': darkCircle,
      'eyeBag': eyeBag,
    };
  }
  
  /// 创建参数副本
  AntiAgingParameters copyWith({
    double? wrinkle,
    double? skinTexture,
    double? skinTone,
    double? darkCircle,
    double? eyeBag,
  }) {
    return AntiAgingParameters(
      wrinkle: wrinkle ?? this.wrinkle,
      skinTexture: skinTexture ?? this.skinTexture,
      skinTone: skinTone ?? this.skinTone,
      darkCircle: darkCircle ?? this.darkCircle,
      eyeBag: eyeBag ?? this.eyeBag,
    );
  }
  
  @override
  String toString() {
    return 'AntiAgingParameters(wrinkle: $wrinkle, skinTexture: $skinTexture, skinTone: $skinTone, darkCircle: $darkCircle, eyeBag: $eyeBag)';
  }
}
