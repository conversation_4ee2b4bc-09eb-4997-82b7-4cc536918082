import 'package:flutter/material.dart';

/// 面部特征点模型
class FeaturePoint {
  /// 特征点索引
  final int index;
  
  /// 特征点名称
  final String? name;
  
  /// 特征点坐标 X
  final double x;
  
  /// 特征点坐标 Y
  final double y;
  
  /// 特征点是否为主要特征点
  final bool isPrimary;
  
  /// 特征点当前的透明度
  final double opacity;
  
  /// 特征点的大小
  final double size;
  
  /// 特征点的颜色
  final Color color;

  /// Z轴坐标（深度）
  final double z;

  /// 可见度
  final double visibility;

  /// 置信度
  final double confidence;

  const FeaturePoint({
    required this.index,
    this.name,
    required this.x,
    required this.y,
    this.z = 0.0,
    this.visibility = 1.0,
    this.confidence = 1.0,
    this.isPrimary = false,
    this.opacity = 1.0,
    this.size = 4.0,
    this.color = Colors.blue,
  });

  /// 从 JSON 创建特征点
  factory FeaturePoint.fromJson(Map<String, dynamic> json) {
    return FeaturePoint(
      index: json['index'] as int,
      name: json['name'] as String?,
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      z: (json['z'] as num?)?.toDouble() ?? 0.0,
      visibility: (json['visibility'] as num?)?.toDouble() ?? 1.0,
      confidence: (json['quality'] as num?)?.toDouble() ?? 1.0,
      isPrimary: json['is_primary'] as bool? ?? false,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1.0,
      size: (json['size'] as num?)?.toDouble() ?? 4.0,
      color: json['color'] != null ? Color(json['color'] as int) : Colors.blue,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'name': name,
      'x': x,
      'y': y,
      'z': z,
      'visibility': visibility,
      'quality': confidence,
      'is_primary': isPrimary,
      'opacity': opacity,
      'size': size,
      'color': color.value,
    };
  }

  /// 创建一个新的特征点实例，但更新某些属性
  FeaturePoint copyWith({
    int? index,
    String? name,
    double? x,
    double? y,
    double? z,
    double? visibility,
    double? confidence,
    bool? isPrimary,
    double? opacity,
    double? size,
    Color? color,
  }) {
    return FeaturePoint(
      index: index ?? this.index,
      name: name ?? this.name,
      x: x ?? this.x,
      y: y ?? this.y,
      z: z ?? this.z,
      visibility: visibility ?? this.visibility,
      confidence: confidence ?? this.confidence,
      isPrimary: isPrimary ?? this.isPrimary,
      opacity: opacity ?? this.opacity,
      size: size ?? this.size,
      color: color ?? this.color,
    );
  }

  @override
  String toString() {
    return 'FeaturePoint{index: $index, name: $name, position: ($x, $y, $z)}';
  }
}
