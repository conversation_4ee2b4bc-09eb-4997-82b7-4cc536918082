import 'package:flutter/foundation.dart';
import 'dart:math' show Point;
import '../models/facial_feature_mapping.dart';

/// 鼻部特征区域映射
class NoseFeatureMapping {
  // 鼻部主要特征点索引
  static const Map<String, int> primaryLandmarks = {
    'noseTip': 4,      // 鼻尖
    'leftWing': 49,    // 左鼻翼
    'rightWing': 279,  // 右鼻翼
    'bridge': 197,     // 鼻梁顶点
  };

  // 鼻部次要特征点索引
  static const Map<String, List<int>> secondaryLandmarks = {
    'leftContour': [98, 97, 2],      // 左侧轮廓
    'rightContour': [326, 327],      // 右侧轮廓
    'bridgeContour': [168, 193, 122, 351, 417], // 鼻梁轮廓
    'leftNostril': [330, 331, 332],  // 左鼻孔
    'rightNostril': [359, 360, 361], // 右鼻孔
  };

  // 对称点对
  static const List<List<int>> symmetryPairs = [
    [49, 279],   // 鼻翼对称点
    [98, 327],   // 轮廓对称点
    [97, 326],   // 轮廓对称点
    [330, 359],  // 鼻孔对称点
    [331, 360],  // 鼻孔对称点
    [332, 361],  // 鼻孔对称点
  ];

  // 容差配置
  static const double baseToleranceDistance = 2.0;  // 基础容差（毫米）
  static const double maxToleranceFactor = 0.08;    // 最大容差因子
  static const double zAxisTolerance = 0.05;        // Z轴容差

  /// 检查特征点是否在容差范围内
  static bool isWithinTolerance(Point<double> p1, Point<double> p2, double z1, double z2) {
    // 计算平面距离
    double xyDistance = sqrt(pow(p2.x - p1.x, 2) + pow(p2.y - p1.y, 2));
    double zDistance = (z2 - z1).abs();

    // 计算动态容差
    double dynamicFactor = min(xyDistance * 0.05, maxToleranceFactor);
    double xyTolerance = baseToleranceDistance * (1 + dynamicFactor);

    // 同时满足平面和Z轴的容差要求
    return xyDistance <= xyTolerance && zDistance <= zAxisTolerance;
  }

  /// 获取特征点在鼻部区域中的角色
  static PointRole getPointRole(int pointIndex) {
    if (primaryLandmarks.containsValue(pointIndex)) {
      return PointRole.primary;
    }

    for (var points in secondaryLandmarks.values) {
      if (points.contains(pointIndex)) {
        return PointRole.secondary;
      }
    }

    return PointRole.auxiliary;
  }

  /// 检查两个特征点是否为对称点对
  static bool isSymmetryPair(int point1, int point2) {
    return symmetryPairs.any((pair) => 
      (pair[0] == point1 && pair[1] == point2) ||
      (pair[0] == point2 && pair[1] == point1)
    );
  }

  /// 获取特定区域的所有特征点
  static List<int> getRegionPoints(String region) {
    if (region == 'primary') {
      return primaryLandmarks.values.toList();
    }

    if (secondaryLandmarks.containsKey(region)) {
      return secondaryLandmarks[region] ?? [];
    }

    return [];
  }

  /// 检查特征点是否属于鼻部区域
  static bool isNosePoint(int pointIndex) {
    if (primaryLandmarks.containsValue(pointIndex)) {
      return true;
    }

    return secondaryLandmarks.values.any((points) => points.contains(pointIndex));
  }

  /// 获取特征点的解剖学约束
  static Map<String, double> getAnatomicalConstraints(int pointIndex) {
    // 基于解剖学原理的约束条件
    return {
      'minWidth': 0.2,    // 最小宽度比例
      'maxWidth': 0.4,    // 最大宽度比例
      'minHeight': 0.1,   // 最小高度比例
      'maxHeight': 0.3,   // 最大高度比例
      'minDepth': -0.1,   // 最小深度
      'maxDepth': 0.1,    // 最大深度
    };
  }

  /// 验证特征点位置是否符合解剖学规则
  static bool validateAnatomicalPosition(Point<double> point, double z, int pointIndex) {
    var constraints = getAnatomicalConstraints(pointIndex);
    
    // 检查x坐标是否在合理范围内
    bool validX = point.x >= constraints['minWidth']! && 
                 point.x <= constraints['maxWidth']!;
    
    // 检查y坐标是否在合理范围内              
    bool validY = point.y >= constraints['minHeight']! && 
                 point.y <= constraints['maxHeight']!;
    
    // 检查z坐标是否在合理范围内
    bool validZ = z >= constraints['minDepth']! && 
                 z <= constraints['maxDepth']!;
                 
    return validX && validY && validZ;
  }
}
