import 'dart:ui';

/// 特征点变形模型
/// 
/// 存储特征点的变形信息
class FeaturePointTransformation {
  /// 特征点索引
  final int index;
  
  /// 原始位置
  Offset? originalPosition;
  
  /// 变形后位置
  Offset? transformedPosition;
  
  /// 是否可见
  bool visible;

  /// 是否高亮显示
  bool highlight;

  /// 是否启用呼吸灯效果
  bool breathing;
  
  /// 高亮颜色
  final Color highlightColor;

  /// 呼吸灯效果的不透明度 (0.0 - 1.0)
  double breathingOpacity;

  /// 呼吸灯效果的大小缩放 (1.0 - 1.5)
  double breathingScale;
  
  /// 变形强度 (0.0 - 1.0)
  double transformationStrength;
  
  /// 描述信息
  final String? description;
  
  FeaturePointTransformation({
    required this.index,
    this.originalPosition,
    this.transformedPosition,
    this.visible = false,
    this.highlight = false,
    this.breathing = false,
    this.highlightColor = const Color(0xFFFFD700), // 默认金色高亮
    this.breathingOpacity = 1.0,
    this.breathingScale = 1.0,
    this.transformationStrength = 0.0,
    this.description,
  });
  
  /// 创建副本
  FeaturePointTransformation copyWith({
    int? index,
    Offset? originalPosition,
    Offset? transformedPosition,
    bool? visible,
    bool? highlight,
    bool? breathing,
    Color? highlightColor,
    double? breathingOpacity,
    double? breathingScale,
    double? transformationStrength,
    String? description,
  }) {
    return FeaturePointTransformation(
      index: index ?? this.index,
      originalPosition: originalPosition ?? this.originalPosition,
      transformedPosition: transformedPosition ?? this.transformedPosition,
      visible: visible ?? this.visible,
      highlight: highlight ?? this.highlight,
      breathing: breathing ?? this.breathing,
      highlightColor: highlightColor ?? this.highlightColor,
      breathingOpacity: breathingOpacity ?? this.breathingOpacity,
      breathingScale: breathingScale ?? this.breathingScale,
      transformationStrength: transformationStrength ?? this.transformationStrength,
      description: description ?? this.description,
    );
  }
}
