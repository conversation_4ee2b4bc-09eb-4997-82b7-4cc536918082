import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../models/feature_point_model.dart';

/// 标签渲染器
/// 
/// 负责渲染特征点的标签及其相关的动画效果
/// 此文件是模块化鼻部特征点可视化系统的渲染层

/// 标签位置枚举
enum LabelPosition {
  /// 上方 - 标签显示在特征点上方
  top,
  
  /// 下方 - 标签显示在特征点下方
  bottom,
  
  /// 左侧 - 标签显示在特征点左侧
  left,
  
  /// 右侧 - 标签显示在特征点右侧
  right,
  
  /// 自动 - 根据特征点位置自动选择最佳显示位置
  auto,
}

/// 标签样式枚举
enum LabelStyle {
  /// 简单样式 - 基础的文本标签
  simple,
  
  /// 带背景样式 - 带有背景的标签
  background,
  
  /// 科技风格 - 具有科技感的标签
  tech,
  
  /// 医疗风格 - 适合医疗场景的标签
  medical,
}

/// 标签渲染器类
class LabelRenderer {
  /// 标签位置
  final LabelPosition position;
  
  /// 标签样式
  final LabelStyle style;
  
  /// 动画控制器值
  final double animationValue;
  
  /// 是否启用动画效果
  final bool enableEffects;
  
  /// 标签字体大小
  final double fontSize;
  
  /// 构造函数
  LabelRenderer({
    this.position = LabelPosition.auto,
    this.style = LabelStyle.tech,
    required this.animationValue,
    this.enableEffects = true,
    this.fontSize = 12.0,
  });
  
  /// 渲染标签
  void render(Canvas canvas, FeaturePoint point, String label) {
    // 如果标签为空，则不渲染
    if (label.isEmpty) {
      return;
    }
    
    // 计算标签位置
    final labelPosition = _calculateLabelPosition(point);
    
    // 根据标签样式选择不同的渲染方法
    switch (style) {
      case LabelStyle.simple:
        _renderSimpleLabel(canvas, point, label, labelPosition);
        break;
      case LabelStyle.background:
        _renderBackgroundLabel(canvas, point, label, labelPosition);
        break;
      case LabelStyle.tech:
        _renderTechLabel(canvas, point, label, labelPosition);
        break;
      case LabelStyle.medical:
        _renderMedicalLabel(canvas, point, label, labelPosition);
        break;
    }
  }
  
  /// 计算标签位置
  Offset _calculateLabelPosition(FeaturePoint point) {
    // 根据指定的位置计算标签偏移
    switch (position) {
      case LabelPosition.top:
        return Offset(point.position.dx, point.position.dy - point.animatedRadius * 3.0);
      case LabelPosition.bottom:
        return Offset(point.position.dx, point.position.dy + point.animatedRadius * 3.0);
      case LabelPosition.left:
        return Offset(point.position.dx - point.animatedRadius * 3.0, point.position.dy);
      case LabelPosition.right:
        return Offset(point.position.dx + point.animatedRadius * 3.0, point.position.dy);
      case LabelPosition.auto:
        // 自动计算最佳位置（简单实现，可以根据需要优化）
        return Offset(point.position.dx, point.position.dy - point.animatedRadius * 3.0);
    }
  }
  
  /// 渲染简单样式的标签
  void _renderSimpleLabel(Canvas canvas, FeaturePoint point, String label, Offset labelPosition) {
    // 创建文本绘制器
    final textSpan = TextSpan(
      text: label,
      style: TextStyle(
        color: point.color,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
      ),
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    // 布局文本
    textPainter.layout();
    
    // 计算文本位置（居中对齐）
    final textOffset = Offset(
      labelPosition.dx - textPainter.width / 2,
      labelPosition.dy - textPainter.height / 2,
    );
    
    // 绘制文本
    textPainter.paint(canvas, textOffset);
  }
  
  /// 渲染带背景样式的标签
  void _renderBackgroundLabel(Canvas canvas, FeaturePoint point, String label, Offset labelPosition) {
    // 创建文本绘制器
    final textSpan = TextSpan(
      text: label,
      style: TextStyle(
        color: Colors.white,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
      ),
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    // 布局文本
    textPainter.layout();
    
    // 计算文本位置（居中对齐）
    final textOffset = Offset(
      labelPosition.dx - textPainter.width / 2,
      labelPosition.dy - textPainter.height / 2,
    );
    
    // 计算背景矩形
    final backgroundRect = Rect.fromLTWH(
      textOffset.dx - 8.0,
      textOffset.dy - 4.0,
      textPainter.width + 16.0,
      textPainter.height + 8.0,
    );
    
    // 创建背景画笔
    final backgroundPaint = Paint()
      ..color = point.color
      ..style = PaintingStyle.fill;
    
    // 绘制圆角背景
    canvas.drawRRect(
      RRect.fromRectAndRadius(backgroundRect, Radius.circular(8.0)),
      backgroundPaint,
    );
    
    // 绘制文本
    textPainter.paint(canvas, textOffset);
    
    // 绘制连接线
    final linePaint = Paint()
      ..color = point.color
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;
    
    // 计算连接线起点和终点
    final lineStart = point.position;
    final lineEnd = Offset(
      labelPosition.dx,
      position == LabelPosition.top || position == LabelPosition.bottom
          ? backgroundRect.closest(point.position).dy
          : backgroundRect.closest(point.position).dx,
    );
    
    // 绘制连接线
    canvas.drawLine(lineStart, lineEnd, linePaint);
  }
  
  /// 渲染科技风格的标签
  void _renderTechLabel(Canvas canvas, FeaturePoint point, String label, Offset labelPosition) {
    // 创建文本绘制器
    final textSpan = TextSpan(
      text: label,
      style: TextStyle(
        color: Colors.white,
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
      ),
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    // 布局文本
    textPainter.layout();
    
    // 计算文本位置（居中对齐）
    final textOffset = Offset(
      labelPosition.dx - textPainter.width / 2,
      labelPosition.dy - textPainter.height / 2,
    );
    
    // 计算背景矩形
    final backgroundRect = Rect.fromLTWH(
      textOffset.dx - 10.0,
      textOffset.dy - 6.0,
      textPainter.width + 20.0,
      textPainter.height + 12.0,
    );
    
    // 创建背景渐变
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        point.color.withOpacity(0.8),
        point.color.withOpacity(0.4),
      ],
    );
    
    // 创建背景画笔
    final backgroundPaint = Paint()
      ..shader = gradient.createShader(backgroundRect)
      ..style = PaintingStyle.fill;
    
    // 绘制背景
    canvas.drawRRect(
      RRect.fromRectAndRadius(backgroundRect, Radius.circular(4.0)),
      backgroundPaint,
    );
    
    // 绘制边框
    final borderPaint = Paint()
      ..color = Colors.white.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(backgroundRect, Radius.circular(4.0)),
      borderPaint,
    );
    
    // 绘制装饰角
    final cornerSize = 6.0;
    
    // 左上角
    canvas.drawLine(
      Offset(backgroundRect.left, backgroundRect.top + cornerSize),
      Offset(backgroundRect.left, backgroundRect.top),
      borderPaint,
    );
    
    canvas.drawLine(
      Offset(backgroundRect.left, backgroundRect.top),
      Offset(backgroundRect.left + cornerSize, backgroundRect.top),
      borderPaint,
    );
    
    // 右上角
    canvas.drawLine(
      Offset(backgroundRect.right - cornerSize, backgroundRect.top),
      Offset(backgroundRect.right, backgroundRect.top),
      borderPaint,
    );
    
    canvas.drawLine(
      Offset(backgroundRect.right, backgroundRect.top),
      Offset(backgroundRect.right, backgroundRect.top + cornerSize),
      borderPaint,
    );
    
    // 左下角
    canvas.drawLine(
      Offset(backgroundRect.left, backgroundRect.bottom - cornerSize),
      Offset(backgroundRect.left, backgroundRect.bottom),
      borderPaint,
    );
    
    canvas.drawLine(
      Offset(backgroundRect.left, backgroundRect.bottom),
      Offset(backgroundRect.left + cornerSize, backgroundRect.bottom),
      borderPaint,
    );
    
    // 右下角
    canvas.drawLine(
      Offset(backgroundRect.right - cornerSize, backgroundRect.bottom),
      Offset(backgroundRect.right, backgroundRect.bottom),
      borderPaint,
    );
    
    canvas.drawLine(
      Offset(backgroundRect.right, backgroundRect.bottom),
      Offset(backgroundRect.right, backgroundRect.bottom - cornerSize),
      borderPaint,
    );
    
    // 绘制文本
    textPainter.paint(canvas, textOffset);
    
    // 绘制连接线
    final linePaint = Paint()
      ..color = Colors.white.withOpacity(0.7)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;
    
    // 计算连接线起点和终点
    final lineStart = point.position;
    final lineEnd = backgroundRect.closest(point.position);
    
    // 绘制连接线
    canvas.drawLine(lineStart, lineEnd, linePaint);
    
    // 如果启用了动画效果，绘制动画效果
    if (enableEffects) {
      // 计算动画点位置
      final animPhase = (animationValue * 2) % 1.0;
      final animDistance = math.sqrt(
        math.pow(lineEnd.dx - lineStart.dx, 2) +
        math.pow(lineEnd.dy - lineStart.dy, 2),
      );
      
      final animX = lineStart.dx + (lineEnd.dx - lineStart.dx) * animPhase;
      final animY = lineStart.dy + (lineEnd.dy - lineStart.dy) * animPhase;
      
      // 绘制动画点
      final animPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(animX, animY),
        1.5,
        animPaint,
      );
    }
  }
  
  /// 渲染医疗风格的标签
  void _renderMedicalLabel(Canvas canvas, FeaturePoint point, String label, Offset labelPosition) {
    // 创建文本绘制器
    final textSpan = TextSpan(
      text: label,
      style: TextStyle(
        color: Colors.black87,
        fontSize: fontSize,
        fontWeight: FontWeight.w500,
      ),
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    // 布局文本
    textPainter.layout();
    
    // 计算文本位置（居中对齐）
    final textOffset = Offset(
      labelPosition.dx - textPainter.width / 2,
      labelPosition.dy - textPainter.height / 2,
    );
    
    // 计算背景矩形
    final backgroundRect = Rect.fromLTWH(
      textOffset.dx - 8.0,
      textOffset.dy - 4.0,
      textPainter.width + 16.0,
      textPainter.height + 8.0,
    );
    
    // 创建背景画笔
    final backgroundPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    // 绘制背景
    canvas.drawRRect(
      RRect.fromRectAndRadius(backgroundRect, Radius.circular(4.0)),
      backgroundPaint,
    );
    
    // 绘制边框
    final borderPaint = Paint()
      ..color = point.color.withOpacity(0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(backgroundRect, Radius.circular(4.0)),
      borderPaint,
    );
    
    // 绘制文本
    textPainter.paint(canvas, textOffset);
    
    // 绘制连接线
    final linePaint = Paint()
      ..color = point.color.withOpacity(0.7)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;
    
    // 计算连接线起点和终点
    final lineStart = point.position;
    final lineEnd = backgroundRect.closest(point.position);
    
    // 绘制虚线连接线
    final dashPath = Path();
    final dashLength = 3.0;
    final gapLength = 2.0;
    
    final dx = lineEnd.dx - lineStart.dx;
    final dy = lineEnd.dy - lineStart.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    
    final unitX = dx / distance;
    final unitY = dy / distance;
    
    double currentDistance = 0;
    
    while (currentDistance < distance) {
      final startX = lineStart.dx + unitX * currentDistance;
      final startY = lineStart.dy + unitY * currentDistance;
      
      final endDistance = math.min(currentDistance + dashLength, distance);
      final endX = lineStart.dx + unitX * endDistance;
      final endY = lineStart.dy + unitY * endDistance;
      
      dashPath.moveTo(startX, startY);
      dashPath.lineTo(endX, endY);
      
      currentDistance += dashLength + gapLength;
    }
    
    canvas.drawPath(dashPath, linePaint);
    
    // 绘制测量标记
    if (point.state == FeaturePointState.highlighted || 
        point.state == FeaturePointState.selected) {
      // 绘制测量值
      final measureText = '${distance.toStringAsFixed(1)}mm';
      final measureSpan = TextSpan(
        text: measureText,
        style: TextStyle(
          color: point.color,
          fontSize: fontSize * 0.8,
          fontWeight: FontWeight.w500,
        ),
      );
      
      final measurePainter = TextPainter(
        text: measureSpan,
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );
      
      // 布局测量文本
      measurePainter.layout();
      
      // 计算测量文本位置
      final measureOffset = Offset(
        (lineStart.dx + lineEnd.dx) / 2 - measurePainter.width / 2,
        (lineStart.dy + lineEnd.dy) / 2 - measurePainter.height - 2.0,
      );
      
      // 绘制测量文本背景
      final measureBackgroundRect = Rect.fromLTWH(
        measureOffset.dx - 2.0,
        measureOffset.dy - 2.0,
        measurePainter.width + 4.0,
        measurePainter.height + 4.0,
      );
      
      canvas.drawRRect(
        RRect.fromRectAndRadius(measureBackgroundRect, Radius.circular(2.0)),
        Paint()..color = Colors.white,
      );
      
      // 绘制测量文本
      measurePainter.paint(canvas, measureOffset);
    }
  }
  
  /// 渲染多个特征点的标签
  void renderLabels(Canvas canvas, List<FeaturePoint> points, Map<int, String> labels) {
    // 先渲染普通点的标签，再渲染高亮点的标签，确保高亮点的标签在最上层
    
    // 渲染普通点的标签
    for (final point in points) {
      if (point.state != FeaturePointState.highlighted && 
          point.state != FeaturePointState.selected) {
        final label = labels[point.id] ?? '';
        if (label.isNotEmpty) {
          render(canvas, point, label);
        }
      }
    }
    
    // 渲染高亮点的标签
    for (final point in points) {
      if (point.state == FeaturePointState.highlighted || 
          point.state == FeaturePointState.selected) {
        final label = labels[point.id] ?? '';
        if (label.isNotEmpty) {
          render(canvas, point, label);
        }
      }
    }
  }
}
