import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../models/connecting_line_model.dart';
import '../models/feature_point_model.dart';
import '../animations/effects/glow_effect.dart';

/// 连接线渲染器
/// 
/// 负责渲染特征点之间的连接线及其相关的动画效果
/// 此文件是模块化鼻部特征点可视化系统的渲染层

/// 连接线渲染样式枚举
enum LineRenderStyle {
  /// 简单样式 - 基础的直线渲染
  simple,
  
  /// 渐变样式 - 带有颜色渐变的渲染
  gradient,
  
  /// 虚线样式 - 虚线渲染
  dashed,
  
  /// 科技风格 - 具有科技感的渲染
  tech,
}

/// 连接线渲染器类
class LineRenderer {
  /// 渲染样式
  final LineRenderStyle style;
  
  /// 动画控制器值
  final double animationValue;
  
  /// 是否启用动画效果
  final bool enableEffects;
  
  /// 构造函数
  LineRenderer({
    this.style = LineRenderStyle.gradient,
    required this.animationValue,
    this.enableEffects = true,
  });
  
  /// 渲染连接线
  void render(Canvas canvas, ConnectingLine line, Map<int, FeaturePoint> pointsMap) {
    // 获取起点和终点
    final startPoint = pointsMap[line.startPointId];
    final endPoint = pointsMap[line.endPointId];
    
    // 如果起点或终点不存在，则不渲染
    if (startPoint == null || endPoint == null) {
      return;
    }
    
    // 根据渲染样式选择不同的渲染方法
    switch (style) {
      case LineRenderStyle.simple:
        _renderSimpleLine(canvas, line, startPoint, endPoint);
        break;
      case LineRenderStyle.gradient:
        _renderGradientLine(canvas, line, startPoint, endPoint);
        break;
      case LineRenderStyle.dashed:
        _renderDashedLine(canvas, line, startPoint, endPoint);
        break;
      case LineRenderStyle.tech:
        _renderTechLine(canvas, line, startPoint, endPoint);
        break;
    }
    
    // 如果启用了动画效果，则渲染动画效果
    if (enableEffects) {
      _renderEffects(canvas, line, startPoint, endPoint);
    }
  }
  
  /// 渲染简单样式的连接线
  void _renderSimpleLine(Canvas canvas, ConnectingLine line, FeaturePoint startPoint, FeaturePoint endPoint) {
    // 创建连接线画笔
    final linePaint = Paint()
      ..color = line.color
      ..strokeWidth = line.width
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    // 绘制连接线
    canvas.drawLine(startPoint.position, endPoint.position, linePaint);
  }
  
  /// 渲染渐变样式的连接线
  void _renderGradientLine(Canvas canvas, ConnectingLine line, FeaturePoint startPoint, FeaturePoint endPoint) {
    // 创建渐变
    final gradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        startPoint.color,
        endPoint.color,
      ],
    );
    
    // 创建连接线画笔
    final linePaint = Paint()
      ..shader = gradient.createShader(
        Rect.fromPoints(startPoint.position, endPoint.position),
      )
      ..strokeWidth = line.width
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    // 绘制连接线
    canvas.drawLine(startPoint.position, endPoint.position, linePaint);
  }
  
  /// 渲染虚线样式的连接线
  void _renderDashedLine(Canvas canvas, ConnectingLine line, FeaturePoint startPoint, FeaturePoint endPoint) {
    // 计算虚线偏移
    final dashOffset = (animationValue * 10) % 10.0;
    
    // 创建虚线画笔
    final dashPaint = Paint()
      ..color = line.color
      ..strokeWidth = line.width
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    // 计算连接线长度和方向
    final dx = endPoint.position.dx - startPoint.position.dx;
    final dy = endPoint.position.dy - startPoint.position.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    
    // 计算单位向量
    final unitX = dx / distance;
    final unitY = dy / distance;
    
    // 绘制虚线
    final dashPath = Path();
    final dashLength = 5.0;
    final gapLength = 3.0;
    
    double currentDistance = -dashOffset;
    
    while (currentDistance < distance) {
      // 计算虚线段起点
      final startX = startPoint.position.dx + unitX * currentDistance;
      final startY = startPoint.position.dy + unitY * currentDistance;
      
      // 计算虚线段终点（不超过连接线终点）
      final endDistance = math.min(currentDistance + dashLength, distance);
      final endX = startPoint.position.dx + unitX * endDistance;
      final endY = startPoint.position.dy + unitY * endDistance;
      
      // 添加虚线段
      dashPath.moveTo(startX, startY);
      dashPath.lineTo(endX, endY);
      
      // 更新当前距离
      currentDistance += dashLength + gapLength;
    }
    
    // 绘制虚线
    canvas.drawPath(dashPath, dashPaint);
  }
  
  /// 渲染科技风格的连接线
  void _renderTechLine(Canvas canvas, ConnectingLine line, FeaturePoint startPoint, FeaturePoint endPoint) {
    // 保存画布状态
    canvas.save();
    
    // 计算连接线中点
    final midPoint = Offset(
      (startPoint.position.dx + endPoint.position.dx) / 2,
      (startPoint.position.dy + endPoint.position.dy) / 2,
    );
    
    // 计算连接线长度和方向
    final dx = endPoint.position.dx - startPoint.position.dx;
    final dy = endPoint.position.dy - startPoint.position.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    final angle = math.atan2(dy, dx);
    
    // 移动画布原点到连接线中点
    canvas.translate(midPoint.dx, midPoint.dy);
    
    // 旋转画布使连接线水平
    canvas.rotate(angle);
    
    // 创建连接线画笔
    final linePaint = Paint()
      ..color = line.color
      ..strokeWidth = line.width
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    
    // 绘制主连接线
    canvas.drawLine(
      Offset(-distance / 2, 0),
      Offset(distance / 2, 0),
      linePaint,
    );
    
    // 如果是主要连接线，绘制额外的科技风格元素
    if (line.isPrimary) {
      // 创建科技元素画笔
      final techPaint = Paint()
        ..color = line.color.withOpacity(0.7)
        ..strokeWidth = line.width * 0.7
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;
      
      // 计算动画偏移
      final animOffset = (animationValue * distance) % distance;
      
      // 绘制移动点
      canvas.drawCircle(
        Offset(-distance / 2 + animOffset, 0),
        line.width * 1.2,
        Paint()..color = line.color,
      );
      
      // 绘制刻度线
      final scaleCount = (distance / 20).floor();
      final scaleSpacing = distance / scaleCount;
      
      for (int i = 0; i < scaleCount; i++) {
        final scaleX = -distance / 2 + i * scaleSpacing;
        final scaleHeight = (i % 2 == 0) ? 3.0 : 1.5;
        
        canvas.drawLine(
          Offset(scaleX, -scaleHeight),
          Offset(scaleX, scaleHeight),
          techPaint,
        );
      }
    }
    
    // 恢复画布状态
    canvas.restore();
  }
  
  /// 渲染连接线的动画效果
  void _renderEffects(Canvas canvas, ConnectingLine line, FeaturePoint startPoint, FeaturePoint endPoint) {
    // 只有当起点或终点处于高亮或选中状态时，才渲染效果
    if (startPoint.state == FeaturePointState.highlighted || 
        startPoint.state == FeaturePointState.selected ||
        endPoint.state == FeaturePointState.highlighted || 
        endPoint.state == FeaturePointState.selected) {
      
      // 渲染发光效果
      final glowEffect = GlowEffect(
        type: GlowEffectType.simple,
        radius: line.width * 3.0,
        color: line.color,
        intensity: line.isPrimary ? 1.2 : 0.8,
      );
      
      glowEffect.drawForLine(
        canvas,
        startPoint.position,
        endPoint.position,
        line.color,
        line.width,
        animationValue,
      );
      
      // 如果是主要连接线，渲染流动效果
      if (line.isPrimary) {
        _renderFlowEffect(canvas, line, startPoint, endPoint);
      }
    }
  }
  
  /// 渲染流动效果
  void _renderFlowEffect(Canvas canvas, ConnectingLine line, FeaturePoint startPoint, FeaturePoint endPoint) {
    // 计算连接线长度和方向
    final dx = endPoint.position.dx - startPoint.position.dx;
    final dy = endPoint.position.dy - startPoint.position.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    
    // 计算单位向量
    final unitX = dx / distance;
    final unitY = dy / distance;
    
    // 计算流动点的位置
    final flowPhase = (animationValue * 2) % 1.0;
    final flowX = startPoint.position.dx + unitX * distance * flowPhase;
    final flowY = startPoint.position.dy + unitY * distance * flowPhase;
    
    // 创建流动点画笔
    final flowPaint = Paint()
      ..color = line.color
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, line.width * 2.0);
    
    // 绘制流动点
    canvas.drawCircle(
      Offset(flowX, flowY),
      line.width * 2.0,
      flowPaint,
    );
    
    // 绘制流动尾迹
    final tailPath = Path();
    final tailLength = distance * 0.2;
    
    for (int i = 0; i < 10; i++) {
      final tailPhase = i / 10.0;
      final tailDistance = tailLength * tailPhase;
      final tailX = flowX - unitX * tailDistance;
      final tailY = flowY - unitY * tailDistance;
      final tailRadius = line.width * 2.0 * (1.0 - tailPhase);
      
      final tailPaint = Paint()
        ..color = line.color.withOpacity(0.7 * (1.0 - tailPhase))
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset(tailX, tailY),
        tailRadius,
        tailPaint,
      );
    }
  }
  
  /// 渲染多条连接线
  void renderLines(Canvas canvas, List<ConnectingLine> lines, Map<int, FeaturePoint> pointsMap) {
    // 先渲染普通线，再渲染主要线，确保主要线在最上层
    
    // 渲染普通线
    for (final line in lines) {
      if (!line.isPrimary) {
        render(canvas, line, pointsMap);
      }
    }
    
    // 渲染主要线
    for (final line in lines) {
      if (line.isPrimary) {
        render(canvas, line, pointsMap);
      }
    }
  }
}
