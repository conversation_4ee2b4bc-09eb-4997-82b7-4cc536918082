import 'dart:math' as math;
import 'package:flutter/material.dart';

import '../models/feature_point_model.dart';
import '../animations/effects/pulse_effect.dart';
import '../animations/effects/scan_effect.dart';
import '../animations/effects/tech_line_effect.dart';
import '../animations/effects/glow_effect.dart';

/// 特效渲染器
/// 
/// 负责渲染特殊视觉效果和背景动画
/// 此文件是模块化鼻部特征点可视化系统的渲染层

/// 特效类型枚举
enum EffectType {
  /// 网格背景 - 绘制网格背景
  grid,
  
  /// 扫描线 - 绘制扫描线效果
  scanLine,
  
  /// 粒子效果 - 绘制粒子动画
  particles,
  
  /// 光线效果 - 绘制光线动画
  rays,
}

/// 特效渲染器类
class EffectRenderer {
  /// 特效类型
  final EffectType type;
  
  /// 动画控制器值
  final double animationValue;
  
  /// 特效颜色
  final Color color;
  
  /// 特效强度（1.0为正常强度）
  final double intensity;
  
  /// 构造函数
  EffectRenderer({
    this.type = EffectType.grid,
    required this.animationValue,
    required this.color,
    this.intensity = 1.0,
  });
  
  /// 渲染特效
  void render(Canvas canvas, Size size) {
    switch (type) {
      case EffectType.grid:
        _renderGridEffect(canvas, size);
        break;
      case EffectType.scanLine:
        _renderScanLineEffect(canvas, size);
        break;
      case EffectType.particles:
        _renderParticlesEffect(canvas, size);
        break;
      case EffectType.rays:
        _renderRaysEffect(canvas, size);
        break;
    }
  }
  
  /// 渲染网格背景效果
  void _renderGridEffect(Canvas canvas, Size size) {
    // 计算网格单元格大小
    final cellSize = 30.0;
    
    // 创建网格画笔
    final gridPaint = Paint()
      ..color = color.withOpacity(0.1 * intensity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    
    // 计算网格行列数
    final columns = (size.width / cellSize).ceil() + 1;
    final rows = (size.height / cellSize).ceil() + 1;
    
    // 绘制垂直网格线
    for (int i = 0; i < columns; i++) {
      final x = i * cellSize;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
    
    // 绘制水平网格线
    for (int i = 0; i < rows; i++) {
      final y = i * cellSize;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
    
    // 绘制动态强调线
    final emphasisPaint = Paint()
      ..color = color.withOpacity(0.3 * intensity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // 计算动态强调线位置
    final emphasisX = (animationValue * columns * 2) % columns * cellSize;
    final emphasisY = (animationValue * rows * 1.5) % rows * cellSize;
    
    // 绘制垂直强调线
    canvas.drawLine(
      Offset(emphasisX, 0),
      Offset(emphasisX, size.height),
      emphasisPaint,
    );
    
    // 绘制水平强调线
    canvas.drawLine(
      Offset(0, emphasisY),
      Offset(size.width, emphasisY),
      emphasisPaint,
    );
    
    // 绘制交叉点
    canvas.drawCircle(
      Offset(emphasisX, emphasisY),
      3.0,
      Paint()..color = color.withOpacity(0.5 * intensity),
    );
  }
  
  /// 渲染扫描线效果
  void _renderScanLineEffect(Canvas canvas, Size size) {
    // 计算扫描线位置
    final scanPhase = (animationValue * 2) % 1.0;
    final scanY = size.height * scanPhase;
    
    // 创建扫描线渐变
    final scanGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        color.withOpacity(0.0),
        color.withOpacity(0.3 * intensity),
        color.withOpacity(0.0),
      ],
      stops: const [0.0, 0.5, 1.0],
    );
    
    // 创建扫描线画笔
    final scanPaint = Paint()
      ..shader = scanGradient.createShader(
        Rect.fromLTWH(0, scanY - 20, size.width, 40),
      );
    
    // 绘制扫描线
    canvas.drawRect(
      Rect.fromLTWH(0, scanY - 20, size.width, 40),
      scanPaint,
    );
    
    // 绘制扫描线边缘
    final edgePaint = Paint()
      ..color = color.withOpacity(0.5 * intensity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    canvas.drawLine(
      Offset(0, scanY),
      Offset(size.width, scanY),
      edgePaint,
    );
    
    // 绘制扫描线两端的装饰
    final decorSize = 10.0;
    
    // 左侧装饰
    canvas.drawLine(
      Offset(0, scanY - decorSize),
      Offset(0, scanY + decorSize),
      edgePaint,
    );
    
    canvas.drawLine(
      Offset(0, scanY),
      Offset(decorSize, scanY),
      edgePaint,
    );
    
    // 右侧装饰
    canvas.drawLine(
      Offset(size.width, scanY - decorSize),
      Offset(size.width, scanY + decorSize),
      edgePaint,
    );
    
    canvas.drawLine(
      Offset(size.width, scanY),
      Offset(size.width - decorSize, scanY),
      edgePaint,
    );
  }
  
  /// 渲染粒子效果
  void _renderParticlesEffect(Canvas canvas, Size size) {
    // 创建随机数生成器
    final random = math.Random(42); // 固定种子以确保一致性
    
    // 粒子数量
    final particleCount = 50;
    
    // 绘制粒子
    for (int i = 0; i < particleCount; i++) {
      // 计算粒子位置
      final particlePhase = (animationValue + i / particleCount) % 1.0;
      final particleX = random.nextDouble() * size.width;
      final particleY = random.nextDouble() * size.height;
      
      // 计算粒子大小
      final particleSize = 1.0 + random.nextDouble() * 3.0 * intensity;
      
      // 计算粒子不透明度
      final particleOpacity = 0.1 + 0.3 * math.sin(particlePhase * math.pi * 2);
      
      // 创建粒子画笔
      final particlePaint = Paint()
        ..color = color.withOpacity(particleOpacity * intensity)
        ..style = PaintingStyle.fill;
      
      // 绘制粒子
      canvas.drawCircle(
        Offset(particleX, particleY),
        particleSize,
        particlePaint,
      );
      
      // 为一些粒子绘制连接线
      if (i % 5 == 0 && i + 1 < particleCount) {
        final nextParticleX = random.nextDouble() * size.width;
        final nextParticleY = random.nextDouble() * size.height;
        
        // 计算距离
        final dx = nextParticleX - particleX;
        final dy = nextParticleY - particleY;
        final distance = math.sqrt(dx * dx + dy * dy);
        
        // 只连接距离适中的粒子
        if (distance < 100) {
          // 创建连接线画笔
          final linePaint = Paint()
            ..color = color.withOpacity(0.05 * intensity)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 0.5;
          
          // 绘制连接线
          canvas.drawLine(
            Offset(particleX, particleY),
            Offset(nextParticleX, nextParticleY),
            linePaint,
          );
        }
      }
    }
  }
  
  /// 渲染光线效果
  void _renderRaysEffect(Canvas canvas, Size size) {
    // 保存画布状态
    canvas.save();
    
    // 移动画布原点到中心点
    canvas.translate(size.width / 2, size.height / 2);
    
    // 计算旋转角度
    final rotationAngle = animationValue * math.pi / 8;
    
    // 旋转画布
    canvas.rotate(rotationAngle);
    
    // 光线数量
    final rayCount = 12;
    
    // 绘制光线
    for (int i = 0; i < rayCount; i++) {
      // 计算光线角度
      final rayAngle = i * 2 * math.pi / rayCount;
      
      // 计算光线起点和终点
      final rayStart = Offset(0, 0);
      final rayEnd = Offset(
        math.cos(rayAngle) * size.width,
        math.sin(rayAngle) * size.height,
      );
      
      // 计算光线不透明度
      final rayPhase = (animationValue + i / rayCount) % 1.0;
      final rayOpacity = 0.1 + 0.1 * math.sin(rayPhase * math.pi * 2);
      
      // 创建光线渐变
      final rayGradient = LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          color.withOpacity(0.3 * intensity),
          color.withOpacity(0.0),
        ],
      );
      
      // 创建光线画笔
      final rayPaint = Paint()
        ..shader = rayGradient.createShader(
          Rect.fromPoints(rayStart, rayEnd),
        )
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      // 绘制光线
      canvas.drawLine(rayStart, rayEnd, rayPaint);
    }
    
    // 恢复画布状态
    canvas.restore();
  }
  
  /// 渲染组合特效
  void renderCombinedEffects(Canvas canvas, Size size, List<FeaturePoint> highlightedPoints) {
    // 首先渲染背景特效
    render(canvas, size);
    
    // 如果有高亮点，渲染高亮点的特殊效果
    if (highlightedPoints.isNotEmpty) {
      // 计算高亮区域的中心点
      Offset centerPoint = Offset.zero;
      for (final point in highlightedPoints) {
        centerPoint += point.position;
      }
      centerPoint = Offset(
        centerPoint.dx / highlightedPoints.length,
        centerPoint.dy / highlightedPoints.length,
      );
      
      // 渲染高亮区域的特殊效果
      _renderHighlightAreaEffects(canvas, size, centerPoint, highlightedPoints);
    }
  }
  
  /// 渲染高亮区域的特殊效果
  void _renderHighlightAreaEffects(Canvas canvas, Size size, Offset center, List<FeaturePoint> highlightedPoints) {
    // 计算高亮区域的半径
    double maxDistance = 0;
    for (final point in highlightedPoints) {
      final dx = point.position.dx - center.dx;
      final dy = point.position.dy - center.dy;
      final distance = math.sqrt(dx * dx + dy * dy);
      if (distance > maxDistance) {
        maxDistance = distance;
      }
    }
    
    // 添加边距
    final areaRadius = maxDistance + 50;
    
    // 渲染扫描效果
    final scanEffect = ScanEffect(
      type: ScanEffectType.circular,
      speed: 0.5,
      color: color,
      radius: areaRadius,
    );
    
    scanEffect.draw(canvas, center, animationValue);
    
    // 渲染光线效果
    _renderAreaRays(canvas, center, areaRadius);
  }
  
  /// 渲染区域光线效果
  void _renderAreaRays(Canvas canvas, Offset center, double radius) {
    // 保存画布状态
    canvas.save();
    
    // 移动画布原点到中心点
    canvas.translate(center.dx, center.dy);
    
    // 计算旋转角度
    final rotationAngle = -animationValue * math.pi / 4;
    
    // 旋转画布
    canvas.rotate(rotationAngle);
    
    // 光线数量
    final rayCount = 8;
    
    // 绘制光线
    for (int i = 0; i < rayCount; i++) {
      // 计算光线角度
      final rayAngle = i * 2 * math.pi / rayCount;
      
      // 旋转画布到光线角度
      canvas.save();
      canvas.rotate(rayAngle);
      
      // 计算光线不透明度
      final rayPhase = (animationValue + i / rayCount) % 1.0;
      final rayOpacity = 0.1 + 0.1 * math.sin(rayPhase * math.pi * 2);
      
      // 创建光线画笔
      final rayPaint = Paint()
        ..color = color.withOpacity(rayOpacity * intensity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;
      
      // 绘制光线
      canvas.drawLine(
        Offset(0, -radius * 0.2),
        Offset(0, -radius * 0.8),
        rayPaint,
      );
      
      // 恢复画布状态
      canvas.restore();
    }
    
    // 恢复画布状态
    canvas.restore();
  }
  
  /// 渲染连接高亮点的效果
  void renderHighlightConnections(Canvas canvas, List<FeaturePoint> highlightedPoints) {
    // 如果高亮点少于2个，则不渲染连接
    if (highlightedPoints.length < 2) {
      return;
    }
    
    // 创建路径
    final path = Path();
    
    // 移动到第一个点
    path.moveTo(
      highlightedPoints[0].position.dx,
      highlightedPoints[0].position.dy,
    );
    
    // 连接到其他点
    for (int i = 1; i < highlightedPoints.length; i++) {
      path.lineTo(
        highlightedPoints[i].position.dx,
        highlightedPoints[i].position.dy,
      );
    }
    
    // 闭合路径
    path.close();
    
    // 创建填充画笔
    final fillPaint = Paint()
      ..color = color.withOpacity(0.1 * intensity)
      ..style = PaintingStyle.fill;
    
    // 绘制填充
    canvas.drawPath(path, fillPaint);
    
    // 创建边框画笔
    final borderPaint = Paint()
      ..color = color.withOpacity(0.3 * intensity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // 绘制边框
    canvas.drawPath(path, borderPaint);
  }
}
