import 'dart:ui';
import 'package:flutter/material.dart';
import '../models/feature_point.dart';
import '../utils/temp_logger.dart';

/// 特征点状态枚举
enum FeaturePointState {
  /// 默认状态
  normal,
  
  /// 活动状态
  active,
  
  /// 高亮状态
  highlighted,
  
  /// 选中状态
  selected,
  
  /// 禁用状态
  disabled,
}

/// 特征点交互控制器
/// 
/// 负责处理特征点的交互事件，如点击、高亮等
class FeatureInteractionController {
  /// 特征点列表
  final List<FeaturePoint> _featurePoints = [];
  
  /// 当前选中的特征点ID
  String? _selectedPointId;
  
  /// 当前高亮的特征点ID列表
  final List<String> _highlightedPointIds = [];
  
  /// 状态变化监听器
  final List<Function(List<FeaturePoint>)> _stateChangeListeners = [];
  
  /// 点击事件监听器
  final List<Function(FeaturePoint)> _clickListeners = [];
  
  /// 获取特征点列表
  List<FeaturePoint> get featurePoints => List.unmodifiable(_featurePoints);
  
  /// 获取当前选中的特征点
  FeaturePoint? get selectedPoint {
    if (_selectedPointId == null) return null;
    return _featurePoints.firstWhere(
      (point) => point.id == _selectedPointId,
      orElse: () => throw Exception('选中的特征点不存在: $_selectedPointId'),
    );
  }
  
  /// 获取当前高亮的特征点列表
  List<FeaturePoint> get highlightedPoints {
    return _featurePoints.where(
      (point) => _highlightedPointIds.contains(point.id)
    ).toList();
  }
  
  /// 设置特征点列表
  void setFeaturePoints(List<FeaturePoint> points) {
    _featurePoints.clear();
    _featurePoints.addAll(points);
    _notifyStateChange();
    
    TempLogger.logInfo(
      'FeatureInteractionController', 
      'setFeaturePoints', 
      '✅ [设置] 更新特征点列表，共 ${points.length} 个特征点'
    );
  }
  
  /// 添加特征点
  void addFeaturePoint(FeaturePoint point) {
    // 检查是否已存在相同ID的点
    if (_featurePoints.any((p) => p.id == point.id)) {
      TempLogger.logWarning(
        'FeatureInteractionController', 
        'addFeaturePoint', 
        '⚠️ [警告] 特征点ID已存在: ${point.id}，将被替换'
      );
      
      // 替换现有点
      final index = _featurePoints.indexWhere((p) => p.id == point.id);
      _featurePoints[index] = point;
    } else {
      _featurePoints.add(point);
    }
    
    _notifyStateChange();
    
    TempLogger.logInfo(
      'FeatureInteractionController', 
      'addFeaturePoint', 
      '✅ [添加] 添加特征点: ${point.id}'
    );
  }
  
  /// 移除特征点
  void removeFeaturePoint(String pointId) {
    final removed = _featurePoints.removeWhere((p) => p.id == pointId);
    
    if (removed) {
      // 如果移除的是当前选中的点，清除选中状态
      if (_selectedPointId == pointId) {
        _selectedPointId = null;
      }
      
      // 从高亮列表中移除
      _highlightedPointIds.remove(pointId);
      
      _notifyStateChange();
      
      TempLogger.logInfo(
        'FeatureInteractionController', 
        'removeFeaturePoint', 
        '✅ [移除] 移除特征点: $pointId'
      );
    } else {
      TempLogger.logWarning(
        'FeatureInteractionController', 
        'removeFeaturePoint', 
        '⚠️ [警告] 特征点不存在: $pointId'
      );
    }
  }
  
  /// 清除所有特征点
  void clearFeaturePoints() {
    _featurePoints.clear();
    _selectedPointId = null;
    _highlightedPointIds.clear();
    _notifyStateChange();
    
    TempLogger.logInfo(
      'FeatureInteractionController', 
      'clearFeaturePoints', 
      '✅ [清除] 清除所有特征点'
    );
  }
  
  /// 选中特征点
  void selectPoint(String pointId) {
    // 检查点是否存在
    final pointExists = _featurePoints.any((p) => p.id == pointId);
    if (!pointExists) {
      TempLogger.logWarning(
        'FeatureInteractionController', 
        'selectPoint', 
        '⚠️ [警告] 特征点不存在: $pointId'
      );
      return;
    }
    
    // 更新选中状态
    _selectedPointId = pointId;
    
    // 更新所有点的状态
    for (final point in _featurePoints) {
      if (point.id == pointId) {
        point.isHighlighted = true;
      }
    }
    
    _notifyStateChange();
    
    TempLogger.logInfo(
      'FeatureInteractionController', 
      'selectPoint', 
      '✅ [选中] 选中特征点: $pointId'
    );
  }
  
  /// 取消选中特征点
  void deselectPoint() {
    if (_selectedPointId != null) {
      final oldSelectedId = _selectedPointId;
      _selectedPointId = null;
      
      // 更新点的状态
      for (final point in _featurePoints) {
        if (point.id == oldSelectedId && !_highlightedPointIds.contains(point.id)) {
          point.isHighlighted = false;
        }
      }
      
      _notifyStateChange();
      
      TempLogger.logInfo(
        'FeatureInteractionController', 
        'deselectPoint', 
        '✅ [取消选中] 取消选中特征点: $oldSelectedId'
      );
    }
  }
  
  /// 高亮特征点
  void highlightPoint(String pointId) {
    // 检查点是否存在
    final pointExists = _featurePoints.any((p) => p.id == pointId);
    if (!pointExists) {
      TempLogger.logWarning(
        'FeatureInteractionController', 
        'highlightPoint', 
        '⚠️ [警告] 特征点不存在: $pointId'
      );
      return;
    }
    
    // 添加到高亮列表
    if (!_highlightedPointIds.contains(pointId)) {
      _highlightedPointIds.add(pointId);
      
      // 更新点的状态
      for (final point in _featurePoints) {
        if (point.id == pointId) {
          point.isHighlighted = true;
        }
      }
      
      _notifyStateChange();
      
      TempLogger.logInfo(
        'FeatureInteractionController', 
        'highlightPoint', 
        '✅ [高亮] 高亮特征点: $pointId'
      );
    }
  }
  
  /// 取消高亮特征点
  void unhighlightPoint(String pointId) {
    if (_highlightedPointIds.remove(pointId)) {
      // 如果不是当前选中的点，更新状态
      if (_selectedPointId != pointId) {
        for (final point in _featurePoints) {
          if (point.id == pointId) {
            point.isHighlighted = false;
          }
        }
      }
      
      _notifyStateChange();
      
      TempLogger.logInfo(
        'FeatureInteractionController', 
        'unhighlightPoint', 
        '✅ [取消高亮] 取消高亮特征点: $pointId'
      );
    }
  }
  
  /// 切换特征点高亮状态
  void toggleHighlight(String pointId) {
    if (_highlightedPointIds.contains(pointId)) {
      unhighlightPoint(pointId);
    } else {
      highlightPoint(pointId);
    }
  }
  
  /// 处理特征点点击事件
  void handlePointClick(String pointId) {
    // 查找点
    final point = _featurePoints.firstWhere(
      (p) => p.id == pointId,
      orElse: () {
        TempLogger.logWarning(
          'FeatureInteractionController', 
          'handlePointClick', 
          '⚠️ [警告] 特征点不存在: $pointId'
        );
        return null;
      },
    );
    
    if (point == null) return;
    
    // 切换状态：normal -> highlighted -> selected -> normal
    if (_selectedPointId == pointId) {
      // 如果已选中，取消选中
      deselectPoint();
      unhighlightPoint(pointId);
    } else if (_highlightedPointIds.contains(pointId)) {
      // 如果已高亮，选中
      selectPoint(pointId);
    } else {
      // 如果未高亮，高亮
      highlightPoint(pointId);
    }
    
    // 通知点击监听器
    for (final listener in _clickListeners) {
      listener(point);
    }
    
    TempLogger.logInfo(
      'FeatureInteractionController', 
      'handlePointClick', 
      '🔄 [点击] 处理特征点点击: $pointId'
    );
  }
  
  /// 处理点击位置
  FeaturePoint? handleTapPosition(Offset position, {double tapRadius = 20.0}) {
    // 查找最近的点
    FeaturePoint? nearestPoint;
    double minDistance = double.infinity;
    
    for (final point in _featurePoints) {
      final distance = (point.position - position).distance;
      if (distance < minDistance && distance <= tapRadius) {
        minDistance = distance;
        nearestPoint = point;
      }
    }
    
    // 如果找到点，处理点击事件
    if (nearestPoint != null) {
      handlePointClick(nearestPoint.id);
      
      TempLogger.logInfo(
        'FeatureInteractionController', 
        'handleTapPosition', 
        '✅ [点击] 点击位置 ($position) 命中特征点: ${nearestPoint.id}, 距离: ${minDistance.toStringAsFixed(2)}'
      );
    } else {
      TempLogger.logInfo(
        'FeatureInteractionController', 
        'handleTapPosition', 
        '🔄 [点击] 点击位置 ($position) 未命中任何特征点'
      );
    }
    
    return nearestPoint;
  }
  
  /// 添加状态变化监听器
  void addStateChangeListener(Function(List<FeaturePoint>) listener) {
    _stateChangeListeners.add(listener);
  }
  
  /// 移除状态变化监听器
  void removeStateChangeListener(Function(List<FeaturePoint>) listener) {
    _stateChangeListeners.remove(listener);
  }
  
  /// 添加点击事件监听器
  void addClickListener(Function(FeaturePoint) listener) {
    _clickListeners.add(listener);
  }
  
  /// 移除点击事件监听器
  void removeClickListener(Function(FeaturePoint) listener) {
    _clickListeners.remove(listener);
  }
  
  /// 通知状态变化
  void _notifyStateChange() {
    for (final listener in _stateChangeListeners) {
      listener(_featurePoints);
    }
  }
}
