import '../core/feature_points_helper.dart';

void main() {
  print('开始测试特征点获取...');
  print('');
  
  // 测试所有区域的特征点
  testAllAreas();
  
  print('');
  print('特征点测试完成！');
}

void testAllAreas() {
  final areas = [
    FeatureAreaType.face_contour,
    FeatureAreaType.nose,
    FeatureAreaType.eyes,
    FeatureAreaType.lips,
    FeatureAreaType.anti_aging,
  ];
  
  for (final area in areas) {
    testArea(area);
    print('');
  }
}

void testArea(FeatureAreaType type) {
  final areaName = FeaturePointsHelper.getAreaDisplayName(type);
  final points = FeaturePointsHelper.getAreaPoints(type);
  
  print('区域: $areaName');
  print('特征点数量: ${points.length}');
  print('特征点列表: ${points.join(", ")}');
  
  // 测试该区域的所有参数
  testAreaParameters(type);
}

void testAreaParameters(FeatureAreaType type) {
  String areaName = '';
  
  switch (type) {
    case FeatureAreaType.face_contour:
      areaName = 'face_contour';
      break;
    case FeatureAreaType.nose:
      areaName = 'nose';
      break;
    case FeatureAreaType.eyes:
      areaName = 'eyes';
      break;
    case FeatureAreaType.lips:
      areaName = 'lips';
      break;
    case FeatureAreaType.anti_aging:
      areaName = 'anti_aging';
      break;
    default:
      areaName = '';
      break;
  }
  
  final paramNames = getParameterNames(areaName);
  
  for (final paramName in paramNames) {
    final points = FeaturePointsHelper.getParameterPoints(type, paramName);
    print('  参数: $paramName');
    print('  特征点数量: ${points.length}');
    print('  特征点列表: ${points.join(", ")}');
    print('');
  }
}

List<String> getParameterNames(String areaName) {
  switch (areaName) {
    case 'face_contour':
      return ['contour_tighten', 'chin_adjust', 'cheekbone_adjust', 'face_shape'];
    case 'nose':
      return ['bridge_height', 'tip_adjust', 'nostril_width', 'base_height'];
    case 'eyes':
      return ['double_fold', 'canthal_tilt', 'eye_bag_removal', 'outer_corner_lift'];
    case 'lips':
      return ['lip_shape', 'mouth_corner'];
    case 'anti_aging':
      return ['nasolabial_folds', 'wrinkle_removal', 'forehead_fullness', 'facial_firmness'];
    default:
      return [];
  }
}
