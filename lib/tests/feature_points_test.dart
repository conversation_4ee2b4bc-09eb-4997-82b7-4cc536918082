import 'package:flutter/material.dart';
import '../core/feature_points_helper.dart';

/// 特征点测试组件
class FeaturePointsTest extends StatelessWidget {
  const FeaturePointsTest({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('特征点测试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('特征点区域测试', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            _buildAreaTest(FeatureAreaType.face_contour),
            _buildAreaTest(FeatureAreaType.nose),
            _buildAreaTest(FeatureAreaType.eyes),
            _buildAreaTest(FeatureAreaType.lips),
            _buildAreaTest(FeatureAreaType.anti_aging),
          ],
        ),
      ),
    );
  }

  Widget _buildAreaTest(FeatureAreaType type) {
    final areaName = FeaturePointsHelper.getAreaDisplayName(type);
    final points = FeaturePointsHelper.getAreaPoints(type);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('区域: $areaName', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('特征点数量: ${points.length}'),
            const SizedBox(height: 8),
            Text('特征点列表: ${points.join(", ")}'),
          ],
        ),
      ),
    );
  }
}
